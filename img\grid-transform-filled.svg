<?xml version="1.0" encoding="UTF-8"?>
<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="367pt" height="153pt" viewBox="0 0 367 153" version="1.1">
<defs>
<g>
<symbol overflow="visible" id="glyph0-0">
<path style="stroke:none;" d=""/>
</symbol>
<symbol overflow="visible" id="glyph0-1">
<path style="stroke:none;" d="M 6.203125 0 L 6.203125 -0.21875 C 5.828125 -0.21875 5.671875 -0.421875 5.21875 -1.515625 L 3.21875 -6.203125 L 2.96875 -6.203125 L 0.96875 -1.3125 C 0.625 -0.453125 0.5 -0.296875 0.078125 -0.21875 L 0.078125 0 L 1.90625 0 L 1.90625 -0.21875 C 1.375 -0.265625 1.171875 -0.359375 1.171875 -0.59375 C 1.171875 -0.8125 1.34375 -1.1875 1.453125 -1.46875 L 1.5625 -1.765625 L 3.59375 -1.765625 C 3.890625 -1.0625 4 -0.75 4 -0.546875 C 4 -0.34375 3.890625 -0.28125 3.59375 -0.25 L 3.296875 -0.21875 L 3.296875 0 Z M 3.453125 -2.125 L 1.703125 -2.125 L 2.546875 -4.3125 Z M 3.453125 -2.125 "/>
</symbol>
<symbol overflow="visible" id="glyph1-0">
<path style="stroke:none;" d=""/>
</symbol>
<symbol overflow="visible" id="glyph1-1">
<path style="stroke:none;" d="M 2.734375 1.453125 C 1.46875 0.390625 1.203125 -0.625 1.203125 -2.296875 C 1.203125 -4.03125 1.484375 -4.875 2.734375 -5.9375 L 2.65625 -6.078125 C 1.21875 -5.234375 0.4375 -3.9375 0.4375 -2.265625 C 0.4375 -0.734375 1.203125 0.78125 2.625 1.59375 Z M 2.734375 1.453125 "/>
</symbol>
<symbol overflow="visible" id="glyph1-2">
<path style="stroke:none;" d="M 4.28125 -2.96875 C 4.28125 -4.8125 3.46875 -6.078125 2.28125 -6.078125 C 0.84375 -6.078125 0.21875 -4.609375 0.21875 -3.03125 C 0.21875 -1.546875 0.71875 0.125 2.25 0.125 C 3.71875 0.125 4.28125 -1.421875 4.28125 -2.96875 Z M 3.421875 -2.921875 C 3.421875 -1.140625 3.015625 -0.109375 2.25 -0.109375 C 1.46875 -0.109375 1.078125 -1.140625 1.078125 -2.96875 C 1.078125 -4.78125 1.484375 -5.84375 2.234375 -5.84375 C 3.03125 -5.84375 3.421875 -4.796875 3.421875 -2.921875 Z M 3.421875 -2.921875 "/>
</symbol>
<symbol overflow="visible" id="glyph1-3">
<path style="stroke:none;" d="M 0.75 1.265625 C 1.375 0.96875 1.75 0.40625 1.75 -0.140625 C 1.75 -0.609375 1.4375 -0.921875 1.03125 -0.921875 C 0.71875 -0.921875 0.5 -0.71875 0.5 -0.40625 C 0.5 -0.09375 0.6875 0.046875 1.015625 0.046875 C 1.109375 0.046875 1.203125 0.015625 1.28125 0.015625 C 1.34375 0.015625 1.40625 0.078125 1.40625 0.140625 C 1.40625 0.4375 1.15625 0.765625 0.65625 1.09375 Z M 0.75 1.265625 "/>
</symbol>
<symbol overflow="visible" id="glyph1-4">
<path style="stroke:none;" d=""/>
</symbol>
<symbol overflow="visible" id="glyph1-5">
<path style="stroke:none;" d="M 3.546875 0 L 3.546875 -0.140625 C 2.875 -0.140625 2.6875 -0.296875 2.6875 -0.6875 L 2.6875 -6.0625 L 2.609375 -6.078125 L 1 -5.265625 L 1 -5.140625 L 1.234375 -5.234375 C 1.40625 -5.296875 1.5625 -5.34375 1.640625 -5.34375 C 1.84375 -5.34375 1.921875 -5.203125 1.921875 -4.890625 L 1.921875 -0.859375 C 1.921875 -0.359375 1.734375 -0.171875 1.0625 -0.140625 L 1.0625 0 Z M 3.546875 0 "/>
</symbol>
<symbol overflow="visible" id="glyph1-6">
<path style="stroke:none;" d="M 0.265625 -5.9375 C 1.5625 -4.90625 1.796875 -3.875 1.796875 -2.203125 C 1.796875 -0.453125 1.53125 0.390625 0.265625 1.453125 L 0.34375 1.59375 C 1.765625 0.71875 2.5625 -0.5625 2.5625 -2.21875 C 2.5625 -3.75 1.75 -5.25 0.375 -6.078125 Z M 0.265625 -5.9375 "/>
</symbol>
<symbol overflow="visible" id="glyph1-7">
<path style="stroke:none;" d="M 2.5625 -1.75 L 2.5625 -2.3125 L 0.34375 -2.3125 L 0.34375 -1.75 Z M 2.5625 -1.75 "/>
</symbol>
<symbol overflow="visible" id="glyph1-8">
<path style="stroke:none;" d="M 4.265625 -1.234375 L 4.140625 -1.28125 C 3.84375 -0.78125 3.65625 -0.6875 3.28125 -0.6875 L 1.171875 -0.6875 L 2.65625 -2.265625 C 3.453125 -3.109375 3.8125 -3.78125 3.8125 -4.5 C 3.8125 -5.390625 3.15625 -6.078125 2.140625 -6.078125 C 1.03125 -6.078125 0.453125 -5.34375 0.265625 -4.296875 L 0.453125 -4.25 C 0.8125 -5.125 1.140625 -5.421875 1.78125 -5.421875 C 2.546875 -5.421875 3.03125 -4.96875 3.03125 -4.15625 C 3.03125 -3.390625 2.703125 -2.703125 1.859375 -1.8125 L 0.265625 -0.109375 L 0.265625 0 L 3.78125 0 Z M 4.265625 -1.234375 "/>
</symbol>
<symbol overflow="visible" id="glyph1-9">
<path style="stroke:none;" d="M 0.546875 -4.59375 C 0.921875 -5.25 1.328125 -5.546875 1.890625 -5.546875 C 2.484375 -5.546875 2.859375 -5.234375 2.859375 -4.625 C 2.859375 -4.078125 2.578125 -3.671875 2.140625 -3.421875 C 1.953125 -3.3125 1.71875 -3.21875 1.375 -3.09375 L 1.375 -2.96875 C 1.890625 -2.96875 2.09375 -2.9375 2.296875 -2.875 C 2.921875 -2.703125 3.234375 -2.265625 3.234375 -1.578125 C 3.234375 -0.8125 2.734375 -0.203125 2.0625 -0.203125 C 1.8125 -0.203125 1.625 -0.25 1.28125 -0.484375 C 1.03125 -0.65625 0.890625 -0.71875 0.734375 -0.71875 C 0.53125 -0.71875 0.375 -0.578125 0.375 -0.390625 C 0.375 -0.0625 0.71875 0.125 1.375 0.125 C 2.171875 0.125 3.03125 -0.140625 3.46875 -0.71875 C 3.71875 -1.046875 3.875 -1.5 3.875 -1.96875 C 3.875 -2.4375 3.734375 -2.859375 3.484375 -3.125 C 3.296875 -3.328125 3.125 -3.4375 2.734375 -3.609375 C 3.34375 -3.96875 3.578125 -4.421875 3.578125 -4.84375 C 3.578125 -5.59375 3 -6.078125 2.171875 -6.078125 C 1.234375 -6.078125 0.671875 -5.484375 0.40625 -4.625 Z M 0.546875 -4.59375 "/>
</symbol>
</g>
</defs>
<g id="surface1">
<path style=" stroke:none;fill-rule:nonzero;fill:rgb(69.804382%,85.098267%,100%);fill-opacity:1;" d="M 277.738281 78.570312 L 301.980469 102.8125 L 350 31.621094 L 350 31.464844 L 325.820312 7.285156 Z M 277.738281 78.570312 "/>
<path style=" stroke:none;fill-rule:nonzero;fill:rgb(69.804382%,85.098267%,100%);fill-opacity:1;" d="M 72 55 L 96 55 L 96 79 L 72 79 Z M 72 55 "/>
<path style="fill:none;stroke-width:1;stroke-linecap:butt;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-dasharray:1,4;stroke-miterlimit:10;" d="M 5 76 L 149 76 " transform="matrix(1,0,0,1,-4,3)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:butt;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-dasharray:1,4;stroke-miterlimit:10;" d="M 76 5 L 76 149 " transform="matrix(1,0,0,1,-4,3)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:butt;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-dasharray:1,4;stroke-miterlimit:10;" d="M 100 5 L 100 149 " transform="matrix(1,0,0,1,-4,3)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:butt;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-dasharray:1,4;stroke-miterlimit:10;" d="M 124 5 L 124 149 " transform="matrix(1,0,0,1,-4,3)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:butt;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-dasharray:1,4;stroke-miterlimit:10;" d="M 52 5 L 52 149 " transform="matrix(1,0,0,1,-4,3)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:butt;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-dasharray:1,4;stroke-miterlimit:10;" d="M 28 5 L 28 149 " transform="matrix(1,0,0,1,-4,3)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:butt;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-dasharray:1,4;stroke-miterlimit:10;" d="M 5 52 L 149 52 " transform="matrix(1,0,0,1,-4,3)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:butt;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-dasharray:1,4;stroke-miterlimit:10;" d="M 5 28 L 149 28 " transform="matrix(1,0,0,1,-4,3)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:butt;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-dasharray:1,4;stroke-miterlimit:10;" d="M 5 100 L 149 100 " transform="matrix(1,0,0,1,-4,3)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:butt;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-dasharray:1,4;stroke-miterlimit:10;" d="M 5 124 L 149 124 " transform="matrix(1,0,0,1,-4,3)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:butt;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 76 57.898438 L 76 74.5 " transform="matrix(1,0,0,1,-4,3)"/>
<path style="fill-rule:nonzero;fill:rgb(0%,0%,0%);fill-opacity:1;stroke-width:1;stroke-linecap:butt;stroke-linejoin:miter;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 74.9375 74.9375 C 75.523438 74.355469 76.476562 74.355469 77.0625 74.9375 C 77.644531 75.523438 77.644531 76.476562 77.0625 77.0625 C 76.476562 77.644531 75.523438 77.644531 74.9375 77.0625 C 74.355469 76.476562 74.355469 75.523438 74.9375 74.9375 " transform="matrix(1,0,0,1,-4,3)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:butt;stroke-linejoin:miter;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 76 53.898438 L 76 57.898438 M 74.5 57.898438 L 76 53.898438 L 77.5 57.898438 " transform="matrix(1,0,0,1,-4,3)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:butt;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 94.101562 76 L 77.5 76 " transform="matrix(1,0,0,1,-4,3)"/>
<path style="fill-rule:nonzero;fill:rgb(0%,0%,0%);fill-opacity:1;stroke-width:1;stroke-linecap:butt;stroke-linejoin:miter;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 77.0625 74.9375 C 77.644531 75.523438 77.644531 76.476562 77.0625 77.0625 C 76.476562 77.644531 75.523438 77.644531 74.9375 77.0625 C 74.355469 76.476562 74.355469 75.523438 74.9375 74.9375 C 75.523438 74.355469 76.476562 74.355469 77.0625 74.9375 " transform="matrix(1,0,0,1,-4,3)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:butt;stroke-linejoin:miter;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 98.101562 76 L 94.101562 76 M 94.101562 74.5 L 98.101562 76 L 94.101562 77.5 " transform="matrix(1,0,0,1,-4,3)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:butt;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 326.726562 8.910156 L 282.832031 74.75 " transform="matrix(1,0,0,1,-4,3)"/>
<path style="fill-rule:nonzero;fill:rgb(0%,0%,0%);fill-opacity:1;stroke-width:1;stroke-linecap:butt;stroke-linejoin:miter;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 281.707031 74.527344 C 282.519531 74.367188 283.308594 74.894531 283.472656 75.707031 C 283.632812 76.519531 283.105469 77.308594 282.292969 77.472656 C 281.480469 77.632812 280.691406 77.105469 280.527344 76.292969 C 280.367188 75.480469 280.894531 74.691406 281.707031 74.527344 " transform="matrix(1,0,0,1,-4,3)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:butt;stroke-linejoin:miter;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 328.945312 5.582031 L 326.726562 8.910156 M 325.480469 8.078125 L 328.945312 5.582031 L 327.976562 9.742188 " transform="matrix(1,0,0,1,-4,3)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:butt;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 301.828125 95.828125 L 283.0625 77.0625 " transform="matrix(1,0,0,1,-4,3)"/>
<path style="fill-rule:nonzero;fill:rgb(0%,0%,0%);fill-opacity:1;stroke-width:1;stroke-linecap:butt;stroke-linejoin:miter;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 283.5 76 C 283.5 76.828125 282.828125 77.5 282 77.5 C 281.171875 77.5 280.5 76.828125 280.5 76 C 280.5 75.171875 281.171875 74.5 282 74.5 C 282.828125 74.5 283.5 75.171875 283.5 76 " transform="matrix(1,0,0,1,-4,3)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:butt;stroke-linejoin:miter;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 304.65625 98.65625 L 301.828125 95.828125 M 302.890625 94.765625 L 304.65625 98.65625 L 300.765625 96.890625 " transform="matrix(1,0,0,1,-4,3)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:butt;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-dasharray:1,4;stroke-miterlimit:10;" d="M 330 4 L 234 148 " transform="matrix(1,0,0,1,-4,3)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:butt;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-dasharray:1,4;stroke-miterlimit:10;" d="M 354 148 L 210 4 " transform="matrix(1,0,0,1,-4,3)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:butt;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-dasharray:1,4;stroke-miterlimit:10;" d="M 234.5 148 L 205.300781 118.800781 " transform="matrix(1,0,0,1,-4,3)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:butt;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-dasharray:1,4;stroke-miterlimit:10;" d="M 250 4 L 205.121094 71.320312 " transform="matrix(1,0,0,1,-4,3)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:butt;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-dasharray:1,4;stroke-miterlimit:10;" d="M 290 4 L 205.042969 131.4375 " transform="matrix(1,0,0,1,-4,3)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:butt;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-dasharray:1,4;stroke-miterlimit:10;" d="M 357.089844 23.367188 L 274 148 " transform="matrix(1,0,0,1,-4,3)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:butt;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-dasharray:1,4;stroke-miterlimit:10;" d="M 359.242188 33 L 330 4 " transform="matrix(1,0,0,1,-4,3)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:butt;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-dasharray:1,4;stroke-miterlimit:10;" d="M 360 79 L 314 148 " transform="matrix(1,0,0,1,-4,3)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:butt;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 186.101562 76 L 168 76 " transform="matrix(1,0,0,1,-4,3)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:butt;stroke-linejoin:miter;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 190.101562 76 L 186.101562 76 M 186.101562 74.5 L 190.101562 76 L 186.101562 77.5 " transform="matrix(1,0,0,1,-4,3)"/>
<g style="fill:rgb(0%,0%,0%);fill-opacity:1;">
  <use xlink:href="#glyph0-1" x="172.251" y="74.126"/>
</g>
<g style="fill:rgb(0%,0%,0%);fill-opacity:1;">
  <use xlink:href="#glyph1-1" x="73.631" y="49.5"/>
  <use xlink:href="#glyph1-2" x="76.628" y="49.5"/>
  <use xlink:href="#glyph1-3" x="81.128" y="49.5"/>
  <use xlink:href="#glyph1-4" x="83.378" y="49.5"/>
  <use xlink:href="#glyph1-5" x="85.628" y="49.5"/>
  <use xlink:href="#glyph1-6" x="90.128" y="49.5"/>
</g>
<g style="fill:rgb(0%,0%,0%);fill-opacity:1;">
  <use xlink:href="#glyph1-1" x="97.753" y="76"/>
  <use xlink:href="#glyph1-5" x="100.75" y="76"/>
  <use xlink:href="#glyph1-3" x="105.25" y="76"/>
  <use xlink:href="#glyph1-4" x="107.5" y="76"/>
  <use xlink:href="#glyph1-2" x="109.75" y="76"/>
  <use xlink:href="#glyph1-6" x="114.25" y="76"/>
</g>
<g style="fill:rgb(0%,0%,0%);fill-opacity:1;">
  <use xlink:href="#glyph1-1" x="304.3765" y="106"/>
  <use xlink:href="#glyph1-5" x="307.3735" y="106"/>
  <use xlink:href="#glyph1-3" x="311.8735" y="106"/>
  <use xlink:href="#glyph1-4" x="314.1235" y="106"/>
  <use xlink:href="#glyph1-7" x="316.3735" y="106"/>
  <use xlink:href="#glyph1-5" x="319.3705" y="106"/>
  <use xlink:href="#glyph1-6" x="323.8705" y="106"/>
</g>
<g style="fill:rgb(0%,0%,0%);fill-opacity:1;">
  <use xlink:href="#glyph1-1" x="299.875" y="14"/>
  <use xlink:href="#glyph1-8" x="302.872" y="14"/>
  <use xlink:href="#glyph1-3" x="307.372" y="14"/>
  <use xlink:href="#glyph1-4" x="309.622" y="14"/>
  <use xlink:href="#glyph1-9" x="311.872" y="14"/>
  <use xlink:href="#glyph1-6" x="316.372" y="14"/>
</g>
</g>
</svg>
