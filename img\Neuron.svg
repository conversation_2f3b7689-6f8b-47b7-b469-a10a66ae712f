<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<!-- Generator: Adobe Illustrator 12.0.0, SVG Export Plug-In . SVG Version: 6.00 Build 51448)  -->
<svg
   xmlns:dc="http://purl.org/dc/elements/1.1/"
   xmlns:cc="http://web.resource.org/cc/"
   xmlns:rdf="http://www.w3.org/1999/02/22-rdf-syntax-ns#"
   xmlns:svg="http://www.w3.org/2000/svg"
   xmlns="http://www.w3.org/2000/svg"
   xmlns:sodipodi="http://sodipodi.sourceforge.net/DTD/sodipodi-0.dtd"
   xmlns:inkscape="http://www.inkscape.org/namespaces/inkscape"
   version="1.1"
   id="Layer_1"
   width="399.821"
   height="214.98"
   viewBox="0 0 399.821 214.98"
   overflow="visible"
   enable-background="new 0 0 399.821 214.98"
   xml:space="preserve"
   sodipodi:version="0.32"
   inkscape:version="0.44.1"
   sodipodi:docname="Neuron.svg"
   sodipodi:docbase="c:\temp"><metadata
   id="metadata78"><rdf:RDF><cc:Work
       rdf:about=""><dc:format>image/svg+xml</dc:format><dc:type
         rdf:resource="http://purl.org/dc/dcmitype/StillImage" /></cc:Work></rdf:RDF></metadata><defs
   id="defs76" /><sodipodi:namedview
   inkscape:window-height="1031"
   inkscape:window-width="1920"
   inkscape:pageshadow="2"
   inkscape:pageopacity="0.0"
   guidetolerance="10.0"
   gridtolerance="10.0"
   objecttolerance="10.0"
   borderopacity="1.0"
   bordercolor="#666666"
   pagecolor="#ffffff"
   id="base"
   inkscape:zoom="1.8327287"
   inkscape:cx="199.91051"
   inkscape:cy="151.14076"
   inkscape:window-x="-4"
   inkscape:window-y="-4"
   inkscape:current-layer="Layer_1" />
<path
   fill="#AFA3CF"
   stroke="#000000"
   d="M126.833,132.089c3.606-1.491,1-6.333,1-6.333s-26.333-5-21-24s5.333-19,5.333-19  s6.333-4.668,11.667-8.334s3.667-4.999-0.667-3.666c-0.667-1.334,2.333-4.667,2.667-5.333c0.333-0.668,6-13.001-5.333,3.666  c0,1-9.667,12-4-7.666c0,0,7.333-35.334-1-9.668c0,0-2,6.334-4,2c0,0,4,13.335-8.333,46.001c-3-2.667-5-5.334-5-5.334  c-1-1.666-1.667-5.666,3-14.333s7.333-14,7.333-14s4.333-8.334-1-2.334c0,0,0.333-18.999-3,4.334c0,0-2.666,4.001-4.333,5.667  c0,0,4.667-11-1-8.333c0,0-3-7-2.667,1c0.333,7.999,2,9.667,1,14.333s-2.667,2.666-3-1.334s-10,0.334,5.333,24.667  c0,0,5.333,25.333-14.333,7.333c0,0-24.667-19.667,7-43c-4,3-5.667,6.335-5,2.334s0-3.667,0-3.667s-3.667,7.001-6,8.667  s-11.667,4.667,9-18.667c-4,3-11.333,8.667-4-7.667c-7,11-11.333,23.667-15.333,12.667c0,0,4.333,46.332-6,16.333  c0,0-3.667-6,5-35.667c-3,6.667-5.667,16-5-3.333c0,0-3,1-2.333,22.333c0,0-9.333,21.334-7.333-7c0,0,3-12.666,3.333-15  s-1.667,0.667-2.333,2s-0.667-5-0.667-5s-2,3-2.333,7s-3.333-3-4.333-4.667s6.666,11,1.333,21.666c0,0-7.666-12.666-8.333-9  c-0.667,3.667,16,23.668,16.333,25.001s10,11-2,9.333c-4-0.333-6.333-2.667-8.333-6s-6.333-10-6.333-10l-9.667-11L31.5,43.755  l-1.333,3.667l-2.333-2.333l2,4.667l0.333,6.333l-2.333,1c0,0,28,28.334,5.333,17.667c0,0-8.334,2.334,10.333,7  c0,0,1.333,7.666,17.333,8.666c0,0,12,13.001,15,25.334c0,0,3.333,26.667-12,6c0,0,3,7.333,1,7.333s-11.667-5.667-13-5.667  s-5.333,2-5.333,2s27,6.666,1.667,7c0,0-18,6.333-11-36c0,0-2.333,12.334-4.333,0.667c0,0-5.333,26.334,0,36.667  c0,0,5,6.332-2.333,6.666c0,0,9-1.334,18-4c0,0-6.667,13.334-10,13.334c0,0,3,0.666-0.333,2.666c0,0,1.333,2.666,5-2  s9-15.999,22-15.666c4.333,1.333,20.333,14.666-4,19.333c-7.333,3-35,13.001-32.667,14.667s16.333-8.334,14-3.334  s-10,19.334-10,19.334s9.333-21.333,7.333-5c1,1.333,25.333-45.333,8,0c0.333,1,4.333-6.001,5,3.333c0,0,0.333-35.332,25.333-24.666  c0.667,9-4.667,21.332-18.667,29.666c14.333-8.333,16.667-12,10,2c3.333-3.333,5-2.999,6,1.334c1.333-15,4-19.666,6.667-22  c2.667,7,3.667,9.999,4,11.666s-1.667-23.667-2-25s13-0.666,13-0.666s10,19.334,5.667,28c2.333-7.334-1.667-22.001,16,3.666  c0,0-12-13.333,7.333-8.333c-6.667-5.333-19-12.666-22.333-18.333s-4-13.333,6.333-6s16.667,10.332,17.667,9.666s-19.667-10,3.333-6  C116.5,151.756,92.167,146.422,126.833,132.089z"
   id="path3" />
<path
   fill="#5B9974"
   stroke="#000000"
   d="M85.125,136.589c-0.125-4.5,1.375-5.875,4.125-5.5s5.5,1.625,6.625,2.875s2.75,3.5,3,4.375  s0.375,1.75,0.5,2.625s0.5,1.75,0.125,2.5s-0.875,2.375-1.875,2.5s-1.625,0.375-2.75-0.25s-2.875-1.75-3.625-2.375  s-3.75-3.625-4.375-4.5S85.125,136.589,85.125,136.589z"
   id="path5" />
<path
   fill="#F9D566"
   stroke="#000000"
   d="M127.5,133.339c2.25-4.5,1.625-6,1.125-7.375s17.625-2,30,5c0,0,11.75,4,1.25,11.5  C159.875,142.464,140.375,145.339,127.5,133.339z"
   id="path7" />
<path
   fill="#F9D566"
   stroke="#000000"
   d="M168.375,145.839c0,0-3.625-1.75-2.5-5s2.125-3.25,3.125-3.625s6.75-0.25,7.25-0.25  s19.75,2,22.375,4.25s3.5,5,3.5,6.125s-0.25,4.75-3.75,4.75s-18.375-0.25-19-0.875S170.875,148.464,168.375,145.839z"
   id="path9" />
<path
   fill="#F9D566"
   stroke="#000000"
   d="M214.375,155.089c-6.625-2.125-9.5-4.625-9.25-7.25s0.75-3.625,1.875-4.25  s3.875-1.375,4.625-1.625s22.375-3.375,24.5-2.875s4.75,3,5,4.5s2.625,5-2.75,7.25s-9.375,3.75-13,4  S214.375,155.089,214.375,155.089z"
   id="path11" />
<path
   fill="#F9D566"
   stroke="#000000"
   d="M252.125,145.464c-6.009,0.376-7.875,1.125-7.75-3.375s-0.375-6.125,3.25-7.875  s13.875-8.5,15.5-9.25s6.875-2.375,9.25,0.75s2.125,4.5-0.375,7.25S256.125,145.214,252.125,145.464z"
   id="path13" />
<path
   fill="#F9D566"
   stroke="#000000"
   d="M274,122.839c-2.44-4.532-3.375-7-1.125-10.25s13.125-18.75,13.5-19.25  s4.625-2.375,8.375,0.875s4.5,5.5,1,10.25s-11.625,17.5-14.5,19S275.75,126.089,274,122.839z"
   id="path15" />
<ellipse
   fill="#E68B60"
   stroke="#B76328"
   cx="140"
   cy="130.964"
   rx="3.375"
   ry="2.125"
   id="ellipse17" />
<ellipse
   fill="#E68B60"
   stroke="#B76328"
   cx="181.875"
   cy="144.839"
   rx="3.375"
   ry="2.125"
   id="ellipse19" />
<ellipse
   fill="#E68B60"
   stroke="#B76328"
   cx="224.125"
   cy="147.839"
   rx="3.375"
   ry="2.125"
   id="ellipse21" />
<ellipse
   transform="matrix(0.912 -0.4103 0.4103 0.912 -32.3066 117.8699)"
   fill="#E68B60"
   stroke="#B76328"
   cx="258.5"
   cy="134.214"
   rx="3.375"
   ry="2.125"
   id="ellipse23" />
<ellipse
   transform="matrix(0.5422 -0.8402 0.8402 0.5422 39.543 287.5071)"
   fill="#E68B60"
   stroke="#B76328"
   cx="283.625"
   cy="107.464"
   rx="3.375"
   ry="2.125"
   id="ellipse25" />
<path
   fill="#AFA3CF"
   stroke="#000000"
   d="M164.25,137.089c2.25,0.5,3.75,0.5,3.75,0.5l-2.125,3.25l-4.125-0.125L164.25,137.089z"
   id="path27" />
<path
   fill="#AFA3CF"
   stroke="#000000"
   d="M201.75,145.214l4.125-0.25L205,149.089h-3.125  C201.875,149.089,202.125,145.714,201.75,145.214z"
   id="path29" />
<path
   fill="#AFA3CF"
   stroke="#000000"
   d="M240.25,141.589c0,0,3.375-2.5,3.625-3.125s0.375,5,0.5,5.375s-2.25,0.875-2.25,0.875  L240.25,141.589z"
   id="path31" />
<path
   fill="#AFA3CF"
   stroke="#000000"
   d="M269.625,123.714l2.75-3.875l2.875,4.375l-2,1.875  C273.25,126.089,271.5,123.964,269.625,123.714z"
   id="path33" />
<path
   fill="#AFA3CF"
   stroke="#000000"
   d="M291.167,92.256c0.041-0.09,0.082-0.18,0.123-0.27c3.275-7.251,5.894-14.772,7.21-18.064  c1.333-3.333,1.833-11.166,1.333-12s-2-6.5-5.833-5.333s-2.501,0-3.667,1.833s-3.999,2-4.166,0.333s0.5-2.167,3.333-2.667  s1.333-2.5-0.667-4.333s-7.334-1.833-3.5-4.333s4-2.834,5.5-1.667s-3.167,2,1,4.5s7.166,4,8.5,4.167s2.5,0.333,2.5-1.167  s1.667-3,2.667-3.833s1.5-4.167,1.5-4.167s-3.5-3.166-0.5-2.833s4.5,0.5,3,2.333c0.333,0.5-0.166,2,0.167,2.5S315,47.089,315,47.089  s4.5-0.333,3,2.5c-0.333,0.833-1.5,2-3.5,0.667c-1.833,0.333-3.667-0.333-4.167-0.167s-7.167,3.167-6.5,18.167  c4.5-2.5,17.668-10.334,19.334-11s9.666-9.833,9.833-10.667s-1.666-3.333-1.333-4s3.499-2,4.166-2.167s1.501-2,2.334,0.167  s0.667,2.167,0,2.667s-1.166,0.833-1,1.833s0.333,1.667,0.833,1.5s0.833,0.833,0.833,0.833l-6.666,5.333c0,0,6.167-1.167,7-2  s2.5-1.667,2.833-0.5s0.667,0.834,0,1.667s-1.833,1.666-4,2.333s-3.667,1.333-5,1.5s-3.833,1-3.833,1s-1.333,0.333-4,2.5  s-4.5,3.167-6.167,4.167s-2.833,1.333-4.5,2.5s-4.5,2.5-4.5,2.5s-2.501,0.834-3.167,1.667s-1.832,2.167-2.166,2.667  s-1,2.166-1,2.166l-0.667,3l-2.167,2.5l-0.5,2.334c0,0,4.166,0.5,5,0.5s1.5-0.001,2.667-0.834s1-0.334,2-1.5  s2.167-2.333,2.667-3.333s1.666-2.167,2.666-3s2.333-2.001,3-2.167s3-0.833,3.5-1.333s1.001,0.001,1.334-0.833s1.334-2.5,1.5-3.333  s0.166-1.167,0.833-1.167s1.167-0.333,1.5,0.5s1.001,0.667,0.667,1.833S327,69.922,327,69.922l0.167,0.334c0,0,1.5,0.499,2-0.334  s1.333-1.333,1.333-1.333l1-1.333l0.833-1.5l0.834-0.667l1.5-0.333c0,0,0.5,0,0.833,1.167s0.5,1.667,0.5,2.167s-2,2-2,2  l-0.667,0.667c0,0-1.5,0.332-2,0.666s-2.166,0.834-2.166,0.834l-2.334,0.166l-1.666,0.834l-0.667,0.5v1c0,0,0.667,0.834,1.167,1  s3.5,0,3.5,0l0.666-0.167l-1,1.333l-3.333,0.667c0,0-0.834,0.166-1.667,0.333s-3,0.5-3,0.5s-1.834,0.834-2.5,1.334s-2,0.167-0.833,1  s0.333,1.334,4,1s1.334-0.667,4.667,0.333s4,0.667,6,1.667s-1.334,0.332,4.333,2.166s5.499,1.666,9.333,3s3.333-0.5,4.5,2  s6.667,2.833,1.167,2.5s-4.499-0.333-7.333-1.333s-2.5-1.167-6.667-2.667s-6.667-3.667-2.167,1.5s5.5,5.667,6.167,10.167  s1,14.167,5,14.5s7.501,1.333,5.167,3.333s-6.168,0.668-7.334-1.166s-3.666-3.5-4.166-6s0.334-4.501-0.5-4.834  s-0.834-0.332-1.667,0.334s-0.333-0.834-1.5,1.833s-1.501,3.167-2.167,3.5s-4.166,0.834-4.166-0.833s0.5-2.168,1.333-2.334  s-0.667,1.333,2-1s3.834-4.166,4.167-5.166s2.166-5.5-5.667-11.167s-13.666-9.166-20.833-5.833c2,3,5.667,5.333,5.5,11.333  c0,0.667,2.832,3-0.334,4c-0.5,0.167-3.5,0.333-1.833-4c0-2.167,0.501-3.499-3.833-7.333c0.166,3.666-0.168,7.332,0.166,8.666  s3.168,4.501-0.666,5.834c-2.167-0.5-3.834-1.833-1.334-6c0-3.834-3.999-20.333-14.166-5  C292.333,93.089,291.167,92.256,291.167,92.256z"
   id="path35" />
<path
   fill="#AFA3CF"
   stroke="#000000"
   d="M367.333,131.756"
   id="path37" />
<line
   stroke="#000000"
   x1="142.5"
   y1="78.089"
   x2="111.5"
   y2="118.589"
   id="line39" />
<line
   stroke="#000000"
   x1="74"
   y1="18.589"
   x2="75.5"
   y2="65.089"
   id="line41" />
<line
   fill="none"
   stroke="#000000"
   x1="89.5"
   y1="142.089"
   x2="43.5"
   y2="200.089"
   id="line43" />
<line
   fill="none"
   stroke="#000000"
   x1="127.5"
   y1="133.339"
   x2="143"
   y2="162.089"
   id="line45" />
<line
   fill="none"
   stroke="#000000"
   x1="227"
   y1="155.089"
   x2="227.5"
   y2="183.589"
   id="line47" />
<line
   fill="none"
   stroke="#000000"
   x1="259.372"
   y1="136.151"
   x2="304"
   y2="159.589"
   id="line49" />
<line
   fill="none"
   stroke="#000000"
   x1="271"
   y1="121.089"
   x2="255"
   y2="69.089"
   id="line51" />
<line
   fill="none"
   stroke="#000000"
   x1="350.333"
   y1="90.922"
   x2="349"
   y2="35.589"
   id="line53" />
<text
   transform="matrix(1 0 0 1 42 12.0889)"
   font-family="'Verdana'"
   font-size="14"
   id="text55">Dendrite</text>

<text
   font-size="14"
   id="text57"
   font-family="'Verdana'"
   transform="matrix(1 0 0 1 138 76.5889)">Cell body</text>

<text
   transform="matrix(1 0 0 1 224.8135 49.5889)"
   id="text59"><tspan
     x="0"
     y="0"
     font-family="'Verdana'"
     font-size="14"
     id="tspan61">Node of</tspan><tspan
     x="0.465"
     y="16.8"
     font-family="'Verdana'"
     font-size="14"
     id="tspan63">Ranvier</tspan></text>

<text
   transform="matrix(1 0 0 1 298.1777 26.5889)"
   font-family="'Verdana'"
   font-size="14"
   id="text65">Axon Terminal</text>

<text
   transform="matrix(1 0 0 1 308 171.5889)"
   font-family="'Verdana'"
   font-size="14"
   id="text67">Schwann cell</text>

<text
   transform="matrix(1 0 0 1 219.5 198.0889)"
   font-family="'Verdana'"
   font-size="14"
   id="text69">Myelin sheath</text>

<text
   transform="matrix(1 0 0 1 140 176.0889)"
   font-family="'Verdana'"
   font-size="14"
   id="text71">Axon</text>

<text
   transform="matrix(1 0 0 1 0 212.0889)"
   font-family="'Verdana'"
   font-size="14"
   id="text73">Nucleus</text>

</svg>