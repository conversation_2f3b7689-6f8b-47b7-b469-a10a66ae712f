{"cells": [{"cell_type": "markdown", "id": "caf5f90e", "metadata": {"origin_pos": 0}, "source": ["# 模型选择、欠拟合和过拟合\n", ":label:`sec_model_selection`\n", "\n", "作为机器学习科学家，我们的目标是发现*模式*（pattern）。\n", "但是，我们如何才能确定模型是真正发现了一种泛化的模式，\n", "而不是简单地记住了数据呢？\n", "例如，我们想要在患者的基因数据与痴呆状态之间寻找模式，\n", "其中标签是从集合$\\{\\text{痴呆}, \\text{轻度认知障碍}, \\text{健康}\\}$中提取的。\n", "因为基因可以唯一确定每个个体（不考虑双胞胎），\n", "所以在这个任务中是有可能记住整个数据集的。\n", "\n", "我们不想让模型只会做这样的事情：“那是鲍勃！我记得他！他有痴呆症！”。\n", "原因很简单：当我们将来部署该模型时，模型需要判断从未见过的患者。\n", "只有当模型真正发现了一种泛化模式时，才会作出有效的预测。\n", "\n", "更正式地说，我们的目标是发现某些模式，\n", "这些模式捕捉到了我们训练集潜在总体的规律。\n", "如果成功做到了这点，即使是对以前从未遇到过的个体，\n", "模型也可以成功地评估风险。\n", "如何发现可以泛化的模式是机器学习的根本问题。\n", "\n", "困难在于，当我们训练模型时，我们只能访问数据中的小部分样本。\n", "最大的公开图像数据集包含大约一百万张图像。\n", "而在大部分时候，我们只能从数千或数万个数据样本中学习。\n", "在大型医院系统中，我们可能会访问数十万份医疗记录。\n", "当我们使用有限的样本时，可能会遇到这样的问题：\n", "当收集到更多的数据时，会发现之前找到的明显关系并不成立。\n", "\n", "将模型在训练数据上拟合的比在潜在分布中更接近的现象称为*过拟合*（overfitting），\n", "用于对抗过拟合的技术称为*正则化*（regularization）。\n", "在前面的章节中，有些读者可能在用Fashion-MNIST数据集做实验时已经观察到了这种过拟合现象。\n", "在实验中调整模型架构或超参数时会发现：\n", "如果有足够多的神经元、层数和训练迭代周期，\n", "模型最终可以在训练集上达到完美的精度，此时测试集的准确性却下降了。\n", "\n", "## 训练误差和泛化误差\n", "\n", "为了进一步讨论这一现象，我们需要了解训练误差和泛化误差。\n", "*训练误差*（training error）是指，\n", "模型在训练数据集上计算得到的误差。\n", "*泛化误差*（generalization error）是指，\n", "模型应用在同样从原始样本的分布中抽取的无限多数据样本时，模型误差的期望。\n", "\n", "问题是，我们永远不能准确地计算出泛化误差。\n", "这是因为无限多的数据样本是一个虚构的对象。\n", "在实际中，我们只能通过将模型应用于一个独立的测试集来估计泛化误差，\n", "该测试集由随机选取的、未曾在训练集中出现的数据样本构成。\n", "\n", "下面的三个思维实验将有助于更好地说明这种情况。\n", "假设一个大学生正在努力准备期末考试。\n", "一个勤奋的学生会努力做好练习，并利用往年的考试题目来测试自己的能力。\n", "尽管如此，在过去的考试题目上取得好成绩并不能保证他会在真正考试时发挥出色。\n", "例如，学生可能试图通过死记硬背考题的答案来做准备。\n", "他甚至可以完全记住过去考试的答案。\n", "另一名学生可能会通过试图理解给出某些答案的原因来做准备。\n", "在大多数情况下，后者会考得更好。\n", "\n", "类似地，考虑一个简单地使用查表法来回答问题的模型。\n", "如果允许的输入集合是离散的并且相当小，\n", "那么也许在查看许多训练样本后，该方法将执行得很好。\n", "但当这个模型面对从未见过的例子时，它表现的可能比随机猜测好不到哪去。\n", "这是因为输入空间太大了，远远不可能记住每一个可能的输入所对应的答案。\n", "例如，考虑$28\\times28$的灰度图像。\n", "如果每个像素可以取$256$个灰度值中的一个，\n", "则有$256^{784}$个可能的图像。\n", "这意味着指甲大小的低分辨率灰度图像的数量比宇宙中的原子要多得多。\n", "即使我们可能遇到这样的数据，我们也不可能存储整个查找表。\n", "\n", "最后，考虑对掷硬币的结果（类别0：正面，类别1：反面）进行分类的问题。\n", "假设硬币是公平的，无论我们想出什么算法，泛化误差始终是$\\frac{1}{2}$。\n", "然而，对于大多数算法，我们应该期望训练误差会更低（取决于运气）。\n", "考虑数据集{0，1，1，1，0，1}。\n", "我们的算法不需要额外的特征，将倾向于总是预测*多数类*，\n", "从我们有限的样本来看，它似乎是1占主流。\n", "在这种情况下，总是预测类1的模型将产生$\\frac{1}{3}$的误差，\n", "这比我们的泛化误差要好得多。\n", "当我们逐渐增加数据量，正面比例明显偏离$\\frac{1}{2}$的可能性将会降低，\n", "我们的训练误差将与泛化误差相匹配。\n", "\n", "### 统计学习理论\n", "\n", "由于泛化是机器学习中的基本问题，\n", "许多数学家和理论家毕生致力于研究描述这一现象的形式理论。\n", "在[同名定理（eponymous theorem）](https://en.wikipedia.org/wiki/Glivenko%E2%80%93C<PERSON><PERSON>_theorem)中，\n", "格里文科和坎特利推导出了训练误差收敛到泛化误差的速率。\n", "在一系列开创性的论文中，\n", "[Vap<PERSON><PERSON>](https://en.wikipedia.org/wiki/Vapnik%E2%80%93Cher<PERSON><PERSON><PERSON>_theory)\n", "将这一理论扩展到更一般种类的函数。\n", "这项工作为统计学习理论奠定了基础。\n", "\n", "在我们目前已探讨、并将在之后继续探讨的监督学习情景中，\n", "我们假设训练数据和测试数据都是从相同的分布中独立提取的。\n", "这通常被称为*独立同分布假设*（i.i.d. assumption），\n", "这意味着对数据进行采样的过程没有进行“记忆”。\n", "换句话说，抽取的第2个样本和第3个样本的相关性，\n", "并不比抽取的第2个样本和第200万个样本的相关性更强。\n", "\n", "要成为一名优秀的机器学习科学家需要具备批判性思考能力。\n", "假设是存在漏洞的，即很容易找出假设失效的情况。\n", "如果我们根据从加州大学旧金山分校医学中心的患者数据训练死亡风险预测模型，\n", "并将其应用于马萨诸塞州综合医院的患者数据，结果会怎么样？\n", "这两个数据的分布可能不完全一样。\n", "此外，抽样过程可能与时间有关。\n", "比如当我们对微博的主题进行分类时，\n", "新闻周期会使得正在讨论的话题产生时间依赖性，从而违反独立性假设。\n", "\n", "有时候我们即使轻微违背独立同分布假设，模型仍将继续运行得非常好。\n", "比如，我们有许多有用的工具已经应用于现实，如人脸识别、语音识别和语言翻译。\n", "毕竟，几乎所有现实的应用都至少涉及到一些违背独立同分布假设的情况。\n", "\n", "有些违背独立同分布假设的行为肯定会带来麻烦。\n", "比如，我们试图只用来自大学生的人脸数据来训练一个人脸识别系统，\n", "然后想要用它来监测疗养院中的老人。\n", "这不太可能有效，因为大学生看起来往往与老年人有很大的不同。\n", "\n", "在接下来的章节中，我们将讨论因违背独立同分布假设而引起的问题。\n", "目前，即使认为独立同分布假设是理所当然的，理解泛化性也是一个困难的问题。\n", "此外，能够解释深层神经网络泛化性能的理论基础，\n", "也仍在继续困扰着学习理论领域最伟大的学者们。\n", "\n", "当我们训练模型时，我们试图找到一个能够尽可能拟合训练数据的函数。\n", "但是如果它执行地“太好了”，而不能对看不见的数据做到很好泛化，就会导致过拟合。\n", "这种情况正是我们想要避免或控制的。\n", "深度学习中有许多启发式的技术旨在防止过拟合。\n", "\n", "### 模型复杂性\n", "\n", "当我们有简单的模型和大量的数据时，我们期望泛化误差与训练误差相近。\n", "当我们有更复杂的模型和更少的样本时，我们预计训练误差会下降，但泛化误差会增大。\n", "模型复杂性由什么构成是一个复杂的问题。\n", "一个模型是否能很好地泛化取决于很多因素。\n", "例如，具有更多参数的模型可能被认为更复杂，\n", "参数有更大取值范围的模型可能更为复杂。\n", "通常对于神经网络，我们认为需要更多训练迭代的模型比较复杂，\n", "而需要*早停*（early stopping）的模型（即较少训练迭代周期）就不那么复杂。\n", "\n", "我们很难比较本质上不同大类的模型之间（例如，决策树与神经网络）的复杂性。\n", "就目前而言，一条简单的经验法则相当有用：\n", "统计学家认为，能够轻松解释任意事实的模型是复杂的，\n", "而表达能力有限但仍能很好地解释数据的模型可能更有现实用途。\n", "在哲学上，这与波普尔的科学理论的可证伪性标准密切相关：\n", "如果一个理论能拟合数据，且有具体的测试可以用来证明它是错误的，那么它就是好的。\n", "这一点很重要，因为所有的统计估计都是*事后归纳*。\n", "也就是说，我们在观察事实之后进行估计，因此容易受到相关谬误的影响。\n", "目前，我们将把哲学放在一边，坚持更切实的问题。\n", "\n", "本节为了给出一些直观的印象，我们将重点介绍几个倾向于影响模型泛化的因素。\n", "\n", "1. 可调整参数的数量。当可调整参数的数量（有时称为*自由度*）很大时，模型往往更容易过拟合。\n", "1. 参数采用的值。当权重的取值范围较大时，模型可能更容易过拟合。\n", "1. 训练样本的数量。即使模型很简单，也很容易过拟合只包含一两个样本的数据集。而过拟合一个有数百万个样本的数据集则需要一个极其灵活的模型。\n", "\n", "## 模型选择\n", "\n", "在机器学习中，我们通常在评估几个候选模型后选择最终的模型。\n", "这个过程叫做*模型选择*。\n", "有时，需要进行比较的模型在本质上是完全不同的（比如，决策树与线性模型）。\n", "又有时，我们需要比较不同的超参数设置下的同一类模型。\n", "\n", "例如，训练多层感知机模型时，我们可能希望比较具有\n", "不同数量的隐藏层、不同数量的隐藏单元以及不同的激活函数组合的模型。\n", "为了确定候选模型中的最佳模型，我们通常会使用验证集。\n", "\n", "### 验证集\n", "\n", "原则上，在我们确定所有的超参数之前，我们不希望用到测试集。\n", "如果我们在模型选择过程中使用测试数据，可能会有过拟合测试数据的风险，那就麻烦大了。\n", "如果我们过拟合了训练数据，还可以在测试数据上的评估来判断过拟合。\n", "但是如果我们过拟合了测试数据，我们又该怎么知道呢？\n", "\n", "因此，我们决不能依靠测试数据进行模型选择。\n", "然而，我们也不能仅仅依靠训练数据来选择模型，因为我们无法估计训练数据的泛化误差。\n", "\n", "在实际应用中，情况变得更加复杂。\n", "虽然理想情况下我们只会使用测试数据一次，\n", "以评估最好的模型或比较一些模型效果，但现实是测试数据很少在使用一次后被丢弃。\n", "我们很少能有充足的数据来对每一轮实验采用全新测试集。\n", "\n", "解决此问题的常见做法是将我们的数据分成三份，\n", "除了训练和测试数据集之外，还增加一个*验证数据集*（validation dataset），\n", "也叫*验证集*（validation set）。\n", "但现实是验证数据和测试数据之间的边界模糊得令人担忧。\n", "除非另有明确说明，否则在这本书的实验中，\n", "我们实际上是在使用应该被正确地称为训练数据和验证数据的数据集，\n", "并没有真正的测试数据集。\n", "因此，书中每次实验报告的准确度都是验证集准确度，而不是测试集准确度。\n", "\n", "### $K$折交叉验证\n", "\n", "当训练数据稀缺时，我们甚至可能无法提供足够的数据来构成一个合适的验证集。\n", "这个问题的一个流行的解决方案是采用$K$*折交叉验证*。\n", "这里，原始训练数据被分成$K$个不重叠的子集。\n", "然后执行$K$次模型训练和验证，每次在$K-1$个子集上进行训练，\n", "并在剩余的一个子集（在该轮中没有用于训练的子集）上进行验证。\n", "最后，通过对$K$次实验的结果取平均来估计训练和验证误差。\n", "\n", "## 欠拟合还是过拟合？\n", "\n", "当我们比较训练和验证误差时，我们要注意两种常见的情况。\n", "首先，我们要注意这样的情况：训练误差和验证误差都很严重，\n", "但它们之间仅有一点差距。\n", "如果模型不能降低训练误差，这可能意味着模型过于简单（即表达能力不足），\n", "无法捕获试图学习的模式。\n", "此外，由于我们的训练和验证误差之间的*泛化误差*很小，\n", "我们有理由相信可以用一个更复杂的模型降低训练误差。\n", "这种现象被称为*欠拟合*（underfitting）。\n", "\n", "另一方面，当我们的训练误差明显低于验证误差时要小心，\n", "这表明严重的*过拟合*（overfitting）。\n", "注意，*过拟合*并不总是一件坏事。\n", "特别是在深度学习领域，众所周知，\n", "最好的预测模型在训练数据上的表现往往比在保留（验证）数据上好得多。\n", "最终，我们通常更关心验证误差，而不是训练误差和验证误差之间的差距。\n", "\n", "是否过拟合或欠拟合可能取决于模型复杂性和可用训练数据集的大小，\n", "这两个点将在下面进行讨论。\n", "\n", "### 模型复杂性\n", "\n", "为了说明一些关于过拟合和模型复杂性的经典直觉，\n", "我们给出一个多项式的例子。\n", "给定由单个特征$x$和对应实数标签$y$组成的训练数据，\n", "我们试图找到下面的$d$阶多项式来估计标签$y$。\n", "\n", "$$\\hat{y}= \\sum_{i=0}^d x^i w_i$$\n", "\n", "这只是一个线性回归问题，我们的特征是$x$的幂给出的，\n", "模型的权重是$w_i$给出的，偏置是$w_0$给出的\n", "（因为对于所有的$x$都有$x^0 = 1$）。\n", "由于这只是一个线性回归问题，我们可以使用平方误差作为我们的损失函数。\n", "\n", "高阶多项式函数比低阶多项式函数复杂得多。\n", "高阶多项式的参数较多，模型函数的选择范围较广。\n", "因此在固定训练数据集的情况下，\n", "高阶多项式函数相对于低阶多项式的训练误差应该始终更低（最坏也是相等）。\n", "事实上，当数据样本包含了$x$的不同值时，\n", "函数阶数等于数据样本数量的多项式函数可以完美拟合训练集。\n", "在 :numref:`fig_capacity_vs_error`中，\n", "我们直观地描述了多项式的阶数和欠拟合与过拟合之间的关系。\n", "\n", "\n", "![模型复杂度对欠拟合和过拟合的影响](../img/capacity-vs-error.svg)\n", ":label:`fig_capacity_vs_error`\n", "\n", "### 数据集大小\n", "\n", "另一个重要因素是数据集的大小。\n", "训练数据集中的样本越少，我们就越有可能（且更严重地）过拟合。\n", "随着训练数据量的增加，泛化误差通常会减小。\n", "此外，一般来说，更多的数据不会有什么坏处。\n", "对于固定的任务和数据分布，模型复杂性和数据集大小之间通常存在关系。\n", "给出更多的数据，我们可能会尝试拟合一个更复杂的模型。\n", "能够拟合更复杂的模型可能是有益的。\n", "如果没有足够的数据，简单的模型可能更有用。\n", "对于许多任务，深度学习只有在有数千个训练样本时才优于线性模型。\n", "从一定程度上来说，深度学习目前的生机要归功于\n", "廉价存储、互联设备以及数字化经济带来的海量数据集。\n", "\n", "## 多项式回归\n", "\n", "我们现在可以(**通过多项式拟合来探索这些概念**)。\n"]}, {"cell_type": "code", "execution_count": 1, "id": "40c36b94", "metadata": {"execution": {"iopub.execute_input": "2022-12-07T17:01:54.120880Z", "iopub.status.busy": "2022-12-07T17:01:54.120324Z", "iopub.status.idle": "2022-12-07T17:01:56.633520Z", "shell.execute_reply": "2022-12-07T17:01:56.632657Z"}, "origin_pos": 2, "tab": ["pytorch"]}, "outputs": [], "source": ["import math\n", "import numpy as np\n", "import torch\n", "from torch import nn\n", "from d2l import torch as d2l"]}, {"cell_type": "markdown", "id": "8902acd7", "metadata": {"origin_pos": 5}, "source": ["### 生成数据集\n", "\n", "给定$x$，我们将[**使用以下三阶多项式来生成训练和测试数据的标签：**]\n", "\n", "(**$$y = 5 + 1.2x - 3.4\\frac{x^2}{2!} + 5.6 \\frac{x^3}{3!} + \\epsilon \\text{ where }\n", "\\epsilon \\sim \\mathcal{N}(0, 0.1^2).$$**)\n", "\n", "噪声项$\\epsilon$服从均值为0且标准差为0.1的正态分布。\n", "在优化的过程中，我们通常希望避免非常大的梯度值或损失值。\n", "这就是我们将特征从$x^i$调整为$\\frac{x^i}{i!}$的原因，\n", "这样可以避免很大的$i$带来的特别大的指数值。\n", "我们将为训练集和测试集各生成100个样本。\n"]}, {"cell_type": "code", "execution_count": 31, "id": "35ab17ae", "metadata": {"execution": {"iopub.execute_input": "2022-12-07T17:01:56.637671Z", "iopub.status.busy": "2022-12-07T17:01:56.637103Z", "iopub.status.idle": "2022-12-07T17:01:56.645127Z", "shell.execute_reply": "2022-12-07T17:01:56.644345Z"}, "origin_pos": 6, "tab": ["pytorch"]}, "outputs": [{"data": {"text/plain": ["array([[ 1.00000000e+00, -1.64147980e-01,  2.69445595e-02,\n", "        -4.42289502e-03,  7.26009284e-04, -1.19172958e-04,\n", "         1.95620003e-05, -3.21106285e-06,  5.27089481e-07,\n", "        -8.65206738e-08,  1.42021939e-08, -2.33126144e-09,\n", "         3.82671857e-10, -6.28148124e-11,  1.03109246e-11,\n", "        -1.69251745e-12,  2.77823321e-13, -4.56041370e-14,\n", "         7.48582699e-15, -1.22878338e-15],\n", "       [ 1.00000000e+00, -2.01377063e+00,  4.05527214e+00,\n", "        -8.16638792e+00,  1.64452321e+01, -3.31169254e+01,\n", "         6.66898916e+01, -1.34298145e+02,  2.70445659e+02,\n", "        -5.44615525e+02,  1.09673075e+03, -2.20856416e+03,\n", "         4.44754164e+03, -8.95632872e+03,  1.80359917e+04,\n", "        -3.63203503e+04,  7.31408546e+04, -1.47288905e+05,\n", "         2.96606070e+05, -5.97296591e+05],\n", "       [ 1.00000000e+00,  4.12320228e-02,  1.70007970e-03,\n", "         7.00977251e-05,  2.89027100e-06,  1.19171720e-07,\n", "         4.91369106e-09,  2.02601422e-10,  8.35366645e-12,\n", "         3.44438565e-13,  1.42018988e-14,  5.85573014e-16,\n", "         2.41443599e-17,  9.95520796e-19,  4.10473362e-20,\n", "         1.69246470e-21,  6.97837431e-23,  2.87732489e-24,\n", "         1.18637925e-25,  4.89168164e-27]])"]}, "execution_count": 31, "metadata": {}, "output_type": "execute_result"}], "source": ["max_degree = 20  # 多项式的最大阶数\n", "n_train, n_test = 100, 100  # 训练和测试数据集大小\n", "true_w = np.zeros(max_degree)  # 分配大量的空间\n", "true_w[0:4] = np.array([5, 1.2, -3.4, 5.6])\n", "\n", "features = np.random.normal(size=(n_train + n_test, 1))\n", "np.random.shuffle(features)\n", "poly_features = np.power(features, np.arange(max_degree).reshape(1, -1))\n", "poly_features[:3,:]\n", "for i in range(max_degree):\n", "    poly_features[:, i] /= math.gamma(i + 1)  # gamma(n)=(n-1)!\n", "# labels的维度:(n_train+n_test,)\n", "labels = np.dot(poly_features, true_w)\n", "labels += np.random.normal(scale=0.1, size=labels.shape)"]}, {"cell_type": "markdown", "id": "ea0c4ad2", "metadata": {"origin_pos": 7}, "source": ["同样，存储在`poly_features`中的单项式由gamma函数重新缩放，\n", "其中$\\Gamma(n)=(n-1)!$。\n", "从生成的数据集中[**查看一下前2个样本**]，\n", "第一个值是与偏置相对应的常量特征。\n"]}, {"cell_type": "code", "execution_count": 3, "id": "ef3be7f8", "metadata": {"execution": {"iopub.execute_input": "2022-12-07T17:01:56.648429Z", "iopub.status.busy": "2022-12-07T17:01:56.647994Z", "iopub.status.idle": "2022-12-07T17:01:56.669697Z", "shell.execute_reply": "2022-12-07T17:01:56.668935Z"}, "origin_pos": 8, "tab": ["pytorch"]}, "outputs": [], "source": ["# NumPy ndarray转换为tensor\n", "true_w, features, poly_features, labels = [torch.tensor(x, dtype=\n", "    torch.float32) for x in [true_w, features, poly_features, labels]]"]}, {"cell_type": "code", "execution_count": 19, "id": "012dc12f", "metadata": {"execution": {"iopub.execute_input": "2022-12-07T17:01:56.672992Z", "iopub.status.busy": "2022-12-07T17:01:56.672555Z", "iopub.status.idle": "2022-12-07T17:01:56.682143Z", "shell.execute_reply": "2022-12-07T17:01:56.681334Z"}, "origin_pos": 9, "tab": ["pytorch"]}, "outputs": [{"data": {"text/plain": ["(tensor([[ 0.3471],\n", "         [-0.1976],\n", "         [-1.8929],\n", "         [-0.2185],\n", "         [ 0.0631]]),\n", " tensor([[ 1.0000e+00,  3.4706e-01,  6.0225e-02,  6.9672e-03,  6.0451e-04,\n", "           4.1960e-05,  2.4271e-06,  1.2034e-07,  5.2205e-09,  2.0131e-10,\n", "           6.9868e-12,  2.2044e-13,  6.3755e-15,  1.7020e-16,  4.2194e-18,\n", "           9.7625e-20,  2.1176e-21,  4.3231e-23,  8.3355e-25,  1.5226e-26],\n", "         [ 1.0000e+00, -1.9760e-01,  1.9524e-02, -1.2860e-03,  6.3528e-05,\n", "          -2.5107e-06,  8.2687e-08, -2.3342e-09,  5.7655e-11, -1.2659e-12,\n", "           2.5014e-14, -4.4935e-16,  7.3994e-18, -1.1247e-19,  1.5875e-21,\n", "          -2.0913e-23,  2.5828e-25, -3.0022e-27,  3.2958e-29, -3.4277e-31]]),\n", " tensor([5.2525, 4.7556]))"]}, "execution_count": 19, "metadata": {}, "output_type": "execute_result"}], "source": ["features[:5], poly_features[:2, :], labels[:2]"]}, {"cell_type": "markdown", "id": "70237741", "metadata": {"origin_pos": 10}, "source": ["### 对模型进行训练和测试\n", "\n", "首先让我们[**实现一个函数来评估模型在给定数据集上的损失**]。\n"]}, {"cell_type": "code", "execution_count": 8, "id": "9cea0962", "metadata": {"execution": {"iopub.execute_input": "2022-12-07T17:01:56.685564Z", "iopub.status.busy": "2022-12-07T17:01:56.685120Z", "iopub.status.idle": "2022-12-07T17:01:56.690488Z", "shell.execute_reply": "2022-12-07T17:01:56.689729Z"}, "origin_pos": 12, "tab": ["pytorch"]}, "outputs": [], "source": ["def evaluate_loss(net, data_iter, loss):  #@save\n", "    \"\"\"评估给定数据集上模型的损失\"\"\"\n", "    metric = d2l.Accumulator(2)  # 损失的总和,样本数量\n", "    for X, y in data_iter:\n", "        out = net(X)\n", "        y = y.reshape(out.shape)\n", "        l = loss(out, y)\n", "        metric.add(l.sum(), l.numel())\n", "    return metric[0] / metric[1]"]}, {"cell_type": "markdown", "id": "c488e62f", "metadata": {"origin_pos": 14}, "source": ["现在[**定义训练函数**]。\n"]}, {"cell_type": "code", "execution_count": 9, "id": "f5cdb221", "metadata": {"execution": {"iopub.execute_input": "2022-12-07T17:01:56.693754Z", "iopub.status.busy": "2022-12-07T17:01:56.693324Z", "iopub.status.idle": "2022-12-07T17:01:56.701367Z", "shell.execute_reply": "2022-12-07T17:01:56.700517Z"}, "origin_pos": 16, "tab": ["pytorch"]}, "outputs": [], "source": ["def train(train_features, test_features, train_labels, test_labels,\n", "          num_epochs=400):\n", "    loss = nn.MSELoss(reduction='none')\n", "    input_shape = train_features.shape[-1]\n", "    # 不设置偏置，因为我们已经在多项式中实现了它\n", "    net = nn.Sequential(nn.Linear(input_shape, 1, bias=False))\n", "    batch_size = min(10, train_labels.shape[0])\n", "    train_iter = d2l.load_array((train_features, train_labels.reshape(-1,1)),\n", "                                batch_size)\n", "    test_iter = d2l.load_array((test_features, test_labels.reshape(-1,1)),\n", "                               batch_size, is_train=False)\n", "    trainer = torch.optim.SGD(net.parameters(), lr=0.01)\n", "    animator = d2l.Animator(xlabel='epoch', ylabel='loss', yscale='log',\n", "                            xlim=[1, num_epochs], ylim=[1e-3, 1e2],\n", "                            legend=['train', 'test'])\n", "    for epoch in range(num_epochs):\n", "        d2l.train_epoch_ch3(net, train_iter, loss, trainer)\n", "        if epoch == 0 or (epoch + 1) % 20 == 0:\n", "            animator.add(epoch + 1, (evaluate_loss(net, train_iter, loss),\n", "                                     evaluate_loss(net, test_iter, loss)))\n", "    print('weight:', net[0].weight.data.numpy())"]}, {"cell_type": "markdown", "id": "b547eeaf", "metadata": {"origin_pos": 19}, "source": ["### [**三阶多项式函数拟合(正常)**]\n", "\n", "我们将首先使用三阶多项式函数，它与数据生成函数的阶数相同。\n", "结果表明，该模型能有效降低训练损失和测试损失。\n", "学习到的模型参数也接近真实值$w = [5, 1.2, -3.4, 5.6]$。\n"]}, {"cell_type": "code", "execution_count": null, "id": "b182c32c", "metadata": {"execution": {"iopub.execute_input": "2022-12-07T17:01:56.704680Z", "iopub.status.busy": "2022-12-07T17:01:56.704165Z", "iopub.status.idle": "2022-12-07T17:02:09.833316Z", "shell.execute_reply": "2022-12-07T17:02:09.832487Z"}, "origin_pos": 20, "tab": ["pytorch"]}, "outputs": [{"data": {"text/plain": ["tensor([  5.2525,   4.7556,  -9.9226,   4.7046,   5.1517,   4.9586,   3.1283,\n", "          4.4763, -46.0565,   4.4798,  -2.4211,  -2.4192,   1.8902,   6.9247,\n", "        -27.1467,   4.6533,   5.3993,   3.6986,   5.8905,   5.4453,   3.9996,\n", "          5.0038,   5.1641,   5.2867,   4.5927,  -1.9574,   4.9416,   2.5697,\n", "          4.8717,   1.9128,   5.1790,   5.2483,   4.4448,  11.6479,   4.7805,\n", "        -13.3823,   5.8154,   5.3909,   5.5756,   3.8376,   2.2850,  -2.6869,\n", "          4.3159,   4.2540,   4.9239,   3.7304,   5.1780,   5.3504,   4.2668,\n", "        -21.0499,   4.0744,  -2.3556, -20.1887,   5.4686,   5.2724,   5.3007,\n", "          4.5853,   3.8564,  -0.8333,   5.1143,   5.2995,  -4.3871,   5.2272,\n", "          6.1882,   5.2614, -11.3072,   4.8649,   5.1847,   5.3327,   4.9003,\n", "          1.2327,   7.8298,  17.8313,   3.8867,  -5.9151,   4.5853,   5.8736,\n", "          7.4912,  -4.3807,  -1.5029,   7.1304,   5.2380,   5.3470,   5.0553,\n", "          3.3927,   4.8885,   1.9349,  -2.9047,   2.5767,  11.6551,   4.4333,\n", "          4.0433,   4.6461,  -5.4643,   2.1802,   5.8211,   4.7823,   5.2683,\n", "          5.3706,  16.2575])"]}, "execution_count": 32, "metadata": {}, "output_type": "execute_result"}, {"ename": "", "evalue": "", "output_type": "error", "traceback": ["\u001b[1;31m在当前单元格或上一个单元格中执行代码时 Kernel 崩溃。\n", "\u001b[1;31m请查看单元格中的代码，以确定故障的可能原因。\n", "\u001b[1;31m单击<a href='https://aka.ms/vscodeJupyterKernelCrash'>此处</a>了解详细信息。\n", "\u001b[1;31m有关更多详细信息，请查看 Jupyter <a href='command:jupyter.viewOutput'>log</a>。"]}], "source": ["# 从多项式特征中选择前4个维度，即1,x,x^2/2!,x^3/3!\n", "# train(poly_features[:n_train, :4], poly_features[n_train:, :4],\n", "#       labels[:n_train], labels[n_train:])\n", "labels[:n_train]"]}, {"cell_type": "markdown", "id": "4c08393a", "metadata": {"origin_pos": 21}, "source": ["### [**线性函数拟合(欠拟合)**]\n", "\n", "让我们再看看线性函数拟合，减少该模型的训练损失相对困难。\n", "在最后一个迭代周期完成后，训练损失仍然很高。\n", "当用来拟合非线性模式（如这里的三阶多项式函数）时，线性模型容易欠拟合。\n"]}, {"cell_type": "code", "execution_count": 12, "id": "bae479f7", "metadata": {"execution": {"iopub.execute_input": "2022-12-07T17:02:09.836970Z", "iopub.status.busy": "2022-12-07T17:02:09.836382Z", "iopub.status.idle": "2022-12-07T17:02:22.637628Z", "shell.execute_reply": "2022-12-07T17:02:22.636774Z"}, "origin_pos": 22, "tab": ["pytorch"]}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["weight: [[3.0109622 5.5057235]]\n"]}, {"data": {"image/svg+xml": ["<?xml version=\"1.0\" encoding=\"utf-8\" standalone=\"no\"?>\n", "<!DOCTYPE svg PUBLIC \"-//W3C//DTD SVG 1.1//EN\"\n", "  \"http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd\">\n", "<svg xmlns:xlink=\"http://www.w3.org/1999/xlink\" width=\"263.421875pt\" height=\"187.155469pt\" viewBox=\"0 0 263.**********.155469\" xmlns=\"http://www.w3.org/2000/svg\" version=\"1.1\">\n", " <metadata>\n", "  <rdf:RDF xmlns:dc=\"http://purl.org/dc/elements/1.1/\" xmlns:cc=\"http://creativecommons.org/ns#\" xmlns:rdf=\"http://www.w3.org/1999/02/22-rdf-syntax-ns#\">\n", "   <cc:Work>\n", "    <dc:type rdf:resource=\"http://purl.org/dc/dcmitype/StillImage\"/>\n", "    <dc:date>2025-07-20T18:47:06.672317</dc:date>\n", "    <dc:format>image/svg+xml</dc:format>\n", "    <dc:creator>\n", "     <cc:Agent>\n", "      <dc:title>Matplotlib v3.5.1, https://matplotlib.org/</dc:title>\n", "     </cc:Agent>\n", "    </dc:creator>\n", "   </cc:Work>\n", "  </rdf:RDF>\n", " </metadata>\n", " <defs>\n", "  <style type=\"text/css\">*{stroke-linejoin: round; stroke-linecap: butt}</style>\n", " </defs>\n", " <g id=\"figure_1\">\n", "  <g id=\"patch_1\">\n", "   <path d=\"M 0 187.155469 \n", "L 263.**********.155469 \n", "L 263.421875 0 \n", "L 0 0 \n", "z\n", "\" style=\"fill: #ffffff\"/>\n", "  </g>\n", "  <g id=\"axes_1\">\n", "   <g id=\"patch_2\">\n", "    <path d=\"M 51.**********.599219 \n", "L 246.**********.599219 \n", "L 246.678125 10.999219 \n", "L 51.378125 10.999219 \n", "z\n", "\" style=\"fill: #ffffff\"/>\n", "   </g>\n", "   <g id=\"matplotlib.axis_1\">\n", "    <g id=\"xtick_1\">\n", "     <g id=\"line2d_1\">\n", "      <path d=\"M 99.83602 149.599219 \n", "L 99.83602 10.999219 \n", "\" clip-path=\"url(#p99d1a66bbd)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_2\">\n", "      <defs>\n", "       <path id=\"mbde2eb8503\" d=\"M 0 0 \n", "L 0 3.5 \n", "\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </defs>\n", "      <g>\n", "       <use xlink:href=\"#mbde2eb8503\" x=\"99.83602\" y=\"149.599219\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_1\">\n", "      <!-- 100 -->\n", "      <g transform=\"translate(90.29227 164.197656)scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-31\" d=\"M 794 531 \n", "L 1825 531 \n", "L 1825 4091 \n", "L 703 3866 \n", "L 703 4441 \n", "L 1819 4666 \n", "L 2450 4666 \n", "L 2450 531 \n", "L 3481 531 \n", "L 3481 0 \n", "L 794 0 \n", "L 794 531 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "        <path id=\"DejaVuSans-30\" d=\"M 2034 4250 \n", "Q 1547 4250 1301 3770 \n", "Q 1056 3291 1056 2328 \n", "Q 1056 1369 1301 889 \n", "Q 1547 409 2034 409 \n", "Q 2525 409 2770 889 \n", "Q 3016 1369 3016 2328 \n", "Q 3016 3291 2770 3770 \n", "Q 2525 4250 2034 4250 \n", "z\n", "M 2034 4750 \n", "Q 2819 4750 3233 4129 \n", "Q 3647 3509 3647 2328 \n", "Q 3647 1150 3233 529 \n", "Q 2819 -91 2034 -91 \n", "Q 1250 -91 836 529 \n", "Q 422 1150 422 2328 \n", "Q 422 3509 836 4129 \n", "Q 1250 4750 2034 4750 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-31\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"127.246094\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_2\">\n", "     <g id=\"line2d_3\">\n", "      <path d=\"M 148.783388 149.599219 \n", "L 148.783388 10.999219 \n", "\" clip-path=\"url(#p99d1a66bbd)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_4\">\n", "      <g>\n", "       <use xlink:href=\"#mbde2eb8503\" x=\"148.783388\" y=\"149.599219\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_2\">\n", "      <!-- 200 -->\n", "      <g transform=\"translate(139.239638 164.197656)scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-32\" d=\"M 1228 531 \n", "L 3431 531 \n", "L 3431 0 \n", "L 469 0 \n", "L 469 531 \n", "Q 828 903 1448 1529 \n", "Q 2069 2156 2228 2338 \n", "Q 2531 2678 2651 2914 \n", "Q 2772 3150 2772 3378 \n", "Q 2772 3750 2511 3984 \n", "Q 2250 4219 1831 4219 \n", "Q 1534 4219 1204 4116 \n", "Q 875 4013 500 3803 \n", "L 500 4441 \n", "Q 881 4594 1212 4672 \n", "Q 1544 4750 1819 4750 \n", "Q 2544 4750 2975 4387 \n", "Q 3406 4025 3406 3419 \n", "Q 3406 3131 3298 2873 \n", "Q 3191 2616 2906 2266 \n", "Q 2828 2175 2409 1742 \n", "Q 1991 1309 1228 531 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-32\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"127.246094\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_3\">\n", "     <g id=\"line2d_5\">\n", "      <path d=\"M 197.730757 149.599219 \n", "L 197.730757 10.999219 \n", "\" clip-path=\"url(#p99d1a66bbd)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_6\">\n", "      <g>\n", "       <use xlink:href=\"#mbde2eb8503\" x=\"197.730757\" y=\"149.599219\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_3\">\n", "      <!-- 300 -->\n", "      <g transform=\"translate(188.187007 164.197656)scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-33\" d=\"M 2597 2516 \n", "Q 3050 2419 3304 2112 \n", "Q 3559 1806 3559 1356 \n", "Q 3559 666 3084 287 \n", "Q 2609 -91 1734 -91 \n", "Q 1441 -91 1130 -33 \n", "Q 819 25 488 141 \n", "L 488 750 \n", "Q 750 597 1062 519 \n", "Q 1375 441 1716 441 \n", "Q 2309 441 2620 675 \n", "Q 2931 909 2931 1356 \n", "Q 2931 1769 2642 2001 \n", "Q 2353 2234 1838 2234 \n", "L 1294 2234 \n", "L 1294 2753 \n", "L 1863 2753 \n", "Q 2328 2753 2575 2939 \n", "Q 2822 3125 2822 3475 \n", "Q 2822 3834 2567 4026 \n", "Q 2313 4219 1838 4219 \n", "Q 1578 4219 1281 4162 \n", "Q 984 4106 628 3988 \n", "L 628 4550 \n", "Q 988 4650 1302 4700 \n", "Q 1616 4750 1894 4750 \n", "Q 2613 4750 3031 4423 \n", "Q 3450 4097 3450 3541 \n", "Q 3450 3153 3228 2886 \n", "Q 3006 2619 2597 2516 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-33\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"127.246094\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_4\">\n", "     <g id=\"line2d_7\">\n", "      <path d=\"M 246.**********.599219 \n", "L 246.678125 10.999219 \n", "\" clip-path=\"url(#p99d1a66bbd)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_8\">\n", "      <g>\n", "       <use xlink:href=\"#mbde2eb8503\" x=\"246.678125\" y=\"149.599219\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_4\">\n", "      <!-- 400 -->\n", "      <g transform=\"translate(237.134375 164.197656)scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-34\" d=\"M 2419 4116 \n", "L 825 1625 \n", "L 2419 1625 \n", "L 2419 4116 \n", "z\n", "M 2253 4666 \n", "L 3047 4666 \n", "L 3047 1625 \n", "L 3713 1625 \n", "L 3713 1100 \n", "L 3047 1100 \n", "L 3047 0 \n", "L 2419 0 \n", "L 2419 1100 \n", "L 313 1100 \n", "L 313 1709 \n", "L 2253 4666 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-34\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"127.246094\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"text_5\">\n", "     <!-- epoch -->\n", "     <g transform=\"translate(133.8 177.875781)scale(0.1 -0.1)\">\n", "      <defs>\n", "       <path id=\"DejaVuSans-65\" d=\"M 3597 1894 \n", "L 3597 1613 \n", "L 953 1613 \n", "Q 991 1019 1311 708 \n", "Q 1631 397 2203 397 \n", "Q 2534 397 2845 478 \n", "Q 3156 559 3463 722 \n", "L 3463 178 \n", "Q 3153 47 2828 -22 \n", "Q 2503 -91 2169 -91 \n", "Q 1331 -91 842 396 \n", "Q 353 884 353 1716 \n", "Q 353 2575 817 3079 \n", "Q 1281 3584 2069 3584 \n", "Q 2775 3584 3186 3129 \n", "Q 3597 2675 3597 1894 \n", "z\n", "M 3022 2063 \n", "Q 3016 2534 2758 2815 \n", "Q 2500 3097 2075 3097 \n", "Q 1594 3097 1305 2825 \n", "Q 1016 2553 972 2059 \n", "L 3022 2063 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-70\" d=\"M 1159 525 \n", "L 1159 -1331 \n", "L 581 -1331 \n", "L 581 3500 \n", "L 1159 3500 \n", "L 1159 2969 \n", "Q 1341 3281 1617 3432 \n", "Q 1894 3584 2278 3584 \n", "Q 2916 3584 3314 3078 \n", "Q 3713 2572 3713 1747 \n", "Q 3713 922 3314 415 \n", "Q 2916 -91 2278 -91 \n", "Q 1894 -91 1617 61 \n", "Q 1341 213 1159 525 \n", "z\n", "M 3116 1747 \n", "Q 3116 2381 2855 2742 \n", "Q 2594 3103 2138 3103 \n", "Q 1681 3103 1420 2742 \n", "Q 1159 2381 1159 1747 \n", "Q 1159 1113 1420 752 \n", "Q 1681 391 2138 391 \n", "Q 2594 391 2855 752 \n", "Q 3116 1113 3116 1747 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-6f\" d=\"M 1959 3097 \n", "Q 1497 3097 1228 2736 \n", "Q 959 2375 959 1747 \n", "Q 959 1119 1226 758 \n", "Q 1494 397 1959 397 \n", "Q 2419 397 2687 759 \n", "Q 2956 1122 2956 1747 \n", "Q 2956 2369 2687 2733 \n", "Q 2419 3097 1959 3097 \n", "z\n", "M 1959 3584 \n", "Q 2709 3584 3137 3096 \n", "Q 3566 2609 3566 1747 \n", "Q 3566 888 3137 398 \n", "Q 2709 -91 1959 -91 \n", "Q 1206 -91 779 398 \n", "Q 353 888 353 1747 \n", "Q 353 2609 779 3096 \n", "Q 1206 3584 1959 3584 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-63\" d=\"M 3122 3366 \n", "L 3122 2828 \n", "Q 2878 2963 2633 3030 \n", "Q 2388 3097 2138 3097 \n", "Q 1578 3097 1268 2742 \n", "Q 959 2388 959 1747 \n", "Q 959 1106 1268 751 \n", "Q 1578 397 2138 397 \n", "Q 2388 397 2633 464 \n", "Q 2878 531 3122 666 \n", "L 3122 134 \n", "Q 2881 22 2623 -34 \n", "Q 2366 -91 2075 -91 \n", "Q 1284 -91 818 406 \n", "Q 353 903 353 1747 \n", "Q 353 2603 823 3093 \n", "Q 1294 3584 2113 3584 \n", "Q 2378 3584 2631 3529 \n", "Q 2884 3475 3122 3366 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-68\" d=\"M 3513 2113 \n", "L 3513 0 \n", "L 2938 0 \n", "L 2938 2094 \n", "Q 2938 2591 2744 2837 \n", "Q 2550 3084 2163 3084 \n", "Q 1697 3084 1428 2787 \n", "Q 1159 2491 1159 1978 \n", "L 1159 0 \n", "L 581 0 \n", "L 581 4863 \n", "L 1159 4863 \n", "L 1159 2956 \n", "Q 1366 3272 1645 3428 \n", "Q 1925 3584 2291 3584 \n", "Q 2894 3584 3203 3211 \n", "Q 3513 2838 3513 2113 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "      </defs>\n", "      <use xlink:href=\"#DejaVuSans-65\"/>\n", "      <use xlink:href=\"#DejaVuSans-70\" x=\"61.523438\"/>\n", "      <use xlink:href=\"#DejaVuSans-6f\" x=\"125\"/>\n", "      <use xlink:href=\"#DejaVuSans-63\" x=\"186.181641\"/>\n", "      <use xlink:href=\"#DejaVuSans-68\" x=\"241.162109\"/>\n", "     </g>\n", "    </g>\n", "   </g>\n", "   <g id=\"matplotlib.axis_2\">\n", "    <g id=\"ytick_1\">\n", "     <g id=\"line2d_9\">\n", "      <path d=\"M 51.**********.599219 \n", "L 246.**********.599219 \n", "\" clip-path=\"url(#p99d1a66bbd)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_10\">\n", "      <defs>\n", "       <path id=\"md8f88da310\" d=\"M 0 0 \n", "L -3.5 0 \n", "\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </defs>\n", "      <g>\n", "       <use xlink:href=\"#md8f88da310\" x=\"51.378125\" y=\"149.599219\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_6\">\n", "      <!-- $\\mathdefault{10^{-3}}$ -->\n", "      <g transform=\"translate(20.878125 153.398438)scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-2212\" d=\"M 678 2272 \n", "L 4684 2272 \n", "L 4684 1741 \n", "L 678 1741 \n", "L 678 2272 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-31\" transform=\"translate(0 0.765625)\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" transform=\"translate(63.623047 0.765625)\"/>\n", "       <use xlink:href=\"#DejaVuSans-2212\" transform=\"translate(128.203125 39.046875)scale(0.7)\"/>\n", "       <use xlink:href=\"#DejaVuSans-33\" transform=\"translate(186.855469 39.046875)scale(0.7)\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_2\">\n", "     <g id=\"line2d_11\">\n", "      <path d=\"M 51.378125 121.879219 \n", "L 246.678125 121.879219 \n", "\" clip-path=\"url(#p99d1a66bbd)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_12\">\n", "      <g>\n", "       <use xlink:href=\"#md8f88da310\" x=\"51.378125\" y=\"121.879219\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_7\">\n", "      <!-- $\\mathdefault{10^{-2}}$ -->\n", "      <g transform=\"translate(20.878125 125.678438)scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-31\" transform=\"translate(0 0.765625)\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" transform=\"translate(63.623047 0.765625)\"/>\n", "       <use xlink:href=\"#DejaVuSans-2212\" transform=\"translate(128.203125 39.046875)scale(0.7)\"/>\n", "       <use xlink:href=\"#DejaVuSans-32\" transform=\"translate(186.855469 39.046875)scale(0.7)\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_3\">\n", "     <g id=\"line2d_13\">\n", "      <path d=\"M 51.378125 94.159219 \n", "L 246.678125 94.159219 \n", "\" clip-path=\"url(#p99d1a66bbd)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_14\">\n", "      <g>\n", "       <use xlink:href=\"#md8f88da310\" x=\"51.378125\" y=\"94.159219\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_8\">\n", "      <!-- $\\mathdefault{10^{-1}}$ -->\n", "      <g transform=\"translate(20.878125 97.958437)scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-31\" transform=\"translate(0 0.684375)\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" transform=\"translate(63.623047 0.684375)\"/>\n", "       <use xlink:href=\"#DejaVuSans-2212\" transform=\"translate(128.203125 38.965625)scale(0.7)\"/>\n", "       <use xlink:href=\"#DejaVuSans-31\" transform=\"translate(186.855469 38.965625)scale(0.7)\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_4\">\n", "     <g id=\"line2d_15\">\n", "      <path d=\"M 51.378125 66.439219 \n", "L 246.678125 66.439219 \n", "\" clip-path=\"url(#p99d1a66bbd)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_16\">\n", "      <g>\n", "       <use xlink:href=\"#md8f88da310\" x=\"51.378125\" y=\"66.439219\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_9\">\n", "      <!-- $\\mathdefault{10^{0}}$ -->\n", "      <g transform=\"translate(26.778125 70.238437)scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-31\" transform=\"translate(0 0.765625)\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" transform=\"translate(63.623047 0.765625)\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" transform=\"translate(128.203125 39.046875)scale(0.7)\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_5\">\n", "     <g id=\"line2d_17\">\n", "      <path d=\"M 51.378125 38.719219 \n", "L 246.678125 38.719219 \n", "\" clip-path=\"url(#p99d1a66bbd)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_18\">\n", "      <g>\n", "       <use xlink:href=\"#md8f88da310\" x=\"51.378125\" y=\"38.719219\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_10\">\n", "      <!-- $\\mathdefault{10^{1}}$ -->\n", "      <g transform=\"translate(26.778125 42.518437)scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-31\" transform=\"translate(0 0.684375)\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" transform=\"translate(63.623047 0.684375)\"/>\n", "       <use xlink:href=\"#DejaVuSans-31\" transform=\"translate(128.203125 38.965625)scale(0.7)\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_6\">\n", "     <g id=\"line2d_19\">\n", "      <path d=\"M 51.378125 10.999219 \n", "L 246.678125 10.999219 \n", "\" clip-path=\"url(#p99d1a66bbd)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_20\">\n", "      <g>\n", "       <use xlink:href=\"#md8f88da310\" x=\"51.378125\" y=\"10.999219\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_11\">\n", "      <!-- $\\mathdefault{10^{2}}$ -->\n", "      <g transform=\"translate(26.778125 14.798438)scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-31\" transform=\"translate(0 0.765625)\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" transform=\"translate(63.623047 0.765625)\"/>\n", "       <use xlink:href=\"#DejaVuSans-32\" transform=\"translate(128.203125 39.046875)scale(0.7)\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_7\">\n", "     <g id=\"line2d_21\">\n", "      <defs>\n", "       <path id=\"med0dd51d60\" d=\"M 0 0 \n", "L -2 0 \n", "\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </defs>\n", "      <g>\n", "       <use xlink:href=\"#med0dd51d60\" x=\"51.378125\" y=\"141.254667\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_8\">\n", "     <g id=\"line2d_22\">\n", "      <g>\n", "       <use xlink:href=\"#med0dd51d60\" x=\"51.378125\" y=\"136.373418\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_9\">\n", "     <g id=\"line2d_23\">\n", "      <g>\n", "       <use xlink:href=\"#med0dd51d60\" x=\"51.378125\" y=\"132.910116\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_10\">\n", "     <g id=\"line2d_24\">\n", "      <g>\n", "       <use xlink:href=\"#med0dd51d60\" x=\"51.378125\" y=\"130.22377\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_11\">\n", "     <g id=\"line2d_25\">\n", "      <g>\n", "       <use xlink:href=\"#med0dd51d60\" x=\"51.378125\" y=\"128.028866\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_12\">\n", "     <g id=\"line2d_26\">\n", "      <g>\n", "       <use xlink:href=\"#med0dd51d60\" x=\"51.378125\" y=\"126.173101\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_13\">\n", "     <g id=\"line2d_27\">\n", "      <g>\n", "       <use xlink:href=\"#med0dd51d60\" x=\"51.378125\" y=\"124.565564\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_14\">\n", "     <g id=\"line2d_28\">\n", "      <g>\n", "       <use xlink:href=\"#med0dd51d60\" x=\"51.378125\" y=\"123.147616\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_15\">\n", "     <g id=\"line2d_29\">\n", "      <g>\n", "       <use xlink:href=\"#med0dd51d60\" x=\"51.378125\" y=\"113.534667\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_16\">\n", "     <g id=\"line2d_30\">\n", "      <g>\n", "       <use xlink:href=\"#med0dd51d60\" x=\"51.378125\" y=\"108.653418\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_17\">\n", "     <g id=\"line2d_31\">\n", "      <g>\n", "       <use xlink:href=\"#med0dd51d60\" x=\"51.378125\" y=\"105.190116\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_18\">\n", "     <g id=\"line2d_32\">\n", "      <g>\n", "       <use xlink:href=\"#med0dd51d60\" x=\"51.378125\" y=\"102.50377\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_19\">\n", "     <g id=\"line2d_33\">\n", "      <g>\n", "       <use xlink:href=\"#med0dd51d60\" x=\"51.378125\" y=\"100.308866\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_20\">\n", "     <g id=\"line2d_34\">\n", "      <g>\n", "       <use xlink:href=\"#med0dd51d60\" x=\"51.378125\" y=\"98.453101\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_21\">\n", "     <g id=\"line2d_35\">\n", "      <g>\n", "       <use xlink:href=\"#med0dd51d60\" x=\"51.378125\" y=\"96.845564\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_22\">\n", "     <g id=\"line2d_36\">\n", "      <g>\n", "       <use xlink:href=\"#med0dd51d60\" x=\"51.378125\" y=\"95.427616\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_23\">\n", "     <g id=\"line2d_37\">\n", "      <g>\n", "       <use xlink:href=\"#med0dd51d60\" x=\"51.378125\" y=\"85.814667\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_24\">\n", "     <g id=\"line2d_38\">\n", "      <g>\n", "       <use xlink:href=\"#med0dd51d60\" x=\"51.378125\" y=\"80.933418\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_25\">\n", "     <g id=\"line2d_39\">\n", "      <g>\n", "       <use xlink:href=\"#med0dd51d60\" x=\"51.378125\" y=\"77.470116\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_26\">\n", "     <g id=\"line2d_40\">\n", "      <g>\n", "       <use xlink:href=\"#med0dd51d60\" x=\"51.378125\" y=\"74.78377\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_27\">\n", "     <g id=\"line2d_41\">\n", "      <g>\n", "       <use xlink:href=\"#med0dd51d60\" x=\"51.378125\" y=\"72.588866\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_28\">\n", "     <g id=\"line2d_42\">\n", "      <g>\n", "       <use xlink:href=\"#med0dd51d60\" x=\"51.378125\" y=\"70.733101\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_29\">\n", "     <g id=\"line2d_43\">\n", "      <g>\n", "       <use xlink:href=\"#med0dd51d60\" x=\"51.378125\" y=\"69.125564\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_30\">\n", "     <g id=\"line2d_44\">\n", "      <g>\n", "       <use xlink:href=\"#med0dd51d60\" x=\"51.378125\" y=\"67.707616\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_31\">\n", "     <g id=\"line2d_45\">\n", "      <g>\n", "       <use xlink:href=\"#med0dd51d60\" x=\"51.378125\" y=\"58.094667\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_32\">\n", "     <g id=\"line2d_46\">\n", "      <g>\n", "       <use xlink:href=\"#med0dd51d60\" x=\"51.378125\" y=\"53.213418\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_33\">\n", "     <g id=\"line2d_47\">\n", "      <g>\n", "       <use xlink:href=\"#med0dd51d60\" x=\"51.378125\" y=\"49.750116\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_34\">\n", "     <g id=\"line2d_48\">\n", "      <g>\n", "       <use xlink:href=\"#med0dd51d60\" x=\"51.378125\" y=\"47.06377\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_35\">\n", "     <g id=\"line2d_49\">\n", "      <g>\n", "       <use xlink:href=\"#med0dd51d60\" x=\"51.378125\" y=\"44.868866\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_36\">\n", "     <g id=\"line2d_50\">\n", "      <g>\n", "       <use xlink:href=\"#med0dd51d60\" x=\"51.378125\" y=\"43.013101\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_37\">\n", "     <g id=\"line2d_51\">\n", "      <g>\n", "       <use xlink:href=\"#med0dd51d60\" x=\"51.378125\" y=\"41.405564\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_38\">\n", "     <g id=\"line2d_52\">\n", "      <g>\n", "       <use xlink:href=\"#med0dd51d60\" x=\"51.378125\" y=\"39.987616\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_39\">\n", "     <g id=\"line2d_53\">\n", "      <g>\n", "       <use xlink:href=\"#med0dd51d60\" x=\"51.378125\" y=\"30.374667\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_40\">\n", "     <g id=\"line2d_54\">\n", "      <g>\n", "       <use xlink:href=\"#med0dd51d60\" x=\"51.378125\" y=\"25.493418\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_41\">\n", "     <g id=\"line2d_55\">\n", "      <g>\n", "       <use xlink:href=\"#med0dd51d60\" x=\"51.378125\" y=\"22.030116\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_42\">\n", "     <g id=\"line2d_56\">\n", "      <g>\n", "       <use xlink:href=\"#med0dd51d60\" x=\"51.378125\" y=\"19.34377\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_43\">\n", "     <g id=\"line2d_57\">\n", "      <g>\n", "       <use xlink:href=\"#med0dd51d60\" x=\"51.378125\" y=\"17.148866\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_44\">\n", "     <g id=\"line2d_58\">\n", "      <g>\n", "       <use xlink:href=\"#med0dd51d60\" x=\"51.378125\" y=\"15.293101\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_45\">\n", "     <g id=\"line2d_59\">\n", "      <g>\n", "       <use xlink:href=\"#med0dd51d60\" x=\"51.378125\" y=\"13.685564\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_46\">\n", "     <g id=\"line2d_60\">\n", "      <g>\n", "       <use xlink:href=\"#med0dd51d60\" x=\"51.378125\" y=\"12.267616\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"text_12\">\n", "     <!-- loss -->\n", "     <g transform=\"translate(14.798437 89.957031)rotate(-90)scale(0.1 -0.1)\">\n", "      <defs>\n", "       <path id=\"DejaVuSans-6c\" d=\"M 603 4863 \n", "L 1178 4863 \n", "L 1178 0 \n", "L 603 0 \n", "L 603 4863 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-73\" d=\"M 2834 3397 \n", "L 2834 2853 \n", "Q 2591 2978 2328 3040 \n", "Q 2066 3103 1784 3103 \n", "Q 1356 3103 1142 2972 \n", "Q 928 2841 928 2578 \n", "Q 928 2378 1081 2264 \n", "Q 1234 2150 1697 2047 \n", "L 1894 2003 \n", "Q 2506 1872 2764 1633 \n", "Q 3022 1394 3022 966 \n", "Q 3022 478 2636 193 \n", "Q 2250 -91 1575 -91 \n", "Q 1294 -91 989 -36 \n", "Q 684 19 347 128 \n", "L 347 722 \n", "Q 666 556 975 473 \n", "Q 1284 391 1588 391 \n", "Q 1994 391 2212 530 \n", "Q 2431 669 2431 922 \n", "Q 2431 1156 2273 1281 \n", "Q 2116 1406 1581 1522 \n", "L 1381 1569 \n", "Q 847 1681 609 1914 \n", "Q 372 2147 372 2553 \n", "Q 372 3047 722 3315 \n", "Q 1072 3584 1716 3584 \n", "Q 2034 3584 2315 3537 \n", "Q 2597 3491 2834 3397 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "      </defs>\n", "      <use xlink:href=\"#DejaVuSans-6c\"/>\n", "      <use xlink:href=\"#DejaVuSans-6f\" x=\"27.783203\"/>\n", "      <use xlink:href=\"#DejaVuSans-73\" x=\"88.964844\"/>\n", "      <use xlink:href=\"#DejaVuSans-73\" x=\"141.064453\"/>\n", "     </g>\n", "    </g>\n", "   </g>\n", "   <g id=\"line2d_61\">\n", "    <path d=\"M 51.378125 17.908208 \n", "L 60.678125 28.372174 \n", "L 70.467599 28.381299 \n", "L 80.257072 28.380902 \n", "L 90.046546 28.380999 \n", "L 99.83602 28.38082 \n", "L 109.625493 28.379502 \n", "L 119.414967 28.380898 \n", "L 129.204441 28.379791 \n", "L 138.993914 28.38137 \n", "L 148.783388 28.381266 \n", "L 158.572862 28.380187 \n", "L 168.362336 28.381237 \n", "L 178.151809 28.381349 \n", "L 187.941283 28.380249 \n", "L 197.730757 28.381129 \n", "L 207.52023 28.381241 \n", "L 217.309704 28.381403 \n", "L 227.099178 28.38129 \n", "L 236.888651 28.38083 \n", "L 246.678125 28.379695 \n", "\" clip-path=\"url(#p99d1a66bbd)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_62\">\n", "    <path d=\"M 51.378125 26.822039 \n", "L 60.678125 39.308574 \n", "L 70.467599 38.965154 \n", "L 80.257072 39.051812 \n", "L 90.046546 38.777088 \n", "L 99.83602 39.065277 \n", "L 109.625493 38.622241 \n", "L 119.414967 39.053905 \n", "L 129.204441 38.655032 \n", "L 138.993914 38.868885 \n", "L 148.783388 38.851292 \n", "L 158.572862 39.131923 \n", "L 168.362336 38.982921 \n", "L 178.151809 38.899425 \n", "L 187.941283 38.689478 \n", "L 197.730757 38.797699 \n", "L 207.52023 38.822094 \n", "L 217.309704 38.91113 \n", "L 227.099178 38.85421 \n", "L 236.888651 38.766101 \n", "L 246.678125 38.63942 \n", "\" clip-path=\"url(#p99d1a66bbd)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #bf00bf; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"patch_3\">\n", "    <path d=\"M 51.**********.599219 \n", "L 51.378125 10.999219 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_4\">\n", "    <path d=\"M 246.**********.599219 \n", "L 246.678125 10.999219 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_5\">\n", "    <path d=\"M 51.**********.599219 \n", "L 246.**********.599219 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_6\">\n", "    <path d=\"M 51.378125 10.999219 \n", "L 246.678125 10.999219 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"legend_1\">\n", "    <g id=\"patch_7\">\n", "     <path d=\"M 58.378125 144.599219 \n", "L 113.653125 144.599219 \n", "Q 115.653125 144.599219 115.653125 142.599219 \n", "L 115.653125 114.242969 \n", "Q 115.653125 112.242969 113.653125 112.242969 \n", "L 58.378125 112.242969 \n", "Q 56.378125 112.242969 56.378125 114.242969 \n", "L 56.378125 142.599219 \n", "Q 56.378125 144.599219 58.378125 144.599219 \n", "z\n", "\" style=\"fill: #ffffff; opacity: 0.8; stroke: #cccccc; stroke-linejoin: miter\"/>\n", "    </g>\n", "    <g id=\"line2d_63\">\n", "     <path d=\"M 60.378125 120.341406 \n", "L 70.378125 120.341406 \n", "L 80.378125 120.341406 \n", "\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "    </g>\n", "    <g id=\"text_13\">\n", "     <!-- train -->\n", "     <g transform=\"translate(88.378125 123.841406)scale(0.1 -0.1)\">\n", "      <defs>\n", "       <path id=\"DejaVuSans-74\" d=\"M 1172 4494 \n", "L 1172 3500 \n", "L 2356 3500 \n", "L 2356 3053 \n", "L 1172 3053 \n", "L 1172 1153 \n", "Q 1172 725 1289 603 \n", "Q 1406 481 1766 481 \n", "L 2356 481 \n", "L 2356 0 \n", "L 1766 0 \n", "Q 1100 0 847 248 \n", "Q 594 497 594 1153 \n", "L 594 3053 \n", "L 172 3053 \n", "L 172 3500 \n", "L 594 3500 \n", "L 594 4494 \n", "L 1172 4494 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-72\" d=\"M 2631 2963 \n", "Q 2534 3019 2420 3045 \n", "Q 2306 3072 2169 3072 \n", "Q 1681 3072 1420 2755 \n", "Q 1159 2438 1159 1844 \n", "L 1159 0 \n", "L 581 0 \n", "L 581 3500 \n", "L 1159 3500 \n", "L 1159 2956 \n", "Q 1341 3275 1631 3429 \n", "Q 1922 3584 2338 3584 \n", "Q 2397 3584 2469 3576 \n", "Q 2541 3569 2628 3553 \n", "L 2631 2963 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-61\" d=\"M 2194 1759 \n", "Q 1497 1759 1228 1600 \n", "Q 959 1441 959 1056 \n", "Q 959 750 1161 570 \n", "Q 1363 391 1709 391 \n", "Q 2188 391 2477 730 \n", "Q 2766 1069 2766 1631 \n", "L 2766 1759 \n", "L 2194 1759 \n", "z\n", "M 3341 1997 \n", "L 3341 0 \n", "L 2766 0 \n", "L 2766 531 \n", "Q 2569 213 2275 61 \n", "Q 1981 -91 1556 -91 \n", "Q 1019 -91 701 211 \n", "Q 384 513 384 1019 \n", "Q 384 1609 779 1909 \n", "Q 1175 2209 1959 2209 \n", "L 2766 2209 \n", "L 2766 2266 \n", "Q 2766 2663 2505 2880 \n", "Q 2244 3097 1772 3097 \n", "Q 1472 3097 1187 3025 \n", "Q 903 2953 641 2809 \n", "L 641 3341 \n", "Q 956 3463 1253 3523 \n", "Q 1550 3584 1831 3584 \n", "Q 2591 3584 2966 3190 \n", "Q 3341 2797 3341 1997 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-69\" d=\"M 603 3500 \n", "L 1178 3500 \n", "L 1178 0 \n", "L 603 0 \n", "L 603 3500 \n", "z\n", "M 603 4863 \n", "L 1178 4863 \n", "L 1178 4134 \n", "L 603 4134 \n", "L 603 4863 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-6e\" d=\"M 3513 2113 \n", "L 3513 0 \n", "L 2938 0 \n", "L 2938 2094 \n", "Q 2938 2591 2744 2837 \n", "Q 2550 3084 2163 3084 \n", "Q 1697 3084 1428 2787 \n", "Q 1159 2491 1159 1978 \n", "L 1159 0 \n", "L 581 0 \n", "L 581 3500 \n", "L 1159 3500 \n", "L 1159 2956 \n", "Q 1366 3272 1645 3428 \n", "Q 1925 3584 2291 3584 \n", "Q 2894 3584 3203 3211 \n", "Q 3513 2838 3513 2113 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "      </defs>\n", "      <use xlink:href=\"#DejaVuSans-74\"/>\n", "      <use xlink:href=\"#DejaVuSans-72\" x=\"39.208984\"/>\n", "      <use xlink:href=\"#DejaVuSans-61\" x=\"80.322266\"/>\n", "      <use xlink:href=\"#DejaVuSans-69\" x=\"141.601562\"/>\n", "      <use xlink:href=\"#DejaVuSans-6e\" x=\"169.384766\"/>\n", "     </g>\n", "    </g>\n", "    <g id=\"line2d_64\">\n", "     <path d=\"M 60.378125 135.019531 \n", "L 70.378125 135.019531 \n", "L 80.378125 135.019531 \n", "\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #bf00bf; stroke-width: 1.5\"/>\n", "    </g>\n", "    <g id=\"text_14\">\n", "     <!-- test -->\n", "     <g transform=\"translate(88.378125 138.519531)scale(0.1 -0.1)\">\n", "      <use xlink:href=\"#DejaVuSans-74\"/>\n", "      <use xlink:href=\"#DejaVuSans-65\" x=\"39.208984\"/>\n", "      <use xlink:href=\"#DejaVuSans-73\" x=\"100.732422\"/>\n", "      <use xlink:href=\"#DejaVuSans-74\" x=\"152.832031\"/>\n", "     </g>\n", "    </g>\n", "   </g>\n", "  </g>\n", " </g>\n", " <defs>\n", "  <clipPath id=\"p99d1a66bbd\">\n", "   <rect x=\"51.378125\" y=\"10.999219\" width=\"195.3\" height=\"138.6\"/>\n", "  </clipPath>\n", " </defs>\n", "</svg>\n"], "text/plain": ["<Figure size 350x250 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["# 从多项式特征中选择前2个维度，即1和x\n", "train(poly_features[:n_train, :2], poly_features[n_train:, :2],\n", "      labels[:n_train], labels[n_train:])"]}, {"cell_type": "markdown", "id": "fe337437", "metadata": {"origin_pos": 23}, "source": ["### [**高阶多项式函数拟合(过拟合)**]\n", "\n", "现在，让我们尝试使用一个阶数过高的多项式来训练模型。\n", "在这种情况下，没有足够的数据用于学到高阶系数应该具有接近于零的值。\n", "因此，这个过于复杂的模型会轻易受到训练数据中噪声的影响。\n", "虽然训练损失可以有效地降低，但测试损失仍然很高。\n", "结果表明，复杂模型对数据造成了过拟合。\n"]}, {"cell_type": "code", "execution_count": 14, "id": "7d15f78f", "metadata": {"execution": {"iopub.execute_input": "2022-12-07T17:02:22.643049Z", "iopub.status.busy": "2022-12-07T17:02:22.642410Z", "iopub.status.idle": "2022-12-07T17:03:09.022157Z", "shell.execute_reply": "2022-12-07T17:03:09.021355Z"}, "origin_pos": 24, "tab": ["pytorch"]}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["weight: [[ 5.0141950e+00  1.2767577e+00 -3.4053514e+00  5.3696146e+00\n", "  -1.5328898e-01  4.7895944e-01  4.9729371e-01 -1.1045765e-01\n", "   1.6577293e-01  1.6034505e-01 -5.6093358e-03 -1.5027468e-01\n", "  -1.4109622e-01  1.6667180e-01 -4.1026704e-02 -1.6000643e-01\n", "  -7.3868997e-02  3.1833728e-03 -1.3323277e-01 -1.2836467e-01]]\n"]}, {"data": {"image/svg+xml": ["<?xml version=\"1.0\" encoding=\"utf-8\" standalone=\"no\"?>\n", "<!DOCTYPE svg PUBLIC \"-//W3C//DTD SVG 1.1//EN\"\n", "  \"http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd\">\n", "<svg xmlns:xlink=\"http://www.w3.org/1999/xlink\" width=\"266.603125pt\" height=\"187.155469pt\" viewBox=\"0 0 266.**********.155469\" xmlns=\"http://www.w3.org/2000/svg\" version=\"1.1\">\n", " <metadata>\n", "  <rdf:RDF xmlns:dc=\"http://purl.org/dc/elements/1.1/\" xmlns:cc=\"http://creativecommons.org/ns#\" xmlns:rdf=\"http://www.w3.org/1999/02/22-rdf-syntax-ns#\">\n", "   <cc:Work>\n", "    <dc:type rdf:resource=\"http://purl.org/dc/dcmitype/StillImage\"/>\n", "    <dc:date>2025-07-20T18:50:06.533762</dc:date>\n", "    <dc:format>image/svg+xml</dc:format>\n", "    <dc:creator>\n", "     <cc:Agent>\n", "      <dc:title>Matplotlib v3.5.1, https://matplotlib.org/</dc:title>\n", "     </cc:Agent>\n", "    </dc:creator>\n", "   </cc:Work>\n", "  </rdf:RDF>\n", " </metadata>\n", " <defs>\n", "  <style type=\"text/css\">*{stroke-linejoin: round; stroke-linecap: butt}</style>\n", " </defs>\n", " <g id=\"figure_1\">\n", "  <g id=\"patch_1\">\n", "   <path d=\"M 0 187.155469 \n", "L 266.**********.155469 \n", "L 266.603125 0 \n", "L 0 0 \n", "z\n", "\" style=\"fill: #ffffff\"/>\n", "  </g>\n", "  <g id=\"axes_1\">\n", "   <g id=\"patch_2\">\n", "    <path d=\"M 51.**********.599219 \n", "L 246.**********.599219 \n", "L 246.678125 10.999219 \n", "L 51.378125 10.999219 \n", "z\n", "\" style=\"fill: #ffffff\"/>\n", "   </g>\n", "   <g id=\"matplotlib.axis_1\">\n", "    <g id=\"xtick_1\">\n", "     <g id=\"line2d_1\">\n", "      <path d=\"M 83.819553 149.599219 \n", "L 83.819553 10.999219 \n", "\" clip-path=\"url(#p81262f4d6a)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_2\">\n", "      <defs>\n", "       <path id=\"m65c721ac7b\" d=\"M 0 0 \n", "L 0 3.5 \n", "\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </defs>\n", "      <g>\n", "       <use xlink:href=\"#m65c721ac7b\" x=\"83.819553\" y=\"149.599219\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_1\">\n", "      <!-- 250 -->\n", "      <g transform=\"translate(74.275803 164.197656)scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-32\" d=\"M 1228 531 \n", "L 3431 531 \n", "L 3431 0 \n", "L 469 0 \n", "L 469 531 \n", "Q 828 903 1448 1529 \n", "Q 2069 2156 2228 2338 \n", "Q 2531 2678 2651 2914 \n", "Q 2772 3150 2772 3378 \n", "Q 2772 3750 2511 3984 \n", "Q 2250 4219 1831 4219 \n", "Q 1534 4219 1204 4116 \n", "Q 875 4013 500 3803 \n", "L 500 4441 \n", "Q 881 4594 1212 4672 \n", "Q 1544 4750 1819 4750 \n", "Q 2544 4750 2975 4387 \n", "Q 3406 4025 3406 3419 \n", "Q 3406 3131 3298 2873 \n", "Q 3191 2616 2906 2266 \n", "Q 2828 2175 2409 1742 \n", "Q 1991 1309 1228 531 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "        <path id=\"DejaVuSans-35\" d=\"M 691 4666 \n", "L 3169 4666 \n", "L 3169 4134 \n", "L 1269 4134 \n", "L 1269 2991 \n", "Q 1406 3038 1543 3061 \n", "Q 1681 3084 1819 3084 \n", "Q 2600 3084 3056 2656 \n", "Q 3513 2228 3513 1497 \n", "Q 3513 744 3044 326 \n", "Q 2575 -91 1722 -91 \n", "Q 1428 -91 1123 -41 \n", "Q 819 9 494 109 \n", "L 494 744 \n", "Q 775 591 1075 516 \n", "Q 1375 441 1709 441 \n", "Q 2250 441 2565 725 \n", "Q 2881 1009 2881 1497 \n", "Q 2881 1984 2565 2268 \n", "Q 2250 2553 1709 2553 \n", "Q 1456 2553 1204 2497 \n", "Q 953 2441 691 2322 \n", "L 691 4666 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "        <path id=\"DejaVuSans-30\" d=\"M 2034 4250 \n", "Q 1547 4250 1301 3770 \n", "Q 1056 3291 1056 2328 \n", "Q 1056 1369 1301 889 \n", "Q 1547 409 2034 409 \n", "Q 2525 409 2770 889 \n", "Q 3016 1369 3016 2328 \n", "Q 3016 3291 2770 3770 \n", "Q 2525 4250 2034 4250 \n", "z\n", "M 2034 4750 \n", "Q 2819 4750 3233 4129 \n", "Q 3647 3509 3647 2328 \n", "Q 3647 1150 3233 529 \n", "Q 2819 -91 2034 -91 \n", "Q 1250 -91 836 529 \n", "Q 422 1150 422 2328 \n", "Q 422 3509 836 4129 \n", "Q 1250 4750 2034 4750 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-32\"/>\n", "       <use xlink:href=\"#DejaVuSans-35\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"127.246094\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_2\">\n", "     <g id=\"line2d_3\">\n", "      <path d=\"M 116.391267 149.599219 \n", "L 116.391267 10.999219 \n", "\" clip-path=\"url(#p81262f4d6a)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_4\">\n", "      <g>\n", "       <use xlink:href=\"#m65c721ac7b\" x=\"116.391267\" y=\"149.599219\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_2\">\n", "      <!-- 500 -->\n", "      <g transform=\"translate(106.847517 164.197656)scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-35\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"127.246094\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_3\">\n", "     <g id=\"line2d_5\">\n", "      <path d=\"M 148.962982 149.599219 \n", "L 148.962982 10.999219 \n", "\" clip-path=\"url(#p81262f4d6a)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_6\">\n", "      <g>\n", "       <use xlink:href=\"#m65c721ac7b\" x=\"148.962982\" y=\"149.599219\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_3\">\n", "      <!-- 750 -->\n", "      <g transform=\"translate(139.419232 164.197656)scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-37\" d=\"M 525 4666 \n", "L 3525 4666 \n", "L 3525 4397 \n", "L 1831 0 \n", "L 1172 0 \n", "L 2766 4134 \n", "L 525 4134 \n", "L 525 4666 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-37\"/>\n", "       <use xlink:href=\"#DejaVuSans-35\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"127.246094\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_4\">\n", "     <g id=\"line2d_7\">\n", "      <path d=\"M 181.534696 149.599219 \n", "L 181.534696 10.999219 \n", "\" clip-path=\"url(#p81262f4d6a)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_8\">\n", "      <g>\n", "       <use xlink:href=\"#m65c721ac7b\" x=\"181.534696\" y=\"149.599219\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_4\">\n", "      <!-- 1000 -->\n", "      <g transform=\"translate(168.809696 164.197656)scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-31\" d=\"M 794 531 \n", "L 1825 531 \n", "L 1825 4091 \n", "L 703 3866 \n", "L 703 4441 \n", "L 1819 4666 \n", "L 2450 4666 \n", "L 2450 531 \n", "L 3481 531 \n", "L 3481 0 \n", "L 794 0 \n", "L 794 531 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-31\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"127.246094\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"190.869141\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_5\">\n", "     <g id=\"line2d_9\">\n", "      <path d=\"M 214.106411 149.599219 \n", "L 214.106411 10.999219 \n", "\" clip-path=\"url(#p81262f4d6a)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_10\">\n", "      <g>\n", "       <use xlink:href=\"#m65c721ac7b\" x=\"214.106411\" y=\"149.599219\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_5\">\n", "      <!-- 1250 -->\n", "      <g transform=\"translate(201.381411 164.197656)scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-31\"/>\n", "       <use xlink:href=\"#DejaVuSans-32\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-35\" x=\"127.246094\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"190.869141\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_6\">\n", "     <g id=\"line2d_11\">\n", "      <path d=\"M 246.**********.599219 \n", "L 246.678125 10.999219 \n", "\" clip-path=\"url(#p81262f4d6a)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_12\">\n", "      <g>\n", "       <use xlink:href=\"#m65c721ac7b\" x=\"246.678125\" y=\"149.599219\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_6\">\n", "      <!-- 1500 -->\n", "      <g transform=\"translate(233.953125 164.197656)scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-31\"/>\n", "       <use xlink:href=\"#DejaVuSans-35\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"127.246094\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"190.869141\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"text_7\">\n", "     <!-- epoch -->\n", "     <g transform=\"translate(133.8 177.875781)scale(0.1 -0.1)\">\n", "      <defs>\n", "       <path id=\"DejaVuSans-65\" d=\"M 3597 1894 \n", "L 3597 1613 \n", "L 953 1613 \n", "Q 991 1019 1311 708 \n", "Q 1631 397 2203 397 \n", "Q 2534 397 2845 478 \n", "Q 3156 559 3463 722 \n", "L 3463 178 \n", "Q 3153 47 2828 -22 \n", "Q 2503 -91 2169 -91 \n", "Q 1331 -91 842 396 \n", "Q 353 884 353 1716 \n", "Q 353 2575 817 3079 \n", "Q 1281 3584 2069 3584 \n", "Q 2775 3584 3186 3129 \n", "Q 3597 2675 3597 1894 \n", "z\n", "M 3022 2063 \n", "Q 3016 2534 2758 2815 \n", "Q 2500 3097 2075 3097 \n", "Q 1594 3097 1305 2825 \n", "Q 1016 2553 972 2059 \n", "L 3022 2063 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-70\" d=\"M 1159 525 \n", "L 1159 -1331 \n", "L 581 -1331 \n", "L 581 3500 \n", "L 1159 3500 \n", "L 1159 2969 \n", "Q 1341 3281 1617 3432 \n", "Q 1894 3584 2278 3584 \n", "Q 2916 3584 3314 3078 \n", "Q 3713 2572 3713 1747 \n", "Q 3713 922 3314 415 \n", "Q 2916 -91 2278 -91 \n", "Q 1894 -91 1617 61 \n", "Q 1341 213 1159 525 \n", "z\n", "M 3116 1747 \n", "Q 3116 2381 2855 2742 \n", "Q 2594 3103 2138 3103 \n", "Q 1681 3103 1420 2742 \n", "Q 1159 2381 1159 1747 \n", "Q 1159 1113 1420 752 \n", "Q 1681 391 2138 391 \n", "Q 2594 391 2855 752 \n", "Q 3116 1113 3116 1747 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-6f\" d=\"M 1959 3097 \n", "Q 1497 3097 1228 2736 \n", "Q 959 2375 959 1747 \n", "Q 959 1119 1226 758 \n", "Q 1494 397 1959 397 \n", "Q 2419 397 2687 759 \n", "Q 2956 1122 2956 1747 \n", "Q 2956 2369 2687 2733 \n", "Q 2419 3097 1959 3097 \n", "z\n", "M 1959 3584 \n", "Q 2709 3584 3137 3096 \n", "Q 3566 2609 3566 1747 \n", "Q 3566 888 3137 398 \n", "Q 2709 -91 1959 -91 \n", "Q 1206 -91 779 398 \n", "Q 353 888 353 1747 \n", "Q 353 2609 779 3096 \n", "Q 1206 3584 1959 3584 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-63\" d=\"M 3122 3366 \n", "L 3122 2828 \n", "Q 2878 2963 2633 3030 \n", "Q 2388 3097 2138 3097 \n", "Q 1578 3097 1268 2742 \n", "Q 959 2388 959 1747 \n", "Q 959 1106 1268 751 \n", "Q 1578 397 2138 397 \n", "Q 2388 397 2633 464 \n", "Q 2878 531 3122 666 \n", "L 3122 134 \n", "Q 2881 22 2623 -34 \n", "Q 2366 -91 2075 -91 \n", "Q 1284 -91 818 406 \n", "Q 353 903 353 1747 \n", "Q 353 2603 823 3093 \n", "Q 1294 3584 2113 3584 \n", "Q 2378 3584 2631 3529 \n", "Q 2884 3475 3122 3366 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-68\" d=\"M 3513 2113 \n", "L 3513 0 \n", "L 2938 0 \n", "L 2938 2094 \n", "Q 2938 2591 2744 2837 \n", "Q 2550 3084 2163 3084 \n", "Q 1697 3084 1428 2787 \n", "Q 1159 2491 1159 1978 \n", "L 1159 0 \n", "L 581 0 \n", "L 581 4863 \n", "L 1159 4863 \n", "L 1159 2956 \n", "Q 1366 3272 1645 3428 \n", "Q 1925 3584 2291 3584 \n", "Q 2894 3584 3203 3211 \n", "Q 3513 2838 3513 2113 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "      </defs>\n", "      <use xlink:href=\"#DejaVuSans-65\"/>\n", "      <use xlink:href=\"#DejaVuSans-70\" x=\"61.523438\"/>\n", "      <use xlink:href=\"#DejaVuSans-6f\" x=\"125\"/>\n", "      <use xlink:href=\"#DejaVuSans-63\" x=\"186.181641\"/>\n", "      <use xlink:href=\"#DejaVuSans-68\" x=\"241.162109\"/>\n", "     </g>\n", "    </g>\n", "   </g>\n", "   <g id=\"matplotlib.axis_2\">\n", "    <g id=\"ytick_1\">\n", "     <g id=\"line2d_13\">\n", "      <path d=\"M 51.**********.599219 \n", "L 246.**********.599219 \n", "\" clip-path=\"url(#p81262f4d6a)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_14\">\n", "      <defs>\n", "       <path id=\"md4fc44b040\" d=\"M 0 0 \n", "L -3.5 0 \n", "\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </defs>\n", "      <g>\n", "       <use xlink:href=\"#md4fc44b040\" x=\"51.378125\" y=\"149.599219\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_8\">\n", "      <!-- $\\mathdefault{10^{-3}}$ -->\n", "      <g transform=\"translate(20.878125 153.398438)scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-2212\" d=\"M 678 2272 \n", "L 4684 2272 \n", "L 4684 1741 \n", "L 678 1741 \n", "L 678 2272 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "        <path id=\"DejaVuSans-33\" d=\"M 2597 2516 \n", "Q 3050 2419 3304 2112 \n", "Q 3559 1806 3559 1356 \n", "Q 3559 666 3084 287 \n", "Q 2609 -91 1734 -91 \n", "Q 1441 -91 1130 -33 \n", "Q 819 25 488 141 \n", "L 488 750 \n", "Q 750 597 1062 519 \n", "Q 1375 441 1716 441 \n", "Q 2309 441 2620 675 \n", "Q 2931 909 2931 1356 \n", "Q 2931 1769 2642 2001 \n", "Q 2353 2234 1838 2234 \n", "L 1294 2234 \n", "L 1294 2753 \n", "L 1863 2753 \n", "Q 2328 2753 2575 2939 \n", "Q 2822 3125 2822 3475 \n", "Q 2822 3834 2567 4026 \n", "Q 2313 4219 1838 4219 \n", "Q 1578 4219 1281 4162 \n", "Q 984 4106 628 3988 \n", "L 628 4550 \n", "Q 988 4650 1302 4700 \n", "Q 1616 4750 1894 4750 \n", "Q 2613 4750 3031 4423 \n", "Q 3450 4097 3450 3541 \n", "Q 3450 3153 3228 2886 \n", "Q 3006 2619 2597 2516 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-31\" transform=\"translate(0 0.765625)\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" transform=\"translate(63.623047 0.765625)\"/>\n", "       <use xlink:href=\"#DejaVuSans-2212\" transform=\"translate(128.203125 39.046875)scale(0.7)\"/>\n", "       <use xlink:href=\"#DejaVuSans-33\" transform=\"translate(186.855469 39.046875)scale(0.7)\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_2\">\n", "     <g id=\"line2d_15\">\n", "      <path d=\"M 51.378125 121.879219 \n", "L 246.678125 121.879219 \n", "\" clip-path=\"url(#p81262f4d6a)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_16\">\n", "      <g>\n", "       <use xlink:href=\"#md4fc44b040\" x=\"51.378125\" y=\"121.879219\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_9\">\n", "      <!-- $\\mathdefault{10^{-2}}$ -->\n", "      <g transform=\"translate(20.878125 125.678438)scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-31\" transform=\"translate(0 0.765625)\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" transform=\"translate(63.623047 0.765625)\"/>\n", "       <use xlink:href=\"#DejaVuSans-2212\" transform=\"translate(128.203125 39.046875)scale(0.7)\"/>\n", "       <use xlink:href=\"#DejaVuSans-32\" transform=\"translate(186.855469 39.046875)scale(0.7)\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_3\">\n", "     <g id=\"line2d_17\">\n", "      <path d=\"M 51.378125 94.159219 \n", "L 246.678125 94.159219 \n", "\" clip-path=\"url(#p81262f4d6a)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_18\">\n", "      <g>\n", "       <use xlink:href=\"#md4fc44b040\" x=\"51.378125\" y=\"94.159219\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_10\">\n", "      <!-- $\\mathdefault{10^{-1}}$ -->\n", "      <g transform=\"translate(20.878125 97.958437)scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-31\" transform=\"translate(0 0.684375)\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" transform=\"translate(63.623047 0.684375)\"/>\n", "       <use xlink:href=\"#DejaVuSans-2212\" transform=\"translate(128.203125 38.965625)scale(0.7)\"/>\n", "       <use xlink:href=\"#DejaVuSans-31\" transform=\"translate(186.855469 38.965625)scale(0.7)\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_4\">\n", "     <g id=\"line2d_19\">\n", "      <path d=\"M 51.378125 66.439219 \n", "L 246.678125 66.439219 \n", "\" clip-path=\"url(#p81262f4d6a)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_20\">\n", "      <g>\n", "       <use xlink:href=\"#md4fc44b040\" x=\"51.378125\" y=\"66.439219\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_11\">\n", "      <!-- $\\mathdefault{10^{0}}$ -->\n", "      <g transform=\"translate(26.778125 70.238437)scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-31\" transform=\"translate(0 0.765625)\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" transform=\"translate(63.623047 0.765625)\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" transform=\"translate(128.203125 39.046875)scale(0.7)\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_5\">\n", "     <g id=\"line2d_21\">\n", "      <path d=\"M 51.378125 38.719219 \n", "L 246.678125 38.719219 \n", "\" clip-path=\"url(#p81262f4d6a)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_22\">\n", "      <g>\n", "       <use xlink:href=\"#md4fc44b040\" x=\"51.378125\" y=\"38.719219\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_12\">\n", "      <!-- $\\mathdefault{10^{1}}$ -->\n", "      <g transform=\"translate(26.778125 42.518437)scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-31\" transform=\"translate(0 0.684375)\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" transform=\"translate(63.623047 0.684375)\"/>\n", "       <use xlink:href=\"#DejaVuSans-31\" transform=\"translate(128.203125 38.965625)scale(0.7)\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_6\">\n", "     <g id=\"line2d_23\">\n", "      <path d=\"M 51.378125 10.999219 \n", "L 246.678125 10.999219 \n", "\" clip-path=\"url(#p81262f4d6a)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_24\">\n", "      <g>\n", "       <use xlink:href=\"#md4fc44b040\" x=\"51.378125\" y=\"10.999219\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_13\">\n", "      <!-- $\\mathdefault{10^{2}}$ -->\n", "      <g transform=\"translate(26.778125 14.798438)scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-31\" transform=\"translate(0 0.765625)\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" transform=\"translate(63.623047 0.765625)\"/>\n", "       <use xlink:href=\"#DejaVuSans-32\" transform=\"translate(128.203125 39.046875)scale(0.7)\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_7\">\n", "     <g id=\"line2d_25\">\n", "      <defs>\n", "       <path id=\"mda1038f86a\" d=\"M 0 0 \n", "L -2 0 \n", "\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </defs>\n", "      <g>\n", "       <use xlink:href=\"#mda1038f86a\" x=\"51.378125\" y=\"141.254667\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_8\">\n", "     <g id=\"line2d_26\">\n", "      <g>\n", "       <use xlink:href=\"#mda1038f86a\" x=\"51.378125\" y=\"136.373418\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_9\">\n", "     <g id=\"line2d_27\">\n", "      <g>\n", "       <use xlink:href=\"#mda1038f86a\" x=\"51.378125\" y=\"132.910116\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_10\">\n", "     <g id=\"line2d_28\">\n", "      <g>\n", "       <use xlink:href=\"#mda1038f86a\" x=\"51.378125\" y=\"130.22377\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_11\">\n", "     <g id=\"line2d_29\">\n", "      <g>\n", "       <use xlink:href=\"#mda1038f86a\" x=\"51.378125\" y=\"128.028866\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_12\">\n", "     <g id=\"line2d_30\">\n", "      <g>\n", "       <use xlink:href=\"#mda1038f86a\" x=\"51.378125\" y=\"126.173101\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_13\">\n", "     <g id=\"line2d_31\">\n", "      <g>\n", "       <use xlink:href=\"#mda1038f86a\" x=\"51.378125\" y=\"124.565564\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_14\">\n", "     <g id=\"line2d_32\">\n", "      <g>\n", "       <use xlink:href=\"#mda1038f86a\" x=\"51.378125\" y=\"123.147616\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_15\">\n", "     <g id=\"line2d_33\">\n", "      <g>\n", "       <use xlink:href=\"#mda1038f86a\" x=\"51.378125\" y=\"113.534667\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_16\">\n", "     <g id=\"line2d_34\">\n", "      <g>\n", "       <use xlink:href=\"#mda1038f86a\" x=\"51.378125\" y=\"108.653418\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_17\">\n", "     <g id=\"line2d_35\">\n", "      <g>\n", "       <use xlink:href=\"#mda1038f86a\" x=\"51.378125\" y=\"105.190116\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_18\">\n", "     <g id=\"line2d_36\">\n", "      <g>\n", "       <use xlink:href=\"#mda1038f86a\" x=\"51.378125\" y=\"102.50377\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_19\">\n", "     <g id=\"line2d_37\">\n", "      <g>\n", "       <use xlink:href=\"#mda1038f86a\" x=\"51.378125\" y=\"100.308866\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_20\">\n", "     <g id=\"line2d_38\">\n", "      <g>\n", "       <use xlink:href=\"#mda1038f86a\" x=\"51.378125\" y=\"98.453101\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_21\">\n", "     <g id=\"line2d_39\">\n", "      <g>\n", "       <use xlink:href=\"#mda1038f86a\" x=\"51.378125\" y=\"96.845564\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_22\">\n", "     <g id=\"line2d_40\">\n", "      <g>\n", "       <use xlink:href=\"#mda1038f86a\" x=\"51.378125\" y=\"95.427616\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_23\">\n", "     <g id=\"line2d_41\">\n", "      <g>\n", "       <use xlink:href=\"#mda1038f86a\" x=\"51.378125\" y=\"85.814667\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_24\">\n", "     <g id=\"line2d_42\">\n", "      <g>\n", "       <use xlink:href=\"#mda1038f86a\" x=\"51.378125\" y=\"80.933418\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_25\">\n", "     <g id=\"line2d_43\">\n", "      <g>\n", "       <use xlink:href=\"#mda1038f86a\" x=\"51.378125\" y=\"77.470116\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_26\">\n", "     <g id=\"line2d_44\">\n", "      <g>\n", "       <use xlink:href=\"#mda1038f86a\" x=\"51.378125\" y=\"74.78377\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_27\">\n", "     <g id=\"line2d_45\">\n", "      <g>\n", "       <use xlink:href=\"#mda1038f86a\" x=\"51.378125\" y=\"72.588866\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_28\">\n", "     <g id=\"line2d_46\">\n", "      <g>\n", "       <use xlink:href=\"#mda1038f86a\" x=\"51.378125\" y=\"70.733101\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_29\">\n", "     <g id=\"line2d_47\">\n", "      <g>\n", "       <use xlink:href=\"#mda1038f86a\" x=\"51.378125\" y=\"69.125564\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_30\">\n", "     <g id=\"line2d_48\">\n", "      <g>\n", "       <use xlink:href=\"#mda1038f86a\" x=\"51.378125\" y=\"67.707616\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_31\">\n", "     <g id=\"line2d_49\">\n", "      <g>\n", "       <use xlink:href=\"#mda1038f86a\" x=\"51.378125\" y=\"58.094667\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_32\">\n", "     <g id=\"line2d_50\">\n", "      <g>\n", "       <use xlink:href=\"#mda1038f86a\" x=\"51.378125\" y=\"53.213418\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_33\">\n", "     <g id=\"line2d_51\">\n", "      <g>\n", "       <use xlink:href=\"#mda1038f86a\" x=\"51.378125\" y=\"49.750116\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_34\">\n", "     <g id=\"line2d_52\">\n", "      <g>\n", "       <use xlink:href=\"#mda1038f86a\" x=\"51.378125\" y=\"47.06377\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_35\">\n", "     <g id=\"line2d_53\">\n", "      <g>\n", "       <use xlink:href=\"#mda1038f86a\" x=\"51.378125\" y=\"44.868866\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_36\">\n", "     <g id=\"line2d_54\">\n", "      <g>\n", "       <use xlink:href=\"#mda1038f86a\" x=\"51.378125\" y=\"43.013101\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_37\">\n", "     <g id=\"line2d_55\">\n", "      <g>\n", "       <use xlink:href=\"#mda1038f86a\" x=\"51.378125\" y=\"41.405564\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_38\">\n", "     <g id=\"line2d_56\">\n", "      <g>\n", "       <use xlink:href=\"#mda1038f86a\" x=\"51.378125\" y=\"39.987616\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_39\">\n", "     <g id=\"line2d_57\">\n", "      <g>\n", "       <use xlink:href=\"#mda1038f86a\" x=\"51.378125\" y=\"30.374667\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_40\">\n", "     <g id=\"line2d_58\">\n", "      <g>\n", "       <use xlink:href=\"#mda1038f86a\" x=\"51.378125\" y=\"25.493418\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_41\">\n", "     <g id=\"line2d_59\">\n", "      <g>\n", "       <use xlink:href=\"#mda1038f86a\" x=\"51.378125\" y=\"22.030116\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_42\">\n", "     <g id=\"line2d_60\">\n", "      <g>\n", "       <use xlink:href=\"#mda1038f86a\" x=\"51.378125\" y=\"19.34377\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_43\">\n", "     <g id=\"line2d_61\">\n", "      <g>\n", "       <use xlink:href=\"#mda1038f86a\" x=\"51.378125\" y=\"17.148866\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_44\">\n", "     <g id=\"line2d_62\">\n", "      <g>\n", "       <use xlink:href=\"#mda1038f86a\" x=\"51.378125\" y=\"15.293101\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_45\">\n", "     <g id=\"line2d_63\">\n", "      <g>\n", "       <use xlink:href=\"#mda1038f86a\" x=\"51.378125\" y=\"13.685564\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_46\">\n", "     <g id=\"line2d_64\">\n", "      <g>\n", "       <use xlink:href=\"#mda1038f86a\" x=\"51.378125\" y=\"12.267616\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"text_14\">\n", "     <!-- loss -->\n", "     <g transform=\"translate(14.798437 89.957031)rotate(-90)scale(0.1 -0.1)\">\n", "      <defs>\n", "       <path id=\"DejaVuSans-6c\" d=\"M 603 4863 \n", "L 1178 4863 \n", "L 1178 0 \n", "L 603 0 \n", "L 603 4863 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-73\" d=\"M 2834 3397 \n", "L 2834 2853 \n", "Q 2591 2978 2328 3040 \n", "Q 2066 3103 1784 3103 \n", "Q 1356 3103 1142 2972 \n", "Q 928 2841 928 2578 \n", "Q 928 2378 1081 2264 \n", "Q 1234 2150 1697 2047 \n", "L 1894 2003 \n", "Q 2506 1872 2764 1633 \n", "Q 3022 1394 3022 966 \n", "Q 3022 478 2636 193 \n", "Q 2250 -91 1575 -91 \n", "Q 1294 -91 989 -36 \n", "Q 684 19 347 128 \n", "L 347 722 \n", "Q 666 556 975 473 \n", "Q 1284 391 1588 391 \n", "Q 1994 391 2212 530 \n", "Q 2431 669 2431 922 \n", "Q 2431 1156 2273 1281 \n", "Q 2116 1406 1581 1522 \n", "L 1381 1569 \n", "Q 847 1681 609 1914 \n", "Q 372 2147 372 2553 \n", "Q 372 3047 722 3315 \n", "Q 1072 3584 1716 3584 \n", "Q 2034 3584 2315 3537 \n", "Q 2597 3491 2834 3397 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "      </defs>\n", "      <use xlink:href=\"#DejaVuSans-6c\"/>\n", "      <use xlink:href=\"#DejaVuSans-6f\" x=\"27.783203\"/>\n", "      <use xlink:href=\"#DejaVuSans-73\" x=\"88.964844\"/>\n", "      <use xlink:href=\"#DejaVuSans-73\" x=\"141.064453\"/>\n", "     </g>\n", "    </g>\n", "   </g>\n", "   <g id=\"line2d_65\">\n", "    <path d=\"M 51.378125 24.382974 \n", "L 53.853575 73.604405 \n", "L 56.459312 82.167089 \n", "L 59.06505 86.745793 \n", "L 61.670787 90.798632 \n", "L 64.276524 94.520816 \n", "L 66.882261 97.890445 \n", "L 69.487998 100.888198 \n", "L 72.093735 103.517873 \n", "L 74.699473 105.715536 \n", "L 77.30521 107.540391 \n", "L 79.910947 109.008451 \n", "L 82.516684 110.148665 \n", "L 85.122421 111.091129 \n", "L 87.728158 111.819421 \n", "L 90.333896 112.436462 \n", "L 92.939633 112.935154 \n", "L 95.54537 113.35041 \n", "L 98.151107 113.72159 \n", "L 100.756844 114.038758 \n", "L 103.362581 114.331893 \n", "L 105.968318 114.621358 \n", "L 108.574056 114.882594 \n", "L 111.179793 115.129153 \n", "L 113.78553 115.36675 \n", "L 116.391267 115.596429 \n", "L 118.997004 115.813327 \n", "L 121.602741 116.035017 \n", "L 124.208479 116.243752 \n", "L 126.814216 116.438523 \n", "L 129.419953 116.652091 \n", "L 132.02569 116.847762 \n", "L 134.631427 117.040645 \n", "L 137.237164 117.225376 \n", "L 139.842902 117.410575 \n", "L 142.448639 117.590628 \n", "L 145.054376 117.764826 \n", "L 147.660113 117.93522 \n", "L 150.26585 118.105754 \n", "L 152.871587 118.270169 \n", "L 155.477324 118.428416 \n", "L 158.083062 118.585598 \n", "L 160.688799 118.725631 \n", "L 163.294536 118.883383 \n", "L 165.900273 119.031939 \n", "L 168.50601 119.173991 \n", "L 171.111747 119.307259 \n", "L 173.717485 119.445824 \n", "L 176.323222 119.574574 \n", "L 178.928959 119.700188 \n", "L 181.534696 119.82775 \n", "L 184.140433 119.948791 \n", "L 186.74617 120.0535 \n", "L 189.351908 120.174764 \n", "L 191.957645 120.288195 \n", "L 194.563382 120.397807 \n", "L 197.169119 120.500575 \n", "L 199.774856 120.603884 \n", "L 202.380593 120.700364 \n", "L 204.98633 120.793493 \n", "L 207.592068 120.893695 \n", "L 210.197805 120.984758 \n", "L 212.803542 121.067929 \n", "L 215.409279 121.15868 \n", "L 218.015016 121.234237 \n", "L 220.620753 121.318208 \n", "L 223.226491 121.397226 \n", "L 225.832228 121.473812 \n", "L 228.437965 121.542984 \n", "L 231.043702 121.619076 \n", "L 233.649439 121.688078 \n", "L 236.255176 121.755789 \n", "L 238.860914 121.81835 \n", "L 241.466651 121.883127 \n", "L 244.072388 121.943363 \n", "L 246.678125 122.000335 \n", "\" clip-path=\"url(#p81262f4d6a)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_66\">\n", "    <path d=\"M 51.378125 31.429425 \n", "L 53.853575 74.79089 \n", "L 56.459312 85.623676 \n", "L 59.06505 91.782235 \n", "L 61.670787 96.531268 \n", "L 64.276524 100.532702 \n", "L 66.882261 103.727307 \n", "L 69.487998 106.283654 \n", "L 72.093735 108.271505 \n", "L 74.699473 109.585291 \n", "L 77.30521 110.513783 \n", "L 79.910947 111.132863 \n", "L 82.516684 111.547487 \n", "L 85.122421 111.762228 \n", "L 87.728158 111.931985 \n", "L 90.333896 112.049697 \n", "L 92.939633 112.13261 \n", "L 95.54537 112.203163 \n", "L 98.151107 112.291967 \n", "L 100.756844 112.405905 \n", "L 103.362581 112.519517 \n", "L 105.968318 112.597093 \n", "L 108.574056 112.730369 \n", "L 111.179793 112.845553 \n", "L 113.78553 112.976819 \n", "L 116.391267 113.105468 \n", "L 118.997004 113.216893 \n", "L 121.602741 113.359531 \n", "L 124.208479 113.492795 \n", "L 126.814216 113.660354 \n", "L 129.419953 113.760249 \n", "L 132.02569 113.880759 \n", "L 134.631427 114.03167 \n", "L 137.237164 114.143563 \n", "L 139.842902 114.281154 \n", "L 142.448639 114.408912 \n", "L 145.054376 114.545996 \n", "L 147.660113 114.66095 \n", "L 150.26585 114.780396 \n", "L 152.871587 114.892631 \n", "L 155.477324 114.988734 \n", "L 158.083062 115.106022 \n", "L 160.688799 115.177257 \n", "L 163.294536 115.291634 \n", "L 165.900273 115.431262 \n", "L 168.50601 115.526659 \n", "L 171.111747 115.595188 \n", "L 173.717485 115.7117 \n", "L 176.323222 115.779381 \n", "L 178.928959 115.907106 \n", "L 181.534696 115.976261 \n", "L 184.140433 116.068871 \n", "L 186.74617 116.16126 \n", "L 189.351908 116.251922 \n", "L 191.957645 116.293035 \n", "L 194.563382 116.34702 \n", "L 197.169119 116.409059 \n", "L 199.774856 116.520297 \n", "L 202.380593 116.551883 \n", "L 204.98633 116.640459 \n", "L 207.592068 116.714246 \n", "L 210.197805 116.758663 \n", "L 212.803542 116.768713 \n", "L 215.409279 116.866009 \n", "L 218.015016 116.92276 \n", "L 220.620753 116.946607 \n", "L 223.226491 117.047362 \n", "L 225.832228 117.065303 \n", "L 228.437965 117.126909 \n", "L 231.043702 117.149399 \n", "L 233.649439 117.226822 \n", "L 236.255176 117.252686 \n", "L 238.860914 117.277332 \n", "L 241.466651 117.335791 \n", "L 244.072388 117.393738 \n", "L 246.678125 117.391877 \n", "\" clip-path=\"url(#p81262f4d6a)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #bf00bf; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"patch_3\">\n", "    <path d=\"M 51.**********.599219 \n", "L 51.378125 10.999219 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_4\">\n", "    <path d=\"M 246.**********.599219 \n", "L 246.678125 10.999219 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_5\">\n", "    <path d=\"M 51.**********.599219 \n", "L 246.**********.599219 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_6\">\n", "    <path d=\"M 51.378125 10.999219 \n", "L 246.678125 10.999219 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"legend_1\">\n", "    <g id=\"patch_7\">\n", "     <path d=\"M 184.403125 48.355469 \n", "L 239.678125 48.355469 \n", "Q 241.678125 48.355469 241.678125 46.355469 \n", "L 241.678125 17.999219 \n", "Q 241.678125 15.999219 239.678125 15.999219 \n", "L 184.403125 15.999219 \n", "Q 182.403125 15.999219 182.403125 17.999219 \n", "L 182.403125 46.355469 \n", "Q 182.403125 48.355469 184.403125 48.355469 \n", "z\n", "\" style=\"fill: #ffffff; opacity: 0.8; stroke: #cccccc; stroke-linejoin: miter\"/>\n", "    </g>\n", "    <g id=\"line2d_67\">\n", "     <path d=\"M 186.403125 24.097656 \n", "L 196.403125 24.097656 \n", "L 206.403125 24.097656 \n", "\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "    </g>\n", "    <g id=\"text_15\">\n", "     <!-- train -->\n", "     <g transform=\"translate(214.403125 27.597656)scale(0.1 -0.1)\">\n", "      <defs>\n", "       <path id=\"DejaVuSans-74\" d=\"M 1172 4494 \n", "L 1172 3500 \n", "L 2356 3500 \n", "L 2356 3053 \n", "L 1172 3053 \n", "L 1172 1153 \n", "Q 1172 725 1289 603 \n", "Q 1406 481 1766 481 \n", "L 2356 481 \n", "L 2356 0 \n", "L 1766 0 \n", "Q 1100 0 847 248 \n", "Q 594 497 594 1153 \n", "L 594 3053 \n", "L 172 3053 \n", "L 172 3500 \n", "L 594 3500 \n", "L 594 4494 \n", "L 1172 4494 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-72\" d=\"M 2631 2963 \n", "Q 2534 3019 2420 3045 \n", "Q 2306 3072 2169 3072 \n", "Q 1681 3072 1420 2755 \n", "Q 1159 2438 1159 1844 \n", "L 1159 0 \n", "L 581 0 \n", "L 581 3500 \n", "L 1159 3500 \n", "L 1159 2956 \n", "Q 1341 3275 1631 3429 \n", "Q 1922 3584 2338 3584 \n", "Q 2397 3584 2469 3576 \n", "Q 2541 3569 2628 3553 \n", "L 2631 2963 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-61\" d=\"M 2194 1759 \n", "Q 1497 1759 1228 1600 \n", "Q 959 1441 959 1056 \n", "Q 959 750 1161 570 \n", "Q 1363 391 1709 391 \n", "Q 2188 391 2477 730 \n", "Q 2766 1069 2766 1631 \n", "L 2766 1759 \n", "L 2194 1759 \n", "z\n", "M 3341 1997 \n", "L 3341 0 \n", "L 2766 0 \n", "L 2766 531 \n", "Q 2569 213 2275 61 \n", "Q 1981 -91 1556 -91 \n", "Q 1019 -91 701 211 \n", "Q 384 513 384 1019 \n", "Q 384 1609 779 1909 \n", "Q 1175 2209 1959 2209 \n", "L 2766 2209 \n", "L 2766 2266 \n", "Q 2766 2663 2505 2880 \n", "Q 2244 3097 1772 3097 \n", "Q 1472 3097 1187 3025 \n", "Q 903 2953 641 2809 \n", "L 641 3341 \n", "Q 956 3463 1253 3523 \n", "Q 1550 3584 1831 3584 \n", "Q 2591 3584 2966 3190 \n", "Q 3341 2797 3341 1997 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-69\" d=\"M 603 3500 \n", "L 1178 3500 \n", "L 1178 0 \n", "L 603 0 \n", "L 603 3500 \n", "z\n", "M 603 4863 \n", "L 1178 4863 \n", "L 1178 4134 \n", "L 603 4134 \n", "L 603 4863 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-6e\" d=\"M 3513 2113 \n", "L 3513 0 \n", "L 2938 0 \n", "L 2938 2094 \n", "Q 2938 2591 2744 2837 \n", "Q 2550 3084 2163 3084 \n", "Q 1697 3084 1428 2787 \n", "Q 1159 2491 1159 1978 \n", "L 1159 0 \n", "L 581 0 \n", "L 581 3500 \n", "L 1159 3500 \n", "L 1159 2956 \n", "Q 1366 3272 1645 3428 \n", "Q 1925 3584 2291 3584 \n", "Q 2894 3584 3203 3211 \n", "Q 3513 2838 3513 2113 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "      </defs>\n", "      <use xlink:href=\"#DejaVuSans-74\"/>\n", "      <use xlink:href=\"#DejaVuSans-72\" x=\"39.208984\"/>\n", "      <use xlink:href=\"#DejaVuSans-61\" x=\"80.322266\"/>\n", "      <use xlink:href=\"#DejaVuSans-69\" x=\"141.601562\"/>\n", "      <use xlink:href=\"#DejaVuSans-6e\" x=\"169.384766\"/>\n", "     </g>\n", "    </g>\n", "    <g id=\"line2d_68\">\n", "     <path d=\"M 186.403125 38.775781 \n", "L 196.403125 38.775781 \n", "L 206.403125 38.775781 \n", "\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #bf00bf; stroke-width: 1.5\"/>\n", "    </g>\n", "    <g id=\"text_16\">\n", "     <!-- test -->\n", "     <g transform=\"translate(214.403125 42.275781)scale(0.1 -0.1)\">\n", "      <use xlink:href=\"#DejaVuSans-74\"/>\n", "      <use xlink:href=\"#DejaVuSans-65\" x=\"39.208984\"/>\n", "      <use xlink:href=\"#DejaVuSans-73\" x=\"100.732422\"/>\n", "      <use xlink:href=\"#DejaVuSans-74\" x=\"152.832031\"/>\n", "     </g>\n", "    </g>\n", "   </g>\n", "  </g>\n", " </g>\n", " <defs>\n", "  <clipPath id=\"p81262f4d6a\">\n", "   <rect x=\"51.378125\" y=\"10.999219\" width=\"195.3\" height=\"138.6\"/>\n", "  </clipPath>\n", " </defs>\n", "</svg>\n"], "text/plain": ["<Figure size 350x250 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["# 从多项式特征中选取所有维度\n", "train(poly_features[:n_train, :], poly_features[n_train:, :],\n", "      labels[:n_train], labels[n_train:], num_epochs=1500)"]}, {"cell_type": "markdown", "id": "a71f5d62", "metadata": {"origin_pos": 25}, "source": ["在接下来的章节中，我们将继续讨论过拟合问题和处理这些问题的方法，例如权重衰减和dropout。\n", "\n", "## 小结\n", "\n", "* 欠拟合是指模型无法继续减少训练误差。过拟合是指训练误差远小于验证误差。\n", "* 由于不能基于训练误差来估计泛化误差，因此简单地最小化训练误差并不一定意味着泛化误差的减小。机器学习模型需要注意防止过拟合，即防止泛化误差过大。\n", "* 验证集可以用于模型选择，但不能过于随意地使用它。\n", "* 我们应该选择一个复杂度适当的模型，避免使用数量不足的训练样本。\n", "\n", "## 练习\n", "\n", "1. 这个多项式回归问题可以准确地解出吗？提示：使用线性代数。\n", "1. 考虑多项式的模型选择。\n", "    1. 绘制训练损失与模型复杂度（多项式的阶数）的关系图。观察到了什么？需要多少阶的多项式才能将训练损失减少到0?\n", "    1. 在这种情况下绘制测试的损失图。\n", "    1. 生成同样的图，作为数据量的函数。\n", "1. 如果不对多项式特征$x^i$进行标准化($1/i!$)，会发生什么事情？能用其他方法解决这个问题吗？\n", "1. 泛化误差可能为零吗？\n"]}, {"cell_type": "markdown", "id": "4982f3f1", "metadata": {"origin_pos": 27, "tab": ["pytorch"]}, "source": ["[Discussions](https://discuss.d2l.ai/t/1806)\n"]}], "metadata": {"kernelspec": {"display_name": "d2l", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.23"}}, "nbformat": 4, "nbformat_minor": 5}