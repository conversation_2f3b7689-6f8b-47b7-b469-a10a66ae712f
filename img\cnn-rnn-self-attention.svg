<?xml version="1.0" encoding="UTF-8"?>
<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="377pt" height="251pt" viewBox="0 0 377 251" version="1.1">
<defs>
<g>
<symbol overflow="visible" id="glyph0-0">
<path style="stroke:none;" d=""/>
</symbol>
<symbol overflow="visible" id="glyph0-1">
<path style="stroke:none;" d="M 4.359375 0 L 4.359375 -0.21875 C 4.21875 -0.265625 4.15625 -0.296875 4.09375 -0.390625 L 2.75 -2.453125 L 3.65625 -3.578125 C 3.828125 -3.78125 4.015625 -3.890625 4.296875 -3.9375 L 4.296875 -4.15625 L 2.78125 -4.15625 L 2.78125 -3.9375 L 2.96875 -3.921875 C 3.171875 -3.890625 3.234375 -3.859375 3.234375 -3.71875 C 3.234375 -3.578125 3.140625 -3.453125 2.890625 -3.140625 L 2.578125 -2.734375 C 2.53125 -2.78125 2.5 -2.828125 2.453125 -2.875 C 2.15625 -3.265625 1.9375 -3.640625 1.9375 -3.765625 C 1.9375 -3.875 2.0625 -3.921875 2.359375 -3.9375 L 2.359375 -4.15625 L 0.109375 -4.15625 L 0.109375 -3.9375 C 0.34375 -3.890625 0.390625 -3.859375 0.578125 -3.578125 L 1.734375 -1.8125 C 1.59375 -1.640625 1.671875 -1.734375 1.53125 -1.5625 L 1.140625 -1.046875 C 0.640625 -0.375 0.453125 -0.234375 0.125 -0.21875 L 0.125 0 L 1.640625 0 L 1.640625 -0.21875 C 1.328125 -0.234375 1.203125 -0.296875 1.203125 -0.4375 C 1.203125 -0.578125 1.421875 -0.9375 1.765625 -1.359375 C 1.828125 -1.421875 1.875 -1.484375 1.921875 -1.53125 C 2.03125 -1.34375 2.15625 -1.15625 2.296875 -0.96875 C 2.515625 -0.65625 2.59375 -0.5 2.59375 -0.390625 C 2.59375 -0.28125 2.46875 -0.234375 2.1875 -0.21875 L 2.1875 0 Z M 4.359375 0 "/>
</symbol>
<symbol overflow="visible" id="glyph1-0">
<path style="stroke:none;" d=""/>
</symbol>
<symbol overflow="visible" id="glyph1-1">
<path style="stroke:none;" d="M 2.359375 0 L 2.359375 -0.09375 C 1.90625 -0.09375 1.796875 -0.203125 1.796875 -0.453125 L 1.796875 -4.03125 L 1.734375 -4.0625 L 0.671875 -3.515625 L 0.671875 -3.421875 L 0.828125 -3.484375 C 0.9375 -3.53125 1.03125 -3.5625 1.09375 -3.5625 C 1.21875 -3.5625 1.28125 -3.46875 1.28125 -3.265625 L 1.28125 -0.5625 C 1.28125 -0.234375 1.15625 -0.109375 0.703125 -0.09375 L 0.703125 0 Z M 2.359375 0 "/>
</symbol>
<symbol overflow="visible" id="glyph1-2">
<path style="stroke:none;" d="M 2.84375 -0.828125 L 2.765625 -0.859375 C 2.5625 -0.515625 2.4375 -0.453125 2.1875 -0.453125 L 0.78125 -0.453125 L 1.765625 -1.515625 C 2.296875 -2.078125 2.53125 -2.53125 2.53125 -3 C 2.53125 -3.59375 2.109375 -4.0625 1.421875 -4.0625 C 0.6875 -4.0625 0.3125 -3.5625 0.1875 -2.859375 L 0.3125 -2.828125 C 0.546875 -3.421875 0.75 -3.609375 1.1875 -3.609375 C 1.703125 -3.609375 2.015625 -3.3125 2.015625 -2.765625 C 2.015625 -2.25 1.8125 -1.796875 1.234375 -1.203125 L 0.171875 -0.078125 L 0.171875 0 L 2.515625 0 Z M 2.84375 -0.828125 "/>
</symbol>
<symbol overflow="visible" id="glyph1-3">
<path style="stroke:none;" d="M 0.359375 -3.0625 C 0.609375 -3.5 0.890625 -3.703125 1.265625 -3.703125 C 1.65625 -3.703125 1.90625 -3.484375 1.90625 -3.078125 C 1.90625 -2.71875 1.71875 -2.453125 1.421875 -2.28125 C 1.296875 -2.203125 1.140625 -2.140625 0.921875 -2.0625 L 0.921875 -1.984375 C 1.265625 -1.984375 1.390625 -1.96875 1.53125 -1.921875 C 1.9375 -1.796875 2.15625 -1.5 2.15625 -1.046875 C 2.15625 -0.546875 1.8125 -0.125 1.375 -0.125 C 1.203125 -0.125 1.078125 -0.15625 0.859375 -0.3125 C 0.6875 -0.4375 0.59375 -0.46875 0.484375 -0.46875 C 0.359375 -0.46875 0.25 -0.390625 0.25 -0.265625 C 0.25 -0.046875 0.484375 0.078125 0.921875 0.078125 C 1.453125 0.078125 2.015625 -0.09375 2.3125 -0.46875 C 2.484375 -0.703125 2.59375 -1 2.59375 -1.3125 C 2.59375 -1.625 2.484375 -1.90625 2.328125 -2.09375 C 2.203125 -2.21875 2.09375 -2.296875 1.828125 -2.40625 C 2.21875 -2.640625 2.375 -2.953125 2.375 -3.234375 C 2.375 -3.71875 2 -4.0625 1.453125 -4.0625 C 0.828125 -4.0625 0.4375 -3.65625 0.265625 -3.078125 Z M 0.359375 -3.0625 "/>
</symbol>
<symbol overflow="visible" id="glyph1-4">
<path style="stroke:none;" d="M 2.84375 -1 L 2.84375 -1.390625 L 2.21875 -1.390625 L 2.21875 -4.0625 L 1.953125 -4.0625 L 0.078125 -1.390625 L 0.078125 -1 L 1.75 -1 L 1.75 0 L 2.21875 0 L 2.21875 -1 Z M 1.75 -1.390625 L 0.3125 -1.390625 L 1.75 -3.4375 Z M 1.75 -1.390625 "/>
</symbol>
<symbol overflow="visible" id="glyph1-5">
<path style="stroke:none;" d="M 2.625 -4.09375 L 2.578125 -4.125 C 2.484375 -4 2.421875 -3.96875 2.28125 -3.96875 L 1.046875 -3.96875 L 0.390625 -2.546875 C 0.390625 -2.546875 0.390625 -2.53125 0.390625 -2.515625 C 0.390625 -2.484375 0.40625 -2.46875 0.453125 -2.46875 C 1.03125 -2.46875 1.453125 -2.28125 1.734375 -2.046875 C 2 -1.84375 2.140625 -1.53125 2.140625 -1.15625 C 2.140625 -0.640625 1.75 -0.140625 1.328125 -0.140625 C 1.203125 -0.140625 1.0625 -0.1875 0.90625 -0.328125 C 0.703125 -0.484375 0.59375 -0.515625 0.453125 -0.515625 C 0.28125 -0.515625 0.1875 -0.4375 0.1875 -0.28125 C 0.1875 -0.0625 0.5 0.078125 0.953125 0.078125 C 1.359375 0.078125 1.6875 0 1.96875 -0.203125 C 2.375 -0.515625 2.5625 -0.875 2.5625 -1.453125 C 2.5625 -1.78125 2.5 -2 2.34375 -2.21875 C 2 -2.6875 1.703125 -2.828125 0.84375 -2.984375 L 1.09375 -3.5 L 2.25 -3.5 C 2.34375 -3.5 2.390625 -3.53125 2.40625 -3.578125 Z M 2.625 -4.09375 "/>
</symbol>
<symbol overflow="visible" id="glyph2-0">
<path style="stroke:none;" d=""/>
</symbol>
<symbol overflow="visible" id="glyph2-1">
<path style="stroke:none;" d="M 7.953125 -7.265625 C 6.578125 -6.953125 5.03125 -6.78125 3.328125 -6.765625 L 3.328125 -3.28125 C 3.28125 -1.703125 3.015625 -0.484375 2.515625 0.40625 L 2.96875 0.890625 C 3.578125 -0.15625 3.890625 -1.53125 3.9375 -3.28125 L 3.9375 -4.90625 L 6 -4.90625 L 5.921875 -4.078125 L 4.484375 -4.078125 L 4.484375 0.890625 L 5.09375 0.890625 L 5.09375 0.328125 L 7.5 0.328125 L 7.5 0.90625 L 8.109375 0.90625 L 8.109375 -4.078125 L 6.546875 -4.078125 C 6.578125 -4.34375 6.609375 -4.609375 6.640625 -4.90625 L 8.484375 -4.90625 L 8.484375 -5.484375 L 6.6875 -5.484375 L 6.71875 -6.125 L 6.078125 -6.21875 L 6.046875 -5.484375 L 3.9375 -5.484375 L 3.9375 -6.234375 C 5.53125 -6.265625 6.96875 -6.4375 8.265625 -6.71875 Z M 5.09375 -0.21875 L 5.09375 -1.015625 L 7.5 -1.015625 L 7.5 -0.21875 Z M 5.09375 -1.515625 L 5.09375 -2.28125 L 7.5 -2.28125 L 7.5 -1.515625 Z M 5.09375 -2.78125 L 5.09375 -3.53125 L 7.5 -3.53125 L 7.5 -2.78125 Z M 2.234375 -7.234375 C 1.875 -6.40625 1.28125 -5.671875 0.484375 -5.03125 L 0.671875 -4.390625 C 1.65625 -5.140625 2.359375 -6 2.796875 -6.96875 Z M 2.40625 -4.96875 C 1.984375 -4.09375 1.3125 -3.296875 0.375 -2.609375 L 0.5625 -1.984375 C 0.875 -2.21875 1.171875 -2.453125 1.453125 -2.703125 L 1.453125 0.859375 L 2.078125 0.859375 L 2.078125 -3.34375 C 2.4375 -3.765625 2.734375 -4.21875 2.96875 -4.703125 Z M 2.40625 -4.96875 "/>
</symbol>
<symbol overflow="visible" id="glyph2-2">
<path style="stroke:none;" d="M 6.921875 -4.46875 L 6.421875 -4.15625 C 7.15625 -3.234375 7.734375 -2.40625 8.171875 -1.65625 L 8.71875 -2.03125 C 8.296875 -2.734375 7.6875 -3.53125 6.921875 -4.46875 Z M 0.625 -4.078125 L 0.625 -3.453125 L 1.625 -3.453125 L 1.625 -1.140625 C 1.203125 -1.03125 0.796875 -0.9375 0.375 -0.875 L 0.53125 -0.25 C 1.578125 -0.484375 2.546875 -0.78125 3.453125 -1.125 L 3.453125 -1.75 C 3.0625 -1.59375 2.65625 -1.453125 2.25 -1.328125 L 2.25 -3.453125 L 3.28125 -3.453125 L 3.28125 -4.078125 L 2.25 -4.078125 L 2.25 -6.015625 L 3.40625 -6.015625 L 3.40625 -6.65625 L 0.484375 -6.65625 L 0.484375 -6.015625 L 1.625 -6.015625 L 1.625 -4.078125 Z M 3.765625 -6.90625 L 3.765625 -6.28125 L 6.078125 -6.28125 C 5.5625 -4.640625 4.65625 -3.234375 3.34375 -2.0625 L 3.734375 -1.5625 C 4.515625 -2.25 5.15625 -3.03125 5.671875 -3.90625 L 5.671875 0.890625 L 6.3125 0.890625 L 6.3125 -5.140625 C 6.46875 -5.515625 6.59375 -5.890625 6.71875 -6.28125 L 8.328125 -6.28125 L 8.328125 -6.90625 Z M 3.765625 -6.90625 "/>
</symbol>
<symbol overflow="visible" id="glyph2-3">
<path style="stroke:none;" d="M 4.421875 -3.53125 L 5.6875 -3.53125 L 5.6875 -2.296875 L 4.421875 -2.296875 Z M 6.328125 -3.53125 L 7.625 -3.53125 L 7.625 -2.296875 L 6.328125 -2.296875 Z M 7.625 -1.671875 L 7.625 -1.1875 L 8.28125 -1.1875 L 8.28125 -5.96875 L 6.328125 -5.96875 L 6.328125 -7.375 L 5.6875 -7.375 L 5.6875 -5.96875 L 3.765625 -5.96875 L 3.765625 -1.171875 L 4.421875 -1.171875 L 4.421875 -1.671875 L 5.6875 -1.671875 L 5.6875 0.875 L 6.328125 0.875 L 6.328125 -1.671875 Z M 7.625 -4.109375 L 6.328125 -4.109375 L 6.328125 -5.359375 L 7.625 -5.359375 Z M 5.6875 -4.109375 L 4.421875 -4.109375 L 4.421875 -5.359375 L 5.6875 -5.359375 Z M 1.6875 -2.859375 L 1.6875 0.90625 L 2.328125 0.90625 L 2.328125 -2.875 C 2.609375 -2.640625 2.890625 -2.375 3.1875 -2.046875 L 3.53125 -2.578125 C 3.171875 -2.953125 2.765625 -3.28125 2.328125 -3.546875 L 2.328125 -3.625 C 2.78125 -4.25 3.125 -4.890625 3.34375 -5.53125 L 3.34375 -6.078125 L 2.234375 -6.078125 L 2.5 -6.1875 C 2.375 -6.5625 2.21875 -6.96875 2.03125 -7.421875 L 1.40625 -7.234375 C 1.609375 -6.859375 1.765625 -6.484375 1.90625 -6.078125 L 0.5625 -6.078125 L 0.5625 -5.46875 L 2.6875 -5.46875 C 2.296875 -4.484375 1.53125 -3.46875 0.375 -2.453125 L 0.5625 -1.765625 C 0.984375 -2.125 1.34375 -2.5 1.6875 -2.859375 Z M 1.6875 -2.859375 "/>
</symbol>
<symbol overflow="visible" id="glyph2-4">
<path style="stroke:none;" d="M 5.640625 -2.03125 L 5.640625 -0.03125 L 3.46875 -0.03125 L 3.46875 0.59375 L 8.5 0.59375 L 8.5 -0.03125 L 6.3125 -0.03125 L 6.3125 -2.03125 L 8.078125 -2.03125 L 8.078125 -2.65625 L 3.875 -2.65625 L 3.875 -2.03125 Z M 6.265625 -4.5625 C 7.046875 -4.046875 7.671875 -3.53125 8.140625 -3.0625 L 8.609375 -3.53125 C 8.09375 -4.015625 7.4375 -4.484375 6.6875 -4.96875 C 7.09375 -5.40625 7.421875 -5.890625 7.65625 -6.4375 L 7.65625 -6.953125 L 3.8125 -6.953125 L 3.8125 -6.328125 L 6.890625 -6.328125 C 6.234375 -5.171875 5.0625 -4.28125 3.390625 -3.640625 L 3.78125 -3.109375 C 4.78125 -3.5 5.609375 -3.984375 6.265625 -4.5625 Z M 3.390625 -1.09375 C 2.53125 -0.671875 1.546875 -0.3125 0.421875 -0.046875 L 0.515625 0.5625 C 1.578125 0.296875 2.53125 -0.046875 3.390625 -0.484375 Z M 1.953125 -7.359375 C 1.609375 -6.3125 1.1875 -5.375 0.71875 -4.578125 C 0.625 -4.453125 0.546875 -4.375 0.4375 -4.328125 L 0.609375 -3.734375 C 1.109375 -3.8125 1.625 -3.875 2.140625 -3.953125 C 1.640625 -3.09375 1.203125 -2.484375 0.84375 -2.125 C 0.765625 -2.046875 0.671875 -1.984375 0.5625 -1.9375 L 0.734375 -1.34375 C 1.578125 -1.46875 2.46875 -1.6875 3.40625 -2 L 3.40625 -2.59375 C 2.765625 -2.375 2.15625 -2.203125 1.515625 -2.0625 C 2.0625 -2.703125 2.765625 -3.78125 3.59375 -5.3125 L 3 -5.53125 C 2.8125 -5.1875 2.640625 -4.84375 2.46875 -4.53125 C 2.046875 -4.46875 1.625 -4.40625 1.21875 -4.375 C 1.671875 -5.0625 2.125 -5.96875 2.578125 -7.125 Z M 1.953125 -7.359375 "/>
</symbol>
<symbol overflow="visible" id="glyph2-5">
<path style="stroke:none;" d="M 4.234375 -1.140625 L 4.671875 -1.578125 C 4.3125 -2.140625 3.953125 -2.640625 3.609375 -3.109375 C 3.84375 -3.875 4.03125 -4.71875 4.171875 -5.65625 L 3.578125 -5.765625 C 3.46875 -5.046875 3.328125 -4.359375 3.15625 -3.71875 C 2.78125 -4.234375 2.421875 -4.65625 2.09375 -5 L 1.6875 -4.609375 C 2.09375 -4.140625 2.515625 -3.625 2.953125 -3.046875 C 2.640625 -2.140625 2.265625 -1.34375 1.796875 -0.671875 L 2.203125 -0.171875 C 2.65625 -0.796875 3.0625 -1.5625 3.390625 -2.4375 C 3.65625 -2.03125 3.953125 -1.609375 4.234375 -1.140625 Z M 6.875 -0.734375 L 7.3125 -1.15625 C 6.9375 -1.8125 6.5625 -2.390625 6.21875 -2.921875 C 6.484375 -3.71875 6.703125 -4.625 6.859375 -5.625 L 6.25 -5.734375 C 6.125 -4.953125 5.96875 -4.234375 5.78125 -3.5625 C 5.40625 -4.09375 5.046875 -4.546875 4.71875 -4.90625 L 4.3125 -4.53125 C 4.71875 -4.015625 5.125 -3.46875 5.546875 -2.859375 C 5.21875 -1.9375 4.78125 -1.140625 4.28125 -0.453125 L 4.703125 0.046875 C 5.1875 -0.578125 5.609375 -1.34375 5.96875 -2.21875 C 6.265625 -1.75 6.5625 -1.265625 6.875 -0.734375 Z M 7.296875 0.84375 C 7.828125 0.84375 8.09375 0.5625 8.09375 0.015625 L 8.09375 -6.984375 L 0.90625 -6.984375 L 0.90625 0.875 L 1.546875 0.875 L 1.546875 -6.359375 L 7.453125 -6.359375 L 7.453125 -0.125 C 7.453125 0.109375 7.328125 0.25 7.078125 0.25 L 6.203125 0.21875 L 6.375 0.84375 Z M 7.296875 0.84375 "/>
</symbol>
<symbol overflow="visible" id="glyph2-6">
<path style="stroke:none;" d="M 3.625 -0.890625 C 2.75 -0.5625 1.703125 -0.28125 0.53125 -0.09375 L 0.609375 0.546875 C 1.75 0.328125 2.75 0.0625 3.625 -0.28125 Z M 2.015625 -7.375 C 1.65625 -6.3125 1.234375 -5.375 0.765625 -4.546875 C 0.6875 -4.4375 0.59375 -4.359375 0.5 -4.3125 L 0.65625 -3.71875 C 1.15625 -3.78125 1.640625 -3.875 2.125 -3.953125 C 1.640625 -3.09375 1.234375 -2.5 0.921875 -2.140625 C 0.84375 -2.0625 0.75 -2 0.625 -1.9375 L 0.796875 -1.359375 C 1.640625 -1.46875 2.53125 -1.703125 3.453125 -2.03125 L 3.453125 -2.609375 C 2.8125 -2.375 2.1875 -2.203125 1.5625 -2.0625 C 2.0625 -2.703125 2.703125 -3.78125 3.46875 -5.3125 L 2.921875 -5.515625 C 2.75 -5.1875 2.59375 -4.859375 2.4375 -4.546875 C 2.046875 -4.484375 1.65625 -4.421875 1.265625 -4.359375 C 1.71875 -5.046875 2.171875 -5.96875 2.625 -7.140625 Z M 7.859375 -2.46875 L 4.171875 -2.46875 L 4.171875 0.84375 L 4.78125 0.84375 L 4.78125 0.453125 L 7.25 0.453125 L 7.25 0.84375 L 7.859375 0.84375 Z M 4.78125 -0.140625 L 4.78125 -1.875 L 7.25 -1.875 L 7.25 -0.140625 Z M 5.0625 -7.375 C 4.765625 -6.421875 4.234375 -5.609375 3.421875 -4.953125 L 3.8125 -4.453125 C 4.109375 -4.6875 4.375 -4.953125 4.609375 -5.25 C 4.859375 -4.84375 5.140625 -4.46875 5.484375 -4.125 C 4.875 -3.6875 4.125 -3.296875 3.265625 -2.984375 L 3.65625 -2.453125 C 4.53125 -2.796875 5.3125 -3.21875 5.953125 -3.703125 C 6.609375 -3.1875 7.390625 -2.796875 8.34375 -2.5 L 8.71875 -3.03125 C 7.828125 -3.28125 7.0625 -3.640625 6.4375 -4.109375 C 7.0625 -4.671875 7.53125 -5.328125 7.859375 -6.078125 L 7.859375 -6.546875 L 5.421875 -6.546875 C 5.515625 -6.765625 5.609375 -7 5.671875 -7.25 Z M 4.96875 -5.703125 C 5.015625 -5.78125 5.078125 -5.875 5.125 -5.96875 L 7.15625 -5.96875 C 6.859375 -5.421875 6.46875 -4.9375 5.953125 -4.5 C 5.5625 -4.84375 5.234375 -5.25 4.96875 -5.703125 Z M 4.96875 -5.703125 "/>
</symbol>
<symbol overflow="visible" id="glyph2-7">
<path style="stroke:none;" d="M 2.359375 -7.234375 L 1.78125 -7.03125 C 2.046875 -6.671875 2.296875 -6.25 2.53125 -5.78125 L 3.078125 -6.03125 C 2.875 -6.453125 2.640625 -6.84375 2.359375 -7.234375 Z M 6.78125 -7.234375 C 6.5625 -6.78125 6.296875 -6.390625 5.953125 -6.0625 L 6.46875 -5.765625 C 6.828125 -6.125 7.125 -6.546875 7.359375 -7.015625 Z M 1.15625 -5.71875 L 1.15625 -5.125 L 3.765625 -5.125 C 3.625 -4.8125 3.4375 -4.5 3.234375 -4.21875 L 0.640625 -4.21875 L 0.640625 -3.625 L 2.78125 -3.625 C 2.15625 -2.921875 1.359375 -2.34375 0.390625 -1.921875 L 0.75 -1.359375 C 1.34375 -1.640625 1.890625 -1.984375 2.375 -2.375 L 2.375 0.078125 C 2.375 0.546875 2.640625 0.78125 3.1875 0.78125 L 6.515625 0.78125 C 7 0.78125 7.3125 0.703125 7.5 0.578125 C 7.6875 0.40625 7.8125 -0.03125 7.890625 -0.734375 L 7.25 -0.9375 C 7.203125 -0.484375 7.125 -0.1875 7.03125 -0.015625 C 6.953125 0.109375 6.71875 0.1875 6.34375 0.1875 L 3.453125 0.1875 C 3.171875 0.1875 3.03125 0.078125 3.03125 -0.125 L 3.03125 -2.15625 L 5.921875 -2.15625 L 5.921875 -1.484375 C 5.921875 -1.296875 5.796875 -1.203125 5.5625 -1.203125 C 5.1875 -1.203125 4.78125 -1.21875 4.328125 -1.234375 L 4.46875 -0.671875 C 4.90625 -0.65625 5.34375 -0.640625 5.8125 -0.640625 C 6.296875 -0.671875 6.546875 -0.890625 6.5625 -1.3125 L 6.5625 -2.53125 C 7.015625 -2.15625 7.5625 -1.796875 8.203125 -1.453125 L 8.625 -1.984375 C 7.53125 -2.46875 6.71875 -3 6.234375 -3.625 L 8.359375 -3.625 L 8.359375 -4.21875 L 3.984375 -4.21875 C 4.15625 -4.5 4.296875 -4.8125 4.453125 -5.125 L 7.859375 -5.125 L 7.859375 -5.71875 L 4.671875 -5.71875 C 4.828125 -6.203125 4.96875 -6.734375 5.0625 -7.296875 L 4.421875 -7.375 C 4.328125 -6.78125 4.1875 -6.234375 4.015625 -5.71875 Z M 3.578125 -3.625 L 5.578125 -3.625 C 5.78125 -3.3125 6.03125 -3.015625 6.34375 -2.734375 L 2.78125 -2.734375 C 3.0625 -3 3.328125 -3.296875 3.578125 -3.625 Z M 3.578125 -3.625 "/>
</symbol>
<symbol overflow="visible" id="glyph2-8">
<path style="stroke:none;" d="M 0.6875 -5.96875 C 1.125 -6.015625 1.53125 -6.09375 1.921875 -6.171875 L 1.921875 -4.890625 L 0.59375 -4.890625 L 0.59375 -4.265625 L 1.890625 -4.265625 C 1.578125 -3.21875 1.046875 -2.296875 0.328125 -1.484375 L 0.59375 -0.8125 C 1.140625 -1.515625 1.578125 -2.328125 1.921875 -3.203125 L 1.921875 0.859375 L 2.53125 0.859375 L 2.53125 -3.25 C 2.796875 -2.890625 3.109375 -2.390625 3.484375 -1.765625 L 3.84375 -2.296875 C 3.390625 -2.859375 2.96875 -3.359375 2.53125 -3.8125 L 2.53125 -4.265625 L 3.640625 -4.265625 L 3.640625 -4.890625 L 2.53125 -4.890625 L 2.53125 -6.3125 C 2.90625 -6.40625 3.265625 -6.5 3.59375 -6.625 L 3.359375 -7.234375 C 2.5625 -6.90625 1.625 -6.703125 0.5625 -6.59375 Z M 4.171875 -6.8125 L 4.171875 -2.625 L 8.0625 -2.625 L 8.0625 -6.8125 Z M 7.421875 -3.234375 L 4.828125 -3.234375 L 4.828125 -6.1875 L 7.421875 -6.1875 Z M 4.953125 -2.046875 C 4.546875 -1.046875 4.046875 -0.1875 3.46875 0.53125 L 4.015625 0.921875 C 4.59375 0.15625 5.109375 -0.734375 5.53125 -1.765625 Z M 7.15625 -2.09375 L 6.640625 -1.765625 C 7.265625 -0.796875 7.78125 0.09375 8.15625 0.90625 L 8.703125 0.515625 C 8.34375 -0.21875 7.828125 -1.09375 7.15625 -2.09375 Z M 7.15625 -2.09375 "/>
</symbol>
<symbol overflow="visible" id="glyph2-9">
<path style="stroke:none;" d="M 7.53125 -6.28125 L 4.078125 -6.28125 C 4.25 -6.59375 4.40625 -6.9375 4.5 -7.296875 L 3.84375 -7.375 C 3.75 -7 3.59375 -6.640625 3.40625 -6.28125 L 1.46875 -6.28125 L 1.46875 0.890625 L 2.140625 0.890625 L 2.140625 0.328125 L 6.859375 0.328125 L 6.859375 0.890625 L 7.53125 0.890625 Z M 2.140625 -0.28125 L 2.140625 -1.75 L 6.859375 -1.75 L 6.859375 -0.28125 Z M 2.140625 -2.34375 L 2.140625 -3.71875 L 6.859375 -3.71875 L 6.859375 -2.34375 Z M 2.140625 -4.3125 L 2.140625 -5.65625 L 6.859375 -5.65625 L 6.859375 -4.3125 Z M 2.140625 -4.3125 "/>
</symbol>
<symbol overflow="visible" id="glyph2-10">
<path style="stroke:none;" d="M 1.171875 -7.21875 L 0.71875 -6.765625 C 1.375 -6.296875 1.90625 -5.84375 2.28125 -5.421875 L 2.75 -5.890625 C 2.328125 -6.328125 1.796875 -6.765625 1.171875 -7.21875 Z M 0.953125 -5.09375 L 0.5 -4.65625 C 1.125 -4.171875 1.625 -3.71875 1.984375 -3.296875 L 2.4375 -3.765625 C 2.046875 -4.21875 1.546875 -4.65625 0.953125 -5.09375 Z M 2.09375 -2.609375 C 1.6875 -1.53125 1.203125 -0.484375 0.671875 0.546875 L 1.3125 0.828125 C 1.8125 -0.171875 2.265625 -1.234375 2.65625 -2.375 Z M 3.09375 -5.828125 L 3.09375 -5.1875 L 5.4375 -5.1875 L 5.4375 -3.03125 L 3.34375 -3.03125 L 3.34375 -2.40625 L 5.4375 -2.40625 L 5.4375 -0.046875 L 2.875 -0.046875 L 2.875 0.59375 L 8.59375 0.59375 L 8.59375 -0.046875 L 6.09375 -0.046875 L 6.09375 -2.40625 L 8.171875 -2.40625 L 8.171875 -3.03125 L 6.09375 -3.03125 L 6.09375 -5.1875 L 8.421875 -5.1875 L 8.421875 -5.828125 L 5.734375 -5.828125 L 6.109375 -5.96875 C 5.96875 -6.40625 5.765625 -6.875 5.515625 -7.390625 L 4.90625 -7.234375 C 5.140625 -6.78125 5.34375 -6.3125 5.515625 -5.828125 Z M 3.09375 -5.828125 "/>
</symbol>
<symbol overflow="visible" id="glyph2-11">
<path style="stroke:none;" d="M 2.46875 -6.25 L 2.75 -5.53125 L 0.59375 -5.53125 L 0.59375 -4.96875 L 8.40625 -4.96875 L 8.40625 -5.53125 L 6.171875 -5.53125 L 6.515625 -6.25 L 7.984375 -6.25 L 7.984375 -6.796875 L 4.8125 -6.796875 C 4.71875 -7.03125 4.625 -7.25 4.53125 -7.4375 L 3.84375 -7.328125 C 3.9375 -7.171875 4.015625 -7 4.109375 -6.796875 L 1.015625 -6.796875 L 1.015625 -6.25 Z M 3.421875 -5.53125 L 3.140625 -6.25 L 5.84375 -6.25 L 5.484375 -5.53125 Z M 1.625 -4.4375 L 1.625 -1.765625 L 7.375 -1.765625 L 7.375 -4.4375 Z M 6.75 -2.28125 L 2.25 -2.28125 L 2.25 -2.875 L 6.75 -2.875 Z M 2.25 -3.34375 L 2.25 -3.921875 L 6.75 -3.921875 L 6.75 -3.34375 Z M 1.265625 -1.34375 C 1.09375 -0.75 0.8125 -0.203125 0.453125 0.28125 L 1.015625 0.640625 C 1.390625 0.109375 1.671875 -0.5 1.890625 -1.21875 Z M 5.734375 0.75 C 6.078125 0.75 6.3125 0.6875 6.484375 0.578125 C 6.65625 0.4375 6.78125 0.0625 6.84375 -0.5625 L 6.234375 -0.75 C 6.203125 -0.3125 6.125 -0.03125 6.015625 0.0625 C 5.953125 0.140625 5.8125 0.171875 5.609375 0.171875 L 3.71875 0.171875 C 3.4375 0.171875 3.296875 0.03125 3.296875 -0.21875 L 3.296875 -1.265625 L 2.625 -1.265625 L 2.625 -0.078125 C 2.625 0.484375 2.921875 0.75 3.53125 0.75 Z M 4.59375 -1.609375 L 4.046875 -1.265625 C 4.421875 -0.875 4.71875 -0.53125 4.9375 -0.203125 L 5.46875 -0.578125 C 5.28125 -0.875 4.984375 -1.21875 4.59375 -1.609375 Z M 7.421875 -1.4375 L 6.90625 -1.109375 C 7.390625 -0.515625 7.765625 0.03125 8.0625 0.515625 L 8.59375 0.140625 C 8.328125 -0.3125 7.921875 -0.828125 7.421875 -1.4375 Z M 7.421875 -1.4375 "/>
</symbol>
<symbol overflow="visible" id="glyph2-12">
<path style="stroke:none;" d="M 3.75 -7.328125 L 3.75 -6.421875 C 3.75 -6.125 3.734375 -5.8125 3.71875 -5.515625 L 1.046875 -5.515625 L 1.046875 -4.875 L 3.65625 -4.875 C 3.5625 -3.828125 3.359375 -2.921875 3.0625 -2.171875 C 2.59375 -1.140625 1.765625 -0.28125 0.578125 0.34375 L 0.9375 0.890625 C 2.140625 0.28125 3 -0.578125 3.546875 -1.671875 C 3.9375 -2.53125 4.1875 -3.59375 4.328125 -4.875 L 7.171875 -4.875 C 7.125 -2.75 7.015625 -1.359375 6.84375 -0.703125 C 6.6875 -0.078125 6.359375 0.234375 5.84375 0.234375 C 5.515625 0.234375 5.171875 0.21875 4.8125 0.203125 L 4.984375 0.84375 L 6.171875 0.84375 C 6.640625 0.796875 6.96875 0.609375 7.1875 0.25 C 7.40625 -0.125 7.5625 -0.796875 7.65625 -1.734375 C 7.734375 -2.703125 7.796875 -3.953125 7.828125 -5.515625 L 4.390625 -5.515625 C 4.40625 -5.8125 4.40625 -6.109375 4.421875 -6.421875 L 4.421875 -7.328125 Z M 3.75 -7.328125 "/>
</symbol>
</g>
</defs>
<g id="surface1">
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 250.234375 382.316406 C 254.863281 386.945312 254.863281 394.449219 250.234375 399.078125 C 245.605469 403.707031 238.101562 403.707031 233.472656 399.078125 C 228.84375 394.449219 228.84375 386.945312 233.472656 382.316406 C 238.101562 377.6875 245.605469 377.6875 250.234375 382.316406 " transform="matrix(1,0,0,1,-6,-153)"/>
<g style="fill:rgb(0%,0%,0%);fill-opacity:1;">
  <use xlink:href="#glyph0-1" x="232.1027" y="239.1971"/>
</g>
<g style="fill:rgb(0%,0%,0%);fill-opacity:1;">
  <use xlink:href="#glyph1-1" x="236.6027" y="242.1971"/>
</g>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 282.234375 382.316406 C 286.863281 386.945312 286.863281 394.449219 282.234375 399.078125 C 277.605469 403.707031 270.101562 403.707031 265.472656 399.078125 C 260.84375 394.449219 260.84375 386.945312 265.472656 382.316406 C 270.101562 377.6875 277.605469 377.6875 282.234375 382.316406 " transform="matrix(1,0,0,1,-6,-153)"/>
<g style="fill:rgb(0%,0%,0%);fill-opacity:1;">
  <use xlink:href="#glyph0-1" x="264.1027" y="239.1971"/>
</g>
<g style="fill:rgb(0%,0%,0%);fill-opacity:1;">
  <use xlink:href="#glyph1-2" x="268.6027" y="242.1971"/>
</g>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 314.234375 382.316406 C 318.863281 386.945312 318.863281 394.449219 314.234375 399.078125 C 309.605469 403.707031 302.101562 403.707031 297.472656 399.078125 C 292.84375 394.449219 292.84375 386.945312 297.472656 382.316406 C 302.101562 377.6875 309.605469 377.6875 314.234375 382.316406 " transform="matrix(1,0,0,1,-6,-153)"/>
<g style="fill:rgb(0%,0%,0%);fill-opacity:1;">
  <use xlink:href="#glyph0-1" x="296.1027" y="239.1971"/>
</g>
<g style="fill:rgb(0%,0%,0%);fill-opacity:1;">
  <use xlink:href="#glyph1-3" x="300.6027" y="242.1971"/>
</g>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 346.234375 382.316406 C 350.863281 386.945312 350.863281 394.449219 346.234375 399.078125 C 341.605469 403.707031 334.101562 403.707031 329.472656 399.078125 C 324.84375 394.449219 324.84375 386.945312 329.472656 382.316406 C 334.101562 377.6875 341.605469 377.6875 346.234375 382.316406 " transform="matrix(1,0,0,1,-6,-153)"/>
<g style="fill:rgb(0%,0%,0%);fill-opacity:1;">
  <use xlink:href="#glyph0-1" x="328.1027" y="239.1971"/>
</g>
<g style="fill:rgb(0%,0%,0%);fill-opacity:1;">
  <use xlink:href="#glyph1-4" x="332.6027" y="242.1971"/>
</g>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 378.234375 382.316406 C 382.863281 386.945312 382.863281 394.449219 378.234375 399.078125 C 373.605469 403.707031 366.101562 403.707031 361.472656 399.078125 C 356.84375 394.449219 356.84375 386.945312 361.472656 382.316406 C 366.101562 377.6875 373.605469 377.6875 378.234375 382.316406 " transform="matrix(1,0,0,1,-6,-153)"/>
<g style="fill:rgb(0%,0%,0%);fill-opacity:1;">
  <use xlink:href="#glyph0-1" x="360.1027" y="239.1971"/>
</g>
<g style="fill:rgb(0%,0%,0%);fill-opacity:1;">
  <use xlink:href="#glyph1-5" x="364.6027" y="242.1971"/>
</g>
<path style="fill-rule:nonzero;fill:rgb(69.804382%,85.098267%,100%);fill-opacity:1;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 250.234375 321.359375 C 254.863281 325.988281 254.863281 333.492188 250.234375 338.121094 C 245.605469 342.75 238.101562 342.75 233.472656 338.121094 C 228.84375 333.492188 228.84375 325.988281 233.472656 321.359375 C 238.101562 316.730469 245.605469 316.730469 250.234375 321.359375 " transform="matrix(1,0,0,1,-6,-153)"/>
<path style="fill-rule:nonzero;fill:rgb(69.804382%,85.098267%,100%);fill-opacity:1;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 282.234375 321.359375 C 286.863281 325.988281 286.863281 333.492188 282.234375 338.121094 C 277.605469 342.75 270.101562 342.75 265.472656 338.121094 C 260.84375 333.492188 260.84375 325.988281 265.472656 321.359375 C 270.101562 316.730469 277.605469 316.730469 282.234375 321.359375 " transform="matrix(1,0,0,1,-6,-153)"/>
<path style="fill-rule:nonzero;fill:rgb(69.804382%,85.098267%,100%);fill-opacity:1;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 314.234375 321.359375 C 318.863281 325.988281 318.863281 333.492188 314.234375 338.121094 C 309.605469 342.75 302.101562 342.75 297.472656 338.121094 C 292.84375 333.492188 292.84375 325.988281 297.472656 321.359375 C 302.101562 316.730469 309.605469 316.730469 314.234375 321.359375 " transform="matrix(1,0,0,1,-6,-153)"/>
<path style="fill-rule:nonzero;fill:rgb(69.804382%,85.098267%,100%);fill-opacity:1;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 346.234375 321.359375 C 350.863281 325.988281 350.863281 333.492188 346.234375 338.121094 C 341.605469 342.75 334.101562 342.75 329.472656 338.121094 C 324.84375 333.492188 324.84375 325.988281 329.472656 321.359375 C 334.101562 316.730469 341.605469 316.730469 346.234375 321.359375 " transform="matrix(1,0,0,1,-6,-153)"/>
<path style="fill-rule:nonzero;fill:rgb(69.804382%,85.098267%,100%);fill-opacity:1;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 378.234375 321.359375 C 382.863281 325.988281 382.863281 333.492188 378.234375 338.121094 C 373.605469 342.75 366.101562 342.75 361.472656 338.121094 C 356.84375 333.492188 356.84375 325.988281 361.472656 321.359375 C 366.101562 316.730469 373.605469 316.730469 378.234375 321.359375 " transform="matrix(1,0,0,1,-6,-153)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 241.851562 378.84375 L 241.851562 347.496094 " transform="matrix(1,0,0,1,-6,-153)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:butt;stroke-linejoin:miter;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 241.851562 343.496094 L 241.851562 347.496094 M 240.351562 347.496094 L 241.851562 343.496094 L 243.351562 347.496094 " transform="matrix(1,0,0,1,-6,-153)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-dasharray:1,4;stroke-miterlimit:10;" d="M 273.851562 378.84375 L 273.851562 347.496094 " transform="matrix(1,0,0,1,-6,-153)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:butt;stroke-linejoin:miter;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 273.851562 343.496094 L 273.851562 347.496094 M 272.351562 347.496094 L 273.851562 343.496094 L 275.351562 347.496094 " transform="matrix(1,0,0,1,-6,-153)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-dasharray:1,4;stroke-miterlimit:10;" d="M 305.851562 378.84375 L 305.851562 347.496094 " transform="matrix(1,0,0,1,-6,-153)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:butt;stroke-linejoin:miter;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 305.851562 343.496094 L 305.851562 347.496094 M 304.351562 347.496094 L 305.851562 343.496094 L 307.351562 347.496094 " transform="matrix(1,0,0,1,-6,-153)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-dasharray:1,4;stroke-miterlimit:10;" d="M 337.851562 378.84375 L 337.851562 347.496094 " transform="matrix(1,0,0,1,-6,-153)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:butt;stroke-linejoin:miter;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 337.851562 343.496094 L 337.851562 347.496094 M 336.351562 347.496094 L 337.851562 343.496094 L 339.351562 347.496094 " transform="matrix(1,0,0,1,-6,-153)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-dasharray:1,4;stroke-miterlimit:10;" d="M 369.851562 378.84375 L 369.851562 347.496094 " transform="matrix(1,0,0,1,-6,-153)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:butt;stroke-linejoin:miter;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 369.851562 343.496094 L 369.851562 347.496094 M 368.351562 347.496094 L 369.851562 343.496094 L 371.351562 347.496094 " transform="matrix(1,0,0,1,-6,-153)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 250.234375 246.425781 C 254.863281 251.054688 254.863281 258.5625 250.234375 263.1875 C 245.605469 267.816406 238.101562 267.816406 233.472656 263.1875 C 228.84375 258.5625 228.84375 251.054688 233.472656 246.425781 C 238.101562 241.796875 245.605469 241.796875 250.234375 246.425781 " transform="matrix(1,0,0,1,-6,-153)"/>
<g style="fill:rgb(0%,0%,0%);fill-opacity:1;">
  <use xlink:href="#glyph0-1" x="232.1027" y="103.3083"/>
</g>
<g style="fill:rgb(0%,0%,0%);fill-opacity:1;">
  <use xlink:href="#glyph1-1" x="236.6027" y="106.3083"/>
</g>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 282.234375 246.425781 C 286.863281 251.054688 286.863281 258.5625 282.234375 263.1875 C 277.605469 267.816406 270.101562 267.816406 265.472656 263.1875 C 260.84375 258.5625 260.84375 251.054688 265.472656 246.425781 C 270.101562 241.796875 277.605469 241.796875 282.234375 246.425781 " transform="matrix(1,0,0,1,-6,-153)"/>
<g style="fill:rgb(0%,0%,0%);fill-opacity:1;">
  <use xlink:href="#glyph0-1" x="264.1027" y="103.3083"/>
</g>
<g style="fill:rgb(0%,0%,0%);fill-opacity:1;">
  <use xlink:href="#glyph1-2" x="268.6027" y="106.3083"/>
</g>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 314.234375 246.425781 C 318.863281 251.054688 318.863281 258.5625 314.234375 263.1875 C 309.605469 267.816406 302.101562 267.816406 297.472656 263.1875 C 292.84375 258.5625 292.84375 251.054688 297.472656 246.425781 C 302.101562 241.796875 309.605469 241.796875 314.234375 246.425781 " transform="matrix(1,0,0,1,-6,-153)"/>
<g style="fill:rgb(0%,0%,0%);fill-opacity:1;">
  <use xlink:href="#glyph0-1" x="296.1027" y="103.3083"/>
</g>
<g style="fill:rgb(0%,0%,0%);fill-opacity:1;">
  <use xlink:href="#glyph1-3" x="300.6027" y="106.3083"/>
</g>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 346.234375 246.425781 C 350.863281 251.054688 350.863281 258.5625 346.234375 263.1875 C 341.605469 267.816406 334.101562 267.816406 329.472656 263.1875 C 324.84375 258.5625 324.84375 251.054688 329.472656 246.425781 C 334.101562 241.796875 341.605469 241.796875 346.234375 246.425781 " transform="matrix(1,0,0,1,-6,-153)"/>
<g style="fill:rgb(0%,0%,0%);fill-opacity:1;">
  <use xlink:href="#glyph0-1" x="328.1027" y="103.3083"/>
</g>
<g style="fill:rgb(0%,0%,0%);fill-opacity:1;">
  <use xlink:href="#glyph1-4" x="332.6027" y="106.3083"/>
</g>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 378.234375 246.425781 C 382.863281 251.054688 382.863281 258.5625 378.234375 263.1875 C 373.605469 267.816406 366.101562 267.816406 361.472656 263.1875 C 356.84375 258.5625 356.84375 251.054688 361.472656 246.425781 C 366.101562 241.796875 373.605469 241.796875 378.234375 246.425781 " transform="matrix(1,0,0,1,-6,-153)"/>
<g style="fill:rgb(0%,0%,0%);fill-opacity:1;">
  <use xlink:href="#glyph0-1" x="360.1027" y="103.3083"/>
</g>
<g style="fill:rgb(0%,0%,0%);fill-opacity:1;">
  <use xlink:href="#glyph1-5" x="364.6027" y="106.3083"/>
</g>
<path style="fill-rule:nonzero;fill:rgb(69.804382%,85.098267%,100%);fill-opacity:1;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 250.234375 185.472656 C 254.863281 190.101562 254.863281 197.605469 250.234375 202.234375 C 245.605469 206.863281 238.101562 206.863281 233.472656 202.234375 C 228.84375 197.605469 228.84375 190.101562 233.472656 185.472656 C 238.101562 180.84375 245.605469 180.84375 250.234375 185.472656 " transform="matrix(1,0,0,1,-6,-153)"/>
<path style="fill-rule:nonzero;fill:rgb(69.804382%,85.098267%,100%);fill-opacity:1;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 282.234375 185.472656 C 286.863281 190.101562 286.863281 197.605469 282.234375 202.234375 C 277.605469 206.863281 270.101562 206.863281 265.472656 202.234375 C 260.84375 197.605469 260.84375 190.101562 265.472656 185.472656 C 270.101562 180.84375 277.605469 180.84375 282.234375 185.472656 " transform="matrix(1,0,0,1,-6,-153)"/>
<path style="fill-rule:nonzero;fill:rgb(69.804382%,85.098267%,100%);fill-opacity:1;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 314.234375 185.472656 C 318.863281 190.101562 318.863281 197.605469 314.234375 202.234375 C 309.605469 206.863281 302.101562 206.863281 297.472656 202.234375 C 292.84375 197.605469 292.84375 190.101562 297.472656 185.472656 C 302.101562 180.84375 309.605469 180.84375 314.234375 185.472656 " transform="matrix(1,0,0,1,-6,-153)"/>
<path style="fill-rule:nonzero;fill:rgb(69.804382%,85.098267%,100%);fill-opacity:1;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 346.234375 185.472656 C 350.863281 190.101562 350.863281 197.605469 346.234375 202.234375 C 341.605469 206.863281 334.101562 206.863281 329.472656 202.234375 C 324.84375 197.605469 324.84375 190.101562 329.472656 185.472656 C 334.101562 180.84375 341.605469 180.84375 346.234375 185.472656 " transform="matrix(1,0,0,1,-6,-153)"/>
<path style="fill-rule:nonzero;fill:rgb(69.804382%,85.098267%,100%);fill-opacity:1;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 378.234375 185.472656 C 382.863281 190.101562 382.863281 197.605469 378.234375 202.234375 C 373.605469 206.863281 366.101562 206.863281 361.472656 202.234375 C 356.84375 197.605469 356.84375 190.101562 361.472656 185.472656 C 366.101562 180.84375 373.605469 180.84375 378.234375 185.472656 " transform="matrix(1,0,0,1,-6,-153)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 253.707031 193.851562 L 256.101562 193.851562 " transform="matrix(1,0,0,1,-6,-153)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:butt;stroke-linejoin:miter;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 260.101562 193.851562 L 256.101562 193.851562 M 256.101562 192.351562 L 260.101562 193.851562 L 256.101562 195.351562 " transform="matrix(1,0,0,1,-6,-153)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 285.707031 193.851562 L 288.101562 193.851562 " transform="matrix(1,0,0,1,-6,-153)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:butt;stroke-linejoin:miter;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 292.101562 193.851562 L 288.101562 193.851562 M 288.101562 192.351562 L 292.101562 193.851562 L 288.101562 195.351562 " transform="matrix(1,0,0,1,-6,-153)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 317.707031 193.851562 L 320.101562 193.851562 " transform="matrix(1,0,0,1,-6,-153)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:butt;stroke-linejoin:miter;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 324.101562 193.851562 L 320.101562 193.851562 M 320.101562 192.351562 L 324.101562 193.851562 L 320.101562 195.351562 " transform="matrix(1,0,0,1,-6,-153)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 349.707031 193.851562 L 352.101562 193.851562 " transform="matrix(1,0,0,1,-6,-153)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:butt;stroke-linejoin:miter;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 356.101562 193.851562 L 352.101562 193.851562 M 352.101562 192.351562 L 356.101562 193.851562 L 352.101562 195.351562 " transform="matrix(1,0,0,1,-6,-153)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 241.851562 242.957031 L 241.851562 211.605469 " transform="matrix(1,0,0,1,-6,-153)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:butt;stroke-linejoin:miter;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 241.851562 207.605469 L 241.851562 211.605469 M 240.351562 211.605469 L 241.851562 207.605469 L 243.351562 211.605469 " transform="matrix(1,0,0,1,-6,-153)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-dasharray:1,4;stroke-miterlimit:10;" d="M 273.851562 242.957031 L 273.851562 211.605469 " transform="matrix(1,0,0,1,-6,-153)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:butt;stroke-linejoin:miter;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 273.851562 207.605469 L 273.851562 211.605469 M 272.351562 211.605469 L 273.851562 207.605469 L 275.351562 211.605469 " transform="matrix(1,0,0,1,-6,-153)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-dasharray:1,4;stroke-miterlimit:10;" d="M 305.851562 242.957031 L 305.851562 211.605469 " transform="matrix(1,0,0,1,-6,-153)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:butt;stroke-linejoin:miter;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 305.851562 207.605469 L 305.851562 211.605469 M 304.351562 211.605469 L 305.851562 207.605469 L 307.351562 211.605469 " transform="matrix(1,0,0,1,-6,-153)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-dasharray:1,4;stroke-miterlimit:10;" d="M 337.851562 242.957031 L 337.851562 211.605469 " transform="matrix(1,0,0,1,-6,-153)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:butt;stroke-linejoin:miter;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 337.851562 207.605469 L 337.851562 211.605469 M 336.351562 211.605469 L 337.851562 207.605469 L 339.351562 211.605469 " transform="matrix(1,0,0,1,-6,-153)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 369.851562 242.957031 L 369.851562 211.605469 " transform="matrix(1,0,0,1,-6,-153)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:butt;stroke-linejoin:miter;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 369.851562 207.605469 L 369.851562 211.605469 M 368.351562 211.605469 L 369.851562 207.605469 L 371.351562 211.605469 " transform="matrix(1,0,0,1,-6,-153)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 359.148438 385.597656 L 257.882812 337.375 " transform="matrix(1,0,0,1,-6,-153)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:butt;stroke-linejoin:miter;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 254.273438 335.65625 L 257.882812 337.375 M 257.238281 338.730469 L 254.273438 335.65625 L 258.527344 336.023438 " transform="matrix(1,0,0,1,-6,-153)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-dasharray:1,4;stroke-miterlimit:10;" d="M 359.84375 384.34375 L 288.839844 339.257812 " transform="matrix(1,0,0,1,-6,-153)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:butt;stroke-linejoin:miter;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 285.464844 337.113281 L 288.839844 339.257812 M 288.039062 340.523438 L 285.464844 337.113281 L 289.644531 337.992188 " transform="matrix(1,0,0,1,-6,-153)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-dasharray:1,4;stroke-miterlimit:10;" d="M 361.269531 382.523438 L 318.707031 341.984375 " transform="matrix(1,0,0,1,-6,-153)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:butt;stroke-linejoin:miter;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 315.8125 339.226562 L 318.707031 341.984375 M 317.671875 343.070312 L 315.8125 339.226562 L 319.742188 340.898438 " transform="matrix(1,0,0,1,-6,-153)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-dasharray:1,4;stroke-miterlimit:10;" d="M 364.34375 380.199219 L 346.105469 345.460938 " transform="matrix(1,0,0,1,-6,-153)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:butt;stroke-linejoin:miter;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 344.246094 341.921875 L 346.105469 345.460938 M 344.777344 346.160156 L 344.246094 341.921875 L 347.433594 344.765625 " transform="matrix(1,0,0,1,-6,-153)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-dasharray:1,4;stroke-miterlimit:10;" d="M 247.363281 380.199219 L 265.597656 345.460938 " transform="matrix(1,0,0,1,-6,-153)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:butt;stroke-linejoin:miter;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 267.457031 341.921875 L 265.597656 345.460938 M 264.269531 344.765625 L 267.457031 341.921875 L 266.925781 346.160156 " transform="matrix(1,0,0,1,-6,-153)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-dasharray:1,4;stroke-miterlimit:10;" d="M 250.4375 382.523438 L 292.996094 341.984375 " transform="matrix(1,0,0,1,-6,-153)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:butt;stroke-linejoin:miter;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 295.894531 339.226562 L 292.996094 341.984375 M 291.964844 340.898438 L 295.894531 339.226562 L 294.03125 343.070312 " transform="matrix(1,0,0,1,-6,-153)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-dasharray:1,4;stroke-miterlimit:10;" d="M 251.859375 384.34375 L 322.863281 339.257812 " transform="matrix(1,0,0,1,-6,-153)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:butt;stroke-linejoin:miter;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 326.242188 337.113281 L 322.863281 339.257812 M 322.058594 337.992188 L 326.242188 337.113281 L 323.667969 340.523438 " transform="matrix(1,0,0,1,-6,-153)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-dasharray:1,4;stroke-miterlimit:10;" d="M 252.558594 385.597656 L 353.820312 337.375 " transform="matrix(1,0,0,1,-6,-153)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:butt;stroke-linejoin:miter;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 357.433594 335.65625 L 353.820312 337.375 M 353.175781 336.023438 L 357.433594 335.65625 L 354.464844 338.730469 " transform="matrix(1,0,0,1,-6,-153)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-dasharray:1,4;stroke-miterlimit:10;" d="M 268.34375 380.199219 L 250.105469 345.460938 " transform="matrix(1,0,0,1,-6,-153)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:butt;stroke-linejoin:miter;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 248.246094 341.921875 L 250.105469 345.460938 M 248.777344 346.160156 L 248.246094 341.921875 L 251.433594 344.765625 " transform="matrix(1,0,0,1,-6,-153)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-dasharray:1,4;stroke-miterlimit:10;" d="M 279.363281 380.199219 L 297.597656 345.460938 " transform="matrix(1,0,0,1,-6,-153)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:butt;stroke-linejoin:miter;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 299.457031 341.921875 L 297.597656 345.460938 M 296.269531 344.765625 L 299.457031 341.921875 L 298.925781 346.160156 " transform="matrix(1,0,0,1,-6,-153)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-dasharray:1,4;stroke-miterlimit:10;" d="M 282.4375 382.523438 L 324.996094 341.984375 " transform="matrix(1,0,0,1,-6,-153)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:butt;stroke-linejoin:miter;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 327.894531 339.226562 L 324.996094 341.984375 M 323.964844 340.898438 L 327.894531 339.226562 L 326.03125 343.070312 " transform="matrix(1,0,0,1,-6,-153)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-dasharray:1,4;stroke-miterlimit:10;" d="M 283.859375 384.34375 L 354.863281 339.257812 " transform="matrix(1,0,0,1,-6,-153)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:butt;stroke-linejoin:miter;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 358.242188 337.113281 L 354.863281 339.257812 M 354.058594 337.992188 L 358.242188 337.113281 L 355.667969 340.523438 " transform="matrix(1,0,0,1,-6,-153)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-dasharray:1,4;stroke-miterlimit:10;" d="M 297.269531 382.523438 L 254.707031 341.984375 " transform="matrix(1,0,0,1,-6,-153)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:butt;stroke-linejoin:miter;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 251.8125 339.226562 L 254.707031 341.984375 M 253.671875 343.070312 L 251.8125 339.226562 L 255.742188 340.898438 " transform="matrix(1,0,0,1,-6,-153)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-dasharray:1,4;stroke-miterlimit:10;" d="M 300.34375 380.199219 L 282.105469 345.460938 " transform="matrix(1,0,0,1,-6,-153)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:butt;stroke-linejoin:miter;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 280.246094 341.921875 L 282.105469 345.460938 M 280.777344 346.160156 L 280.246094 341.921875 L 283.433594 344.765625 " transform="matrix(1,0,0,1,-6,-153)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-dasharray:1,4;stroke-miterlimit:10;" d="M 311.363281 380.199219 L 329.597656 345.460938 " transform="matrix(1,0,0,1,-6,-153)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:butt;stroke-linejoin:miter;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 331.457031 341.921875 L 329.597656 345.460938 M 328.269531 344.765625 L 331.457031 341.921875 L 330.925781 346.160156 " transform="matrix(1,0,0,1,-6,-153)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-dasharray:1,4;stroke-miterlimit:10;" d="M 314.4375 382.523438 L 356.996094 341.984375 " transform="matrix(1,0,0,1,-6,-153)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:butt;stroke-linejoin:miter;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 359.894531 339.226562 L 356.996094 341.984375 M 355.964844 340.898438 L 359.894531 339.226562 L 358.03125 343.070312 " transform="matrix(1,0,0,1,-6,-153)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-dasharray:1,4;stroke-miterlimit:10;" d="M 327.84375 384.34375 L 256.839844 339.257812 " transform="matrix(1,0,0,1,-6,-153)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:butt;stroke-linejoin:miter;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 253.464844 337.113281 L 256.839844 339.257812 M 256.039062 340.523438 L 253.464844 337.113281 L 257.644531 337.992188 " transform="matrix(1,0,0,1,-6,-153)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-dasharray:1,4;stroke-miterlimit:10;" d="M 329.269531 382.523438 L 286.707031 341.984375 " transform="matrix(1,0,0,1,-6,-153)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:butt;stroke-linejoin:miter;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 283.8125 339.226562 L 286.707031 341.984375 M 285.671875 343.070312 L 283.8125 339.226562 L 287.742188 340.898438 " transform="matrix(1,0,0,1,-6,-153)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-dasharray:1,4;stroke-miterlimit:10;" d="M 332.34375 380.199219 L 314.105469 345.460938 " transform="matrix(1,0,0,1,-6,-153)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:butt;stroke-linejoin:miter;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 312.246094 341.921875 L 314.105469 345.460938 M 312.777344 346.160156 L 312.246094 341.921875 L 315.433594 344.765625 " transform="matrix(1,0,0,1,-6,-153)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-dasharray:1,4;stroke-miterlimit:10;" d="M 343.363281 380.199219 L 361.597656 345.460938 " transform="matrix(1,0,0,1,-6,-153)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:butt;stroke-linejoin:miter;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 363.457031 341.921875 L 361.597656 345.460938 M 360.269531 344.765625 L 363.457031 341.921875 L 362.925781 346.160156 " transform="matrix(1,0,0,1,-6,-153)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 42.234375 347.382812 C 46.863281 352.011719 46.863281 359.515625 42.234375 364.144531 C 37.605469 368.773438 30.101562 368.773438 25.472656 364.144531 C 20.84375 359.515625 20.84375 352.011719 25.472656 347.382812 C 30.101562 342.753906 37.605469 342.753906 42.234375 347.382812 " transform="matrix(1,0,0,1,-6,-153)"/>
<g style="fill:rgb(0%,0%,0%);fill-opacity:1;">
  <use xlink:href="#glyph0-1" x="24.10274" y="204.2638"/>
</g>
<g style="fill:rgb(0%,0%,0%);fill-opacity:1;">
  <use xlink:href="#glyph1-1" x="28.60274" y="207.2638"/>
</g>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 74.234375 347.382812 C 78.863281 352.011719 78.863281 359.515625 74.234375 364.144531 C 69.605469 368.773438 62.101562 368.773438 57.472656 364.144531 C 52.84375 359.515625 52.84375 352.011719 57.472656 347.382812 C 62.101562 342.753906 69.605469 342.753906 74.234375 347.382812 " transform="matrix(1,0,0,1,-6,-153)"/>
<g style="fill:rgb(0%,0%,0%);fill-opacity:1;">
  <use xlink:href="#glyph0-1" x="56.10274" y="204.2638"/>
</g>
<g style="fill:rgb(0%,0%,0%);fill-opacity:1;">
  <use xlink:href="#glyph1-2" x="60.60274" y="207.2638"/>
</g>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 106.234375 347.382812 C 110.863281 352.011719 110.863281 359.515625 106.234375 364.144531 C 101.605469 368.773438 94.101562 368.773438 89.472656 364.144531 C 84.84375 359.515625 84.84375 352.011719 89.472656 347.382812 C 94.101562 342.753906 101.605469 342.753906 106.234375 347.382812 " transform="matrix(1,0,0,1,-6,-153)"/>
<g style="fill:rgb(0%,0%,0%);fill-opacity:1;">
  <use xlink:href="#glyph0-1" x="88.10274" y="204.2638"/>
</g>
<g style="fill:rgb(0%,0%,0%);fill-opacity:1;">
  <use xlink:href="#glyph1-3" x="92.60274" y="207.2638"/>
</g>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 138.234375 347.382812 C 142.863281 352.011719 142.863281 359.515625 138.234375 364.144531 C 133.605469 368.773438 126.101562 368.773438 121.472656 364.144531 C 116.84375 359.515625 116.84375 352.011719 121.472656 347.382812 C 126.101562 342.753906 133.605469 342.753906 138.234375 347.382812 " transform="matrix(1,0,0,1,-6,-153)"/>
<g style="fill:rgb(0%,0%,0%);fill-opacity:1;">
  <use xlink:href="#glyph0-1" x="120.1027" y="204.2638"/>
</g>
<g style="fill:rgb(0%,0%,0%);fill-opacity:1;">
  <use xlink:href="#glyph1-4" x="124.6027" y="207.2638"/>
</g>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 170.234375 347.382812 C 174.863281 352.011719 174.863281 359.515625 170.234375 364.144531 C 165.605469 368.773438 158.101562 368.773438 153.472656 364.144531 C 148.84375 359.515625 148.84375 352.011719 153.472656 347.382812 C 158.101562 342.753906 165.605469 342.753906 170.234375 347.382812 " transform="matrix(1,0,0,1,-6,-153)"/>
<g style="fill:rgb(0%,0%,0%);fill-opacity:1;">
  <use xlink:href="#glyph0-1" x="152.1027" y="204.2638"/>
</g>
<g style="fill:rgb(0%,0%,0%);fill-opacity:1;">
  <use xlink:href="#glyph1-5" x="156.6027" y="207.2638"/>
</g>
<path style="fill-rule:nonzero;fill:rgb(69.804382%,85.098267%,100%);fill-opacity:1;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 42.234375 286.425781 C 46.863281 291.054688 46.863281 298.5625 42.234375 303.1875 C 37.605469 307.816406 30.101562 307.816406 25.472656 303.1875 C 20.84375 298.5625 20.84375 291.054688 25.472656 286.425781 C 30.101562 281.796875 37.605469 281.796875 42.234375 286.425781 " transform="matrix(1,0,0,1,-6,-153)"/>
<path style="fill-rule:nonzero;fill:rgb(69.804382%,85.098267%,100%);fill-opacity:1;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 74.234375 286.425781 C 78.863281 291.054688 78.863281 298.5625 74.234375 303.1875 C 69.605469 307.816406 62.101562 307.816406 57.472656 303.1875 C 52.84375 298.5625 52.84375 291.054688 57.472656 286.425781 C 62.101562 281.796875 69.605469 281.796875 74.234375 286.425781 " transform="matrix(1,0,0,1,-6,-153)"/>
<path style="fill-rule:nonzero;fill:rgb(69.804382%,85.098267%,100%);fill-opacity:1;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 106.234375 286.425781 C 110.863281 291.054688 110.863281 298.5625 106.234375 303.1875 C 101.605469 307.816406 94.101562 307.816406 89.472656 303.1875 C 84.84375 298.5625 84.84375 291.054688 89.472656 286.425781 C 94.101562 281.796875 101.605469 281.796875 106.234375 286.425781 " transform="matrix(1,0,0,1,-6,-153)"/>
<path style="fill-rule:nonzero;fill:rgb(69.804382%,85.098267%,100%);fill-opacity:1;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 138.234375 286.425781 C 142.863281 291.054688 142.863281 298.5625 138.234375 303.1875 C 133.605469 307.816406 126.101562 307.816406 121.472656 303.1875 C 116.84375 298.5625 116.84375 291.054688 121.472656 286.425781 C 126.101562 281.796875 133.605469 281.796875 138.234375 286.425781 " transform="matrix(1,0,0,1,-6,-153)"/>
<path style="fill-rule:nonzero;fill:rgb(69.804382%,85.098267%,100%);fill-opacity:1;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 170.234375 286.425781 C 174.863281 291.054688 174.863281 298.5625 170.234375 303.1875 C 165.605469 307.816406 158.101562 307.816406 153.472656 303.1875 C 148.84375 298.5625 148.84375 291.054688 153.472656 286.425781 C 158.101562 281.796875 165.605469 281.796875 170.234375 286.425781 " transform="matrix(1,0,0,1,-6,-153)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-dasharray:1,4;stroke-miterlimit:10;" d="M 33.851562 343.910156 L 33.851562 312.5625 " transform="matrix(1,0,0,1,-6,-153)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:butt;stroke-linejoin:miter;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 33.851562 308.5625 L 33.851562 312.5625 M 32.351562 312.5625 L 33.851562 308.5625 L 35.351562 312.5625 " transform="matrix(1,0,0,1,-6,-153)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-dasharray:1,4;stroke-miterlimit:10;" d="M 65.851562 343.910156 L 65.851562 312.5625 " transform="matrix(1,0,0,1,-6,-153)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:butt;stroke-linejoin:miter;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 65.851562 308.5625 L 65.851562 312.5625 M 64.351562 312.5625 L 65.851562 308.5625 L 67.351562 312.5625 " transform="matrix(1,0,0,1,-6,-153)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-dasharray:1,4;stroke-miterlimit:10;" d="M 97.851562 343.910156 L 97.851562 312.5625 " transform="matrix(1,0,0,1,-6,-153)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:butt;stroke-linejoin:miter;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 97.851562 308.5625 L 97.851562 312.5625 M 96.351562 312.5625 L 97.851562 308.5625 L 99.351562 312.5625 " transform="matrix(1,0,0,1,-6,-153)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-dasharray:1,4;stroke-miterlimit:10;" d="M 129.851562 343.910156 L 129.851562 312.5625 " transform="matrix(1,0,0,1,-6,-153)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:butt;stroke-linejoin:miter;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 129.851562 308.5625 L 129.851562 312.5625 M 128.351562 312.5625 L 129.851562 308.5625 L 131.351562 312.5625 " transform="matrix(1,0,0,1,-6,-153)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-dasharray:1,4;stroke-miterlimit:10;" d="M 161.851562 343.910156 L 161.851562 312.5625 " transform="matrix(1,0,0,1,-6,-153)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:butt;stroke-linejoin:miter;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 161.851562 308.5625 L 161.851562 312.5625 M 160.351562 312.5625 L 161.851562 308.5625 L 163.351562 312.5625 " transform="matrix(1,0,0,1,-6,-153)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 156.34375 345.265625 L 138.105469 310.53125 " transform="matrix(1,0,0,1,-6,-153)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:butt;stroke-linejoin:miter;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 136.246094 306.988281 L 138.105469 310.53125 M 136.777344 311.226562 L 136.246094 306.988281 L 139.433594 309.832031 " transform="matrix(1,0,0,1,-6,-153)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 39.363281 345.265625 L 57.597656 310.53125 " transform="matrix(1,0,0,1,-6,-153)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:butt;stroke-linejoin:miter;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 59.457031 306.988281 L 57.597656 310.53125 M 56.269531 309.832031 L 59.457031 306.988281 L 58.925781 311.226562 " transform="matrix(1,0,0,1,-6,-153)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-dasharray:1,4;stroke-miterlimit:10;" d="M 60.34375 345.265625 L 42.105469 310.53125 " transform="matrix(1,0,0,1,-6,-153)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:butt;stroke-linejoin:miter;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 40.246094 306.988281 L 42.105469 310.53125 M 40.777344 311.226562 L 40.246094 306.988281 L 43.433594 309.832031 " transform="matrix(1,0,0,1,-6,-153)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-dasharray:1,4;stroke-miterlimit:10;" d="M 71.363281 345.265625 L 89.597656 310.53125 " transform="matrix(1,0,0,1,-6,-153)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:butt;stroke-linejoin:miter;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 91.457031 306.988281 L 89.597656 310.53125 M 88.269531 309.832031 L 91.457031 306.988281 L 90.925781 311.226562 " transform="matrix(1,0,0,1,-6,-153)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-dasharray:1,4;stroke-miterlimit:10;" d="M 92.34375 345.265625 L 74.105469 310.53125 " transform="matrix(1,0,0,1,-6,-153)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:butt;stroke-linejoin:miter;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 72.246094 306.988281 L 74.105469 310.53125 M 72.777344 311.226562 L 72.246094 306.988281 L 75.433594 309.832031 " transform="matrix(1,0,0,1,-6,-153)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-dasharray:1,4;stroke-miterlimit:10;" d="M 103.363281 345.265625 L 121.597656 310.53125 " transform="matrix(1,0,0,1,-6,-153)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:butt;stroke-linejoin:miter;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 123.457031 306.988281 L 121.597656 310.53125 M 120.269531 309.832031 L 123.457031 306.988281 L 122.925781 311.226562 " transform="matrix(1,0,0,1,-6,-153)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-dasharray:1,4;stroke-miterlimit:10;" d="M 124.34375 345.265625 L 106.105469 310.53125 " transform="matrix(1,0,0,1,-6,-153)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:butt;stroke-linejoin:miter;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 104.246094 306.988281 L 106.105469 310.53125 M 104.777344 311.226562 L 104.246094 306.988281 L 107.433594 309.832031 " transform="matrix(1,0,0,1,-6,-153)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-dasharray:1,4;stroke-miterlimit:10;" d="M 135.363281 345.265625 L 153.597656 310.53125 " transform="matrix(1,0,0,1,-6,-153)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:butt;stroke-linejoin:miter;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 155.457031 306.988281 L 153.597656 310.53125 M 152.269531 309.832031 L 155.457031 306.988281 L 154.925781 311.226562 " transform="matrix(1,0,0,1,-6,-153)"/>
<path style="fill-rule:nonzero;fill:rgb(39.99939%,74.902344%,100%);fill-opacity:1;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 42.234375 225.472656 C 46.863281 230.101562 46.863281 237.605469 42.234375 242.234375 C 37.605469 246.863281 30.101562 246.863281 25.472656 242.234375 C 20.84375 237.605469 20.84375 230.101562 25.472656 225.472656 C 30.101562 220.84375 37.605469 220.84375 42.234375 225.472656 " transform="matrix(1,0,0,1,-6,-153)"/>
<path style="fill-rule:nonzero;fill:rgb(39.99939%,74.902344%,100%);fill-opacity:1;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 74.234375 225.472656 C 78.863281 230.101562 78.863281 237.605469 74.234375 242.234375 C 69.605469 246.863281 62.101562 246.863281 57.472656 242.234375 C 52.84375 237.605469 52.84375 230.101562 57.472656 225.472656 C 62.101562 220.84375 69.605469 220.84375 74.234375 225.472656 " transform="matrix(1,0,0,1,-6,-153)"/>
<path style="fill-rule:nonzero;fill:rgb(39.99939%,74.902344%,100%);fill-opacity:1;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 106.234375 225.472656 C 110.863281 230.101562 110.863281 237.605469 106.234375 242.234375 C 101.605469 246.863281 94.101562 246.863281 89.472656 242.234375 C 84.84375 237.605469 84.84375 230.101562 89.472656 225.472656 C 94.101562 220.84375 101.605469 220.84375 106.234375 225.472656 " transform="matrix(1,0,0,1,-6,-153)"/>
<path style="fill-rule:nonzero;fill:rgb(39.99939%,74.902344%,100%);fill-opacity:1;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 138.234375 225.472656 C 142.863281 230.101562 142.863281 237.605469 138.234375 242.234375 C 133.605469 246.863281 126.101562 246.863281 121.472656 242.234375 C 116.84375 237.605469 116.84375 230.101562 121.472656 225.472656 C 126.101562 220.84375 133.605469 220.84375 138.234375 225.472656 " transform="matrix(1,0,0,1,-6,-153)"/>
<path style="fill-rule:nonzero;fill:rgb(39.99939%,74.902344%,100%);fill-opacity:1;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 170.234375 225.472656 C 174.863281 230.101562 174.863281 237.605469 170.234375 242.234375 C 165.605469 246.863281 158.101562 246.863281 153.472656 242.234375 C 148.84375 237.605469 148.84375 230.101562 153.472656 225.472656 C 158.101562 220.84375 165.605469 220.84375 170.234375 225.472656 " transform="matrix(1,0,0,1,-6,-153)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-dasharray:1,4;stroke-miterlimit:10;" d="M 39.363281 284.3125 L 57.597656 249.574219 " transform="matrix(1,0,0,1,-6,-153)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:butt;stroke-linejoin:miter;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 59.457031 246.03125 L 57.597656 249.574219 M 56.269531 248.875 L 59.457031 246.03125 L 58.925781 250.269531 " transform="matrix(1,0,0,1,-6,-153)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-dasharray:1,4;stroke-miterlimit:10;" d="M 65.851562 282.957031 L 65.851562 251.605469 " transform="matrix(1,0,0,1,-6,-153)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:butt;stroke-linejoin:miter;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 65.851562 247.605469 L 65.851562 251.605469 M 64.351562 251.605469 L 65.851562 247.605469 L 67.351562 251.605469 " transform="matrix(1,0,0,1,-6,-153)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-dasharray:1,4;stroke-miterlimit:10;" d="M 92.34375 284.3125 L 74.105469 249.574219 " transform="matrix(1,0,0,1,-6,-153)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:butt;stroke-linejoin:miter;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 72.246094 246.03125 L 74.105469 249.574219 M 72.777344 250.269531 L 72.246094 246.03125 L 75.433594 248.875 " transform="matrix(1,0,0,1,-6,-153)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 71.363281 284.3125 L 89.597656 249.574219 " transform="matrix(1,0,0,1,-6,-153)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:butt;stroke-linejoin:miter;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 91.457031 246.03125 L 89.597656 249.574219 M 88.269531 248.875 L 91.457031 246.03125 L 90.925781 250.269531 " transform="matrix(1,0,0,1,-6,-153)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-dasharray:1,4;stroke-miterlimit:10;" d="M 97.851562 282.957031 L 97.851562 251.605469 " transform="matrix(1,0,0,1,-6,-153)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:butt;stroke-linejoin:miter;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 97.851562 247.605469 L 97.851562 251.605469 M 96.351562 251.605469 L 97.851562 247.605469 L 99.351562 251.605469 " transform="matrix(1,0,0,1,-6,-153)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 124.34375 284.3125 L 106.105469 249.574219 " transform="matrix(1,0,0,1,-6,-153)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:butt;stroke-linejoin:miter;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 104.246094 246.03125 L 106.105469 249.574219 M 104.777344 250.269531 L 104.246094 246.03125 L 107.433594 248.875 " transform="matrix(1,0,0,1,-6,-153)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-dasharray:1,4;stroke-miterlimit:10;" d="M 103.363281 284.3125 L 121.597656 249.574219 " transform="matrix(1,0,0,1,-6,-153)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:butt;stroke-linejoin:miter;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 123.457031 246.03125 L 121.597656 249.574219 M 120.269531 248.875 L 123.457031 246.03125 L 122.925781 250.269531 " transform="matrix(1,0,0,1,-6,-153)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-dasharray:1,4;stroke-miterlimit:10;" d="M 129.851562 282.957031 L 129.851562 251.605469 " transform="matrix(1,0,0,1,-6,-153)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:butt;stroke-linejoin:miter;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 129.851562 247.605469 L 129.851562 251.605469 M 128.351562 251.605469 L 129.851562 247.605469 L 131.351562 251.605469 " transform="matrix(1,0,0,1,-6,-153)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-dasharray:1,4;stroke-miterlimit:10;" d="M 156.34375 284.3125 L 138.105469 249.574219 " transform="matrix(1,0,0,1,-6,-153)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:butt;stroke-linejoin:miter;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 136.246094 246.03125 L 138.105469 249.574219 M 136.777344 250.269531 L 136.246094 246.03125 L 139.433594 248.875 " transform="matrix(1,0,0,1,-6,-153)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-dasharray:1,4;stroke-miterlimit:10;" d="M 33.851562 282.957031 L 33.851562 251.605469 " transform="matrix(1,0,0,1,-6,-153)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:butt;stroke-linejoin:miter;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 33.851562 247.605469 L 33.851562 251.605469 M 32.351562 251.605469 L 33.851562 247.605469 L 35.351562 251.605469 " transform="matrix(1,0,0,1,-6,-153)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-dasharray:1,4;stroke-miterlimit:10;" d="M 60.34375 284.3125 L 42.105469 249.574219 " transform="matrix(1,0,0,1,-6,-153)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:butt;stroke-linejoin:miter;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 40.246094 246.03125 L 42.105469 249.574219 M 40.777344 250.269531 L 40.246094 246.03125 L 43.433594 248.875 " transform="matrix(1,0,0,1,-6,-153)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-dasharray:1,4;stroke-miterlimit:10;" d="M 135.363281 284.3125 L 153.597656 249.574219 " transform="matrix(1,0,0,1,-6,-153)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:butt;stroke-linejoin:miter;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 155.457031 246.03125 L 153.597656 249.574219 M 152.269531 248.875 L 155.457031 246.03125 L 154.925781 250.269531 " transform="matrix(1,0,0,1,-6,-153)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-dasharray:1,4;stroke-miterlimit:10;" d="M 161.851562 282.957031 L 161.851562 251.605469 " transform="matrix(1,0,0,1,-6,-153)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:butt;stroke-linejoin:miter;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 161.851562 247.605469 L 161.851562 251.605469 M 160.351562 251.605469 L 161.851562 247.605469 L 163.351562 251.605469 " transform="matrix(1,0,0,1,-6,-153)"/>
<g style="fill:rgb(0%,0%,0%);fill-opacity:1;">
  <use xlink:href="#glyph2-1" x="272.8527" y="15.5"/>
  <use xlink:href="#glyph2-2" x="281.8527" y="15.5"/>
  <use xlink:href="#glyph2-3" x="290.8527" y="15.5"/>
  <use xlink:href="#glyph2-4" x="299.8527" y="15.5"/>
  <use xlink:href="#glyph2-5" x="308.8527" y="15.5"/>
  <use xlink:href="#glyph2-6" x="317.8527" y="15.5"/>
</g>
<g style="fill:rgb(0%,0%,0%);fill-opacity:1;">
  <use xlink:href="#glyph2-7" x="64.85274" y="55.5"/>
  <use xlink:href="#glyph2-8" x="73.85274" y="55.5"/>
  <use xlink:href="#glyph2-3" x="82.85274" y="55.5"/>
  <use xlink:href="#glyph2-4" x="91.85274" y="55.5"/>
  <use xlink:href="#glyph2-5" x="100.85274" y="55.5"/>
  <use xlink:href="#glyph2-6" x="109.85274" y="55.5"/>
</g>
<g style="fill:rgb(0%,0%,0%);fill-opacity:1;">
  <use xlink:href="#glyph2-9" x="281.8527" y="151.3888"/>
  <use xlink:href="#glyph2-10" x="290.8527" y="151.3888"/>
  <use xlink:href="#glyph2-11" x="299.8527" y="151.3888"/>
  <use xlink:href="#glyph2-12" x="308.8527" y="151.3888"/>
</g>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-dasharray:1,4;stroke-miterlimit:10;" d="M 7.5 282.957031 L 25.457031 249.496094 " transform="matrix(1,0,0,1,-6,-153)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:butt;stroke-linejoin:miter;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 27.347656 245.972656 L 25.457031 249.496094 M 24.132812 248.789062 L 27.347656 245.972656 L 26.777344 250.207031 " transform="matrix(1,0,0,1,-6,-153)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-dasharray:1,4;stroke-miterlimit:10;" d="M 189.214844 284.902344 L 170.242188 249.503906 " transform="matrix(1,0,0,1,-6,-153)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:butt;stroke-linejoin:miter;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 168.351562 245.976562 L 170.242188 249.503906 M 168.917969 250.210938 L 168.351562 245.976562 L 171.5625 248.792969 " transform="matrix(1,0,0,1,-6,-153)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-dasharray:1,4;stroke-miterlimit:10;" d="M 6.5 343.910156 L 25.210938 310.320312 " transform="matrix(1,0,0,1,-6,-153)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:butt;stroke-linejoin:miter;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 27.160156 306.824219 L 25.210938 310.320312 M 23.902344 309.589844 L 27.160156 306.824219 L 26.523438 311.050781 " transform="matrix(1,0,0,1,-6,-153)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-dasharray:1,4;stroke-miterlimit:10;" d="M 189 343.910156 L 170.445312 310.347656 " transform="matrix(1,0,0,1,-6,-153)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:butt;stroke-linejoin:miter;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 168.507812 306.847656 L 170.445312 310.347656 M 169.132812 311.074219 L 168.507812 306.847656 L 171.757812 309.621094 " transform="matrix(1,0,0,1,-6,-153)"/>
</g>
</svg>
