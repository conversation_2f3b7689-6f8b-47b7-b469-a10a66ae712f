{"cells": [{"cell_type": "markdown", "id": "a7443ef5", "metadata": {"origin_pos": 0}, "source": ["# 图像分类数据集\n", ":label:`sec_fashion_mnist`\n", "\n", "(**MNIST数据集**) :cite:`LeCun.Bottou.Bengio.ea.1998`\n", "(**是图像分类中广泛使用的数据集之一，但作为基准数据集过于简单。\n", "我们将使用类似但更复杂的Fashion-MNIST数据集**) :cite:`<PERSON><PERSON>Rasul.Vollgraf.2017`。\n"]}, {"cell_type": "code", "execution_count": 1, "id": "726c5142", "metadata": {"execution": {"iopub.execute_input": "2022-12-07T16:28:05.346942Z", "iopub.status.busy": "2022-12-07T16:28:05.346332Z", "iopub.status.idle": "2022-12-07T16:28:07.533165Z", "shell.execute_reply": "2022-12-07T16:28:07.532343Z"}, "origin_pos": 2, "tab": ["pytorch"]}, "outputs": [], "source": ["%matplotlib inline\n", "import torch\n", "import torchvision\n", "from torch.utils import data\n", "from torchvision import transforms\n", "from d2l import torch as d2l\n", "\n", "d2l.use_svg_display()"]}, {"cell_type": "markdown", "id": "30e592f6", "metadata": {"origin_pos": 5}, "source": ["## 读取数据集\n", "\n", "我们可以[**通过框架中的内置函数将Fashion-MNIST数据集下载并读取到内存中**]。\n"]}, {"cell_type": "code", "execution_count": 2, "id": "8c8219f9", "metadata": {"execution": {"iopub.execute_input": "2022-12-07T16:28:07.537349Z", "iopub.status.busy": "2022-12-07T16:28:07.536791Z", "iopub.status.idle": "2022-12-07T16:28:15.079938Z", "shell.execute_reply": "2022-12-07T16:28:15.079111Z"}, "origin_pos": 7, "tab": ["pytorch"]}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Downloading http://fashion-mnist.s3-website.eu-central-1.amazonaws.com/train-images-idx3-ubyte.gz\n", "Downloading http://fashion-mnist.s3-website.eu-central-1.amazonaws.com/train-images-idx3-ubyte.gz to ../data\\FashionMNIST\\raw\\train-images-idx3-ubyte.gz\n"]}, {"name": "stderr", "output_type": "stream", "text": ["100.0%\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Extracting ../data\\FashionMNIST\\raw\\train-images-idx3-ubyte.gz to ../data\\FashionMNIST\\raw\n", "\n", "Downloading http://fashion-mnist.s3-website.eu-central-1.amazonaws.com/train-labels-idx1-ubyte.gz\n", "Downloading http://fashion-mnist.s3-website.eu-central-1.amazonaws.com/train-labels-idx1-ubyte.gz to ../data\\FashionMNIST\\raw\\train-labels-idx1-ubyte.gz\n"]}, {"name": "stderr", "output_type": "stream", "text": ["100.0%\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Extracting ../data\\FashionMNIST\\raw\\train-labels-idx1-ubyte.gz to ../data\\FashionMNIST\\raw\n", "\n", "Downloading http://fashion-mnist.s3-website.eu-central-1.amazonaws.com/t10k-images-idx3-ubyte.gz\n", "Downloading http://fashion-mnist.s3-website.eu-central-1.amazonaws.com/t10k-images-idx3-ubyte.gz to ../data\\FashionMNIST\\raw\\t10k-images-idx3-ubyte.gz\n"]}, {"name": "stderr", "output_type": "stream", "text": ["100.0%\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Extracting ../data\\FashionMNIST\\raw\\t10k-images-idx3-ubyte.gz to ../data\\FashionMNIST\\raw\n", "\n", "Downloading http://fashion-mnist.s3-website.eu-central-1.amazonaws.com/t10k-labels-idx1-ubyte.gz\n", "Downloading http://fashion-mnist.s3-website.eu-central-1.amazonaws.com/t10k-labels-idx1-ubyte.gz to ../data\\FashionMNIST\\raw\\t10k-labels-idx1-ubyte.gz\n"]}, {"name": "stderr", "output_type": "stream", "text": ["100.0%"]}, {"name": "stdout", "output_type": "stream", "text": ["Extracting ../data\\FashionMNIST\\raw\\t10k-labels-idx1-ubyte.gz to ../data\\FashionMNIST\\raw\n", "\n"]}, {"name": "stderr", "output_type": "stream", "text": ["\n"]}], "source": ["# 通过ToTensor实例将图像数据从PIL类型变换成32位浮点数格式，\n", "# 并除以255使得所有像素的数值均在0～1之间\n", "trans = transforms.ToTensor()\n", "mnist_train = torchvision.datasets.FashionMNIST(\n", "    root=\"../data\", train=True, transform=trans, download=True)\n", "mnist_test = torchvision.datasets.FashionMNIST(\n", "    root=\"../data\", train=False, transform=trans, download=True)"]}, {"cell_type": "markdown", "id": "a1d2be35", "metadata": {"origin_pos": 10}, "source": ["Fashion-MNIST由10个类别的图像组成，\n", "每个类别由*训练数据集*（train dataset）中的6000张图像\n", "和*测试数据集*（test dataset）中的1000张图像组成。\n", "因此，训练集和测试集分别包含60000和10000张图像。\n", "测试数据集不会用于训练，只用于评估模型性能。\n"]}, {"cell_type": "code", "execution_count": 3, "id": "267b4e92", "metadata": {"execution": {"iopub.execute_input": "2022-12-07T16:28:15.083401Z", "iopub.status.busy": "2022-12-07T16:28:15.082851Z", "iopub.status.idle": "2022-12-07T16:28:15.090099Z", "shell.execute_reply": "2022-12-07T16:28:15.089358Z"}, "origin_pos": 11, "tab": ["pytorch"]}, "outputs": [{"data": {"text/plain": ["(60000, 10000)"]}, "execution_count": 3, "metadata": {}, "output_type": "execute_result"}], "source": ["len(mnist_train), len(mnist_test)"]}, {"cell_type": "markdown", "id": "f6dc8fdf", "metadata": {"origin_pos": 13}, "source": ["每个输入图像的高度和宽度均为28像素。\n", "数据集由灰度图像组成，其通道数为1。\n", "为了简洁起见，本书将高度$h$像素、宽度$w$像素图像的形状记为$h \\times w$或（$h$,$w$）。\n"]}, {"cell_type": "code", "execution_count": 4, "id": "38b77e4d", "metadata": {"execution": {"iopub.execute_input": "2022-12-07T16:28:15.093310Z", "iopub.status.busy": "2022-12-07T16:28:15.092799Z", "iopub.status.idle": "2022-12-07T16:28:15.098592Z", "shell.execute_reply": "2022-12-07T16:28:15.097850Z"}, "origin_pos": 14, "tab": ["pytorch"]}, "outputs": [{"data": {"text/plain": ["torch.<PERSON><PERSON>([1, 28, 28])"]}, "execution_count": 4, "metadata": {}, "output_type": "execute_result"}], "source": ["mnist_train[0][0].shape"]}, {"cell_type": "markdown", "id": "169848dd", "metadata": {"origin_pos": 15}, "source": ["[~~两个可视化数据集的函数~~]\n", "\n", "Fashion-MNIST中包含的10个类别，分别为t-shirt（T恤）、trouser（裤子）、pullover（套衫）、dress（连衣裙）、coat（外套）、sandal（凉鞋）、shirt（衬衫）、sneaker（运动鞋）、bag（包）和ankle boot（短靴）。\n", "以下函数用于在数字标签索引及其文本名称之间进行转换。\n"]}, {"cell_type": "code", "execution_count": 5, "id": "43b28d3a", "metadata": {"execution": {"iopub.execute_input": "2022-12-07T16:28:15.101944Z", "iopub.status.busy": "2022-12-07T16:28:15.101317Z", "iopub.status.idle": "2022-12-07T16:28:15.105958Z", "shell.execute_reply": "2022-12-07T16:28:15.105208Z"}, "origin_pos": 16, "tab": ["pytorch"]}, "outputs": [], "source": ["def get_fashion_mnist_labels(labels):  #@save\n", "    \"\"\"返回Fashion-MNIST数据集的文本标签\"\"\"\n", "    text_labels = ['t-shirt', 'trouser', 'pullover', 'dress', 'coat',\n", "                   'sandal', 'shirt', 'sneaker', 'bag', 'ankle boot']\n", "    return [text_labels[int(i)] for i in labels]"]}, {"cell_type": "markdown", "id": "c4f8012a", "metadata": {"origin_pos": 17}, "source": ["我们现在可以创建一个函数来可视化这些样本。\n"]}, {"cell_type": "code", "execution_count": 6, "id": "8c2ba91b", "metadata": {"execution": {"iopub.execute_input": "2022-12-07T16:28:15.109338Z", "iopub.status.busy": "2022-12-07T16:28:15.108722Z", "iopub.status.idle": "2022-12-07T16:28:15.114733Z", "shell.execute_reply": "2022-12-07T16:28:15.113948Z"}, "origin_pos": 19, "tab": ["pytorch"]}, "outputs": [], "source": ["def show_images(imgs, num_rows, num_cols, titles=None, scale=1.5):  #@save\n", "    \"\"\"绘制图像列表\"\"\"\n", "    figsize = (num_cols * scale, num_rows * scale)\n", "    _, axes = d2l.plt.subplots(num_rows, num_cols, figsize=figsize)\n", "    axes = axes.flatten()\n", "    for i, (ax, img) in enumerate(zip(axes, imgs)):\n", "        if torch.is_tensor(img):\n", "            # 图片张量\n", "            ax.imshow(img.numpy())\n", "        else:\n", "            # PIL图片\n", "            ax.imshow(img)\n", "        ax.axes.get_xaxis().set_visible(False)\n", "        ax.axes.get_yaxis().set_visible(False)\n", "        if titles:\n", "            ax.set_title(titles[i])\n", "    return axes"]}, {"cell_type": "markdown", "id": "50709460", "metadata": {"origin_pos": 21}, "source": ["以下是训练数据集中前[**几个样本的图像及其相应的标签**]。\n"]}, {"cell_type": "code", "execution_count": 7, "id": "fa163fa2", "metadata": {"execution": {"iopub.execute_input": "2022-12-07T16:28:15.117826Z", "iopub.status.busy": "2022-12-07T16:28:15.117433Z", "iopub.status.idle": "2022-12-07T16:28:15.845873Z", "shell.execute_reply": "2022-12-07T16:28:15.845083Z"}, "origin_pos": 23, "tab": ["pytorch"]}, "outputs": [{"data": {"image/svg+xml": ["<?xml version=\"1.0\" encoding=\"utf-8\" standalone=\"no\"?>\n", "<!DOCTYPE svg PUBLIC \"-//W3C//DTD SVG 1.1//EN\"\n", "  \"http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd\">\n", "<svg xmlns:xlink=\"http://www.w3.org/1999/xlink\" width=\"771.2pt\" height=\"194.804163pt\" viewBox=\"0 0 771.2 194.804163\" xmlns=\"http://www.w3.org/2000/svg\" version=\"1.1\">\n", " <metadata>\n", "  <rdf:RDF xmlns:dc=\"http://purl.org/dc/elements/1.1/\" xmlns:cc=\"http://creativecommons.org/ns#\" xmlns:rdf=\"http://www.w3.org/1999/02/22-rdf-syntax-ns#\">\n", "   <cc:Work>\n", "    <dc:type rdf:resource=\"http://purl.org/dc/dcmitype/StillImage\"/>\n", "    <dc:date>2025-07-19T20:58:12.340834</dc:date>\n", "    <dc:format>image/svg+xml</dc:format>\n", "    <dc:creator>\n", "     <cc:Agent>\n", "      <dc:title>Matplotlib v3.5.1, https://matplotlib.org/</dc:title>\n", "     </cc:Agent>\n", "    </dc:creator>\n", "   </cc:Work>\n", "  </rdf:RDF>\n", " </metadata>\n", " <defs>\n", "  <style type=\"text/css\">*{stroke-linejoin: round; stroke-linecap: butt}</style>\n", " </defs>\n", " <g id=\"figure_1\">\n", "  <g id=\"patch_1\">\n", "   <path d=\"M 0 194.804163 \n", "L 771.2 194.804163 \n", "L 771.2 0 \n", "L 0 0 \n", "z\n", "\" style=\"fill: #ffffff\"/>\n", "  </g>\n", "  <g id=\"axes_1\">\n", "   <g id=\"patch_2\">\n", "    <path d=\"M 10.7 93.384163 \n", "L 81.766038 93.384163 \n", "L 81.766038 22.318125 \n", "L 10.7 22.318125 \n", "z\n", "\" style=\"fill: #ffffff\"/>\n", "   </g>\n", "   <g clip-path=\"url(#pd2b10f666d)\">\n", "    <image xlink:href=\"data:image/png;base64,\n", "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\" id=\"imagede61398cd3\" transform=\"scale(1 -1)translate(0 -71.28)\" x=\"10.7\" y=\"-22.104163\" width=\"71.28\" height=\"71.28\"/>\n", "   </g>\n", "   <g id=\"patch_3\">\n", "    <path d=\"M 10.7 93.384163 \n", "L 10.7 22.318125 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_4\">\n", "    <path d=\"M 81.766038 93.384163 \n", "L 81.766038 22.318125 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_5\">\n", "    <path d=\"M 10.7 93.384163 \n", "L 81.766038 93.384163 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_6\">\n", "    <path d=\"M 10.7 22.318125 \n", "L 81.766038 22.318125 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"text_1\">\n", "    <!-- ankle boot -->\n", "    <g transform=\"translate(14.509894 16.318125)scale(0.12 -0.12)\">\n", "     <defs>\n", "      <path id=\"DejaVuSans-61\" d=\"M 2194 1759 \n", "Q 1497 1759 1228 1600 \n", "Q 959 1441 959 1056 \n", "Q 959 750 1161 570 \n", "Q 1363 391 1709 391 \n", "Q 2188 391 2477 730 \n", "Q 2766 1069 2766 1631 \n", "L 2766 1759 \n", "L 2194 1759 \n", "z\n", "M 3341 1997 \n", "L 3341 0 \n", "L 2766 0 \n", "L 2766 531 \n", "Q 2569 213 2275 61 \n", "Q 1981 -91 1556 -91 \n", "Q 1019 -91 701 211 \n", "Q 384 513 384 1019 \n", "Q 384 1609 779 1909 \n", "Q 1175 2209 1959 2209 \n", "L 2766 2209 \n", "L 2766 2266 \n", "Q 2766 2663 2505 2880 \n", "Q 2244 3097 1772 3097 \n", "Q 1472 3097 1187 3025 \n", "Q 903 2953 641 2809 \n", "L 641 3341 \n", "Q 956 3463 1253 3523 \n", "Q 1550 3584 1831 3584 \n", "Q 2591 3584 2966 3190 \n", "Q 3341 2797 3341 1997 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "      <path id=\"DejaVuSans-6e\" d=\"M 3513 2113 \n", "L 3513 0 \n", "L 2938 0 \n", "L 2938 2094 \n", "Q 2938 2591 2744 2837 \n", "Q 2550 3084 2163 3084 \n", "Q 1697 3084 1428 2787 \n", "Q 1159 2491 1159 1978 \n", "L 1159 0 \n", "L 581 0 \n", "L 581 3500 \n", "L 1159 3500 \n", "L 1159 2956 \n", "Q 1366 3272 1645 3428 \n", "Q 1925 3584 2291 3584 \n", "Q 2894 3584 3203 3211 \n", "Q 3513 2838 3513 2113 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "      <path id=\"DejaVuSans-6b\" d=\"M 581 4863 \n", "L 1159 4863 \n", "L 1159 1991 \n", "L 2875 3500 \n", "L 3609 3500 \n", "L 1753 1863 \n", "L 3688 0 \n", "L 2938 0 \n", "L 1159 1709 \n", "L 1159 0 \n", "L 581 0 \n", "L 581 4863 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "      <path id=\"DejaVuSans-6c\" d=\"M 603 4863 \n", "L 1178 4863 \n", "L 1178 0 \n", "L 603 0 \n", "L 603 4863 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "      <path id=\"DejaVuSans-65\" d=\"M 3597 1894 \n", "L 3597 1613 \n", "L 953 1613 \n", "Q 991 1019 1311 708 \n", "Q 1631 397 2203 397 \n", "Q 2534 397 2845 478 \n", "Q 3156 559 3463 722 \n", "L 3463 178 \n", "Q 3153 47 2828 -22 \n", "Q 2503 -91 2169 -91 \n", "Q 1331 -91 842 396 \n", "Q 353 884 353 1716 \n", "Q 353 2575 817 3079 \n", "Q 1281 3584 2069 3584 \n", "Q 2775 3584 3186 3129 \n", "Q 3597 2675 3597 1894 \n", "z\n", "M 3022 2063 \n", "Q 3016 2534 2758 2815 \n", "Q 2500 3097 2075 3097 \n", "Q 1594 3097 1305 2825 \n", "Q 1016 2553 972 2059 \n", "L 3022 2063 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "      <path id=\"DejaVuSans-20\" transform=\"scale(0.015625)\"/>\n", "      <path id=\"DejaVuSans-62\" d=\"M 3116 1747 \n", "Q 3116 2381 2855 2742 \n", "Q 2594 3103 2138 3103 \n", "Q 1681 3103 1420 2742 \n", "Q 1159 2381 1159 1747 \n", "Q 1159 1113 1420 752 \n", "Q 1681 391 2138 391 \n", "Q 2594 391 2855 752 \n", "Q 3116 1113 3116 1747 \n", "z\n", "M 1159 2969 \n", "Q 1341 3281 1617 3432 \n", "Q 1894 3584 2278 3584 \n", "Q 2916 3584 3314 3078 \n", "Q 3713 2572 3713 1747 \n", "Q 3713 922 3314 415 \n", "Q 2916 -91 2278 -91 \n", "Q 1894 -91 1617 61 \n", "Q 1341 213 1159 525 \n", "L 1159 0 \n", "L 581 0 \n", "L 581 4863 \n", "L 1159 4863 \n", "L 1159 2969 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "      <path id=\"DejaVuSans-6f\" d=\"M 1959 3097 \n", "Q 1497 3097 1228 2736 \n", "Q 959 2375 959 1747 \n", "Q 959 1119 1226 758 \n", "Q 1494 397 1959 397 \n", "Q 2419 397 2687 759 \n", "Q 2956 1122 2956 1747 \n", "Q 2956 2369 2687 2733 \n", "Q 2419 3097 1959 3097 \n", "z\n", "M 1959 3584 \n", "Q 2709 3584 3137 3096 \n", "Q 3566 2609 3566 1747 \n", "Q 3566 888 3137 398 \n", "Q 2709 -91 1959 -91 \n", "Q 1206 -91 779 398 \n", "Q 353 888 353 1747 \n", "Q 353 2609 779 3096 \n", "Q 1206 3584 1959 3584 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "      <path id=\"DejaVuSans-74\" d=\"M 1172 4494 \n", "L 1172 3500 \n", "L 2356 3500 \n", "L 2356 3053 \n", "L 1172 3053 \n", "L 1172 1153 \n", "Q 1172 725 1289 603 \n", "Q 1406 481 1766 481 \n", "L 2356 481 \n", "L 2356 0 \n", "L 1766 0 \n", "Q 1100 0 847 248 \n", "Q 594 497 594 1153 \n", "L 594 3053 \n", "L 172 3053 \n", "L 172 3500 \n", "L 594 3500 \n", "L 594 4494 \n", "L 1172 4494 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "     </defs>\n", "     <use xlink:href=\"#DejaVuSans-61\"/>\n", "     <use xlink:href=\"#DejaVuSans-6e\" x=\"61.279297\"/>\n", "     <use xlink:href=\"#DejaVuSans-6b\" x=\"124.658203\"/>\n", "     <use xlink:href=\"#DejaVuSans-6c\" x=\"182.568359\"/>\n", "     <use xlink:href=\"#DejaVuSans-65\" x=\"210.351562\"/>\n", "     <use xlink:href=\"#DejaVuSans-20\" x=\"271.875\"/>\n", "     <use xlink:href=\"#DejaVuSans-62\" x=\"303.662109\"/>\n", "     <use xlink:href=\"#DejaVuSans-6f\" x=\"367.138672\"/>\n", "     <use xlink:href=\"#DejaVuSans-6f\" x=\"428.320312\"/>\n", "     <use xlink:href=\"#DejaVuSans-74\" x=\"489.501953\"/>\n", "    </g>\n", "   </g>\n", "  </g>\n", "  <g id=\"axes_2\">\n", "   <g id=\"patch_7\">\n", "    <path d=\"M 95.979245 93.384163 \n", "L 167.045283 93.384163 \n", "L 167.045283 22.318125 \n", "L 95.979245 22.318125 \n", "z\n", "\" style=\"fill: #ffffff\"/>\n", "   </g>\n", "   <g clip-path=\"url(#p276b300061)\">\n", "    <image xlink:href=\"data:image/png;base64,\n", "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\" id=\"imageb6278889ee\" transform=\"scale(1 -1)translate(0 -71.28)\" x=\"95.979245\" y=\"-22.104163\" width=\"71.28\" height=\"71.28\"/>\n", "   </g>\n", "   <g id=\"patch_8\">\n", "    <path d=\"M 95.979245 93.384163 \n", "L 95.979245 22.318125 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_9\">\n", "    <path d=\"M 167.045283 93.384163 \n", "L 167.045283 22.318125 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_10\">\n", "    <path d=\"M 95.979245 93.384163 \n", "L 167.045283 93.384163 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_11\">\n", "    <path d=\"M 95.979245 22.318125 \n", "L 167.045283 22.318125 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"text_2\">\n", "    <!-- t-shirt -->\n", "    <g transform=\"translate(113.581639 16.318125)scale(0.12 -0.12)\">\n", "     <defs>\n", "      <path id=\"DejaVuSans-2d\" d=\"M 313 2009 \n", "L 1997 2009 \n", "L 1997 1497 \n", "L 313 1497 \n", "L 313 2009 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "      <path id=\"DejaVuSans-73\" d=\"M 2834 3397 \n", "L 2834 2853 \n", "Q 2591 2978 2328 3040 \n", "Q 2066 3103 1784 3103 \n", "Q 1356 3103 1142 2972 \n", "Q 928 2841 928 2578 \n", "Q 928 2378 1081 2264 \n", "Q 1234 2150 1697 2047 \n", "L 1894 2003 \n", "Q 2506 1872 2764 1633 \n", "Q 3022 1394 3022 966 \n", "Q 3022 478 2636 193 \n", "Q 2250 -91 1575 -91 \n", "Q 1294 -91 989 -36 \n", "Q 684 19 347 128 \n", "L 347 722 \n", "Q 666 556 975 473 \n", "Q 1284 391 1588 391 \n", "Q 1994 391 2212 530 \n", "Q 2431 669 2431 922 \n", "Q 2431 1156 2273 1281 \n", "Q 2116 1406 1581 1522 \n", "L 1381 1569 \n", "Q 847 1681 609 1914 \n", "Q 372 2147 372 2553 \n", "Q 372 3047 722 3315 \n", "Q 1072 3584 1716 3584 \n", "Q 2034 3584 2315 3537 \n", "Q 2597 3491 2834 3397 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "      <path id=\"DejaVuSans-68\" d=\"M 3513 2113 \n", "L 3513 0 \n", "L 2938 0 \n", "L 2938 2094 \n", "Q 2938 2591 2744 2837 \n", "Q 2550 3084 2163 3084 \n", "Q 1697 3084 1428 2787 \n", "Q 1159 2491 1159 1978 \n", "L 1159 0 \n", "L 581 0 \n", "L 581 4863 \n", "L 1159 4863 \n", "L 1159 2956 \n", "Q 1366 3272 1645 3428 \n", "Q 1925 3584 2291 3584 \n", "Q 2894 3584 3203 3211 \n", "Q 3513 2838 3513 2113 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "      <path id=\"DejaVuSans-69\" d=\"M 603 3500 \n", "L 1178 3500 \n", "L 1178 0 \n", "L 603 0 \n", "L 603 3500 \n", "z\n", "M 603 4863 \n", "L 1178 4863 \n", "L 1178 4134 \n", "L 603 4134 \n", "L 603 4863 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "      <path id=\"DejaVuSans-72\" d=\"M 2631 2963 \n", "Q 2534 3019 2420 3045 \n", "Q 2306 3072 2169 3072 \n", "Q 1681 3072 1420 2755 \n", "Q 1159 2438 1159 1844 \n", "L 1159 0 \n", "L 581 0 \n", "L 581 3500 \n", "L 1159 3500 \n", "L 1159 2956 \n", "Q 1341 3275 1631 3429 \n", "Q 1922 3584 2338 3584 \n", "Q 2397 3584 2469 3576 \n", "Q 2541 3569 2628 3553 \n", "L 2631 2963 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "     </defs>\n", "     <use xlink:href=\"#DejaVuSans-74\"/>\n", "     <use xlink:href=\"#DejaVuSans-2d\" x=\"39.208984\"/>\n", "     <use xlink:href=\"#DejaVuSans-73\" x=\"75.292969\"/>\n", "     <use xlink:href=\"#DejaVuSans-68\" x=\"127.392578\"/>\n", "     <use xlink:href=\"#DejaVuSans-69\" x=\"190.771484\"/>\n", "     <use xlink:href=\"#DejaVuSans-72\" x=\"218.554688\"/>\n", "     <use xlink:href=\"#DejaVuSans-74\" x=\"259.667969\"/>\n", "    </g>\n", "   </g>\n", "  </g>\n", "  <g id=\"axes_3\">\n", "   <g id=\"patch_12\">\n", "    <path d=\"M 181.258491 93.384163 \n", "L 252.324528 93.384163 \n", "L 252.324528 22.318125 \n", "L 181.258491 22.318125 \n", "z\n", "\" style=\"fill: #ffffff\"/>\n", "   </g>\n", "   <g clip-path=\"url(#p79ac6c70b7)\">\n", "    <image xlink:href=\"data:image/png;base64,\n", "iVBORw0KGgoAAAANSUhEUgAAAGMAAABjCAYAAACPO76VAAAFJUlEQVR4nO2dv28cRRTHd+724sv54uicxARBZKSIghAEoUJCKAXuKQglBTSIioKKjn+CBiQU0dKhiIoeKQUFBQRREHACAdmKdcmd725/DDqDdt575x2vndvdJ+33U8149mZW/u77sbOzs2bL3LBBlRjD67bY8OHTF1k93tzIytPBCh9CdHl6e+iGu3uPtaWjUbFzLXieT0Kr9BFAYSCGIiCGIsLKRxS+t7W6mpX33nqJte2fd9dKtCq6IZdRKzliyFfPZWWTuPKc/v00K69/8xNrS4Yu1lQBLEMREEMRpurUtr22xup/fHjVnUzKjw33PR2RY1ux9aa27GdtXp+cN7njb37+S1ZOdnaDsoFlKAJiKAJiNDm1ffTmC6xO/fTKns3372IWxZB01oo4IENGK84/n94Dd3TU54NMXnkuK3e+Q8xoFBCjyW4q7nJX0Ipc2QpXRO+sZRtzTUck50mHpq82906+85i3xX03SCcoH1iGIiCGIiBGk2NGf3vC6uOnernTEQF90CYvm2NM4sg4wdpIUyr+G+MLLmacDsoHlqEIiNFkN9WeJrmuKBVns+C2loCvz5SkwHPW73CXWjawDEVADEVAjCbHDDONPI0BYyGdLQE6zZKKOY/w0dQdV/6pwDI0ATEa7aYikdrawx8YHfbQiHFCv5GG3Bcaso5Luim66KEKYBmKgBiKgBiKqD21pdMTRsQBVi0pt7RtEkPkk8axS22PWM67FGAZioAYiqj9lQCavlrhJljam3ruzuXvZDfkdTCayv43BH2CJX64+zCoEliGIiCGIiBGo2NGvKQk0RzjUBInaPw4gKbW8tRmnhnmEoBlKAJiKKL21Jalr1Y0+VyRPZnLkvBUl3dkkyruux2wDEVADEVAjCbHDDsa8z+YYtMh1nPZLDwh9B0r5jxYqiuz3n0sYmssEEMREKPRMSPOfw/YnvDSWPidJ/b43t1gT/3mpLjPaCwQo9ELEsSs6cJMacnIWdtwUu1+mT5gGYqAGIqAGI2OGT3+Em+ycvjWFQf43LnxHCfqdGWJTIPplhiztfzNLL2bES8JWIYiIEajd9V58Derm+RywTvloPi7GwLqmuQitrjryuGIp71VuCYKLEMREEMREKPRq0OOsX1EK8nfHNjXh2/2V24eSXeG6+7WOzUCy1AExFCEKjcV93hqSfe5lUtk6YI372K3g4Pzm7wzABUDy1AExFAExFBE/TGDpJqmXfydPlaXv/OkyzK+0H6O+lxQ2cAyFAExFFG7mzpF9h+PrOcOPCi+F7rvMz8+d9eudmntArAMRUAMRUAMRdQeM9haV8OvjbSd7/y7e2nup+GiHu8notMsskvSJPupGliGIiCGImp3U3IDR0pvJ8n97MLwUnjoVyjnrN3l+euZe+42f3whzL8jf4JXmJcBLEMREEMREEMRtccM+tXI3j/c1w833eltvf89a3uxdz8rr7cfs7afJ8+w+s07r2Xlja/4Wt/pgPwLan5VA5ahCIihiNrdVGvmfEO8wnPL6+/dzspvnHEfSj+od3ey8qDtvmg253LnR1a/em07K39s32Ft5752v5316702YRmKgBiKgBiN/jTcYMDqs7MuTrz70bes7WHsXuO6+dfrrO2zyK0+G0WnWNvF1SGrn+24R3ifvnyLtX0yfDsrr99GzAD/AzEUYbbMjVrvO8PNS1n51w+eZW3Pf/FnVo5/+30p46XXr7H67hX3HtnGlz+wNjt1n2yoAliGIiCGIiBGoId/AV2WUj/022DnAAAAAElFTkSuQmCC\" id=\"image6676aebd18\" transform=\"scale(1 -1)translate(0 -71.28)\" x=\"181.258491\" y=\"-22.104163\" width=\"71.28\" height=\"71.28\"/>\n", "   </g>\n", "   <g id=\"patch_13\">\n", "    <path d=\"M 181.258491 93.384163 \n", "L 181.258491 22.318125 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_14\">\n", "    <path d=\"M 252.324528 93.384163 \n", "L 252.324528 22.318125 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_15\">\n", "    <path d=\"M 181.258491 93.384163 \n", "L 252.324528 93.384163 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_16\">\n", "    <path d=\"M 181.258491 22.318125 \n", "L 252.324528 22.318125 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"text_3\">\n", "    <!-- t-shirt -->\n", "    <g transform=\"translate(198.860884 16.318125)scale(0.12 -0.12)\">\n", "     <use xlink:href=\"#DejaVuSans-74\"/>\n", "     <use xlink:href=\"#DejaVuSans-2d\" x=\"39.208984\"/>\n", "     <use xlink:href=\"#DejaVuSans-73\" x=\"75.292969\"/>\n", "     <use xlink:href=\"#DejaVuSans-68\" x=\"127.392578\"/>\n", "     <use xlink:href=\"#DejaVuSans-69\" x=\"190.771484\"/>\n", "     <use xlink:href=\"#DejaVuSans-72\" x=\"218.554688\"/>\n", "     <use xlink:href=\"#DejaVuSans-74\" x=\"259.667969\"/>\n", "    </g>\n", "   </g>\n", "  </g>\n", "  <g id=\"axes_4\">\n", "   <g id=\"patch_17\">\n", "    <path d=\"M 266.537736 93.384163 \n", "L 337.603774 93.384163 \n", "L 337.603774 22.318125 \n", "L 266.537736 22.318125 \n", "z\n", "\" style=\"fill: #ffffff\"/>\n", "   </g>\n", "   <g clip-path=\"url(#pec1b5d1fac)\">\n", "    <image xlink:href=\"data:image/png;base64,\n", "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\" id=\"imagefce24db92d\" transform=\"scale(1 -1)translate(0 -71.28)\" x=\"266.537736\" y=\"-22.104163\" width=\"71.28\" height=\"71.28\"/>\n", "   </g>\n", "   <g id=\"patch_18\">\n", "    <path d=\"M 266.537736 93.384163 \n", "L 266.537736 22.318125 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_19\">\n", "    <path d=\"M 337.603774 93.384163 \n", "L 337.603774 22.318125 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_20\">\n", "    <path d=\"M 266.537736 93.384163 \n", "L 337.603774 93.384163 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_21\">\n", "    <path d=\"M 266.537736 22.318125 \n", "L 337.603774 22.318125 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"text_4\">\n", "    <!-- dress -->\n", "    <g transform=\"translate(285.987005 16.318125)scale(0.12 -0.12)\">\n", "     <defs>\n", "      <path id=\"DejaVuSans-64\" d=\"M 2906 2969 \n", "L 2906 4863 \n", "L 3481 4863 \n", "L 3481 0 \n", "L 2906 0 \n", "L 2906 525 \n", "Q 2725 213 2448 61 \n", "Q 2172 -91 1784 -91 \n", "Q 1150 -91 751 415 \n", "Q 353 922 353 1747 \n", "Q 353 2572 751 3078 \n", "Q 1150 3584 1784 3584 \n", "Q 2172 3584 2448 3432 \n", "Q 2725 3281 2906 2969 \n", "z\n", "M 947 1747 \n", "Q 947 1113 1208 752 \n", "Q 1469 391 1925 391 \n", "Q 2381 391 2643 752 \n", "Q 2906 1113 2906 1747 \n", "Q 2906 2381 2643 2742 \n", "Q 2381 3103 1925 3103 \n", "Q 1469 3103 1208 2742 \n", "Q 947 2381 947 1747 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "     </defs>\n", "     <use xlink:href=\"#DejaVuSans-64\"/>\n", "     <use xlink:href=\"#DejaVuSans-72\" x=\"63.476562\"/>\n", "     <use xlink:href=\"#DejaVuSans-65\" x=\"102.339844\"/>\n", "     <use xlink:href=\"#DejaVuSans-73\" x=\"163.863281\"/>\n", "     <use xlink:href=\"#DejaVuSans-73\" x=\"215.962891\"/>\n", "    </g>\n", "   </g>\n", "  </g>\n", "  <g id=\"axes_5\">\n", "   <g id=\"patch_22\">\n", "    <path d=\"M 351.816981 93.384163 \n", "L 422.883019 93.384163 \n", "L 422.883019 22.318125 \n", "L 351.816981 22.318125 \n", "z\n", "\" style=\"fill: #ffffff\"/>\n", "   </g>\n", "   <g clip-path=\"url(#p32082b3170)\">\n", "    <image xlink:href=\"data:image/png;base64,\n", "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\" id=\"image55fdb19e50\" transform=\"scale(1 -1)translate(0 -71.28)\" x=\"351.816981\" y=\"-22.104163\" width=\"71.28\" height=\"71.28\"/>\n", "   </g>\n", "   <g id=\"patch_23\">\n", "    <path d=\"M 351.816981 93.384163 \n", "L 351.816981 22.318125 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_24\">\n", "    <path d=\"M 422.883019 93.384163 \n", "L 422.883019 22.318125 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_25\">\n", "    <path d=\"M 351.816981 93.384163 \n", "L 422.883019 93.384163 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_26\">\n", "    <path d=\"M 351.816981 22.318125 \n", "L 422.883019 22.318125 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"text_5\">\n", "    <!-- t-shirt -->\n", "    <g transform=\"translate(369.419375 16.318125)scale(0.12 -0.12)\">\n", "     <use xlink:href=\"#DejaVuSans-74\"/>\n", "     <use xlink:href=\"#DejaVuSans-2d\" x=\"39.208984\"/>\n", "     <use xlink:href=\"#DejaVuSans-73\" x=\"75.292969\"/>\n", "     <use xlink:href=\"#DejaVuSans-68\" x=\"127.392578\"/>\n", "     <use xlink:href=\"#DejaVuSans-69\" x=\"190.771484\"/>\n", "     <use xlink:href=\"#DejaVuSans-72\" x=\"218.554688\"/>\n", "     <use xlink:href=\"#DejaVuSans-74\" x=\"259.667969\"/>\n", "    </g>\n", "   </g>\n", "  </g>\n", "  <g id=\"axes_6\">\n", "   <g id=\"patch_27\">\n", "    <path d=\"M 437.096226 93.384163 \n", "L 508.162264 93.384163 \n", "L 508.162264 22.318125 \n", "L 437.096226 22.318125 \n", "z\n", "\" style=\"fill: #ffffff\"/>\n", "   </g>\n", "   <g clip-path=\"url(#p288f6b9b2b)\">\n", "    <image xlink:href=\"data:image/png;base64,\n", "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\" id=\"image9a15e07f91\" transform=\"scale(1 -1)translate(0 -71.28)\" x=\"437.096226\" y=\"-22.104163\" width=\"71.28\" height=\"71.28\"/>\n", "   </g>\n", "   <g id=\"patch_28\">\n", "    <path d=\"M 437.096226 93.384163 \n", "L 437.096226 22.318125 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_29\">\n", "    <path d=\"M 508.162264 93.384163 \n", "L 508.162264 22.318125 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_30\">\n", "    <path d=\"M 437.096226 93.384163 \n", "L 508.162264 93.384163 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_31\">\n", "    <path d=\"M 437.096226 22.318125 \n", "L 508.162264 22.318125 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"text_6\">\n", "    <!-- pullover -->\n", "    <g transform=\"translate(448.302995 16.318125)scale(0.12 -0.12)\">\n", "     <defs>\n", "      <path id=\"DejaVuSans-70\" d=\"M 1159 525 \n", "L 1159 -1331 \n", "L 581 -1331 \n", "L 581 3500 \n", "L 1159 3500 \n", "L 1159 2969 \n", "Q 1341 3281 1617 3432 \n", "Q 1894 3584 2278 3584 \n", "Q 2916 3584 3314 3078 \n", "Q 3713 2572 3713 1747 \n", "Q 3713 922 3314 415 \n", "Q 2916 -91 2278 -91 \n", "Q 1894 -91 1617 61 \n", "Q 1341 213 1159 525 \n", "z\n", "M 3116 1747 \n", "Q 3116 2381 2855 2742 \n", "Q 2594 3103 2138 3103 \n", "Q 1681 3103 1420 2742 \n", "Q 1159 2381 1159 1747 \n", "Q 1159 1113 1420 752 \n", "Q 1681 391 2138 391 \n", "Q 2594 391 2855 752 \n", "Q 3116 1113 3116 1747 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "      <path id=\"DejaVuSans-75\" d=\"M 544 1381 \n", "L 544 3500 \n", "L 1119 3500 \n", "L 1119 1403 \n", "Q 1119 906 1312 657 \n", "Q 1506 409 1894 409 \n", "Q 2359 409 2629 706 \n", "Q 2900 1003 2900 1516 \n", "L 2900 3500 \n", "L 3475 3500 \n", "L 3475 0 \n", "L 2900 0 \n", "L 2900 538 \n", "Q 2691 219 2414 64 \n", "Q 2138 -91 1772 -91 \n", "Q 1169 -91 856 284 \n", "Q 544 659 544 1381 \n", "z\n", "M 1991 3584 \n", "L 1991 3584 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "      <path id=\"DejaVuSans-76\" d=\"M 191 3500 \n", "L 800 3500 \n", "L 1894 563 \n", "L 2988 3500 \n", "L 3597 3500 \n", "L 2284 0 \n", "L 1503 0 \n", "L 191 3500 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "     </defs>\n", "     <use xlink:href=\"#DejaVuSans-70\"/>\n", "     <use xlink:href=\"#DejaVuSans-75\" x=\"63.476562\"/>\n", "     <use xlink:href=\"#DejaVuSans-6c\" x=\"126.855469\"/>\n", "     <use xlink:href=\"#DejaVuSans-6c\" x=\"154.638672\"/>\n", "     <use xlink:href=\"#DejaVuSans-6f\" x=\"182.421875\"/>\n", "     <use xlink:href=\"#DejaVuSans-76\" x=\"243.603516\"/>\n", "     <use xlink:href=\"#DejaVuSans-65\" x=\"302.783203\"/>\n", "     <use xlink:href=\"#DejaVuSans-72\" x=\"364.306641\"/>\n", "    </g>\n", "   </g>\n", "  </g>\n", "  <g id=\"axes_7\">\n", "   <g id=\"patch_32\">\n", "    <path d=\"M 522.375472 93.384163 \n", "L 593.441509 93.384163 \n", "L 593.441509 22.318125 \n", "L 522.375472 22.318125 \n", "z\n", "\" style=\"fill: #ffffff\"/>\n", "   </g>\n", "   <g clip-path=\"url(#p8d92a856f0)\">\n", "    <image xlink:href=\"data:image/png;base64,\n", "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\" id=\"imageab5e0ff585\" transform=\"scale(1 -1)translate(0 -71.28)\" x=\"522.375472\" y=\"-22.104163\" width=\"71.28\" height=\"71.28\"/>\n", "   </g>\n", "   <g id=\"patch_33\">\n", "    <path d=\"M 522.375472 93.384163 \n", "L 522.375472 22.318125 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_34\">\n", "    <path d=\"M 593.441509 93.384163 \n", "L 593.441509 22.318125 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_35\">\n", "    <path d=\"M 522.375472 93.384163 \n", "L 593.441509 93.384163 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_36\">\n", "    <path d=\"M 522.375472 22.318125 \n", "L 593.441509 22.318125 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"text_7\">\n", "    <!-- sneaker -->\n", "    <g transform=\"translate(534.196303 16.318125)scale(0.12 -0.12)\">\n", "     <use xlink:href=\"#DejaVuSans-73\"/>\n", "     <use xlink:href=\"#DejaVuSans-6e\" x=\"52.099609\"/>\n", "     <use xlink:href=\"#DejaVuSans-65\" x=\"115.478516\"/>\n", "     <use xlink:href=\"#DejaVuSans-61\" x=\"177.001953\"/>\n", "     <use xlink:href=\"#DejaVuSans-6b\" x=\"238.28125\"/>\n", "     <use xlink:href=\"#DejaVuSans-65\" x=\"292.566406\"/>\n", "     <use xlink:href=\"#DejaVuSans-72\" x=\"354.089844\"/>\n", "    </g>\n", "   </g>\n", "  </g>\n", "  <g id=\"axes_8\">\n", "   <g id=\"patch_37\">\n", "    <path d=\"M 607.654717 93.384163 \n", "L 678.720755 93.384163 \n", "L 678.720755 22.318125 \n", "L 607.654717 22.318125 \n", "z\n", "\" style=\"fill: #ffffff\"/>\n", "   </g>\n", "   <g clip-path=\"url(#pfce0c330d5)\">\n", "    <image xlink:href=\"data:image/png;base64,\n", "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\" id=\"image43badf3906\" transform=\"scale(1 -1)translate(0 -71.28)\" x=\"607.654717\" y=\"-22.104163\" width=\"71.28\" height=\"71.28\"/>\n", "   </g>\n", "   <g id=\"patch_38\">\n", "    <path d=\"M 607.654717 93.384163 \n", "L 607.654717 22.318125 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_39\">\n", "    <path d=\"M 678.720755 93.384163 \n", "L 678.720755 22.318125 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_40\">\n", "    <path d=\"M 607.654717 93.384163 \n", "L 678.720755 93.384163 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_41\">\n", "    <path d=\"M 607.654717 22.318125 \n", "L 678.720755 22.318125 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"text_8\">\n", "    <!-- pullover -->\n", "    <g transform=\"translate(618.861486 16.318125)scale(0.12 -0.12)\">\n", "     <use xlink:href=\"#DejaVuSans-70\"/>\n", "     <use xlink:href=\"#DejaVuSans-75\" x=\"63.476562\"/>\n", "     <use xlink:href=\"#DejaVuSans-6c\" x=\"126.855469\"/>\n", "     <use xlink:href=\"#DejaVuSans-6c\" x=\"154.638672\"/>\n", "     <use xlink:href=\"#DejaVuSans-6f\" x=\"182.421875\"/>\n", "     <use xlink:href=\"#DejaVuSans-76\" x=\"243.603516\"/>\n", "     <use xlink:href=\"#DejaVuSans-65\" x=\"302.783203\"/>\n", "     <use xlink:href=\"#DejaVuSans-72\" x=\"364.306641\"/>\n", "    </g>\n", "   </g>\n", "  </g>\n", "  <g id=\"axes_9\">\n", "   <g id=\"patch_42\">\n", "    <path d=\"M 692.933962 93.384163 \n", "L 764 93.384163 \n", "L 764 22.318125 \n", "L 692.933962 22.318125 \n", "z\n", "\" style=\"fill: #ffffff\"/>\n", "   </g>\n", "   <g clip-path=\"url(#p90efdf36f1)\">\n", "    <image xlink:href=\"data:image/png;base64,\n", "iVBORw0KGgoAAAANSUhEUgAAAGMAAABjCAYAAACPO76VAAAG5klEQVR4nO2dXWwUVRTHZ3aH3W27Xbp8FJDSCrRYFFEigqKkgfCRoMEYEHgwsSZ+8KBgC<PERSON>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\" id=\"image8461cdf35a\" transform=\"scale(1 -1)translate(0 -71.28)\" x=\"692.933962\" y=\"-22.104163\" width=\"71.28\" height=\"71.28\"/>\n", "   </g>\n", "   <g id=\"patch_43\">\n", "    <path d=\"M 692.933962 93.384163 \n", "L 692.933962 22.318125 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_44\">\n", "    <path d=\"M 764 93.384163 \n", "L 764 22.318125 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_45\">\n", "    <path d=\"M 692.933962 93.384163 \n", "L 764 93.384163 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_46\">\n", "    <path d=\"M 692.933962 22.318125 \n", "L 764 22.318125 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"text_9\">\n", "    <!-- sandal -->\n", "    <g transform=\"translate(708.709169 16.318125)scale(0.12 -0.12)\">\n", "     <use xlink:href=\"#DejaVuSans-73\"/>\n", "     <use xlink:href=\"#DejaVuSans-61\" x=\"52.099609\"/>\n", "     <use xlink:href=\"#DejaVuSans-6e\" x=\"113.378906\"/>\n", "     <use xlink:href=\"#DejaVuSans-64\" x=\"176.757812\"/>\n", "     <use xlink:href=\"#DejaVuSans-61\" x=\"240.234375\"/>\n", "     <use xlink:href=\"#DejaVuSans-6c\" x=\"301.513672\"/>\n", "    </g>\n", "   </g>\n", "  </g>\n", "  <g id=\"axes_10\">\n", "   <g id=\"patch_47\">\n", "    <path d=\"M 10.7 184.104163 \n", "L 81.766038 184.104163 \n", "L 81.766038 113.038125 \n", "L 10.7 113.038125 \n", "z\n", "\" style=\"fill: #ffffff\"/>\n", "   </g>\n", "   <g clip-path=\"url(#p91ba52fd33)\">\n", "    <image xlink:href=\"data:image/png;base64,\n", "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\" id=\"image71abad0158\" transform=\"scale(1 -1)translate(0 -71.28)\" x=\"10.7\" y=\"-112.824163\" width=\"71.28\" height=\"71.28\"/>\n", "   </g>\n", "   <g id=\"patch_48\">\n", "    <path d=\"M 10.7 184.104163 \n", "L 10.7 113.038125 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_49\">\n", "    <path d=\"M 81.766038 184.104163 \n", "L 81.766038 113.038125 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_50\">\n", "    <path d=\"M 10.7 184.104163 \n", "L 81.766038 184.104163 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_51\">\n", "    <path d=\"M 10.7 113.038125 \n", "L 81.766038 113.038125 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"text_10\">\n", "    <!-- sandal -->\n", "    <g transform=\"translate(26.475206 107.038125)scale(0.12 -0.12)\">\n", "     <use xlink:href=\"#DejaVuSans-73\"/>\n", "     <use xlink:href=\"#DejaVuSans-61\" x=\"52.099609\"/>\n", "     <use xlink:href=\"#DejaVuSans-6e\" x=\"113.378906\"/>\n", "     <use xlink:href=\"#DejaVuSans-64\" x=\"176.757812\"/>\n", "     <use xlink:href=\"#DejaVuSans-61\" x=\"240.234375\"/>\n", "     <use xlink:href=\"#DejaVuSans-6c\" x=\"301.513672\"/>\n", "    </g>\n", "   </g>\n", "  </g>\n", "  <g id=\"axes_11\">\n", "   <g id=\"patch_52\">\n", "    <path d=\"M 95.979245 184.104163 \n", "L 167.045283 184.104163 \n", "L 167.045283 113.038125 \n", "L 95.979245 113.038125 \n", "z\n", "\" style=\"fill: #ffffff\"/>\n", "   </g>\n", "   <g clip-path=\"url(#p26998eb952)\">\n", "    <image xlink:href=\"data:image/png;base64,\n", "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\" id=\"image7b815ba5ac\" transform=\"scale(1 -1)translate(0 -71.28)\" x=\"95.979245\" y=\"-112.824163\" width=\"71.28\" height=\"71.28\"/>\n", "   </g>\n", "   <g id=\"patch_53\">\n", "    <path d=\"M 95.979245 184.104163 \n", "L 95.979245 113.038125 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_54\">\n", "    <path d=\"M 167.045283 184.104163 \n", "L 167.045283 113.038125 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_55\">\n", "    <path d=\"M 95.979245 184.104163 \n", "L 167.045283 184.104163 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_56\">\n", "    <path d=\"M 95.979245 113.038125 \n", "L 167.045283 113.038125 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"text_11\">\n", "    <!-- t-shirt -->\n", "    <g transform=\"translate(113.581639 107.038125)scale(0.12 -0.12)\">\n", "     <use xlink:href=\"#DejaVuSans-74\"/>\n", "     <use xlink:href=\"#DejaVuSans-2d\" x=\"39.208984\"/>\n", "     <use xlink:href=\"#DejaVuSans-73\" x=\"75.292969\"/>\n", "     <use xlink:href=\"#DejaVuSans-68\" x=\"127.392578\"/>\n", "     <use xlink:href=\"#DejaVuSans-69\" x=\"190.771484\"/>\n", "     <use xlink:href=\"#DejaVuSans-72\" x=\"218.554688\"/>\n", "     <use xlink:href=\"#DejaVuSans-74\" x=\"259.667969\"/>\n", "    </g>\n", "   </g>\n", "  </g>\n", "  <g id=\"axes_12\">\n", "   <g id=\"patch_57\">\n", "    <path d=\"M 181.258491 184.104163 \n", "L 252.324528 184.104163 \n", "L 252.324528 113.038125 \n", "L 181.258491 113.038125 \n", "z\n", "\" style=\"fill: #ffffff\"/>\n", "   </g>\n", "   <g clip-path=\"url(#p92945399b7)\">\n", "    <image xlink:href=\"data:image/png;base64,\n", "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\" id=\"imagec2576bf3a0\" transform=\"scale(1 -1)translate(0 -71.28)\" x=\"181.258491\" y=\"-112.824163\" width=\"71.28\" height=\"71.28\"/>\n", "   </g>\n", "   <g id=\"patch_58\">\n", "    <path d=\"M 181.258491 184.104163 \n", "L 181.258491 113.038125 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_59\">\n", "    <path d=\"M 252.324528 184.104163 \n", "L 252.324528 113.038125 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_60\">\n", "    <path d=\"M 181.258491 184.104163 \n", "L 252.324528 184.104163 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_61\">\n", "    <path d=\"M 181.258491 113.038125 \n", "L 252.324528 113.038125 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"text_12\">\n", "    <!-- ankle boot -->\n", "    <g transform=\"translate(185.068384 107.038125)scale(0.12 -0.12)\">\n", "     <use xlink:href=\"#DejaVuSans-61\"/>\n", "     <use xlink:href=\"#DejaVuSans-6e\" x=\"61.279297\"/>\n", "     <use xlink:href=\"#DejaVuSans-6b\" x=\"124.658203\"/>\n", "     <use xlink:href=\"#DejaVuSans-6c\" x=\"182.568359\"/>\n", "     <use xlink:href=\"#DejaVuSans-65\" x=\"210.351562\"/>\n", "     <use xlink:href=\"#DejaVuSans-20\" x=\"271.875\"/>\n", "     <use xlink:href=\"#DejaVuSans-62\" x=\"303.662109\"/>\n", "     <use xlink:href=\"#DejaVuSans-6f\" x=\"367.138672\"/>\n", "     <use xlink:href=\"#DejaVuSans-6f\" x=\"428.320312\"/>\n", "     <use xlink:href=\"#DejaVuSans-74\" x=\"489.501953\"/>\n", "    </g>\n", "   </g>\n", "  </g>\n", "  <g id=\"axes_13\">\n", "   <g id=\"patch_62\">\n", "    <path d=\"M 266.537736 184.104163 \n", "L 337.603774 184.104163 \n", "L 337.603774 113.038125 \n", "L 266.537736 113.038125 \n", "z\n", "\" style=\"fill: #ffffff\"/>\n", "   </g>\n", "   <g clip-path=\"url(#pfac1befe5b)\">\n", "    <image xlink:href=\"data:image/png;base64,\n", "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\" id=\"image39fa97b3bd\" transform=\"scale(1 -1)translate(0 -71.28)\" x=\"266.537736\" y=\"-112.824163\" width=\"71.28\" height=\"71.28\"/>\n", "   </g>\n", "   <g id=\"patch_63\">\n", "    <path d=\"M 266.537736 184.104163 \n", "L 266.537736 113.038125 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_64\">\n", "    <path d=\"M 337.603774 184.104163 \n", "L 337.603774 113.038125 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_65\">\n", "    <path d=\"M 266.537736 184.104163 \n", "L 337.603774 184.104163 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_66\">\n", "    <path d=\"M 266.537736 113.038125 \n", "L 337.603774 113.038125 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"text_13\">\n", "    <!-- sandal -->\n", "    <g transform=\"translate(282.312942 107.038125)scale(0.12 -0.12)\">\n", "     <use xlink:href=\"#DejaVuSans-73\"/>\n", "     <use xlink:href=\"#DejaVuSans-61\" x=\"52.099609\"/>\n", "     <use xlink:href=\"#DejaVuSans-6e\" x=\"113.378906\"/>\n", "     <use xlink:href=\"#DejaVuSans-64\" x=\"176.757812\"/>\n", "     <use xlink:href=\"#DejaVuSans-61\" x=\"240.234375\"/>\n", "     <use xlink:href=\"#DejaVuSans-6c\" x=\"301.513672\"/>\n", "    </g>\n", "   </g>\n", "  </g>\n", "  <g id=\"axes_14\">\n", "   <g id=\"patch_67\">\n", "    <path d=\"M 351.816981 184.104163 \n", "L 422.883019 184.104163 \n", "L 422.883019 113.038125 \n", "L 351.816981 113.038125 \n", "z\n", "\" style=\"fill: #ffffff\"/>\n", "   </g>\n", "   <g clip-path=\"url(#p79c2abd050)\">\n", "    <image xlink:href=\"data:image/png;base64,\n", "iVBORw0KGgoAAAANSUhEUgAAAGMAAABjCAYAAACPO76VAAAF3ElEQVR4nO2bTWhcVRTH333zkcwkk0kmpvlqTZM0qa0oKIoxWEGQolCqBbV1Jaib6ta1uBDUjYiCunDjwo+CuBJ0pbhoK2ksFUtLkkmbTjtNSJq2M8xM5uO9eTJZ3HP/L3kRQzGH4fxW582Zufe++c8997xz76hn1UueJbDA3ukBCISIwQgRgxEiBiNEDEaIGIwQMRghYjBCxGCEiMEIEYMRIgYjRAxGiBiMEDEYIWIwQsRghIjBCBGDESIGI0QMRogYjBAxGCFiMELEYISIwQgRgxHh7X5QhfGjdnsb+ZId4KsnyWfZqH+tq5VcZRc/1xKiNj08ElxORbXt+X5S7ZkSjq1UpfdGqM0GTju1U+mOYv9hpe1QFfuvJqhThcO2WnL0QrhQA190fon6XlzCcWIzwk4iYjBCxGBEONS7C15YOjaq7dIAvrluhNTWFYqnDVSdbIeWgXVqCYq3tZQ/wNJ1NI5x2bYp3j6zdw58p7PD2k7GyuBzww5em21a2EdI0fU7e34D30ylX9vpEn5PdWOhagtXrCB+Th+E6wN91J/35gj4ZGYwQsRghDp05COYt9deJDu6jOlrNEehqZzC6e50U2iwYxgm4m00jbvia+BzPbVpyGiQW6N4Fz+VtILSTtvBz9XagkOojUODlFW5XuDnKkn83VYT1Ec9gm2a10akXad8H/Ux/sUN8MnMYISIwQgRgxFqu389nvv0CXyh0xccDXp7ctouVrDkULhOpZOWPixjTO65qu0bE4XA9lUE27RH7odrJ0XlmMJQDHxVY30p9eNaE1+krya5QCWVBtFbNFY7j+O2qvRdeGVMu1Winca1kMFxYyvCTiJiNEPVtmcadYwvhzZNOxsoN0V2EqumsSi9t/U2hpBfj9DT67g1FTgWr4YhxJ1JY/+GnTgb2IwVPTEB12c+/lLbR+eeA9/xvnPavlDEsFg30vWLd7GMcdtI17uP+cJr8NCE/xsRgxEiBiPClo0x3PKMGoBvd82kMIjrwvIkXXdcDgeWAMYOLaDvPaqMZp/2lXvDtBbY8Ti46qVS4K6j5/oqw+Z9KBXoS3z/B7jGX3hN2yO9t8B3aulxbe+O3wXfcGxF2/l2XAfz1Ra6p31D4JOZwQgRgxEiBiPCVt0XX022iK+7PzgDrtrhx7RdThnrjmVZfUepVJz+nXboGozOXNH2oMKc/JXPf9H2D48cxqGdvmBc+H5Tnq9OHnAP/8bez8hOv+3b6atRn5citO41GDJ2T9si+AxUqdH6tjaEp2hkZjBCxGiGqu2GlNgMdz6fevSAtr3pi+DbP03bYjNvPQA+J0HlglAJQ486+9e2hr3VAYw7D2PI7hm6o+18EdPu5E9Ufe24hpXZYj+lr4UTVLFef28r7XpGPukGn8wMRogYjBAxmmLN8Ke9RnqpbPR5TnCqmX+Vytb9J+fBl/1qn7arHdhmbqweeALD7fL1Z9xhKO474FagNSuyimWVUIX6rAzjutByldaQ4W/wALNyaWyz73eCzyyrhE7S2rJ+HzhqYScRMZphp2/Dk6znblr43ZDq+p74O76jSmlx/iHwrR6nPuwB3PT3CsYuWQ5T6cgKnioLlSncKAd319wY9RHJYyisdBnnYuvoc4xzwek3esE3+i2lxF4Wq7Y3Y/TUPTB7CXwyMxghYjBCxGiKNeO/sEVl2DyA5k39Db5R40BI4WU8NNc5dVPbTgYPEK++jqc8Vico91Vl/P2NHcxqezaDsT95nlLP3h+x+mpN/anN0IP7wXX9eaPMMYhrXTGDlVoTmRmMEDGa4gn8no1AbWvjJ/PupLZ3ncen6thiEa4Xn6L/dsRXMO/uOresbXeONrruFVc+fBL7u2zYX+OJOpkZjBAxGCFiMGLn1wxBIzODESIGI0QMRogYjBAxGCFiMELEYISIwQgRgxEiBiNEDEaIGIwQMRghYjBCxGCEiMEIEYMRIgYjRAxGiBiMEDEYIWIwQsRghIjBCBGDESIGI0QMiw//AJFTe3XEkqyRAAAAAElFTkSuQmCC\" id=\"imageae93cc9c34\" transform=\"scale(1 -1)translate(0 -71.28)\" x=\"351.816981\" y=\"-112.824163\" width=\"71.28\" height=\"71.28\"/>\n", "   </g>\n", "   <g id=\"patch_68\">\n", "    <path d=\"M 351.816981 184.104163 \n", "L 351.816981 113.038125 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_69\">\n", "    <path d=\"M 422.883019 184.104163 \n", "L 422.883019 113.038125 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_70\">\n", "    <path d=\"M 351.816981 184.104163 \n", "L 422.883019 184.104163 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_71\">\n", "    <path d=\"M 351.816981 113.038125 \n", "L 422.883019 113.038125 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"text_14\">\n", "    <!-- sandal -->\n", "    <g transform=\"translate(367.592188 107.038125)scale(0.12 -0.12)\">\n", "     <use xlink:href=\"#DejaVuSans-73\"/>\n", "     <use xlink:href=\"#DejaVuSans-61\" x=\"52.099609\"/>\n", "     <use xlink:href=\"#DejaVuSans-6e\" x=\"113.378906\"/>\n", "     <use xlink:href=\"#DejaVuSans-64\" x=\"176.757812\"/>\n", "     <use xlink:href=\"#DejaVuSans-61\" x=\"240.234375\"/>\n", "     <use xlink:href=\"#DejaVuSans-6c\" x=\"301.513672\"/>\n", "    </g>\n", "   </g>\n", "  </g>\n", "  <g id=\"axes_15\">\n", "   <g id=\"patch_72\">\n", "    <path d=\"M 437.096226 184.104163 \n", "L 508.162264 184.104163 \n", "L 508.162264 113.038125 \n", "L 437.096226 113.038125 \n", "z\n", "\" style=\"fill: #ffffff\"/>\n", "   </g>\n", "   <g clip-path=\"url(#p7b38b0b165)\">\n", "    <image xlink:href=\"data:image/png;base64,\n", "iVBORw0KGgoAAAANSUhEUgAAAGMAAABjCAYAAACPO76VAAAErElEQVR4nO2cy24cRRSGu3um3Z4LTnwltkMCQnZMQEEiBMJlFbLIKgtExAOwIWyQ2CKRFUJBPAASSxY8APKKbBCChMsCECIKliLkaCIhEo89HsYz7p4uNEGqU6eSHplcyA/83+r0nOmqHn+uU9XVY4fHw1dMQCCI7vcFEIEygKAMICgDCMoAgjKAoAwgKAMIygCCMoCgDCAoAwjKAIIygKAMICgDCMoAgjKAoAwgKAMIygCCMoCgDCAoAwjKAIIygKAMICgDCMoAgjKAoAwgyv4LpalJOZjY7b27JHHWV6kwzWxs3PcNct57FUb+PMRUEn1ep1vYXxAN+T2KQn0cescFOVOKCvswlRF92bF8xjDLC5s3Jd13nsiPPG6s6e6Kr5L801AGEGVVloIg6F+7LgdufJcIE12Kgr6Un6ha1aleT87zSk1YrxV3kknJHGC60k7utPlX8s7/iu7vtOB+CjM5oXIcGUBQBhCUAUTZr+G/n37Oxs2ndO2df2hnc8h6p6KO66NSp4/N/qJy7b70Px7r9mfjdRvviTdUbjRMbxkPOBC3dDvluo17Rr+3k8txNYpV7nIquSvZLpX7NZ22cWr0Ut5lI9Pz4Iv1SzZ+693TKseRAQRlAFFuvPyweuH9Nz+y8evLr6nc2pd7bBxveg1tyQIv8dZ6YVteWJ6eKTyvN+HdKTvtjK/oO/C04t456/OM10xalxecqniDzKmouX/hDv1R77wpp9ylXoe5HCeTWyr10/ycjWfO67LMkQEEZQBBGUCEN/1XnchZpj3zuEq1HpFlWndCe9wekzir6ibjtlvf9QWkY/Le6lVdezePSL01Tb1rWluVhhZOrqjc3qosiQd8/vGRoAh35Vnydkq2ndVsueOdVzOF80m8IZ9jxJtbW4dlJ3rpbFvlODKAoAwgKAP5SV+QO+v5Cz+q1NgFJ3bnlkG9dZ4KmjnZKhjQflSK75Y316QPSLyxpLdfxr+SYtw8pO8zutNSs5/c1VC5lT/0vUxnrvj+IXJuFzoLeqskCOW8bEQ/zev33Kee3n3OvPM5Et3mwSm5t8iv6omIIwMIygAiPB6d0mM4dPyY/K4/FfMpPbZg48Z7umqWIum/2dC7pvG6lImkqctEvKmv05xo2rjV0jvKeVf6HPlN95+shbcsZzfaWZSyWdur16/ta/IU8pOXPlS5LzqLNj73hFOjOTKwoAwgKAOI8k3zgBnyhbNhDPui2JC5pn9RtjJmX9X72/2jB23cOql/b/J9sq2QL26rXKWmt62vfy1b/2a3ngf3nZPj0U/PBzvlQSfOjh1WuRMffGfj/WV9LW9cftrG04E89RvAkQEEZUDv2t7zHv2nebfXfXRoycarZ/RuQHdVLxlfOPqzjb/5TO9E739n56Xpdrjy9vPqeOZ7WSMny9+qHEcGEJQBBGX8r+eM+zAvtU89a+OxS/rLcPkPFwvPuxfbP8PgyACCMoD4b5apfykcGUBQBhCUAQRlAEEZQFAGEJQBBGUAQRlAUAYQlAEEZQBBGUBQBhCUAQRlAEEZQFAGEJQBBGUAQRlAUAYQlAEEZQBBGUBQBhCUAQRlAEEZQFAGEJQR4PAnizcax/lGjiIAAAAASUVORK5CYII=\" id=\"image4ee9bee684\" transform=\"scale(1 -1)translate(0 -71.28)\" x=\"437.096226\" y=\"-112.824163\" width=\"71.28\" height=\"71.28\"/>\n", "   </g>\n", "   <g id=\"patch_73\">\n", "    <path d=\"M 437.096226 184.104163 \n", "L 437.096226 113.038125 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_74\">\n", "    <path d=\"M 508.162264 184.104163 \n", "L 508.162264 113.038125 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_75\">\n", "    <path d=\"M 437.096226 184.104163 \n", "L 508.162264 184.104163 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_76\">\n", "    <path d=\"M 437.096226 113.038125 \n", "L 508.162264 113.038125 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"text_15\">\n", "    <!-- sneaker -->\n", "    <g transform=\"translate(448.917058 107.038125)scale(0.12 -0.12)\">\n", "     <use xlink:href=\"#DejaVuSans-73\"/>\n", "     <use xlink:href=\"#DejaVuSans-6e\" x=\"52.099609\"/>\n", "     <use xlink:href=\"#DejaVuSans-65\" x=\"115.478516\"/>\n", "     <use xlink:href=\"#DejaVuSans-61\" x=\"177.001953\"/>\n", "     <use xlink:href=\"#DejaVuSans-6b\" x=\"238.28125\"/>\n", "     <use xlink:href=\"#DejaVuSans-65\" x=\"292.566406\"/>\n", "     <use xlink:href=\"#DejaVuSans-72\" x=\"354.089844\"/>\n", "    </g>\n", "   </g>\n", "  </g>\n", "  <g id=\"axes_16\">\n", "   <g id=\"patch_77\">\n", "    <path d=\"M 522.**********.104163 \n", "L 593.**********.104163 \n", "L 593.**********.038125 \n", "L 522.**********.038125 \n", "z\n", "\" style=\"fill: #ffffff\"/>\n", "   </g>\n", "   <g clip-path=\"url(#pd6923dde0e)\">\n", "    <image xlink:href=\"data:image/png;base64,\n", "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\" id=\"imagec6a7c27639\" transform=\"scale(1 -1)translate(0 -71.28)\" x=\"522.375472\" y=\"-112.824163\" width=\"71.28\" height=\"71.28\"/>\n", "   </g>\n", "   <g id=\"patch_78\">\n", "    <path d=\"M 522.**********.104163 \n", "L 522.**********.038125 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_79\">\n", "    <path d=\"M 593.**********.104163 \n", "L 593.**********.038125 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_80\">\n", "    <path d=\"M 522.**********.104163 \n", "L 593.**********.104163 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_81\">\n", "    <path d=\"M 522.**********.038125 \n", "L 593.**********.038125 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"text_16\">\n", "    <!-- ankle boot -->\n", "    <g transform=\"translate(526.185366 107.038125)scale(0.12 -0.12)\">\n", "     <use xlink:href=\"#DejaVuSans-61\"/>\n", "     <use xlink:href=\"#DejaVuSans-6e\" x=\"61.279297\"/>\n", "     <use xlink:href=\"#DejaVuSans-6b\" x=\"124.658203\"/>\n", "     <use xlink:href=\"#DejaVuSans-6c\" x=\"182.568359\"/>\n", "     <use xlink:href=\"#DejaVuSans-65\" x=\"210.351562\"/>\n", "     <use xlink:href=\"#DejaVuSans-20\" x=\"271.875\"/>\n", "     <use xlink:href=\"#DejaVuSans-62\" x=\"303.662109\"/>\n", "     <use xlink:href=\"#DejaVuSans-6f\" x=\"367.138672\"/>\n", "     <use xlink:href=\"#DejaVuSans-6f\" x=\"428.320312\"/>\n", "     <use xlink:href=\"#DejaVuSans-74\" x=\"489.501953\"/>\n", "    </g>\n", "   </g>\n", "  </g>\n", "  <g id=\"axes_17\">\n", "   <g id=\"patch_82\">\n", "    <path d=\"M 607.654717 184.104163 \n", "L 678.720755 184.104163 \n", "L 678.720755 113.038125 \n", "L 607.654717 113.038125 \n", "z\n", "\" style=\"fill: #ffffff\"/>\n", "   </g>\n", "   <g clip-path=\"url(#p8d2a1c0565)\">\n", "    <image xlink:href=\"data:image/png;base64,\n", "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\" id=\"image9b43d946e5\" transform=\"scale(1 -1)translate(0 -71.28)\" x=\"607.654717\" y=\"-112.824163\" width=\"71.28\" height=\"71.28\"/>\n", "   </g>\n", "   <g id=\"patch_83\">\n", "    <path d=\"M 607.654717 184.104163 \n", "L 607.654717 113.038125 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_84\">\n", "    <path d=\"M 678.720755 184.104163 \n", "L 678.720755 113.038125 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_85\">\n", "    <path d=\"M 607.654717 184.104163 \n", "L 678.720755 184.104163 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_86\">\n", "    <path d=\"M 607.654717 113.038125 \n", "L 678.720755 113.038125 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"text_17\">\n", "    <!-- trouser -->\n", "    <g transform=\"translate(621.746173 107.038125)scale(0.12 -0.12)\">\n", "     <use xlink:href=\"#DejaVuSans-74\"/>\n", "     <use xlink:href=\"#DejaVuSans-72\" x=\"39.208984\"/>\n", "     <use xlink:href=\"#DejaVuSans-6f\" x=\"78.072266\"/>\n", "     <use xlink:href=\"#DejaVuSans-75\" x=\"139.253906\"/>\n", "     <use xlink:href=\"#DejaVuSans-73\" x=\"202.632812\"/>\n", "     <use xlink:href=\"#DejaVuSans-65\" x=\"254.732422\"/>\n", "     <use xlink:href=\"#DejaVuSans-72\" x=\"316.255859\"/>\n", "    </g>\n", "   </g>\n", "  </g>\n", "  <g id=\"axes_18\">\n", "   <g id=\"patch_87\">\n", "    <path d=\"M 692.933962 184.104163 \n", "L 764 184.104163 \n", "L 764 113.038125 \n", "L 692.933962 113.038125 \n", "z\n", "\" style=\"fill: #ffffff\"/>\n", "   </g>\n", "   <g clip-path=\"url(#p1f6381c53f)\">\n", "    <image xlink:href=\"data:image/png;base64,\n", "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\" id=\"image571f0817fc\" transform=\"scale(1 -1)translate(0 -71.28)\" x=\"692.933962\" y=\"-112.824163\" width=\"71.28\" height=\"71.28\"/>\n", "   </g>\n", "   <g id=\"patch_88\">\n", "    <path d=\"M 692.933962 184.104163 \n", "L 692.933962 113.038125 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_89\">\n", "    <path d=\"M 764 184.104163 \n", "L 764 113.038125 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_90\">\n", "    <path d=\"M 692.933962 184.104163 \n", "L 764 184.104163 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_91\">\n", "    <path d=\"M 692.933962 113.038125 \n", "L 764 113.038125 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"text_18\">\n", "    <!-- t-shirt -->\n", "    <g transform=\"translate(710.536356 107.038125)scale(0.12 -0.12)\">\n", "     <use xlink:href=\"#DejaVuSans-74\"/>\n", "     <use xlink:href=\"#DejaVuSans-2d\" x=\"39.208984\"/>\n", "     <use xlink:href=\"#DejaVuSans-73\" x=\"75.292969\"/>\n", "     <use xlink:href=\"#DejaVuSans-68\" x=\"127.392578\"/>\n", "     <use xlink:href=\"#DejaVuSans-69\" x=\"190.771484\"/>\n", "     <use xlink:href=\"#DejaVuSans-72\" x=\"218.554688\"/>\n", "     <use xlink:href=\"#DejaVuSans-74\" x=\"259.667969\"/>\n", "    </g>\n", "   </g>\n", "  </g>\n", " </g>\n", " <defs>\n", "  <clipPath id=\"pd2b10f666d\">\n", "   <rect x=\"10.7\" y=\"22.318125\" width=\"71.066038\" height=\"71.066038\"/>\n", "  </clipPath>\n", "  <clipPath id=\"p276b300061\">\n", "   <rect x=\"95.979245\" y=\"22.318125\" width=\"71.066038\" height=\"71.066038\"/>\n", "  </clipPath>\n", "  <clipPath id=\"p79ac6c70b7\">\n", "   <rect x=\"181.258491\" y=\"22.318125\" width=\"71.066038\" height=\"71.066038\"/>\n", "  </clipPath>\n", "  <clipPath id=\"pec1b5d1fac\">\n", "   <rect x=\"266.537736\" y=\"22.318125\" width=\"71.066038\" height=\"71.066038\"/>\n", "  </clipPath>\n", "  <clipPath id=\"p32082b3170\">\n", "   <rect x=\"351.816981\" y=\"22.318125\" width=\"71.066038\" height=\"71.066038\"/>\n", "  </clipPath>\n", "  <clipPath id=\"p288f6b9b2b\">\n", "   <rect x=\"437.096226\" y=\"22.318125\" width=\"71.066038\" height=\"71.066038\"/>\n", "  </clipPath>\n", "  <clipPath id=\"p8d92a856f0\">\n", "   <rect x=\"522.375472\" y=\"22.318125\" width=\"71.066038\" height=\"71.066038\"/>\n", "  </clipPath>\n", "  <clipPath id=\"pfce0c330d5\">\n", "   <rect x=\"607.654717\" y=\"22.318125\" width=\"71.066038\" height=\"71.066038\"/>\n", "  </clipPath>\n", "  <clipPath id=\"p90efdf36f1\">\n", "   <rect x=\"692.933962\" y=\"22.318125\" width=\"71.066038\" height=\"71.066038\"/>\n", "  </clipPath>\n", "  <clipPath id=\"p91ba52fd33\">\n", "   <rect x=\"10.7\" y=\"113.038125\" width=\"71.066038\" height=\"71.066038\"/>\n", "  </clipPath>\n", "  <clipPath id=\"p26998eb952\">\n", "   <rect x=\"95.979245\" y=\"113.038125\" width=\"71.066038\" height=\"71.066038\"/>\n", "  </clipPath>\n", "  <clipPath id=\"p92945399b7\">\n", "   <rect x=\"181.258491\" y=\"113.038125\" width=\"71.066038\" height=\"71.066038\"/>\n", "  </clipPath>\n", "  <clipPath id=\"pfac1befe5b\">\n", "   <rect x=\"266.537736\" y=\"113.038125\" width=\"71.066038\" height=\"71.066038\"/>\n", "  </clipPath>\n", "  <clipPath id=\"p79c2abd050\">\n", "   <rect x=\"351.816981\" y=\"113.038125\" width=\"71.066038\" height=\"71.066038\"/>\n", "  </clipPath>\n", "  <clipPath id=\"p7b38b0b165\">\n", "   <rect x=\"437.096226\" y=\"113.038125\" width=\"71.066038\" height=\"71.066038\"/>\n", "  </clipPath>\n", "  <clipPath id=\"pd6923dde0e\">\n", "   <rect x=\"522.375472\" y=\"113.038125\" width=\"71.066038\" height=\"71.066038\"/>\n", "  </clipPath>\n", "  <clipPath id=\"p8d2a1c0565\">\n", "   <rect x=\"607.654717\" y=\"113.038125\" width=\"71.066038\" height=\"71.066038\"/>\n", "  </clipPath>\n", "  <clipPath id=\"p1f6381c53f\">\n", "   <rect x=\"692.933962\" y=\"113.038125\" width=\"71.066038\" height=\"71.066038\"/>\n", "  </clipPath>\n", " </defs>\n", "</svg>\n"], "text/plain": ["<Figure size 1350x300 with 18 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["X, y = next(iter(data.DataLoader(mnist_train, batch_size=18)))\n", "show_images(<PERSON><PERSON>reshape(18, 28, 28), 2, 9, titles=get_fashion_mnist_labels(y));"]}, {"cell_type": "markdown", "id": "24f7d468", "metadata": {"origin_pos": 26}, "source": ["## 读取小批量\n", "\n", "为了使我们在读取训练集和测试集时更容易，我们使用内置的数据迭代器，而不是从零开始创建。\n", "回顾一下，在每次迭代中，数据加载器每次都会[**读取一小批量数据，大小为`batch_size`**]。\n", "通过内置数据迭代器，我们可以随机打乱了所有样本，从而无偏见地读取小批量。\n"]}, {"cell_type": "code", "execution_count": 8, "id": "9ab9799b", "metadata": {"execution": {"iopub.execute_input": "2022-12-07T16:28:15.849341Z", "iopub.status.busy": "2022-12-07T16:28:15.848807Z", "iopub.status.idle": "2022-12-07T16:28:15.853250Z", "shell.execute_reply": "2022-12-07T16:28:15.852526Z"}, "origin_pos": 28, "tab": ["pytorch"]}, "outputs": [], "source": ["batch_size = 256\n", "\n", "def get_dataloader_workers():  #@save\n", "    \"\"\"使用4个进程来读取数据\"\"\"\n", "    return 4\n", "\n", "train_iter = data.DataLoader(mnist_train, batch_size, shuffle=True,\n", "                             num_workers=get_dataloader_workers())"]}, {"cell_type": "markdown", "id": "25fb080e", "metadata": {"origin_pos": 31}, "source": ["我们看一下读取训练数据所需的时间。\n"]}, {"cell_type": "code", "execution_count": 9, "id": "36217872", "metadata": {"execution": {"iopub.execute_input": "2022-12-07T16:28:15.856347Z", "iopub.status.busy": "2022-12-07T16:28:15.855959Z", "iopub.status.idle": "2022-12-07T16:28:18.047614Z", "shell.execute_reply": "2022-12-07T16:28:18.046405Z"}, "origin_pos": 32, "tab": ["pytorch"]}, "outputs": [{"data": {"text/plain": ["'2.37 sec'"]}, "execution_count": 9, "metadata": {}, "output_type": "execute_result"}], "source": ["timer = d2l.Timer()\n", "for X, y in train_iter:\n", "    continue\n", "f'{timer.stop():.2f} sec'"]}, {"cell_type": "markdown", "id": "37804bdf", "metadata": {"origin_pos": 33}, "source": ["## 整合所有组件\n", "\n", "现在我们[**定义`load_data_fashion_mnist`函数**]，用于获取和读取Fashion-MNIST数据集。\n", "这个函数返回训练集和验证集的数据迭代器。\n", "此外，这个函数还接受一个可选参数`resize`，用来将图像大小调整为另一种形状。\n"]}, {"cell_type": "code", "execution_count": 10, "id": "435324a5", "metadata": {"execution": {"iopub.execute_input": "2022-12-07T16:28:18.051337Z", "iopub.status.busy": "2022-12-07T16:28:18.050842Z", "iopub.status.idle": "2022-12-07T16:28:18.058980Z", "shell.execute_reply": "2022-12-07T16:28:18.057872Z"}, "origin_pos": 35, "tab": ["pytorch"]}, "outputs": [], "source": ["def load_data_fashion_mnist(batch_size, resize=None):  #@save\n", "    \"\"\"下载Fashion-MNIST数据集，然后将其加载到内存中\"\"\"\n", "    trans = [transforms.ToTensor()]\n", "    if resize:\n", "        trans.insert(0, transforms.Resize(resize))\n", "    trans = transforms.Compose(trans)\n", "    mnist_train = torchvision.datasets.FashionMNIST(\n", "        root=\"../data\", train=True, transform=trans, download=True)\n", "    mnist_test = torchvision.datasets.FashionMNIST(\n", "        root=\"../data\", train=False, transform=trans, download=True)\n", "    return (data.DataLoader(mnist_train, batch_size, shuffle=True,\n", "                            num_workers=get_dataloader_workers()),\n", "            data.DataLoader(mnist_test, batch_size, shuffle=False,\n", "                            num_workers=get_dataloader_workers()))"]}, {"cell_type": "markdown", "id": "4f3edbdd", "metadata": {"origin_pos": 38}, "source": ["下面，我们通过指定`resize`参数来测试`load_data_fashion_mnist`函数的图像大小调整功能。\n"]}, {"cell_type": "code", "execution_count": 11, "id": "8279d5b1", "metadata": {"execution": {"iopub.execute_input": "2022-12-07T16:28:18.062379Z", "iopub.status.busy": "2022-12-07T16:28:18.061689Z", "iopub.status.idle": "2022-12-07T16:28:18.227129Z", "shell.execute_reply": "2022-12-07T16:28:18.226152Z"}, "origin_pos": 39, "tab": ["pytorch"]}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["torch.<PERSON><PERSON>([32, 1, 64, 64]) torch.float32 torch.<PERSON><PERSON>([32]) torch.int64\n"]}], "source": ["train_iter, test_iter = load_data_fashion_mnist(32, resize=64)\n", "for X, y in train_iter:\n", "    print(X.shape, X.dtype, y.shape, y.dtype)\n", "    break"]}, {"cell_type": "markdown", "id": "189494c5", "metadata": {"origin_pos": 40}, "source": ["我们现在已经准备好使用Fashion-MNIST数据集，便于下面的章节调用来评估各种分类算法。\n", "\n", "## 小结\n", "\n", "* Fashion-MNIST是一个服装分类数据集，由10个类别的图像组成。我们将在后续章节中使用此数据集来评估各种分类算法。\n", "* 我们将高度$h$像素，宽度$w$像素图像的形状记为$h \\times w$或（$h$,$w$）。\n", "* 数据迭代器是获得更高性能的关键组件。依靠实现良好的数据迭代器，利用高性能计算来避免减慢训练过程。\n", "\n", "## 练习\n", "\n", "1. 减少`batch_size`（如减少到1）是否会影响读取性能？\n", "1. 数据迭代器的性能非常重要。当前的实现足够快吗？探索各种选择来改进它。\n", "1. 查阅框架的在线API文档。还有哪些其他数据集可用？\n"]}, {"cell_type": "markdown", "id": "8d6c34eb", "metadata": {"origin_pos": 42, "tab": ["pytorch"]}, "source": ["[Discussions](https://discuss.d2l.ai/t/1787)\n"]}], "metadata": {"kernelspec": {"display_name": "d2l", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.23"}}, "nbformat": 4, "nbformat_minor": 5}