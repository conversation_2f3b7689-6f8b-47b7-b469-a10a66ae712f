<?xml version="1.0" encoding="UTF-8"?>
<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="200pt" height="226pt" viewBox="0 0 200 226" version="1.1">
<defs>
<g>
<symbol overflow="visible" id="glyph0-0">
<path style="stroke:none;" d=""/>
</symbol>
<symbol overflow="visible" id="glyph0-1">
<path style="stroke:none;" d="M 6.828125 0 L 6.828125 -0.21875 C 6.1875 -0.3125 6.03125 -0.40625 6.03125 -0.875 L 6.03125 -5.1875 C 6.03125 -5.671875 6.21875 -5.8125 6.828125 -5.859375 L 6.828125 -6.078125 L 3.78125 -6.078125 L 3.78125 -5.859375 C 4.40625 -5.8125 4.578125 -5.703125 4.578125 -5.1875 L 4.578125 -3.359375 L 2.40625 -3.359375 L 2.40625 -5.1875 C 2.40625 -5.703125 2.578125 -5.8125 3.21875 -5.859375 L 3.21875 -6.078125 L 0.1875 -6.078125 L 0.1875 -5.859375 C 0.796875 -5.8125 0.953125 -5.6875 0.953125 -5.1875 L 0.953125 -0.875 C 0.953125 -0.40625 0.8125 -0.3125 0.1875 -0.21875 L 0.1875 0 L 3.21875 0 L 3.21875 -0.21875 C 2.5625 -0.296875 2.40625 -0.40625 2.40625 -0.875 L 2.40625 -2.9375 L 4.578125 -2.9375 L 4.578125 -0.875 C 4.578125 -0.421875 4.4375 -0.296875 3.78125 -0.21875 L 3.78125 0 Z M 6.828125 0 "/>
</symbol>
<symbol overflow="visible" id="glyph0-2">
<path style="stroke:none;" d="M 6.296875 0 L 6.296875 -0.21875 C 5.984375 -0.234375 5.84375 -0.34375 5.421875 -1 L 3.78125 -3.546875 L 4.5 -4.546875 C 5.28125 -5.640625 5.515625 -5.796875 6.1875 -5.859375 L 6.1875 -6.078125 L 3.9375 -6.078125 L 3.9375 -5.859375 L 4.125 -5.84375 C 4.46875 -5.8125 4.59375 -5.734375 4.59375 -5.53125 C 4.59375 -5.328125 4.484375 -5.15625 4.0625 -4.578125 L 3.5625 -3.875 L 2.6875 -5.234375 C 2.578125 -5.40625 2.5625 -5.453125 2.5625 -5.5625 C 2.5625 -5.75 2.65625 -5.828125 2.984375 -5.84375 L 3.265625 -5.859375 L 3.265625 -6.078125 L 0.15625 -6.078125 L 0.15625 -5.859375 C 0.484375 -5.828125 0.59375 -5.734375 0.875 -5.34375 L 2.65625 -2.71875 L 1.078125 -0.734375 C 0.8125 -0.390625 0.59375 -0.28125 0.140625 -0.21875 L 0.140625 0 L 2.390625 0 L 2.390625 -0.21875 C 1.84375 -0.28125 1.671875 -0.375 1.671875 -0.609375 C 1.671875 -0.796875 1.84375 -1.078125 2.515625 -1.984375 L 2.859375 -2.4375 L 3.765625 -0.984375 C 3.875 -0.8125 3.953125 -0.609375 3.953125 -0.5 C 3.953125 -0.34375 3.796875 -0.265625 3.484375 -0.25 L 3.234375 -0.21875 L 3.234375 0 Z M 6.296875 0 "/>
</symbol>
<symbol overflow="visible" id="glyph0-3">
<path style="stroke:none;" d="M 6.6875 -3.015625 C 6.6875 -4.875 5.359375 -6.21875 3.546875 -6.21875 C 1.671875 -6.21875 0.3125 -4.921875 0.3125 -3 C 0.3125 -1.140625 1.625 0.171875 3.484375 0.171875 C 5.359375 0.171875 6.6875 -1.140625 6.6875 -3.015625 Z M 5.09375 -2.953125 C 5.09375 -1.09375 4.546875 -0.125 3.515625 -0.125 C 2.484375 -0.125 1.90625 -1.078125 1.90625 -2.96875 C 1.90625 -4.859375 2.46875 -5.921875 3.46875 -5.921875 C 4.515625 -5.921875 5.09375 -4.875 5.09375 -2.953125 Z M 5.09375 -2.953125 "/>
</symbol>
<symbol overflow="visible" id="glyph1-0">
<path style="stroke:none;" d=""/>
</symbol>
<symbol overflow="visible" id="glyph1-1">
<path style="stroke:none;" d="M 3.796875 -3.921875 L 0.609375 -3.921875 L 0.359375 -2.984375 L 0.46875 -2.96875 C 0.75 -3.609375 0.9375 -3.703125 1.890625 -3.703125 L 1.03125 -0.546875 C 0.9375 -0.21875 0.796875 -0.125 0.390625 -0.09375 L 0.390625 0 L 2.125 0 L 2.125 -0.09375 L 1.90625 -0.109375 C 1.65625 -0.140625 1.59375 -0.1875 1.59375 -0.375 C 1.59375 -0.5 1.640625 -0.640625 1.671875 -0.765625 L 2.5 -3.703125 L 2.84375 -3.703125 C 3.234375 -3.703125 3.453125 -3.546875 3.453125 -3.21875 C 3.453125 -3.140625 3.4375 -3.046875 3.4375 -2.953125 L 3.53125 -2.9375 Z M 3.796875 -3.921875 "/>
</symbol>
<symbol overflow="visible" id="glyph1-2">
<path style="stroke:none;" d="M 3.359375 -1.078125 L 3.234375 -1.109375 C 3.0625 -0.765625 2.9375 -0.5625 2.734375 -0.4375 C 2.453125 -0.25 2.265625 -0.21875 1.65625 -0.21875 C 1.171875 -0.21875 1.03125 -0.28125 1.03125 -0.46875 C 1.03125 -0.546875 1.0625 -0.640625 1.09375 -0.78125 L 1.8125 -3.359375 C 1.90625 -3.703125 2.0625 -3.796875 2.421875 -3.828125 L 2.421875 -3.921875 L 0.78125 -3.921875 L 0.78125 -3.828125 C 1.15625 -3.796875 1.234375 -3.734375 1.234375 -3.5625 C 1.234375 -3.46875 1.21875 -3.359375 1.171875 -3.1875 L 0.4375 -0.5625 C 0.34375 -0.1875 0.28125 -0.125 -0.046875 -0.09375 L -0.046875 0 L 3 0 Z M 3.359375 -1.078125 "/>
</symbol>
<symbol overflow="visible" id="glyph2-0">
<path style="stroke:none;" d=""/>
</symbol>
<symbol overflow="visible" id="glyph2-1">
<path style="stroke:none;" d="M 1.828125 0.96875 C 0.984375 0.265625 0.796875 -0.40625 0.796875 -1.53125 C 0.796875 -2.6875 0.984375 -3.25 1.828125 -3.953125 L 1.765625 -4.0625 C 0.8125 -3.484375 0.28125 -2.625 0.28125 -1.515625 C 0.28125 -0.484375 0.796875 0.515625 1.75 1.0625 Z M 1.828125 0.96875 "/>
</symbol>
<symbol overflow="visible" id="glyph2-2">
<path style="stroke:none;" d="M 2.84375 -0.828125 L 2.765625 -0.859375 C 2.5625 -0.515625 2.4375 -0.453125 2.1875 -0.453125 L 0.78125 -0.453125 L 1.765625 -1.515625 C 2.296875 -2.078125 2.53125 -2.53125 2.53125 -3 C 2.53125 -3.59375 2.109375 -4.0625 1.421875 -4.0625 C 0.6875 -4.0625 0.3125 -3.5625 0.1875 -2.859375 L 0.3125 -2.828125 C 0.546875 -3.421875 0.75 -3.609375 1.1875 -3.609375 C 1.703125 -3.609375 2.015625 -3.3125 2.015625 -2.765625 C 2.015625 -2.25 1.8125 -1.796875 1.234375 -1.203125 L 0.171875 -0.078125 L 0.171875 0 L 2.515625 0 Z M 2.84375 -0.828125 "/>
</symbol>
<symbol overflow="visible" id="glyph2-3">
<path style="stroke:none;" d="M 0.171875 -3.953125 C 1.046875 -3.28125 1.1875 -2.59375 1.1875 -1.46875 C 1.1875 -0.296875 1.03125 0.265625 0.171875 0.96875 L 0.234375 1.0625 C 1.1875 0.484375 1.703125 -0.375 1.703125 -1.484375 C 1.703125 -2.5 1.15625 -3.5 0.25 -4.0625 Z M 0.171875 -3.953125 "/>
</symbol>
<symbol overflow="visible" id="glyph2-4">
<path style="stroke:none;" d="M 2.359375 0 L 2.359375 -0.09375 C 1.90625 -0.09375 1.796875 -0.203125 1.796875 -0.453125 L 1.796875 -4.03125 L 1.734375 -4.0625 L 0.671875 -3.515625 L 0.671875 -3.421875 L 0.828125 -3.484375 C 0.9375 -3.53125 1.03125 -3.5625 1.09375 -3.5625 C 1.21875 -3.5625 1.28125 -3.46875 1.28125 -3.265625 L 1.28125 -0.5625 C 1.28125 -0.234375 1.15625 -0.109375 0.703125 -0.09375 L 0.703125 0 Z M 2.359375 0 "/>
</symbol>
<symbol overflow="visible" id="glyph2-5">
<path style="stroke:none;" d="M 0.359375 -3.0625 C 0.609375 -3.5 0.890625 -3.703125 1.265625 -3.703125 C 1.65625 -3.703125 1.90625 -3.484375 1.90625 -3.078125 C 1.90625 -2.71875 1.71875 -2.453125 1.421875 -2.28125 C 1.296875 -2.203125 1.140625 -2.140625 0.921875 -2.0625 L 0.921875 -1.984375 C 1.265625 -1.984375 1.390625 -1.96875 1.53125 -1.921875 C 1.9375 -1.796875 2.15625 -1.5 2.15625 -1.046875 C 2.15625 -0.546875 1.8125 -0.125 1.375 -0.125 C 1.203125 -0.125 1.078125 -0.15625 0.859375 -0.3125 C 0.6875 -0.4375 0.59375 -0.46875 0.484375 -0.46875 C 0.359375 -0.46875 0.25 -0.390625 0.25 -0.265625 C 0.25 -0.046875 0.484375 0.078125 0.921875 0.078125 C 1.453125 0.078125 2.015625 -0.09375 2.3125 -0.46875 C 2.484375 -0.703125 2.59375 -1 2.59375 -1.3125 C 2.59375 -1.625 2.484375 -1.90625 2.328125 -2.09375 C 2.203125 -2.21875 2.09375 -2.296875 1.828125 -2.40625 C 2.21875 -2.640625 2.375 -2.953125 2.375 -3.234375 C 2.375 -3.71875 2 -4.0625 1.453125 -4.0625 C 0.828125 -4.0625 0.4375 -3.65625 0.265625 -3.078125 Z M 0.359375 -3.0625 "/>
</symbol>
<symbol overflow="visible" id="glyph3-0">
<path style="stroke:none;" d=""/>
</symbol>
<symbol overflow="visible" id="glyph3-1">
<path style="stroke:none;" d="M 7.625 -0.4375 C 7.625 -0.734375 7.359375 -1 7.0625 -1 C 6.765625 -1 6.515625 -0.75 6.515625 -0.4375 C 6.515625 -0.140625 6.765625 0.109375 7.046875 0.109375 C 7.359375 0.109375 7.625 -0.125 7.625 -0.4375 Z M 4.65625 -0.4375 C 4.65625 -0.734375 4.390625 -1 4.09375 -1 C 3.796875 -1 3.546875 -0.75 3.546875 -0.4375 C 3.546875 -0.140625 3.796875 0.109375 4.0625 0.109375 C 4.390625 0.109375 4.65625 -0.125 4.65625 -0.4375 Z M 1.6875 -0.4375 C 1.6875 -0.734375 1.421875 -1 1.125 -1 C 0.8125 -1 0.5625 -0.75 0.5625 -0.4375 C 0.5625 -0.140625 0.8125 0.109375 1.09375 0.109375 C 1.421875 0.109375 1.6875 -0.125 1.6875 -0.4375 Z M 1.6875 -0.4375 "/>
</symbol>
</g>
</defs>
<g id="surface1">
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 229.605469 646.292969 L 253.605469 646.292969 L 253.605469 670.292969 L 229.605469 670.292969 Z M 229.605469 646.292969 " transform="matrix(1,0,0,1,-55,-534)"/>
<g style="fill:rgb(0%,0%,0%);fill-opacity:1;">
  <use xlink:href="#glyph0-1" x="179.6036" y="128.2912"/>
</g>
<g style="fill:rgb(0%,0%,0%);fill-opacity:1;">
  <use xlink:href="#glyph1-1" x="186.6056" y="131.2912"/>
</g>
<g style="fill:rgb(0%,0%,0%);fill-opacity:1;">
  <use xlink:href="#glyph2-1" x="187.8287" y="121.8876"/>
  <use xlink:href="#glyph2-2" x="189.8267" y="121.8876"/>
  <use xlink:href="#glyph2-3" x="192.8267" y="121.8876"/>
</g>
<path style="fill-rule:nonzero;fill:rgb(69.804382%,85.098267%,100%);fill-opacity:1;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 229.605469 734.859375 L 253.605469 734.859375 L 253.605469 758.859375 L 229.605469 758.859375 Z M 229.605469 734.859375 " transform="matrix(1,0,0,1,-55,-534)"/>
<g style="fill:rgb(0%,0%,0%);fill-opacity:1;">
  <use xlink:href="#glyph0-2" x="181.6866" y="214.3599"/>
</g>
<g style="fill:rgb(0%,0%,0%);fill-opacity:1;">
  <use xlink:href="#glyph1-1" x="188.1846" y="217.3599"/>
</g>
<path style="fill-rule:nonzero;fill:rgb(69.804382%,85.098267%,100%);fill-opacity:1;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 100.804688 734.859375 L 124.804688 734.859375 L 124.804688 758.859375 L 100.804688 758.859375 Z M 100.804688 734.859375 " transform="matrix(1,0,0,1,-55,-534)"/>
<g style="fill:rgb(0%,0%,0%);fill-opacity:1;">
  <use xlink:href="#glyph0-2" x="53.0546" y="214.3599"/>
</g>
<g style="fill:rgb(0%,0%,0%);fill-opacity:1;">
  <use xlink:href="#glyph2-2" x="59.5526" y="217.3599"/>
</g>
<path style="fill-rule:nonzero;fill:rgb(69.804382%,85.098267%,100%);fill-opacity:1;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 55.804688 734.859375 L 79.804688 734.859375 L 79.804688 758.859375 L 55.804688 758.859375 Z M 55.804688 734.859375 " transform="matrix(1,0,0,1,-55,-534)"/>
<g style="fill:rgb(0%,0%,0%);fill-opacity:1;">
  <use xlink:href="#glyph0-2" x="8.05461" y="214.3599"/>
</g>
<g style="fill:rgb(0%,0%,0%);fill-opacity:1;">
  <use xlink:href="#glyph2-4" x="14.55261" y="217.3599"/>
</g>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 55.804688 646.292969 L 79.804688 646.292969 L 79.804688 670.292969 L 55.804688 670.292969 Z M 55.804688 646.292969 " transform="matrix(1,0,0,1,-55,-534)"/>
<g style="fill:rgb(0%,0%,0%);fill-opacity:1;">
  <use xlink:href="#glyph0-1" x="5.80361" y="128.2912"/>
</g>
<g style="fill:rgb(0%,0%,0%);fill-opacity:1;">
  <use xlink:href="#glyph2-4" x="12.80561" y="131.2912"/>
</g>
<g style="fill:rgb(0%,0%,0%);fill-opacity:1;">
  <use xlink:href="#glyph2-1" x="14.0287" y="121.8876"/>
  <use xlink:href="#glyph2-2" x="16.0267" y="121.8876"/>
  <use xlink:href="#glyph2-3" x="19.0267" y="121.8876"/>
</g>
<g style="fill:rgb(0%,0%,0%);fill-opacity:1;">
  <use xlink:href="#glyph3-1" x="142.6036" y="171.5755"/>
</g>
<g style="fill:rgb(0%,0%,0%);fill-opacity:1;">
  <use xlink:href="#glyph3-1" x="145.1586" y="127.2912"/>
</g>
<g style="fill:rgb(0%,0%,0%);fill-opacity:1;">
  <use xlink:href="#glyph3-1" x="145.1586" y="60.565"/>
</g>
<path style="fill-rule:nonzero;fill:rgb(69.804382%,85.098267%,100%);fill-opacity:1;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 229.605469 535 L 253.605469 535 L 253.605469 559 L 229.605469 559 Z M 229.605469 535 " transform="matrix(1,0,0,1,-55,-534)"/>
<g style="fill:rgb(0%,0%,0%);fill-opacity:1;">
  <use xlink:href="#glyph0-3" x="181.4346" y="14.5"/>
</g>
<g style="fill:rgb(0%,0%,0%);fill-opacity:1;">
  <use xlink:href="#glyph1-1" x="188.4366" y="17.5"/>
</g>
<path style="fill-rule:nonzero;fill:rgb(69.804382%,85.098267%,100%);fill-opacity:1;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 100.804688 535 L 124.804688 535 L 124.804688 559 L 100.804688 559 Z M 100.804688 535 " transform="matrix(1,0,0,1,-55,-534)"/>
<g style="fill:rgb(0%,0%,0%);fill-opacity:1;">
  <use xlink:href="#glyph0-3" x="52.8026" y="14.5"/>
</g>
<g style="fill:rgb(0%,0%,0%);fill-opacity:1;">
  <use xlink:href="#glyph2-2" x="59.8046" y="17.5"/>
</g>
<path style="fill-rule:nonzero;fill:rgb(69.804382%,85.098267%,100%);fill-opacity:1;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 55.804688 535 L 79.804688 535 L 79.804688 559 L 55.804688 559 Z M 55.804688 535 " transform="matrix(1,0,0,1,-55,-534)"/>
<g style="fill:rgb(0%,0%,0%);fill-opacity:1;">
  <use xlink:href="#glyph0-3" x="7.80261" y="14.5"/>
</g>
<g style="fill:rgb(0%,0%,0%);fill-opacity:1;">
  <use xlink:href="#glyph2-4" x="14.80461" y="17.5"/>
</g>
<g style="fill:rgb(0%,0%,0%);fill-opacity:1;">
  <use xlink:href="#glyph3-1" x="8.35861" y="93.2293"/>
</g>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 112.804688 734.859375 L 112.804688 720.476562 " transform="matrix(1,0,0,1,-55,-534)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:butt;stroke-linejoin:miter;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 112.804688 716.476562 L 112.804688 720.476562 M 111.304688 720.476562 L 112.804688 716.476562 L 114.304688 720.476562 " transform="matrix(1,0,0,1,-55,-534)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 55.804688 690.574219 L 79.804688 690.574219 L 79.804688 714.574219 L 55.804688 714.574219 Z M 55.804688 690.574219 " transform="matrix(1,0,0,1,-55,-534)"/>
<g style="fill:rgb(0%,0%,0%);fill-opacity:1;">
  <use xlink:href="#glyph0-1" x="5.80361" y="172.5755"/>
</g>
<g style="fill:rgb(0%,0%,0%);fill-opacity:1;">
  <use xlink:href="#glyph2-4" x="12.80561" y="175.5755"/>
</g>
<g style="fill:rgb(0%,0%,0%);fill-opacity:1;">
  <use xlink:href="#glyph2-1" x="14.0287" y="166.1719"/>
  <use xlink:href="#glyph2-4" x="16.0267" y="166.1719"/>
  <use xlink:href="#glyph2-3" x="19.0267" y="166.1719"/>
</g>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 55.804688 579.566406 L 79.804688 579.566406 L 79.804688 603.566406 L 55.804688 603.566406 Z M 55.804688 579.566406 " transform="matrix(1,0,0,1,-55,-534)"/>
<g style="fill:rgb(0%,0%,0%);fill-opacity:1;">
  <use xlink:href="#glyph0-1" x="5.80361" y="61.565"/>
</g>
<g style="fill:rgb(0%,0%,0%);fill-opacity:1;">
  <use xlink:href="#glyph2-4" x="12.80561" y="64.565"/>
</g>
<g style="fill:rgb(0%,0%,0%);fill-opacity:1;">
  <use xlink:href="#glyph2-1" x="13.8607" y="55.1613"/>
</g>
<g style="fill:rgb(0%,0%,0%);fill-opacity:1;">
  <use xlink:href="#glyph1-2" x="15.8587" y="55.1613"/>
</g>
<g style="fill:rgb(0%,0%,0%);fill-opacity:1;">
  <use xlink:href="#glyph2-3" x="19.1947" y="55.1613"/>
</g>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 100.804688 690.574219 L 124.804688 690.574219 L 124.804688 714.574219 L 100.804688 714.574219 Z M 100.804688 690.574219 " transform="matrix(1,0,0,1,-55,-534)"/>
<g style="fill:rgb(0%,0%,0%);fill-opacity:1;">
  <use xlink:href="#glyph0-1" x="50.8036" y="172.5755"/>
</g>
<g style="fill:rgb(0%,0%,0%);fill-opacity:1;">
  <use xlink:href="#glyph2-2" x="57.8056" y="175.5755"/>
</g>
<g style="fill:rgb(0%,0%,0%);fill-opacity:1;">
  <use xlink:href="#glyph2-1" x="59.0287" y="166.1719"/>
  <use xlink:href="#glyph2-4" x="61.0267" y="166.1719"/>
  <use xlink:href="#glyph2-3" x="64.0267" y="166.1719"/>
</g>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 229.605469 690.574219 L 253.605469 690.574219 L 253.605469 714.574219 L 229.605469 714.574219 Z M 229.605469 690.574219 " transform="matrix(1,0,0,1,-55,-534)"/>
<g style="fill:rgb(0%,0%,0%);fill-opacity:1;">
  <use xlink:href="#glyph0-1" x="179.6036" y="172.5755"/>
</g>
<g style="fill:rgb(0%,0%,0%);fill-opacity:1;">
  <use xlink:href="#glyph1-1" x="186.6056" y="175.5755"/>
</g>
<g style="fill:rgb(0%,0%,0%);fill-opacity:1;">
  <use xlink:href="#glyph2-1" x="187.8287" y="166.1719"/>
  <use xlink:href="#glyph2-4" x="189.8267" y="166.1719"/>
  <use xlink:href="#glyph2-3" x="192.8267" y="166.1719"/>
</g>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 100.804688 646.292969 L 124.804688 646.292969 L 124.804688 670.292969 L 100.804688 670.292969 Z M 100.804688 646.292969 " transform="matrix(1,0,0,1,-55,-534)"/>
<g style="fill:rgb(0%,0%,0%);fill-opacity:1;">
  <use xlink:href="#glyph0-1" x="50.8036" y="128.2912"/>
</g>
<g style="fill:rgb(0%,0%,0%);fill-opacity:1;">
  <use xlink:href="#glyph2-2" x="57.8056" y="131.2912"/>
</g>
<g style="fill:rgb(0%,0%,0%);fill-opacity:1;">
  <use xlink:href="#glyph2-1" x="59.0287" y="121.8876"/>
  <use xlink:href="#glyph2-2" x="61.0267" y="121.8876"/>
  <use xlink:href="#glyph2-3" x="64.0267" y="121.8876"/>
</g>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 100.804688 579.566406 L 124.804688 579.566406 L 124.804688 603.566406 L 100.804688 603.566406 Z M 100.804688 579.566406 " transform="matrix(1,0,0,1,-55,-534)"/>
<g style="fill:rgb(0%,0%,0%);fill-opacity:1;">
  <use xlink:href="#glyph0-1" x="50.8036" y="61.565"/>
</g>
<g style="fill:rgb(0%,0%,0%);fill-opacity:1;">
  <use xlink:href="#glyph2-2" x="57.8056" y="64.565"/>
</g>
<g style="fill:rgb(0%,0%,0%);fill-opacity:1;">
  <use xlink:href="#glyph2-1" x="58.8607" y="55.1613"/>
</g>
<g style="fill:rgb(0%,0%,0%);fill-opacity:1;">
  <use xlink:href="#glyph1-2" x="60.8587" y="55.1613"/>
</g>
<g style="fill:rgb(0%,0%,0%);fill-opacity:1;">
  <use xlink:href="#glyph2-3" x="64.1947" y="55.1613"/>
</g>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 229.605469 579.523438 L 253.605469 579.523438 L 253.605469 603.523438 L 229.605469 603.523438 Z M 229.605469 579.523438 " transform="matrix(1,0,0,1,-55,-534)"/>
<g style="fill:rgb(0%,0%,0%);fill-opacity:1;">
  <use xlink:href="#glyph0-1" x="179.6036" y="61.5221"/>
</g>
<g style="fill:rgb(0%,0%,0%);fill-opacity:1;">
  <use xlink:href="#glyph1-1" x="186.6056" y="64.5221"/>
</g>
<g style="fill:rgb(0%,0%,0%);fill-opacity:1;">
  <use xlink:href="#glyph2-1" x="187.6607" y="55.1185"/>
</g>
<g style="fill:rgb(0%,0%,0%);fill-opacity:1;">
  <use xlink:href="#glyph1-2" x="189.6587" y="55.1185"/>
</g>
<g style="fill:rgb(0%,0%,0%);fill-opacity:1;">
  <use xlink:href="#glyph2-3" x="192.9947" y="55.1185"/>
</g>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 67.804688 734.859375 L 67.804688 720.476562 " transform="matrix(1,0,0,1,-55,-534)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:butt;stroke-linejoin:miter;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 67.804688 716.476562 L 67.804688 720.476562 M 66.304688 720.476562 L 67.804688 716.476562 L 69.304688 720.476562 " transform="matrix(1,0,0,1,-55,-534)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 79.804688 702.574219 L 94.902344 702.574219 " transform="matrix(1,0,0,1,-55,-534)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:butt;stroke-linejoin:miter;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 98.902344 702.574219 L 94.902344 702.574219 M 94.902344 701.074219 L 98.902344 702.574219 L 94.902344 704.074219 " transform="matrix(1,0,0,1,-55,-534)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 171.703125 702.574219 L 188.703125 702.574219 " transform="matrix(1,0,0,1,-55,-534)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:butt;stroke-linejoin:miter;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 192.703125 702.574219 L 188.703125 702.574219 M 188.703125 701.074219 L 192.703125 702.574219 L 188.703125 704.074219 " transform="matrix(1,0,0,1,-55,-534)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 209.492188 702.574219 L 223.703125 702.574219 " transform="matrix(1,0,0,1,-55,-534)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:butt;stroke-linejoin:miter;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 227.703125 702.574219 L 223.703125 702.574219 M 223.703125 701.074219 L 227.703125 702.574219 L 223.703125 704.074219 " transform="matrix(1,0,0,1,-55,-534)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 67.804688 579.566406 L 67.804688 564.898438 " transform="matrix(1,0,0,1,-55,-534)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:butt;stroke-linejoin:miter;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 67.804688 560.898438 L 67.804688 564.898438 M 66.304688 564.898438 L 67.804688 560.898438 L 69.304688 564.898438 " transform="matrix(1,0,0,1,-55,-534)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 241.605469 734.859375 L 241.605469 720.476562 " transform="matrix(1,0,0,1,-55,-534)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:butt;stroke-linejoin:miter;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 241.605469 716.476562 L 241.605469 720.476562 M 240.105469 720.476562 L 241.605469 716.476562 L 243.105469 720.476562 " transform="matrix(1,0,0,1,-55,-534)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 79.804688 591.566406 L 94.902344 591.566406 " transform="matrix(1,0,0,1,-55,-534)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:butt;stroke-linejoin:miter;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 98.902344 591.566406 L 94.902344 591.566406 M 94.902344 590.066406 L 98.902344 591.566406 L 94.902344 593.066406 " transform="matrix(1,0,0,1,-55,-534)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 171.703125 591.566406 L 191.257812 591.566406 " transform="matrix(1,0,0,1,-55,-534)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:butt;stroke-linejoin:miter;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 195.257812 591.566406 L 191.257812 591.566406 M 191.257812 590.066406 L 195.257812 591.566406 L 191.257812 593.066406 " transform="matrix(1,0,0,1,-55,-534)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 212.046875 591.554688 L 223.703125 591.542969 " transform="matrix(1,0,0,1,-55,-534)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:butt;stroke-linejoin:miter;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 227.703125 591.539062 L 223.703125 591.542969 M 223.703125 590.042969 L 227.703125 591.539062 L 223.707031 593.042969 " transform="matrix(1,0,0,1,-55,-534)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 241.605469 579.523438 L 241.605469 564.898438 " transform="matrix(1,0,0,1,-55,-534)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:butt;stroke-linejoin:miter;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 241.605469 560.898438 L 241.605469 564.898438 M 240.105469 564.898438 L 241.605469 560.898438 L 243.105469 564.898438 " transform="matrix(1,0,0,1,-55,-534)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 79.804688 658.292969 L 94.902344 658.292969 " transform="matrix(1,0,0,1,-55,-534)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:butt;stroke-linejoin:miter;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 98.902344 658.292969 L 94.902344 658.292969 M 94.902344 656.792969 L 98.902344 658.292969 L 94.902344 659.792969 " transform="matrix(1,0,0,1,-55,-534)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 171.703125 658.292969 L 191.257812 658.292969 " transform="matrix(1,0,0,1,-55,-534)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:butt;stroke-linejoin:miter;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 195.257812 658.292969 L 191.257812 658.292969 M 191.257812 656.792969 L 195.257812 658.292969 L 191.257812 659.792969 " transform="matrix(1,0,0,1,-55,-534)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 212.046875 658.292969 L 223.703125 658.292969 " transform="matrix(1,0,0,1,-55,-534)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:butt;stroke-linejoin:miter;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 227.703125 658.292969 L 223.703125 658.292969 M 223.703125 656.792969 L 227.703125 658.292969 L 223.703125 659.792969 " transform="matrix(1,0,0,1,-55,-534)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 112.804688 579.566406 L 112.804688 564.898438 " transform="matrix(1,0,0,1,-55,-534)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:butt;stroke-linejoin:miter;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 112.804688 560.898438 L 112.804688 564.898438 M 111.304688 564.898438 L 112.804688 560.898438 L 114.304688 564.898438 " transform="matrix(1,0,0,1,-55,-534)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 67.804688 690.574219 L 67.804688 676.191406 " transform="matrix(1,0,0,1,-55,-534)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:butt;stroke-linejoin:miter;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 67.804688 672.191406 L 67.804688 676.191406 M 66.304688 676.191406 L 67.804688 672.191406 L 69.304688 676.191406 " transform="matrix(1,0,0,1,-55,-534)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 67.804688 646.292969 L 67.804688 639.128906 " transform="matrix(1,0,0,1,-55,-534)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:butt;stroke-linejoin:miter;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 67.804688 635.128906 L 67.804688 639.128906 M 66.304688 639.128906 L 67.804688 635.128906 L 69.304688 639.128906 " transform="matrix(1,0,0,1,-55,-534)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 67.804688 615.230469 L 67.804688 609.464844 " transform="matrix(1,0,0,1,-55,-534)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:butt;stroke-linejoin:miter;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 67.804688 605.464844 L 67.804688 609.464844 M 66.304688 609.464844 L 67.804688 605.464844 L 69.304688 609.464844 " transform="matrix(1,0,0,1,-55,-534)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 112.804688 690.574219 L 112.804688 676.191406 " transform="matrix(1,0,0,1,-55,-534)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:butt;stroke-linejoin:miter;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 112.804688 672.191406 L 112.804688 676.191406 M 111.304688 676.191406 L 112.804688 672.191406 L 114.304688 676.191406 " transform="matrix(1,0,0,1,-55,-534)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 241.605469 690.574219 L 241.605469 676.191406 " transform="matrix(1,0,0,1,-55,-534)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:butt;stroke-linejoin:miter;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 241.605469 672.191406 L 241.605469 676.191406 M 240.105469 676.191406 L 241.605469 672.191406 L 243.105469 676.191406 " transform="matrix(1,0,0,1,-55,-534)"/>
<g style="fill:rgb(0%,0%,0%);fill-opacity:1;">
  <use xlink:href="#glyph3-1" x="53.3586" y="94.6293"/>
</g>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 112.804688 646.292969 L 112.804688 640.53125 " transform="matrix(1,0,0,1,-55,-534)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:butt;stroke-linejoin:miter;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 112.804688 636.53125 L 112.804688 640.53125 M 111.304688 640.53125 L 112.804688 636.53125 L 114.304688 640.53125 " transform="matrix(1,0,0,1,-55,-534)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 112.804688 616.628906 L 112.804688 609.464844 " transform="matrix(1,0,0,1,-55,-534)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:butt;stroke-linejoin:miter;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 112.804688 605.464844 L 112.804688 609.464844 M 111.304688 609.464844 L 112.804688 605.464844 L 114.304688 609.464844 " transform="matrix(1,0,0,1,-55,-534)"/>
<g style="fill:rgb(0%,0%,0%);fill-opacity:1;">
  <use xlink:href="#glyph3-1" x="182.1586" y="93.6293"/>
</g>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 241.605469 646.292969 L 241.605469 639.53125 " transform="matrix(1,0,0,1,-55,-534)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:butt;stroke-linejoin:miter;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 241.605469 635.53125 L 241.605469 639.53125 M 240.105469 639.53125 L 241.605469 635.53125 L 243.105469 639.53125 " transform="matrix(1,0,0,1,-55,-534)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 241.605469 615.628906 L 241.605469 609.421875 " transform="matrix(1,0,0,1,-55,-534)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:butt;stroke-linejoin:miter;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 241.605469 605.421875 L 241.605469 609.421875 M 240.105469 609.421875 L 241.605469 605.421875 L 243.105469 609.421875 " transform="matrix(1,0,0,1,-55,-534)"/>
<path style="fill-rule:nonzero;fill:rgb(69.804382%,85.098267%,100%);fill-opacity:1;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 147.703125 734.859375 L 171.703125 734.859375 L 171.703125 758.859375 L 147.703125 758.859375 Z M 147.703125 734.859375 " transform="matrix(1,0,0,1,-55,-534)"/>
<g style="fill:rgb(0%,0%,0%);fill-opacity:1;">
  <use xlink:href="#glyph0-2" x="99.9546" y="214.3599"/>
</g>
<g style="fill:rgb(0%,0%,0%);fill-opacity:1;">
  <use xlink:href="#glyph2-5" x="106.4526" y="217.3599"/>
</g>
<path style="fill-rule:nonzero;fill:rgb(69.804382%,85.098267%,100%);fill-opacity:1;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 147.703125 535 L 171.703125 535 L 171.703125 559 L 147.703125 559 Z M 147.703125 535 " transform="matrix(1,0,0,1,-55,-534)"/>
<g style="fill:rgb(0%,0%,0%);fill-opacity:1;">
  <use xlink:href="#glyph0-3" x="99.7026" y="14.5"/>
</g>
<g style="fill:rgb(0%,0%,0%);fill-opacity:1;">
  <use xlink:href="#glyph2-5" x="106.7046" y="17.5"/>
</g>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 159.703125 734.859375 L 159.703125 720.476562 " transform="matrix(1,0,0,1,-55,-534)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:butt;stroke-linejoin:miter;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 159.703125 716.476562 L 159.703125 720.476562 M 158.203125 720.476562 L 159.703125 716.476562 L 161.203125 720.476562 " transform="matrix(1,0,0,1,-55,-534)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 147.703125 690.574219 L 171.703125 690.574219 L 171.703125 714.574219 L 147.703125 714.574219 Z M 147.703125 690.574219 " transform="matrix(1,0,0,1,-55,-534)"/>
<g style="fill:rgb(0%,0%,0%);fill-opacity:1;">
  <use xlink:href="#glyph0-1" x="97.7036" y="172.5755"/>
</g>
<g style="fill:rgb(0%,0%,0%);fill-opacity:1;">
  <use xlink:href="#glyph2-5" x="104.7056" y="175.5755"/>
</g>
<g style="fill:rgb(0%,0%,0%);fill-opacity:1;">
  <use xlink:href="#glyph2-1" x="105.9287" y="166.1719"/>
  <use xlink:href="#glyph2-4" x="107.9267" y="166.1719"/>
  <use xlink:href="#glyph2-3" x="110.9267" y="166.1719"/>
</g>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 147.703125 646.292969 L 171.703125 646.292969 L 171.703125 670.292969 L 147.703125 670.292969 Z M 147.703125 646.292969 " transform="matrix(1,0,0,1,-55,-534)"/>
<g style="fill:rgb(0%,0%,0%);fill-opacity:1;">
  <use xlink:href="#glyph0-1" x="97.7036" y="128.2912"/>
</g>
<g style="fill:rgb(0%,0%,0%);fill-opacity:1;">
  <use xlink:href="#glyph2-5" x="104.7056" y="131.2912"/>
</g>
<g style="fill:rgb(0%,0%,0%);fill-opacity:1;">
  <use xlink:href="#glyph2-1" x="105.9287" y="121.8876"/>
  <use xlink:href="#glyph2-2" x="107.9267" y="121.8876"/>
  <use xlink:href="#glyph2-3" x="110.9267" y="121.8876"/>
</g>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 147.703125 579.566406 L 171.703125 579.566406 L 171.703125 603.566406 L 147.703125 603.566406 Z M 147.703125 579.566406 " transform="matrix(1,0,0,1,-55,-534)"/>
<g style="fill:rgb(0%,0%,0%);fill-opacity:1;">
  <use xlink:href="#glyph0-1" x="97.7036" y="61.565"/>
</g>
<g style="fill:rgb(0%,0%,0%);fill-opacity:1;">
  <use xlink:href="#glyph2-5" x="104.7056" y="64.565"/>
</g>
<g style="fill:rgb(0%,0%,0%);fill-opacity:1;">
  <use xlink:href="#glyph2-1" x="105.7607" y="55.1613"/>
</g>
<g style="fill:rgb(0%,0%,0%);fill-opacity:1;">
  <use xlink:href="#glyph1-2" x="107.7587" y="55.1613"/>
</g>
<g style="fill:rgb(0%,0%,0%);fill-opacity:1;">
  <use xlink:href="#glyph2-3" x="111.0947" y="55.1613"/>
</g>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 124.804688 702.574219 L 141.804688 702.574219 " transform="matrix(1,0,0,1,-55,-534)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:butt;stroke-linejoin:miter;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 145.804688 702.574219 L 141.804688 702.574219 M 141.804688 701.074219 L 145.804688 702.574219 L 141.804688 704.074219 " transform="matrix(1,0,0,1,-55,-534)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 124.804688 591.566406 L 141.804688 591.566406 " transform="matrix(1,0,0,1,-55,-534)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:butt;stroke-linejoin:miter;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 145.804688 591.566406 L 141.804688 591.566406 M 141.804688 590.066406 L 145.804688 591.566406 L 141.804688 593.066406 " transform="matrix(1,0,0,1,-55,-534)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 124.804688 658.292969 L 141.804688 658.292969 " transform="matrix(1,0,0,1,-55,-534)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:butt;stroke-linejoin:miter;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 145.804688 658.292969 L 141.804688 658.292969 M 141.804688 656.792969 L 145.804688 658.292969 L 141.804688 659.792969 " transform="matrix(1,0,0,1,-55,-534)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 159.703125 579.566406 L 159.703125 564.898438 " transform="matrix(1,0,0,1,-55,-534)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:butt;stroke-linejoin:miter;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 159.703125 560.898438 L 159.703125 564.898438 M 158.203125 564.898438 L 159.703125 560.898438 L 161.203125 564.898438 " transform="matrix(1,0,0,1,-55,-534)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 159.703125 690.574219 L 159.703125 676.191406 " transform="matrix(1,0,0,1,-55,-534)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:butt;stroke-linejoin:miter;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 159.703125 672.191406 L 159.703125 676.191406 M 158.203125 676.191406 L 159.703125 672.191406 L 161.203125 676.191406 " transform="matrix(1,0,0,1,-55,-534)"/>
<g style="fill:rgb(0%,0%,0%);fill-opacity:1;">
  <use xlink:href="#glyph3-1" x="100.2586" y="94.6293"/>
</g>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 159.703125 646.292969 L 159.703125 640.53125 " transform="matrix(1,0,0,1,-55,-534)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:butt;stroke-linejoin:miter;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 159.703125 636.53125 L 159.703125 640.53125 M 158.203125 640.53125 L 159.703125 636.53125 L 161.203125 640.53125 " transform="matrix(1,0,0,1,-55,-534)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 159.703125 616.628906 L 159.703125 609.464844 " transform="matrix(1,0,0,1,-55,-534)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:butt;stroke-linejoin:miter;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 159.703125 605.464844 L 159.703125 609.464844 M 158.203125 609.464844 L 159.703125 605.464844 L 161.203125 609.464844 " transform="matrix(1,0,0,1,-55,-534)"/>
</g>
</svg>
