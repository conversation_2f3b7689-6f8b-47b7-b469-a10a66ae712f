<?xml version="1.0" encoding="UTF-8"?>
<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="480pt" height="122pt" viewBox="0 0 480 122" version="1.1">
<g id="surface1">
<path style="fill-rule:nonzero;fill:rgb(39.215088%,74.902344%,100%);fill-opacity:1;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 343 960.5 L 395 903 L 469 914.5 L 480 986 L 379 1021 Z M 343 960.5 " transform="matrix(1,0,0,1,-178,-900)"/>
<path style="fill-rule:nonzero;fill:rgb(69.804382%,85.098267%,100%);fill-opacity:1;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 291.960938 932.769531 C 289.238281 927.667969 285.726562 922.875 281.425781 918.574219 C 257.996094 895.140625 220.003906 895.140625 196.574219 918.574219 C 173.140625 942.003906 173.140625 979.996094 196.574219 1003.425781 C 220.003906 1026.859375 257.996094 1026.859375 281.425781 1003.425781 C 285.726562 999.125 289.238281 994.332031 291.960938 989.230469 L 235.5 961 Z M 291.960938 932.769531 " transform="matrix(1,0,0,1,-178,-900)"/>
<path style="fill-rule:nonzero;fill:rgb(39.215088%,74.902344%,100%);fill-opacity:1;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 540.308594 938.59375 L 558.019531 919.011719 C 567.058594 909.019531 580.519531 904.246094 593.832031 906.316406 L 617.5625 910.003906 C 634.792969 912.679688 648.300781 926.210938 650.953125 943.445312 L 652.429688 953.042969 C 655.367188 972.132812 644.242188 990.59375 625.992188 996.917969 L 587.300781 1010.324219 C 569.367188 1016.539062 549.535156 1009.296875 539.832031 992.984375 L 535.601562 985.878906 C 526.625 970.792969 528.535156 951.617188 540.308594 938.59375 Z M 540.308594 938.59375 " transform="matrix(1,0,0,1,-178,-900)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 267.835938 926.636719 L 262.03125 997.363281 " transform="matrix(1,0,0,1,-178,-900)"/>
<path style="fill-rule:nonzero;fill:rgb(0%,0%,0%);fill-opacity:1;stroke-width:1;stroke-linecap:butt;stroke-linejoin:miter;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 259.84375 998.0625 C 261.109375 996.992188 263.003906 997.148438 264.074219 998.410156 C 265.144531 999.675781 264.992188 1001.566406 263.726562 1002.640625 C 262.464844 1003.710938 260.570312 1003.554688 259.5 1002.292969 C 258.425781 1001.027344 258.582031 999.136719 259.84375 998.0625 " transform="matrix(1,0,0,1,-178,-900)"/>
<path style="fill-rule:nonzero;fill:rgb(0%,0%,0%);fill-opacity:1;stroke-width:1;stroke-linecap:butt;stroke-linejoin:miter;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 270.023438 925.9375 C 268.757812 927.007812 266.863281 926.851562 265.792969 925.589844 C 264.722656 924.324219 264.875 922.433594 266.140625 921.359375 C 267.402344 920.289062 269.296875 920.445312 270.367188 921.707031 C 271.441406 922.972656 271.285156 924.863281 270.023438 925.9375 " transform="matrix(1,0,0,1,-178,-900)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 426.671875 920.976562 L 420.867188 991.703125 " transform="matrix(1,0,0,1,-178,-900)"/>
<path style="fill-rule:nonzero;fill:rgb(0%,0%,0%);fill-opacity:1;stroke-width:1;stroke-linecap:butt;stroke-linejoin:miter;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 418.683594 992.402344 C 419.945312 991.332031 421.839844 991.488281 422.910156 992.75 C 423.980469 994.015625 423.828125 995.90625 422.5625 996.980469 C 421.300781 998.050781 419.40625 997.894531 418.335938 996.632812 C 417.261719 995.367188 417.417969 993.476562 418.683594 992.402344 " transform="matrix(1,0,0,1,-178,-900)"/>
<path style="fill-rule:nonzero;fill:rgb(0%,0%,0%);fill-opacity:1;stroke-width:1;stroke-linecap:butt;stroke-linejoin:miter;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 428.859375 920.277344 C 427.59375 921.347656 425.703125 921.191406 424.628906 919.929688 C 423.558594 918.664062 423.714844 916.773438 424.976562 915.699219 C 426.238281 914.628906 428.132812 914.785156 429.203125 916.046875 C 430.277344 917.3125 430.121094 919.203125 428.859375 920.277344 " transform="matrix(1,0,0,1,-178,-900)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 606.335938 920.976562 L 600.53125 991.703125 " transform="matrix(1,0,0,1,-178,-900)"/>
<path style="fill-rule:nonzero;fill:rgb(0%,0%,0%);fill-opacity:1;stroke-width:1;stroke-linecap:butt;stroke-linejoin:miter;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 598.34375 992.402344 C 599.609375 991.332031 601.503906 991.488281 602.574219 992.75 C 603.644531 994.015625 603.492188 995.90625 602.226562 996.980469 C 600.964844 998.050781 599.070312 997.894531 598 996.632812 C 596.925781 995.367188 597.082031 993.476562 598.34375 992.402344 " transform="matrix(1,0,0,1,-178,-900)"/>
<path style="fill-rule:nonzero;fill:rgb(0%,0%,0%);fill-opacity:1;stroke-width:1;stroke-linecap:butt;stroke-linejoin:miter;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 608.523438 920.277344 C 607.257812 921.347656 605.363281 921.191406 604.292969 919.929688 C 603.222656 918.664062 603.375 916.773438 604.640625 915.699219 C 605.902344 914.628906 607.796875 914.785156 608.867188 916.046875 C 609.941406 917.3125 609.785156 919.203125 608.523438 920.277344 " transform="matrix(1,0,0,1,-178,-900)"/>
</g>
</svg>
