<?xml version="1.0" encoding="UTF-8"?>
<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="205pt" height="154pt" viewBox="0 0 205 154" version="1.1">
<defs>
<g>
<symbol overflow="visible" id="glyph0-0">
<path style="stroke:none;" d=""/>
</symbol>
<symbol overflow="visible" id="glyph0-1">
<path style="stroke:none;" d="M 2.734375 1.453125 C 1.46875 0.390625 1.203125 -0.625 1.203125 -2.296875 C 1.203125 -4.03125 1.484375 -4.875 2.734375 -5.9375 L 2.65625 -6.078125 C 1.21875 -5.234375 0.4375 -3.9375 0.4375 -2.265625 C 0.4375 -0.734375 1.203125 0.78125 2.625 1.59375 Z M 2.734375 1.453125 "/>
</symbol>
<symbol overflow="visible" id="glyph0-2">
<path style="stroke:none;" d="M 0.546875 -4.59375 C 0.921875 -5.25 1.328125 -5.546875 1.890625 -5.546875 C 2.484375 -5.546875 2.859375 -5.234375 2.859375 -4.625 C 2.859375 -4.078125 2.578125 -3.671875 2.140625 -3.421875 C 1.953125 -3.3125 1.71875 -3.21875 1.375 -3.09375 L 1.375 -2.96875 C 1.890625 -2.96875 2.09375 -2.9375 2.296875 -2.875 C 2.921875 -2.703125 3.234375 -2.265625 3.234375 -1.578125 C 3.234375 -0.8125 2.734375 -0.203125 2.0625 -0.203125 C 1.8125 -0.203125 1.625 -0.25 1.28125 -0.484375 C 1.03125 -0.65625 0.890625 -0.71875 0.734375 -0.71875 C 0.53125 -0.71875 0.375 -0.578125 0.375 -0.390625 C 0.375 -0.0625 0.71875 0.125 1.375 0.125 C 2.171875 0.125 3.03125 -0.140625 3.46875 -0.71875 C 3.71875 -1.046875 3.875 -1.5 3.875 -1.96875 C 3.875 -2.4375 3.734375 -2.859375 3.484375 -3.125 C 3.296875 -3.328125 3.125 -3.4375 2.734375 -3.609375 C 3.34375 -3.96875 3.578125 -4.421875 3.578125 -4.84375 C 3.578125 -5.59375 3 -6.078125 2.171875 -6.078125 C 1.234375 -6.078125 0.671875 -5.484375 0.40625 -4.625 Z M 0.546875 -4.59375 "/>
</symbol>
<symbol overflow="visible" id="glyph0-3">
<path style="stroke:none;" d="M 0.75 1.265625 C 1.375 0.96875 1.75 0.40625 1.75 -0.140625 C 1.75 -0.609375 1.4375 -0.921875 1.03125 -0.921875 C 0.71875 -0.921875 0.5 -0.71875 0.5 -0.40625 C 0.5 -0.09375 0.6875 0.046875 1.015625 0.046875 C 1.109375 0.046875 1.203125 0.015625 1.28125 0.015625 C 1.34375 0.015625 1.40625 0.078125 1.40625 0.140625 C 1.40625 0.4375 1.15625 0.765625 0.65625 1.09375 Z M 0.75 1.265625 "/>
</symbol>
<symbol overflow="visible" id="glyph0-4">
<path style="stroke:none;" d=""/>
</symbol>
<symbol overflow="visible" id="glyph0-5">
<path style="stroke:none;" d="M 2.5625 -1.75 L 2.5625 -2.3125 L 0.34375 -2.3125 L 0.34375 -1.75 Z M 2.5625 -1.75 "/>
</symbol>
<symbol overflow="visible" id="glyph0-6">
<path style="stroke:none;" d="M 3.546875 0 L 3.546875 -0.140625 C 2.875 -0.140625 2.6875 -0.296875 2.6875 -0.6875 L 2.6875 -6.0625 L 2.609375 -6.078125 L 1 -5.265625 L 1 -5.140625 L 1.234375 -5.234375 C 1.40625 -5.296875 1.5625 -5.34375 1.640625 -5.34375 C 1.84375 -5.34375 1.921875 -5.203125 1.921875 -4.890625 L 1.921875 -0.859375 C 1.921875 -0.359375 1.734375 -0.171875 1.0625 -0.140625 L 1.0625 0 Z M 3.546875 0 "/>
</symbol>
<symbol overflow="visible" id="glyph0-7">
<path style="stroke:none;" d="M 0.265625 -5.9375 C 1.5625 -4.90625 1.796875 -3.875 1.796875 -2.203125 C 1.796875 -0.453125 1.53125 0.390625 0.265625 1.453125 L 0.34375 1.59375 C 1.765625 0.71875 2.5625 -0.5625 2.5625 -2.21875 C 2.5625 -3.75 1.75 -5.25 0.375 -6.078125 Z M 0.265625 -5.9375 "/>
</symbol>
<symbol overflow="visible" id="glyph0-8">
<path style="stroke:none;" d="M 4.265625 -1.234375 L 4.140625 -1.28125 C 3.84375 -0.78125 3.65625 -0.6875 3.28125 -0.6875 L 1.171875 -0.6875 L 2.65625 -2.265625 C 3.453125 -3.109375 3.8125 -3.78125 3.8125 -4.5 C 3.8125 -5.390625 3.15625 -6.078125 2.140625 -6.078125 C 1.03125 -6.078125 0.453125 -5.34375 0.265625 -4.296875 L 0.453125 -4.25 C 0.8125 -5.125 1.140625 -5.421875 1.78125 -5.421875 C 2.546875 -5.421875 3.03125 -4.96875 3.03125 -4.15625 C 3.03125 -3.390625 2.703125 -2.703125 1.859375 -1.8125 L 0.265625 -0.109375 L 0.265625 0 L 3.78125 0 Z M 4.265625 -1.234375 "/>
</symbol>
<symbol overflow="visible" id="glyph1-0">
<path style="stroke:none;" d=""/>
</symbol>
<symbol overflow="visible" id="glyph1-1">
<path style="stroke:none;" d="M 4.9375 -5.109375 C 4.9375 -6.296875 4.421875 -6.78125 3.703125 -6.78125 C 3.3125 -6.78125 2.9375 -6.6875 2.484375 -6.359375 C 1.40625 -5.53125 0.265625 -3.765625 0.265625 -1.609375 C 0.265625 -0.546875 0.734375 0.109375 1.546875 0.109375 C 2.25 0.109375 2.953125 -0.203125 3.59375 -1.0625 C 4.34375 -2.046875 4.9375 -3.484375 4.9375 -5.109375 Z M 1.46875 -3.46875 C 1.71875 -4.359375 2.703125 -6.5625 3.5625 -6.5625 C 3.921875 -6.5625 4.09375 -6.15625 4.09375 -5.515625 C 4.09375 -4.765625 3.96875 -4.046875 3.796875 -3.46875 Z M 3.734375 -3.15625 C 3.484375 -2.171875 2.625 -0.09375 1.671875 -0.09375 C 1.296875 -0.09375 1.109375 -0.609375 1.109375 -1.5 C 1.109375 -2.125 1.203125 -2.65625 1.40625 -3.15625 Z M 3.734375 -3.15625 "/>
</symbol>
</g>
</defs>
<g id="surface1">
<path style="fill:none;stroke-width:1;stroke-linecap:butt;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 166.398438 98.140625 L 101.421875 76.578125 " transform="matrix(1,0,0,1,1,1)"/>
<path style="fill-rule:nonzero;fill:rgb(0%,0%,0%);fill-opacity:1;stroke-width:1;stroke-linecap:butt;stroke-linejoin:miter;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 101.339844 75.429688 C 101.710938 76.171875 101.414062 77.074219 100.671875 77.445312 C 99.933594 77.816406 99.03125 77.515625 98.660156 76.777344 C 98.289062 76.035156 98.585938 75.136719 99.328125 74.761719 C 100.066406 74.390625 100.96875 74.691406 101.339844 75.429688 " transform="matrix(1,0,0,1,1,1)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:butt;stroke-linejoin:miter;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 170.195312 99.402344 L 166.398438 98.140625 M 166.871094 96.71875 L 170.195312 99.402344 L 165.929688 99.566406 " transform="matrix(1,0,0,1,1,1)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:butt;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 5.898438 76 L 194.101562 76 " transform="matrix(1,0,0,1,1,1)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:butt;stroke-linejoin:miter;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 198.101562 76 L 194.101562 76 M 194.101562 74.5 L 198.101562 76 L 194.101562 77.5 " transform="matrix(1,0,0,1,1,1)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:butt;stroke-linejoin:miter;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 1.898438 76 L 5.898438 76 M 5.898438 77.5 L 1.898438 76 L 5.898438 74.5 " transform="matrix(1,0,0,1,1,1)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:butt;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 100 5.898438 L 100 146.101562 " transform="matrix(1,0,0,1,1,1)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:butt;stroke-linejoin:miter;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 100 150.101562 L 100 146.101562 M 101.5 146.101562 L 100 150.101562 L 98.5 146.101562 " transform="matrix(1,0,0,1,1,1)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:butt;stroke-linejoin:miter;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 100 1.898438 L 100 5.898438 M 98.5 5.898438 L 100 1.898438 L 101.5 5.898438 " transform="matrix(1,0,0,1,1,1)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:butt;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-dasharray:1,4;stroke-miterlimit:10;" d="M 124 8 L 124 144 " transform="matrix(1,0,0,1,1,1)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:butt;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-dasharray:1,4;stroke-miterlimit:10;" d="M 148 8 L 148 144 " transform="matrix(1,0,0,1,1,1)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:butt;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-dasharray:1,4;stroke-miterlimit:10;" d="M 172 8 L 172 144 " transform="matrix(1,0,0,1,1,1)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:butt;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-dasharray:1,4;stroke-miterlimit:10;" d="M 76 8 L 76 144 " transform="matrix(1,0,0,1,1,1)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:butt;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-dasharray:1,4;stroke-miterlimit:10;" d="M 28 8 L 28 144 " transform="matrix(1,0,0,1,1,1)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:butt;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-dasharray:1,4;stroke-miterlimit:10;" d="M 52 8 L 52 144 " transform="matrix(1,0,0,1,1,1)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:butt;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-dasharray:1,4;stroke-miterlimit:10;" d="M 8 52 L 192 52 " transform="matrix(1,0,0,1,1,1)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:butt;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-dasharray:1,4;stroke-miterlimit:10;" d="M 8 28 L 192 28 " transform="matrix(1,0,0,1,1,1)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:butt;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-dasharray:1,4;stroke-miterlimit:10;" d="M 8 100 L 192 100 " transform="matrix(1,0,0,1,1,1)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:butt;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-dasharray:1,4;stroke-miterlimit:10;" d="M 8 124 L 192 124 " transform="matrix(1,0,0,1,1,1)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:butt;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 143.828125 32.171875 L 101.0625 74.9375 " transform="matrix(1,0,0,1,1,1)"/>
<path style="fill-rule:nonzero;fill:rgb(0%,0%,0%);fill-opacity:1;stroke-width:1;stroke-linecap:butt;stroke-linejoin:miter;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 100 74.5 C 100.828125 74.5 101.5 75.171875 101.5 76 C 101.5 76.828125 100.828125 77.5 100 77.5 C 99.171875 77.5 98.5 76.828125 98.5 76 C 98.5 75.171875 99.171875 74.5 100 74.5 " transform="matrix(1,0,0,1,1,1)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:butt;stroke-linejoin:miter;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 146.65625 29.34375 L 143.828125 32.171875 M 142.765625 31.109375 L 146.65625 29.34375 L 144.890625 33.234375 " transform="matrix(1,0,0,1,1,1)"/>
<g style="fill:rgb(0%,0%,0%);fill-opacity:1;">
  <use xlink:href="#glyph0-1" x="176.875" y="110"/>
  <use xlink:href="#glyph0-2" x="179.872" y="110"/>
  <use xlink:href="#glyph0-3" x="184.372" y="110"/>
  <use xlink:href="#glyph0-4" x="186.622" y="110"/>
  <use xlink:href="#glyph0-5" x="188.872" y="110"/>
  <use xlink:href="#glyph0-6" x="191.869" y="110"/>
  <use xlink:href="#glyph0-7" x="196.369" y="110"/>
</g>
<g style="fill:rgb(0%,0%,0%);fill-opacity:1;">
  <use xlink:href="#glyph0-1" x="128.875" y="26"/>
  <use xlink:href="#glyph0-8" x="131.872" y="26"/>
  <use xlink:href="#glyph0-3" x="136.372" y="26"/>
  <use xlink:href="#glyph0-4" x="138.622" y="26"/>
  <use xlink:href="#glyph0-8" x="140.872" y="26"/>
  <use xlink:href="#glyph0-7" x="145.372" y="26"/>
</g>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 122.382812 53.699219 C 131.1875 62.070312 134.269531 74.703125 130.285156 86.09375 " transform="matrix(1,0,0,1,1,1)"/>
<g style="fill:rgb(0%,0%,0%);fill-opacity:1;">
  <use xlink:href="#glyph1-1" x="135.1" y="71"/>
</g>
</g>
</svg>
