{"cells": [{"cell_type": "markdown", "id": "fc960b02", "metadata": {"origin_pos": 0}, "source": ["# 残差网络（ResNet）\n", ":label:`sec_resnet`\n", "\n", "随着我们设计越来越深的网络，深刻理解“新添加的层如何提升神经网络的性能”变得至关重要。更重要的是设计网络的能力，在这种网络中，添加层会使网络更具表现力，\n", "为了取得质的突破，我们需要一些数学基础知识。\n", "\n", "## 函数类\n", "\n", "首先，假设有一类特定的神经网络架构$\\mathcal{F}$，它包括学习速率和其他超参数设置。\n", "对于所有$f \\in \\mathcal{F}$，存在一些参数集（例如权重和偏置），这些参数可以通过在合适的数据集上进行训练而获得。\n", "现在假设$f^*$是我们真正想要找到的函数，如果是$f^* \\in \\mathcal{F}$，那我们可以轻而易举的训练得到它，但通常我们不会那么幸运。\n", "相反，我们将尝试找到一个函数$f^*_\\mathcal{F}$，这是我们在$\\mathcal{F}$中的最佳选择。\n", "例如，给定一个具有$\\mathbf{X}$特性和$\\mathbf{y}$标签的数据集，我们可以尝试通过解决以下优化问题来找到它：\n", "\n", "$$f^*_\\mathcal{F} := \\mathop{\\mathrm{argmin}}_f L(\\mathbf{X}, \\mathbf{y}, f) \\text{ subject to } f \\in \\mathcal{F}.$$\n", "\n", "那么，怎样得到更近似真正$f^*$的函数呢？\n", "唯一合理的可能性是，我们需要设计一个更强大的架构$\\mathcal{F}'$。\n", "换句话说，我们预计$f^*_{\\mathcal{F}'}$比$f^*_{\\mathcal{F}}$“更近似”。\n", "然而，如果$\\mathcal{F} \\not\\subseteq \\mathcal{F}'$，则无法保证新的体系“更近似”。\n", "事实上，$f^*_{\\mathcal{F}'}$可能更糟：\n", "如 :numref:`fig_functionclasses`所示，对于非嵌套函数（non-nested function）类，较复杂的函数类并不总是向“真”函数$f^*$靠拢（复杂度由$\\mathcal{F}_1$向$\\mathcal{F}_6$递增）。\n", "在 :numref:`fig_functionclasses`的左边，虽然$\\mathcal{F}_3$比$\\mathcal{F}_1$更接近$f^*$，但$\\mathcal{F}_6$却离的更远了。\n", "相反对于 :numref:`fig_functionclasses`右侧的嵌套函数（nested function）类$\\mathcal{F}_1 \\subseteq \\ldots \\subseteq \\mathcal{F}_6$，我们可以避免上述问题。\n", "\n", "![对于非嵌套函数类，较复杂（由较大区域表示）的函数类不能保证更接近“真”函数（ $f^*$ ）。这种现象在嵌套函数类中不会发生。](../img/functionclasses.svg)\n", ":label:`fig_functionclasses`\n", "\n", "因此，只有当较复杂的函数类包含较小的函数类时，我们才能确保提高它们的性能。\n", "对于深度神经网络，如果我们能将新添加的层训练成*恒等映射*（identity function）$f(\\mathbf{x}) = \\mathbf{x}$，新模型和原模型将同样有效。\n", "同时，由于新模型可能得出更优的解来拟合训练数据集，因此添加层似乎更容易降低训练误差。\n", "\n", "针对这一问题，何恺明等人提出了*残差网络*（ResNet） :cite:`<PERSON><PERSON>.Ren.ea.2016`。\n", "它在2015年的ImageNet图像识别挑战赛夺魁，并深刻影响了后来的深度神经网络的设计。\n", "残差网络的核心思想是：每个附加层都应该更容易地包含原始函数作为其元素之一。\n", "于是，*残差块*（residual blocks）便诞生了，这个设计对如何建立深层神经网络产生了深远的影响。\n", "凭借它，ResNet赢得了2015年ImageNet大规模视觉识别挑战赛。\n", "\n", "## (**残差块**)\n", "\n", "让我们聚焦于神经网络局部：如图 :numref:`fig_residual_block`所示，假设我们的原始输入为$x$，而希望学出的理想映射为$f(\\mathbf{x})$（作为 :numref:`fig_residual_block`上方激活函数的输入）。\n", " :numref:`fig_residual_block`左图虚线框中的部分需要直接拟合出该映射$f(\\mathbf{x})$，而右图虚线框中的部分则需要拟合出残差映射$f(\\mathbf{x}) - \\mathbf{x}$。\n", "残差映射在现实中往往更容易优化。\n", "以本节开头提到的恒等映射作为我们希望学出的理想映射$f(\\mathbf{x})$，我们只需将 :numref:`fig_residual_block`中右图虚线框内上方的加权运算（如仿射）的权重和偏置参数设成0，那么$f(\\mathbf{x})$即为恒等映射。\n", "实际中，当理想映射$f(\\mathbf{x})$极接近于恒等映射时，残差映射也易于捕捉恒等映射的细微波动。\n", " :numref:`fig_residual_block`右图是ResNet的基础架构--*残差块*（residual block）。\n", "在残差块中，输入可通过跨层数据线路更快地向前传播。\n", "\n", "![一个正常块（左图）和一个残差块（右图）。](../img/residual-block.svg)\n", ":label:`fig_residual_block`\n", "\n", "ResNet沿用了VGG完整的$3\\times 3$卷积层设计。\n", "残差块里首先有2个有相同输出通道数的$3\\times 3$卷积层。\n", "每个卷积层后接一个批量规范化层和ReLU激活函数。\n", "然后我们通过跨层数据通路，跳过这2个卷积运算，将输入直接加在最后的ReLU激活函数前。\n", "这样的设计要求2个卷积层的输出与输入形状一样，从而使它们可以相加。\n", "如果想改变通道数，就需要引入一个额外的$1\\times 1$卷积层来将输入变换成需要的形状后再做相加运算。\n", "残差块的实现如下：\n"]}, {"cell_type": "code", "execution_count": 1, "id": "7fa5f60b", "metadata": {"execution": {"iopub.execute_input": "2022-12-07T16:45:35.571633Z", "iopub.status.busy": "2022-12-07T16:45:35.571089Z", "iopub.status.idle": "2022-12-07T16:45:38.202149Z", "shell.execute_reply": "2022-12-07T16:45:38.200637Z"}, "origin_pos": 2, "tab": ["pytorch"]}, "outputs": [], "source": ["import torch\n", "from torch import nn\n", "from torch.nn import functional as F\n", "from d2l import torch as d2l\n", "\n", "\n", "class Residual(nn.Mo<PERSON>le):  #@save\n", "    def __init__(self, input_channels, num_channels,\n", "                 use_1x1conv=False, strides=1):\n", "        super().__init__()\n", "        self.conv1 = nn.Conv2d(input_channels, num_channels,\n", "                               kernel_size=3, padding=1, stride=strides)\n", "        self.conv2 = nn.Conv2d(num_channels, num_channels,\n", "                               kernel_size=3, padding=1)\n", "        if use_1x1conv:\n", "            self.conv3 = nn.Conv2d(input_channels, num_channels,\n", "                                   kernel_size=1, stride=strides)\n", "        else:\n", "            self.conv3 = None\n", "        self.bn1 = nn.BatchNorm2d(num_channels)\n", "        self.bn2 = nn.BatchNorm2d(num_channels)\n", "\n", "    def forward(self, X):\n", "        Y = F.relu(self.bn1(self.conv1(X)))\n", "        Y = self.bn2(self.conv2(Y))\n", "        if self.conv3:\n", "            X = self.conv3(X)\n", "        Y += X\n", "        return <PERSON><PERSON>re<PERSON>(Y)"]}, {"cell_type": "markdown", "id": "6987cadb", "metadata": {"origin_pos": 5}, "source": ["如 :numref:`fig_resnet_block`所示，此代码生成两种类型的网络：\n", "一种是当`use_1x1conv=False`时，应用ReLU非线性函数之前，将输入添加到输出。\n", "另一种是当`use_1x1conv=True`时，添加通过$1 \\times 1$卷积调整通道和分辨率。\n", "\n", "![包含以及不包含 $1 \\times 1$ 卷积层的残差块。](../img/resnet-block.svg)\n", ":label:`fig_resnet_block`\n", "\n", "下面我们来查看[**输入和输出形状一致**]的情况。\n"]}, {"cell_type": "code", "execution_count": 2, "id": "652c9ed3", "metadata": {"execution": {"iopub.execute_input": "2022-12-07T16:45:38.215111Z", "iopub.status.busy": "2022-12-07T16:45:38.213997Z", "iopub.status.idle": "2022-12-07T16:45:38.250789Z", "shell.execute_reply": "2022-12-07T16:45:38.249975Z"}, "origin_pos": 7, "tab": ["pytorch"]}, "outputs": [{"data": {"text/plain": ["<PERSON>.<PERSON><PERSON>([4, 3, 6, 6])"]}, "execution_count": 2, "metadata": {}, "output_type": "execute_result"}], "source": ["blk = Residual(3,3)\n", "X = torch.rand(4, 3, 6, 6)\n", "Y = blk(X)\n", "Y.shape"]}, {"cell_type": "markdown", "id": "a956eddd", "metadata": {"origin_pos": 10}, "source": ["我们也可以在[**增加输出通道数的同时，减半输出的高和宽**]。\n"]}, {"cell_type": "code", "execution_count": 3, "id": "90871f40", "metadata": {"execution": {"iopub.execute_input": "2022-12-07T16:45:38.256818Z", "iopub.status.busy": "2022-12-07T16:45:38.256295Z", "iopub.status.idle": "2022-12-07T16:45:38.271374Z", "shell.execute_reply": "2022-12-07T16:45:38.270604Z"}, "origin_pos": 12, "tab": ["pytorch"]}, "outputs": [{"data": {"text/plain": ["torch.<PERSON><PERSON>([4, 6, 3, 3])"]}, "execution_count": 3, "metadata": {}, "output_type": "execute_result"}], "source": ["blk = Residual(3,6, use_1x1conv=True, strides=2)\n", "blk(X).shape"]}, {"cell_type": "markdown", "id": "d8ea5fee", "metadata": {"origin_pos": 15}, "source": ["## [**ResNet模型**]\n", "\n", "ResNet的前两层跟之前介绍的GoogLeNet中的一样：\n", "在输出通道数为64、步幅为2的$7 \\times 7$卷积层后，接步幅为2的$3 \\times 3$的最大汇聚层。\n", "不同之处在于ResNet每个卷积层后增加了批量规范化层。\n"]}, {"cell_type": "code", "execution_count": 4, "id": "45d1ff6c", "metadata": {"execution": {"iopub.execute_input": "2022-12-07T16:45:38.274572Z", "iopub.status.busy": "2022-12-07T16:45:38.274133Z", "iopub.status.idle": "2022-12-07T16:45:38.279285Z", "shell.execute_reply": "2022-12-07T16:45:38.278488Z"}, "origin_pos": 17, "tab": ["pytorch"]}, "outputs": [], "source": ["b1 = nn.Sequential(nn.Conv2d(1, 64, kernel_size=7, stride=2, padding=3),\n", "                   nn.<PERSON><PERSON><PERSON><PERSON>2d(64), nn.<PERSON><PERSON><PERSON>(),\n", "                   nn.MaxPool2d(kernel_size=3, stride=2, padding=1))"]}, {"cell_type": "markdown", "id": "65e5097e", "metadata": {"origin_pos": 20}, "source": ["GoogLeNet在后面接了4个由Inception块组成的模块。\n", "ResNet则使用4个由残差块组成的模块，每个模块使用若干个同样输出通道数的残差块。\n", "第一个模块的通道数同输入通道数一致。\n", "由于之前已经使用了步幅为2的最大汇聚层，所以无须减小高和宽。\n", "之后的每个模块在第一个残差块里将上一个模块的通道数翻倍，并将高和宽减半。\n", "\n", "下面我们来实现这个模块。注意，我们对第一个模块做了特别处理。\n"]}, {"cell_type": "code", "execution_count": 5, "id": "0f3bc6ba", "metadata": {"execution": {"iopub.execute_input": "2022-12-07T16:45:38.282390Z", "iopub.status.busy": "2022-12-07T16:45:38.281943Z", "iopub.status.idle": "2022-12-07T16:45:38.287260Z", "shell.execute_reply": "2022-12-07T16:45:38.286293Z"}, "origin_pos": 22, "tab": ["pytorch"]}, "outputs": [], "source": ["def resnet_block(input_channels, num_channels, num_residuals,\n", "                 first_block=False):\n", "    blk = []\n", "    for i in range(num_residuals):\n", "        if i == 0 and not first_block:\n", "            blk.append(Residual(input_channels, num_channels,\n", "                                use_1x1conv=True, strides=2))\n", "        else:\n", "            blk.append(Residual(num_channels, num_channels))\n", "    return blk"]}, {"cell_type": "markdown", "id": "4c0b53fc", "metadata": {"origin_pos": 25}, "source": ["接着在ResNet加入所有残差块，这里每个模块使用2个残差块。\n"]}, {"cell_type": "code", "execution_count": 6, "id": "eb2330a0", "metadata": {"execution": {"iopub.execute_input": "2022-12-07T16:45:38.290288Z", "iopub.status.busy": "2022-12-07T16:45:38.289868Z", "iopub.status.idle": "2022-12-07T16:45:38.454785Z", "shell.execute_reply": "2022-12-07T16:45:38.453666Z"}, "origin_pos": 27, "tab": ["pytorch"]}, "outputs": [], "source": ["b2 = nn.Sequential(*resnet_block(64, 64, 2, first_block=True))\n", "b3 = nn.Sequential(*resnet_block(64, 128, 2))\n", "b4 = nn.Sequential(*resnet_block(128, 256, 2))\n", "b5 = nn.Sequential(*resnet_block(256, 512, 2))"]}, {"cell_type": "markdown", "id": "47041d9d", "metadata": {"origin_pos": 29}, "source": ["最后，与GoogLeNet一样，在ResNet中加入全局平均汇聚层，以及全连接层输出。\n"]}, {"cell_type": "code", "execution_count": 7, "id": "5dd4de17", "metadata": {"execution": {"iopub.execute_input": "2022-12-07T16:45:38.459084Z", "iopub.status.busy": "2022-12-07T16:45:38.458290Z", "iopub.status.idle": "2022-12-07T16:45:38.463816Z", "shell.execute_reply": "2022-12-07T16:45:38.463045Z"}, "origin_pos": 31, "tab": ["pytorch"]}, "outputs": [], "source": ["net = nn.Sequential(b1, b2, b3, b4, b5,\n", "                    nn.AdaptiveAvgPool2d((1,1)),\n", "                    nn.<PERSON><PERSON>(), nn.<PERSON>(512, 10))"]}, {"cell_type": "markdown", "id": "aaa53430", "metadata": {"origin_pos": 34}, "source": ["每个模块有4个卷积层（不包括恒等映射的$1\\times 1$卷积层）。\n", "加上第一个$7\\times 7$卷积层和最后一个全连接层，共有18层。\n", "因此，这种模型通常被称为ResNet-18。\n", "通过配置不同的通道数和模块里的残差块数可以得到不同的ResNet模型，例如更深的含152层的ResNet-152。\n", "虽然ResNet的主体架构跟GoogLeNet类似，但ResNet架构更简单，修改也更方便。这些因素都导致了ResNet迅速被广泛使用。\n", " :numref:`fig_resnet18`描述了完整的ResNet-18。\n", "\n", "![ResNet-18 架构](../img/resnet18.svg)\n", ":label:`fig_resnet18`\n", "\n", "在训练ResNet之前，让我们[**观察一下ResNet中不同模块的输入形状是如何变化的**]。\n", "在之前所有架构中，分辨率降低，通道数量增加，直到全局平均汇聚层聚集所有特征。\n"]}, {"cell_type": "code", "execution_count": 8, "id": "964ee6e0", "metadata": {"execution": {"iopub.execute_input": "2022-12-07T16:45:38.468322Z", "iopub.status.busy": "2022-12-07T16:45:38.467820Z", "iopub.status.idle": "2022-12-07T16:45:38.508924Z", "shell.execute_reply": "2022-12-07T16:45:38.507868Z"}, "origin_pos": 36, "tab": ["pytorch"]}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Sequential output shape:\t torch.Si<PERSON>([1, 64, 56, 56])\n", "Sequential output shape:\t torch.Si<PERSON>([1, 64, 56, 56])\n", "Sequential output shape:\t torch.Size([1, 128, 28, 28])\n", "Sequential output shape:\t torch.Size([1, 256, 14, 14])\n", "Sequential output shape:\t torch.Size([1, 512, 7, 7])\n", "AdaptiveAvgPool2d output shape:\t torch.Size([1, 512, 1, 1])\n", "Flatten output shape:\t torch.Size([1, 512])\n", "Linear output shape:\t torch.Size([1, 10])\n"]}], "source": ["X = torch.rand(size=(1, 1, 224, 224))\n", "for layer in net:\n", "    X = layer(X)\n", "    print(layer.__class__.__name__,'output shape:\\t', X.shape)"]}, {"cell_type": "markdown", "id": "c98001ac", "metadata": {"origin_pos": 39}, "source": ["## [**训练模型**]\n", "\n", "同之前一样，我们在Fashion-MNIST数据集上训练ResNet。\n"]}, {"cell_type": "code", "execution_count": 9, "id": "d6c0a33d", "metadata": {"execution": {"iopub.execute_input": "2022-12-07T16:45:38.512922Z", "iopub.status.busy": "2022-12-07T16:45:38.512330Z", "iopub.status.idle": "2022-12-07T16:48:25.875806Z", "shell.execute_reply": "2022-12-07T16:48:25.874647Z"}, "origin_pos": 40, "tab": ["pytorch"]}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["loss 0.008, train acc 0.999, test acc 0.898\n", "4650.1 examples/sec on cuda:0\n"]}, {"data": {"image/svg+xml": ["<?xml version=\"1.0\" encoding=\"utf-8\" standalone=\"no\"?>\n", "<!DOCTYPE svg PUBLIC \"-//W3C//DTD SVG 1.1//EN\"\n", "  \"http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd\">\n", "<svg xmlns:xlink=\"http://www.w3.org/1999/xlink\" width=\"238.965625pt\" height=\"183.35625pt\" viewBox=\"0 0 238.**********.35625\" xmlns=\"http://www.w3.org/2000/svg\" version=\"1.1\">\n", " <metadata>\n", "  <rdf:RDF xmlns:dc=\"http://purl.org/dc/elements/1.1/\" xmlns:cc=\"http://creativecommons.org/ns#\" xmlns:rdf=\"http://www.w3.org/1999/02/22-rdf-syntax-ns#\">\n", "   <cc:Work>\n", "    <dc:type rdf:resource=\"http://purl.org/dc/dcmitype/StillImage\"/>\n", "    <dc:date>2022-12-07T16:48:25.819158</dc:date>\n", "    <dc:format>image/svg+xml</dc:format>\n", "    <dc:creator>\n", "     <cc:Agent>\n", "      <dc:title>Matplotlib v3.5.1, https://matplotlib.org/</dc:title>\n", "     </cc:Agent>\n", "    </dc:creator>\n", "   </cc:Work>\n", "  </rdf:RDF>\n", " </metadata>\n", " <defs>\n", "  <style type=\"text/css\">*{stroke-linejoin: round; stroke-linecap: butt}</style>\n", " </defs>\n", " <g id=\"figure_1\">\n", "  <g id=\"patch_1\">\n", "   <path d=\"M 0 183.35625 \n", "L 238.**********.35625 \n", "L 238.965625 0 \n", "L 0 0 \n", "z\n", "\" style=\"fill: #ffffff\"/>\n", "  </g>\n", "  <g id=\"axes_1\">\n", "   <g id=\"patch_2\">\n", "    <path d=\"M 30.**********.8 \n", "L 225.**********.8 \n", "L 225.403125 7.2 \n", "L 30.103125 7.2 \n", "z\n", "\" style=\"fill: #ffffff\"/>\n", "   </g>\n", "   <g id=\"matplotlib.axis_1\">\n", "    <g id=\"xtick_1\">\n", "     <g id=\"line2d_1\">\n", "      <path d=\"M 51.803125 145.8 \n", "L 51.803125 7.2 \n", "\" clip-path=\"url(#p8f97f30c52)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_2\">\n", "      <defs>\n", "       <path id=\"md051d00c26\" d=\"M 0 0 \n", "L 0 3.5 \n", "\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </defs>\n", "      <g>\n", "       <use xlink:href=\"#md051d00c26\" x=\"51.803125\" y=\"145.8\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_1\">\n", "      <!-- 2 -->\n", "      <g transform=\"translate(48.621875 160.398438)scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-32\" d=\"M 1228 531 \n", "L 3431 531 \n", "L 3431 0 \n", "L 469 0 \n", "L 469 531 \n", "Q 828 903 1448 1529 \n", "Q 2069 2156 2228 2338 \n", "Q 2531 2678 2651 2914 \n", "Q 2772 3150 2772 3378 \n", "Q 2772 3750 2511 3984 \n", "Q 2250 4219 1831 4219 \n", "Q 1534 4219 1204 4116 \n", "Q 875 4013 500 3803 \n", "L 500 4441 \n", "Q 881 4594 1212 4672 \n", "Q 1544 4750 1819 4750 \n", "Q 2544 4750 2975 4387 \n", "Q 3406 4025 3406 3419 \n", "Q 3406 3131 3298 2873 \n", "Q 3191 2616 2906 2266 \n", "Q 2828 2175 2409 1742 \n", "Q 1991 1309 1228 531 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-32\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_2\">\n", "     <g id=\"line2d_3\">\n", "      <path d=\"M 95.203125 145.8 \n", "L 95.203125 7.2 \n", "\" clip-path=\"url(#p8f97f30c52)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_4\">\n", "      <g>\n", "       <use xlink:href=\"#md051d00c26\" x=\"95.203125\" y=\"145.8\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_2\">\n", "      <!-- 4 -->\n", "      <g transform=\"translate(92.021875 160.398438)scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-34\" d=\"M 2419 4116 \n", "L 825 1625 \n", "L 2419 1625 \n", "L 2419 4116 \n", "z\n", "M 2253 4666 \n", "L 3047 4666 \n", "L 3047 1625 \n", "L 3713 1625 \n", "L 3713 1100 \n", "L 3047 1100 \n", "L 3047 0 \n", "L 2419 0 \n", "L 2419 1100 \n", "L 313 1100 \n", "L 313 1709 \n", "L 2253 4666 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-34\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_3\">\n", "     <g id=\"line2d_5\">\n", "      <path d=\"M 138.603125 145.8 \n", "L 138.603125 7.2 \n", "\" clip-path=\"url(#p8f97f30c52)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_6\">\n", "      <g>\n", "       <use xlink:href=\"#md051d00c26\" x=\"138.603125\" y=\"145.8\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_3\">\n", "      <!-- 6 -->\n", "      <g transform=\"translate(135.421875 160.398438)scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-36\" d=\"M 2113 2584 \n", "Q 1688 2584 1439 2293 \n", "Q 1191 2003 1191 1497 \n", "Q 1191 994 1439 701 \n", "Q 1688 409 2113 409 \n", "Q 2538 409 2786 701 \n", "Q 3034 994 3034 1497 \n", "Q 3034 2003 2786 2293 \n", "Q 2538 2584 2113 2584 \n", "z\n", "M 3366 4563 \n", "L 3366 3988 \n", "Q 3128 4100 2886 4159 \n", "Q 2644 4219 2406 4219 \n", "Q 1781 4219 1451 3797 \n", "Q 1122 3375 1075 2522 \n", "Q 1259 2794 1537 2939 \n", "Q 1816 3084 2150 3084 \n", "Q 2853 3084 3261 2657 \n", "Q 3669 2231 3669 1497 \n", "Q 3669 778 3244 343 \n", "Q 2819 -91 2113 -91 \n", "Q 1303 -91 875 529 \n", "Q 447 1150 447 2328 \n", "Q 447 3434 972 4092 \n", "Q 1497 4750 2381 4750 \n", "Q 2619 4750 2861 4703 \n", "Q 3103 4656 3366 4563 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-36\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_4\">\n", "     <g id=\"line2d_7\">\n", "      <path d=\"M 182.003125 145.8 \n", "L 182.003125 7.2 \n", "\" clip-path=\"url(#p8f97f30c52)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_8\">\n", "      <g>\n", "       <use xlink:href=\"#md051d00c26\" x=\"182.003125\" y=\"145.8\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_4\">\n", "      <!-- 8 -->\n", "      <g transform=\"translate(178.821875 160.398438)scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-38\" d=\"M 2034 2216 \n", "Q 1584 2216 1326 1975 \n", "Q 1069 1734 1069 1313 \n", "Q 1069 891 1326 650 \n", "Q 1584 409 2034 409 \n", "Q 2484 409 2743 651 \n", "Q 3003 894 3003 1313 \n", "Q 3003 1734 2745 1975 \n", "Q 2488 2216 2034 2216 \n", "z\n", "M 1403 2484 \n", "Q 997 2584 770 2862 \n", "Q 544 3141 544 3541 \n", "Q 544 4100 942 4425 \n", "Q 1341 4750 2034 4750 \n", "Q 2731 4750 3128 4425 \n", "Q 3525 4100 3525 3541 \n", "Q 3525 3141 3298 2862 \n", "Q 3072 2584 2669 2484 \n", "Q 3125 2378 3379 2068 \n", "Q 3634 1759 3634 1313 \n", "Q 3634 634 3220 271 \n", "Q 2806 -91 2034 -91 \n", "Q 1263 -91 848 271 \n", "Q 434 634 434 1313 \n", "Q 434 1759 690 2068 \n", "Q 947 2378 1403 2484 \n", "z\n", "M 1172 3481 \n", "Q 1172 3119 1398 2916 \n", "Q 1625 2713 2034 2713 \n", "Q 2441 2713 2670 2916 \n", "Q 2900 3119 2900 3481 \n", "Q 2900 3844 2670 4047 \n", "Q 2441 4250 2034 4250 \n", "Q 1625 4250 1398 4047 \n", "Q 1172 3844 1172 3481 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-38\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_5\">\n", "     <g id=\"line2d_9\">\n", "      <path d=\"M 225.**********.8 \n", "L 225.403125 7.2 \n", "\" clip-path=\"url(#p8f97f30c52)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_10\">\n", "      <g>\n", "       <use xlink:href=\"#md051d00c26\" x=\"225.403125\" y=\"145.8\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_5\">\n", "      <!-- 10 -->\n", "      <g transform=\"translate(219.040625 160.398438)scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-31\" d=\"M 794 531 \n", "L 1825 531 \n", "L 1825 4091 \n", "L 703 3866 \n", "L 703 4441 \n", "L 1819 4666 \n", "L 2450 4666 \n", "L 2450 531 \n", "L 3481 531 \n", "L 3481 0 \n", "L 794 0 \n", "L 794 531 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "        <path id=\"DejaVuSans-30\" d=\"M 2034 4250 \n", "Q 1547 4250 1301 3770 \n", "Q 1056 3291 1056 2328 \n", "Q 1056 1369 1301 889 \n", "Q 1547 409 2034 409 \n", "Q 2525 409 2770 889 \n", "Q 3016 1369 3016 2328 \n", "Q 3016 3291 2770 3770 \n", "Q 2525 4250 2034 4250 \n", "z\n", "M 2034 4750 \n", "Q 2819 4750 3233 4129 \n", "Q 3647 3509 3647 2328 \n", "Q 3647 1150 3233 529 \n", "Q 2819 -91 2034 -91 \n", "Q 1250 -91 836 529 \n", "Q 422 1150 422 2328 \n", "Q 422 3509 836 4129 \n", "Q 1250 4750 2034 4750 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-31\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"63.623047\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"text_6\">\n", "     <!-- epoch -->\n", "     <g transform=\"translate(112.525 174.076563)scale(0.1 -0.1)\">\n", "      <defs>\n", "       <path id=\"DejaVuSans-65\" d=\"M 3597 1894 \n", "L 3597 1613 \n", "L 953 1613 \n", "Q 991 1019 1311 708 \n", "Q 1631 397 2203 397 \n", "Q 2534 397 2845 478 \n", "Q 3156 559 3463 722 \n", "L 3463 178 \n", "Q 3153 47 2828 -22 \n", "Q 2503 -91 2169 -91 \n", "Q 1331 -91 842 396 \n", "Q 353 884 353 1716 \n", "Q 353 2575 817 3079 \n", "Q 1281 3584 2069 3584 \n", "Q 2775 3584 3186 3129 \n", "Q 3597 2675 3597 1894 \n", "z\n", "M 3022 2063 \n", "Q 3016 2534 2758 2815 \n", "Q 2500 3097 2075 3097 \n", "Q 1594 3097 1305 2825 \n", "Q 1016 2553 972 2059 \n", "L 3022 2063 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-70\" d=\"M 1159 525 \n", "L 1159 -1331 \n", "L 581 -1331 \n", "L 581 3500 \n", "L 1159 3500 \n", "L 1159 2969 \n", "Q 1341 3281 1617 3432 \n", "Q 1894 3584 2278 3584 \n", "Q 2916 3584 3314 3078 \n", "Q 3713 2572 3713 1747 \n", "Q 3713 922 3314 415 \n", "Q 2916 -91 2278 -91 \n", "Q 1894 -91 1617 61 \n", "Q 1341 213 1159 525 \n", "z\n", "M 3116 1747 \n", "Q 3116 2381 2855 2742 \n", "Q 2594 3103 2138 3103 \n", "Q 1681 3103 1420 2742 \n", "Q 1159 2381 1159 1747 \n", "Q 1159 1113 1420 752 \n", "Q 1681 391 2138 391 \n", "Q 2594 391 2855 752 \n", "Q 3116 1113 3116 1747 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-6f\" d=\"M 1959 3097 \n", "Q 1497 3097 1228 2736 \n", "Q 959 2375 959 1747 \n", "Q 959 1119 1226 758 \n", "Q 1494 397 1959 397 \n", "Q 2419 397 2687 759 \n", "Q 2956 1122 2956 1747 \n", "Q 2956 2369 2687 2733 \n", "Q 2419 3097 1959 3097 \n", "z\n", "M 1959 3584 \n", "Q 2709 3584 3137 3096 \n", "Q 3566 2609 3566 1747 \n", "Q 3566 888 3137 398 \n", "Q 2709 -91 1959 -91 \n", "Q 1206 -91 779 398 \n", "Q 353 888 353 1747 \n", "Q 353 2609 779 3096 \n", "Q 1206 3584 1959 3584 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-63\" d=\"M 3122 3366 \n", "L 3122 2828 \n", "Q 2878 2963 2633 3030 \n", "Q 2388 3097 2138 3097 \n", "Q 1578 3097 1268 2742 \n", "Q 959 2388 959 1747 \n", "Q 959 1106 1268 751 \n", "Q 1578 397 2138 397 \n", "Q 2388 397 2633 464 \n", "Q 2878 531 3122 666 \n", "L 3122 134 \n", "Q 2881 22 2623 -34 \n", "Q 2366 -91 2075 -91 \n", "Q 1284 -91 818 406 \n", "Q 353 903 353 1747 \n", "Q 353 2603 823 3093 \n", "Q 1294 3584 2113 3584 \n", "Q 2378 3584 2631 3529 \n", "Q 2884 3475 3122 3366 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-68\" d=\"M 3513 2113 \n", "L 3513 0 \n", "L 2938 0 \n", "L 2938 2094 \n", "Q 2938 2591 2744 2837 \n", "Q 2550 3084 2163 3084 \n", "Q 1697 3084 1428 2787 \n", "Q 1159 2491 1159 1978 \n", "L 1159 0 \n", "L 581 0 \n", "L 581 4863 \n", "L 1159 4863 \n", "L 1159 2956 \n", "Q 1366 3272 1645 3428 \n", "Q 1925 3584 2291 3584 \n", "Q 2894 3584 3203 3211 \n", "Q 3513 2838 3513 2113 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "      </defs>\n", "      <use xlink:href=\"#DejaVuSans-65\"/>\n", "      <use xlink:href=\"#DejaVuSans-70\" x=\"61.523438\"/>\n", "      <use xlink:href=\"#DejaVuSans-6f\" x=\"125\"/>\n", "      <use xlink:href=\"#DejaVuSans-63\" x=\"186.181641\"/>\n", "      <use xlink:href=\"#DejaVuSans-68\" x=\"241.162109\"/>\n", "     </g>\n", "    </g>\n", "   </g>\n", "   <g id=\"matplotlib.axis_2\">\n", "    <g id=\"ytick_1\">\n", "     <g id=\"line2d_11\">\n", "      <path d=\"M 30.103125 140.470099 \n", "L 225.403125 140.470099 \n", "\" clip-path=\"url(#p8f97f30c52)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_12\">\n", "      <defs>\n", "       <path id=\"m5e5929ba87\" d=\"M 0 0 \n", "L -3.5 0 \n", "\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </defs>\n", "      <g>\n", "       <use xlink:href=\"#m5e5929ba87\" x=\"30.103125\" y=\"140.470099\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_7\">\n", "      <!-- 0.0 -->\n", "      <g transform=\"translate(7.2 144.269318)scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-2e\" d=\"M 684 794 \n", "L 1344 794 \n", "L 1344 0 \n", "L 684 0 \n", "L 684 794 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"95.410156\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_2\">\n", "     <g id=\"line2d_13\">\n", "      <path d=\"M 30.103125 115.04491 \n", "L 225.403125 115.04491 \n", "\" clip-path=\"url(#p8f97f30c52)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_14\">\n", "      <g>\n", "       <use xlink:href=\"#m5e5929ba87\" x=\"30.103125\" y=\"115.04491\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_8\">\n", "      <!-- 0.2 -->\n", "      <g transform=\"translate(7.2 118.844129)scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-32\" x=\"95.410156\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_3\">\n", "     <g id=\"line2d_15\">\n", "      <path d=\"M 30.103125 89.619722 \n", "L 225.403125 89.619722 \n", "\" clip-path=\"url(#p8f97f30c52)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_16\">\n", "      <g>\n", "       <use xlink:href=\"#m5e5929ba87\" x=\"30.103125\" y=\"89.619722\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_9\">\n", "      <!-- 0.4 -->\n", "      <g transform=\"translate(7.2 93.418941)scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-34\" x=\"95.410156\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_4\">\n", "     <g id=\"line2d_17\">\n", "      <path d=\"M 30.103125 64.194533 \n", "L 225.403125 64.194533 \n", "\" clip-path=\"url(#p8f97f30c52)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_18\">\n", "      <g>\n", "       <use xlink:href=\"#m5e5929ba87\" x=\"30.103125\" y=\"64.194533\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_10\">\n", "      <!-- 0.6 -->\n", "      <g transform=\"translate(7.2 67.993752)scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-36\" x=\"95.410156\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_5\">\n", "     <g id=\"line2d_19\">\n", "      <path d=\"M 30.103125 38.769345 \n", "L 225.403125 38.769345 \n", "\" clip-path=\"url(#p8f97f30c52)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_20\">\n", "      <g>\n", "       <use xlink:href=\"#m5e5929ba87\" x=\"30.103125\" y=\"38.769345\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_11\">\n", "      <!-- 0.8 -->\n", "      <g transform=\"translate(7.2 42.568564)scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-38\" x=\"95.410156\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_6\">\n", "     <g id=\"line2d_21\">\n", "      <path d=\"M 30.103125 13.344157 \n", "L 225.403125 13.344157 \n", "\" clip-path=\"url(#p8f97f30c52)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_22\">\n", "      <g>\n", "       <use xlink:href=\"#m5e5929ba87\" x=\"30.103125\" y=\"13.344157\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_12\">\n", "      <!-- 1.0 -->\n", "      <g transform=\"translate(7.2 17.143375)scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-31\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"95.410156\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "   </g>\n", "   <g id=\"line2d_23\">\n", "    <path d=\"M 12.743125 21.196911 \n", "L 17.083125 52.655519 \n", "L 21.423125 65.329819 \n", "L 25.763125 73.279428 \n", "L 30.103125 78.477005 \n", "L 34.443125 107.034964 \n", "L 38.783125 107.962622 \n", "L 43.123125 108.279326 \n", "L 47.463125 108.581649 \n", "L 51.803125 108.372705 \n", "L 56.143125 116.508427 \n", "L 60.483125 116.770686 \n", "L 64.823125 116.373648 \n", "L 69.163125 116.133998 \n", "L 73.503125 115.993712 \n", "L 77.843125 123.48335 \n", "L 82.183125 123.433683 \n", "L 86.523125 123.213838 \n", "L 90.863125 122.89787 \n", "L 95.203125 122.354661 \n", "L 99.543125 127.915951 \n", "L 103.883125 128.130378 \n", "L 108.223125 127.129985 \n", "L 112.563125 127.146068 \n", "L 116.903125 126.748245 \n", "L 121.243125 131.811316 \n", "L 125.583125 131.783064 \n", "L 129.923125 130.979708 \n", "L 134.263125 130.800336 \n", "L 138.603125 130.652772 \n", "L 142.943125 134.840886 \n", "L 147.283125 134.970435 \n", "L 151.623125 134.737142 \n", "L 155.963125 134.41505 \n", "L 160.303125 133.984288 \n", "L 164.643125 136.308668 \n", "L 168.983125 136.252822 \n", "L 173.323125 136.541534 \n", "L 177.663125 136.296799 \n", "L 182.003125 136.242883 \n", "L 186.343125 138.252376 \n", "L 190.683125 138.044314 \n", "L 195.023125 137.914766 \n", "L 199.363125 137.861864 \n", "L 203.703125 137.84783 \n", "L 208.043125 139.110592 \n", "L 212.383125 139.290232 \n", "L 216.723125 139.457603 \n", "L 221.063125 139.492911 \n", "L 225.403125 139.5 \n", "\" clip-path=\"url(#p8f97f30c52)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_24\">\n", "    <path d=\"M 12.743125 52.553297 \n", "L 17.083125 43.23439 \n", "L 21.423125 39.311011 \n", "L 25.763125 36.887074 \n", "L 30.103125 35.25855 \n", "L 34.443125 25.68484 \n", "L 38.783125 25.278062 \n", "L 43.123125 25.117816 \n", "L 47.463125 25.019204 \n", "L 51.803125 25.124494 \n", "L 56.143125 22.20874 \n", "L 60.483125 22.203457 \n", "L 64.823125 22.247481 \n", "L 69.163125 22.41477 \n", "L 73.503125 22.440018 \n", "L 77.843125 19.334882 \n", "L 82.183125 19.403559 \n", "L 86.523125 19.458148 \n", "L 90.863125 19.63072 \n", "L 95.203125 19.855124 \n", "L 99.543125 17.485893 \n", "L 103.883125 17.623246 \n", "L 108.223125 17.993044 \n", "L 112.563125 18.053797 \n", "L 116.903125 18.240624 \n", "L 121.243125 16.133489 \n", "L 125.583125 16.22858 \n", "L 129.923125 16.570203 \n", "L 134.263125 16.669696 \n", "L 138.603125 16.738419 \n", "L 142.943125 15.161449 \n", "L 147.283125 15.129752 \n", "L 151.623125 15.235409 \n", "L 155.963125 15.346348 \n", "L 160.303125 15.50106 \n", "L 164.643125 14.474682 \n", "L 168.983125 14.543358 \n", "L 173.323125 14.467638 \n", "L 177.663125 14.585621 \n", "L 182.003125 14.632366 \n", "L 186.343125 13.956965 \n", "L 190.683125 14.04149 \n", "L 195.023125 14.104884 \n", "L 199.363125 14.107525 \n", "L 203.703125 14.109031 \n", "L 208.043125 13.618864 \n", "L 212.383125 13.55547 \n", "L 216.723125 13.502641 \n", "L 221.063125 13.5 \n", "L 225.403125 13.511539 \n", "\" clip-path=\"url(#p8f97f30c52)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #bf00bf; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_25\">\n", "    <path d=\"M 30.103125 32.972402 \n", "L 51.803125 28.027203 \n", "L 73.503125 26.387278 \n", "L 95.203125 26.133026 \n", "L 116.903125 28.167041 \n", "L 138.603125 27.67125 \n", "L 160.303125 25.649948 \n", "L 182.003125 24.849054 \n", "L 203.703125 24.035448 \n", "L 225.403125 26.285578 \n", "\" clip-path=\"url(#p8f97f30c52)\" style=\"fill: none; stroke-dasharray: 9.6,2.4,1.5,2.4; stroke-dashoffset: 0; stroke: #008000; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"patch_3\">\n", "    <path d=\"M 30.**********.8 \n", "L 30.103125 7.2 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_4\">\n", "    <path d=\"M 225.**********.8 \n", "L 225.403125 7.2 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_5\">\n", "    <path d=\"M 30.**********.8 \n", "L 225.**********.8 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_6\">\n", "    <path d=\"M 30.103125 7.2 \n", "L 225.403125 7.2 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"legend_1\">\n", "    <g id=\"patch_7\">\n", "     <path d=\"M 140.634375 100.017188 \n", "L 218.403125 100.017188 \n", "Q 220.403125 100.017188 220.403125 98.017188 \n", "L 220.403125 54.982812 \n", "Q 220.403125 52.982812 218.403125 52.982812 \n", "L 140.634375 52.982812 \n", "Q 138.634375 52.982812 138.634375 54.982812 \n", "L 138.634375 98.017188 \n", "Q 138.634375 100.017188 140.634375 100.017188 \n", "z\n", "\" style=\"fill: #ffffff; opacity: 0.8; stroke: #cccccc; stroke-linejoin: miter\"/>\n", "    </g>\n", "    <g id=\"line2d_26\">\n", "     <path d=\"M 142.634375 61.08125 \n", "L 152.634375 61.08125 \n", "L 162.634375 61.08125 \n", "\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "    </g>\n", "    <g id=\"text_13\">\n", "     <!-- train loss -->\n", "     <g transform=\"translate(170.634375 64.58125)scale(0.1 -0.1)\">\n", "      <defs>\n", "       <path id=\"DejaVuSans-74\" d=\"M 1172 4494 \n", "L 1172 3500 \n", "L 2356 3500 \n", "L 2356 3053 \n", "L 1172 3053 \n", "L 1172 1153 \n", "Q 1172 725 1289 603 \n", "Q 1406 481 1766 481 \n", "L 2356 481 \n", "L 2356 0 \n", "L 1766 0 \n", "Q 1100 0 847 248 \n", "Q 594 497 594 1153 \n", "L 594 3053 \n", "L 172 3053 \n", "L 172 3500 \n", "L 594 3500 \n", "L 594 4494 \n", "L 1172 4494 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-72\" d=\"M 2631 2963 \n", "Q 2534 3019 2420 3045 \n", "Q 2306 3072 2169 3072 \n", "Q 1681 3072 1420 2755 \n", "Q 1159 2438 1159 1844 \n", "L 1159 0 \n", "L 581 0 \n", "L 581 3500 \n", "L 1159 3500 \n", "L 1159 2956 \n", "Q 1341 3275 1631 3429 \n", "Q 1922 3584 2338 3584 \n", "Q 2397 3584 2469 3576 \n", "Q 2541 3569 2628 3553 \n", "L 2631 2963 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-61\" d=\"M 2194 1759 \n", "Q 1497 1759 1228 1600 \n", "Q 959 1441 959 1056 \n", "Q 959 750 1161 570 \n", "Q 1363 391 1709 391 \n", "Q 2188 391 2477 730 \n", "Q 2766 1069 2766 1631 \n", "L 2766 1759 \n", "L 2194 1759 \n", "z\n", "M 3341 1997 \n", "L 3341 0 \n", "L 2766 0 \n", "L 2766 531 \n", "Q 2569 213 2275 61 \n", "Q 1981 -91 1556 -91 \n", "Q 1019 -91 701 211 \n", "Q 384 513 384 1019 \n", "Q 384 1609 779 1909 \n", "Q 1175 2209 1959 2209 \n", "L 2766 2209 \n", "L 2766 2266 \n", "Q 2766 2663 2505 2880 \n", "Q 2244 3097 1772 3097 \n", "Q 1472 3097 1187 3025 \n", "Q 903 2953 641 2809 \n", "L 641 3341 \n", "Q 956 3463 1253 3523 \n", "Q 1550 3584 1831 3584 \n", "Q 2591 3584 2966 3190 \n", "Q 3341 2797 3341 1997 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-69\" d=\"M 603 3500 \n", "L 1178 3500 \n", "L 1178 0 \n", "L 603 0 \n", "L 603 3500 \n", "z\n", "M 603 4863 \n", "L 1178 4863 \n", "L 1178 4134 \n", "L 603 4134 \n", "L 603 4863 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-6e\" d=\"M 3513 2113 \n", "L 3513 0 \n", "L 2938 0 \n", "L 2938 2094 \n", "Q 2938 2591 2744 2837 \n", "Q 2550 3084 2163 3084 \n", "Q 1697 3084 1428 2787 \n", "Q 1159 2491 1159 1978 \n", "L 1159 0 \n", "L 581 0 \n", "L 581 3500 \n", "L 1159 3500 \n", "L 1159 2956 \n", "Q 1366 3272 1645 3428 \n", "Q 1925 3584 2291 3584 \n", "Q 2894 3584 3203 3211 \n", "Q 3513 2838 3513 2113 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-20\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-6c\" d=\"M 603 4863 \n", "L 1178 4863 \n", "L 1178 0 \n", "L 603 0 \n", "L 603 4863 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-73\" d=\"M 2834 3397 \n", "L 2834 2853 \n", "Q 2591 2978 2328 3040 \n", "Q 2066 3103 1784 3103 \n", "Q 1356 3103 1142 2972 \n", "Q 928 2841 928 2578 \n", "Q 928 2378 1081 2264 \n", "Q 1234 2150 1697 2047 \n", "L 1894 2003 \n", "Q 2506 1872 2764 1633 \n", "Q 3022 1394 3022 966 \n", "Q 3022 478 2636 193 \n", "Q 2250 -91 1575 -91 \n", "Q 1294 -91 989 -36 \n", "Q 684 19 347 128 \n", "L 347 722 \n", "Q 666 556 975 473 \n", "Q 1284 391 1588 391 \n", "Q 1994 391 2212 530 \n", "Q 2431 669 2431 922 \n", "Q 2431 1156 2273 1281 \n", "Q 2116 1406 1581 1522 \n", "L 1381 1569 \n", "Q 847 1681 609 1914 \n", "Q 372 2147 372 2553 \n", "Q 372 3047 722 3315 \n", "Q 1072 3584 1716 3584 \n", "Q 2034 3584 2315 3537 \n", "Q 2597 3491 2834 3397 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "      </defs>\n", "      <use xlink:href=\"#DejaVuSans-74\"/>\n", "      <use xlink:href=\"#DejaVuSans-72\" x=\"39.208984\"/>\n", "      <use xlink:href=\"#DejaVuSans-61\" x=\"80.322266\"/>\n", "      <use xlink:href=\"#DejaVuSans-69\" x=\"141.601562\"/>\n", "      <use xlink:href=\"#DejaVuSans-6e\" x=\"169.384766\"/>\n", "      <use xlink:href=\"#DejaVuSans-20\" x=\"232.763672\"/>\n", "      <use xlink:href=\"#DejaVuSans-6c\" x=\"264.550781\"/>\n", "      <use xlink:href=\"#DejaVuSans-6f\" x=\"292.333984\"/>\n", "      <use xlink:href=\"#DejaVuSans-73\" x=\"353.515625\"/>\n", "      <use xlink:href=\"#DejaVuSans-73\" x=\"405.615234\"/>\n", "     </g>\n", "    </g>\n", "    <g id=\"line2d_27\">\n", "     <path d=\"M 142.634375 75.759375 \n", "L 152.634375 75.759375 \n", "L 162.634375 75.759375 \n", "\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #bf00bf; stroke-width: 1.5\"/>\n", "    </g>\n", "    <g id=\"text_14\">\n", "     <!-- train acc -->\n", "     <g transform=\"translate(170.634375 79.259375)scale(0.1 -0.1)\">\n", "      <use xlink:href=\"#DejaVuSans-74\"/>\n", "      <use xlink:href=\"#DejaVuSans-72\" x=\"39.208984\"/>\n", "      <use xlink:href=\"#DejaVuSans-61\" x=\"80.322266\"/>\n", "      <use xlink:href=\"#DejaVuSans-69\" x=\"141.601562\"/>\n", "      <use xlink:href=\"#DejaVuSans-6e\" x=\"169.384766\"/>\n", "      <use xlink:href=\"#DejaVuSans-20\" x=\"232.763672\"/>\n", "      <use xlink:href=\"#DejaVuSans-61\" x=\"264.550781\"/>\n", "      <use xlink:href=\"#DejaVuSans-63\" x=\"325.830078\"/>\n", "      <use xlink:href=\"#DejaVuSans-63\" x=\"380.810547\"/>\n", "     </g>\n", "    </g>\n", "    <g id=\"line2d_28\">\n", "     <path d=\"M 142.634375 90.4375 \n", "L 152.634375 90.4375 \n", "L 162.634375 90.4375 \n", "\" style=\"fill: none; stroke-dasharray: 9.6,2.4,1.5,2.4; stroke-dashoffset: 0; stroke: #008000; stroke-width: 1.5\"/>\n", "    </g>\n", "    <g id=\"text_15\">\n", "     <!-- test acc -->\n", "     <g transform=\"translate(170.634375 93.9375)scale(0.1 -0.1)\">\n", "      <use xlink:href=\"#DejaVuSans-74\"/>\n", "      <use xlink:href=\"#DejaVuSans-65\" x=\"39.208984\"/>\n", "      <use xlink:href=\"#DejaVuSans-73\" x=\"100.732422\"/>\n", "      <use xlink:href=\"#DejaVuSans-74\" x=\"152.832031\"/>\n", "      <use xlink:href=\"#DejaVuSans-20\" x=\"192.041016\"/>\n", "      <use xlink:href=\"#DejaVuSans-61\" x=\"223.828125\"/>\n", "      <use xlink:href=\"#DejaVuSans-63\" x=\"285.107422\"/>\n", "      <use xlink:href=\"#DejaVuSans-63\" x=\"340.087891\"/>\n", "     </g>\n", "    </g>\n", "   </g>\n", "  </g>\n", " </g>\n", " <defs>\n", "  <clipPath id=\"p8f97f30c52\">\n", "   <rect x=\"30.103125\" y=\"7.2\" width=\"195.3\" height=\"138.6\"/>\n", "  </clipPath>\n", " </defs>\n", "</svg>\n"], "text/plain": ["<Figure size 350x250 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["lr, num_epochs, batch_size = 0.05, 10, 256\n", "train_iter, test_iter = d2l.load_data_fashion_mnist(batch_size, resize=96)\n", "d2l.train_ch6(net, train_iter, test_iter, num_epochs, lr, d2l.try_gpu())"]}, {"cell_type": "markdown", "id": "ba190824", "metadata": {"origin_pos": 41}, "source": ["## 小结\n", "\n", "* 学习嵌套函数（nested function）是训练神经网络的理想情况。在深层神经网络中，学习另一层作为恒等映射（identity function）较容易（尽管这是一个极端情况）。\n", "* 残差映射可以更容易地学习同一函数，例如将权重层中的参数近似为零。\n", "* 利用残差块（residual blocks）可以训练出一个有效的深层神经网络：输入可以通过层间的残余连接更快地向前传播。\n", "* 残差网络（ResNet）对随后的深层神经网络设计产生了深远影响。\n", "\n", "## 练习\n", "\n", "1.  :numref:`fig_inception`中的Inception块与残差块之间的主要区别是什么？在删除了Inception块中的一些路径之后，它们是如何相互关联的？\n", "1. 参考ResNet论文 :cite:`<PERSON><PERSON>.Ren.ea.2016`中的表1，以实现不同的变体。\n", "1. 对于更深层次的网络，ResNet引入了“bottleneck”架构来降低模型复杂性。请试着去实现它。\n", "1. 在ResNet的后续版本中，作者将“卷积层、批量规范化层和激活层”架构更改为“批量规范化层、激活层和卷积层”架构。请尝试做这个改进。详见 :cite:`<PERSON><PERSON>Zhang.Ren.ea.2016*1`中的图1。\n", "1. 为什么即使函数类是嵌套的，我们仍然要限制增加函数的复杂性呢？\n"]}, {"cell_type": "markdown", "id": "32364e1b", "metadata": {"origin_pos": 43, "tab": ["pytorch"]}, "source": ["[Discussions](https://discuss.d2l.ai/t/1877)\n"]}], "metadata": {"language_info": {"name": "python"}}, "nbformat": 4, "nbformat_minor": 5}