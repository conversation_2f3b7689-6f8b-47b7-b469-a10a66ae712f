<?xml version="1.0" encoding="UTF-8"?>
<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="330pt" height="102pt" viewBox="0 0 330 102" version="1.1">
<g id="surface1">
<path style="fill-rule:nonzero;fill:rgb(69.804382%,85.098267%,100%);fill-opacity:1;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 510.105469 1570.5 L 560.308594 1620.703125 L 510.105469 1670.910156 L 459.898438 1620.703125 Z M 510.105469 1570.5 " transform="matrix(1,0,0,1,-273,-1570)"/>
<path style="fill-rule:nonzero;fill:rgb(69.804382%,85.098267%,100%);fill-opacity:1;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 359.355469 1585.144531 C 378.882812 1604.671875 378.882812 1636.328125 359.355469 1655.855469 C 339.828125 1675.382812 308.171875 1675.382812 288.644531 1655.855469 C 269.117188 1636.328125 269.117188 1604.671875 288.644531 1585.144531 C 308.171875 1565.617188 339.828125 1565.617188 359.355469 1585.144531 " transform="matrix(1,0,0,1,-273,-1570)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 324.5 1620 L 414.601562 1620.722656 " transform="matrix(1,0,0,1,-273,-1570)"/>
<path style="fill-rule:nonzero;fill:rgb(0%,0%,0%);fill-opacity:1;stroke-width:1;stroke-linecap:butt;stroke-linejoin:miter;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 422.601562 1620.785156 L 414.625 1617.722656 L 414.578125 1623.722656 Z M 422.601562 1620.785156 " transform="matrix(1,0,0,1,-273,-1570)"/>
<path style="fill:none;stroke-width:3;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(100%,14.901733%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 324.5 1619 L 366.105469 1619.664062 " transform="matrix(1,0,0,1,-273,-1570)"/>
<path style="fill-rule:nonzero;fill:rgb(100%,14.901733%,0%);fill-opacity:1;stroke-width:3;stroke-linecap:butt;stroke-linejoin:miter;stroke:rgb(100%,14.901733%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 369.800781 1619.726562 L 366.128906 1618.28125 L 366.082031 1621.050781 Z M 369.800781 1619.726562 " transform="matrix(1,0,0,1,-273,-1570)"/>
<path style="fill:none;stroke-width:3;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(100%,98.431396%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 324.5 1619 L 340.851562 1591.945312 " transform="matrix(1,0,0,1,-273,-1570)"/>
<path style="fill-rule:nonzero;fill:rgb(100%,98.431396%,0%);fill-opacity:1;stroke-width:3;stroke-linecap:butt;stroke-linejoin:miter;stroke:rgb(100%,98.431396%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 342.761719 1588.78125 L 339.664062 1591.226562 L 342.035156 1592.660156 Z M 342.761719 1588.78125 " transform="matrix(1,0,0,1,-273,-1570)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 510.308594 1619 L 592.441406 1605.414062 " transform="matrix(1,0,0,1,-273,-1570)"/>
<path style="fill-rule:nonzero;fill:rgb(0%,0%,0%);fill-opacity:1;stroke-width:1;stroke-linecap:butt;stroke-linejoin:miter;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 600.335938 1604.109375 L 591.953125 1602.457031 L 592.929688 1608.375 Z M 600.335938 1604.109375 " transform="matrix(1,0,0,1,-273,-1570)"/>
<path style="fill:none;stroke-width:3;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(100%,14.901733%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 510.308594 1620.398438 L 551.914062 1620.398438 " transform="matrix(1,0,0,1,-273,-1570)"/>
<path style="fill-rule:nonzero;fill:rgb(100%,14.901733%,0%);fill-opacity:1;stroke-width:3;stroke-linecap:butt;stroke-linejoin:miter;stroke:rgb(100%,14.901733%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 555.609375 1620.398438 L 551.914062 1619.015625 L 551.914062 1621.785156 Z M 555.609375 1620.398438 " transform="matrix(1,0,0,1,-273,-1570)"/>
<path style="fill:none;stroke-width:3;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(100%,98.431396%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 510.308594 1618 L 520.453125 1594.660156 " transform="matrix(1,0,0,1,-273,-1570)"/>
<path style="fill-rule:nonzero;fill:rgb(100%,98.431396%,0%);fill-opacity:1;stroke-width:3;stroke-linecap:butt;stroke-linejoin:miter;stroke:rgb(100%,98.431396%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 521.929688 1591.269531 L 519.183594 1594.105469 L 521.726562 1595.210938 Z M 521.929688 1591.269531 " transform="matrix(1,0,0,1,-273,-1570)"/>
</g>
</svg>
