{"cells": [{"cell_type": "markdown", "id": "a511c0e3", "metadata": {"origin_pos": 0}, "source": ["# 用于预训练BERT的数据集\n", ":label:`sec_bert-dataset`\n", "\n", "为了预训练 :numref:`sec_bert`中实现的BERT模型，我们需要以理想的格式生成数据集，以便于两个预训练任务：遮蔽语言模型和下一句预测。一方面，最初的BERT模型是在两个庞大的图书语料库和英语维基百科（参见 :numref:`subsec_bert_pretraining_tasks`）的合集上预训练的，但它很难吸引这本书的大多数读者。另一方面，现成的预训练BERT模型可能不适合医学等特定领域的应用。因此，在定制的数据集上对BERT进行预训练变得越来越流行。为了方便BERT预训练的演示，我们使用了较小的语料库WikiText-2 :cite:`Merity.Xiong.Bradbury.ea.2016`。\n", "\n", "与 :numref:`sec_word2vec_data`中用于预训练word2vec的PTB数据集相比，WikiText-2（1）保留了原来的标点符号，适合于下一句预测；（2）保留了原来的大小写和数字；（3）大了一倍以上。\n"]}, {"cell_type": "code", "execution_count": 1, "id": "10a3f68e", "metadata": {"execution": {"iopub.execute_input": "2022-12-07T16:50:19.495430Z", "iopub.status.busy": "2022-12-07T16:50:19.494886Z", "iopub.status.idle": "2022-12-07T16:50:21.704082Z", "shell.execute_reply": "2022-12-07T16:50:21.703244Z"}, "origin_pos": 2, "tab": ["pytorch"]}, "outputs": [], "source": ["import os\n", "import random\n", "import torch\n", "from d2l import torch as d2l"]}, {"cell_type": "markdown", "id": "577aab19", "metadata": {"origin_pos": 4}, "source": ["在WikiText-2数据集中，每行代表一个段落，其中在任意标点符号及其前面的词元之间插入空格。保留至少有两句话的段落。为了简单起见，我们仅使用句号作为分隔符来拆分句子。我们将更复杂的句子拆分技术的讨论留在本节末尾的练习中。\n"]}, {"cell_type": "code", "execution_count": 2, "id": "f3e015c6", "metadata": {"execution": {"iopub.execute_input": "2022-12-07T16:50:21.708013Z", "iopub.status.busy": "2022-12-07T16:50:21.707648Z", "iopub.status.idle": "2022-12-07T16:50:21.713395Z", "shell.execute_reply": "2022-12-07T16:50:21.712660Z"}, "origin_pos": 5, "tab": ["pytorch"]}, "outputs": [], "source": ["#@save\n", "d2l.DATA_HUB['wikitext-2'] = (\n", "    'https://s3.amazonaws.com/research.metamind.io/wikitext/'\n", "    'wikitext-2-v1.zip', '3c914d17d80b1459be871a5039ac23e752a53cbe')\n", "\n", "#@save\n", "def _read_wiki(data_dir):\n", "    file_name = os.path.join(data_dir, 'wiki.train.tokens')\n", "    with open(file_name, 'r') as f:\n", "        lines = f.readlines()\n", "    # 大写字母转换为小写字母\n", "    paragraphs = [line.strip().lower().split(' . ')\n", "                  for line in lines if len(line.split(' . ')) >= 2]\n", "    random.shuffle(paragraphs)\n", "    return paragraphs"]}, {"cell_type": "markdown", "id": "73ce56ad", "metadata": {"origin_pos": 6}, "source": ["## 为预训练任务定义辅助函数\n", "\n", "在下文中，我们首先为BERT的两个预训练任务实现辅助函数。这些辅助函数将在稍后将原始文本语料库转换为理想格式的数据集时调用，以预训练BERT。\n", "\n", "### 生成下一句预测任务的数据\n", "\n", "根据 :numref:`subsec_nsp`的描述，`_get_next_sentence`函数生成二分类任务的训练样本。\n"]}, {"cell_type": "code", "execution_count": 3, "id": "c1a3c8d7", "metadata": {"execution": {"iopub.execute_input": "2022-12-07T16:50:21.716296Z", "iopub.status.busy": "2022-12-07T16:50:21.716031Z", "iopub.status.idle": "2022-12-07T16:50:21.720528Z", "shell.execute_reply": "2022-12-07T16:50:21.719804Z"}, "origin_pos": 7, "tab": ["pytorch"]}, "outputs": [], "source": ["#@save\n", "def _get_next_sentence(sentence, next_sentence, paragraphs):\n", "    if random.random() < 0.5:\n", "        is_next = True\n", "    else:\n", "        # paragraphs是三重列表的嵌套\n", "        next_sentence = random.choice(random.choice(paragraphs))\n", "        is_next = False\n", "    return sentence, next_sentence, is_next"]}, {"cell_type": "markdown", "id": "640c4c7d", "metadata": {"origin_pos": 8}, "source": ["下面的函数通过调用`_get_next_sentence`函数从输入`paragraph`生成用于下一句预测的训练样本。这里`paragraph`是句子列表，其中每个句子都是词元列表。自变量`max_len`指定预训练期间的BERT输入序列的最大长度。\n"]}, {"cell_type": "code", "execution_count": 4, "id": "ce68a0db", "metadata": {"execution": {"iopub.execute_input": "2022-12-07T16:50:21.723438Z", "iopub.status.busy": "2022-12-07T16:50:21.723174Z", "iopub.status.idle": "2022-12-07T16:50:21.728460Z", "shell.execute_reply": "2022-12-07T16:50:21.727712Z"}, "origin_pos": 9, "tab": ["pytorch"]}, "outputs": [], "source": ["#@save\n", "def _get_nsp_data_from_paragraph(paragraph, paragraphs, vocab, max_len):\n", "    nsp_data_from_paragraph = []\n", "    for i in range(len(paragraph) - 1):\n", "        tokens_a, tokens_b, is_next = _get_next_sentence(\n", "            paragraph[i], paragraph[i + 1], paragraphs)\n", "        # 考虑1个'<cls>'词元和2个'<sep>'词元\n", "        if len(tokens_a) + len(tokens_b) + 3 > max_len:\n", "            continue\n", "        tokens, segments = d2l.get_tokens_and_segments(tokens_a, tokens_b)\n", "        nsp_data_from_paragraph.append((tokens, segments, is_next))\n", "    return nsp_data_from_paragraph"]}, {"cell_type": "markdown", "id": "135620f5", "metadata": {"origin_pos": 10}, "source": ["### 生成遮蔽语言模型任务的数据\n", ":label:`subsec_prepare_mlm_data`\n", "\n", "为了从BERT输入序列生成遮蔽语言模型的训练样本，我们定义了以下`_replace_mlm_tokens`函数。在其输入中，`tokens`是表示BERT输入序列的词元的列表，`candidate_pred_positions`是不包括特殊词元的BERT输入序列的词元索引的列表（特殊词元在遮蔽语言模型任务中不被预测），以及`num_mlm_preds`指示预测的数量（选择15%要预测的随机词元）。在 :numref:`subsec_mlm`中定义遮蔽语言模型任务之后，在每个预测位置，输入可以由特殊的“掩码”词元或随机词元替换，或者保持不变。最后，该函数返回可能替换后的输入词元、发生预测的词元索引和这些预测的标签。\n"]}, {"cell_type": "code", "execution_count": 5, "id": "306bf20b", "metadata": {"execution": {"iopub.execute_input": "2022-12-07T16:50:21.731449Z", "iopub.status.busy": "2022-12-07T16:50:21.731183Z", "iopub.status.idle": "2022-12-07T16:50:21.737423Z", "shell.execute_reply": "2022-12-07T16:50:21.736641Z"}, "origin_pos": 11, "tab": ["pytorch"]}, "outputs": [], "source": ["#@save\n", "def _replace_mlm_tokens(tokens, candidate_pred_positions, num_mlm_preds,\n", "                        vocab):\n", "    # 为遮蔽语言模型的输入创建新的词元副本，其中输入可能包含替换的“<mask>”或随机词元\n", "    mlm_input_tokens = [token for token in tokens]\n", "    pred_positions_and_labels = []\n", "    # 打乱后用于在遮蔽语言模型任务中获取15%的随机词元进行预测\n", "    random.shuffle(candidate_pred_positions)\n", "    for mlm_pred_position in candidate_pred_positions:\n", "        if len(pred_positions_and_labels) >= num_mlm_preds:\n", "            break\n", "        masked_token = None\n", "        # 80%的时间：将词替换为“<mask>”词元\n", "        if random.random() < 0.8:\n", "            masked_token = '<mask>'\n", "        else:\n", "            # 10%的时间：保持词不变\n", "            if random.random() < 0.5:\n", "                masked_token = tokens[mlm_pred_position]\n", "            # 10%的时间：用随机词替换该词\n", "            else:\n", "                masked_token = random.choice(vocab.idx_to_token)\n", "        mlm_input_tokens[mlm_pred_position] = masked_token\n", "        pred_positions_and_labels.append(\n", "            (mlm_pred_position, tokens[mlm_pred_position]))\n", "    return mlm_input_tokens, pred_positions_and_labels"]}, {"cell_type": "markdown", "id": "05a2caef", "metadata": {"origin_pos": 12}, "source": ["通过调用前述的`_replace_mlm_tokens`函数，以下函数将BERT输入序列（`tokens`）作为输入，并返回输入词元的索引（在 :numref:`subsec_mlm`中描述的可能的词元替换之后）、发生预测的词元索引以及这些预测的标签索引。\n"]}, {"cell_type": "code", "execution_count": 6, "id": "504db850", "metadata": {"execution": {"iopub.execute_input": "2022-12-07T16:50:21.740412Z", "iopub.status.busy": "2022-12-07T16:50:21.740144Z", "iopub.status.idle": "2022-12-07T16:50:21.746275Z", "shell.execute_reply": "2022-12-07T16:50:21.745472Z"}, "origin_pos": 13, "tab": ["pytorch"]}, "outputs": [], "source": ["#@save\n", "def _get_mlm_data_from_tokens(tokens, vocab):\n", "    candidate_pred_positions = []\n", "    # tokens是一个字符串列表\n", "    for i, token in enumerate(tokens):\n", "        # 在遮蔽语言模型任务中不会预测特殊词元\n", "        if token in ['<cls>', '<sep>']:\n", "            continue\n", "        candidate_pred_positions.append(i)\n", "    # 遮蔽语言模型任务中预测15%的随机词元\n", "    num_mlm_preds = max(1, round(len(tokens) * 0.15))\n", "    mlm_input_tokens, pred_positions_and_labels = _replace_mlm_tokens(\n", "        tokens, candidate_pred_positions, num_mlm_preds, vocab)\n", "    pred_positions_and_labels = sorted(pred_positions_and_labels,\n", "                                       key=lambda x: x[0])\n", "    pred_positions = [v[0] for v in pred_positions_and_labels]\n", "    mlm_pred_labels = [v[1] for v in pred_positions_and_labels]\n", "    return vocab[mlm_input_tokens], pred_positions, vocab[mlm_pred_labels]"]}, {"cell_type": "markdown", "id": "19299cdb", "metadata": {"origin_pos": 14}, "source": ["## 将文本转换为预训练数据集\n", "\n", "现在我们几乎准备好为BERT预训练定制一个`Dataset`类。在此之前，我们仍然需要定义辅助函数`_pad_bert_inputs`来将特殊的“&lt;mask&gt;”词元附加到输入。它的参数`examples`包含来自两个预训练任务的辅助函数`_get_nsp_data_from_paragraph`和`_get_mlm_data_from_tokens`的输出。\n"]}, {"cell_type": "code", "execution_count": 7, "id": "d067db09", "metadata": {"execution": {"iopub.execute_input": "2022-12-07T16:50:21.749302Z", "iopub.status.busy": "2022-12-07T16:50:21.749036Z", "iopub.status.idle": "2022-12-07T16:50:21.757343Z", "shell.execute_reply": "2022-12-07T16:50:21.756618Z"}, "origin_pos": 16, "tab": ["pytorch"]}, "outputs": [], "source": ["#@save\n", "def _pad_bert_inputs(examples, max_len, vocab):\n", "    max_num_mlm_preds = round(max_len * 0.15)\n", "    all_token_ids, all_segments, valid_lens,  = [], [], []\n", "    all_pred_positions, all_mlm_weights, all_mlm_labels = [], [], []\n", "    nsp_labels = []\n", "    for (token_ids, pred_positions, mlm_pred_label_ids, segments,\n", "         is_next) in examples:\n", "        all_token_ids.append(torch.tensor(token_ids + [vocab['<pad>']] * (\n", "            max_len - len(token_ids)), dtype=torch.long))\n", "        all_segments.append(torch.tensor(segments + [0] * (\n", "            max_len - len(segments)), dtype=torch.long))\n", "        # valid_lens不包括'<pad>'的计数\n", "        valid_lens.append(torch.tensor(len(token_ids), dtype=torch.float32))\n", "        all_pred_positions.append(torch.tensor(pred_positions + [0] * (\n", "            max_num_mlm_preds - len(pred_positions)), dtype=torch.long))\n", "        # 填充词元的预测将通过乘以0权重在损失中过滤掉\n", "        all_mlm_weights.append(\n", "            torch.tensor([1.0] * len(mlm_pred_label_ids) + [0.0] * (\n", "                max_num_mlm_preds - len(pred_positions)),\n", "                dtype=torch.float32))\n", "        all_mlm_labels.append(torch.tensor(mlm_pred_label_ids + [0] * (\n", "            max_num_mlm_preds - len(mlm_pred_label_ids)), dtype=torch.long))\n", "        nsp_labels.append(torch.tensor(is_next, dtype=torch.long))\n", "    return (all_token_ids, all_segments, valid_lens, all_pred_positions,\n", "            all_mlm_weights, all_mlm_labels, nsp_labels)"]}, {"cell_type": "markdown", "id": "18399a4e", "metadata": {"origin_pos": 18}, "source": ["将用于生成两个预训练任务的训练样本的辅助函数和用于填充输入的辅助函数放在一起，我们定义以下`_WikiTextDataset`类为用于预训练BERT的WikiText-2数据集。通过实现`__getitem__ `函数，我们可以任意访问WikiText-2语料库的一对句子生成的预训练样本（遮蔽语言模型和下一句预测）样本。\n", "\n", "最初的BERT模型使用词表大小为30000的WordPiece嵌入 :cite:`Wu.Schuster.Chen.ea.2016`。WordPiece的词元化方法是对 :numref:`subsec_Byte_Pair_Encoding`中原有的字节对编码算法稍作修改。为简单起见，我们使用`d2l.tokenize`函数进行词元化。出现次数少于5次的不频繁词元将被过滤掉。\n"]}, {"cell_type": "code", "execution_count": 8, "id": "900343bf", "metadata": {"execution": {"iopub.execute_input": "2022-12-07T16:50:21.760590Z", "iopub.status.busy": "2022-12-07T16:50:21.760019Z", "iopub.status.idle": "2022-12-07T16:50:21.767859Z", "shell.execute_reply": "2022-12-07T16:50:21.767094Z"}, "origin_pos": 20, "tab": ["pytorch"]}, "outputs": [], "source": ["#@save\n", "class _WikiTextDataset(torch.utils.data.Dataset):\n", "    def __init__(self, paragraphs, max_len):\n", "        # 输入paragraphs[i]是代表段落的句子字符串列表；\n", "        # 而输出paragraphs[i]是代表段落的句子列表，其中每个句子都是词元列表\n", "        paragraphs = [d2l.tokenize(\n", "            paragraph, token='word') for paragraph in paragraphs]\n", "        sentences = [sentence for paragraph in paragraphs\n", "                     for sentence in paragraph]\n", "        self.vocab = d2l.Vocab(sentences, min_freq=5, reserved_tokens=[\n", "            '<pad>', '<mask>', '<cls>', '<sep>'])\n", "        # 获取下一句子预测任务的数据\n", "        examples = []\n", "        for paragraph in paragraphs:\n", "            examples.extend(_get_nsp_data_from_paragraph(\n", "                paragraph, paragraphs, self.vocab, max_len))\n", "        # 获取遮蔽语言模型任务的数据\n", "        examples = [(_get_mlm_data_from_tokens(tokens, self.vocab)\n", "                      + (segments, is_next))\n", "                     for tokens, segments, is_next in examples]\n", "        # 填充输入\n", "        (self.all_token_ids, self.all_segments, self.valid_lens,\n", "         self.all_pred_positions, self.all_mlm_weights,\n", "         self.all_mlm_labels, self.nsp_labels) = _pad_bert_inputs(\n", "            examples, max_len, self.vocab)\n", "\n", "    def __getitem__(self, idx):\n", "        return (self.all_token_ids[idx], self.all_segments[idx],\n", "                self.valid_lens[idx], self.all_pred_positions[idx],\n", "                self.all_mlm_weights[idx], self.all_mlm_labels[idx],\n", "                self.nsp_labels[idx])\n", "\n", "    def __len__(self):\n", "        return len(self.all_token_ids)"]}, {"cell_type": "markdown", "id": "6a9498ed", "metadata": {"origin_pos": 22}, "source": ["通过使用`_read_wiki`函数和`_WikiTextDataset`类，我们定义了下面的`load_data_wiki`来下载并生成WikiText-2数据集，并从中生成预训练样本。\n"]}, {"cell_type": "code", "execution_count": 9, "id": "3e497130", "metadata": {"execution": {"iopub.execute_input": "2022-12-07T16:50:21.770794Z", "iopub.status.busy": "2022-12-07T16:50:21.770510Z", "iopub.status.idle": "2022-12-07T16:50:21.775969Z", "shell.execute_reply": "2022-12-07T16:50:21.775213Z"}, "origin_pos": 24, "tab": ["pytorch"]}, "outputs": [], "source": ["#@save\n", "def load_data_wiki(batch_size, max_len):\n", "    \"\"\"加载WikiText-2数据集\"\"\"\n", "    num_workers = d2l.get_dataloader_workers()\n", "    data_dir = d2l.download_extract('wikitext-2', 'wikitext-2')\n", "    paragraphs = _read_wiki(data_dir)\n", "    train_set = _WikiTextDataset(paragraphs, max_len)\n", "    train_iter = torch.utils.data.DataLoader(train_set, batch_size,\n", "                                        shuffle=True, num_workers=num_workers)\n", "    return train_iter, train_set.vocab"]}, {"cell_type": "markdown", "id": "301a29cb", "metadata": {"origin_pos": 26}, "source": ["将批量大小设置为512，将BERT输入序列的最大长度设置为64，我们打印出小批量的BERT预训练样本的形状。注意，在每个BERT输入序列中，为遮蔽语言模型任务预测$10$（$64 \\times 0.15$）个位置。\n"]}, {"cell_type": "code", "execution_count": 10, "id": "9a4350c0", "metadata": {"execution": {"iopub.execute_input": "2022-12-07T16:50:21.778890Z", "iopub.status.busy": "2022-12-07T16:50:21.778601Z", "iopub.status.idle": "2022-12-07T16:50:29.047567Z", "shell.execute_reply": "2022-12-07T16:50:29.045934Z"}, "origin_pos": 27, "tab": ["pytorch"]}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["torch.<PERSON><PERSON>([512, 64]) torch.<PERSON><PERSON>([512, 64]) torch.<PERSON><PERSON>([512]) torch.<PERSON><PERSON>([512, 10]) torch.<PERSON><PERSON>([512, 10]) torch.<PERSON><PERSON>([512, 10]) torch.<PERSON><PERSON>([512])\n"]}], "source": ["batch_size, max_len = 512, 64\n", "train_iter, vocab = load_data_wiki(batch_size, max_len)\n", "\n", "for (tokens_X, segments_X, valid_lens_x, pred_positions_X, mlm_weights_X,\n", "     mlm_Y, nsp_y) in train_iter:\n", "    print(tokens_X.shape, segments_X.shape, valid_lens_x.shape,\n", "          pred_positions_X.shape, mlm_weights_X.shape, mlm_Y.shape,\n", "          nsp_y.shape)\n", "    break"]}, {"cell_type": "markdown", "id": "6f77b724", "metadata": {"origin_pos": 28}, "source": ["最后，我们来看一下词量。即使在过滤掉不频繁的词元之后，它仍然比PTB数据集的大两倍以上。\n"]}, {"cell_type": "code", "execution_count": 11, "id": "040d7d8a", "metadata": {"execution": {"iopub.execute_input": "2022-12-07T16:50:29.051736Z", "iopub.status.busy": "2022-12-07T16:50:29.051221Z", "iopub.status.idle": "2022-12-07T16:50:29.060916Z", "shell.execute_reply": "2022-12-07T16:50:29.059784Z"}, "origin_pos": 29, "tab": ["pytorch"]}, "outputs": [{"data": {"text/plain": ["20256"]}, "execution_count": 11, "metadata": {}, "output_type": "execute_result"}], "source": ["len(vocab)"]}, {"cell_type": "markdown", "id": "8b5b0607", "metadata": {"origin_pos": 30}, "source": ["## 小结\n", "\n", "* 与PTB数据集相比，WikiText-2数据集保留了原来的标点符号、大小写和数字，并且比PTB数据集大了两倍多。\n", "* 我们可以任意访问从WikiText-2语料库中的一对句子生成的预训练（遮蔽语言模型和下一句预测）样本。\n", "\n", "## 练习\n", "\n", "1. 为简单起见，句号用作拆分句子的唯一分隔符。尝试其他的句子拆分技术，比如Spacy和NLTK。以NLTK为例，需要先安装NLTK：`pip install nltk`。在代码中先`import nltk`。然后下载Punkt语句词元分析器：`nltk.download('punkt')`。要拆分句子，比如`sentences = 'This is great ! Why not ?'`，调用`nltk.tokenize.sent_tokenize(sentences)`将返回两个句子字符串的列表：`['This is great !', 'Why not ?']`。\n", "1. 如果我们不过滤出一些不常见的词元，词量会有多大？\n"]}, {"cell_type": "markdown", "id": "9ec04485", "metadata": {"origin_pos": 32, "tab": ["pytorch"]}, "source": ["[Discussions](https://discuss.d2l.ai/t/5738)\n"]}], "metadata": {"language_info": {"name": "python"}}, "nbformat": 4, "nbformat_minor": 5}