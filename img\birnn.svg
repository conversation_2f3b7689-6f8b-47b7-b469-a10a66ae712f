<?xml version="1.0" encoding="UTF-8"?>
<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="240pt" height="141pt" viewBox="0 0 240 141" version="1.1">
<defs>
<g>
<symbol overflow="visible" id="glyph0-0">
<path style="stroke:none;" d=""/>
</symbol>
<symbol overflow="visible" id="glyph0-1">
<path style="stroke:none;" d="M 6.828125 0 L 6.828125 -0.21875 C 6.1875 -0.3125 6.03125 -0.40625 6.03125 -0.875 L 6.03125 -5.1875 C 6.03125 -5.671875 6.21875 -5.8125 6.828125 -5.859375 L 6.828125 -6.078125 L 3.78125 -6.078125 L 3.78125 -5.859375 C 4.40625 -5.8125 4.578125 -5.703125 4.578125 -5.1875 L 4.578125 -3.359375 L 2.40625 -3.359375 L 2.40625 -5.1875 C 2.40625 -5.703125 2.578125 -5.8125 3.21875 -5.859375 L 3.21875 -6.078125 L 0.1875 -6.078125 L 0.1875 -5.859375 C 0.796875 -5.8125 0.953125 -5.6875 0.953125 -5.1875 L 0.953125 -0.875 C 0.953125 -0.40625 0.8125 -0.3125 0.1875 -0.21875 L 0.1875 0 L 3.21875 0 L 3.21875 -0.21875 C 2.5625 -0.296875 2.40625 -0.40625 2.40625 -0.875 L 2.40625 -2.9375 L 4.578125 -2.9375 L 4.578125 -0.875 C 4.578125 -0.421875 4.4375 -0.296875 3.78125 -0.21875 L 3.78125 0 Z M 6.828125 0 "/>
</symbol>
<symbol overflow="visible" id="glyph0-2">
<path style="stroke:none;" d="M 6.296875 0 L 6.296875 -0.21875 C 5.984375 -0.234375 5.84375 -0.34375 5.421875 -1 L 3.78125 -3.546875 L 4.5 -4.546875 C 5.28125 -5.640625 5.515625 -5.796875 6.1875 -5.859375 L 6.1875 -6.078125 L 3.9375 -6.078125 L 3.9375 -5.859375 L 4.125 -5.84375 C 4.46875 -5.8125 4.59375 -5.734375 4.59375 -5.53125 C 4.59375 -5.328125 4.484375 -5.15625 4.0625 -4.578125 L 3.5625 -3.875 L 2.6875 -5.234375 C 2.578125 -5.40625 2.5625 -5.453125 2.5625 -5.5625 C 2.5625 -5.75 2.65625 -5.828125 2.984375 -5.84375 L 3.265625 -5.859375 L 3.265625 -6.078125 L 0.15625 -6.078125 L 0.15625 -5.859375 C 0.484375 -5.828125 0.59375 -5.734375 0.875 -5.34375 L 2.65625 -2.71875 L 1.078125 -0.734375 C 0.8125 -0.390625 0.59375 -0.28125 0.140625 -0.21875 L 0.140625 0 L 2.390625 0 L 2.390625 -0.21875 C 1.84375 -0.28125 1.671875 -0.375 1.671875 -0.609375 C 1.671875 -0.796875 1.84375 -1.078125 2.515625 -1.984375 L 2.859375 -2.4375 L 3.765625 -0.984375 C 3.875 -0.8125 3.953125 -0.609375 3.953125 -0.5 C 3.953125 -0.34375 3.796875 -0.265625 3.484375 -0.25 L 3.234375 -0.21875 L 3.234375 0 Z M 6.296875 0 "/>
</symbol>
<symbol overflow="visible" id="glyph0-3">
<path style="stroke:none;" d="M 6.6875 -3.015625 C 6.6875 -4.875 5.359375 -6.21875 3.546875 -6.21875 C 1.671875 -6.21875 0.3125 -4.921875 0.3125 -3 C 0.3125 -1.140625 1.625 0.171875 3.484375 0.171875 C 5.359375 0.171875 6.6875 -1.140625 6.6875 -3.015625 Z M 5.09375 -2.953125 C 5.09375 -1.09375 4.546875 -0.125 3.515625 -0.125 C 2.484375 -0.125 1.90625 -1.078125 1.90625 -2.96875 C 1.90625 -4.859375 2.46875 -5.921875 3.46875 -5.921875 C 4.515625 -5.921875 5.09375 -4.875 5.09375 -2.953125 Z M 5.09375 -2.953125 "/>
</symbol>
<symbol overflow="visible" id="glyph1-0">
<path style="stroke:none;" d=""/>
</symbol>
<symbol overflow="visible" id="glyph1-1">
<path style="stroke:none;" d="M 0.359375 -3.0625 C 0.609375 -3.5 0.890625 -3.703125 1.265625 -3.703125 C 1.65625 -3.703125 1.90625 -3.484375 1.90625 -3.078125 C 1.90625 -2.71875 1.71875 -2.453125 1.421875 -2.28125 C 1.296875 -2.203125 1.140625 -2.140625 0.921875 -2.0625 L 0.921875 -1.984375 C 1.265625 -1.984375 1.390625 -1.96875 1.53125 -1.921875 C 1.9375 -1.796875 2.15625 -1.5 2.15625 -1.046875 C 2.15625 -0.546875 1.8125 -0.125 1.375 -0.125 C 1.203125 -0.125 1.078125 -0.15625 0.859375 -0.3125 C 0.6875 -0.4375 0.59375 -0.46875 0.484375 -0.46875 C 0.359375 -0.46875 0.25 -0.390625 0.25 -0.265625 C 0.25 -0.046875 0.484375 0.078125 0.921875 0.078125 C 1.453125 0.078125 2.015625 -0.09375 2.3125 -0.46875 C 2.484375 -0.703125 2.59375 -1 2.59375 -1.3125 C 2.59375 -1.625 2.484375 -1.90625 2.328125 -2.09375 C 2.203125 -2.21875 2.09375 -2.296875 1.828125 -2.40625 C 2.21875 -2.640625 2.375 -2.953125 2.375 -3.234375 C 2.375 -3.71875 2 -4.0625 1.453125 -4.0625 C 0.828125 -4.0625 0.4375 -3.65625 0.265625 -3.078125 Z M 0.359375 -3.0625 "/>
</symbol>
<symbol overflow="visible" id="glyph1-2">
<path style="stroke:none;" d="M 2.84375 -0.828125 L 2.765625 -0.859375 C 2.5625 -0.515625 2.4375 -0.453125 2.1875 -0.453125 L 0.78125 -0.453125 L 1.765625 -1.515625 C 2.296875 -2.078125 2.53125 -2.53125 2.53125 -3 C 2.53125 -3.59375 2.109375 -4.0625 1.421875 -4.0625 C 0.6875 -4.0625 0.3125 -3.5625 0.1875 -2.859375 L 0.3125 -2.828125 C 0.546875 -3.421875 0.75 -3.609375 1.1875 -3.609375 C 1.703125 -3.609375 2.015625 -3.3125 2.015625 -2.765625 C 2.015625 -2.25 1.8125 -1.796875 1.234375 -1.203125 L 0.171875 -0.078125 L 0.171875 0 L 2.515625 0 Z M 2.84375 -0.828125 "/>
</symbol>
<symbol overflow="visible" id="glyph1-3">
<path style="stroke:none;" d="M 2.359375 0 L 2.359375 -0.09375 C 1.90625 -0.09375 1.796875 -0.203125 1.796875 -0.453125 L 1.796875 -4.03125 L 1.734375 -4.0625 L 0.671875 -3.515625 L 0.671875 -3.421875 L 0.828125 -3.484375 C 0.9375 -3.53125 1.03125 -3.5625 1.09375 -3.5625 C 1.21875 -3.5625 1.28125 -3.46875 1.28125 -3.265625 L 1.28125 -0.5625 C 1.28125 -0.234375 1.15625 -0.109375 0.703125 -0.09375 L 0.703125 0 Z M 2.359375 0 "/>
</symbol>
<symbol overflow="visible" id="glyph2-0">
<path style="stroke:none;" d=""/>
</symbol>
<symbol overflow="visible" id="glyph2-1">
<path style="stroke:none;" d="M 3.796875 -3.921875 L 0.609375 -3.921875 L 0.359375 -2.984375 L 0.46875 -2.96875 C 0.75 -3.609375 0.9375 -3.703125 1.890625 -3.703125 L 1.03125 -0.546875 C 0.9375 -0.21875 0.796875 -0.125 0.390625 -0.09375 L 0.390625 0 L 2.125 0 L 2.125 -0.09375 L 1.90625 -0.109375 C 1.65625 -0.140625 1.59375 -0.1875 1.59375 -0.375 C 1.59375 -0.5 1.640625 -0.640625 1.671875 -0.765625 L 2.5 -3.703125 L 2.84375 -3.703125 C 3.234375 -3.703125 3.453125 -3.546875 3.453125 -3.21875 C 3.453125 -3.140625 3.4375 -3.046875 3.4375 -2.953125 L 3.53125 -2.9375 Z M 3.796875 -3.921875 "/>
</symbol>
<symbol overflow="visible" id="glyph3-0">
<path style="stroke:none;" d=""/>
</symbol>
<symbol overflow="visible" id="glyph3-1">
<path style="stroke:none;" d="M 6.859375 -0.390625 C 6.859375 -0.671875 6.625 -0.90625 6.359375 -0.90625 C 6.078125 -0.90625 5.859375 -0.671875 5.859375 -0.390625 C 5.859375 -0.125 6.078125 0.09375 6.34375 0.09375 C 6.625 0.09375 6.859375 -0.109375 6.859375 -0.390625 Z M 4.1875 -0.390625 C 4.1875 -0.671875 3.953125 -0.90625 3.6875 -0.90625 C 3.40625 -0.90625 3.1875 -0.671875 3.1875 -0.390625 C 3.1875 -0.125 3.40625 0.09375 3.65625 0.09375 C 3.953125 0.09375 4.1875 -0.109375 4.1875 -0.390625 Z M 1.515625 -0.390625 C 1.515625 -0.671875 1.28125 -0.90625 1.015625 -0.90625 C 0.734375 -0.90625 0.515625 -0.671875 0.515625 -0.390625 C 0.515625 -0.125 0.734375 0.09375 0.984375 0.09375 C 1.28125 0.09375 1.515625 -0.109375 1.515625 -0.390625 Z M 1.515625 -0.390625 "/>
</symbol>
</g>
</defs>
<g id="surface1">
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 203.898438 516.398438 L 227.898438 516.398438 L 227.898438 540.398438 L 203.898438 540.398438 Z M 203.898438 516.398438 " transform="matrix(1,0,0,1,-80,-439)"/>
<g style="fill:rgb(0%,0%,0%);fill-opacity:1;">
  <use xlink:href="#glyph0-1" x="130.899" y="92.4"/>
</g>
<g style="fill:rgb(0%,0%,0%);fill-opacity:1;">
  <use xlink:href="#glyph1-1" x="137.901" y="95.4"/>
</g>
<path style="fill-rule:nonzero;fill:rgb(69.804382%,85.098267%,100%);fill-opacity:1;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 150.765625 554.601562 L 174.765625 554.601562 L 174.765625 578.601562 L 150.765625 578.601562 Z M 150.765625 554.601562 " transform="matrix(1,0,0,1,-80,-439)"/>
<g style="fill:rgb(0%,0%,0%);fill-opacity:1;">
  <use xlink:href="#glyph0-2" x="78.0149" y="128.6"/>
</g>
<g style="fill:rgb(0%,0%,0%);fill-opacity:1;">
  <use xlink:href="#glyph1-2" x="84.5129" y="132.6"/>
</g>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 150.765625 478.199219 L 174.765625 478.199219 L 174.765625 502.199219 L 150.765625 502.199219 Z M 150.765625 478.199219 " transform="matrix(1,0,0,1,-80,-439)"/>
<g style="fill:rgb(0%,0%,0%);fill-opacity:1;">
  <use xlink:href="#glyph0-1" x="77.7629" y="54.2"/>
</g>
<g style="fill:rgb(0%,0%,0%);fill-opacity:1;">
  <use xlink:href="#glyph1-2" x="84.7649" y="57.2"/>
</g>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 150.765625 516.398438 L 174.765625 516.398438 L 174.765625 540.398438 L 150.765625 540.398438 Z M 150.765625 516.398438 " transform="matrix(1,0,0,1,-80,-439)"/>
<g style="fill:rgb(0%,0%,0%);fill-opacity:1;">
  <use xlink:href="#glyph0-1" x="77.7629" y="92.4"/>
</g>
<g style="fill:rgb(0%,0%,0%);fill-opacity:1;">
  <use xlink:href="#glyph1-2" x="84.7649" y="95.4"/>
</g>
<path style="fill:none;stroke-width:0.5;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 157.746094 522.601562 L 162.546875 522.601562 " transform="matrix(1,0,0,1,-80,-439)"/>
<path style="fill:none;stroke-width:0.5;stroke-linecap:butt;stroke-linejoin:miter;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 164.546875 522.601562 L 162.546875 522.601562 M 162.546875 521.851562 L 164.546875 522.601562 L 162.546875 523.351562 " transform="matrix(1,0,0,1,-80,-439)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 151.089844 554.601562 C 146.78125 549.035156 142.582031 541.878906 141.078125 533.867188 M 140.867188 522.914062 C 141.78125 516.875 144.296875 511.390625 147.3125 506.710938 " transform="matrix(1,0,0,1,-80,-439)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:butt;stroke-linejoin:miter;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 149.652344 503.464844 L 147.316406 506.710938 M 146.097656 505.832031 L 149.652344 503.464844 L 148.53125 507.585938 " transform="matrix(1,0,0,1,-80,-439)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 162.765625 554.601562 L 162.765625 546.300781 " transform="matrix(1,0,0,1,-80,-439)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:butt;stroke-linejoin:miter;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 162.765625 542.300781 L 162.765625 546.300781 M 161.265625 546.300781 L 162.765625 542.300781 L 164.265625 546.300781 " transform="matrix(1,0,0,1,-80,-439)"/>
<path style="fill-rule:nonzero;fill:rgb(69.804382%,85.098267%,100%);fill-opacity:1;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 150.765625 440 L 174.765625 440 L 174.765625 464 L 150.765625 464 Z M 150.765625 440 " transform="matrix(1,0,0,1,-80,-439)"/>
<g style="fill:rgb(0%,0%,0%);fill-opacity:1;">
  <use xlink:href="#glyph0-3" x="77.7629" y="14.5"/>
</g>
<g style="fill:rgb(0%,0%,0%);fill-opacity:1;">
  <use xlink:href="#glyph1-2" x="84.7649" y="17.5"/>
</g>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 162.765625 478.199219 L 162.765625 469.898438 " transform="matrix(1,0,0,1,-80,-439)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:butt;stroke-linejoin:miter;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 162.765625 465.898438 L 162.765625 469.898438 M 161.265625 469.898438 L 162.765625 465.898438 L 164.265625 469.898438 " transform="matrix(1,0,0,1,-80,-439)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 174.765625 523.332031 C 180.652344 519.476562 186.5 512.941406 186.5 502.199219 C 186.5 500.035156 186.261719 497.804688 185.835938 495.535156 M 182.707031 485.007812 C 180.492188 479.367188 177.566406 473.847656 174.636719 468.992188 " transform="matrix(1,0,0,1,-80,-439)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:butt;stroke-linejoin:miter;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 172.503906 465.605469 L 174.636719 468.992188 M 173.367188 469.792969 L 172.503906 465.605469 L 175.90625 468.191406 " transform="matrix(1,0,0,1,-80,-439)"/>
<path style="fill-rule:nonzero;fill:rgb(69.804382%,85.098267%,100%);fill-opacity:1;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 203.898438 554.601562 L 227.898438 554.601562 L 227.898438 578.601562 L 203.898438 578.601562 Z M 203.898438 554.601562 " transform="matrix(1,0,0,1,-80,-439)"/>
<g style="fill:rgb(0%,0%,0%);fill-opacity:1;">
  <use xlink:href="#glyph0-2" x="131.151" y="128.6"/>
</g>
<g style="fill:rgb(0%,0%,0%);fill-opacity:1;">
  <use xlink:href="#glyph1-1" x="137.649" y="132.6"/>
</g>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 203.898438 478.199219 L 227.898438 478.199219 L 227.898438 502.199219 L 203.898438 502.199219 Z M 203.898438 478.199219 " transform="matrix(1,0,0,1,-80,-439)"/>
<g style="fill:rgb(0%,0%,0%);fill-opacity:1;">
  <use xlink:href="#glyph0-1" x="130.899" y="54.2"/>
</g>
<g style="fill:rgb(0%,0%,0%);fill-opacity:1;">
  <use xlink:href="#glyph1-1" x="137.901" y="57.2"/>
</g>
<path style="fill:none;stroke-width:0.5;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 217.25 484.449219 L 212.449219 484.449219 " transform="matrix(1,0,0,1,-80,-439)"/>
<path style="fill:none;stroke-width:0.5;stroke-linecap:butt;stroke-linejoin:miter;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 210.449219 484.449219 L 212.449219 484.449219 M 212.449219 485.199219 L 210.449219 484.449219 L 212.449219 483.699219 " transform="matrix(1,0,0,1,-80,-439)"/>
<path style="fill:none;stroke-width:0.5;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 210.398438 522.601562 L 215.199219 522.601562 " transform="matrix(1,0,0,1,-80,-439)"/>
<path style="fill:none;stroke-width:0.5;stroke-linecap:butt;stroke-linejoin:miter;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 217.199219 522.601562 L 215.199219 522.601562 M 215.199219 521.851562 L 217.199219 522.601562 L 215.199219 523.351562 " transform="matrix(1,0,0,1,-80,-439)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 204.941406 554.601562 C 200.898438 549.035156 196.957031 541.878906 195.546875 533.875 M 195.34375 522.910156 C 196.179688 517.042969 198.433594 511.699219 201.15625 507.109375 " transform="matrix(1,0,0,1,-80,-439)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:butt;stroke-linejoin:miter;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 203.375 503.78125 L 201.15625 507.109375 M 199.910156 506.277344 L 203.375 503.78125 L 202.40625 507.941406 " transform="matrix(1,0,0,1,-80,-439)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 215.898438 554.601562 L 215.898438 546.300781 " transform="matrix(1,0,0,1,-80,-439)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:butt;stroke-linejoin:miter;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 215.898438 542.300781 L 215.898438 546.300781 M 214.398438 546.300781 L 215.898438 542.300781 L 217.398438 546.300781 " transform="matrix(1,0,0,1,-80,-439)"/>
<path style="fill-rule:nonzero;fill:rgb(69.804382%,85.098267%,100%);fill-opacity:1;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 204.433594 440 L 228.433594 440 L 228.433594 464 L 204.433594 464 Z M 204.433594 440 " transform="matrix(1,0,0,1,-80,-439)"/>
<g style="fill:rgb(0%,0%,0%);fill-opacity:1;">
  <use xlink:href="#glyph0-3" x="131.4337" y="14.5"/>
</g>
<g style="fill:rgb(0%,0%,0%);fill-opacity:1;">
  <use xlink:href="#glyph1-1" x="138.4357" y="17.5"/>
</g>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 216.066406 478.199219 L 216.183594 469.898438 " transform="matrix(1,0,0,1,-80,-439)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:butt;stroke-linejoin:miter;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 216.238281 465.898438 L 216.183594 469.898438 M 214.683594 469.878906 L 216.238281 465.898438 L 217.683594 469.921875 " transform="matrix(1,0,0,1,-80,-439)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 227.898438 522.882812 C 233.269531 518.976562 238.425781 512.539062 238.5 502.199219 C 238.515625 499.863281 238.273438 497.449219 237.824219 494.996094 M 234.824219 484.421875 C 232.8125 479.023438 230.199219 473.753906 227.582031 469.085938 " transform="matrix(1,0,0,1,-80,-439)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:butt;stroke-linejoin:miter;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 225.554688 465.636719 L 227.582031 469.085938 M 226.289062 469.847656 L 225.554688 465.636719 L 228.875 468.328125 " transform="matrix(1,0,0,1,-80,-439)"/>
<path style="fill-rule:nonzero;fill:rgb(69.804382%,85.098267%,100%);fill-opacity:1;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 277.898438 554.601562 L 301.898438 554.601562 L 301.898438 578.601562 L 277.898438 578.601562 Z M 277.898438 554.601562 " transform="matrix(1,0,0,1,-80,-439)"/>
<g style="fill:rgb(0%,0%,0%);fill-opacity:1;">
  <use xlink:href="#glyph0-2" x="204.983" y="128.6"/>
</g>
<g style="fill:rgb(0%,0%,0%);fill-opacity:1;">
  <use xlink:href="#glyph2-1" x="211.481" y="132.6"/>
</g>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 277.898438 478.199219 L 301.898438 478.199219 L 301.898438 502.199219 L 277.898438 502.199219 Z M 277.898438 478.199219 " transform="matrix(1,0,0,1,-80,-439)"/>
<g style="fill:rgb(0%,0%,0%);fill-opacity:1;">
  <use xlink:href="#glyph0-1" x="204.731" y="54.2"/>
</g>
<g style="fill:rgb(0%,0%,0%);fill-opacity:1;">
  <use xlink:href="#glyph2-1" x="211.733" y="57.2"/>
</g>
<path style="fill:none;stroke-width:0.5;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 291.199219 484.449219 L 286.398438 484.449219 " transform="matrix(1,0,0,1,-80,-439)"/>
<path style="fill:none;stroke-width:0.5;stroke-linecap:butt;stroke-linejoin:miter;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 284.398438 484.449219 L 286.398438 484.449219 M 286.398438 485.199219 L 284.398438 484.449219 L 286.398438 483.699219 " transform="matrix(1,0,0,1,-80,-439)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 277.898438 516.398438 L 301.898438 516.398438 L 301.898438 540.398438 L 277.898438 540.398438 Z M 277.898438 516.398438 " transform="matrix(1,0,0,1,-80,-439)"/>
<g style="fill:rgb(0%,0%,0%);fill-opacity:1;">
  <use xlink:href="#glyph0-1" x="204.731" y="92.4"/>
</g>
<g style="fill:rgb(0%,0%,0%);fill-opacity:1;">
  <use xlink:href="#glyph2-1" x="211.733" y="95.4"/>
</g>
<path style="fill:none;stroke-width:0.5;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 284.648438 522.601562 L 289.449219 522.601562 " transform="matrix(1,0,0,1,-80,-439)"/>
<path style="fill:none;stroke-width:0.5;stroke-linecap:butt;stroke-linejoin:miter;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 291.449219 522.601562 L 289.449219 522.601562 M 289.449219 521.851562 L 291.449219 522.601562 L 289.449219 523.351562 " transform="matrix(1,0,0,1,-80,-439)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 278.277344 554.601562 C 274.015625 548.953125 269.949219 541.628906 268.8125 533.3125 M 269.109375 522.355469 C 270.171875 516.734375 272.507812 511.5625 275.246094 507.078125 " transform="matrix(1,0,0,1,-80,-439)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:butt;stroke-linejoin:miter;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 277.492188 503.773438 L 275.246094 507.082031 M 274.003906 506.238281 L 277.492188 503.773438 L 276.488281 507.921875 " transform="matrix(1,0,0,1,-80,-439)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 289.898438 554.601562 L 289.898438 546.300781 " transform="matrix(1,0,0,1,-80,-439)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:butt;stroke-linejoin:miter;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 289.898438 542.300781 L 289.898438 546.300781 M 288.398438 546.300781 L 289.898438 542.300781 L 291.398438 546.300781 " transform="matrix(1,0,0,1,-80,-439)"/>
<path style="fill-rule:nonzero;fill:rgb(69.804382%,85.098267%,100%);fill-opacity:1;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 277.898438 440 L 301.898438 440 L 301.898438 464 L 277.898438 464 Z M 277.898438 440 " transform="matrix(1,0,0,1,-80,-439)"/>
<g style="fill:rgb(0%,0%,0%);fill-opacity:1;">
  <use xlink:href="#glyph0-3" x="204.731" y="14.5"/>
</g>
<g style="fill:rgb(0%,0%,0%);fill-opacity:1;">
  <use xlink:href="#glyph2-1" x="211.733" y="17.5"/>
</g>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 289.898438 478.199219 L 289.898438 469.898438 " transform="matrix(1,0,0,1,-80,-439)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:butt;stroke-linejoin:miter;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 289.898438 465.898438 L 289.898438 469.898438 M 288.398438 469.898438 L 289.898438 465.898438 L 291.398438 469.898438 " transform="matrix(1,0,0,1,-80,-439)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 301.898438 523.449219 C 307.941406 519.609375 314 513.050781 314 502.199219 C 314 491.375 307.96875 478.816406 301.941406 468.96875 " transform="matrix(1,0,0,1,-80,-439)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:butt;stroke-linejoin:miter;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 299.785156 465.601562 L 301.941406 468.96875 M 300.679688 469.777344 L 299.785156 465.601562 L 303.203125 468.160156 " transform="matrix(1,0,0,1,-80,-439)"/>
<g style="fill:rgb(0%,0%,0%);fill-opacity:1;">
  <use xlink:href="#glyph3-1" x="168.8995" y="90.9"/>
</g>
<g style="fill:rgb(0%,0%,0%);fill-opacity:1;">
  <use xlink:href="#glyph3-1" x="168.8995" y="52.7"/>
</g>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 174.765625 528.398438 L 198 528.398438 " transform="matrix(1,0,0,1,-80,-439)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:butt;stroke-linejoin:miter;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 202 528.398438 L 198 528.398438 M 198 526.898438 L 202 528.398438 L 198 529.898438 " transform="matrix(1,0,0,1,-80,-439)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 227.898438 528.074219 L 238 527.800781 " transform="matrix(1,0,0,1,-80,-439)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:butt;stroke-linejoin:miter;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 242 527.695312 L 238 527.800781 M 237.960938 526.304688 L 242 527.695312 L 238.042969 529.300781 " transform="matrix(1,0,0,1,-80,-439)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 261.902344 527.644531 L 272.003906 527.917969 " transform="matrix(1,0,0,1,-80,-439)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:butt;stroke-linejoin:miter;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 276 528.023438 L 272.003906 527.917969 M 272.042969 526.417969 L 276 528.023438 L 271.960938 529.414062 " transform="matrix(1,0,0,1,-80,-439)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 277.898438 489.875 L 267.796875 489.601562 " transform="matrix(1,0,0,1,-80,-439)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:butt;stroke-linejoin:miter;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 263.800781 489.496094 L 267.796875 489.601562 M 267.757812 491.101562 L 263.800781 489.496094 L 267.839844 488.101562 " transform="matrix(1,0,0,1,-80,-439)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 243.898438 489.441406 L 233.796875 489.714844 " transform="matrix(1,0,0,1,-80,-439)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:butt;stroke-linejoin:miter;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 229.800781 489.824219 L 233.796875 489.714844 M 233.839844 491.214844 L 229.800781 489.824219 L 233.757812 488.21875 " transform="matrix(1,0,0,1,-80,-439)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 203.898438 490.199219 L 180.664062 490.199219 " transform="matrix(1,0,0,1,-80,-439)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:butt;stroke-linejoin:miter;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 176.664062 490.199219 L 180.664062 490.199219 M 180.664062 491.699219 L 176.664062 490.199219 L 180.664062 488.699219 " transform="matrix(1,0,0,1,-80,-439)"/>
<path style="fill:none;stroke-width:0.5;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 164.398438 484.449219 L 159.601562 484.449219 " transform="matrix(1,0,0,1,-80,-439)"/>
<path style="fill:none;stroke-width:0.5;stroke-linecap:butt;stroke-linejoin:miter;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 157.601562 484.449219 L 159.601562 484.449219 M 159.601562 485.199219 L 157.601562 484.449219 L 159.601562 483.699219 " transform="matrix(1,0,0,1,-80,-439)"/>
<path style="fill-rule:nonzero;fill:rgb(69.804382%,85.098267%,100%);fill-opacity:1;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 97.09375 554.601562 L 121.09375 554.601562 L 121.09375 578.601562 L 97.09375 578.601562 Z M 97.09375 554.601562 " transform="matrix(1,0,0,1,-80,-439)"/>
<g style="fill:rgb(0%,0%,0%);fill-opacity:1;">
  <use xlink:href="#glyph0-2" x="24.344" y="128.6"/>
</g>
<g style="fill:rgb(0%,0%,0%);fill-opacity:1;">
  <use xlink:href="#glyph1-3" x="30.842" y="132.6"/>
</g>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 97.09375 478.199219 L 121.09375 478.199219 L 121.09375 502.199219 L 97.09375 502.199219 Z M 97.09375 478.199219 " transform="matrix(1,0,0,1,-80,-439)"/>
<g style="fill:rgb(0%,0%,0%);fill-opacity:1;">
  <use xlink:href="#glyph0-1" x="24.092" y="54.2"/>
</g>
<g style="fill:rgb(0%,0%,0%);fill-opacity:1;">
  <use xlink:href="#glyph1-3" x="31.094" y="57.2"/>
</g>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 97.09375 516.398438 L 121.09375 516.398438 L 121.09375 540.398438 L 97.09375 540.398438 Z M 97.09375 516.398438 " transform="matrix(1,0,0,1,-80,-439)"/>
<g style="fill:rgb(0%,0%,0%);fill-opacity:1;">
  <use xlink:href="#glyph0-1" x="24.092" y="92.4"/>
</g>
<g style="fill:rgb(0%,0%,0%);fill-opacity:1;">
  <use xlink:href="#glyph1-3" x="31.094" y="95.4"/>
</g>
<path style="fill:none;stroke-width:0.5;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 103.800781 522.601562 L 108.601562 522.601562 " transform="matrix(1,0,0,1,-80,-439)"/>
<path style="fill:none;stroke-width:0.5;stroke-linecap:butt;stroke-linejoin:miter;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 110.601562 522.601562 L 108.601562 522.601562 M 108.601562 521.851562 L 110.601562 522.601562 L 108.601562 523.351562 " transform="matrix(1,0,0,1,-80,-439)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 97.417969 554.601562 C 92.039062 547.652344 86.828125 538.21875 86.828125 527.75 C 86.828125 519.792969 89.839844 512.613281 93.644531 506.710938 " transform="matrix(1,0,0,1,-80,-439)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:butt;stroke-linejoin:miter;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 95.980469 503.464844 L 93.644531 506.710938 M 92.425781 505.832031 L 95.980469 503.464844 L 94.859375 507.585938 " transform="matrix(1,0,0,1,-80,-439)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 109.09375 554.601562 L 109.09375 546.300781 " transform="matrix(1,0,0,1,-80,-439)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:butt;stroke-linejoin:miter;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 109.09375 542.300781 L 109.09375 546.300781 M 107.59375 546.300781 L 109.09375 542.300781 L 110.59375 546.300781 " transform="matrix(1,0,0,1,-80,-439)"/>
<path style="fill-rule:nonzero;fill:rgb(69.804382%,85.098267%,100%);fill-opacity:1;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 97.09375 440 L 121.09375 440 L 121.09375 464 L 97.09375 464 Z M 97.09375 440 " transform="matrix(1,0,0,1,-80,-439)"/>
<g style="fill:rgb(0%,0%,0%);fill-opacity:1;">
  <use xlink:href="#glyph0-3" x="24.092" y="14.5"/>
</g>
<g style="fill:rgb(0%,0%,0%);fill-opacity:1;">
  <use xlink:href="#glyph1-3" x="31.094" y="17.5"/>
</g>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 109.09375 478.199219 L 109.09375 469.898438 " transform="matrix(1,0,0,1,-80,-439)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:butt;stroke-linejoin:miter;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 109.09375 465.898438 L 109.09375 469.898438 M 107.59375 469.898438 L 109.09375 465.898438 L 110.59375 469.898438 " transform="matrix(1,0,0,1,-80,-439)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 121.09375 528.398438 L 144.863281 528.398438 " transform="matrix(1,0,0,1,-80,-439)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:butt;stroke-linejoin:miter;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 148.863281 528.398438 L 144.863281 528.398438 M 144.863281 526.898438 L 148.863281 528.398438 L 144.863281 529.898438 " transform="matrix(1,0,0,1,-80,-439)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 150.765625 490.199219 L 126.992188 490.199219 " transform="matrix(1,0,0,1,-80,-439)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:butt;stroke-linejoin:miter;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 122.992188 490.199219 L 126.992188 490.199219 M 126.992188 491.699219 L 122.992188 490.199219 L 126.992188 488.699219 " transform="matrix(1,0,0,1,-80,-439)"/>
<path style="fill:none;stroke-width:0.5;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 110.59375 484.449219 L 105.792969 484.449219 " transform="matrix(1,0,0,1,-80,-439)"/>
<path style="fill:none;stroke-width:0.5;stroke-linecap:butt;stroke-linejoin:miter;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 103.792969 484.449219 L 105.792969 484.449219 M 105.792969 485.199219 L 103.792969 484.449219 L 105.792969 483.699219 " transform="matrix(1,0,0,1,-80,-439)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 121.09375 523.492188 C 127.195312 519.660156 133.335938 513.09375 133.335938 502.199219 C 133.335938 491.371094 127.265625 478.808594 121.199219 468.960938 " transform="matrix(1,0,0,1,-80,-439)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:butt;stroke-linejoin:miter;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 119.035156 465.597656 L 121.199219 468.960938 M 119.941406 469.773438 L 119.035156 465.597656 L 122.460938 468.148438 " transform="matrix(1,0,0,1,-80,-439)"/>
</g>
</svg>
