{"cells": [{"cell_type": "markdown", "id": "ddaeef65", "metadata": {"origin_pos": 0}, "source": ["# 选择服务器和GPU\n", ":label:`sec_buy_gpu`\n", "\n", "深度学习训练通常需要大量的计算。目前，GPU是深度学习最具成本效益的硬件加速器。与CPU相比，GPU更便宜，性能更高，通常超过一个数量级。此外，一台服务器可以支持多个GPU，高端服务器最多支持8个GPU。更典型的数字是工程工作站最多4个GPU，这是因为热量、冷却和电源需求会迅速增加，超出办公楼所能支持的范围。对于更大的部署，云计算（例如亚马逊的[P3](https://aws.amazon.com/ec2/instance-types/p3/)和[G4](https://aws.amazon.com/blogs/aws/in-the-works-ec2-instances-g4-with-nvidia-t4-gpus/)实例）是一个更实用的解决方案。\n", "\n", "## 选择服务器\n", "\n", "通常不需要购买具有多个线程的高端CPU，因为大部分计算都发生在GPU上。这就是说，由于Python中的全局解释器锁（GIL），CPU的单线程性能在有4-8个GPU的情况下可能很重要。所有的条件都是一样的，这意味着核数较少但时钟频率较高的CPU可能是更经济的选择。例如，当在6核4GHz和8核3.5GHz CPU之间进行选择时，前者更可取，即使其聚合速度较低。一个重要的考虑因素是，GPU使用大量的电能，从而释放大量的热量。这需要非常好的冷却和足够大的机箱来容纳GPU。如有可能，请遵循以下指南：\n", "\n", "1. **电源**。GPU使用大量的电源。每个设备预计高达350W（检查显卡的*峰值需求*而不是一般需求，因为高效代码可能会消耗大量能源）。如果电源不能满足需求，系统会变得不稳定。\n", "1. **机箱尺寸**。GPU很大，辅助电源连接器通常需要额外的空间。此外，大型机箱更容易冷却。\n", "1. **GPU散热**。如果有大量的GPU，可能需要投资水冷。此外，即使风扇较少，也应以“公版设计”为目标，因为它们足够薄，可以在设备之间进气。当使用多风扇GPU，安装多个GPU时，它可能太厚而无法获得足够的空气。\n", "1. **PCIe插槽**。在GPU之间来回移动数据（以及在GPU之间交换数据）需要大量带宽。建议使用16通道的PCIe 3.0插槽。当安装了多个GPU时，请务必仔细阅读主板说明，以确保在同时使用多个GPU时16$\\times$带宽仍然可用，并且使用的是PCIe3.0，而不是用于附加插槽的PCIe2.0。在安装多个GPU的情况下，一些主板的带宽降级到8$\\times$甚至4$\\times$。这部分是由于CPU提供的PCIe通道数量限制。\n", "\n", "简而言之，以下是构建深度学习服务器的一些建议。\n", "\n", "* **初学者**。购买低功耗的低端GPU（适合深度学习的廉价游戏GPU，功耗150-200W）。如果幸运的话，大家现在常用的计算机将支持它。\n", "* **1个GPU**。一个4核的低端CPU就足够了，大多数主板也足够了。以至少32 GB的DRAM为目标，投资SSD进行本地数据访问。600W的电源应足够。买一个有很多风扇的GPU。\n", "* **2个GPU**。一个4-6核的低端CPU就足够了。可以考虑64 GB的DRAM并投资于SSD。两个高端GPU将需要1000瓦的功率。对于主板，请确保它们具有*两个*PCIe 3.0 x16插槽。如果可以，请使用PCIe 3.0 x16插槽之间有两个可用空间（60毫米间距）的主板，以提供额外的空气。在这种情况下，购买两个具有大量风扇的GPU。\n", "* **4个GPU**。确保购买的CPU具有相对较快的单线程速度（即较高的时钟频率）。可能需要具有更多PCIe通道的CPU，例如AMD Threadripper。可能需要相对昂贵的主板才能获得4个PCIe 3.0 x16插槽，因为它们可能需要一个PLX来多路复用PCIe通道。购买带有公版设计的GPU，这些GPU很窄，并且让空气进入GPU之间。需要一个1600-2000W的电源，而办公室的插座可能不支持。此服务器可能在运行时*声音很大，很热*。不想把它放在桌子下面。建议使用128 GB的DRAM。获取一个用于本地存储的SSD（1-2 TB NVMe）和RAID配置的硬盘来存储数据。\n", "* **8 GPU**。需要购买带有多个冗余电源的专用多GPU服务器机箱（例如，每个电源为1600W时为2+1）。这将需要双插槽服务器CPU、256 GB ECC DRAM、快速网卡（建议使用10 GBE），并且需要检查服务器是否支持GPU的*物理外形*。用户GPU和服务器GPU之间的气流和布线位置存在显著差异（例如RTX 2080和Tesla V100）。这意味着可能无法在服务器中安装消费级GPU，因为电源线间隙不足或缺少合适的接线（本书一位合著者痛苦地发现了这一点）。\n", "\n", "## 选择GPU\n", "\n", "目前，AMD和NVIDIA是专用GPU的两大主要制造商。NVIDIA是第一个进入深度学习领域的公司，通过CUDA为深度学习框架提供更好的支持。因此，大多数买家选择NVIDIA GPU。\n", "\n", "NVIDIA提供两种类型的GPU，针对个人用户（例如，通过GTX和RTX系列）和企业用户（通过其Tesla系列）。这两种类型的GPU提供了相当的计算能力。但是，企业用户GPU通常使用强制（被动）冷却、更多内存和ECC（纠错）内存。这些GPU更适用于数据中心，通常成本是消费者GPU的十倍。\n", "\n", "如果是一个拥有100个服务器的大公司，则应该考虑英伟达Tesla系列，或者在云中使用GPU服务器。对于实验室或10+服务器的中小型公司，英伟达RTX系列可能是最具成本效益的，可以购买超微或华硕机箱的预配置服务器，这些服务器可以有效地容纳4-8个GPU。\n", "\n", "GPU供应商通常每一到两年发布一代，例如2017年发布的GTX 1000（Pascal）系列和2019年发布的RTX 2000（Turing）系列。每个系列都提供几种不同的型号，提供不同的性能级别。GPU性能主要是以下三个参数的组合：\n", "\n", "1. **计算能力**。通常大家会追求32位浮点计算能力。16位浮点训练（FP16）也进入主流。如果只对预测感兴趣，还可以使用8位整数。最新一代图灵GPU提供4-bit加速。不幸的是，目前训练低精度网络的算法还没有普及；\n", "1. **内存大小**。随着模型变大或训练期间使用的批量变大，将需要更多的GPU内存。检查HBM2（高带宽内存）与GDDR6（图形DDR）内存。HBM2速度更快，但成本更高；\n", "1. **内存带宽**。当有足够的内存带宽时，才能最大限度地利用计算能力。如果使用GDDR6，请追求宽内存总线。\n", "\n", "对于大多数用户，只需看看计算能力就足够了。请注意，许多GPU提供不同类型的加速。例如，NVIDIA的Tensor Cores将操作符子集的速度提高了5$\\times$。确保所使用的库支持这一点。GPU内存应不小于4GB（8GB更好）。尽量避免将GPU也用于显示GUI（改用内置显卡）。如果无法避免，请添加额外的2GB RAM以确保安全。\n", "\n", ":numref:`fig_flopsvsprice`比较了各种GTX 900、GTX 1000和RTX 2000系列的（GFlops）和价格（Price）。价格是维基百科上的建议价格。\n", "\n", "![浮点计算能力和价格比较](../img/flopsvsprice.svg)\n", ":label:`fig_flopsvsprice`\n", "\n", "由上图，可以看出很多事情：\n", "\n", "1. 在每个系列中，价格和性能大致成比例。Titan因拥有大GPU内存而有相当的溢价。然而，通过比较980 Ti和1080 Ti可以看出，较新型号具有更好的成本效益。RTX 2000系列的价格似乎没有多大提高。然而，它们提供了更优秀的低精度性能（FP16、INT8和INT4）；\n", "2. GTX 1000系列的性价比大约是900系列的两倍；\n", "3. 对于RTX 2000系列，浮点计算能力是价格的“仿射”函数。\n", "\n", "![浮点计算能力和能耗](../img/wattvsprice.svg)\n", ":label:`fig_wattvsprice`\n", "\n", ":numref:`fig_wattvsprice`显示了能耗与计算量基本成线性关系。其次，后一代更有效率。这似乎与对应于RTX 2000系列的图表相矛盾。然而，这是TensorCore不成比例的大能耗的结果。\n", "\n", "## 小结\n", "\n", "* 在构建服务器时，请注意电源、PCIe总线通道、CPU单线程速度和散热。\n", "* 如果可能，应该购买最新一代的GPU。\n", "* 使用云进行大型部署。\n", "* 高密度服务器可能不与所有GPU兼容。在购买之前，请检查一下机械和散热规格。\n", "* 为提高效率，请使用FP16或更低的精度。\n"]}], "metadata": {"language_info": {"name": "python"}}, "nbformat": 4, "nbformat_minor": 5}