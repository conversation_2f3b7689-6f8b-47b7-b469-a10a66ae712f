{"cells": [{"cell_type": "markdown", "id": "3569f632", "metadata": {"origin_pos": 0}, "source": ["# 词的相似性和类比任务\n", ":label:`sec_synonyms`\n", "\n", "在 :numref:`sec_word2vec_pretraining`中，我们在一个小的数据集上训练了一个word2vec模型，并使用它为一个输入词寻找语义相似的词。实际上，在大型语料库上预先训练的词向量可以应用于下游的自然语言处理任务，这将在后面的 :numref:`chap_nlp_app`中讨论。为了直观地演示大型语料库中预训练词向量的语义，让我们将预训练词向量应用到词的相似性和类比任务中。\n"]}, {"cell_type": "code", "execution_count": 1, "id": "bf7ee198", "metadata": {"execution": {"iopub.execute_input": "2022-12-07T16:51:10.112650Z", "iopub.status.busy": "2022-12-07T16:51:10.112198Z", "iopub.status.idle": "2022-12-07T16:51:12.338937Z", "shell.execute_reply": "2022-12-07T16:51:12.337853Z"}, "origin_pos": 2, "tab": ["pytorch"]}, "outputs": [], "source": ["import os\n", "import torch\n", "from torch import nn\n", "from d2l import torch as d2l"]}, {"cell_type": "markdown", "id": "6a00e67d", "metadata": {"origin_pos": 4}, "source": ["## 加载预训练词向量\n", "\n", "以下列出维度为50、100和300的预训练GloVe嵌入，可从[GloVe网站](https://nlp.stanford.edu/projects/glove/)下载。预训练的fastText嵌入有多种语言。这里我们使用可以从[fastText网站](https://fasttext.cc/)下载300维度的英文版本（“wiki.en”）。\n"]}, {"cell_type": "code", "execution_count": 2, "id": "79777daf", "metadata": {"execution": {"iopub.execute_input": "2022-12-07T16:51:12.343611Z", "iopub.status.busy": "2022-12-07T16:51:12.342793Z", "iopub.status.idle": "2022-12-07T16:51:12.348005Z", "shell.execute_reply": "2022-12-07T16:51:12.347200Z"}, "origin_pos": 5, "tab": ["pytorch"]}, "outputs": [], "source": ["#@save\n", "d2l.DATA_HUB['glove.6b.50d'] = (d2l.DATA_URL + 'glove.6B.50d.zip',\n", "                                '0b8703943ccdb6eb788e6f091b8946e82231bc4d')\n", "\n", "#@save\n", "d2l.DATA_HUB['glove.6b.100d'] = (d2l.DATA_URL + 'glove.6B.100d.zip',\n", "                                 'cd43bfb07e44e6f27cbcc7bc9ae3d80284fdaf5a')\n", "\n", "#@save\n", "d2l.DATA_HUB['glove.42b.300d'] = (d2l.DATA_URL + 'glove.42B.300d.zip',\n", "                                  'b5116e234e9eb9076672cfeabf5469f3eec904fa')\n", "\n", "#@save\n", "d2l.DATA_HUB['wiki.en'] = (d2l.DATA_URL + 'wiki.en.zip',\n", "                           'c1816da3821ae9f43899be655002f6c723e91b88')"]}, {"cell_type": "markdown", "id": "16b6c01c", "metadata": {"origin_pos": 6}, "source": ["为了加载这些预训练的GloVe和fastText嵌入，我们定义了以下`TokenEmbedding`类。\n"]}, {"cell_type": "code", "execution_count": 3, "id": "09656692", "metadata": {"execution": {"iopub.execute_input": "2022-12-07T16:51:12.351360Z", "iopub.status.busy": "2022-12-07T16:51:12.350830Z", "iopub.status.idle": "2022-12-07T16:51:12.359636Z", "shell.execute_reply": "2022-12-07T16:51:12.358826Z"}, "origin_pos": 7, "tab": ["pytorch"]}, "outputs": [], "source": ["#@save\n", "class TokenEmbedding:\n", "    \"\"\"GloVe嵌入\"\"\"\n", "    def __init__(self, embedding_name):\n", "        self.idx_to_token, self.idx_to_vec = self._load_embedding(\n", "            embedding_name)\n", "        self.unknown_idx = 0\n", "        self.token_to_idx = {token: idx for idx, token in\n", "                             enumerate(self.idx_to_token)}\n", "\n", "    def _load_embedding(self, embedding_name):\n", "        idx_to_token, idx_to_vec = ['<unk>'], []\n", "        data_dir = d2l.download_extract(embedding_name)\n", "        # GloVe网站：https://nlp.stanford.edu/projects/glove/\n", "        # fastText网站：https://fasttext.cc/\n", "        with open(os.path.join(data_dir, 'vec.txt'), 'r') as f:\n", "            for line in f:\n", "                elems = line.rstrip().split(' ')\n", "                token, elems = elems[0], [float(elem) for elem in elems[1:]]\n", "                # 跳过标题信息，例如fastText中的首行\n", "                if len(elems) > 1:\n", "                    idx_to_token.append(token)\n", "                    idx_to_vec.append(elems)\n", "        idx_to_vec = [[0] * len(idx_to_vec[0])] + idx_to_vec\n", "        return idx_to_token, torch.tensor(idx_to_vec)\n", "\n", "    def __getitem__(self, tokens):\n", "        indices = [self.token_to_idx.get(token, self.unknown_idx)\n", "                   for token in tokens]\n", "        vecs = self.idx_to_vec[torch.tensor(indices)]\n", "        return vecs\n", "\n", "    def __len__(self):\n", "        return len(self.idx_to_token)"]}, {"cell_type": "markdown", "id": "626714ae", "metadata": {"origin_pos": 8}, "source": ["下面我们加载50维GloVe嵌入（在维基百科的子集上预训练）。创建`TokenEmbedding`实例时，如果尚未下载指定的嵌入文件，则必须下载该文件。\n"]}, {"cell_type": "code", "execution_count": 4, "id": "a75fb69b", "metadata": {"execution": {"iopub.execute_input": "2022-12-07T16:51:12.363100Z", "iopub.status.busy": "2022-12-07T16:51:12.362348Z", "iopub.status.idle": "2022-12-07T16:51:23.012129Z", "shell.execute_reply": "2022-12-07T16:51:23.011187Z"}, "origin_pos": 9, "tab": ["pytorch"]}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Downloading ../data/glove.6B.50d.zip from http://d2l-data.s3-accelerate.amazonaws.com/glove.6B.50d.zip...\n"]}], "source": ["glove_6b50d = TokenEmbedding('glove.6b.50d')"]}, {"cell_type": "markdown", "id": "5310ca50", "metadata": {"origin_pos": 10}, "source": ["输出词表大小。词表包含400000个词（词元）和一个特殊的未知词元。\n"]}, {"cell_type": "code", "execution_count": 5, "id": "357926f1", "metadata": {"execution": {"iopub.execute_input": "2022-12-07T16:51:23.015849Z", "iopub.status.busy": "2022-12-07T16:51:23.015552Z", "iopub.status.idle": "2022-12-07T16:51:23.022796Z", "shell.execute_reply": "2022-12-07T16:51:23.022014Z"}, "origin_pos": 11, "tab": ["pytorch"]}, "outputs": [{"data": {"text/plain": ["400001"]}, "execution_count": 5, "metadata": {}, "output_type": "execute_result"}], "source": ["len(glove_6b50d)"]}, {"cell_type": "markdown", "id": "5c5af473", "metadata": {"origin_pos": 12}, "source": ["我们可以得到词表中一个单词的索引，反之亦然。\n"]}, {"cell_type": "code", "execution_count": 6, "id": "0b339393", "metadata": {"execution": {"iopub.execute_input": "2022-12-07T16:51:23.025806Z", "iopub.status.busy": "2022-12-07T16:51:23.025520Z", "iopub.status.idle": "2022-12-07T16:51:23.030876Z", "shell.execute_reply": "2022-12-07T16:51:23.030106Z"}, "origin_pos": 13, "tab": ["pytorch"]}, "outputs": [{"data": {"text/plain": ["(3367, 'beautiful')"]}, "execution_count": 6, "metadata": {}, "output_type": "execute_result"}], "source": ["glove_6b50d.token_to_idx['beautiful'], glove_6b50d.idx_to_token[3367]"]}, {"cell_type": "markdown", "id": "8c128239", "metadata": {"origin_pos": 14}, "source": ["## 应用预训练词向量\n", "\n", "使用加载的GloVe向量，我们将通过下面的词相似性和类比任务中来展示词向量的语义。\n", "\n", "### 词相似度\n", "\n", "与 :numref:`subsec_apply-word-embed`类似，为了根据词向量之间的余弦相似性为输入词查找语义相似的词，我们实现了以下`knn`（$k$近邻）函数。\n"]}, {"cell_type": "code", "execution_count": 7, "id": "2963c55f", "metadata": {"execution": {"iopub.execute_input": "2022-12-07T16:51:23.033954Z", "iopub.status.busy": "2022-12-07T16:51:23.033668Z", "iopub.status.idle": "2022-12-07T16:51:23.038797Z", "shell.execute_reply": "2022-12-07T16:51:23.038020Z"}, "origin_pos": 16, "tab": ["pytorch"]}, "outputs": [], "source": ["def knn(W, x, k):\n", "    # 增加1e-9以获得数值稳定性\n", "    cos = torch.mv(W, x.reshape(-1,)) / (\n", "        torch.sqrt(torch.sum(W * W, axis=1) + 1e-9) *\n", "        torch.sqrt((x * x).sum()))\n", "    _, topk = torch.topk(cos, k=k)\n", "    return topk, [cos[int(i)] for i in topk]"]}, {"cell_type": "markdown", "id": "25fd0e66", "metadata": {"origin_pos": 18}, "source": ["然后，我们使用`TokenEmbedding`的实例`embed`中预训练好的词向量来搜索相似的词。\n"]}, {"cell_type": "code", "execution_count": 8, "id": "1540fa04", "metadata": {"execution": {"iopub.execute_input": "2022-12-07T16:51:23.041891Z", "iopub.status.busy": "2022-12-07T16:51:23.041609Z", "iopub.status.idle": "2022-12-07T16:51:23.046356Z", "shell.execute_reply": "2022-12-07T16:51:23.045580Z"}, "origin_pos": 19, "tab": ["pytorch"]}, "outputs": [], "source": ["def get_similar_tokens(query_token, k, embed):\n", "    topk, cos = knn(embed.idx_to_vec, embed[[query_token]], k + 1)\n", "    for i, c in zip(topk[1:], cos[1:]):  # 排除输入词\n", "        print(f'{embed.idx_to_token[int(i)]}：cosine相似度={float(c):.3f}')"]}, {"cell_type": "markdown", "id": "acf6997c", "metadata": {"origin_pos": 20}, "source": ["`glove_6b50d`中预训练词向量的词表包含400000个词和一个特殊的未知词元。排除输入词和未知词元后，我们在词表中找到与“chip”一词语义最相似的三个词。\n"]}, {"cell_type": "code", "execution_count": 9, "id": "f0669f2b", "metadata": {"execution": {"iopub.execute_input": "2022-12-07T16:51:23.049358Z", "iopub.status.busy": "2022-12-07T16:51:23.049077Z", "iopub.status.idle": "2022-12-07T16:51:23.099748Z", "shell.execute_reply": "2022-12-07T16:51:23.098885Z"}, "origin_pos": 21, "tab": ["pytorch"]}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["chips：cosine相似度=0.856\n", "intel：cosine相似度=0.749\n", "electronics：cosine相似度=0.749\n"]}], "source": ["get_similar_tokens('chip', 3, glove_6b50d)"]}, {"cell_type": "markdown", "id": "a9bae1e0", "metadata": {"origin_pos": 22}, "source": ["下面输出与“baby”和“beautiful”相似的词。\n"]}, {"cell_type": "code", "execution_count": 10, "id": "bb3b33c5", "metadata": {"execution": {"iopub.execute_input": "2022-12-07T16:51:23.103124Z", "iopub.status.busy": "2022-12-07T16:51:23.102833Z", "iopub.status.idle": "2022-12-07T16:51:23.126540Z", "shell.execute_reply": "2022-12-07T16:51:23.125681Z"}, "origin_pos": 23, "tab": ["pytorch"]}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["babies：cosine相似度=0.839\n", "boy：cosine相似度=0.800\n", "girl：cosine相似度=0.792\n"]}], "source": ["get_similar_tokens('baby', 3, glove_6b50d)"]}, {"cell_type": "code", "execution_count": 11, "id": "ee2e2e6b", "metadata": {"execution": {"iopub.execute_input": "2022-12-07T16:51:23.129790Z", "iopub.status.busy": "2022-12-07T16:51:23.129497Z", "iopub.status.idle": "2022-12-07T16:51:23.153639Z", "shell.execute_reply": "2022-12-07T16:51:23.152820Z"}, "origin_pos": 24, "tab": ["pytorch"]}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["lovely：cosine相似度=0.921\n", "gorgeous：cosine相似度=0.893\n", "wonderful：cosine相似度=0.830\n"]}], "source": ["get_similar_tokens('beautiful', 3, glove_6b50d)"]}, {"cell_type": "markdown", "id": "eca1d5fa", "metadata": {"origin_pos": 25}, "source": ["### 词类比\n", "\n", "除了找到相似的词，我们还可以将词向量应用到词类比任务中。\n", "例如，“man” : “woman” :: “son” : “daughter”是一个词的类比。\n", "“man”是对“woman”的类比，“son”是对“daughter”的类比。\n", "具体来说，词类比任务可以定义为：\n", "对于单词类比$a : b :: c : d$，给出前三个词$a$、$b$和$c$，找到$d$。\n", "用$\\text{vec}(w)$表示词$w$的向量，\n", "为了完成这个类比，我们将找到一个词，\n", "其向量与$\\text{vec}(c)+\\text{vec}(b)-\\text{vec}(a)$的结果最相似。\n"]}, {"cell_type": "code", "execution_count": 12, "id": "fd7cd269", "metadata": {"execution": {"iopub.execute_input": "2022-12-07T16:51:23.156898Z", "iopub.status.busy": "2022-12-07T16:51:23.156619Z", "iopub.status.idle": "2022-12-07T16:51:23.161571Z", "shell.execute_reply": "2022-12-07T16:51:23.160818Z"}, "origin_pos": 26, "tab": ["pytorch"]}, "outputs": [], "source": ["def get_analogy(token_a, token_b, token_c, embed):\n", "    vecs = embed[[token_a, token_b, token_c]]\n", "    x = vecs[1] - vecs[0] + vecs[2]\n", "    topk, cos = knn(embed.idx_to_vec, x, 1)\n", "    return embed.idx_to_token[int(topk[0])]  # 删除未知词"]}, {"cell_type": "markdown", "id": "cd6dc813", "metadata": {"origin_pos": 27}, "source": ["让我们使用加载的词向量来验证“male-female”类比。\n"]}, {"cell_type": "code", "execution_count": 13, "id": "5b0bd518", "metadata": {"execution": {"iopub.execute_input": "2022-12-07T16:51:23.164865Z", "iopub.status.busy": "2022-12-07T16:51:23.164243Z", "iopub.status.idle": "2022-12-07T16:51:23.186809Z", "shell.execute_reply": "2022-12-07T16:51:23.186003Z"}, "origin_pos": 28, "tab": ["pytorch"]}, "outputs": [{"data": {"text/plain": ["'daughter'"]}, "execution_count": 13, "metadata": {}, "output_type": "execute_result"}], "source": ["get_analogy('man', 'woman', 'son', glove_6b50d)"]}, {"cell_type": "markdown", "id": "33812cfb", "metadata": {"origin_pos": 29}, "source": ["下面完成一个“首都-国家”的类比：\n", "“beijing” : “china” :: “tokyo” : “japan”。\n", "这说明了预训练词向量中的语义。\n"]}, {"cell_type": "code", "execution_count": 14, "id": "f6b0c199", "metadata": {"execution": {"iopub.execute_input": "2022-12-07T16:51:23.190044Z", "iopub.status.busy": "2022-12-07T16:51:23.189767Z", "iopub.status.idle": "2022-12-07T16:51:23.215594Z", "shell.execute_reply": "2022-12-07T16:51:23.214794Z"}, "origin_pos": 30, "tab": ["pytorch"]}, "outputs": [{"data": {"text/plain": ["'japan'"]}, "execution_count": 14, "metadata": {}, "output_type": "execute_result"}], "source": ["get_analogy('beijing', 'china', 'tokyo', glove_6b50d)"]}, {"cell_type": "markdown", "id": "c437f5b9", "metadata": {"origin_pos": 31}, "source": ["另外，对于“bad” : “worst” :: “big” : “biggest”等“形容词-形容词最高级”的比喻，预训练词向量可以捕捉到句法信息。\n"]}, {"cell_type": "code", "execution_count": 15, "id": "f16dbec9", "metadata": {"execution": {"iopub.execute_input": "2022-12-07T16:51:23.218766Z", "iopub.status.busy": "2022-12-07T16:51:23.218446Z", "iopub.status.idle": "2022-12-07T16:51:23.240691Z", "shell.execute_reply": "2022-12-07T16:51:23.239912Z"}, "origin_pos": 32, "tab": ["pytorch"]}, "outputs": [{"data": {"text/plain": ["'biggest'"]}, "execution_count": 15, "metadata": {}, "output_type": "execute_result"}], "source": ["get_analogy('bad', 'worst', 'big', glove_6b50d)"]}, {"cell_type": "markdown", "id": "8567e10e", "metadata": {"origin_pos": 33}, "source": ["为了演示在预训练词向量中捕捉到的过去式概念，我们可以使用“现在式-过去式”的类比来测试句法：“do” : “did” :: “go” : “went”。\n"]}, {"cell_type": "code", "execution_count": 16, "id": "f5223972", "metadata": {"execution": {"iopub.execute_input": "2022-12-07T16:51:23.244300Z", "iopub.status.busy": "2022-12-07T16:51:23.243727Z", "iopub.status.idle": "2022-12-07T16:51:23.267678Z", "shell.execute_reply": "2022-12-07T16:51:23.266850Z"}, "origin_pos": 34, "tab": ["pytorch"]}, "outputs": [{"data": {"text/plain": ["'went'"]}, "execution_count": 16, "metadata": {}, "output_type": "execute_result"}], "source": ["get_analogy('do', 'did', 'go', glove_6b50d)"]}, {"cell_type": "markdown", "id": "04da9c5f", "metadata": {"origin_pos": 35}, "source": ["## 小结\n", "\n", "* 在实践中，在大型语料库上预先练的词向量可以应用于下游的自然语言处理任务。\n", "* 预训练的词向量可以应用于词的相似性和类比任务。\n", "\n", "## 练习\n", "\n", "1. 使用`TokenEmbedding('wiki.en')`测试fastText结果。\n", "1. 当词表非常大时，我们怎样才能更快地找到相似的词或完成一个词的类比呢？\n"]}, {"cell_type": "markdown", "id": "f634d533", "metadata": {"origin_pos": 37, "tab": ["pytorch"]}, "source": ["[Discussions](https://discuss.d2l.ai/t/5746)\n"]}], "metadata": {"language_info": {"name": "python"}}, "nbformat": 4, "nbformat_minor": 5}