{"cells": [{"cell_type": "markdown", "id": "b1994573", "metadata": {"origin_pos": 0}, "source": ["# 序列模型\n", ":label:`sec_sequence`\n", "\n", "想象一下有人正在看网飞（Netflix，一个国外的视频网站）上的电影。\n", "一名忠实的用户会对每一部电影都给出评价，\n", "毕竟一部好电影需要更多的支持和认可。\n", "然而事实证明，事情并不那么简单。\n", "随着时间的推移，人们对电影的看法会发生很大的变化。\n", "事实上，心理学家甚至对这些现象起了名字：\n", "\n", "* *锚定*（anchoring）效应：基于其他人的意见做出评价。\n", "  例如，奥斯卡颁奖后，受到关注的电影的评分会上升，尽管它还是原来那部电影。\n", "  这种影响将持续几个月，直到人们忘记了这部电影曾经获得的奖项。\n", "  结果表明（ :cite:`<PERSON><PERSON><PERSON>.Beutel.ea.2017`），这种效应会使评分提高半个百分点以上。\n", "* *享乐适应*（hedonic adaption）：人们迅速接受并且适应一种更好或者更坏的情况\n", "  作为新的常态。\n", "  例如，在看了很多好电影之后，人们会强烈期望下部电影会更好。\n", "  因此，在许多精彩的电影被看过之后，即使是一部普通的也可能被认为是糟糕的。\n", "* *季节性*（seasonality）：少有观众喜欢在八月看圣诞老人的电影。\n", "* 有时，电影会由于导演或演员在制作中的不当行为变得不受欢迎。\n", "* 有些电影因为其极度糟糕只能成为小众电影。*Plan9from Outer Space*和*Troll2*就因为这个原因而臭名昭著的。\n", "\n", "简而言之，电影评分决不是固定不变的。\n", "因此，使用时间动力学可以得到更准确的电影推荐 :cite:`<PERSON>ren.2009`。\n", "当然，序列数据不仅仅是关于电影评分的。\n", "下面给出了更多的场景。\n", "\n", "* 在使用程序时，许多用户都有很强的特定习惯。\n", "  例如，在学生放学后社交媒体应用更受欢迎。在市场开放时股市交易软件更常用。\n", "* 预测明天的股价要比过去的股价更困难，尽管两者都只是估计一个数字。\n", "  毕竟，先见之明比事后诸葛亮难得多。\n", "  在统计学中，前者（对超出已知观测范围进行预测）称为*外推法*（extrapolation），\n", "  而后者（在现有观测值之间进行估计）称为*内插法*（interpolation）。\n", "* 在本质上，音乐、语音、文本和视频都是连续的。\n", "  如果它们的序列被我们重排，那么就会失去原有的意义。\n", "  比如，一个文本标题“狗咬人”远没有“人咬狗”那么令人惊讶，尽管组成两句话的字完全相同。\n", "* 地震具有很强的相关性，即大地震发生后，很可能会有几次小余震，\n", "  这些余震的强度比非大地震后的余震要大得多。\n", "  事实上，地震是时空相关的，即余震通常发生在很短的时间跨度和很近的距离内。\n", "* 人类之间的互动也是连续的，这可以从微博上的争吵和辩论中看出。\n", "\n", "## 统计工具\n", "\n", "处理序列数据需要统计工具和新的深度神经网络架构。\n", "为了简单起见，我们以 :numref:`fig_ftse100`所示的股票价格（富时100指数）为例。\n", "\n", "![近30年的富时100指数](../img/ftse100.png)\n", ":width:`400px`\n", ":label:`fig_ftse100`\n", "\n", "其中，用$x_t$表示价格，即在*时间步*（time step）\n", "$t \\in \\mathbb{Z}^+$时，观察到的价格$x_t$。\n", "请注意，$t$对于本文中的序列通常是离散的，并在整数或其子集上变化。\n", "假设一个交易员想在$t$日的股市中表现良好，于是通过以下途径预测$x_t$：\n", "\n", "$$x_t \\sim P(x_t \\mid x_{t-1}, \\ldots, x_1).$$\n", "\n", "### 自回归模型\n", "\n", "为了实现这个预测，交易员可以使用回归模型，\n", "例如在 :numref:`sec_linear_concise`中训练的模型。\n", "仅有一个主要问题：输入数据的数量，\n", "输入$x_{t-1}, \\ldots, x_1$本身因$t$而异。\n", "也就是说，输入数据的数量这个数字将会随着我们遇到的数据量的增加而增加，\n", "因此需要一个近似方法来使这个计算变得容易处理。\n", "本章后面的大部分内容将围绕着如何有效估计\n", "$P(x_t \\mid x_{t-1}, \\ldots, x_1)$展开。\n", "简单地说，它归结为以下两种策略。\n", "\n", "第一种策略，假设在现实情况下相当长的序列\n", "$x_{t-1}, \\ldots, x_1$可能是不必要的，\n", "因此我们只需要满足某个长度为$\\tau$的时间跨度，\n", "即使用观测序列$x_{t-1}, \\ldots, x_{t-\\tau}$。\n", "当下获得的最直接的好处就是参数的数量总是不变的，\n", "至少在$t > \\tau$时如此，这就使我们能够训练一个上面提及的深度网络。\n", "这种模型被称为*自回归模型*（autoregressive models），\n", "因为它们是对自己执行回归。\n", "\n", "第二种策略，如 :numref:`fig_sequence-model`所示，\n", "是保留一些对过去观测的总结$h_t$，\n", "并且同时更新预测$\\hat{x}_t$和总结$h_t$。\n", "这就产生了基于$\\hat{x}_t = P(x_t \\mid h_{t})$估计$x_t$，\n", "以及公式$h_t = g(h_{t-1}, x_{t-1})$更新的模型。\n", "由于$h_t$从未被观测到，这类模型也被称为\n", "*隐变量自回归模型*（latent autoregressive models）。\n", "\n", "![隐变量自回归模型](../img/sequence-model.svg)\n", ":label:`fig_sequence-model`\n", "\n", "这两种情况都有一个显而易见的问题：如何生成训练数据？\n", "一个经典方法是使用历史观测来预测下一个未来观测。\n", "显然，我们并不指望时间会停滞不前。\n", "然而，一个常见的假设是虽然特定值$x_t$可能会改变，\n", "但是序列本身的动力学不会改变。\n", "这样的假设是合理的，因为新的动力学一定受新的数据影响，\n", "而我们不可能用目前所掌握的数据来预测新的动力学。\n", "统计学家称不变的动力学为*静止的*（stationary）。\n", "因此，整个序列的估计值都将通过以下的方式获得：\n", "\n", "$$P(x_1, \\ldots, x_T) = \\prod_{t=1}^T P(x_t \\mid x_{t-1}, \\ldots, x_1).$$\n", "\n", "注意，如果我们处理的是离散的对象（如单词），\n", "而不是连续的数字，则上述的考虑仍然有效。\n", "唯一的差别是，对于离散的对象，\n", "我们需要使用分类器而不是回归模型来估计$P(x_t \\mid  x_{t-1}, \\ldots, x_1)$。\n", "\n", "### 马尔可夫模型\n", "\n", "回想一下，在自回归模型的近似法中，\n", "我们使用$x_{t-1}, \\ldots, x_{t-\\tau}$\n", "而不是$x_{t-1}, \\ldots, x_1$来估计$x_t$。\n", "只要这种是近似精确的，我们就说序列满足*马尔可夫条件*（Markov condition）。\n", "特别是，如果$\\tau = 1$，得到一个\n", "*一阶马尔可夫模型*（first-order Markov model），\n", "$P(x)$由下式给出：\n", "\n", "$$P(x_1, \\ldots, x_T) = \\prod_{t=1}^T P(x_t \\mid x_{t-1}) \\text{ 当 } P(x_1 \\mid x_0) = P(x_1).$$\n", "\n", "当假设$x_t$仅是离散值时，这样的模型特别棒，\n", "因为在这种情况下，使用动态规划可以沿着马尔可夫链精确地计算结果。\n", "例如，我们可以高效地计算$P(x_{t+1} \\mid x_{t-1})$：\n", "\n", "$$\n", "\\begin{aligned}\n", "P(x_{t+1} \\mid x_{t-1})\n", "&= \\frac{\\sum_{x_t} P(x_{t+1}, x_t, x_{t-1})}{P(x_{t-1})}\\\\\n", "&= \\frac{\\sum_{x_t} P(x_{t+1} \\mid x_t, x_{t-1}) P(x_t, x_{t-1})}{P(x_{t-1})}\\\\\n", "&= \\sum_{x_t} P(x_{t+1} \\mid x_t) P(x_t \\mid x_{t-1})\n", "\\end{aligned}\n", "$$\n", "\n", "利用这一事实，我们只需要考虑过去观察中的一个非常短的历史：\n", "$P(x_{t+1} \\mid x_t, x_{t-1}) = P(x_{t+1} \\mid x_t)$。\n", "隐马尔可夫模型中的动态规划超出了本节的范围\n", "（我们将在 :numref:`sec_bi_rnn`再次遇到），\n", "而动态规划这些计算工具已经在控制算法和强化学习算法广泛使用。\n", "\n", "### 因果关系\n", "\n", "原则上，将$P(x_1, \\ldots, x_T)$倒序展开也没什么问题。\n", "毕竟，基于条件概率公式，我们总是可以写出：\n", "\n", "$$P(x_1, \\ldots, x_T) = \\prod_{t=T}^1 P(x_t \\mid x_{t+1}, \\ldots, x_T).$$\n", "\n", "事实上，如果基于一个马尔可夫模型，\n", "我们还可以得到一个反向的条件概率分布。\n", "然而，在许多情况下，数据存在一个自然的方向，即在时间上是前进的。\n", "很明显，未来的事件不能影响过去。\n", "因此，如果我们改变$x_t$，可能会影响未来发生的事情$x_{t+1}$，但不能反过来。\n", "也就是说，如果我们改变$x_t$，基于过去事件得到的分布不会改变。\n", "因此，解释$P(x_{t+1} \\mid x_t)$应该比解释$P(x_t \\mid x_{t+1})$更容易。\n", "例如，在某些情况下，对于某些可加性噪声$\\epsilon$，\n", "显然我们可以找到$x_{t+1} = f(x_t) + \\epsilon$，\n", "而反之则不行 :cite:`Hoyer.Janzing.Mooij.ea.2009`。\n", "而这个向前推进的方向恰好也是我们通常感兴趣的方向。\n", "彼得斯等人 :cite:<PERSON><PERSON>.Janzing.Scholkopf.2017`\n", "对该主题的更多内容做了详尽的解释，而我们的上述讨论只是其中的冰山一角。\n", "\n", "## 训练\n", "\n", "在了解了上述统计工具后，让我们在实践中尝试一下！\n", "首先，我们生成一些数据：(**使用正弦函数和一些可加性噪声来生成序列数据，\n", "时间步为$1, 2, \\ldots, 1000$。**)\n"]}, {"cell_type": "code", "execution_count": 1, "id": "21808a81", "metadata": {"execution": {"iopub.execute_input": "2022-12-07T16:51:34.411769Z", "iopub.status.busy": "2022-12-07T16:51:34.411198Z", "iopub.status.idle": "2022-12-07T16:51:37.060203Z", "shell.execute_reply": "2022-12-07T16:51:37.059391Z"}, "origin_pos": 2, "tab": ["pytorch"]}, "outputs": [], "source": ["%matplotlib inline\n", "import torch\n", "from torch import nn\n", "from d2l import torch as d2l"]}, {"cell_type": "code", "execution_count": 2, "id": "74d53731", "metadata": {"execution": {"iopub.execute_input": "2022-12-07T16:51:37.064175Z", "iopub.status.busy": "2022-12-07T16:51:37.063683Z", "iopub.status.idle": "2022-12-07T16:51:37.298865Z", "shell.execute_reply": "2022-12-07T16:51:37.297756Z"}, "origin_pos": 5, "tab": ["pytorch"]}, "outputs": [{"data": {"image/svg+xml": ["<?xml version=\"1.0\" encoding=\"utf-8\" standalone=\"no\"?>\n", "<!DOCTYPE svg PUBLIC \"-//W3C//DTD SVG 1.1//EN\"\n", "  \"http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd\">\n", "<svg xmlns:xlink=\"http://www.w3.org/1999/xlink\" width=\"406.885938pt\" height=\"211.07625pt\" viewBox=\"0 0 406.**********.07625\" xmlns=\"http://www.w3.org/2000/svg\" version=\"1.1\">\n", " <metadata>\n", "  <rdf:RDF xmlns:dc=\"http://purl.org/dc/elements/1.1/\" xmlns:cc=\"http://creativecommons.org/ns#\" xmlns:rdf=\"http://www.w3.org/1999/02/22-rdf-syntax-ns#\">\n", "   <cc:Work>\n", "    <dc:type rdf:resource=\"http://purl.org/dc/dcmitype/StillImage\"/>\n", "    <dc:date>2022-12-07T16:51:37.261396</dc:date>\n", "    <dc:format>image/svg+xml</dc:format>\n", "    <dc:creator>\n", "     <cc:Agent>\n", "      <dc:title>Matplotlib v3.5.1, https://matplotlib.org/</dc:title>\n", "     </cc:Agent>\n", "    </dc:creator>\n", "   </cc:Work>\n", "  </rdf:RDF>\n", " </metadata>\n", " <defs>\n", "  <style type=\"text/css\">*{stroke-linejoin: round; stroke-linecap: butt}</style>\n", " </defs>\n", " <g id=\"figure_1\">\n", "  <g id=\"patch_1\">\n", "   <path d=\"M 0 211.07625 \n", "L 406.**********.07625 \n", "L 406.885938 0 \n", "L 0 0 \n", "z\n", "\" style=\"fill: #ffffff\"/>\n", "  </g>\n", "  <g id=\"axes_1\">\n", "   <g id=\"patch_2\">\n", "    <path d=\"M 52.**********.52 \n", "L 386.**********.52 \n", "L 386.960938 7.2 \n", "L 52.160938 7.2 \n", "z\n", "\" style=\"fill: #ffffff\"/>\n", "   </g>\n", "   <g id=\"matplotlib.axis_1\">\n", "    <g id=\"xtick_1\">\n", "     <g id=\"line2d_1\">\n", "      <path d=\"M 118.852829 173.52 \n", "L 118.852829 7.2 \n", "\" clip-path=\"url(#p7f0f2eafbd)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_2\">\n", "      <defs>\n", "       <path id=\"m355e784165\" d=\"M 0 0 \n", "L 0 3.5 \n", "\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </defs>\n", "      <g>\n", "       <use xlink:href=\"#m355e784165\" x=\"118.852829\" y=\"173.52\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_1\">\n", "      <!-- 200 -->\n", "      <g transform=\"translate(109.309079 188.118438)scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-32\" d=\"M 1228 531 \n", "L 3431 531 \n", "L 3431 0 \n", "L 469 0 \n", "L 469 531 \n", "Q 828 903 1448 1529 \n", "Q 2069 2156 2228 2338 \n", "Q 2531 2678 2651 2914 \n", "Q 2772 3150 2772 3378 \n", "Q 2772 3750 2511 3984 \n", "Q 2250 4219 1831 4219 \n", "Q 1534 4219 1204 4116 \n", "Q 875 4013 500 3803 \n", "L 500 4441 \n", "Q 881 4594 1212 4672 \n", "Q 1544 4750 1819 4750 \n", "Q 2544 4750 2975 4387 \n", "Q 3406 4025 3406 3419 \n", "Q 3406 3131 3298 2873 \n", "Q 3191 2616 2906 2266 \n", "Q 2828 2175 2409 1742 \n", "Q 1991 1309 1228 531 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "        <path id=\"DejaVuSans-30\" d=\"M 2034 4250 \n", "Q 1547 4250 1301 3770 \n", "Q 1056 3291 1056 2328 \n", "Q 1056 1369 1301 889 \n", "Q 1547 409 2034 409 \n", "Q 2525 409 2770 889 \n", "Q 3016 1369 3016 2328 \n", "Q 3016 3291 2770 3770 \n", "Q 2525 4250 2034 4250 \n", "z\n", "M 2034 4750 \n", "Q 2819 4750 3233 4129 \n", "Q 3647 3509 3647 2328 \n", "Q 3647 1150 3233 529 \n", "Q 2819 -91 2034 -91 \n", "Q 1250 -91 836 529 \n", "Q 422 1150 422 2328 \n", "Q 422 3509 836 4129 \n", "Q 1250 4750 2034 4750 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-32\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"127.246094\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_2\">\n", "     <g id=\"line2d_3\">\n", "      <path d=\"M 185.879856 173.52 \n", "L 185.879856 7.2 \n", "\" clip-path=\"url(#p7f0f2eafbd)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_4\">\n", "      <g>\n", "       <use xlink:href=\"#m355e784165\" x=\"185.879856\" y=\"173.52\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_2\">\n", "      <!-- 400 -->\n", "      <g transform=\"translate(176.336106 188.118438)scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-34\" d=\"M 2419 4116 \n", "L 825 1625 \n", "L 2419 1625 \n", "L 2419 4116 \n", "z\n", "M 2253 4666 \n", "L 3047 4666 \n", "L 3047 1625 \n", "L 3713 1625 \n", "L 3713 1100 \n", "L 3047 1100 \n", "L 3047 0 \n", "L 2419 0 \n", "L 2419 1100 \n", "L 313 1100 \n", "L 313 1709 \n", "L 2253 4666 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-34\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"127.246094\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_3\">\n", "     <g id=\"line2d_5\">\n", "      <path d=\"M 252.906883 173.52 \n", "L 252.906883 7.2 \n", "\" clip-path=\"url(#p7f0f2eafbd)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_6\">\n", "      <g>\n", "       <use xlink:href=\"#m355e784165\" x=\"252.906883\" y=\"173.52\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_3\">\n", "      <!-- 600 -->\n", "      <g transform=\"translate(243.363133 188.118438)scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-36\" d=\"M 2113 2584 \n", "Q 1688 2584 1439 2293 \n", "Q 1191 2003 1191 1497 \n", "Q 1191 994 1439 701 \n", "Q 1688 409 2113 409 \n", "Q 2538 409 2786 701 \n", "Q 3034 994 3034 1497 \n", "Q 3034 2003 2786 2293 \n", "Q 2538 2584 2113 2584 \n", "z\n", "M 3366 4563 \n", "L 3366 3988 \n", "Q 3128 4100 2886 4159 \n", "Q 2644 4219 2406 4219 \n", "Q 1781 4219 1451 3797 \n", "Q 1122 3375 1075 2522 \n", "Q 1259 2794 1537 2939 \n", "Q 1816 3084 2150 3084 \n", "Q 2853 3084 3261 2657 \n", "Q 3669 2231 3669 1497 \n", "Q 3669 778 3244 343 \n", "Q 2819 -91 2113 -91 \n", "Q 1303 -91 875 529 \n", "Q 447 1150 447 2328 \n", "Q 447 3434 972 4092 \n", "Q 1497 4750 2381 4750 \n", "Q 2619 4750 2861 4703 \n", "Q 3103 4656 3366 4563 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-36\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"127.246094\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_4\">\n", "     <g id=\"line2d_7\">\n", "      <path d=\"M 319.93391 173.52 \n", "L 319.93391 7.2 \n", "\" clip-path=\"url(#p7f0f2eafbd)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_8\">\n", "      <g>\n", "       <use xlink:href=\"#m355e784165\" x=\"319.93391\" y=\"173.52\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_4\">\n", "      <!-- 800 -->\n", "      <g transform=\"translate(310.39016 188.118438)scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-38\" d=\"M 2034 2216 \n", "Q 1584 2216 1326 1975 \n", "Q 1069 1734 1069 1313 \n", "Q 1069 891 1326 650 \n", "Q 1584 409 2034 409 \n", "Q 2484 409 2743 651 \n", "Q 3003 894 3003 1313 \n", "Q 3003 1734 2745 1975 \n", "Q 2488 2216 2034 2216 \n", "z\n", "M 1403 2484 \n", "Q 997 2584 770 2862 \n", "Q 544 3141 544 3541 \n", "Q 544 4100 942 4425 \n", "Q 1341 4750 2034 4750 \n", "Q 2731 4750 3128 4425 \n", "Q 3525 4100 3525 3541 \n", "Q 3525 3141 3298 2862 \n", "Q 3072 2584 2669 2484 \n", "Q 3125 2378 3379 2068 \n", "Q 3634 1759 3634 1313 \n", "Q 3634 634 3220 271 \n", "Q 2806 -91 2034 -91 \n", "Q 1263 -91 848 271 \n", "Q 434 634 434 1313 \n", "Q 434 1759 690 2068 \n", "Q 947 2378 1403 2484 \n", "z\n", "M 1172 3481 \n", "Q 1172 3119 1398 2916 \n", "Q 1625 2713 2034 2713 \n", "Q 2441 2713 2670 2916 \n", "Q 2900 3119 2900 3481 \n", "Q 2900 3844 2670 4047 \n", "Q 2441 4250 2034 4250 \n", "Q 1625 4250 1398 4047 \n", "Q 1172 3844 1172 3481 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-38\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"127.246094\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_5\">\n", "     <g id=\"line2d_9\">\n", "      <path d=\"M 386.**********.52 \n", "L 386.960938 7.2 \n", "\" clip-path=\"url(#p7f0f2eafbd)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_10\">\n", "      <g>\n", "       <use xlink:href=\"#m355e784165\" x=\"386.960938\" y=\"173.52\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_5\">\n", "      <!-- 1000 -->\n", "      <g transform=\"translate(374.235937 188.118438)scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-31\" d=\"M 794 531 \n", "L 1825 531 \n", "L 1825 4091 \n", "L 703 3866 \n", "L 703 4441 \n", "L 1819 4666 \n", "L 2450 4666 \n", "L 2450 531 \n", "L 3481 531 \n", "L 3481 0 \n", "L 794 0 \n", "L 794 531 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-31\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"127.246094\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"190.869141\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"text_6\">\n", "     <!-- time -->\n", "     <g transform=\"translate(208.264844 201.796563)scale(0.1 -0.1)\">\n", "      <defs>\n", "       <path id=\"DejaVuSans-74\" d=\"M 1172 4494 \n", "L 1172 3500 \n", "L 2356 3500 \n", "L 2356 3053 \n", "L 1172 3053 \n", "L 1172 1153 \n", "Q 1172 725 1289 603 \n", "Q 1406 481 1766 481 \n", "L 2356 481 \n", "L 2356 0 \n", "L 1766 0 \n", "Q 1100 0 847 248 \n", "Q 594 497 594 1153 \n", "L 594 3053 \n", "L 172 3053 \n", "L 172 3500 \n", "L 594 3500 \n", "L 594 4494 \n", "L 1172 4494 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-69\" d=\"M 603 3500 \n", "L 1178 3500 \n", "L 1178 0 \n", "L 603 0 \n", "L 603 3500 \n", "z\n", "M 603 4863 \n", "L 1178 4863 \n", "L 1178 4134 \n", "L 603 4134 \n", "L 603 4863 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-6d\" d=\"M 3328 2828 \n", "Q 3544 3216 3844 3400 \n", "Q 4144 3584 4550 3584 \n", "Q 5097 3584 5394 3201 \n", "Q 5691 2819 5691 2113 \n", "L 5691 0 \n", "L 5113 0 \n", "L 5113 2094 \n", "Q 5113 2597 4934 2840 \n", "Q 4756 3084 4391 3084 \n", "Q 3944 3084 3684 2787 \n", "Q 3425 2491 3425 1978 \n", "L 3425 0 \n", "L 2847 0 \n", "L 2847 2094 \n", "Q 2847 2600 2669 2842 \n", "Q 2491 3084 2119 3084 \n", "Q 1678 3084 1418 2786 \n", "Q 1159 2488 1159 1978 \n", "L 1159 0 \n", "L 581 0 \n", "L 581 3500 \n", "L 1159 3500 \n", "L 1159 2956 \n", "Q 1356 3278 1631 3431 \n", "Q 1906 3584 2284 3584 \n", "Q 2666 3584 2933 3390 \n", "Q 3200 3197 3328 2828 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-65\" d=\"M 3597 1894 \n", "L 3597 1613 \n", "L 953 1613 \n", "Q 991 1019 1311 708 \n", "Q 1631 397 2203 397 \n", "Q 2534 397 2845 478 \n", "Q 3156 559 3463 722 \n", "L 3463 178 \n", "Q 3153 47 2828 -22 \n", "Q 2503 -91 2169 -91 \n", "Q 1331 -91 842 396 \n", "Q 353 884 353 1716 \n", "Q 353 2575 817 3079 \n", "Q 1281 3584 2069 3584 \n", "Q 2775 3584 3186 3129 \n", "Q 3597 2675 3597 1894 \n", "z\n", "M 3022 2063 \n", "Q 3016 2534 2758 2815 \n", "Q 2500 3097 2075 3097 \n", "Q 1594 3097 1305 2825 \n", "Q 1016 2553 972 2059 \n", "L 3022 2063 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "      </defs>\n", "      <use xlink:href=\"#DejaVuSans-74\"/>\n", "      <use xlink:href=\"#DejaVuSans-69\" x=\"39.208984\"/>\n", "      <use xlink:href=\"#DejaVuSans-6d\" x=\"66.992188\"/>\n", "      <use xlink:href=\"#DejaVuSans-65\" x=\"164.404297\"/>\n", "     </g>\n", "    </g>\n", "   </g>\n", "   <g id=\"matplotlib.axis_2\">\n", "    <g id=\"ytick_1\">\n", "     <g id=\"line2d_11\">\n", "      <path d=\"M 52.160938 166.995592 \n", "L 386.960938 166.995592 \n", "\" clip-path=\"url(#p7f0f2eafbd)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_12\">\n", "      <defs>\n", "       <path id=\"m6ed3182de1\" d=\"M 0 0 \n", "L -3.5 0 \n", "\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </defs>\n", "      <g>\n", "       <use xlink:href=\"#m6ed3182de1\" x=\"52.160938\" y=\"166.995592\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_7\">\n", "      <!-- −1.5 -->\n", "      <g transform=\"translate(20.878125 170.794811)scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-2212\" d=\"M 678 2272 \n", "L 4684 2272 \n", "L 4684 1741 \n", "L 678 1741 \n", "L 678 2272 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "        <path id=\"DejaVuSans-2e\" d=\"M 684 794 \n", "L 1344 794 \n", "L 1344 0 \n", "L 684 0 \n", "L 684 794 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "        <path id=\"DejaVuSans-35\" d=\"M 691 4666 \n", "L 3169 4666 \n", "L 3169 4134 \n", "L 1269 4134 \n", "L 1269 2991 \n", "Q 1406 3038 1543 3061 \n", "Q 1681 3084 1819 3084 \n", "Q 2600 3084 3056 2656 \n", "Q 3513 2228 3513 1497 \n", "Q 3513 744 3044 326 \n", "Q 2575 -91 1722 -91 \n", "Q 1428 -91 1123 -41 \n", "Q 819 9 494 109 \n", "L 494 744 \n", "Q 775 591 1075 516 \n", "Q 1375 441 1709 441 \n", "Q 2250 441 2565 725 \n", "Q 2881 1009 2881 1497 \n", "Q 2881 1984 2565 2268 \n", "Q 2250 2553 1709 2553 \n", "Q 1456 2553 1204 2497 \n", "Q 953 2441 691 2322 \n", "L 691 4666 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-2212\"/>\n", "       <use xlink:href=\"#DejaVuSans-31\" x=\"83.789062\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"147.412109\"/>\n", "       <use xlink:href=\"#DejaVuSans-35\" x=\"179.199219\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_2\">\n", "     <g id=\"line2d_13\">\n", "      <path d=\"M 52.160938 142.631734 \n", "L 386.960938 142.631734 \n", "\" clip-path=\"url(#p7f0f2eafbd)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_14\">\n", "      <g>\n", "       <use xlink:href=\"#m6ed3182de1\" x=\"52.160938\" y=\"142.631734\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_8\">\n", "      <!-- −1.0 -->\n", "      <g transform=\"translate(20.878125 146.430952)scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-2212\"/>\n", "       <use xlink:href=\"#DejaVuSans-31\" x=\"83.789062\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"147.412109\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"179.199219\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_3\">\n", "     <g id=\"line2d_15\">\n", "      <path d=\"M 52.160938 118.267875 \n", "L 386.960938 118.267875 \n", "\" clip-path=\"url(#p7f0f2eafbd)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_16\">\n", "      <g>\n", "       <use xlink:href=\"#m6ed3182de1\" x=\"52.160938\" y=\"118.267875\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_9\">\n", "      <!-- −0.5 -->\n", "      <g transform=\"translate(20.878125 122.067094)scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-2212\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"83.789062\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"147.412109\"/>\n", "       <use xlink:href=\"#DejaVuSans-35\" x=\"179.199219\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_4\">\n", "     <g id=\"line2d_17\">\n", "      <path d=\"M 52.160938 93.904016 \n", "L 386.960938 93.904016 \n", "\" clip-path=\"url(#p7f0f2eafbd)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_18\">\n", "      <g>\n", "       <use xlink:href=\"#m6ed3182de1\" x=\"52.160938\" y=\"93.904016\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_10\">\n", "      <!-- 0.0 -->\n", "      <g transform=\"translate(29.257812 97.703235)scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"95.410156\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_5\">\n", "     <g id=\"line2d_19\">\n", "      <path d=\"M 52.160938 69.540158 \n", "L 386.960938 69.540158 \n", "\" clip-path=\"url(#p7f0f2eafbd)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_20\">\n", "      <g>\n", "       <use xlink:href=\"#m6ed3182de1\" x=\"52.160938\" y=\"69.540158\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_11\">\n", "      <!-- 0.5 -->\n", "      <g transform=\"translate(29.257812 73.339377)scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-35\" x=\"95.410156\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_6\">\n", "     <g id=\"line2d_21\">\n", "      <path d=\"M 52.160938 45.176299 \n", "L 386.960938 45.176299 \n", "\" clip-path=\"url(#p7f0f2eafbd)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_22\">\n", "      <g>\n", "       <use xlink:href=\"#m6ed3182de1\" x=\"52.160938\" y=\"45.176299\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_12\">\n", "      <!-- 1.0 -->\n", "      <g transform=\"translate(29.257812 48.975518)scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-31\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"95.410156\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_7\">\n", "     <g id=\"line2d_23\">\n", "      <path d=\"M 52.160938 20.812441 \n", "L 386.960938 20.812441 \n", "\" clip-path=\"url(#p7f0f2eafbd)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_24\">\n", "      <g>\n", "       <use xlink:href=\"#m6ed3182de1\" x=\"52.160938\" y=\"20.812441\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_13\">\n", "      <!-- 1.5 -->\n", "      <g transform=\"translate(29.257812 24.611659)scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-31\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-35\" x=\"95.410156\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"text_14\">\n", "     <!-- x -->\n", "     <g transform=\"translate(14.798438 93.319375)rotate(-90)scale(0.1 -0.1)\">\n", "      <defs>\n", "       <path id=\"DejaVuSans-78\" d=\"M 3513 3500 \n", "L 2247 1797 \n", "L 3578 0 \n", "L 2900 0 \n", "L 1881 1375 \n", "L 863 0 \n", "L 184 0 \n", "L 1544 1831 \n", "L 300 3500 \n", "L 978 3500 \n", "L 1906 2253 \n", "L 2834 3500 \n", "L 3513 3500 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "      </defs>\n", "      <use xlink:href=\"#DejaVuSans-78\"/>\n", "     </g>\n", "    </g>\n", "   </g>\n", "   <g id=\"line2d_25\">\n", "    <path d=\"M 52.160938 74.122182 \n", "L 52.496073 82.976358 \n", "L 52.831208 86.558977 \n", "L 53.166343 83.001781 \n", "L 53.501478 82.64153 \n", "L 53.836613 85.200891 \n", "L 54.171748 78.027478 \n", "L 54.506883 82.563021 \n", "L 54.842019 78.881125 \n", "L 55.177154 63.187702 \n", "L 55.512289 104.799279 \n", "L 55.847424 101.529501 \n", "L 56.182559 77.360766 \n", "L 56.517694 78.594721 \n", "L 56.852829 98.553949 \n", "L 57.187965 92.06747 \n", "L 57.5231 78.070344 \n", "L 57.858235 102.228351 \n", "L 58.19337 83.277319 \n", "L 58.528505 79.367227 \n", "L 58.86364 72.546103 \n", "L 59.198775 101.833262 \n", "L 59.53391 93.326703 \n", "L 59.869046 89.723121 \n", "L 60.204181 77.823903 \n", "L 60.539316 81.70565 \n", "L 60.874451 64.461051 \n", "L 61.209586 84.679889 \n", "L 61.544721 78.420531 \n", "L 61.879856 67.901008 \n", "L 62.214992 67.108084 \n", "L 62.550127 60.179015 \n", "L 62.885262 101.993429 \n", "L 63.220397 74.308081 \n", "L 63.555532 67.066302 \n", "L 64.225802 89.228962 \n", "L 64.560938 80.305291 \n", "L 64.896073 48.521341 \n", "L 65.231208 72.533865 \n", "L 65.566343 77.365541 \n", "L 65.901478 74.073157 \n", "L 66.236613 83.163421 \n", "L 66.571748 64.118552 \n", "L 66.906883 82.451677 \n", "L 67.242019 82.780181 \n", "L 67.577154 63.032906 \n", "L 67.912289 56.477075 \n", "L 68.247424 74.51123 \n", "L 68.582559 77.660944 \n", "L 68.917694 79.406715 \n", "L 69.587965 69.246506 \n", "L 69.9231 67.676429 \n", "L 70.258235 69.621616 \n", "L 70.59337 61.960669 \n", "L 70.928505 70.970748 \n", "L 71.26364 63.431543 \n", "L 71.598775 70.363378 \n", "L 71.93391 67.026198 \n", "L 72.269046 66.295803 \n", "L 72.604181 58.59723 \n", "L 72.939316 74.558062 \n", "L 73.274451 57.760131 \n", "L 73.609586 52.936239 \n", "L 73.944721 52.300379 \n", "L 74.279856 49.711407 \n", "L 74.614992 69.070976 \n", "L 74.950127 76.704328 \n", "L 75.285262 58.766496 \n", "L 75.620397 70.914502 \n", "L 75.955532 68.629031 \n", "L 76.290667 76.122422 \n", "L 76.625802 54.722089 \n", "L 76.960938 57.870062 \n", "L 77.631208 54.247635 \n", "L 78.301478 79.934818 \n", "L 78.636613 56.857057 \n", "L 78.971748 52.632044 \n", "L 79.306883 52.454562 \n", "L 79.642019 55.07879 \n", "L 79.977154 61.14855 \n", "L 80.312289 44.971208 \n", "L 80.647424 46.844795 \n", "L 80.982559 68.34088 \n", "L 81.317694 52.221934 \n", "L 81.652829 58.852675 \n", "L 81.987965 61.54735 \n", "L 82.3231 66.295617 \n", "L 82.658235 57.934153 \n", "L 82.99337 54.135645 \n", "L 83.328505 58.786388 \n", "L 83.66364 57.171827 \n", "L 83.998775 61.658556 \n", "L 84.33391 59.793372 \n", "L 84.669046 42.114157 \n", "L 85.004181 46.375392 \n", "L 85.339316 61.905317 \n", "L 85.674451 43.29131 \n", "L 86.009586 50.473117 \n", "L 86.344721 61.904951 \n", "L 86.679856 57.67373 \n", "L 87.014992 60.784042 \n", "L 87.350127 31.671405 \n", "L 88.020397 55.47522 \n", "L 88.355532 47.265718 \n", "L 88.690667 45.848023 \n", "L 89.025802 49.093185 \n", "L 89.360938 50.768184 \n", "L 89.696073 57.478776 \n", "L 90.366343 40.765541 \n", "L 90.701478 43.428578 \n", "L 91.036613 39.507983 \n", "L 91.706883 58.448845 \n", "L 92.042019 41.859627 \n", "L 92.377154 36.339418 \n", "L 92.712289 62.010441 \n", "L 93.047424 51.105503 \n", "L 93.382559 33.425876 \n", "L 93.717694 30.236295 \n", "L 94.052829 54.818753 \n", "L 94.387965 49.477173 \n", "L 94.7231 38.673642 \n", "L 95.058235 36.546194 \n", "L 95.39337 55.301694 \n", "L 95.728505 33.595104 \n", "L 96.398775 54.597877 \n", "L 97.069046 34.687065 \n", "L 97.739316 47.32601 \n", "L 98.074451 54.569181 \n", "L 98.409586 41.007675 \n", "L 98.744721 41.859848 \n", "L 99.079856 53.729517 \n", "L 99.414992 50.770536 \n", "L 99.750127 21.531088 \n", "L 100.085262 32.556968 \n", "L 100.420397 38.042702 \n", "L 100.755532 38.846814 \n", "L 101.090667 45.123869 \n", "L 101.760938 50.01453 \n", "L 102.096073 48.865216 \n", "L 102.431208 46.663607 \n", "L 102.766343 50.066847 \n", "L 103.101478 47.34876 \n", "L 103.771748 64.230389 \n", "L 104.106883 49.479859 \n", "L 104.442019 43.334115 \n", "L 104.777154 61.813425 \n", "L 105.112289 49.557706 \n", "L 105.447424 21.106691 \n", "L 105.782559 29.668172 \n", "L 106.117694 34.84227 \n", "L 106.452829 42.628451 \n", "L 106.787965 40.812307 \n", "L 107.1231 55.911385 \n", "L 107.458235 59.605518 \n", "L 108.128505 36.199321 \n", "L 108.46364 45.036464 \n", "L 108.798775 14.76 \n", "L 109.13391 45.084085 \n", "L 109.469046 42.518932 \n", "L 109.804181 37.967885 \n", "L 110.139316 55.18309 \n", "L 110.474451 56.16315 \n", "L 110.809586 46.497356 \n", "L 111.144721 48.028317 \n", "L 111.479856 38.634136 \n", "L 111.814992 52.529751 \n", "L 112.150127 37.844465 \n", "L 112.485262 40.981907 \n", "L 112.820397 33.238008 \n", "L 113.155532 39.443087 \n", "L 113.490667 60.496187 \n", "L 113.825802 50.277265 \n", "L 114.160938 47.686719 \n", "L 114.496073 46.424703 \n", "L 114.831208 50.855461 \n", "L 115.166343 48.273233 \n", "L 115.501478 49.836441 \n", "L 115.836613 39.206593 \n", "L 116.171748 44.026413 \n", "L 116.506883 41.823642 \n", "L 116.842019 60.954437 \n", "L 117.177154 44.662076 \n", "L 117.512289 39.875476 \n", "L 117.847424 58.049682 \n", "L 118.182559 31.080546 \n", "L 118.517694 47.363207 \n", "L 118.852829 53.541446 \n", "L 119.187965 56.515578 \n", "L 119.858235 39.105241 \n", "L 120.19337 52.218408 \n", "L 120.528505 39.256148 \n", "L 120.86364 46.240404 \n", "L 121.198775 41.974781 \n", "L 121.53391 53.846738 \n", "L 121.869046 52.550587 \n", "L 122.204181 58.158541 \n", "L 122.539316 46.863136 \n", "L 122.874451 63.584532 \n", "L 123.209586 48.814532 \n", "L 123.544721 59.667405 \n", "L 123.879856 63.531298 \n", "L 124.214992 59.230598 \n", "L 124.550127 66.808144 \n", "L 124.885262 45.554124 \n", "L 125.555532 66.620828 \n", "L 125.890667 58.426301 \n", "L 126.225802 53.024701 \n", "L 126.560938 39.026892 \n", "L 126.896073 67.900021 \n", "L 127.566343 39.890492 \n", "L 127.901478 46.506345 \n", "L 128.236613 55.657857 \n", "L 128.571748 56.188715 \n", "L 128.906883 51.804287 \n", "L 129.577154 55.269836 \n", "L 129.912289 54.751032 \n", "L 130.247424 64.196387 \n", "L 130.582559 67.982047 \n", "L 130.917694 61.594325 \n", "L 131.252829 66.488742 \n", "L 131.587965 51.616373 \n", "L 131.9231 67.834303 \n", "L 132.258235 55.916535 \n", "L 132.59337 58.952037 \n", "L 132.928505 76.572419 \n", "L 133.26364 40.997091 \n", "L 133.598775 73.107654 \n", "L 133.93391 71.857237 \n", "L 134.269046 51.203858 \n", "L 134.939316 80.93351 \n", "L 135.274451 44.688581 \n", "L 135.609586 67.69281 \n", "L 135.944721 69.479604 \n", "L 136.279856 77.991395 \n", "L 136.614992 63.03516 \n", "L 136.950127 64.080139 \n", "L 137.285262 60.579956 \n", "L 137.620397 58.380705 \n", "L 137.955532 76.36534 \n", "L 138.290667 53.871803 \n", "L 138.625802 74.117331 \n", "L 138.960938 72.100446 \n", "L 139.296073 78.896079 \n", "L 139.631208 78.689855 \n", "L 139.966343 71.499177 \n", "L 140.301478 67.016817 \n", "L 140.636613 80.978291 \n", "L 140.971748 74.063908 \n", "L 141.306883 82.309214 \n", "L 141.642019 74.399541 \n", "L 141.977154 56.450035 \n", "L 142.312289 65.276455 \n", "L 142.647424 57.087469 \n", "L 142.982559 81.949268 \n", "L 143.317694 67.81918 \n", "L 143.652829 60.835429 \n", "L 143.987965 80.972241 \n", "L 144.3231 56.774488 \n", "L 144.658235 77.152168 \n", "L 144.99337 77.330111 \n", "L 145.328505 82.816353 \n", "L 145.66364 62.89569 \n", "L 145.998775 101.728936 \n", "L 146.33391 89.605577 \n", "L 146.669046 91.743255 \n", "L 147.339316 69.964368 \n", "L 147.674451 76.091184 \n", "L 148.009586 99.109418 \n", "L 148.344721 96.737219 \n", "L 148.679856 72.754827 \n", "L 149.014992 84.190594 \n", "L 149.350127 80.783762 \n", "L 149.685262 80.462836 \n", "L 150.020397 87.558727 \n", "L 150.355532 72.004855 \n", "L 150.690667 100.381437 \n", "L 151.025802 82.76547 \n", "L 151.696073 72.484614 \n", "L 152.031208 78.231729 \n", "L 152.366343 91.292196 \n", "L 152.701478 84.314366 \n", "L 153.036613 82.234719 \n", "L 153.371748 85.793342 \n", "L 153.706883 96.798187 \n", "L 154.042019 84.319305 \n", "L 154.377154 76.755225 \n", "L 154.712289 82.878741 \n", "L 155.047424 73.441116 \n", "L 155.382559 94.434888 \n", "L 155.717694 90.928361 \n", "L 156.052829 95.051205 \n", "L 156.387965 88.54265 \n", "L 156.7231 95.295112 \n", "L 157.058235 91.186746 \n", "L 157.39337 93.997711 \n", "L 157.728505 106.357914 \n", "L 158.06364 100.123307 \n", "L 158.398775 124.556246 \n", "L 158.73391 98.441045 \n", "L 159.069046 92.631761 \n", "L 159.404181 111.994417 \n", "L 159.739316 101.774558 \n", "L 160.074451 112.960521 \n", "L 160.409586 94.004235 \n", "L 160.744721 96.975301 \n", "L 161.079856 94.022321 \n", "L 161.414992 101.485591 \n", "L 161.750127 92.555663 \n", "L 162.085262 113.659214 \n", "L 162.420397 99.817401 \n", "L 162.755532 104.741996 \n", "L 163.090667 96.429009 \n", "L 163.425802 110.817288 \n", "L 163.760938 87.88694 \n", "L 164.096073 110.958457 \n", "L 164.431208 111.363763 \n", "L 164.766343 107.884371 \n", "L 165.436613 87.543632 \n", "L 165.771748 107.976449 \n", "L 166.106883 110.810034 \n", "L 166.442019 107.195185 \n", "L 166.777154 106.43953 \n", "L 167.112289 96.322765 \n", "L 167.447424 115.789651 \n", "L 167.782559 118.260733 \n", "L 168.117694 106.940622 \n", "L 168.452829 102.408069 \n", "L 168.787965 89.180145 \n", "L 169.1231 109.694511 \n", "L 169.458235 117.616055 \n", "L 169.79337 117.204022 \n", "L 170.128505 97.80659 \n", "L 170.46364 123.859263 \n", "L 171.13391 111.797512 \n", "L 171.469046 101.818608 \n", "L 171.804181 119.903882 \n", "L 172.139316 120.979973 \n", "L 172.474451 114.140519 \n", "L 172.809586 115.84008 \n", "L 173.144721 118.562759 \n", "L 173.479856 129.871402 \n", "L 173.814992 114.43148 \n", "L 174.150127 110.454135 \n", "L 174.485262 121.600602 \n", "L 174.820397 122.778484 \n", "L 175.155532 126.896471 \n", "L 175.490667 123.102304 \n", "L 175.825802 109.965809 \n", "L 176.160938 126.443085 \n", "L 176.496073 109.167743 \n", "L 176.831208 133.899596 \n", "L 177.166343 118.036238 \n", "L 177.501478 115.984871 \n", "L 178.171748 139.225368 \n", "L 178.506883 116.119274 \n", "L 178.842019 129.404166 \n", "L 179.177154 113.709983 \n", "L 179.847424 126.569633 \n", "L 180.182559 115.226744 \n", "L 180.517694 129.282289 \n", "L 180.852829 125.918496 \n", "L 181.187965 115.175925 \n", "L 181.5231 119.261365 \n", "L 181.858235 137.783729 \n", "L 182.19337 128.523514 \n", "L 182.528505 103.564498 \n", "L 182.86364 125.028937 \n", "L 183.198775 133.263684 \n", "L 183.53391 126.567004 \n", "L 183.869046 130.705481 \n", "L 184.204181 142.722699 \n", "L 184.539316 126.269219 \n", "L 184.874451 135.905534 \n", "L 185.209586 132.455076 \n", "L 185.544721 149.412237 \n", "L 185.879856 129.601723 \n", "L 186.214992 130.007316 \n", "L 186.550127 140.761127 \n", "L 186.885262 134.989001 \n", "L 187.220397 133.31447 \n", "L 187.555532 128.858162 \n", "L 187.890667 136.658606 \n", "L 188.225802 126.315495 \n", "L 188.560938 123.610684 \n", "L 188.896073 138.78895 \n", "L 189.231208 147.510449 \n", "L 189.566343 125.873327 \n", "L 190.236613 151.15605 \n", "L 190.571748 123.473774 \n", "L 190.906883 152.106502 \n", "L 191.242019 126.895471 \n", "L 191.577154 149.237282 \n", "L 191.912289 135.887236 \n", "L 192.247424 132.08952 \n", "L 192.917694 137.784493 \n", "L 193.252829 138.578811 \n", "L 193.587965 136.398227 \n", "L 193.9231 136.819054 \n", "L 194.258235 154.657947 \n", "L 194.59337 136.729532 \n", "L 194.928505 145.6692 \n", "L 195.26364 148.314444 \n", "L 195.598775 126.131876 \n", "L 195.93391 153.338403 \n", "L 196.269046 154.236873 \n", "L 196.604181 123.903587 \n", "L 196.939316 138.51111 \n", "L 197.274451 136.908472 \n", "L 197.609586 144.408098 \n", "L 197.944721 136.512248 \n", "L 198.279856 131.984122 \n", "L 198.614992 150.486545 \n", "L 198.950127 138.117349 \n", "L 199.285262 142.125073 \n", "L 199.620397 129.792115 \n", "L 199.955532 146.859974 \n", "L 200.290667 137.656566 \n", "L 200.625802 159.033068 \n", "L 200.960938 143.287227 \n", "L 201.296073 140.894174 \n", "L 201.631208 154.002865 \n", "L 201.966343 155.796819 \n", "L 202.301478 141.997105 \n", "L 202.636613 141.873959 \n", "L 202.971748 143.413691 \n", "L 203.306883 141.520915 \n", "L 203.642019 158.191827 \n", "L 203.977154 137.50351 \n", "L 204.312289 153.17851 \n", "L 204.647424 141.464009 \n", "L 204.982559 143.130047 \n", "L 205.317694 133.890811 \n", "L 205.652829 136.494426 \n", "L 205.987965 149.425923 \n", "L 206.658235 140.113423 \n", "L 206.99337 144.887655 \n", "L 207.328505 145.767734 \n", "L 207.66364 138.250434 \n", "L 207.998775 146.850459 \n", "L 208.33391 141.284174 \n", "L 208.669046 143.835502 \n", "L 209.004181 158.903154 \n", "L 209.339316 154.548242 \n", "L 209.674451 165.96 \n", "L 210.009586 134.823895 \n", "L 210.344721 138.67057 \n", "L 210.679856 151.367705 \n", "L 211.014992 133.467852 \n", "L 211.350127 164.494342 \n", "L 211.685262 144.529235 \n", "L 212.020397 150.848695 \n", "L 212.355532 150.914142 \n", "L 212.690667 155.378115 \n", "L 213.025802 128.632749 \n", "L 213.360938 144.495434 \n", "L 213.696073 148.088511 \n", "L 214.031208 146.137075 \n", "L 214.366343 124.879273 \n", "L 214.701478 134.323865 \n", "L 215.036613 155.504248 \n", "L 215.371748 146.751739 \n", "L 216.042019 145.38896 \n", "L 216.377154 149.815211 \n", "L 216.712289 143.822514 \n", "L 217.047424 142.069918 \n", "L 217.382559 135.954319 \n", "L 217.717694 144.225453 \n", "L 218.052829 146.464854 \n", "L 218.387965 156.775247 \n", "L 218.7231 151.135789 \n", "L 219.058235 120.227011 \n", "L 219.39337 141.620376 \n", "L 219.728505 143.748562 \n", "L 220.06364 126.056687 \n", "L 220.398775 138.445322 \n", "L 220.73391 157.138053 \n", "L 221.069046 143.868595 \n", "L 221.404181 153.936523 \n", "L 221.739316 127.584191 \n", "L 222.074451 143.54946 \n", "L 222.409586 130.311764 \n", "L 222.744721 147.35676 \n", "L 223.079856 136.632438 \n", "L 223.414992 134.192415 \n", "L 223.750127 134.369496 \n", "L 224.085262 133.819879 \n", "L 224.420397 138.477663 \n", "L 224.755532 156.344775 \n", "L 225.090667 144.877298 \n", "L 225.425802 153.590447 \n", "L 225.760938 124.716188 \n", "L 226.096073 134.79917 \n", "L 226.766343 141.504946 \n", "L 227.101478 140.680083 \n", "L 227.436613 137.494309 \n", "L 227.771748 139.026156 \n", "L 228.106883 134.947166 \n", "L 228.442019 127.365444 \n", "L 228.777154 132.093496 \n", "L 229.112289 127.946021 \n", "L 229.447424 142.168886 \n", "L 229.782559 150.509804 \n", "L 230.117694 119.885082 \n", "L 230.452829 126.566601 \n", "L 230.787965 142.409814 \n", "L 231.1231 137.976622 \n", "L 231.458235 153.415608 \n", "L 231.79337 131.730513 \n", "L 232.128505 133.381606 \n", "L 232.46364 133.005213 \n", "L 232.798775 122.511189 \n", "L 233.13391 116.659335 \n", "L 233.804181 145.177956 \n", "L 234.139316 125.205251 \n", "L 234.474451 139.273143 \n", "L 234.809586 128.719875 \n", "L 235.144721 132.583828 \n", "L 235.814992 117.535868 \n", "L 236.150127 126.708934 \n", "L 236.485262 126.663883 \n", "L 236.820397 125.540395 \n", "L 237.155532 129.728451 \n", "L 237.490667 129.103023 \n", "L 237.825802 136.631439 \n", "L 238.160938 112.479726 \n", "L 238.496073 131.592824 \n", "L 238.831208 105.319914 \n", "L 239.166343 141.392543 \n", "L 239.501478 131.446373 \n", "L 239.836613 126.596423 \n", "L 240.171748 132.710248 \n", "L 240.506883 155.6859 \n", "L 240.842019 110.266371 \n", "L 241.177154 121.812263 \n", "L 241.512289 129.327074 \n", "L 241.847424 109.058873 \n", "L 242.517694 127.93046 \n", "L 242.852829 127.655335 \n", "L 243.187965 101.100084 \n", "L 243.5231 114.685435 \n", "L 243.858235 116.485125 \n", "L 244.19337 134.518195 \n", "L 244.528505 115.448721 \n", "L 244.86364 110.555254 \n", "L 245.198775 119.553533 \n", "L 245.53391 116.123524 \n", "L 245.869046 102.514994 \n", "L 246.204181 120.017011 \n", "L 246.539316 119.943759 \n", "L 246.874451 133.978672 \n", "L 247.209586 100.938322 \n", "L 247.544721 116.163515 \n", "L 247.879856 110.83836 \n", "L 248.214992 119.842349 \n", "L 248.550127 93.594694 \n", "L 248.885262 113.91589 \n", "L 249.220397 102.738063 \n", "L 249.555532 113.634371 \n", "L 249.890667 118.305075 \n", "L 250.225802 108.55523 \n", "L 250.560938 103.510515 \n", "L 250.896073 107.064743 \n", "L 251.231208 100.600565 \n", "L 251.566343 101.591273 \n", "L 251.901478 101.255869 \n", "L 252.571748 99.00492 \n", "L 252.906883 98.935157 \n", "L 253.242019 100.339615 \n", "L 253.577154 116.971299 \n", "L 253.912289 106.870787 \n", "L 254.247424 108.42852 \n", "L 254.582559 108.473647 \n", "L 254.917694 104.296306 \n", "L 255.252829 115.70195 \n", "L 255.587965 90.170256 \n", "L 255.9231 100.212505 \n", "L 256.258235 99.069072 \n", "L 256.59337 104.550048 \n", "L 256.928505 88.10503 \n", "L 257.598775 122.519519 \n", "L 257.93391 111.091175 \n", "L 258.269046 86.08018 \n", "L 258.604181 99.776671 \n", "L 258.939316 99.86324 \n", "L 259.274451 104.279022 \n", "L 259.609586 98.52269 \n", "L 259.944721 105.804131 \n", "L 260.279856 93.738329 \n", "L 260.614992 108.0413 \n", "L 260.950127 95.45474 \n", "L 261.285262 88.682498 \n", "L 261.620397 100.845327 \n", "L 261.955532 88.463805 \n", "L 262.290667 98.872803 \n", "L 262.625802 94.605555 \n", "L 262.960938 97.809408 \n", "L 263.631208 82.496181 \n", "L 264.301478 97.039864 \n", "L 264.636613 93.121583 \n", "L 264.971748 105.186654 \n", "L 265.306883 95.005097 \n", "L 265.642019 99.631876 \n", "L 265.977154 88.209012 \n", "L 266.312289 85.970676 \n", "L 266.647424 85.27872 \n", "L 266.982559 81.293832 \n", "L 267.317694 82.303372 \n", "L 267.652829 98.897848 \n", "L 267.987965 92.928675 \n", "L 268.3231 70.870747 \n", "L 268.658235 77.844387 \n", "L 268.99337 78.106689 \n", "L 269.328505 66.709955 \n", "L 269.66364 95.768692 \n", "L 269.998775 96.25946 \n", "L 270.33391 80.015546 \n", "L 270.669046 86.511928 \n", "L 271.004181 79.372592 \n", "L 271.339316 86.159739 \n", "L 271.674451 76.039655 \n", "L 272.009586 77.783681 \n", "L 272.344721 86.995124 \n", "L 272.679856 75.471741 \n", "L 273.014992 85.446228 \n", "L 273.350127 76.0489 \n", "L 273.685262 89.544298 \n", "L 274.020397 92.565636 \n", "L 274.690667 70.2588 \n", "L 275.025802 84.032657 \n", "L 275.360937 81.538159 \n", "L 275.696073 82.912849 \n", "L 276.031208 82.487098 \n", "L 276.366343 63.105432 \n", "L 276.701478 66.895204 \n", "L 277.036613 80.786107 \n", "L 277.371748 68.820808 \n", "L 277.706883 88.593721 \n", "L 278.042019 61.477804 \n", "L 278.377154 78.660639 \n", "L 278.712289 67.2971 \n", "L 279.047424 69.666813 \n", "L 279.382559 73.885986 \n", "L 279.717694 74.147886 \n", "L 280.052829 65.215744 \n", "L 280.387965 69.66156 \n", "L 280.7231 70.131763 \n", "L 281.058235 56.316003 \n", "L 281.728505 77.26494 \n", "L 282.06364 49.474861 \n", "L 282.398775 65.693294 \n", "L 282.73391 52.035117 \n", "L 283.069046 73.64204 \n", "L 283.404181 67.602622 \n", "L 283.739316 71.023396 \n", "L 284.074451 65.047365 \n", "L 284.409586 56.754555 \n", "L 284.744721 73.643153 \n", "L 285.079856 62.71314 \n", "L 285.414992 57.894976 \n", "L 285.750127 64.300362 \n", "L 286.085262 62.259906 \n", "L 286.420397 47.2557 \n", "L 286.755532 68.142477 \n", "L 287.090667 63.441735 \n", "L 287.425802 82.488975 \n", "L 287.760938 53.720298 \n", "L 288.096073 70.589806 \n", "L 288.431208 64.518903 \n", "L 288.766343 55.050434 \n", "L 289.101478 62.958097 \n", "L 289.436613 64.387343 \n", "L 289.771748 72.193278 \n", "L 290.106883 65.185483 \n", "L 290.442019 54.856697 \n", "L 290.777154 61.263567 \n", "L 291.112289 50.25981 \n", "L 291.447424 50.362596 \n", "L 291.782559 60.025544 \n", "L 292.452829 44.353727 \n", "L 292.787965 62.173224 \n", "L 293.1231 59.021226 \n", "L 293.458235 60.520561 \n", "L 293.79337 41.121591 \n", "L 294.128505 36.692273 \n", "L 294.46364 57.477869 \n", "L 294.798775 44.359036 \n", "L 295.13391 53.07628 \n", "L 295.469046 51.78032 \n", "L 295.804181 25.42078 \n", "L 296.139316 41.580811 \n", "L 296.474451 42.365492 \n", "L 296.809586 58.023266 \n", "L 297.144721 50.473318 \n", "L 297.479856 48.329061 \n", "L 297.814992 32.164212 \n", "L 298.150127 47.864549 \n", "L 298.485262 57.128255 \n", "L 298.820397 48.603396 \n", "L 299.155532 44.726989 \n", "L 299.490667 54.743146 \n", "L 299.825802 44.179132 \n", "L 300.160938 37.858162 \n", "L 300.496073 37.644097 \n", "L 300.831208 62.819607 \n", "L 301.166343 64.192574 \n", "L 301.501478 24.978951 \n", "L 301.836613 39.514686 \n", "L 302.171748 62.359091 \n", "L 302.506883 45.460198 \n", "L 302.842019 45.461459 \n", "L 303.177154 51.842076 \n", "L 303.512289 66.945154 \n", "L 303.847424 37.952968 \n", "L 304.182559 65.425148 \n", "L 304.517694 38.848347 \n", "L 305.187965 62.057031 \n", "L 305.5231 35.300851 \n", "L 305.858235 45.266341 \n", "L 306.528505 53.449324 \n", "L 306.86364 47.645534 \n", "L 307.198775 44.344997 \n", "L 307.53391 60.311342 \n", "L 307.869046 67.324056 \n", "L 308.204181 35.333636 \n", "L 308.874451 45.962166 \n", "L 309.209586 34.147614 \n", "L 309.544721 43.466317 \n", "L 309.879856 47.983766 \n", "L 310.214992 42.97979 \n", "L 310.550127 64.61539 \n", "L 310.885262 32.231007 \n", "L 311.220397 42.748257 \n", "L 311.555532 60.854392 \n", "L 311.890667 66.013623 \n", "L 312.225802 38.634717 \n", "L 312.560938 44.384555 \n", "L 312.896073 56.069565 \n", "L 313.231208 41.944802 \n", "L 313.566343 56.082512 \n", "L 313.901478 38.861586 \n", "L 314.236613 51.85799 \n", "L 314.571748 54.809439 \n", "L 314.906883 55.578683 \n", "L 315.242019 41.366379 \n", "L 315.577154 54.696263 \n", "L 315.912289 58.938674 \n", "L 316.247424 36.956196 \n", "L 316.582559 50.308943 \n", "L 316.917694 45.634163 \n", "L 317.252829 34.133231 \n", "L 317.587965 31.42528 \n", "L 317.9231 56.351939 \n", "L 318.258235 66.976591 \n", "L 318.59337 42.897183 \n", "L 318.928505 34.801016 \n", "L 319.26364 44.260757 \n", "L 319.598775 38.327159 \n", "L 319.93391 47.899152 \n", "L 320.269046 40.202831 \n", "L 320.604181 40.418965 \n", "L 320.939316 24.67299 \n", "L 321.274451 36.422693 \n", "L 321.609586 36.979901 \n", "L 321.944721 61.885535 \n", "L 322.279856 26.057064 \n", "L 322.614992 33.010733 \n", "L 322.950127 50.947989 \n", "L 323.285262 42.209021 \n", "L 323.620397 45.162556 \n", "L 323.955532 31.027308 \n", "L 324.290667 39.548592 \n", "L 324.625802 54.399791 \n", "L 324.960938 54.800035 \n", "L 325.296073 55.681531 \n", "L 325.631208 37.760534 \n", "L 325.966343 46.015229 \n", "L 326.301478 37.512696 \n", "L 326.636613 37.14587 \n", "L 326.971748 50.534958 \n", "L 327.306883 54.403227 \n", "L 327.642019 51.589443 \n", "L 327.977154 45.876465 \n", "L 328.312289 43.429461 \n", "L 328.647424 42.608991 \n", "L 328.982559 46.236774 \n", "L 329.317694 62.89899 \n", "L 329.652829 42.091619 \n", "L 329.987965 45.736363 \n", "L 330.3231 55.907188 \n", "L 330.658235 54.976242 \n", "L 330.99337 62.94528 \n", "L 331.328505 42.551763 \n", "L 331.66364 46.755005 \n", "L 331.998775 45.275377 \n", "L 332.33391 61.839169 \n", "L 332.669046 43.877586 \n", "L 333.004181 43.844697 \n", "L 333.339316 44.944691 \n", "L 333.674451 63.125242 \n", "L 334.009586 30.513996 \n", "L 334.344721 46.929333 \n", "L 334.679856 46.210619 \n", "L 335.014992 59.244902 \n", "L 335.350127 38.829016 \n", "L 335.685262 54.121919 \n", "L 336.020397 63.833724 \n", "L 336.355532 54.401601 \n", "L 336.690667 65.528539 \n", "L 337.025802 58.807233 \n", "L 337.360937 43.950544 \n", "L 337.696073 47.947101 \n", "L 338.031208 48.925485 \n", "L 338.366343 46.595687 \n", "L 338.701478 63.493921 \n", "L 339.036613 63.088734 \n", "L 339.371748 64.237493 \n", "L 339.706883 57.540756 \n", "L 340.042019 58.078981 \n", "L 340.377154 57.261001 \n", "L 340.712289 47.108415 \n", "L 341.047424 55.915524 \n", "L 341.382559 53.928131 \n", "L 341.717694 45.915146 \n", "L 342.052829 50.110936 \n", "L 342.387965 61.140545 \n", "L 342.7231 58.862986 \n", "L 343.058235 36.426892 \n", "L 343.39337 87.21454 \n", "L 343.728505 71.314331 \n", "L 344.06364 65.023435 \n", "L 344.398775 71.874138 \n", "L 344.73391 66.120606 \n", "L 345.069046 56.458086 \n", "L 345.404181 67.016596 \n", "L 345.739316 63.353299 \n", "L 346.074451 53.051668 \n", "L 346.744721 84.061934 \n", "L 347.079856 83.488185 \n", "L 347.414992 71.964509 \n", "L 347.750127 75.980809 \n", "L 348.085262 38.001669 \n", "L 348.420397 72.409037 \n", "L 348.755532 69.094975 \n", "L 349.090667 90.148197 \n", "L 349.425802 76.75297 \n", "L 349.760938 72.457868 \n", "L 350.096073 63.296881 \n", "L 350.431208 72.95957 \n", "L 350.766343 76.989967 \n", "L 351.101478 78.851165 \n", "L 351.436613 73.271072 \n", "L 351.771748 57.0232 \n", "L 352.106883 64.932847 \n", "L 352.442019 64.016492 \n", "L 352.777154 65.72255 \n", "L 353.112289 74.67013 \n", "L 353.447424 86.837408 \n", "L 353.782559 63.951593 \n", "L 354.117694 65.666062 \n", "L 354.452829 79.107465 \n", "L 354.787965 101.451466 \n", "L 355.1231 73.598768 \n", "L 355.458235 80.89558 \n", "L 355.79337 77.590992 \n", "L 356.128505 71.000879 \n", "L 356.46364 89.26004 \n", "L 356.798775 92.558124 \n", "L 357.13391 87.124488 \n", "L 357.469046 79.080655 \n", "L 357.804181 95.487891 \n", "L 358.139316 58.545759 \n", "L 358.474451 77.519692 \n", "L 358.809586 83.134692 \n", "L 359.144721 94.282429 \n", "L 359.479856 75.854837 \n", "L 359.814992 65.074323 \n", "L 360.150127 65.32237 \n", "L 360.485262 92.363018 \n", "L 360.820397 71.87024 \n", "L 361.155532 82.868709 \n", "L 361.490667 87.69368 \n", "L 361.825802 78.198965 \n", "L 362.160938 87.077575 \n", "L 362.496073 82.462158 \n", "L 362.831208 84.073484 \n", "L 363.166343 93.272929 \n", "L 363.836613 75.409046 \n", "L 364.171748 88.763673 \n", "L 364.506883 93.839258 \n", "L 364.842019 119.424241 \n", "L 365.177154 92.525979 \n", "L 365.512289 84.975138 \n", "L 365.847424 93.14001 \n", "L 366.182559 104.508942 \n", "L 366.517694 92.91488 \n", "L 366.852829 93.212533 \n", "L 367.187965 86.356417 \n", "L 367.5231 108.714747 \n", "L 367.858235 90.902056 \n", "L 368.19337 107.857921 \n", "L 368.528505 98.097803 \n", "L 369.198775 84.472201 \n", "L 369.53391 89.489557 \n", "L 369.869046 106.341411 \n", "L 370.204181 107.350688 \n", "L 370.539316 100.367594 \n", "L 370.874451 103.063221 \n", "L 371.209586 104.363041 \n", "L 371.544721 91.682501 \n", "L 371.879856 114.137115 \n", "L 372.214992 106.599804 \n", "L 372.550127 108.584077 \n", "L 372.885262 92.219318 \n", "L 373.220397 101.018691 \n", "L 373.555532 116.296612 \n", "L 373.890667 96.993194 \n", "L 374.225802 118.953182 \n", "L 374.560938 122.562339 \n", "L 374.896073 100.513015 \n", "L 375.231208 121.144048 \n", "L 375.566343 106.768413 \n", "L 376.236613 105.521416 \n", "L 376.571748 128.923038 \n", "L 376.906883 115.418919 \n", "L 377.242019 106.55171 \n", "L 377.577154 113.095772 \n", "L 377.912289 112.674382 \n", "L 378.247424 108.1739 \n", "L 378.582559 91.720892 \n", "L 378.917694 115.766163 \n", "L 379.252829 129.440369 \n", "L 379.587965 98.58422 \n", "L 379.9231 100.002236 \n", "L 380.258235 100.494226 \n", "L 380.59337 102.929046 \n", "L 380.928505 96.913464 \n", "L 381.598775 128.475368 \n", "L 381.93391 113.686341 \n", "L 382.269046 112.441606 \n", "L 382.604181 106.273918 \n", "L 382.939316 118.336555 \n", "L 383.274451 100.382387 \n", "L 383.609586 128.56343 \n", "L 383.944721 126.966493 \n", "L 384.279856 117.755986 \n", "L 384.614992 122.722214 \n", "L 384.950127 118.78685 \n", "L 385.285262 103.746844 \n", "L 385.620397 103.717631 \n", "L 385.955532 111.164847 \n", "L 386.290667 144.333194 \n", "L 386.625802 115.060377 \n", "L 386.960938 113.486222 \n", "L 386.960938 113.486222 \n", "\" clip-path=\"url(#p7f0f2eafbd)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_3\">\n", "    <path d=\"M 52.**********.52 \n", "L 52.160938 7.2 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_4\">\n", "    <path d=\"M 386.**********.52 \n", "L 386.960938 7.2 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_5\">\n", "    <path d=\"M 52.**********.52 \n", "L 386.**********.52 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_6\">\n", "    <path d=\"M 52.160938 7.2 \n", "L 386.960938 7.2 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "  </g>\n", " </g>\n", " <defs>\n", "  <clipPath id=\"p7f0f2eafbd\">\n", "   <rect x=\"52.160938\" y=\"7.2\" width=\"334.8\" height=\"166.32\"/>\n", "  </clipPath>\n", " </defs>\n", "</svg>\n"], "text/plain": ["<Figure size 600x300 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["T = 1000  # 总共产生1000个点\n", "time = torch.arange(1, T + 1, dtype=torch.float32)\n", "x = torch.sin(0.01 * time) + torch.normal(0, 0.2, (T,))\n", "d2l.plot(time, [x], 'time', 'x', xlim=[1, 1000], figsize=(6, 3))"]}, {"cell_type": "markdown", "id": "5c8f7839", "metadata": {"origin_pos": 7}, "source": ["接下来，我们将这个序列转换为模型的*特征－标签*（feature-label）对。\n", "基于嵌入维度$\\tau$，我们[**将数据映射为数据对$y_t = x_t$\n", "和$\\mathbf{x}_t = [x_{t-\\tau}, \\ldots, x_{t-1}]$。**]\n", "这比我们提供的数据样本少了$\\tau$个，\n", "因为我们没有足够的历史记录来描述前$\\tau$个数据样本。\n", "一个简单的解决办法是：如果拥有足够长的序列就丢弃这几项；\n", "另一个方法是用零填充序列。\n", "在这里，我们仅使用前600个“特征－标签”对进行训练。\n"]}, {"cell_type": "code", "execution_count": 3, "id": "123d433e", "metadata": {"execution": {"iopub.execute_input": "2022-12-07T16:51:37.302653Z", "iopub.status.busy": "2022-12-07T16:51:37.302084Z", "iopub.status.idle": "2022-12-07T16:51:37.307456Z", "shell.execute_reply": "2022-12-07T16:51:37.306394Z"}, "origin_pos": 8, "tab": ["pytorch"]}, "outputs": [], "source": ["tau = 4\n", "features = torch.zeros((T - tau, tau))\n", "for i in range(tau):\n", "    features[:, i] = x[i: T - tau + i]\n", "labels = x[tau:].reshape((-1, 1))"]}, {"cell_type": "code", "execution_count": 4, "id": "3906751b", "metadata": {"execution": {"iopub.execute_input": "2022-12-07T16:51:37.311172Z", "iopub.status.busy": "2022-12-07T16:51:37.310360Z", "iopub.status.idle": "2022-12-07T16:51:37.315699Z", "shell.execute_reply": "2022-12-07T16:51:37.314612Z"}, "origin_pos": 10, "tab": ["pytorch"]}, "outputs": [], "source": ["batch_size, n_train = 16, 600\n", "# 只有前n_train个样本用于训练\n", "train_iter = d2l.load_array((features[:n_train], labels[:n_train]),\n", "                            batch_size, is_train=True)"]}, {"cell_type": "markdown", "id": "90efdebc", "metadata": {"origin_pos": 11}, "source": ["在这里，我们[**使用一个相当简单的架构训练模型：\n", "一个拥有两个全连接层的多层感知机**]，ReLU激活函数和平方损失。\n"]}, {"cell_type": "code", "execution_count": 5, "id": "f0db6b31", "metadata": {"execution": {"iopub.execute_input": "2022-12-07T16:51:37.318995Z", "iopub.status.busy": "2022-12-07T16:51:37.318418Z", "iopub.status.idle": "2022-12-07T16:51:37.323548Z", "shell.execute_reply": "2022-12-07T16:51:37.322784Z"}, "origin_pos": 13, "tab": ["pytorch"]}, "outputs": [], "source": ["# 初始化网络权重的函数\n", "def init_weights(m):\n", "    if type(m) == nn.Linear:\n", "        nn.init.xavier_uniform_(m.weight)\n", "\n", "# 一个简单的多层感知机\n", "def get_net():\n", "    net = nn.Sequential(nn.<PERSON>(4, 10),\n", "                        nn.ReLU(),\n", "                        nn.<PERSON><PERSON>(10, 1))\n", "    net.apply(init_weights)\n", "    return net\n", "\n", "# 平方损失。注意：MSELoss计算平方误差时不带系数1/2\n", "loss = nn.MSELoss(reduction='none')"]}, {"cell_type": "markdown", "id": "4444face", "metadata": {"origin_pos": 16}, "source": ["现在，准备[**训练模型**]了。实现下面的训练代码的方式与前面几节（如 :numref:`sec_linear_concise`）中的循环训练基本相同。因此，我们不会深入探讨太多细节。\n"]}, {"cell_type": "code", "execution_count": 6, "id": "fea52a0d", "metadata": {"execution": {"iopub.execute_input": "2022-12-07T16:51:37.326696Z", "iopub.status.busy": "2022-12-07T16:51:37.326223Z", "iopub.status.idle": "2022-12-07T16:51:37.572610Z", "shell.execute_reply": "2022-12-07T16:51:37.571762Z"}, "origin_pos": 18, "tab": ["pytorch"]}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["epoch 1, loss: 0.063133\n", "epoch 2, loss: 0.053832\n", "epoch 3, loss: 0.051174\n", "epoch 4, loss: 0.050547\n", "epoch 5, loss: 0.047369\n"]}], "source": ["def train(net, train_iter, loss, epochs, lr):\n", "    trainer = torch.optim.Adam(net.parameters(), lr)\n", "    for epoch in range(epochs):\n", "        for X, y in train_iter:\n", "            trainer.zero_grad()\n", "            l = loss(net(X), y)\n", "            l.sum().backward()\n", "            trainer.step()\n", "        print(f'epoch {epoch + 1}, '\n", "              f'loss: {d2l.evaluate_loss(net, train_iter, loss):f}')\n", "\n", "net = get_net()\n", "train(net, train_iter, loss, 5, 0.01)"]}, {"cell_type": "markdown", "id": "7be84ea6", "metadata": {"origin_pos": 21}, "source": ["## 预测\n", "\n", "由于训练损失很小，因此我们期望模型能有很好的工作效果。\n", "让我们看看这在实践中意味着什么。\n", "首先是检查[**模型预测下一个时间步**]的能力，\n", "也就是*单步预测*（one-step-ahead prediction）。\n"]}, {"cell_type": "code", "execution_count": 7, "id": "aee5b446", "metadata": {"execution": {"iopub.execute_input": "2022-12-07T16:51:37.576235Z", "iopub.status.busy": "2022-12-07T16:51:37.575673Z", "iopub.status.idle": "2022-12-07T16:51:37.788122Z", "shell.execute_reply": "2022-12-07T16:51:37.787282Z"}, "origin_pos": 22, "tab": ["pytorch"]}, "outputs": [{"data": {"image/svg+xml": ["<?xml version=\"1.0\" encoding=\"utf-8\" standalone=\"no\"?>\n", "<!DOCTYPE svg PUBLIC \"-//W3C//DTD SVG 1.1//EN\"\n", "  \"http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd\">\n", "<svg xmlns:xlink=\"http://www.w3.org/1999/xlink\" width=\"406.885938pt\" height=\"211.07625pt\" viewBox=\"0 0 406.**********.07625\" xmlns=\"http://www.w3.org/2000/svg\" version=\"1.1\">\n", " <metadata>\n", "  <rdf:RDF xmlns:dc=\"http://purl.org/dc/elements/1.1/\" xmlns:cc=\"http://creativecommons.org/ns#\" xmlns:rdf=\"http://www.w3.org/1999/02/22-rdf-syntax-ns#\">\n", "   <cc:Work>\n", "    <dc:type rdf:resource=\"http://purl.org/dc/dcmitype/StillImage\"/>\n", "    <dc:date>2022-12-07T16:51:37.740902</dc:date>\n", "    <dc:format>image/svg+xml</dc:format>\n", "    <dc:creator>\n", "     <cc:Agent>\n", "      <dc:title>Matplotlib v3.5.1, https://matplotlib.org/</dc:title>\n", "     </cc:Agent>\n", "    </dc:creator>\n", "   </cc:Work>\n", "  </rdf:RDF>\n", " </metadata>\n", " <defs>\n", "  <style type=\"text/css\">*{stroke-linejoin: round; stroke-linecap: butt}</style>\n", " </defs>\n", " <g id=\"figure_1\">\n", "  <g id=\"patch_1\">\n", "   <path d=\"M 0 211.07625 \n", "L 406.**********.07625 \n", "L 406.885938 0 \n", "L 0 0 \n", "z\n", "\" style=\"fill: #ffffff\"/>\n", "  </g>\n", "  <g id=\"axes_1\">\n", "   <g id=\"patch_2\">\n", "    <path d=\"M 52.**********.52 \n", "L 386.**********.52 \n", "L 386.960938 7.2 \n", "L 52.160938 7.2 \n", "z\n", "\" style=\"fill: #ffffff\"/>\n", "   </g>\n", "   <g id=\"matplotlib.axis_1\">\n", "    <g id=\"xtick_1\">\n", "     <g id=\"line2d_1\">\n", "      <path d=\"M 118.852829 173.52 \n", "L 118.852829 7.2 \n", "\" clip-path=\"url(#p5055f4ef74)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_2\">\n", "      <defs>\n", "       <path id=\"m61445d649d\" d=\"M 0 0 \n", "L 0 3.5 \n", "\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </defs>\n", "      <g>\n", "       <use xlink:href=\"#m61445d649d\" x=\"118.852829\" y=\"173.52\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_1\">\n", "      <!-- 200 -->\n", "      <g transform=\"translate(109.309079 188.118438)scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-32\" d=\"M 1228 531 \n", "L 3431 531 \n", "L 3431 0 \n", "L 469 0 \n", "L 469 531 \n", "Q 828 903 1448 1529 \n", "Q 2069 2156 2228 2338 \n", "Q 2531 2678 2651 2914 \n", "Q 2772 3150 2772 3378 \n", "Q 2772 3750 2511 3984 \n", "Q 2250 4219 1831 4219 \n", "Q 1534 4219 1204 4116 \n", "Q 875 4013 500 3803 \n", "L 500 4441 \n", "Q 881 4594 1212 4672 \n", "Q 1544 4750 1819 4750 \n", "Q 2544 4750 2975 4387 \n", "Q 3406 4025 3406 3419 \n", "Q 3406 3131 3298 2873 \n", "Q 3191 2616 2906 2266 \n", "Q 2828 2175 2409 1742 \n", "Q 1991 1309 1228 531 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "        <path id=\"DejaVuSans-30\" d=\"M 2034 4250 \n", "Q 1547 4250 1301 3770 \n", "Q 1056 3291 1056 2328 \n", "Q 1056 1369 1301 889 \n", "Q 1547 409 2034 409 \n", "Q 2525 409 2770 889 \n", "Q 3016 1369 3016 2328 \n", "Q 3016 3291 2770 3770 \n", "Q 2525 4250 2034 4250 \n", "z\n", "M 2034 4750 \n", "Q 2819 4750 3233 4129 \n", "Q 3647 3509 3647 2328 \n", "Q 3647 1150 3233 529 \n", "Q 2819 -91 2034 -91 \n", "Q 1250 -91 836 529 \n", "Q 422 1150 422 2328 \n", "Q 422 3509 836 4129 \n", "Q 1250 4750 2034 4750 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-32\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"127.246094\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_2\">\n", "     <g id=\"line2d_3\">\n", "      <path d=\"M 185.879856 173.52 \n", "L 185.879856 7.2 \n", "\" clip-path=\"url(#p5055f4ef74)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_4\">\n", "      <g>\n", "       <use xlink:href=\"#m61445d649d\" x=\"185.879856\" y=\"173.52\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_2\">\n", "      <!-- 400 -->\n", "      <g transform=\"translate(176.336106 188.118438)scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-34\" d=\"M 2419 4116 \n", "L 825 1625 \n", "L 2419 1625 \n", "L 2419 4116 \n", "z\n", "M 2253 4666 \n", "L 3047 4666 \n", "L 3047 1625 \n", "L 3713 1625 \n", "L 3713 1100 \n", "L 3047 1100 \n", "L 3047 0 \n", "L 2419 0 \n", "L 2419 1100 \n", "L 313 1100 \n", "L 313 1709 \n", "L 2253 4666 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-34\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"127.246094\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_3\">\n", "     <g id=\"line2d_5\">\n", "      <path d=\"M 252.906883 173.52 \n", "L 252.906883 7.2 \n", "\" clip-path=\"url(#p5055f4ef74)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_6\">\n", "      <g>\n", "       <use xlink:href=\"#m61445d649d\" x=\"252.906883\" y=\"173.52\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_3\">\n", "      <!-- 600 -->\n", "      <g transform=\"translate(243.363133 188.118438)scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-36\" d=\"M 2113 2584 \n", "Q 1688 2584 1439 2293 \n", "Q 1191 2003 1191 1497 \n", "Q 1191 994 1439 701 \n", "Q 1688 409 2113 409 \n", "Q 2538 409 2786 701 \n", "Q 3034 994 3034 1497 \n", "Q 3034 2003 2786 2293 \n", "Q 2538 2584 2113 2584 \n", "z\n", "M 3366 4563 \n", "L 3366 3988 \n", "Q 3128 4100 2886 4159 \n", "Q 2644 4219 2406 4219 \n", "Q 1781 4219 1451 3797 \n", "Q 1122 3375 1075 2522 \n", "Q 1259 2794 1537 2939 \n", "Q 1816 3084 2150 3084 \n", "Q 2853 3084 3261 2657 \n", "Q 3669 2231 3669 1497 \n", "Q 3669 778 3244 343 \n", "Q 2819 -91 2113 -91 \n", "Q 1303 -91 875 529 \n", "Q 447 1150 447 2328 \n", "Q 447 3434 972 4092 \n", "Q 1497 4750 2381 4750 \n", "Q 2619 4750 2861 4703 \n", "Q 3103 4656 3366 4563 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-36\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"127.246094\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_4\">\n", "     <g id=\"line2d_7\">\n", "      <path d=\"M 319.93391 173.52 \n", "L 319.93391 7.2 \n", "\" clip-path=\"url(#p5055f4ef74)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_8\">\n", "      <g>\n", "       <use xlink:href=\"#m61445d649d\" x=\"319.93391\" y=\"173.52\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_4\">\n", "      <!-- 800 -->\n", "      <g transform=\"translate(310.39016 188.118438)scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-38\" d=\"M 2034 2216 \n", "Q 1584 2216 1326 1975 \n", "Q 1069 1734 1069 1313 \n", "Q 1069 891 1326 650 \n", "Q 1584 409 2034 409 \n", "Q 2484 409 2743 651 \n", "Q 3003 894 3003 1313 \n", "Q 3003 1734 2745 1975 \n", "Q 2488 2216 2034 2216 \n", "z\n", "M 1403 2484 \n", "Q 997 2584 770 2862 \n", "Q 544 3141 544 3541 \n", "Q 544 4100 942 4425 \n", "Q 1341 4750 2034 4750 \n", "Q 2731 4750 3128 4425 \n", "Q 3525 4100 3525 3541 \n", "Q 3525 3141 3298 2862 \n", "Q 3072 2584 2669 2484 \n", "Q 3125 2378 3379 2068 \n", "Q 3634 1759 3634 1313 \n", "Q 3634 634 3220 271 \n", "Q 2806 -91 2034 -91 \n", "Q 1263 -91 848 271 \n", "Q 434 634 434 1313 \n", "Q 434 1759 690 2068 \n", "Q 947 2378 1403 2484 \n", "z\n", "M 1172 3481 \n", "Q 1172 3119 1398 2916 \n", "Q 1625 2713 2034 2713 \n", "Q 2441 2713 2670 2916 \n", "Q 2900 3119 2900 3481 \n", "Q 2900 3844 2670 4047 \n", "Q 2441 4250 2034 4250 \n", "Q 1625 4250 1398 4047 \n", "Q 1172 3844 1172 3481 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-38\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"127.246094\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_5\">\n", "     <g id=\"line2d_9\">\n", "      <path d=\"M 386.**********.52 \n", "L 386.960938 7.2 \n", "\" clip-path=\"url(#p5055f4ef74)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_10\">\n", "      <g>\n", "       <use xlink:href=\"#m61445d649d\" x=\"386.960938\" y=\"173.52\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_5\">\n", "      <!-- 1000 -->\n", "      <g transform=\"translate(374.235937 188.118438)scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-31\" d=\"M 794 531 \n", "L 1825 531 \n", "L 1825 4091 \n", "L 703 3866 \n", "L 703 4441 \n", "L 1819 4666 \n", "L 2450 4666 \n", "L 2450 531 \n", "L 3481 531 \n", "L 3481 0 \n", "L 794 0 \n", "L 794 531 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-31\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"127.246094\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"190.869141\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"text_6\">\n", "     <!-- time -->\n", "     <g transform=\"translate(208.264844 201.796563)scale(0.1 -0.1)\">\n", "      <defs>\n", "       <path id=\"DejaVuSans-74\" d=\"M 1172 4494 \n", "L 1172 3500 \n", "L 2356 3500 \n", "L 2356 3053 \n", "L 1172 3053 \n", "L 1172 1153 \n", "Q 1172 725 1289 603 \n", "Q 1406 481 1766 481 \n", "L 2356 481 \n", "L 2356 0 \n", "L 1766 0 \n", "Q 1100 0 847 248 \n", "Q 594 497 594 1153 \n", "L 594 3053 \n", "L 172 3053 \n", "L 172 3500 \n", "L 594 3500 \n", "L 594 4494 \n", "L 1172 4494 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-69\" d=\"M 603 3500 \n", "L 1178 3500 \n", "L 1178 0 \n", "L 603 0 \n", "L 603 3500 \n", "z\n", "M 603 4863 \n", "L 1178 4863 \n", "L 1178 4134 \n", "L 603 4134 \n", "L 603 4863 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-6d\" d=\"M 3328 2828 \n", "Q 3544 3216 3844 3400 \n", "Q 4144 3584 4550 3584 \n", "Q 5097 3584 5394 3201 \n", "Q 5691 2819 5691 2113 \n", "L 5691 0 \n", "L 5113 0 \n", "L 5113 2094 \n", "Q 5113 2597 4934 2840 \n", "Q 4756 3084 4391 3084 \n", "Q 3944 3084 3684 2787 \n", "Q 3425 2491 3425 1978 \n", "L 3425 0 \n", "L 2847 0 \n", "L 2847 2094 \n", "Q 2847 2600 2669 2842 \n", "Q 2491 3084 2119 3084 \n", "Q 1678 3084 1418 2786 \n", "Q 1159 2488 1159 1978 \n", "L 1159 0 \n", "L 581 0 \n", "L 581 3500 \n", "L 1159 3500 \n", "L 1159 2956 \n", "Q 1356 3278 1631 3431 \n", "Q 1906 3584 2284 3584 \n", "Q 2666 3584 2933 3390 \n", "Q 3200 3197 3328 2828 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-65\" d=\"M 3597 1894 \n", "L 3597 1613 \n", "L 953 1613 \n", "Q 991 1019 1311 708 \n", "Q 1631 397 2203 397 \n", "Q 2534 397 2845 478 \n", "Q 3156 559 3463 722 \n", "L 3463 178 \n", "Q 3153 47 2828 -22 \n", "Q 2503 -91 2169 -91 \n", "Q 1331 -91 842 396 \n", "Q 353 884 353 1716 \n", "Q 353 2575 817 3079 \n", "Q 1281 3584 2069 3584 \n", "Q 2775 3584 3186 3129 \n", "Q 3597 2675 3597 1894 \n", "z\n", "M 3022 2063 \n", "Q 3016 2534 2758 2815 \n", "Q 2500 3097 2075 3097 \n", "Q 1594 3097 1305 2825 \n", "Q 1016 2553 972 2059 \n", "L 3022 2063 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "      </defs>\n", "      <use xlink:href=\"#DejaVuSans-74\"/>\n", "      <use xlink:href=\"#DejaVuSans-69\" x=\"39.208984\"/>\n", "      <use xlink:href=\"#DejaVuSans-6d\" x=\"66.992188\"/>\n", "      <use xlink:href=\"#DejaVuSans-65\" x=\"164.404297\"/>\n", "     </g>\n", "    </g>\n", "   </g>\n", "   <g id=\"matplotlib.axis_2\">\n", "    <g id=\"ytick_1\">\n", "     <g id=\"line2d_11\">\n", "      <path d=\"M 52.160938 166.995592 \n", "L 386.960938 166.995592 \n", "\" clip-path=\"url(#p5055f4ef74)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_12\">\n", "      <defs>\n", "       <path id=\"m35582aff21\" d=\"M 0 0 \n", "L -3.5 0 \n", "\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </defs>\n", "      <g>\n", "       <use xlink:href=\"#m35582aff21\" x=\"52.160938\" y=\"166.995592\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_7\">\n", "      <!-- −1.5 -->\n", "      <g transform=\"translate(20.878125 170.794811)scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-2212\" d=\"M 678 2272 \n", "L 4684 2272 \n", "L 4684 1741 \n", "L 678 1741 \n", "L 678 2272 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "        <path id=\"DejaVuSans-2e\" d=\"M 684 794 \n", "L 1344 794 \n", "L 1344 0 \n", "L 684 0 \n", "L 684 794 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "        <path id=\"DejaVuSans-35\" d=\"M 691 4666 \n", "L 3169 4666 \n", "L 3169 4134 \n", "L 1269 4134 \n", "L 1269 2991 \n", "Q 1406 3038 1543 3061 \n", "Q 1681 3084 1819 3084 \n", "Q 2600 3084 3056 2656 \n", "Q 3513 2228 3513 1497 \n", "Q 3513 744 3044 326 \n", "Q 2575 -91 1722 -91 \n", "Q 1428 -91 1123 -41 \n", "Q 819 9 494 109 \n", "L 494 744 \n", "Q 775 591 1075 516 \n", "Q 1375 441 1709 441 \n", "Q 2250 441 2565 725 \n", "Q 2881 1009 2881 1497 \n", "Q 2881 1984 2565 2268 \n", "Q 2250 2553 1709 2553 \n", "Q 1456 2553 1204 2497 \n", "Q 953 2441 691 2322 \n", "L 691 4666 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-2212\"/>\n", "       <use xlink:href=\"#DejaVuSans-31\" x=\"83.789062\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"147.412109\"/>\n", "       <use xlink:href=\"#DejaVuSans-35\" x=\"179.199219\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_2\">\n", "     <g id=\"line2d_13\">\n", "      <path d=\"M 52.160938 142.631734 \n", "L 386.960938 142.631734 \n", "\" clip-path=\"url(#p5055f4ef74)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_14\">\n", "      <g>\n", "       <use xlink:href=\"#m35582aff21\" x=\"52.160938\" y=\"142.631734\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_8\">\n", "      <!-- −1.0 -->\n", "      <g transform=\"translate(20.878125 146.430952)scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-2212\"/>\n", "       <use xlink:href=\"#DejaVuSans-31\" x=\"83.789062\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"147.412109\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"179.199219\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_3\">\n", "     <g id=\"line2d_15\">\n", "      <path d=\"M 52.160938 118.267875 \n", "L 386.960938 118.267875 \n", "\" clip-path=\"url(#p5055f4ef74)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_16\">\n", "      <g>\n", "       <use xlink:href=\"#m35582aff21\" x=\"52.160938\" y=\"118.267875\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_9\">\n", "      <!-- −0.5 -->\n", "      <g transform=\"translate(20.878125 122.067094)scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-2212\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"83.789062\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"147.412109\"/>\n", "       <use xlink:href=\"#DejaVuSans-35\" x=\"179.199219\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_4\">\n", "     <g id=\"line2d_17\">\n", "      <path d=\"M 52.160938 93.904016 \n", "L 386.960938 93.904016 \n", "\" clip-path=\"url(#p5055f4ef74)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_18\">\n", "      <g>\n", "       <use xlink:href=\"#m35582aff21\" x=\"52.160938\" y=\"93.904016\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_10\">\n", "      <!-- 0.0 -->\n", "      <g transform=\"translate(29.257812 97.703235)scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"95.410156\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_5\">\n", "     <g id=\"line2d_19\">\n", "      <path d=\"M 52.160938 69.540158 \n", "L 386.960938 69.540158 \n", "\" clip-path=\"url(#p5055f4ef74)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_20\">\n", "      <g>\n", "       <use xlink:href=\"#m35582aff21\" x=\"52.160938\" y=\"69.540158\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_11\">\n", "      <!-- 0.5 -->\n", "      <g transform=\"translate(29.257812 73.339377)scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-35\" x=\"95.410156\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_6\">\n", "     <g id=\"line2d_21\">\n", "      <path d=\"M 52.160938 45.176299 \n", "L 386.960938 45.176299 \n", "\" clip-path=\"url(#p5055f4ef74)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_22\">\n", "      <g>\n", "       <use xlink:href=\"#m35582aff21\" x=\"52.160938\" y=\"45.176299\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_12\">\n", "      <!-- 1.0 -->\n", "      <g transform=\"translate(29.257812 48.975518)scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-31\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"95.410156\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_7\">\n", "     <g id=\"line2d_23\">\n", "      <path d=\"M 52.160938 20.812441 \n", "L 386.960938 20.812441 \n", "\" clip-path=\"url(#p5055f4ef74)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_24\">\n", "      <g>\n", "       <use xlink:href=\"#m35582aff21\" x=\"52.160938\" y=\"20.812441\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_13\">\n", "      <!-- 1.5 -->\n", "      <g transform=\"translate(29.257812 24.611659)scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-31\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-35\" x=\"95.410156\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"text_14\">\n", "     <!-- x -->\n", "     <g transform=\"translate(14.798438 93.319375)rotate(-90)scale(0.1 -0.1)\">\n", "      <defs>\n", "       <path id=\"DejaVuSans-78\" d=\"M 3513 3500 \n", "L 2247 1797 \n", "L 3578 0 \n", "L 2900 0 \n", "L 1881 1375 \n", "L 863 0 \n", "L 184 0 \n", "L 1544 1831 \n", "L 300 3500 \n", "L 978 3500 \n", "L 1906 2253 \n", "L 2834 3500 \n", "L 3513 3500 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "      </defs>\n", "      <use xlink:href=\"#DejaVuSans-78\"/>\n", "     </g>\n", "    </g>\n", "   </g>\n", "   <g id=\"line2d_25\">\n", "    <path d=\"M 52.160938 74.122182 \n", "L 52.496073 82.976358 \n", "L 52.831208 86.558977 \n", "L 53.166343 83.001781 \n", "L 53.501478 82.64153 \n", "L 53.836613 85.200891 \n", "L 54.171748 78.027478 \n", "L 54.506883 82.563021 \n", "L 54.842019 78.881125 \n", "L 55.177154 63.187702 \n", "L 55.512289 104.799279 \n", "L 55.847424 101.529501 \n", "L 56.182559 77.360766 \n", "L 56.517694 78.594721 \n", "L 56.852829 98.553949 \n", "L 57.187965 92.06747 \n", "L 57.5231 78.070344 \n", "L 57.858235 102.228351 \n", "L 58.19337 83.277319 \n", "L 58.528505 79.367227 \n", "L 58.86364 72.546103 \n", "L 59.198775 101.833262 \n", "L 59.53391 93.326703 \n", "L 59.869046 89.723121 \n", "L 60.204181 77.823903 \n", "L 60.539316 81.70565 \n", "L 60.874451 64.461051 \n", "L 61.209586 84.679889 \n", "L 61.544721 78.420531 \n", "L 61.879856 67.901008 \n", "L 62.214992 67.108084 \n", "L 62.550127 60.179015 \n", "L 62.885262 101.993429 \n", "L 63.220397 74.308081 \n", "L 63.555532 67.066302 \n", "L 64.225802 89.228962 \n", "L 64.560938 80.305291 \n", "L 64.896073 48.521341 \n", "L 65.231208 72.533865 \n", "L 65.566343 77.365541 \n", "L 65.901478 74.073157 \n", "L 66.236613 83.163421 \n", "L 66.571748 64.118552 \n", "L 66.906883 82.451677 \n", "L 67.242019 82.780181 \n", "L 67.577154 63.032906 \n", "L 67.912289 56.477075 \n", "L 68.247424 74.51123 \n", "L 68.582559 77.660944 \n", "L 68.917694 79.406715 \n", "L 69.587965 69.246506 \n", "L 69.9231 67.676429 \n", "L 70.258235 69.621616 \n", "L 70.59337 61.960669 \n", "L 70.928505 70.970748 \n", "L 71.26364 63.431543 \n", "L 71.598775 70.363378 \n", "L 71.93391 67.026198 \n", "L 72.269046 66.295803 \n", "L 72.604181 58.59723 \n", "L 72.939316 74.558062 \n", "L 73.274451 57.760131 \n", "L 73.609586 52.936239 \n", "L 73.944721 52.300379 \n", "L 74.279856 49.711407 \n", "L 74.614992 69.070976 \n", "L 74.950127 76.704328 \n", "L 75.285262 58.766496 \n", "L 75.620397 70.914502 \n", "L 75.955532 68.629031 \n", "L 76.290667 76.122422 \n", "L 76.625802 54.722089 \n", "L 76.960938 57.870062 \n", "L 77.631208 54.247635 \n", "L 78.301478 79.934818 \n", "L 78.636613 56.857057 \n", "L 78.971748 52.632044 \n", "L 79.306883 52.454562 \n", "L 79.642019 55.07879 \n", "L 79.977154 61.14855 \n", "L 80.312289 44.971208 \n", "L 80.647424 46.844795 \n", "L 80.982559 68.34088 \n", "L 81.317694 52.221934 \n", "L 81.652829 58.852675 \n", "L 81.987965 61.54735 \n", "L 82.3231 66.295617 \n", "L 82.658235 57.934153 \n", "L 82.99337 54.135645 \n", "L 83.328505 58.786388 \n", "L 83.66364 57.171827 \n", "L 83.998775 61.658556 \n", "L 84.33391 59.793372 \n", "L 84.669046 42.114157 \n", "L 85.004181 46.375392 \n", "L 85.339316 61.905317 \n", "L 85.674451 43.29131 \n", "L 86.009586 50.473117 \n", "L 86.344721 61.904951 \n", "L 86.679856 57.67373 \n", "L 87.014992 60.784042 \n", "L 87.350127 31.671405 \n", "L 88.020397 55.47522 \n", "L 88.355532 47.265718 \n", "L 88.690667 45.848023 \n", "L 89.025802 49.093185 \n", "L 89.360938 50.768184 \n", "L 89.696073 57.478776 \n", "L 90.366343 40.765541 \n", "L 90.701478 43.428578 \n", "L 91.036613 39.507983 \n", "L 91.706883 58.448845 \n", "L 92.042019 41.859627 \n", "L 92.377154 36.339418 \n", "L 92.712289 62.010441 \n", "L 93.047424 51.105503 \n", "L 93.382559 33.425876 \n", "L 93.717694 30.236295 \n", "L 94.052829 54.818753 \n", "L 94.387965 49.477173 \n", "L 94.7231 38.673642 \n", "L 95.058235 36.546194 \n", "L 95.39337 55.301694 \n", "L 95.728505 33.595104 \n", "L 96.398775 54.597877 \n", "L 97.069046 34.687065 \n", "L 97.739316 47.32601 \n", "L 98.074451 54.569181 \n", "L 98.409586 41.007675 \n", "L 98.744721 41.859848 \n", "L 99.079856 53.729517 \n", "L 99.414992 50.770536 \n", "L 99.750127 21.531088 \n", "L 100.085262 32.556968 \n", "L 100.420397 38.042702 \n", "L 100.755532 38.846814 \n", "L 101.090667 45.123869 \n", "L 101.760938 50.01453 \n", "L 102.096073 48.865216 \n", "L 102.431208 46.663607 \n", "L 102.766343 50.066847 \n", "L 103.101478 47.34876 \n", "L 103.771748 64.230389 \n", "L 104.106883 49.479859 \n", "L 104.442019 43.334115 \n", "L 104.777154 61.813425 \n", "L 105.112289 49.557706 \n", "L 105.447424 21.106691 \n", "L 105.782559 29.668172 \n", "L 106.117694 34.84227 \n", "L 106.452829 42.628451 \n", "L 106.787965 40.812307 \n", "L 107.1231 55.911385 \n", "L 107.458235 59.605518 \n", "L 108.128505 36.199321 \n", "L 108.46364 45.036464 \n", "L 108.798775 14.76 \n", "L 109.13391 45.084085 \n", "L 109.469046 42.518932 \n", "L 109.804181 37.967885 \n", "L 110.139316 55.18309 \n", "L 110.474451 56.16315 \n", "L 110.809586 46.497356 \n", "L 111.144721 48.028317 \n", "L 111.479856 38.634136 \n", "L 111.814992 52.529751 \n", "L 112.150127 37.844465 \n", "L 112.485262 40.981907 \n", "L 112.820397 33.238008 \n", "L 113.155532 39.443087 \n", "L 113.490667 60.496187 \n", "L 113.825802 50.277265 \n", "L 114.160938 47.686719 \n", "L 114.496073 46.424703 \n", "L 114.831208 50.855461 \n", "L 115.166343 48.273233 \n", "L 115.501478 49.836441 \n", "L 115.836613 39.206593 \n", "L 116.171748 44.026413 \n", "L 116.506883 41.823642 \n", "L 116.842019 60.954437 \n", "L 117.177154 44.662076 \n", "L 117.512289 39.875476 \n", "L 117.847424 58.049682 \n", "L 118.182559 31.080546 \n", "L 118.517694 47.363207 \n", "L 118.852829 53.541446 \n", "L 119.187965 56.515578 \n", "L 119.858235 39.105241 \n", "L 120.19337 52.218408 \n", "L 120.528505 39.256148 \n", "L 120.86364 46.240404 \n", "L 121.198775 41.974781 \n", "L 121.53391 53.846738 \n", "L 121.869046 52.550587 \n", "L 122.204181 58.158541 \n", "L 122.539316 46.863136 \n", "L 122.874451 63.584532 \n", "L 123.209586 48.814532 \n", "L 123.544721 59.667405 \n", "L 123.879856 63.531298 \n", "L 124.214992 59.230598 \n", "L 124.550127 66.808144 \n", "L 124.885262 45.554124 \n", "L 125.555532 66.620828 \n", "L 125.890667 58.426301 \n", "L 126.225802 53.024701 \n", "L 126.560938 39.026892 \n", "L 126.896073 67.900021 \n", "L 127.566343 39.890492 \n", "L 127.901478 46.506345 \n", "L 128.236613 55.657857 \n", "L 128.571748 56.188715 \n", "L 128.906883 51.804287 \n", "L 129.577154 55.269836 \n", "L 129.912289 54.751032 \n", "L 130.247424 64.196387 \n", "L 130.582559 67.982047 \n", "L 130.917694 61.594325 \n", "L 131.252829 66.488742 \n", "L 131.587965 51.616373 \n", "L 131.9231 67.834303 \n", "L 132.258235 55.916535 \n", "L 132.59337 58.952037 \n", "L 132.928505 76.572419 \n", "L 133.26364 40.997091 \n", "L 133.598775 73.107654 \n", "L 133.93391 71.857237 \n", "L 134.269046 51.203858 \n", "L 134.939316 80.93351 \n", "L 135.274451 44.688581 \n", "L 135.609586 67.69281 \n", "L 135.944721 69.479604 \n", "L 136.279856 77.991395 \n", "L 136.614992 63.03516 \n", "L 136.950127 64.080139 \n", "L 137.285262 60.579956 \n", "L 137.620397 58.380705 \n", "L 137.955532 76.36534 \n", "L 138.290667 53.871803 \n", "L 138.625802 74.117331 \n", "L 138.960938 72.100446 \n", "L 139.296073 78.896079 \n", "L 139.631208 78.689855 \n", "L 139.966343 71.499177 \n", "L 140.301478 67.016817 \n", "L 140.636613 80.978291 \n", "L 140.971748 74.063908 \n", "L 141.306883 82.309214 \n", "L 141.642019 74.399541 \n", "L 141.977154 56.450035 \n", "L 142.312289 65.276455 \n", "L 142.647424 57.087469 \n", "L 142.982559 81.949268 \n", "L 143.317694 67.81918 \n", "L 143.652829 60.835429 \n", "L 143.987965 80.972241 \n", "L 144.3231 56.774488 \n", "L 144.658235 77.152168 \n", "L 144.99337 77.330111 \n", "L 145.328505 82.816353 \n", "L 145.66364 62.89569 \n", "L 145.998775 101.728936 \n", "L 146.33391 89.605577 \n", "L 146.669046 91.743255 \n", "L 147.339316 69.964368 \n", "L 147.674451 76.091184 \n", "L 148.009586 99.109418 \n", "L 148.344721 96.737219 \n", "L 148.679856 72.754827 \n", "L 149.014992 84.190594 \n", "L 149.350127 80.783762 \n", "L 149.685262 80.462836 \n", "L 150.020397 87.558727 \n", "L 150.355532 72.004855 \n", "L 150.690667 100.381437 \n", "L 151.025802 82.76547 \n", "L 151.696073 72.484614 \n", "L 152.031208 78.231729 \n", "L 152.366343 91.292196 \n", "L 152.701478 84.314366 \n", "L 153.036613 82.234719 \n", "L 153.371748 85.793342 \n", "L 153.706883 96.798187 \n", "L 154.042019 84.319305 \n", "L 154.377154 76.755225 \n", "L 154.712289 82.878741 \n", "L 155.047424 73.441116 \n", "L 155.382559 94.434888 \n", "L 155.717694 90.928361 \n", "L 156.052829 95.051205 \n", "L 156.387965 88.54265 \n", "L 156.7231 95.295112 \n", "L 157.058235 91.186746 \n", "L 157.39337 93.997711 \n", "L 157.728505 106.357914 \n", "L 158.06364 100.123307 \n", "L 158.398775 124.556246 \n", "L 158.73391 98.441045 \n", "L 159.069046 92.631761 \n", "L 159.404181 111.994417 \n", "L 159.739316 101.774558 \n", "L 160.074451 112.960521 \n", "L 160.409586 94.004235 \n", "L 160.744721 96.975301 \n", "L 161.079856 94.022321 \n", "L 161.414992 101.485591 \n", "L 161.750127 92.555663 \n", "L 162.085262 113.659214 \n", "L 162.420397 99.817401 \n", "L 162.755532 104.741996 \n", "L 163.090667 96.429009 \n", "L 163.425802 110.817288 \n", "L 163.760938 87.88694 \n", "L 164.096073 110.958457 \n", "L 164.431208 111.363763 \n", "L 164.766343 107.884371 \n", "L 165.436613 87.543632 \n", "L 165.771748 107.976449 \n", "L 166.106883 110.810034 \n", "L 166.442019 107.195185 \n", "L 166.777154 106.43953 \n", "L 167.112289 96.322765 \n", "L 167.447424 115.789651 \n", "L 167.782559 118.260733 \n", "L 168.117694 106.940622 \n", "L 168.452829 102.408069 \n", "L 168.787965 89.180145 \n", "L 169.1231 109.694511 \n", "L 169.458235 117.616055 \n", "L 169.79337 117.204022 \n", "L 170.128505 97.80659 \n", "L 170.46364 123.859263 \n", "L 171.13391 111.797512 \n", "L 171.469046 101.818608 \n", "L 171.804181 119.903882 \n", "L 172.139316 120.979973 \n", "L 172.474451 114.140519 \n", "L 172.809586 115.84008 \n", "L 173.144721 118.562759 \n", "L 173.479856 129.871402 \n", "L 173.814992 114.43148 \n", "L 174.150127 110.454135 \n", "L 174.485262 121.600602 \n", "L 174.820397 122.778484 \n", "L 175.155532 126.896471 \n", "L 175.490667 123.102304 \n", "L 175.825802 109.965809 \n", "L 176.160938 126.443085 \n", "L 176.496073 109.167743 \n", "L 176.831208 133.899596 \n", "L 177.166343 118.036238 \n", "L 177.501478 115.984871 \n", "L 178.171748 139.225368 \n", "L 178.506883 116.119274 \n", "L 178.842019 129.404166 \n", "L 179.177154 113.709983 \n", "L 179.847424 126.569633 \n", "L 180.182559 115.226744 \n", "L 180.517694 129.282289 \n", "L 180.852829 125.918496 \n", "L 181.187965 115.175925 \n", "L 181.5231 119.261365 \n", "L 181.858235 137.783729 \n", "L 182.19337 128.523514 \n", "L 182.528505 103.564498 \n", "L 182.86364 125.028937 \n", "L 183.198775 133.263684 \n", "L 183.53391 126.567004 \n", "L 183.869046 130.705481 \n", "L 184.204181 142.722699 \n", "L 184.539316 126.269219 \n", "L 184.874451 135.905534 \n", "L 185.209586 132.455076 \n", "L 185.544721 149.412237 \n", "L 185.879856 129.601723 \n", "L 186.214992 130.007316 \n", "L 186.550127 140.761127 \n", "L 186.885262 134.989001 \n", "L 187.220397 133.31447 \n", "L 187.555532 128.858162 \n", "L 187.890667 136.658606 \n", "L 188.225802 126.315495 \n", "L 188.560938 123.610684 \n", "L 188.896073 138.78895 \n", "L 189.231208 147.510449 \n", "L 189.566343 125.873327 \n", "L 190.236613 151.15605 \n", "L 190.571748 123.473774 \n", "L 190.906883 152.106502 \n", "L 191.242019 126.895471 \n", "L 191.577154 149.237282 \n", "L 191.912289 135.887236 \n", "L 192.247424 132.08952 \n", "L 192.917694 137.784493 \n", "L 193.252829 138.578811 \n", "L 193.587965 136.398227 \n", "L 193.9231 136.819054 \n", "L 194.258235 154.657947 \n", "L 194.59337 136.729532 \n", "L 194.928505 145.6692 \n", "L 195.26364 148.314444 \n", "L 195.598775 126.131876 \n", "L 195.93391 153.338403 \n", "L 196.269046 154.236873 \n", "L 196.604181 123.903587 \n", "L 196.939316 138.51111 \n", "L 197.274451 136.908472 \n", "L 197.609586 144.408098 \n", "L 197.944721 136.512248 \n", "L 198.279856 131.984122 \n", "L 198.614992 150.486545 \n", "L 198.950127 138.117349 \n", "L 199.285262 142.125073 \n", "L 199.620397 129.792115 \n", "L 199.955532 146.859974 \n", "L 200.290667 137.656566 \n", "L 200.625802 159.033068 \n", "L 200.960938 143.287227 \n", "L 201.296073 140.894174 \n", "L 201.631208 154.002865 \n", "L 201.966343 155.796819 \n", "L 202.301478 141.997105 \n", "L 202.636613 141.873959 \n", "L 202.971748 143.413691 \n", "L 203.306883 141.520915 \n", "L 203.642019 158.191827 \n", "L 203.977154 137.50351 \n", "L 204.312289 153.17851 \n", "L 204.647424 141.464009 \n", "L 204.982559 143.130047 \n", "L 205.317694 133.890811 \n", "L 205.652829 136.494426 \n", "L 205.987965 149.425923 \n", "L 206.658235 140.113423 \n", "L 206.99337 144.887655 \n", "L 207.328505 145.767734 \n", "L 207.66364 138.250434 \n", "L 207.998775 146.850459 \n", "L 208.33391 141.284174 \n", "L 208.669046 143.835502 \n", "L 209.004181 158.903154 \n", "L 209.339316 154.548242 \n", "L 209.674451 165.96 \n", "L 210.009586 134.823895 \n", "L 210.344721 138.67057 \n", "L 210.679856 151.367705 \n", "L 211.014992 133.467852 \n", "L 211.350127 164.494342 \n", "L 211.685262 144.529235 \n", "L 212.020397 150.848695 \n", "L 212.355532 150.914142 \n", "L 212.690667 155.378115 \n", "L 213.025802 128.632749 \n", "L 213.360938 144.495434 \n", "L 213.696073 148.088511 \n", "L 214.031208 146.137075 \n", "L 214.366343 124.879273 \n", "L 214.701478 134.323865 \n", "L 215.036613 155.504248 \n", "L 215.371748 146.751739 \n", "L 216.042019 145.38896 \n", "L 216.377154 149.815211 \n", "L 216.712289 143.822514 \n", "L 217.047424 142.069918 \n", "L 217.382559 135.954319 \n", "L 217.717694 144.225453 \n", "L 218.052829 146.464854 \n", "L 218.387965 156.775247 \n", "L 218.7231 151.135789 \n", "L 219.058235 120.227011 \n", "L 219.39337 141.620376 \n", "L 219.728505 143.748562 \n", "L 220.06364 126.056687 \n", "L 220.398775 138.445322 \n", "L 220.73391 157.138053 \n", "L 221.069046 143.868595 \n", "L 221.404181 153.936523 \n", "L 221.739316 127.584191 \n", "L 222.074451 143.54946 \n", "L 222.409586 130.311764 \n", "L 222.744721 147.35676 \n", "L 223.079856 136.632438 \n", "L 223.414992 134.192415 \n", "L 223.750127 134.369496 \n", "L 224.085262 133.819879 \n", "L 224.420397 138.477663 \n", "L 224.755532 156.344775 \n", "L 225.090667 144.877298 \n", "L 225.425802 153.590447 \n", "L 225.760938 124.716188 \n", "L 226.096073 134.79917 \n", "L 226.766343 141.504946 \n", "L 227.101478 140.680083 \n", "L 227.436613 137.494309 \n", "L 227.771748 139.026156 \n", "L 228.106883 134.947166 \n", "L 228.442019 127.365444 \n", "L 228.777154 132.093496 \n", "L 229.112289 127.946021 \n", "L 229.447424 142.168886 \n", "L 229.782559 150.509804 \n", "L 230.117694 119.885082 \n", "L 230.452829 126.566601 \n", "L 230.787965 142.409814 \n", "L 231.1231 137.976622 \n", "L 231.458235 153.415608 \n", "L 231.79337 131.730513 \n", "L 232.128505 133.381606 \n", "L 232.46364 133.005213 \n", "L 232.798775 122.511189 \n", "L 233.13391 116.659335 \n", "L 233.804181 145.177956 \n", "L 234.139316 125.205251 \n", "L 234.474451 139.273143 \n", "L 234.809586 128.719875 \n", "L 235.144721 132.583828 \n", "L 235.814992 117.535868 \n", "L 236.150127 126.708934 \n", "L 236.485262 126.663883 \n", "L 236.820397 125.540395 \n", "L 237.155532 129.728451 \n", "L 237.490667 129.103023 \n", "L 237.825802 136.631439 \n", "L 238.160938 112.479726 \n", "L 238.496073 131.592824 \n", "L 238.831208 105.319914 \n", "L 239.166343 141.392543 \n", "L 239.501478 131.446373 \n", "L 239.836613 126.596423 \n", "L 240.171748 132.710248 \n", "L 240.506883 155.6859 \n", "L 240.842019 110.266371 \n", "L 241.177154 121.812263 \n", "L 241.512289 129.327074 \n", "L 241.847424 109.058873 \n", "L 242.517694 127.93046 \n", "L 242.852829 127.655335 \n", "L 243.187965 101.100084 \n", "L 243.5231 114.685435 \n", "L 243.858235 116.485125 \n", "L 244.19337 134.518195 \n", "L 244.528505 115.448721 \n", "L 244.86364 110.555254 \n", "L 245.198775 119.553533 \n", "L 245.53391 116.123524 \n", "L 245.869046 102.514994 \n", "L 246.204181 120.017011 \n", "L 246.539316 119.943759 \n", "L 246.874451 133.978672 \n", "L 247.209586 100.938322 \n", "L 247.544721 116.163515 \n", "L 247.879856 110.83836 \n", "L 248.214992 119.842349 \n", "L 248.550127 93.594694 \n", "L 248.885262 113.91589 \n", "L 249.220397 102.738063 \n", "L 249.555532 113.634371 \n", "L 249.890667 118.305075 \n", "L 250.225802 108.55523 \n", "L 250.560938 103.510515 \n", "L 250.896073 107.064743 \n", "L 251.231208 100.600565 \n", "L 251.566343 101.591273 \n", "L 251.901478 101.255869 \n", "L 252.571748 99.00492 \n", "L 252.906883 98.935157 \n", "L 253.242019 100.339615 \n", "L 253.577154 116.971299 \n", "L 253.912289 106.870787 \n", "L 254.247424 108.42852 \n", "L 254.582559 108.473647 \n", "L 254.917694 104.296306 \n", "L 255.252829 115.70195 \n", "L 255.587965 90.170256 \n", "L 255.9231 100.212505 \n", "L 256.258235 99.069072 \n", "L 256.59337 104.550048 \n", "L 256.928505 88.10503 \n", "L 257.598775 122.519519 \n", "L 257.93391 111.091175 \n", "L 258.269046 86.08018 \n", "L 258.604181 99.776671 \n", "L 258.939316 99.86324 \n", "L 259.274451 104.279022 \n", "L 259.609586 98.52269 \n", "L 259.944721 105.804131 \n", "L 260.279856 93.738329 \n", "L 260.614992 108.0413 \n", "L 260.950127 95.45474 \n", "L 261.285262 88.682498 \n", "L 261.620397 100.845327 \n", "L 261.955532 88.463805 \n", "L 262.290667 98.872803 \n", "L 262.625802 94.605555 \n", "L 262.960938 97.809408 \n", "L 263.631208 82.496181 \n", "L 264.301478 97.039864 \n", "L 264.636613 93.121583 \n", "L 264.971748 105.186654 \n", "L 265.306883 95.005097 \n", "L 265.642019 99.631876 \n", "L 265.977154 88.209012 \n", "L 266.312289 85.970676 \n", "L 266.647424 85.27872 \n", "L 266.982559 81.293832 \n", "L 267.317694 82.303372 \n", "L 267.652829 98.897848 \n", "L 267.987965 92.928675 \n", "L 268.3231 70.870747 \n", "L 268.658235 77.844387 \n", "L 268.99337 78.106689 \n", "L 269.328505 66.709955 \n", "L 269.66364 95.768692 \n", "L 269.998775 96.25946 \n", "L 270.33391 80.015546 \n", "L 270.669046 86.511928 \n", "L 271.004181 79.372592 \n", "L 271.339316 86.159739 \n", "L 271.674451 76.039655 \n", "L 272.009586 77.783681 \n", "L 272.344721 86.995124 \n", "L 272.679856 75.471741 \n", "L 273.014992 85.446228 \n", "L 273.350127 76.0489 \n", "L 273.685262 89.544298 \n", "L 274.020397 92.565636 \n", "L 274.690667 70.2588 \n", "L 275.025802 84.032657 \n", "L 275.360937 81.538159 \n", "L 275.696073 82.912849 \n", "L 276.031208 82.487098 \n", "L 276.366343 63.105432 \n", "L 276.701478 66.895204 \n", "L 277.036613 80.786107 \n", "L 277.371748 68.820808 \n", "L 277.706883 88.593721 \n", "L 278.042019 61.477804 \n", "L 278.377154 78.660639 \n", "L 278.712289 67.2971 \n", "L 279.047424 69.666813 \n", "L 279.382559 73.885986 \n", "L 279.717694 74.147886 \n", "L 280.052829 65.215744 \n", "L 280.387965 69.66156 \n", "L 280.7231 70.131763 \n", "L 281.058235 56.316003 \n", "L 281.728505 77.26494 \n", "L 282.06364 49.474861 \n", "L 282.398775 65.693294 \n", "L 282.73391 52.035117 \n", "L 283.069046 73.64204 \n", "L 283.404181 67.602622 \n", "L 283.739316 71.023396 \n", "L 284.074451 65.047365 \n", "L 284.409586 56.754555 \n", "L 284.744721 73.643153 \n", "L 285.079856 62.71314 \n", "L 285.414992 57.894976 \n", "L 285.750127 64.300362 \n", "L 286.085262 62.259906 \n", "L 286.420397 47.2557 \n", "L 286.755532 68.142477 \n", "L 287.090667 63.441735 \n", "L 287.425802 82.488975 \n", "L 287.760938 53.720298 \n", "L 288.096073 70.589806 \n", "L 288.431208 64.518903 \n", "L 288.766343 55.050434 \n", "L 289.101478 62.958097 \n", "L 289.436613 64.387343 \n", "L 289.771748 72.193278 \n", "L 290.106883 65.185483 \n", "L 290.442019 54.856697 \n", "L 290.777154 61.263567 \n", "L 291.112289 50.25981 \n", "L 291.447424 50.362596 \n", "L 291.782559 60.025544 \n", "L 292.452829 44.353727 \n", "L 292.787965 62.173224 \n", "L 293.1231 59.021226 \n", "L 293.458235 60.520561 \n", "L 293.79337 41.121591 \n", "L 294.128505 36.692273 \n", "L 294.46364 57.477869 \n", "L 294.798775 44.359036 \n", "L 295.13391 53.07628 \n", "L 295.469046 51.78032 \n", "L 295.804181 25.42078 \n", "L 296.139316 41.580811 \n", "L 296.474451 42.365492 \n", "L 296.809586 58.023266 \n", "L 297.144721 50.473318 \n", "L 297.479856 48.329061 \n", "L 297.814992 32.164212 \n", "L 298.150127 47.864549 \n", "L 298.485262 57.128255 \n", "L 298.820397 48.603396 \n", "L 299.155532 44.726989 \n", "L 299.490667 54.743146 \n", "L 299.825802 44.179132 \n", "L 300.160938 37.858162 \n", "L 300.496073 37.644097 \n", "L 300.831208 62.819607 \n", "L 301.166343 64.192574 \n", "L 301.501478 24.978951 \n", "L 301.836613 39.514686 \n", "L 302.171748 62.359091 \n", "L 302.506883 45.460198 \n", "L 302.842019 45.461459 \n", "L 303.177154 51.842076 \n", "L 303.512289 66.945154 \n", "L 303.847424 37.952968 \n", "L 304.182559 65.425148 \n", "L 304.517694 38.848347 \n", "L 305.187965 62.057031 \n", "L 305.5231 35.300851 \n", "L 305.858235 45.266341 \n", "L 306.528505 53.449324 \n", "L 306.86364 47.645534 \n", "L 307.198775 44.344997 \n", "L 307.53391 60.311342 \n", "L 307.869046 67.324056 \n", "L 308.204181 35.333636 \n", "L 308.874451 45.962166 \n", "L 309.209586 34.147614 \n", "L 309.544721 43.466317 \n", "L 309.879856 47.983766 \n", "L 310.214992 42.97979 \n", "L 310.550127 64.61539 \n", "L 310.885262 32.231007 \n", "L 311.220397 42.748257 \n", "L 311.555532 60.854392 \n", "L 311.890667 66.013623 \n", "L 312.225802 38.634717 \n", "L 312.560938 44.384555 \n", "L 312.896073 56.069565 \n", "L 313.231208 41.944802 \n", "L 313.566343 56.082512 \n", "L 313.901478 38.861586 \n", "L 314.236613 51.85799 \n", "L 314.571748 54.809439 \n", "L 314.906883 55.578683 \n", "L 315.242019 41.366379 \n", "L 315.577154 54.696263 \n", "L 315.912289 58.938674 \n", "L 316.247424 36.956196 \n", "L 316.582559 50.308943 \n", "L 316.917694 45.634163 \n", "L 317.252829 34.133231 \n", "L 317.587965 31.42528 \n", "L 317.9231 56.351939 \n", "L 318.258235 66.976591 \n", "L 318.59337 42.897183 \n", "L 318.928505 34.801016 \n", "L 319.26364 44.260757 \n", "L 319.598775 38.327159 \n", "L 319.93391 47.899152 \n", "L 320.269046 40.202831 \n", "L 320.604181 40.418965 \n", "L 320.939316 24.67299 \n", "L 321.274451 36.422693 \n", "L 321.609586 36.979901 \n", "L 321.944721 61.885535 \n", "L 322.279856 26.057064 \n", "L 322.614992 33.010733 \n", "L 322.950127 50.947989 \n", "L 323.285262 42.209021 \n", "L 323.620397 45.162556 \n", "L 323.955532 31.027308 \n", "L 324.290667 39.548592 \n", "L 324.625802 54.399791 \n", "L 324.960938 54.800035 \n", "L 325.296073 55.681531 \n", "L 325.631208 37.760534 \n", "L 325.966343 46.015229 \n", "L 326.301478 37.512696 \n", "L 326.636613 37.14587 \n", "L 326.971748 50.534958 \n", "L 327.306883 54.403227 \n", "L 327.642019 51.589443 \n", "L 327.977154 45.876465 \n", "L 328.312289 43.429461 \n", "L 328.647424 42.608991 \n", "L 328.982559 46.236774 \n", "L 329.317694 62.89899 \n", "L 329.652829 42.091619 \n", "L 329.987965 45.736363 \n", "L 330.3231 55.907188 \n", "L 330.658235 54.976242 \n", "L 330.99337 62.94528 \n", "L 331.328505 42.551763 \n", "L 331.66364 46.755005 \n", "L 331.998775 45.275377 \n", "L 332.33391 61.839169 \n", "L 332.669046 43.877586 \n", "L 333.004181 43.844697 \n", "L 333.339316 44.944691 \n", "L 333.674451 63.125242 \n", "L 334.009586 30.513996 \n", "L 334.344721 46.929333 \n", "L 334.679856 46.210619 \n", "L 335.014992 59.244902 \n", "L 335.350127 38.829016 \n", "L 335.685262 54.121919 \n", "L 336.020397 63.833724 \n", "L 336.355532 54.401601 \n", "L 336.690667 65.528539 \n", "L 337.025802 58.807233 \n", "L 337.360937 43.950544 \n", "L 337.696073 47.947101 \n", "L 338.031208 48.925485 \n", "L 338.366343 46.595687 \n", "L 338.701478 63.493921 \n", "L 339.036613 63.088734 \n", "L 339.371748 64.237493 \n", "L 339.706883 57.540756 \n", "L 340.042019 58.078981 \n", "L 340.377154 57.261001 \n", "L 340.712289 47.108415 \n", "L 341.047424 55.915524 \n", "L 341.382559 53.928131 \n", "L 341.717694 45.915146 \n", "L 342.052829 50.110936 \n", "L 342.387965 61.140545 \n", "L 342.7231 58.862986 \n", "L 343.058235 36.426892 \n", "L 343.39337 87.21454 \n", "L 343.728505 71.314331 \n", "L 344.06364 65.023435 \n", "L 344.398775 71.874138 \n", "L 344.73391 66.120606 \n", "L 345.069046 56.458086 \n", "L 345.404181 67.016596 \n", "L 345.739316 63.353299 \n", "L 346.074451 53.051668 \n", "L 346.744721 84.061934 \n", "L 347.079856 83.488185 \n", "L 347.414992 71.964509 \n", "L 347.750127 75.980809 \n", "L 348.085262 38.001669 \n", "L 348.420397 72.409037 \n", "L 348.755532 69.094975 \n", "L 349.090667 90.148197 \n", "L 349.425802 76.75297 \n", "L 349.760938 72.457868 \n", "L 350.096073 63.296881 \n", "L 350.431208 72.95957 \n", "L 350.766343 76.989967 \n", "L 351.101478 78.851165 \n", "L 351.436613 73.271072 \n", "L 351.771748 57.0232 \n", "L 352.106883 64.932847 \n", "L 352.442019 64.016492 \n", "L 352.777154 65.72255 \n", "L 353.112289 74.67013 \n", "L 353.447424 86.837408 \n", "L 353.782559 63.951593 \n", "L 354.117694 65.666062 \n", "L 354.452829 79.107465 \n", "L 354.787965 101.451466 \n", "L 355.1231 73.598768 \n", "L 355.458235 80.89558 \n", "L 355.79337 77.590992 \n", "L 356.128505 71.000879 \n", "L 356.46364 89.26004 \n", "L 356.798775 92.558124 \n", "L 357.13391 87.124488 \n", "L 357.469046 79.080655 \n", "L 357.804181 95.487891 \n", "L 358.139316 58.545759 \n", "L 358.474451 77.519692 \n", "L 358.809586 83.134692 \n", "L 359.144721 94.282429 \n", "L 359.479856 75.854837 \n", "L 359.814992 65.074323 \n", "L 360.150127 65.32237 \n", "L 360.485262 92.363018 \n", "L 360.820397 71.87024 \n", "L 361.155532 82.868709 \n", "L 361.490667 87.69368 \n", "L 361.825802 78.198965 \n", "L 362.160938 87.077575 \n", "L 362.496073 82.462158 \n", "L 362.831208 84.073484 \n", "L 363.166343 93.272929 \n", "L 363.836613 75.409046 \n", "L 364.171748 88.763673 \n", "L 364.506883 93.839258 \n", "L 364.842019 119.424241 \n", "L 365.177154 92.525979 \n", "L 365.512289 84.975138 \n", "L 365.847424 93.14001 \n", "L 366.182559 104.508942 \n", "L 366.517694 92.91488 \n", "L 366.852829 93.212533 \n", "L 367.187965 86.356417 \n", "L 367.5231 108.714747 \n", "L 367.858235 90.902056 \n", "L 368.19337 107.857921 \n", "L 368.528505 98.097803 \n", "L 369.198775 84.472201 \n", "L 369.53391 89.489557 \n", "L 369.869046 106.341411 \n", "L 370.204181 107.350688 \n", "L 370.539316 100.367594 \n", "L 370.874451 103.063221 \n", "L 371.209586 104.363041 \n", "L 371.544721 91.682501 \n", "L 371.879856 114.137115 \n", "L 372.214992 106.599804 \n", "L 372.550127 108.584077 \n", "L 372.885262 92.219318 \n", "L 373.220397 101.018691 \n", "L 373.555532 116.296612 \n", "L 373.890667 96.993194 \n", "L 374.225802 118.953182 \n", "L 374.560938 122.562339 \n", "L 374.896073 100.513015 \n", "L 375.231208 121.144048 \n", "L 375.566343 106.768413 \n", "L 376.236613 105.521416 \n", "L 376.571748 128.923038 \n", "L 376.906883 115.418919 \n", "L 377.242019 106.55171 \n", "L 377.577154 113.095772 \n", "L 377.912289 112.674382 \n", "L 378.247424 108.1739 \n", "L 378.582559 91.720892 \n", "L 378.917694 115.766163 \n", "L 379.252829 129.440369 \n", "L 379.587965 98.58422 \n", "L 379.9231 100.002236 \n", "L 380.258235 100.494226 \n", "L 380.59337 102.929046 \n", "L 380.928505 96.913464 \n", "L 381.598775 128.475368 \n", "L 381.93391 113.686341 \n", "L 382.269046 112.441606 \n", "L 382.604181 106.273918 \n", "L 382.939316 118.336555 \n", "L 383.274451 100.382387 \n", "L 383.609586 128.56343 \n", "L 383.944721 126.966493 \n", "L 384.279856 117.755986 \n", "L 384.614992 122.722214 \n", "L 384.950127 118.78685 \n", "L 385.285262 103.746844 \n", "L 385.620397 103.717631 \n", "L 385.955532 111.164847 \n", "L 386.290667 144.333194 \n", "L 386.625802 115.060377 \n", "L 386.960938 113.486222 \n", "L 386.960938 113.486222 \n", "\" clip-path=\"url(#p5055f4ef74)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_26\">\n", "    <path d=\"M 53.501478 78.453472 \n", "L 53.836613 81.778387 \n", "L 54.171748 82.724174 \n", "L 54.506883 78.616687 \n", "L 54.842019 80.65561 \n", "L 55.177154 78.336831 \n", "L 55.512289 73.021707 \n", "L 55.847424 87.622432 \n", "L 56.517694 83.469846 \n", "L 56.852829 96.330971 \n", "L 57.187965 91.219496 \n", "L 57.5231 83.867246 \n", "L 57.858235 83.880657 \n", "L 58.19337 95.068723 \n", "L 58.528505 87.196899 \n", "L 58.86364 83.792928 \n", "L 59.198775 89.718911 \n", "L 59.869046 84.295774 \n", "L 60.539316 91.163729 \n", "L 61.209586 79.437162 \n", "L 61.544721 79.078591 \n", "L 62.214992 68.013005 \n", "L 62.550127 78.063536 \n", "L 62.885262 70.128862 \n", "L 63.220397 80.484221 \n", "L 63.555532 69.86478 \n", "L 63.890667 69.94935 \n", "L 64.225802 88.588234 \n", "L 64.560938 77.372989 \n", "L 64.896073 73.440114 \n", "L 65.231208 76.355613 \n", "L 65.566343 86.516495 \n", "L 65.901478 69.872941 \n", "L 66.236613 62.081146 \n", "L 66.571748 76.135968 \n", "L 66.906883 69.769698 \n", "L 67.242019 77.059099 \n", "L 67.577154 76.43303 \n", "L 67.912289 66.667174 \n", "L 68.247424 77.996395 \n", "L 68.582559 75.652563 \n", "L 68.917694 64.350454 \n", "L 69.587965 71.979858 \n", "L 70.258235 72.849209 \n", "L 70.928505 64.4818 \n", "L 71.26364 67.623049 \n", "L 71.598775 63.412705 \n", "L 72.269046 65.38846 \n", "L 72.604181 63.606342 \n", "L 72.939316 64.749536 \n", "L 73.274451 67.952233 \n", "L 73.609586 60.317497 \n", "L 73.944721 60.776206 \n", "L 74.279856 65.91684 \n", "L 74.614992 55.524426 \n", "L 74.950127 57.814974 \n", "L 75.285262 58.308295 \n", "L 75.620397 55.781326 \n", "L 75.955532 70.589912 \n", "L 76.290667 67.496138 \n", "L 76.960938 64.844504 \n", "L 77.296073 67.989381 \n", "L 77.631208 65.795616 \n", "L 77.966343 55.463719 \n", "L 78.301478 60.157337 \n", "L 78.636613 62.479113 \n", "L 78.971748 56.94969 \n", "L 79.306883 67.097713 \n", "L 79.642019 69.934337 \n", "L 79.977154 56.190132 \n", "L 80.312289 54.882305 \n", "L 80.647424 51.847075 \n", "L 81.317694 60.667706 \n", "L 81.652829 46.652076 \n", "L 82.3231 62.280269 \n", "L 82.658235 56.157004 \n", "L 82.99337 57.397499 \n", "L 83.66364 62.821866 \n", "L 83.998775 56.399835 \n", "L 84.33391 56.874821 \n", "L 84.669046 57.619787 \n", "L 85.004181 55.562474 \n", "L 85.339316 60.271874 \n", "L 85.674451 57.911345 \n", "L 86.009586 44.625335 \n", "L 86.344721 53.645182 \n", "L 86.679856 58.511013 \n", "L 87.014992 47.55546 \n", "L 87.350127 55.436597 \n", "L 87.685262 56.378203 \n", "L 88.020397 60.237417 \n", "L 88.355532 54.61763 \n", "L 88.690667 39.47281 \n", "L 89.025802 49.962536 \n", "L 89.360938 54.072503 \n", "L 89.696073 49.304803 \n", "L 90.031208 50.568268 \n", "L 90.366343 49.640923 \n", "L 90.701478 51.406867 \n", "L 91.036613 55.365016 \n", "L 91.371748 47.922774 \n", "L 91.706883 47.154607 \n", "L 92.042019 47.309022 \n", "L 92.377154 44.257121 \n", "L 93.047424 59.051167 \n", "L 93.382559 42.505804 \n", "L 93.717694 44.887985 \n", "L 94.052829 56.321849 \n", "L 94.387965 52.780397 \n", "L 94.7231 37.159556 \n", "L 95.058235 40.434097 \n", "L 95.39337 52.716413 \n", "L 95.728505 52.119525 \n", "L 96.06364 39.978518 \n", "L 96.398775 47.621332 \n", "L 96.73391 52.154668 \n", "L 97.069046 40.129634 \n", "L 97.739316 53.48761 \n", "L 98.074451 45.764504 \n", "L 98.409586 43.197608 \n", "L 98.744721 44.694593 \n", "L 99.079856 50.854587 \n", "L 99.414992 53.935448 \n", "L 99.750127 44.290801 \n", "L 100.085262 44.961263 \n", "L 100.420397 54.735522 \n", "L 100.755532 45.528928 \n", "L 101.090667 32.285006 \n", "L 101.425802 40.526474 \n", "L 102.766343 49.568476 \n", "L 103.101478 51.718207 \n", "L 103.436613 49.353724 \n", "L 104.106883 52.967682 \n", "L 104.442019 49.762873 \n", "L 105.112289 62.469999 \n", "L 105.447424 47.824628 \n", "L 105.782559 47.58426 \n", "L 106.117694 58.015976 \n", "L 106.787965 32.847367 \n", "L 107.1231 36.598211 \n", "L 107.458235 44.886731 \n", "L 107.79337 46.4941 \n", "L 108.128505 46.575342 \n", "L 108.46364 53.997921 \n", "L 108.798775 57.575419 \n", "L 109.13391 44.028289 \n", "L 109.469046 48.514237 \n", "L 110.139316 30.729816 \n", "L 110.474451 50.479847 \n", "L 110.809586 45.113953 \n", "L 111.144721 44.805983 \n", "L 111.479856 55.655879 \n", "L 111.814992 51.442957 \n", "L 112.150127 51.772626 \n", "L 112.485262 45.237983 \n", "L 113.155532 47.611492 \n", "L 113.490667 43.674871 \n", "L 113.825802 47.128769 \n", "L 114.160938 38.87781 \n", "L 114.831208 56.406617 \n", "L 115.166343 51.800259 \n", "L 115.501478 48.757655 \n", "L 115.836613 49.992855 \n", "L 116.171748 48.969897 \n", "L 116.506883 51.05601 \n", "L 116.842019 47.951138 \n", "L 117.177154 48.423812 \n", "L 117.512289 44.525894 \n", "L 117.847424 48.401912 \n", "L 118.182559 59.433253 \n", "L 118.517694 43.60883 \n", "L 118.852829 50.957478 \n", "L 119.187965 52.31486 \n", "L 119.5231 41.84417 \n", "L 119.858235 49.007764 \n", "L 120.528505 56.397317 \n", "L 120.86364 44.678613 \n", "L 121.198775 47.409892 \n", "L 121.53391 48.808447 \n", "L 121.869046 47.314872 \n", "L 122.204181 47.389782 \n", "L 122.874451 51.979161 \n", "L 123.209586 58.271005 \n", "L 123.544721 52.897863 \n", "L 123.879856 55.370975 \n", "L 124.214992 59.338491 \n", "L 124.550127 52.612741 \n", "L 124.885262 61.630006 \n", "L 125.220397 58.365398 \n", "L 125.555532 62.696545 \n", "L 125.890667 61.247595 \n", "L 126.225802 49.958333 \n", "L 126.560938 58.479355 \n", "L 126.896073 59.197584 \n", "L 127.231208 62.521921 \n", "L 127.566343 48.791752 \n", "L 127.901478 47.985416 \n", "L 128.236613 62.332783 \n", "L 128.906883 45.695763 \n", "L 129.242019 50.146346 \n", "L 129.577154 56.147646 \n", "L 129.912289 55.521446 \n", "L 130.247424 53.17724 \n", "L 130.582559 56.695207 \n", "L 130.917694 56.648129 \n", "L 131.252829 56.441821 \n", "L 131.587965 64.491498 \n", "L 131.9231 61.424772 \n", "L 132.258235 64.967139 \n", "L 132.59337 58.544733 \n", "L 132.928505 57.32854 \n", "L 133.26364 66.315123 \n", "L 133.598775 54.470583 \n", "L 133.93391 69.171038 \n", "L 134.269046 64.757073 \n", "L 134.604181 51.057006 \n", "L 134.939316 72.331906 \n", "L 135.274451 68.013763 \n", "L 135.609586 54.675134 \n", "L 135.944721 73.083916 \n", "L 136.279856 68.268444 \n", "L 136.614992 61.365049 \n", "L 136.950127 63.940896 \n", "L 137.285262 68.743807 \n", "L 137.620397 68.845321 \n", "L 137.955532 61.306479 \n", "L 138.290667 65.546578 \n", "L 138.625802 57.291422 \n", "L 138.960938 66.666604 \n", "L 139.296073 66.910272 \n", "L 139.631208 66.732737 \n", "L 139.966343 72.983952 \n", "L 140.301478 70.33641 \n", "L 140.636613 73.395822 \n", "L 140.971748 75.071425 \n", "L 141.306883 68.355181 \n", "L 141.642019 74.833491 \n", "L 142.312289 71.021816 \n", "L 142.647424 77.643958 \n", "L 142.982559 64.171026 \n", "L 143.317694 66.492959 \n", "L 143.652829 61.425499 \n", "L 143.987965 61.683598 \n", "L 144.3231 77.024545 \n", "L 144.658235 61.635466 \n", "L 144.99337 69.168476 \n", "L 145.328505 71.335851 \n", "L 145.66364 70.609735 \n", "L 145.998775 70.727017 \n", "L 146.33391 86.206246 \n", "L 146.669046 81.531733 \n", "L 147.004181 85.421205 \n", "L 147.339316 91.018087 \n", "L 147.674451 84.025518 \n", "L 148.009586 82.952398 \n", "L 148.344721 83.82011 \n", "L 149.014992 82.858062 \n", "L 149.350127 91.821511 \n", "L 150.020397 76.82831 \n", "L 150.355532 82.079685 \n", "L 150.690667 75.599009 \n", "L 151.025802 87.720741 \n", "L 151.360938 82.450251 \n", "L 151.696073 80.233705 \n", "L 152.031208 88.563805 \n", "L 152.366343 76.489282 \n", "L 152.701478 80.859556 \n", "L 153.036613 77.401751 \n", "L 153.706883 85.317978 \n", "L 154.042019 86.836991 \n", "L 154.377154 83.636666 \n", "L 154.712289 84.655928 \n", "L 155.047424 86.574014 \n", "L 155.382559 74.97919 \n", "L 155.717694 83.881064 \n", "L 156.052829 83.106647 \n", "L 156.387965 87.924933 \n", "L 156.7231 90.938671 \n", "L 157.058235 91.64751 \n", "L 157.39337 91.249252 \n", "L 157.728505 90.525716 \n", "L 158.06364 96.057571 \n", "L 158.398775 94.571065 \n", "L 158.73391 107.027122 \n", "L 159.069046 108.316736 \n", "L 159.404181 105.659171 \n", "L 159.739316 113.308856 \n", "L 160.074451 99.04198 \n", "L 160.744721 107.414785 \n", "L 161.079856 103.064031 \n", "L 161.414992 102.205816 \n", "L 161.750127 95.874415 \n", "L 162.085262 94.799253 \n", "L 162.420397 100.635423 \n", "L 162.755532 100.69968 \n", "L 163.090667 101.609745 \n", "L 163.425802 105.697964 \n", "L 164.096073 101.378136 \n", "L 164.431208 103.016008 \n", "L 164.766343 105.439249 \n", "L 165.101478 101.020926 \n", "L 165.436613 108.599732 \n", "L 165.771748 103.424665 \n", "L 166.106883 103.530027 \n", "L 166.442019 98.67127 \n", "L 166.777154 99.965676 \n", "L 167.112289 109.212533 \n", "L 167.447424 106.759127 \n", "L 167.782559 108.923476 \n", "L 168.452829 107.437818 \n", "L 168.787965 113.991959 \n", "L 169.1231 107.832218 \n", "L 169.79337 103.411634 \n", "L 170.128505 105.551075 \n", "L 170.798775 119.31331 \n", "L 171.469046 110.802076 \n", "L 171.804181 117.908752 \n", "L 172.139316 117.032332 \n", "L 172.474451 113.515492 \n", "L 172.809586 112.594156 \n", "L 173.144721 121.048287 \n", "L 173.479856 120.422889 \n", "L 173.814992 121.009177 \n", "L 174.150127 121.140752 \n", "L 174.485262 121.653181 \n", "L 174.820397 123.289644 \n", "L 175.155532 117.968908 \n", "L 175.490667 120.628881 \n", "L 175.825802 125.622904 \n", "L 176.160938 124.287679 \n", "L 176.496073 124.532497 \n", "L 176.831208 120.268793 \n", "L 177.501478 124.56956 \n", "L 177.836613 120.282229 \n", "L 178.171748 126.600585 \n", "L 178.506883 126.327487 \n", "L 178.842019 126.986681 \n", "L 179.177154 128.215332 \n", "L 179.512289 128.762456 \n", "L 179.847424 122.639256 \n", "L 180.182559 126.368486 \n", "L 180.517694 119.476781 \n", "L 180.852829 125.957072 \n", "L 181.187965 126.708884 \n", "L 181.5231 122.969704 \n", "L 181.858235 125.055111 \n", "L 182.19337 128.209291 \n", "L 182.528505 124.720797 \n", "L 182.86364 127.064144 \n", "L 183.198775 124.201401 \n", "L 183.53391 125.684361 \n", "L 183.869046 119.7508 \n", "L 184.204181 130.336051 \n", "L 184.539316 133.842031 \n", "L 184.874451 134.524542 \n", "L 185.209586 132.636825 \n", "L 185.879856 135.658029 \n", "L 186.214992 139.342749 \n", "L 186.550127 133.193523 \n", "L 186.885262 135.20174 \n", "L 187.220397 134.963448 \n", "L 187.890667 133.483466 \n", "L 188.225802 133.030839 \n", "L 188.560938 132.934973 \n", "L 188.896073 129.659067 \n", "L 189.231208 131.603437 \n", "L 189.566343 135.80704 \n", "L 189.901478 137.056677 \n", "L 190.236613 134.231587 \n", "L 190.571748 138.746715 \n", "L 190.906883 138.820931 \n", "L 191.242019 136.419717 \n", "L 191.577154 140.871267 \n", "L 191.912289 135.36235 \n", "L 192.247424 140.019227 \n", "L 192.582559 134.626091 \n", "L 192.917694 135.314326 \n", "L 193.252829 134.926191 \n", "L 193.587965 135.59372 \n", "L 193.9231 135.947464 \n", "L 194.258235 135.766291 \n", "L 194.928505 142.250546 \n", "L 195.26364 138.825982 \n", "L 195.598775 141.810988 \n", "L 195.93391 140.122345 \n", "L 196.269046 137.740288 \n", "L 196.604181 143.290765 \n", "L 196.939316 142.736867 \n", "L 197.274451 135.820019 \n", "L 197.609586 136.668284 \n", "L 197.944721 135.886942 \n", "L 198.279856 137.719609 \n", "L 198.614992 135.343547 \n", "L 199.285262 139.901506 \n", "L 199.620397 137.671649 \n", "L 199.955532 137.647908 \n", "L 200.290667 136.253762 \n", "L 200.960938 140.110998 \n", "L 201.296073 145.181302 \n", "L 201.631208 140.123408 \n", "L 202.301478 144.569792 \n", "L 202.636613 144.775633 \n", "L 202.971748 141.326305 \n", "L 203.306883 140.517306 \n", "L 203.642019 139.165938 \n", "L 204.312289 144.85397 \n", "L 204.647424 141.197208 \n", "L 204.982559 143.332036 \n", "L 205.317694 139.520525 \n", "L 205.652829 138.781312 \n", "L 205.987965 135.838021 \n", "L 206.658235 140.091297 \n", "L 206.99337 139.48668 \n", "L 207.66364 140.489066 \n", "L 207.998775 139.255394 \n", "L 208.33391 139.450418 \n", "L 208.669046 140.065933 \n", "L 209.004181 139.109892 \n", "L 209.674451 145.670873 \n", "L 210.344721 150.802695 \n", "L 210.679856 139.850758 \n", "L 211.014992 141.528939 \n", "L 211.350127 140.772347 \n", "L 211.685262 141.330607 \n", "L 212.020397 147.642309 \n", "L 212.355532 142.341982 \n", "L 212.690667 145.321398 \n", "L 213.025802 144.713972 \n", "L 213.360938 144.94899 \n", "L 213.696073 138.001028 \n", "L 214.031208 140.810386 \n", "L 214.366343 140.111918 \n", "L 214.701478 139.934184 \n", "L 215.036613 134.045891 \n", "L 215.371748 138.271785 \n", "L 215.706883 140.578295 \n", "L 216.042019 141.327699 \n", "L 216.377154 142.378511 \n", "L 216.712289 142.062143 \n", "L 217.047424 141.977297 \n", "L 217.717694 138.759079 \n", "L 218.052829 137.934346 \n", "L 218.387965 139.759139 \n", "L 219.058235 145.026427 \n", "L 219.39337 143.159672 \n", "L 219.728505 134.251441 \n", "L 220.06364 138.08595 \n", "L 220.398775 135.284045 \n", "L 220.73391 134.240793 \n", "L 221.069046 139.503328 \n", "L 221.404181 142.57716 \n", "L 221.739316 142.578516 \n", "L 222.074451 144.094499 \n", "L 222.409586 136.716169 \n", "L 222.744721 137.573942 \n", "L 223.079856 135.545786 \n", "L 223.414992 138.833582 \n", "L 223.750127 135.380439 \n", "L 224.085262 135.535522 \n", "L 224.420397 134.179987 \n", "L 224.755532 134.663273 \n", "L 225.425802 143.045674 \n", "L 225.760938 142.70787 \n", "L 226.096073 143.948872 \n", "L 226.431208 134.437468 \n", "L 226.766343 135.910114 \n", "L 227.101478 135.375588 \n", "L 227.436613 137.440755 \n", "L 228.106883 137.019222 \n", "L 228.442019 136.296759 \n", "L 228.777154 133.665418 \n", "L 229.112289 132.359864 \n", "L 229.447424 131.728509 \n", "L 229.782559 132.928711 \n", "L 230.117694 138.244323 \n", "L 230.452829 140.082497 \n", "L 230.787965 131.125373 \n", "L 231.1231 133.851319 \n", "L 231.458235 131.497461 \n", "L 231.79337 138.436751 \n", "L 232.128505 142.210044 \n", "L 232.46364 135.280612 \n", "L 232.798775 135.245143 \n", "L 233.469046 127.525447 \n", "L 233.804181 127.449817 \n", "L 234.139316 130.416947 \n", "L 234.474451 130.50161 \n", "L 234.809586 133.113027 \n", "L 235.144721 134.710382 \n", "L 235.479856 131.796182 \n", "L 235.814992 131.744533 \n", "L 236.150127 127.642889 \n", "L 236.485262 127.221409 \n", "L 237.155532 125.507303 \n", "L 237.490667 129.466912 \n", "L 237.825802 130.330062 \n", "L 238.160938 131.814355 \n", "L 238.496073 132.360779 \n", "L 238.831208 127.567677 \n", "L 239.166343 128.252801 \n", "L 239.501478 125.386515 \n", "L 239.836613 129.177326 \n", "L 240.171748 124.914765 \n", "L 240.506883 132.197944 \n", "L 240.842019 136.561222 \n", "L 241.177154 137.615094 \n", "L 241.512289 127.275419 \n", "L 241.847424 129.875129 \n", "L 242.182559 118.990867 \n", "L 242.852829 125.617403 \n", "L 243.187965 119.830154 \n", "L 243.5231 123.340383 \n", "L 243.858235 120.592651 \n", "L 244.19337 119.013199 \n", "L 244.528505 116.077384 \n", "L 244.86364 121.497955 \n", "L 245.198775 122.188699 \n", "L 245.53391 123.499669 \n", "L 245.869046 116.493259 \n", "L 246.204181 113.856074 \n", "L 246.539316 119.361666 \n", "L 246.874451 115.48854 \n", "L 247.209586 118.027792 \n", "L 247.544721 123.437454 \n", "L 247.879856 120.585777 \n", "L 248.214992 120.580825 \n", "L 248.550127 111.442719 \n", "L 248.885262 114.043285 \n", "L 249.220397 113.833321 \n", "L 249.890667 104.842681 \n", "L 250.225802 113.426688 \n", "L 250.560938 109.80106 \n", "L 250.896073 113.452815 \n", "L 251.231208 113.180136 \n", "L 251.566343 105.748184 \n", "L 251.901478 104.035595 \n", "L 252.236613 103.7974 \n", "L 252.571748 100.742824 \n", "L 252.906883 100.68058 \n", "L 253.577154 99.587165 \n", "L 253.912289 103.578012 \n", "L 254.247424 103.548878 \n", "L 254.917694 112.934139 \n", "L 255.252829 107.546713 \n", "L 255.587965 110.901383 \n", "L 255.9231 107.091059 \n", "L 256.59337 104.031441 \n", "L 256.928505 96.476742 \n", "L 257.598775 100.509428 \n", "L 257.93391 104.799169 \n", "L 258.269046 103.241828 \n", "L 258.604181 108.787219 \n", "L 258.939316 110.851363 \n", "L 259.274451 100.819627 \n", "L 259.609586 95.554398 \n", "L 259.944721 99.70492 \n", "L 260.279856 102.405595 \n", "L 260.614992 100.815979 \n", "L 260.950127 102.334009 \n", "L 261.285262 100.884267 \n", "L 261.620397 96.074715 \n", "L 261.955532 101.293811 \n", "L 262.290667 91.951016 \n", "L 262.625802 93.301872 \n", "L 262.960938 95.223911 \n", "L 263.296073 92.917271 \n", "L 263.631208 94.542206 \n", "L 263.966343 90.590196 \n", "L 264.301478 91.100039 \n", "L 264.636613 88.464074 \n", "L 264.971748 87.995444 \n", "L 265.306883 95.595726 \n", "L 265.642019 95.896754 \n", "L 265.977154 97.50474 \n", "L 266.312289 97.794453 \n", "L 266.647424 92.199772 \n", "L 266.982559 90.415151 \n", "L 267.317694 83.532643 \n", "L 267.652829 82.195839 \n", "L 267.987965 87.337593 \n", "L 268.658235 84.56 \n", "L 268.99337 91.074536 \n", "L 269.66364 67.69931 \n", "L 269.998775 83.123909 \n", "L 270.33391 83.095406 \n", "L 270.669046 81.164178 \n", "L 271.004181 90.419032 \n", "L 271.339316 84.943771 \n", "L 271.674451 82.382104 \n", "L 272.009586 78.732454 \n", "L 272.344721 77.244112 \n", "L 272.679856 81.006963 \n", "L 273.014992 74.004648 \n", "L 273.350127 81.472106 \n", "L 273.685262 77.672819 \n", "L 274.020397 82.592369 \n", "L 274.355532 84.345009 \n", "L 274.690667 81.150621 \n", "L 275.025802 84.058136 \n", "L 275.360937 83.746169 \n", "L 275.696073 76.399539 \n", "L 276.031208 77.161009 \n", "L 276.366343 80.344549 \n", "L 276.701478 75.722048 \n", "L 277.036613 79.170559 \n", "L 277.371748 75.47037 \n", "L 277.706883 63.588331 \n", "L 278.042019 77.892173 \n", "L 278.377154 70.556912 \n", "L 278.712289 74.551102 \n", "L 279.047424 74.132903 \n", "L 279.382559 65.281915 \n", "L 279.717694 71.948096 \n", "L 280.052829 67.634216 \n", "L 280.387965 66.06594 \n", "L 280.7231 70.972494 \n", "L 281.058235 68.281894 \n", "L 281.39337 62.310082 \n", "L 281.728505 68.824259 \n", "L 282.06364 66.367777 \n", "L 282.398775 56.976181 \n", "L 282.73391 70.016148 \n", "L 283.069046 63.307949 \n", "L 283.404181 59.811516 \n", "L 283.739316 59.325799 \n", "L 284.074451 61.912601 \n", "L 284.409586 67.167076 \n", "L 284.744721 64.084978 \n", "L 285.079856 69.842541 \n", "L 285.414992 59.514762 \n", "L 285.750127 59.829819 \n", "L 286.085262 68.284406 \n", "L 286.420397 59.879752 \n", "L 286.755532 56.856802 \n", "L 287.090667 66.240285 \n", "L 287.425802 56.694818 \n", "L 287.760938 64.588557 \n", "L 288.096073 62.278497 \n", "L 288.431208 69.928325 \n", "L 288.766343 69.056712 \n", "L 289.101478 56.7719 \n", "L 289.436613 67.364319 \n", "L 289.771748 60.872818 \n", "L 290.106883 60.543064 \n", "L 290.442019 60.739163 \n", "L 290.777154 62.368075 \n", "L 291.112289 67.992346 \n", "L 291.447424 58.296134 \n", "L 291.782559 56.526275 \n", "L 292.117694 59.414763 \n", "L 292.452829 50.172413 \n", "L 292.787965 52.213999 \n", "L 293.1231 60.815903 \n", "L 293.458235 50.878557 \n", "L 293.79337 52.047987 \n", "L 294.128505 57.330143 \n", "L 294.46364 57.055834 \n", "L 294.798775 58.836532 \n", "L 295.13391 41.810073 \n", "L 295.804181 53.686773 \n", "L 296.139316 46.245519 \n", "L 296.474451 55.982476 \n", "L 296.809586 46.277043 \n", "L 297.144721 39.998849 \n", "L 298.150127 52.606749 \n", "L 298.485262 54.018452 \n", "L 298.820397 48.309454 \n", "L 299.155532 40.337119 \n", "L 299.490667 50.979092 \n", "L 299.825802 56.739197 \n", "L 300.160938 47.348667 \n", "L 300.496073 48.055174 \n", "L 300.831208 51.831475 \n", "L 301.166343 50.726029 \n", "L 301.501478 43.639048 \n", "L 301.836613 45.452211 \n", "L 302.171748 63.519166 \n", "L 302.506883 57.508421 \n", "L 302.842019 34.414778 \n", "L 303.177154 48.679311 \n", "L 303.512289 57.881767 \n", "L 303.847424 51.481887 \n", "L 304.182559 47.360447 \n", "L 304.517694 62.057455 \n", "L 304.852829 54.548037 \n", "L 305.187965 50.662751 \n", "L 305.5231 59.560796 \n", "L 305.858235 43.261912 \n", "L 306.19337 55.370479 \n", "L 306.528505 54.911906 \n", "L 306.86364 43.499114 \n", "L 307.869046 55.97166 \n", "L 308.204181 50.611146 \n", "L 308.539316 48.715698 \n", "L 308.874451 61.243489 \n", "L 309.209586 57.639842 \n", "L 309.544721 39.16854 \n", "L 309.879856 46.903847 \n", "L 310.214992 46.285018 \n", "L 310.550127 40.431872 \n", "L 310.885262 52.167151 \n", "L 311.220397 46.128826 \n", "L 311.890667 58.489573 \n", "L 312.225802 44.056445 \n", "L 312.560938 48.222862 \n", "L 312.896073 61.516641 \n", "L 313.231208 59.31685 \n", "L 313.566343 42.29538 \n", "L 313.901478 52.78268 \n", "L 314.236613 50.065096 \n", "L 314.571748 51.138855 \n", "L 314.906883 52.594728 \n", "L 315.242019 46.447531 \n", "L 315.577154 51.223233 \n", "L 315.912289 57.807809 \n", "L 316.247424 53.345041 \n", "L 316.582559 45.882155 \n", "L 316.917694 58.356825 \n", "L 317.252829 51.79745 \n", "L 317.587965 42.340793 \n", "L 317.9231 49.166402 \n", "L 318.258235 50.269031 \n", "L 318.59337 42.014833 \n", "L 318.928505 41.847865 \n", "L 319.26364 56.444662 \n", "L 319.598775 59.892644 \n", "L 319.93391 42.505908 \n", "L 320.269046 43.876935 \n", "L 320.604181 43.790524 \n", "L 320.939316 44.48238 \n", "L 321.274451 44.687082 \n", "L 321.609586 45.653085 \n", "L 321.944721 39.994301 \n", "L 322.279856 40.284857 \n", "L 322.614992 38.673927 \n", "L 323.285262 55.173465 \n", "L 323.620397 33.36119 \n", "L 323.955532 43.186188 \n", "L 324.290667 47.06069 \n", "L 324.625802 47.336097 \n", "L 324.960938 47.337079 \n", "L 325.296073 39.398377 \n", "L 325.631208 47.424832 \n", "L 326.301478 56.508855 \n", "L 326.971748 43.497609 \n", "L 327.306883 48.865298 \n", "L 327.642019 42.811492 \n", "L 327.977154 44.416526 \n", "L 328.312289 51.415388 \n", "L 328.647424 53.238979 \n", "L 329.317694 48.188846 \n", "L 329.652829 49.882116 \n", "L 329.987965 44.783915 \n", "L 330.3231 52.946221 \n", "L 330.658235 58.244767 \n", "L 330.99337 46.319883 \n", "L 331.328505 52.587679 \n", "L 331.66364 53.405952 \n", "L 331.998775 57.590775 \n", "L 332.33391 55.751957 \n", "L 333.004181 46.658861 \n", "L 333.674451 55.962157 \n", "L 334.344721 44.466894 \n", "L 334.679856 54.76217 \n", "L 335.014992 53.187687 \n", "L 335.350127 43.439277 \n", "L 335.685262 46.665466 \n", "L 336.020397 54.598829 \n", "L 336.355532 55.856753 \n", "L 336.690667 45.496236 \n", "L 337.025802 59.459561 \n", "L 337.360937 58.611866 \n", "L 337.696073 55.222247 \n", "L 338.031208 62.044394 \n", "L 338.701478 47.173416 \n", "L 339.036613 53.759935 \n", "L 339.371748 50.351931 \n", "L 339.706883 53.625746 \n", "L 340.042019 60.435752 \n", "L 340.377154 61.867005 \n", "L 340.712289 60.590104 \n", "L 341.047424 55.11274 \n", "L 341.382559 59.094475 \n", "L 342.052829 49.58725 \n", "L 342.387965 56.090171 \n", "L 342.7231 54.927366 \n", "L 343.058235 49.231403 \n", "L 343.39337 51.673778 \n", "L 343.728505 70.199997 \n", "L 344.06364 56.799051 \n", "L 344.398775 57.671299 \n", "L 344.73391 78.813687 \n", "L 345.069046 65.45184 \n", "L 345.404181 62.699946 \n", "L 345.739316 69.069294 \n", "L 346.074451 61.03542 \n", "L 346.409586 57.129362 \n", "L 346.744721 67.458236 \n", "L 347.079856 66.769742 \n", "L 347.414992 68.05416 \n", "L 347.750127 72.652168 \n", "L 348.085262 78.994038 \n", "L 348.420397 75.545208 \n", "L 348.755532 76.107479 \n", "L 349.090667 63.352785 \n", "L 349.425802 66.611894 \n", "L 349.760938 71.793506 \n", "L 350.096073 72.688735 \n", "L 350.431208 79.829747 \n", "L 350.766343 73.157876 \n", "L 351.101478 69.059715 \n", "L 351.436613 69.62706 \n", "L 351.771748 71.115111 \n", "L 352.106883 71.885965 \n", "L 352.442019 74.428205 \n", "L 352.777154 65.378236 \n", "L 353.112289 59.464768 \n", "L 353.782559 71.924233 \n", "L 354.117694 66.234334 \n", "L 354.452829 74.197517 \n", "L 354.787965 77.583462 \n", "L 355.1231 79.649816 \n", "L 355.458235 74.657856 \n", "L 355.79337 83.713968 \n", "L 356.128505 86.516233 \n", "L 356.46364 70.925161 \n", "L 356.798775 81.228194 \n", "L 357.469046 81.94577 \n", "L 358.139316 89.139537 \n", "L 358.474451 79.110658 \n", "L 358.809586 82.343381 \n", "L 359.479856 77.226906 \n", "L 359.814992 77.90116 \n", "L 360.150127 80.454582 \n", "L 360.485262 84.274344 \n", "L 360.820397 78.472687 \n", "L 361.155532 67.219454 \n", "L 361.490667 77.609741 \n", "L 361.825802 82.831678 \n", "L 362.160938 75.705414 \n", "L 362.496073 83.278349 \n", "L 362.831208 81.556702 \n", "L 363.166343 81.231255 \n", "L 363.501478 85.770944 \n", "L 363.836613 82.788066 \n", "L 364.171748 82.178109 \n", "L 364.506883 86.270072 \n", "L 364.842019 84.652331 \n", "L 365.177154 96.810502 \n", "L 365.512289 96.512325 \n", "L 365.847424 98.796497 \n", "L 366.182559 102.397881 \n", "L 366.517694 93.019815 \n", "L 366.852829 90.490779 \n", "L 367.187965 95.41331 \n", "L 367.5231 95.342379 \n", "L 367.858235 95.923404 \n", "L 368.19337 92.852275 \n", "L 368.528505 98.370936 \n", "L 368.86364 101.507627 \n", "L 369.198775 95.856534 \n", "L 369.53391 97.437429 \n", "L 369.869046 91.950745 \n", "L 370.204181 93.244072 \n", "L 370.539316 94.984594 \n", "L 370.874451 97.660045 \n", "L 371.209586 105.817661 \n", "L 371.544721 104.841402 \n", "L 371.879856 99.822879 \n", "L 372.214992 105.450766 \n", "L 372.550127 102.59319 \n", "L 372.885262 103.061544 \n", "L 373.220397 108.337212 \n", "L 373.555532 104.924014 \n", "L 373.890667 105.971037 \n", "L 374.225802 99.670258 \n", "L 374.560938 109.861931 \n", "L 374.896073 114.519026 \n", "L 375.231208 109.408952 \n", "L 375.566343 120.264213 \n", "L 375.901478 115.362007 \n", "L 376.236613 108.624338 \n", "L 376.571748 113.830015 \n", "L 376.906883 113.045418 \n", "L 377.242019 112.651794 \n", "L 377.577154 114.591601 \n", "L 377.912289 120.711014 \n", "L 378.247424 113.708943 \n", "L 378.582559 110.246768 \n", "L 378.917694 109.535148 \n", "L 379.252829 111.531864 \n", "L 379.587965 110.646149 \n", "L 379.9231 108.008951 \n", "L 380.258235 115.392518 \n", "L 380.59337 113.185448 \n", "L 380.928505 99.971901 \n", "L 381.26364 99.430814 \n", "L 381.93391 109.922637 \n", "L 382.269046 111.158016 \n", "L 382.604181 120.016137 \n", "L 382.939316 120.183712 \n", "L 383.609586 111.170638 \n", "L 383.944721 115.842845 \n", "L 384.279856 118.642618 \n", "L 384.614992 116.700697 \n", "L 384.950127 126.380101 \n", "L 385.285262 125.072596 \n", "L 385.620397 118.929828 \n", "L 385.955532 117.423616 \n", "L 386.290667 113.092496 \n", "L 386.625802 117.783944 \n", "L 386.960938 117.949117 \n", "L 386.960938 117.949117 \n", "\" clip-path=\"url(#p5055f4ef74)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #bf00bf; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"patch_3\">\n", "    <path d=\"M 52.**********.52 \n", "L 52.160938 7.2 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_4\">\n", "    <path d=\"M 386.**********.52 \n", "L 386.960938 7.2 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_5\">\n", "    <path d=\"M 52.**********.52 \n", "L 386.**********.52 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_6\">\n", "    <path d=\"M 52.160938 7.2 \n", "L 386.960938 7.2 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"legend_1\">\n", "    <g id=\"patch_7\">\n", "     <path d=\"M 59.160938 168.52 \n", "L 153.885938 168.52 \n", "Q 155.885938 168.52 155.885938 166.52 \n", "L 155.885938 138.16375 \n", "Q 155.885938 136.16375 153.885938 136.16375 \n", "L 59.160938 136.16375 \n", "Q 57.160938 136.16375 57.160938 138.16375 \n", "L 57.160938 166.52 \n", "Q 57.160938 168.52 59.160938 168.52 \n", "z\n", "\" style=\"fill: #ffffff; opacity: 0.8; stroke: #cccccc; stroke-linejoin: miter\"/>\n", "    </g>\n", "    <g id=\"line2d_27\">\n", "     <path d=\"M 61.160938 144.262188 \n", "L 71.160938 144.262188 \n", "L 81.160938 144.262188 \n", "\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "    </g>\n", "    <g id=\"text_15\">\n", "     <!-- data -->\n", "     <g transform=\"translate(89.160938 147.762188)scale(0.1 -0.1)\">\n", "      <defs>\n", "       <path id=\"DejaVuSans-64\" d=\"M 2906 2969 \n", "L 2906 4863 \n", "L 3481 4863 \n", "L 3481 0 \n", "L 2906 0 \n", "L 2906 525 \n", "Q 2725 213 2448 61 \n", "Q 2172 -91 1784 -91 \n", "Q 1150 -91 751 415 \n", "Q 353 922 353 1747 \n", "Q 353 2572 751 3078 \n", "Q 1150 3584 1784 3584 \n", "Q 2172 3584 2448 3432 \n", "Q 2725 3281 2906 2969 \n", "z\n", "M 947 1747 \n", "Q 947 1113 1208 752 \n", "Q 1469 391 1925 391 \n", "Q 2381 391 2643 752 \n", "Q 2906 1113 2906 1747 \n", "Q 2906 2381 2643 2742 \n", "Q 2381 3103 1925 3103 \n", "Q 1469 3103 1208 2742 \n", "Q 947 2381 947 1747 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-61\" d=\"M 2194 1759 \n", "Q 1497 1759 1228 1600 \n", "Q 959 1441 959 1056 \n", "Q 959 750 1161 570 \n", "Q 1363 391 1709 391 \n", "Q 2188 391 2477 730 \n", "Q 2766 1069 2766 1631 \n", "L 2766 1759 \n", "L 2194 1759 \n", "z\n", "M 3341 1997 \n", "L 3341 0 \n", "L 2766 0 \n", "L 2766 531 \n", "Q 2569 213 2275 61 \n", "Q 1981 -91 1556 -91 \n", "Q 1019 -91 701 211 \n", "Q 384 513 384 1019 \n", "Q 384 1609 779 1909 \n", "Q 1175 2209 1959 2209 \n", "L 2766 2209 \n", "L 2766 2266 \n", "Q 2766 2663 2505 2880 \n", "Q 2244 3097 1772 3097 \n", "Q 1472 3097 1187 3025 \n", "Q 903 2953 641 2809 \n", "L 641 3341 \n", "Q 956 3463 1253 3523 \n", "Q 1550 3584 1831 3584 \n", "Q 2591 3584 2966 3190 \n", "Q 3341 2797 3341 1997 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "      </defs>\n", "      <use xlink:href=\"#DejaVuSans-64\"/>\n", "      <use xlink:href=\"#DejaVuSans-61\" x=\"63.476562\"/>\n", "      <use xlink:href=\"#DejaVuSans-74\" x=\"124.755859\"/>\n", "      <use xlink:href=\"#DejaVuSans-61\" x=\"163.964844\"/>\n", "     </g>\n", "    </g>\n", "    <g id=\"line2d_28\">\n", "     <path d=\"M 61.160938 158.940313 \n", "L 71.160938 158.940313 \n", "L 81.160938 158.940313 \n", "\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #bf00bf; stroke-width: 1.5\"/>\n", "    </g>\n", "    <g id=\"text_16\">\n", "     <!-- 1-step preds -->\n", "     <g transform=\"translate(89.160938 162.440313)scale(0.1 -0.1)\">\n", "      <defs>\n", "       <path id=\"DejaVuSans-2d\" d=\"M 313 2009 \n", "L 1997 2009 \n", "L 1997 1497 \n", "L 313 1497 \n", "L 313 2009 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-73\" d=\"M 2834 3397 \n", "L 2834 2853 \n", "Q 2591 2978 2328 3040 \n", "Q 2066 3103 1784 3103 \n", "Q 1356 3103 1142 2972 \n", "Q 928 2841 928 2578 \n", "Q 928 2378 1081 2264 \n", "Q 1234 2150 1697 2047 \n", "L 1894 2003 \n", "Q 2506 1872 2764 1633 \n", "Q 3022 1394 3022 966 \n", "Q 3022 478 2636 193 \n", "Q 2250 -91 1575 -91 \n", "Q 1294 -91 989 -36 \n", "Q 684 19 347 128 \n", "L 347 722 \n", "Q 666 556 975 473 \n", "Q 1284 391 1588 391 \n", "Q 1994 391 2212 530 \n", "Q 2431 669 2431 922 \n", "Q 2431 1156 2273 1281 \n", "Q 2116 1406 1581 1522 \n", "L 1381 1569 \n", "Q 847 1681 609 1914 \n", "Q 372 2147 372 2553 \n", "Q 372 3047 722 3315 \n", "Q 1072 3584 1716 3584 \n", "Q 2034 3584 2315 3537 \n", "Q 2597 3491 2834 3397 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-70\" d=\"M 1159 525 \n", "L 1159 -1331 \n", "L 581 -1331 \n", "L 581 3500 \n", "L 1159 3500 \n", "L 1159 2969 \n", "Q 1341 3281 1617 3432 \n", "Q 1894 3584 2278 3584 \n", "Q 2916 3584 3314 3078 \n", "Q 3713 2572 3713 1747 \n", "Q 3713 922 3314 415 \n", "Q 2916 -91 2278 -91 \n", "Q 1894 -91 1617 61 \n", "Q 1341 213 1159 525 \n", "z\n", "M 3116 1747 \n", "Q 3116 2381 2855 2742 \n", "Q 2594 3103 2138 3103 \n", "Q 1681 3103 1420 2742 \n", "Q 1159 2381 1159 1747 \n", "Q 1159 1113 1420 752 \n", "Q 1681 391 2138 391 \n", "Q 2594 391 2855 752 \n", "Q 3116 1113 3116 1747 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-20\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-72\" d=\"M 2631 2963 \n", "Q 2534 3019 2420 3045 \n", "Q 2306 3072 2169 3072 \n", "Q 1681 3072 1420 2755 \n", "Q 1159 2438 1159 1844 \n", "L 1159 0 \n", "L 581 0 \n", "L 581 3500 \n", "L 1159 3500 \n", "L 1159 2956 \n", "Q 1341 3275 1631 3429 \n", "Q 1922 3584 2338 3584 \n", "Q 2397 3584 2469 3576 \n", "Q 2541 3569 2628 3553 \n", "L 2631 2963 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "      </defs>\n", "      <use xlink:href=\"#DejaVuSans-31\"/>\n", "      <use xlink:href=\"#DejaVuSans-2d\" x=\"63.623047\"/>\n", "      <use xlink:href=\"#DejaVuSans-73\" x=\"99.707031\"/>\n", "      <use xlink:href=\"#DejaVuSans-74\" x=\"151.806641\"/>\n", "      <use xlink:href=\"#DejaVuSans-65\" x=\"191.015625\"/>\n", "      <use xlink:href=\"#DejaVuSans-70\" x=\"252.539062\"/>\n", "      <use xlink:href=\"#DejaVuSans-20\" x=\"316.015625\"/>\n", "      <use xlink:href=\"#DejaVuSans-70\" x=\"347.802734\"/>\n", "      <use xlink:href=\"#DejaVuSans-72\" x=\"411.279297\"/>\n", "      <use xlink:href=\"#DejaVuSans-65\" x=\"450.142578\"/>\n", "      <use xlink:href=\"#DejaVuSans-64\" x=\"511.666016\"/>\n", "      <use xlink:href=\"#DejaVuSans-73\" x=\"575.142578\"/>\n", "     </g>\n", "    </g>\n", "   </g>\n", "  </g>\n", " </g>\n", " <defs>\n", "  <clipPath id=\"p5055f4ef74\">\n", "   <rect x=\"52.160938\" y=\"7.2\" width=\"334.8\" height=\"166.32\"/>\n", "  </clipPath>\n", " </defs>\n", "</svg>\n"], "text/plain": ["<Figure size 600x300 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["onestep_preds = net(features)\n", "d2l.plot([time, time[tau:]],\n", "         [x.detach().numpy(), onestep_preds.detach().numpy()], 'time',\n", "         'x', legend=['data', '1-step preds'], xlim=[1, 1000],\n", "         figsize=(6, 3))"]}, {"cell_type": "markdown", "id": "42adfc43", "metadata": {"origin_pos": 23}, "source": ["正如我们所料，单步预测效果不错。\n", "即使这些预测的时间步超过了$600+4$（`n_train + tau`），\n", "其结果看起来仍然是可信的。\n", "然而有一个小问题：如果数据观察序列的时间步只到$604$，\n", "我们需要一步一步地向前迈进：\n", "$$\n", "\\hat{x}_{605} = f(x_{601}, x_{602}, x_{603}, x_{604}), \\\\\n", "\\hat{x}_{606} = f(x_{602}, x_{603}, x_{604}, \\hat{x}_{605}), \\\\\n", "\\hat{x}_{607} = f(x_{603}, x_{604}, \\hat{x}_{605}, \\hat{x}_{606}),\\\\\n", "\\hat{x}_{608} = f(x_{604}, \\hat{x}_{605}, \\hat{x}_{606}, \\hat{x}_{607}),\\\\\n", "\\hat{x}_{609} = f(\\hat{x}_{605}, \\hat{x}_{606}, \\hat{x}_{607}, \\hat{x}_{608}),\\\\\n", "\\ldots\n", "$$\n", "\n", "通常，对于直到$x_t$的观测序列，其在时间步$t+k$处的预测输出$\\hat{x}_{t+k}$\n", "称为$k$*步预测*（$k$-step-ahead-prediction）。\n", "由于我们的观察已经到了$x_{604}$，它的$k$步预测是$\\hat{x}_{604+k}$。\n", "换句话说，我们必须使用我们自己的预测（而不是原始数据）来[**进行多步预测**]。\n", "让我们看看效果如何。\n"]}, {"cell_type": "code", "execution_count": 8, "id": "fc614321", "metadata": {"execution": {"iopub.execute_input": "2022-12-07T16:51:37.792689Z", "iopub.status.busy": "2022-12-07T16:51:37.792136Z", "iopub.status.idle": "2022-12-07T16:51:37.832808Z", "shell.execute_reply": "2022-12-07T16:51:37.832074Z"}, "origin_pos": 24, "tab": ["pytorch"]}, "outputs": [], "source": ["multistep_preds = torch.zeros(T)\n", "multistep_preds[: n_train + tau] = x[: n_train + tau]\n", "for i in range(n_train + tau, T):\n", "    multistep_preds[i] = net(\n", "        multistep_preds[i - tau:i].reshape((1, -1)))"]}, {"cell_type": "code", "execution_count": 9, "id": "1ed66d04", "metadata": {"execution": {"iopub.execute_input": "2022-12-07T16:51:37.836292Z", "iopub.status.busy": "2022-12-07T16:51:37.835737Z", "iopub.status.idle": "2022-12-07T16:51:38.125555Z", "shell.execute_reply": "2022-12-07T16:51:38.124437Z"}, "origin_pos": 27, "tab": ["pytorch"]}, "outputs": [{"data": {"image/svg+xml": ["<?xml version=\"1.0\" encoding=\"utf-8\" standalone=\"no\"?>\n", "<!DOCTYPE svg PUBLIC \"-//W3C//DTD SVG 1.1//EN\"\n", "  \"http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd\">\n", "<svg xmlns:xlink=\"http://www.w3.org/1999/xlink\" width=\"406.885938pt\" height=\"211.07625pt\" viewBox=\"0 0 406.**********.07625\" xmlns=\"http://www.w3.org/2000/svg\" version=\"1.1\">\n", " <metadata>\n", "  <rdf:RDF xmlns:dc=\"http://purl.org/dc/elements/1.1/\" xmlns:cc=\"http://creativecommons.org/ns#\" xmlns:rdf=\"http://www.w3.org/1999/02/22-rdf-syntax-ns#\">\n", "   <cc:Work>\n", "    <dc:type rdf:resource=\"http://purl.org/dc/dcmitype/StillImage\"/>\n", "    <dc:date>2022-12-07T16:51:38.065276</dc:date>\n", "    <dc:format>image/svg+xml</dc:format>\n", "    <dc:creator>\n", "     <cc:Agent>\n", "      <dc:title>Matplotlib v3.5.1, https://matplotlib.org/</dc:title>\n", "     </cc:Agent>\n", "    </dc:creator>\n", "   </cc:Work>\n", "  </rdf:RDF>\n", " </metadata>\n", " <defs>\n", "  <style type=\"text/css\">*{stroke-linejoin: round; stroke-linecap: butt}</style>\n", " </defs>\n", " <g id=\"figure_1\">\n", "  <g id=\"patch_1\">\n", "   <path d=\"M 0 211.07625 \n", "L 406.**********.07625 \n", "L 406.885938 0 \n", "L 0 0 \n", "z\n", "\" style=\"fill: #ffffff\"/>\n", "  </g>\n", "  <g id=\"axes_1\">\n", "   <g id=\"patch_2\">\n", "    <path d=\"M 52.**********.52 \n", "L 386.**********.52 \n", "L 386.960938 7.2 \n", "L 52.160938 7.2 \n", "z\n", "\" style=\"fill: #ffffff\"/>\n", "   </g>\n", "   <g id=\"matplotlib.axis_1\">\n", "    <g id=\"xtick_1\">\n", "     <g id=\"line2d_1\">\n", "      <path d=\"M 118.852829 173.52 \n", "L 118.852829 7.2 \n", "\" clip-path=\"url(#pda9f443d7f)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_2\">\n", "      <defs>\n", "       <path id=\"m8f7316f5ef\" d=\"M 0 0 \n", "L 0 3.5 \n", "\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </defs>\n", "      <g>\n", "       <use xlink:href=\"#m8f7316f5ef\" x=\"118.852829\" y=\"173.52\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_1\">\n", "      <!-- 200 -->\n", "      <g transform=\"translate(109.309079 188.118438)scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-32\" d=\"M 1228 531 \n", "L 3431 531 \n", "L 3431 0 \n", "L 469 0 \n", "L 469 531 \n", "Q 828 903 1448 1529 \n", "Q 2069 2156 2228 2338 \n", "Q 2531 2678 2651 2914 \n", "Q 2772 3150 2772 3378 \n", "Q 2772 3750 2511 3984 \n", "Q 2250 4219 1831 4219 \n", "Q 1534 4219 1204 4116 \n", "Q 875 4013 500 3803 \n", "L 500 4441 \n", "Q 881 4594 1212 4672 \n", "Q 1544 4750 1819 4750 \n", "Q 2544 4750 2975 4387 \n", "Q 3406 4025 3406 3419 \n", "Q 3406 3131 3298 2873 \n", "Q 3191 2616 2906 2266 \n", "Q 2828 2175 2409 1742 \n", "Q 1991 1309 1228 531 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "        <path id=\"DejaVuSans-30\" d=\"M 2034 4250 \n", "Q 1547 4250 1301 3770 \n", "Q 1056 3291 1056 2328 \n", "Q 1056 1369 1301 889 \n", "Q 1547 409 2034 409 \n", "Q 2525 409 2770 889 \n", "Q 3016 1369 3016 2328 \n", "Q 3016 3291 2770 3770 \n", "Q 2525 4250 2034 4250 \n", "z\n", "M 2034 4750 \n", "Q 2819 4750 3233 4129 \n", "Q 3647 3509 3647 2328 \n", "Q 3647 1150 3233 529 \n", "Q 2819 -91 2034 -91 \n", "Q 1250 -91 836 529 \n", "Q 422 1150 422 2328 \n", "Q 422 3509 836 4129 \n", "Q 1250 4750 2034 4750 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-32\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"127.246094\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_2\">\n", "     <g id=\"line2d_3\">\n", "      <path d=\"M 185.879856 173.52 \n", "L 185.879856 7.2 \n", "\" clip-path=\"url(#pda9f443d7f)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_4\">\n", "      <g>\n", "       <use xlink:href=\"#m8f7316f5ef\" x=\"185.879856\" y=\"173.52\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_2\">\n", "      <!-- 400 -->\n", "      <g transform=\"translate(176.336106 188.118438)scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-34\" d=\"M 2419 4116 \n", "L 825 1625 \n", "L 2419 1625 \n", "L 2419 4116 \n", "z\n", "M 2253 4666 \n", "L 3047 4666 \n", "L 3047 1625 \n", "L 3713 1625 \n", "L 3713 1100 \n", "L 3047 1100 \n", "L 3047 0 \n", "L 2419 0 \n", "L 2419 1100 \n", "L 313 1100 \n", "L 313 1709 \n", "L 2253 4666 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-34\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"127.246094\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_3\">\n", "     <g id=\"line2d_5\">\n", "      <path d=\"M 252.906883 173.52 \n", "L 252.906883 7.2 \n", "\" clip-path=\"url(#pda9f443d7f)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_6\">\n", "      <g>\n", "       <use xlink:href=\"#m8f7316f5ef\" x=\"252.906883\" y=\"173.52\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_3\">\n", "      <!-- 600 -->\n", "      <g transform=\"translate(243.363133 188.118438)scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-36\" d=\"M 2113 2584 \n", "Q 1688 2584 1439 2293 \n", "Q 1191 2003 1191 1497 \n", "Q 1191 994 1439 701 \n", "Q 1688 409 2113 409 \n", "Q 2538 409 2786 701 \n", "Q 3034 994 3034 1497 \n", "Q 3034 2003 2786 2293 \n", "Q 2538 2584 2113 2584 \n", "z\n", "M 3366 4563 \n", "L 3366 3988 \n", "Q 3128 4100 2886 4159 \n", "Q 2644 4219 2406 4219 \n", "Q 1781 4219 1451 3797 \n", "Q 1122 3375 1075 2522 \n", "Q 1259 2794 1537 2939 \n", "Q 1816 3084 2150 3084 \n", "Q 2853 3084 3261 2657 \n", "Q 3669 2231 3669 1497 \n", "Q 3669 778 3244 343 \n", "Q 2819 -91 2113 -91 \n", "Q 1303 -91 875 529 \n", "Q 447 1150 447 2328 \n", "Q 447 3434 972 4092 \n", "Q 1497 4750 2381 4750 \n", "Q 2619 4750 2861 4703 \n", "Q 3103 4656 3366 4563 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-36\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"127.246094\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_4\">\n", "     <g id=\"line2d_7\">\n", "      <path d=\"M 319.93391 173.52 \n", "L 319.93391 7.2 \n", "\" clip-path=\"url(#pda9f443d7f)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_8\">\n", "      <g>\n", "       <use xlink:href=\"#m8f7316f5ef\" x=\"319.93391\" y=\"173.52\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_4\">\n", "      <!-- 800 -->\n", "      <g transform=\"translate(310.39016 188.118438)scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-38\" d=\"M 2034 2216 \n", "Q 1584 2216 1326 1975 \n", "Q 1069 1734 1069 1313 \n", "Q 1069 891 1326 650 \n", "Q 1584 409 2034 409 \n", "Q 2484 409 2743 651 \n", "Q 3003 894 3003 1313 \n", "Q 3003 1734 2745 1975 \n", "Q 2488 2216 2034 2216 \n", "z\n", "M 1403 2484 \n", "Q 997 2584 770 2862 \n", "Q 544 3141 544 3541 \n", "Q 544 4100 942 4425 \n", "Q 1341 4750 2034 4750 \n", "Q 2731 4750 3128 4425 \n", "Q 3525 4100 3525 3541 \n", "Q 3525 3141 3298 2862 \n", "Q 3072 2584 2669 2484 \n", "Q 3125 2378 3379 2068 \n", "Q 3634 1759 3634 1313 \n", "Q 3634 634 3220 271 \n", "Q 2806 -91 2034 -91 \n", "Q 1263 -91 848 271 \n", "Q 434 634 434 1313 \n", "Q 434 1759 690 2068 \n", "Q 947 2378 1403 2484 \n", "z\n", "M 1172 3481 \n", "Q 1172 3119 1398 2916 \n", "Q 1625 2713 2034 2713 \n", "Q 2441 2713 2670 2916 \n", "Q 2900 3119 2900 3481 \n", "Q 2900 3844 2670 4047 \n", "Q 2441 4250 2034 4250 \n", "Q 1625 4250 1398 4047 \n", "Q 1172 3844 1172 3481 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-38\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"127.246094\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_5\">\n", "     <g id=\"line2d_9\">\n", "      <path d=\"M 386.**********.52 \n", "L 386.960938 7.2 \n", "\" clip-path=\"url(#pda9f443d7f)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_10\">\n", "      <g>\n", "       <use xlink:href=\"#m8f7316f5ef\" x=\"386.960938\" y=\"173.52\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_5\">\n", "      <!-- 1000 -->\n", "      <g transform=\"translate(374.235937 188.118438)scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-31\" d=\"M 794 531 \n", "L 1825 531 \n", "L 1825 4091 \n", "L 703 3866 \n", "L 703 4441 \n", "L 1819 4666 \n", "L 2450 4666 \n", "L 2450 531 \n", "L 3481 531 \n", "L 3481 0 \n", "L 794 0 \n", "L 794 531 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-31\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"127.246094\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"190.869141\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"text_6\">\n", "     <!-- time -->\n", "     <g transform=\"translate(208.264844 201.796563)scale(0.1 -0.1)\">\n", "      <defs>\n", "       <path id=\"DejaVuSans-74\" d=\"M 1172 4494 \n", "L 1172 3500 \n", "L 2356 3500 \n", "L 2356 3053 \n", "L 1172 3053 \n", "L 1172 1153 \n", "Q 1172 725 1289 603 \n", "Q 1406 481 1766 481 \n", "L 2356 481 \n", "L 2356 0 \n", "L 1766 0 \n", "Q 1100 0 847 248 \n", "Q 594 497 594 1153 \n", "L 594 3053 \n", "L 172 3053 \n", "L 172 3500 \n", "L 594 3500 \n", "L 594 4494 \n", "L 1172 4494 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-69\" d=\"M 603 3500 \n", "L 1178 3500 \n", "L 1178 0 \n", "L 603 0 \n", "L 603 3500 \n", "z\n", "M 603 4863 \n", "L 1178 4863 \n", "L 1178 4134 \n", "L 603 4134 \n", "L 603 4863 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-6d\" d=\"M 3328 2828 \n", "Q 3544 3216 3844 3400 \n", "Q 4144 3584 4550 3584 \n", "Q 5097 3584 5394 3201 \n", "Q 5691 2819 5691 2113 \n", "L 5691 0 \n", "L 5113 0 \n", "L 5113 2094 \n", "Q 5113 2597 4934 2840 \n", "Q 4756 3084 4391 3084 \n", "Q 3944 3084 3684 2787 \n", "Q 3425 2491 3425 1978 \n", "L 3425 0 \n", "L 2847 0 \n", "L 2847 2094 \n", "Q 2847 2600 2669 2842 \n", "Q 2491 3084 2119 3084 \n", "Q 1678 3084 1418 2786 \n", "Q 1159 2488 1159 1978 \n", "L 1159 0 \n", "L 581 0 \n", "L 581 3500 \n", "L 1159 3500 \n", "L 1159 2956 \n", "Q 1356 3278 1631 3431 \n", "Q 1906 3584 2284 3584 \n", "Q 2666 3584 2933 3390 \n", "Q 3200 3197 3328 2828 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-65\" d=\"M 3597 1894 \n", "L 3597 1613 \n", "L 953 1613 \n", "Q 991 1019 1311 708 \n", "Q 1631 397 2203 397 \n", "Q 2534 397 2845 478 \n", "Q 3156 559 3463 722 \n", "L 3463 178 \n", "Q 3153 47 2828 -22 \n", "Q 2503 -91 2169 -91 \n", "Q 1331 -91 842 396 \n", "Q 353 884 353 1716 \n", "Q 353 2575 817 3079 \n", "Q 1281 3584 2069 3584 \n", "Q 2775 3584 3186 3129 \n", "Q 3597 2675 3597 1894 \n", "z\n", "M 3022 2063 \n", "Q 3016 2534 2758 2815 \n", "Q 2500 3097 2075 3097 \n", "Q 1594 3097 1305 2825 \n", "Q 1016 2553 972 2059 \n", "L 3022 2063 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "      </defs>\n", "      <use xlink:href=\"#DejaVuSans-74\"/>\n", "      <use xlink:href=\"#DejaVuSans-69\" x=\"39.208984\"/>\n", "      <use xlink:href=\"#DejaVuSans-6d\" x=\"66.992188\"/>\n", "      <use xlink:href=\"#DejaVuSans-65\" x=\"164.404297\"/>\n", "     </g>\n", "    </g>\n", "   </g>\n", "   <g id=\"matplotlib.axis_2\">\n", "    <g id=\"ytick_1\">\n", "     <g id=\"line2d_11\">\n", "      <path d=\"M 52.160938 166.995592 \n", "L 386.960938 166.995592 \n", "\" clip-path=\"url(#pda9f443d7f)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_12\">\n", "      <defs>\n", "       <path id=\"m55d3fa6974\" d=\"M 0 0 \n", "L -3.5 0 \n", "\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </defs>\n", "      <g>\n", "       <use xlink:href=\"#m55d3fa6974\" x=\"52.160938\" y=\"166.995592\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_7\">\n", "      <!-- −1.5 -->\n", "      <g transform=\"translate(20.878125 170.794811)scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-2212\" d=\"M 678 2272 \n", "L 4684 2272 \n", "L 4684 1741 \n", "L 678 1741 \n", "L 678 2272 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "        <path id=\"DejaVuSans-2e\" d=\"M 684 794 \n", "L 1344 794 \n", "L 1344 0 \n", "L 684 0 \n", "L 684 794 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "        <path id=\"DejaVuSans-35\" d=\"M 691 4666 \n", "L 3169 4666 \n", "L 3169 4134 \n", "L 1269 4134 \n", "L 1269 2991 \n", "Q 1406 3038 1543 3061 \n", "Q 1681 3084 1819 3084 \n", "Q 2600 3084 3056 2656 \n", "Q 3513 2228 3513 1497 \n", "Q 3513 744 3044 326 \n", "Q 2575 -91 1722 -91 \n", "Q 1428 -91 1123 -41 \n", "Q 819 9 494 109 \n", "L 494 744 \n", "Q 775 591 1075 516 \n", "Q 1375 441 1709 441 \n", "Q 2250 441 2565 725 \n", "Q 2881 1009 2881 1497 \n", "Q 2881 1984 2565 2268 \n", "Q 2250 2553 1709 2553 \n", "Q 1456 2553 1204 2497 \n", "Q 953 2441 691 2322 \n", "L 691 4666 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-2212\"/>\n", "       <use xlink:href=\"#DejaVuSans-31\" x=\"83.789062\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"147.412109\"/>\n", "       <use xlink:href=\"#DejaVuSans-35\" x=\"179.199219\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_2\">\n", "     <g id=\"line2d_13\">\n", "      <path d=\"M 52.160938 142.631734 \n", "L 386.960938 142.631734 \n", "\" clip-path=\"url(#pda9f443d7f)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_14\">\n", "      <g>\n", "       <use xlink:href=\"#m55d3fa6974\" x=\"52.160938\" y=\"142.631734\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_8\">\n", "      <!-- −1.0 -->\n", "      <g transform=\"translate(20.878125 146.430952)scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-2212\"/>\n", "       <use xlink:href=\"#DejaVuSans-31\" x=\"83.789062\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"147.412109\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"179.199219\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_3\">\n", "     <g id=\"line2d_15\">\n", "      <path d=\"M 52.160938 118.267875 \n", "L 386.960938 118.267875 \n", "\" clip-path=\"url(#pda9f443d7f)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_16\">\n", "      <g>\n", "       <use xlink:href=\"#m55d3fa6974\" x=\"52.160938\" y=\"118.267875\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_9\">\n", "      <!-- −0.5 -->\n", "      <g transform=\"translate(20.878125 122.067094)scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-2212\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"83.789062\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"147.412109\"/>\n", "       <use xlink:href=\"#DejaVuSans-35\" x=\"179.199219\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_4\">\n", "     <g id=\"line2d_17\">\n", "      <path d=\"M 52.160938 93.904016 \n", "L 386.960938 93.904016 \n", "\" clip-path=\"url(#pda9f443d7f)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_18\">\n", "      <g>\n", "       <use xlink:href=\"#m55d3fa6974\" x=\"52.160938\" y=\"93.904016\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_10\">\n", "      <!-- 0.0 -->\n", "      <g transform=\"translate(29.257812 97.703235)scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"95.410156\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_5\">\n", "     <g id=\"line2d_19\">\n", "      <path d=\"M 52.160938 69.540158 \n", "L 386.960938 69.540158 \n", "\" clip-path=\"url(#pda9f443d7f)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_20\">\n", "      <g>\n", "       <use xlink:href=\"#m55d3fa6974\" x=\"52.160938\" y=\"69.540158\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_11\">\n", "      <!-- 0.5 -->\n", "      <g transform=\"translate(29.257812 73.339377)scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-35\" x=\"95.410156\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_6\">\n", "     <g id=\"line2d_21\">\n", "      <path d=\"M 52.160938 45.176299 \n", "L 386.960938 45.176299 \n", "\" clip-path=\"url(#pda9f443d7f)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_22\">\n", "      <g>\n", "       <use xlink:href=\"#m55d3fa6974\" x=\"52.160938\" y=\"45.176299\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_12\">\n", "      <!-- 1.0 -->\n", "      <g transform=\"translate(29.257812 48.975518)scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-31\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"95.410156\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_7\">\n", "     <g id=\"line2d_23\">\n", "      <path d=\"M 52.160938 20.812441 \n", "L 386.960938 20.812441 \n", "\" clip-path=\"url(#pda9f443d7f)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_24\">\n", "      <g>\n", "       <use xlink:href=\"#m55d3fa6974\" x=\"52.160938\" y=\"20.812441\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_13\">\n", "      <!-- 1.5 -->\n", "      <g transform=\"translate(29.257812 24.611659)scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-31\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-35\" x=\"95.410156\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"text_14\">\n", "     <!-- x -->\n", "     <g transform=\"translate(14.798438 93.319375)rotate(-90)scale(0.1 -0.1)\">\n", "      <defs>\n", "       <path id=\"DejaVuSans-78\" d=\"M 3513 3500 \n", "L 2247 1797 \n", "L 3578 0 \n", "L 2900 0 \n", "L 1881 1375 \n", "L 863 0 \n", "L 184 0 \n", "L 1544 1831 \n", "L 300 3500 \n", "L 978 3500 \n", "L 1906 2253 \n", "L 2834 3500 \n", "L 3513 3500 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "      </defs>\n", "      <use xlink:href=\"#DejaVuSans-78\"/>\n", "     </g>\n", "    </g>\n", "   </g>\n", "   <g id=\"line2d_25\">\n", "    <path d=\"M 52.160938 74.122182 \n", "L 52.496073 82.976358 \n", "L 52.831208 86.558977 \n", "L 53.166343 83.001781 \n", "L 53.501478 82.64153 \n", "L 53.836613 85.200891 \n", "L 54.171748 78.027478 \n", "L 54.506883 82.563021 \n", "L 54.842019 78.881125 \n", "L 55.177154 63.187702 \n", "L 55.512289 104.799279 \n", "L 55.847424 101.529501 \n", "L 56.182559 77.360766 \n", "L 56.517694 78.594721 \n", "L 56.852829 98.553949 \n", "L 57.187965 92.06747 \n", "L 57.5231 78.070344 \n", "L 57.858235 102.228351 \n", "L 58.19337 83.277319 \n", "L 58.528505 79.367227 \n", "L 58.86364 72.546103 \n", "L 59.198775 101.833262 \n", "L 59.53391 93.326703 \n", "L 59.869046 89.723121 \n", "L 60.204181 77.823903 \n", "L 60.539316 81.70565 \n", "L 60.874451 64.461051 \n", "L 61.209586 84.679889 \n", "L 61.544721 78.420531 \n", "L 61.879856 67.901008 \n", "L 62.214992 67.108084 \n", "L 62.550127 60.179015 \n", "L 62.885262 101.993429 \n", "L 63.220397 74.308081 \n", "L 63.555532 67.066302 \n", "L 64.225802 89.228962 \n", "L 64.560938 80.305291 \n", "L 64.896073 48.521341 \n", "L 65.231208 72.533865 \n", "L 65.566343 77.365541 \n", "L 65.901478 74.073157 \n", "L 66.236613 83.163421 \n", "L 66.571748 64.118552 \n", "L 66.906883 82.451677 \n", "L 67.242019 82.780181 \n", "L 67.577154 63.032906 \n", "L 67.912289 56.477075 \n", "L 68.247424 74.51123 \n", "L 68.582559 77.660944 \n", "L 68.917694 79.406715 \n", "L 69.587965 69.246506 \n", "L 69.9231 67.676429 \n", "L 70.258235 69.621616 \n", "L 70.59337 61.960669 \n", "L 70.928505 70.970748 \n", "L 71.26364 63.431543 \n", "L 71.598775 70.363378 \n", "L 71.93391 67.026198 \n", "L 72.269046 66.295803 \n", "L 72.604181 58.59723 \n", "L 72.939316 74.558062 \n", "L 73.274451 57.760131 \n", "L 73.609586 52.936239 \n", "L 73.944721 52.300379 \n", "L 74.279856 49.711407 \n", "L 74.614992 69.070976 \n", "L 74.950127 76.704328 \n", "L 75.285262 58.766496 \n", "L 75.620397 70.914502 \n", "L 75.955532 68.629031 \n", "L 76.290667 76.122422 \n", "L 76.625802 54.722089 \n", "L 76.960938 57.870062 \n", "L 77.631208 54.247635 \n", "L 78.301478 79.934818 \n", "L 78.636613 56.857057 \n", "L 78.971748 52.632044 \n", "L 79.306883 52.454562 \n", "L 79.642019 55.07879 \n", "L 79.977154 61.14855 \n", "L 80.312289 44.971208 \n", "L 80.647424 46.844795 \n", "L 80.982559 68.34088 \n", "L 81.317694 52.221934 \n", "L 81.652829 58.852675 \n", "L 81.987965 61.54735 \n", "L 82.3231 66.295617 \n", "L 82.658235 57.934153 \n", "L 82.99337 54.135645 \n", "L 83.328505 58.786388 \n", "L 83.66364 57.171827 \n", "L 83.998775 61.658556 \n", "L 84.33391 59.793372 \n", "L 84.669046 42.114157 \n", "L 85.004181 46.375392 \n", "L 85.339316 61.905317 \n", "L 85.674451 43.29131 \n", "L 86.009586 50.473117 \n", "L 86.344721 61.904951 \n", "L 86.679856 57.67373 \n", "L 87.014992 60.784042 \n", "L 87.350127 31.671405 \n", "L 88.020397 55.47522 \n", "L 88.355532 47.265718 \n", "L 88.690667 45.848023 \n", "L 89.025802 49.093185 \n", "L 89.360938 50.768184 \n", "L 89.696073 57.478776 \n", "L 90.366343 40.765541 \n", "L 90.701478 43.428578 \n", "L 91.036613 39.507983 \n", "L 91.706883 58.448845 \n", "L 92.042019 41.859627 \n", "L 92.377154 36.339418 \n", "L 92.712289 62.010441 \n", "L 93.047424 51.105503 \n", "L 93.382559 33.425876 \n", "L 93.717694 30.236295 \n", "L 94.052829 54.818753 \n", "L 94.387965 49.477173 \n", "L 94.7231 38.673642 \n", "L 95.058235 36.546194 \n", "L 95.39337 55.301694 \n", "L 95.728505 33.595104 \n", "L 96.398775 54.597877 \n", "L 97.069046 34.687065 \n", "L 97.739316 47.32601 \n", "L 98.074451 54.569181 \n", "L 98.409586 41.007675 \n", "L 98.744721 41.859848 \n", "L 99.079856 53.729517 \n", "L 99.414992 50.770536 \n", "L 99.750127 21.531088 \n", "L 100.085262 32.556968 \n", "L 100.420397 38.042702 \n", "L 100.755532 38.846814 \n", "L 101.090667 45.123869 \n", "L 101.760938 50.01453 \n", "L 102.096073 48.865216 \n", "L 102.431208 46.663607 \n", "L 102.766343 50.066847 \n", "L 103.101478 47.34876 \n", "L 103.771748 64.230389 \n", "L 104.106883 49.479859 \n", "L 104.442019 43.334115 \n", "L 104.777154 61.813425 \n", "L 105.112289 49.557706 \n", "L 105.447424 21.106691 \n", "L 105.782559 29.668172 \n", "L 106.117694 34.84227 \n", "L 106.452829 42.628451 \n", "L 106.787965 40.812307 \n", "L 107.1231 55.911385 \n", "L 107.458235 59.605518 \n", "L 108.128505 36.199321 \n", "L 108.46364 45.036464 \n", "L 108.798775 14.76 \n", "L 109.13391 45.084085 \n", "L 109.469046 42.518932 \n", "L 109.804181 37.967885 \n", "L 110.139316 55.18309 \n", "L 110.474451 56.16315 \n", "L 110.809586 46.497356 \n", "L 111.144721 48.028317 \n", "L 111.479856 38.634136 \n", "L 111.814992 52.529751 \n", "L 112.150127 37.844465 \n", "L 112.485262 40.981907 \n", "L 112.820397 33.238008 \n", "L 113.155532 39.443087 \n", "L 113.490667 60.496187 \n", "L 113.825802 50.277265 \n", "L 114.160938 47.686719 \n", "L 114.496073 46.424703 \n", "L 114.831208 50.855461 \n", "L 115.166343 48.273233 \n", "L 115.501478 49.836441 \n", "L 115.836613 39.206593 \n", "L 116.171748 44.026413 \n", "L 116.506883 41.823642 \n", "L 116.842019 60.954437 \n", "L 117.177154 44.662076 \n", "L 117.512289 39.875476 \n", "L 117.847424 58.049682 \n", "L 118.182559 31.080546 \n", "L 118.517694 47.363207 \n", "L 118.852829 53.541446 \n", "L 119.187965 56.515578 \n", "L 119.858235 39.105241 \n", "L 120.19337 52.218408 \n", "L 120.528505 39.256148 \n", "L 120.86364 46.240404 \n", "L 121.198775 41.974781 \n", "L 121.53391 53.846738 \n", "L 121.869046 52.550587 \n", "L 122.204181 58.158541 \n", "L 122.539316 46.863136 \n", "L 122.874451 63.584532 \n", "L 123.209586 48.814532 \n", "L 123.544721 59.667405 \n", "L 123.879856 63.531298 \n", "L 124.214992 59.230598 \n", "L 124.550127 66.808144 \n", "L 124.885262 45.554124 \n", "L 125.555532 66.620828 \n", "L 125.890667 58.426301 \n", "L 126.225802 53.024701 \n", "L 126.560938 39.026892 \n", "L 126.896073 67.900021 \n", "L 127.566343 39.890492 \n", "L 127.901478 46.506345 \n", "L 128.236613 55.657857 \n", "L 128.571748 56.188715 \n", "L 128.906883 51.804287 \n", "L 129.577154 55.269836 \n", "L 129.912289 54.751032 \n", "L 130.247424 64.196387 \n", "L 130.582559 67.982047 \n", "L 130.917694 61.594325 \n", "L 131.252829 66.488742 \n", "L 131.587965 51.616373 \n", "L 131.9231 67.834303 \n", "L 132.258235 55.916535 \n", "L 132.59337 58.952037 \n", "L 132.928505 76.572419 \n", "L 133.26364 40.997091 \n", "L 133.598775 73.107654 \n", "L 133.93391 71.857237 \n", "L 134.269046 51.203858 \n", "L 134.939316 80.93351 \n", "L 135.274451 44.688581 \n", "L 135.609586 67.69281 \n", "L 135.944721 69.479604 \n", "L 136.279856 77.991395 \n", "L 136.614992 63.03516 \n", "L 136.950127 64.080139 \n", "L 137.285262 60.579956 \n", "L 137.620397 58.380705 \n", "L 137.955532 76.36534 \n", "L 138.290667 53.871803 \n", "L 138.625802 74.117331 \n", "L 138.960938 72.100446 \n", "L 139.296073 78.896079 \n", "L 139.631208 78.689855 \n", "L 139.966343 71.499177 \n", "L 140.301478 67.016817 \n", "L 140.636613 80.978291 \n", "L 140.971748 74.063908 \n", "L 141.306883 82.309214 \n", "L 141.642019 74.399541 \n", "L 141.977154 56.450035 \n", "L 142.312289 65.276455 \n", "L 142.647424 57.087469 \n", "L 142.982559 81.949268 \n", "L 143.317694 67.81918 \n", "L 143.652829 60.835429 \n", "L 143.987965 80.972241 \n", "L 144.3231 56.774488 \n", "L 144.658235 77.152168 \n", "L 144.99337 77.330111 \n", "L 145.328505 82.816353 \n", "L 145.66364 62.89569 \n", "L 145.998775 101.728936 \n", "L 146.33391 89.605577 \n", "L 146.669046 91.743255 \n", "L 147.339316 69.964368 \n", "L 147.674451 76.091184 \n", "L 148.009586 99.109418 \n", "L 148.344721 96.737219 \n", "L 148.679856 72.754827 \n", "L 149.014992 84.190594 \n", "L 149.350127 80.783762 \n", "L 149.685262 80.462836 \n", "L 150.020397 87.558727 \n", "L 150.355532 72.004855 \n", "L 150.690667 100.381437 \n", "L 151.025802 82.76547 \n", "L 151.696073 72.484614 \n", "L 152.031208 78.231729 \n", "L 152.366343 91.292196 \n", "L 152.701478 84.314366 \n", "L 153.036613 82.234719 \n", "L 153.371748 85.793342 \n", "L 153.706883 96.798187 \n", "L 154.042019 84.319305 \n", "L 154.377154 76.755225 \n", "L 154.712289 82.878741 \n", "L 155.047424 73.441116 \n", "L 155.382559 94.434888 \n", "L 155.717694 90.928361 \n", "L 156.052829 95.051205 \n", "L 156.387965 88.54265 \n", "L 156.7231 95.295112 \n", "L 157.058235 91.186746 \n", "L 157.39337 93.997711 \n", "L 157.728505 106.357914 \n", "L 158.06364 100.123307 \n", "L 158.398775 124.556246 \n", "L 158.73391 98.441045 \n", "L 159.069046 92.631761 \n", "L 159.404181 111.994417 \n", "L 159.739316 101.774558 \n", "L 160.074451 112.960521 \n", "L 160.409586 94.004235 \n", "L 160.744721 96.975301 \n", "L 161.079856 94.022321 \n", "L 161.414992 101.485591 \n", "L 161.750127 92.555663 \n", "L 162.085262 113.659214 \n", "L 162.420397 99.817401 \n", "L 162.755532 104.741996 \n", "L 163.090667 96.429009 \n", "L 163.425802 110.817288 \n", "L 163.760938 87.88694 \n", "L 164.096073 110.958457 \n", "L 164.431208 111.363763 \n", "L 164.766343 107.884371 \n", "L 165.436613 87.543632 \n", "L 165.771748 107.976449 \n", "L 166.106883 110.810034 \n", "L 166.442019 107.195185 \n", "L 166.777154 106.43953 \n", "L 167.112289 96.322765 \n", "L 167.447424 115.789651 \n", "L 167.782559 118.260733 \n", "L 168.117694 106.940622 \n", "L 168.452829 102.408069 \n", "L 168.787965 89.180145 \n", "L 169.1231 109.694511 \n", "L 169.458235 117.616055 \n", "L 169.79337 117.204022 \n", "L 170.128505 97.80659 \n", "L 170.46364 123.859263 \n", "L 171.13391 111.797512 \n", "L 171.469046 101.818608 \n", "L 171.804181 119.903882 \n", "L 172.139316 120.979973 \n", "L 172.474451 114.140519 \n", "L 172.809586 115.84008 \n", "L 173.144721 118.562759 \n", "L 173.479856 129.871402 \n", "L 173.814992 114.43148 \n", "L 174.150127 110.454135 \n", "L 174.485262 121.600602 \n", "L 174.820397 122.778484 \n", "L 175.155532 126.896471 \n", "L 175.490667 123.102304 \n", "L 175.825802 109.965809 \n", "L 176.160938 126.443085 \n", "L 176.496073 109.167743 \n", "L 176.831208 133.899596 \n", "L 177.166343 118.036238 \n", "L 177.501478 115.984871 \n", "L 178.171748 139.225368 \n", "L 178.506883 116.119274 \n", "L 178.842019 129.404166 \n", "L 179.177154 113.709983 \n", "L 179.847424 126.569633 \n", "L 180.182559 115.226744 \n", "L 180.517694 129.282289 \n", "L 180.852829 125.918496 \n", "L 181.187965 115.175925 \n", "L 181.5231 119.261365 \n", "L 181.858235 137.783729 \n", "L 182.19337 128.523514 \n", "L 182.528505 103.564498 \n", "L 182.86364 125.028937 \n", "L 183.198775 133.263684 \n", "L 183.53391 126.567004 \n", "L 183.869046 130.705481 \n", "L 184.204181 142.722699 \n", "L 184.539316 126.269219 \n", "L 184.874451 135.905534 \n", "L 185.209586 132.455076 \n", "L 185.544721 149.412237 \n", "L 185.879856 129.601723 \n", "L 186.214992 130.007316 \n", "L 186.550127 140.761127 \n", "L 186.885262 134.989001 \n", "L 187.220397 133.31447 \n", "L 187.555532 128.858162 \n", "L 187.890667 136.658606 \n", "L 188.225802 126.315495 \n", "L 188.560938 123.610684 \n", "L 188.896073 138.78895 \n", "L 189.231208 147.510449 \n", "L 189.566343 125.873327 \n", "L 190.236613 151.15605 \n", "L 190.571748 123.473774 \n", "L 190.906883 152.106502 \n", "L 191.242019 126.895471 \n", "L 191.577154 149.237282 \n", "L 191.912289 135.887236 \n", "L 192.247424 132.08952 \n", "L 192.917694 137.784493 \n", "L 193.252829 138.578811 \n", "L 193.587965 136.398227 \n", "L 193.9231 136.819054 \n", "L 194.258235 154.657947 \n", "L 194.59337 136.729532 \n", "L 194.928505 145.6692 \n", "L 195.26364 148.314444 \n", "L 195.598775 126.131876 \n", "L 195.93391 153.338403 \n", "L 196.269046 154.236873 \n", "L 196.604181 123.903587 \n", "L 196.939316 138.51111 \n", "L 197.274451 136.908472 \n", "L 197.609586 144.408098 \n", "L 197.944721 136.512248 \n", "L 198.279856 131.984122 \n", "L 198.614992 150.486545 \n", "L 198.950127 138.117349 \n", "L 199.285262 142.125073 \n", "L 199.620397 129.792115 \n", "L 199.955532 146.859974 \n", "L 200.290667 137.656566 \n", "L 200.625802 159.033068 \n", "L 200.960938 143.287227 \n", "L 201.296073 140.894174 \n", "L 201.631208 154.002865 \n", "L 201.966343 155.796819 \n", "L 202.301478 141.997105 \n", "L 202.636613 141.873959 \n", "L 202.971748 143.413691 \n", "L 203.306883 141.520915 \n", "L 203.642019 158.191827 \n", "L 203.977154 137.50351 \n", "L 204.312289 153.17851 \n", "L 204.647424 141.464009 \n", "L 204.982559 143.130047 \n", "L 205.317694 133.890811 \n", "L 205.652829 136.494426 \n", "L 205.987965 149.425923 \n", "L 206.658235 140.113423 \n", "L 206.99337 144.887655 \n", "L 207.328505 145.767734 \n", "L 207.66364 138.250434 \n", "L 207.998775 146.850459 \n", "L 208.33391 141.284174 \n", "L 208.669046 143.835502 \n", "L 209.004181 158.903154 \n", "L 209.339316 154.548242 \n", "L 209.674451 165.96 \n", "L 210.009586 134.823895 \n", "L 210.344721 138.67057 \n", "L 210.679856 151.367705 \n", "L 211.014992 133.467852 \n", "L 211.350127 164.494342 \n", "L 211.685262 144.529235 \n", "L 212.020397 150.848695 \n", "L 212.355532 150.914142 \n", "L 212.690667 155.378115 \n", "L 213.025802 128.632749 \n", "L 213.360938 144.495434 \n", "L 213.696073 148.088511 \n", "L 214.031208 146.137075 \n", "L 214.366343 124.879273 \n", "L 214.701478 134.323865 \n", "L 215.036613 155.504248 \n", "L 215.371748 146.751739 \n", "L 216.042019 145.38896 \n", "L 216.377154 149.815211 \n", "L 216.712289 143.822514 \n", "L 217.047424 142.069918 \n", "L 217.382559 135.954319 \n", "L 217.717694 144.225453 \n", "L 218.052829 146.464854 \n", "L 218.387965 156.775247 \n", "L 218.7231 151.135789 \n", "L 219.058235 120.227011 \n", "L 219.39337 141.620376 \n", "L 219.728505 143.748562 \n", "L 220.06364 126.056687 \n", "L 220.398775 138.445322 \n", "L 220.73391 157.138053 \n", "L 221.069046 143.868595 \n", "L 221.404181 153.936523 \n", "L 221.739316 127.584191 \n", "L 222.074451 143.54946 \n", "L 222.409586 130.311764 \n", "L 222.744721 147.35676 \n", "L 223.079856 136.632438 \n", "L 223.414992 134.192415 \n", "L 223.750127 134.369496 \n", "L 224.085262 133.819879 \n", "L 224.420397 138.477663 \n", "L 224.755532 156.344775 \n", "L 225.090667 144.877298 \n", "L 225.425802 153.590447 \n", "L 225.760938 124.716188 \n", "L 226.096073 134.79917 \n", "L 226.766343 141.504946 \n", "L 227.101478 140.680083 \n", "L 227.436613 137.494309 \n", "L 227.771748 139.026156 \n", "L 228.106883 134.947166 \n", "L 228.442019 127.365444 \n", "L 228.777154 132.093496 \n", "L 229.112289 127.946021 \n", "L 229.447424 142.168886 \n", "L 229.782559 150.509804 \n", "L 230.117694 119.885082 \n", "L 230.452829 126.566601 \n", "L 230.787965 142.409814 \n", "L 231.1231 137.976622 \n", "L 231.458235 153.415608 \n", "L 231.79337 131.730513 \n", "L 232.128505 133.381606 \n", "L 232.46364 133.005213 \n", "L 232.798775 122.511189 \n", "L 233.13391 116.659335 \n", "L 233.804181 145.177956 \n", "L 234.139316 125.205251 \n", "L 234.474451 139.273143 \n", "L 234.809586 128.719875 \n", "L 235.144721 132.583828 \n", "L 235.814992 117.535868 \n", "L 236.150127 126.708934 \n", "L 236.485262 126.663883 \n", "L 236.820397 125.540395 \n", "L 237.155532 129.728451 \n", "L 237.490667 129.103023 \n", "L 237.825802 136.631439 \n", "L 238.160938 112.479726 \n", "L 238.496073 131.592824 \n", "L 238.831208 105.319914 \n", "L 239.166343 141.392543 \n", "L 239.501478 131.446373 \n", "L 239.836613 126.596423 \n", "L 240.171748 132.710248 \n", "L 240.506883 155.6859 \n", "L 240.842019 110.266371 \n", "L 241.177154 121.812263 \n", "L 241.512289 129.327074 \n", "L 241.847424 109.058873 \n", "L 242.517694 127.93046 \n", "L 242.852829 127.655335 \n", "L 243.187965 101.100084 \n", "L 243.5231 114.685435 \n", "L 243.858235 116.485125 \n", "L 244.19337 134.518195 \n", "L 244.528505 115.448721 \n", "L 244.86364 110.555254 \n", "L 245.198775 119.553533 \n", "L 245.53391 116.123524 \n", "L 245.869046 102.514994 \n", "L 246.204181 120.017011 \n", "L 246.539316 119.943759 \n", "L 246.874451 133.978672 \n", "L 247.209586 100.938322 \n", "L 247.544721 116.163515 \n", "L 247.879856 110.83836 \n", "L 248.214992 119.842349 \n", "L 248.550127 93.594694 \n", "L 248.885262 113.91589 \n", "L 249.220397 102.738063 \n", "L 249.555532 113.634371 \n", "L 249.890667 118.305075 \n", "L 250.225802 108.55523 \n", "L 250.560938 103.510515 \n", "L 250.896073 107.064743 \n", "L 251.231208 100.600565 \n", "L 251.566343 101.591273 \n", "L 251.901478 101.255869 \n", "L 252.571748 99.00492 \n", "L 252.906883 98.935157 \n", "L 253.242019 100.339615 \n", "L 253.577154 116.971299 \n", "L 253.912289 106.870787 \n", "L 254.247424 108.42852 \n", "L 254.582559 108.473647 \n", "L 254.917694 104.296306 \n", "L 255.252829 115.70195 \n", "L 255.587965 90.170256 \n", "L 255.9231 100.212505 \n", "L 256.258235 99.069072 \n", "L 256.59337 104.550048 \n", "L 256.928505 88.10503 \n", "L 257.598775 122.519519 \n", "L 257.93391 111.091175 \n", "L 258.269046 86.08018 \n", "L 258.604181 99.776671 \n", "L 258.939316 99.86324 \n", "L 259.274451 104.279022 \n", "L 259.609586 98.52269 \n", "L 259.944721 105.804131 \n", "L 260.279856 93.738329 \n", "L 260.614992 108.0413 \n", "L 260.950127 95.45474 \n", "L 261.285262 88.682498 \n", "L 261.620397 100.845327 \n", "L 261.955532 88.463805 \n", "L 262.290667 98.872803 \n", "L 262.625802 94.605555 \n", "L 262.960938 97.809408 \n", "L 263.631208 82.496181 \n", "L 264.301478 97.039864 \n", "L 264.636613 93.121583 \n", "L 264.971748 105.186654 \n", "L 265.306883 95.005097 \n", "L 265.642019 99.631876 \n", "L 265.977154 88.209012 \n", "L 266.312289 85.970676 \n", "L 266.647424 85.27872 \n", "L 266.982559 81.293832 \n", "L 267.317694 82.303372 \n", "L 267.652829 98.897848 \n", "L 267.987965 92.928675 \n", "L 268.3231 70.870747 \n", "L 268.658235 77.844387 \n", "L 268.99337 78.106689 \n", "L 269.328505 66.709955 \n", "L 269.66364 95.768692 \n", "L 269.998775 96.25946 \n", "L 270.33391 80.015546 \n", "L 270.669046 86.511928 \n", "L 271.004181 79.372592 \n", "L 271.339316 86.159739 \n", "L 271.674451 76.039655 \n", "L 272.009586 77.783681 \n", "L 272.344721 86.995124 \n", "L 272.679856 75.471741 \n", "L 273.014992 85.446228 \n", "L 273.350127 76.0489 \n", "L 273.685262 89.544298 \n", "L 274.020397 92.565636 \n", "L 274.690667 70.2588 \n", "L 275.025802 84.032657 \n", "L 275.360937 81.538159 \n", "L 275.696073 82.912849 \n", "L 276.031208 82.487098 \n", "L 276.366343 63.105432 \n", "L 276.701478 66.895204 \n", "L 277.036613 80.786107 \n", "L 277.371748 68.820808 \n", "L 277.706883 88.593721 \n", "L 278.042019 61.477804 \n", "L 278.377154 78.660639 \n", "L 278.712289 67.2971 \n", "L 279.047424 69.666813 \n", "L 279.382559 73.885986 \n", "L 279.717694 74.147886 \n", "L 280.052829 65.215744 \n", "L 280.387965 69.66156 \n", "L 280.7231 70.131763 \n", "L 281.058235 56.316003 \n", "L 281.728505 77.26494 \n", "L 282.06364 49.474861 \n", "L 282.398775 65.693294 \n", "L 282.73391 52.035117 \n", "L 283.069046 73.64204 \n", "L 283.404181 67.602622 \n", "L 283.739316 71.023396 \n", "L 284.074451 65.047365 \n", "L 284.409586 56.754555 \n", "L 284.744721 73.643153 \n", "L 285.079856 62.71314 \n", "L 285.414992 57.894976 \n", "L 285.750127 64.300362 \n", "L 286.085262 62.259906 \n", "L 286.420397 47.2557 \n", "L 286.755532 68.142477 \n", "L 287.090667 63.441735 \n", "L 287.425802 82.488975 \n", "L 287.760938 53.720298 \n", "L 288.096073 70.589806 \n", "L 288.431208 64.518903 \n", "L 288.766343 55.050434 \n", "L 289.101478 62.958097 \n", "L 289.436613 64.387343 \n", "L 289.771748 72.193278 \n", "L 290.106883 65.185483 \n", "L 290.442019 54.856697 \n", "L 290.777154 61.263567 \n", "L 291.112289 50.25981 \n", "L 291.447424 50.362596 \n", "L 291.782559 60.025544 \n", "L 292.452829 44.353727 \n", "L 292.787965 62.173224 \n", "L 293.1231 59.021226 \n", "L 293.458235 60.520561 \n", "L 293.79337 41.121591 \n", "L 294.128505 36.692273 \n", "L 294.46364 57.477869 \n", "L 294.798775 44.359036 \n", "L 295.13391 53.07628 \n", "L 295.469046 51.78032 \n", "L 295.804181 25.42078 \n", "L 296.139316 41.580811 \n", "L 296.474451 42.365492 \n", "L 296.809586 58.023266 \n", "L 297.144721 50.473318 \n", "L 297.479856 48.329061 \n", "L 297.814992 32.164212 \n", "L 298.150127 47.864549 \n", "L 298.485262 57.128255 \n", "L 298.820397 48.603396 \n", "L 299.155532 44.726989 \n", "L 299.490667 54.743146 \n", "L 299.825802 44.179132 \n", "L 300.160938 37.858162 \n", "L 300.496073 37.644097 \n", "L 300.831208 62.819607 \n", "L 301.166343 64.192574 \n", "L 301.501478 24.978951 \n", "L 301.836613 39.514686 \n", "L 302.171748 62.359091 \n", "L 302.506883 45.460198 \n", "L 302.842019 45.461459 \n", "L 303.177154 51.842076 \n", "L 303.512289 66.945154 \n", "L 303.847424 37.952968 \n", "L 304.182559 65.425148 \n", "L 304.517694 38.848347 \n", "L 305.187965 62.057031 \n", "L 305.5231 35.300851 \n", "L 305.858235 45.266341 \n", "L 306.528505 53.449324 \n", "L 306.86364 47.645534 \n", "L 307.198775 44.344997 \n", "L 307.53391 60.311342 \n", "L 307.869046 67.324056 \n", "L 308.204181 35.333636 \n", "L 308.874451 45.962166 \n", "L 309.209586 34.147614 \n", "L 309.544721 43.466317 \n", "L 309.879856 47.983766 \n", "L 310.214992 42.97979 \n", "L 310.550127 64.61539 \n", "L 310.885262 32.231007 \n", "L 311.220397 42.748257 \n", "L 311.555532 60.854392 \n", "L 311.890667 66.013623 \n", "L 312.225802 38.634717 \n", "L 312.560938 44.384555 \n", "L 312.896073 56.069565 \n", "L 313.231208 41.944802 \n", "L 313.566343 56.082512 \n", "L 313.901478 38.861586 \n", "L 314.236613 51.85799 \n", "L 314.571748 54.809439 \n", "L 314.906883 55.578683 \n", "L 315.242019 41.366379 \n", "L 315.577154 54.696263 \n", "L 315.912289 58.938674 \n", "L 316.247424 36.956196 \n", "L 316.582559 50.308943 \n", "L 316.917694 45.634163 \n", "L 317.252829 34.133231 \n", "L 317.587965 31.42528 \n", "L 317.9231 56.351939 \n", "L 318.258235 66.976591 \n", "L 318.59337 42.897183 \n", "L 318.928505 34.801016 \n", "L 319.26364 44.260757 \n", "L 319.598775 38.327159 \n", "L 319.93391 47.899152 \n", "L 320.269046 40.202831 \n", "L 320.604181 40.418965 \n", "L 320.939316 24.67299 \n", "L 321.274451 36.422693 \n", "L 321.609586 36.979901 \n", "L 321.944721 61.885535 \n", "L 322.279856 26.057064 \n", "L 322.614992 33.010733 \n", "L 322.950127 50.947989 \n", "L 323.285262 42.209021 \n", "L 323.620397 45.162556 \n", "L 323.955532 31.027308 \n", "L 324.290667 39.548592 \n", "L 324.625802 54.399791 \n", "L 324.960938 54.800035 \n", "L 325.296073 55.681531 \n", "L 325.631208 37.760534 \n", "L 325.966343 46.015229 \n", "L 326.301478 37.512696 \n", "L 326.636613 37.14587 \n", "L 326.971748 50.534958 \n", "L 327.306883 54.403227 \n", "L 327.642019 51.589443 \n", "L 327.977154 45.876465 \n", "L 328.312289 43.429461 \n", "L 328.647424 42.608991 \n", "L 328.982559 46.236774 \n", "L 329.317694 62.89899 \n", "L 329.652829 42.091619 \n", "L 329.987965 45.736363 \n", "L 330.3231 55.907188 \n", "L 330.658235 54.976242 \n", "L 330.99337 62.94528 \n", "L 331.328505 42.551763 \n", "L 331.66364 46.755005 \n", "L 331.998775 45.275377 \n", "L 332.33391 61.839169 \n", "L 332.669046 43.877586 \n", "L 333.004181 43.844697 \n", "L 333.339316 44.944691 \n", "L 333.674451 63.125242 \n", "L 334.009586 30.513996 \n", "L 334.344721 46.929333 \n", "L 334.679856 46.210619 \n", "L 335.014992 59.244902 \n", "L 335.350127 38.829016 \n", "L 335.685262 54.121919 \n", "L 336.020397 63.833724 \n", "L 336.355532 54.401601 \n", "L 336.690667 65.528539 \n", "L 337.025802 58.807233 \n", "L 337.360937 43.950544 \n", "L 337.696073 47.947101 \n", "L 338.031208 48.925485 \n", "L 338.366343 46.595687 \n", "L 338.701478 63.493921 \n", "L 339.036613 63.088734 \n", "L 339.371748 64.237493 \n", "L 339.706883 57.540756 \n", "L 340.042019 58.078981 \n", "L 340.377154 57.261001 \n", "L 340.712289 47.108415 \n", "L 341.047424 55.915524 \n", "L 341.382559 53.928131 \n", "L 341.717694 45.915146 \n", "L 342.052829 50.110936 \n", "L 342.387965 61.140545 \n", "L 342.7231 58.862986 \n", "L 343.058235 36.426892 \n", "L 343.39337 87.21454 \n", "L 343.728505 71.314331 \n", "L 344.06364 65.023435 \n", "L 344.398775 71.874138 \n", "L 344.73391 66.120606 \n", "L 345.069046 56.458086 \n", "L 345.404181 67.016596 \n", "L 345.739316 63.353299 \n", "L 346.074451 53.051668 \n", "L 346.744721 84.061934 \n", "L 347.079856 83.488185 \n", "L 347.414992 71.964509 \n", "L 347.750127 75.980809 \n", "L 348.085262 38.001669 \n", "L 348.420397 72.409037 \n", "L 348.755532 69.094975 \n", "L 349.090667 90.148197 \n", "L 349.425802 76.75297 \n", "L 349.760938 72.457868 \n", "L 350.096073 63.296881 \n", "L 350.431208 72.95957 \n", "L 350.766343 76.989967 \n", "L 351.101478 78.851165 \n", "L 351.436613 73.271072 \n", "L 351.771748 57.0232 \n", "L 352.106883 64.932847 \n", "L 352.442019 64.016492 \n", "L 352.777154 65.72255 \n", "L 353.112289 74.67013 \n", "L 353.447424 86.837408 \n", "L 353.782559 63.951593 \n", "L 354.117694 65.666062 \n", "L 354.452829 79.107465 \n", "L 354.787965 101.451466 \n", "L 355.1231 73.598768 \n", "L 355.458235 80.89558 \n", "L 355.79337 77.590992 \n", "L 356.128505 71.000879 \n", "L 356.46364 89.26004 \n", "L 356.798775 92.558124 \n", "L 357.13391 87.124488 \n", "L 357.469046 79.080655 \n", "L 357.804181 95.487891 \n", "L 358.139316 58.545759 \n", "L 358.474451 77.519692 \n", "L 358.809586 83.134692 \n", "L 359.144721 94.282429 \n", "L 359.479856 75.854837 \n", "L 359.814992 65.074323 \n", "L 360.150127 65.32237 \n", "L 360.485262 92.363018 \n", "L 360.820397 71.87024 \n", "L 361.155532 82.868709 \n", "L 361.490667 87.69368 \n", "L 361.825802 78.198965 \n", "L 362.160938 87.077575 \n", "L 362.496073 82.462158 \n", "L 362.831208 84.073484 \n", "L 363.166343 93.272929 \n", "L 363.836613 75.409046 \n", "L 364.171748 88.763673 \n", "L 364.506883 93.839258 \n", "L 364.842019 119.424241 \n", "L 365.177154 92.525979 \n", "L 365.512289 84.975138 \n", "L 365.847424 93.14001 \n", "L 366.182559 104.508942 \n", "L 366.517694 92.91488 \n", "L 366.852829 93.212533 \n", "L 367.187965 86.356417 \n", "L 367.5231 108.714747 \n", "L 367.858235 90.902056 \n", "L 368.19337 107.857921 \n", "L 368.528505 98.097803 \n", "L 369.198775 84.472201 \n", "L 369.53391 89.489557 \n", "L 369.869046 106.341411 \n", "L 370.204181 107.350688 \n", "L 370.539316 100.367594 \n", "L 370.874451 103.063221 \n", "L 371.209586 104.363041 \n", "L 371.544721 91.682501 \n", "L 371.879856 114.137115 \n", "L 372.214992 106.599804 \n", "L 372.550127 108.584077 \n", "L 372.885262 92.219318 \n", "L 373.220397 101.018691 \n", "L 373.555532 116.296612 \n", "L 373.890667 96.993194 \n", "L 374.225802 118.953182 \n", "L 374.560938 122.562339 \n", "L 374.896073 100.513015 \n", "L 375.231208 121.144048 \n", "L 375.566343 106.768413 \n", "L 376.236613 105.521416 \n", "L 376.571748 128.923038 \n", "L 376.906883 115.418919 \n", "L 377.242019 106.55171 \n", "L 377.577154 113.095772 \n", "L 377.912289 112.674382 \n", "L 378.247424 108.1739 \n", "L 378.582559 91.720892 \n", "L 378.917694 115.766163 \n", "L 379.252829 129.440369 \n", "L 379.587965 98.58422 \n", "L 379.9231 100.002236 \n", "L 380.258235 100.494226 \n", "L 380.59337 102.929046 \n", "L 380.928505 96.913464 \n", "L 381.598775 128.475368 \n", "L 381.93391 113.686341 \n", "L 382.269046 112.441606 \n", "L 382.604181 106.273918 \n", "L 382.939316 118.336555 \n", "L 383.274451 100.382387 \n", "L 383.609586 128.56343 \n", "L 383.944721 126.966493 \n", "L 384.279856 117.755986 \n", "L 384.614992 122.722214 \n", "L 384.950127 118.78685 \n", "L 385.285262 103.746844 \n", "L 385.620397 103.717631 \n", "L 385.955532 111.164847 \n", "L 386.290667 144.333194 \n", "L 386.625802 115.060377 \n", "L 386.960938 113.486222 \n", "L 386.960938 113.486222 \n", "\" clip-path=\"url(#pda9f443d7f)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_26\">\n", "    <path d=\"M 53.501478 78.453472 \n", "L 53.836613 81.778387 \n", "L 54.171748 82.724174 \n", "L 54.506883 78.616687 \n", "L 54.842019 80.65561 \n", "L 55.177154 78.336831 \n", "L 55.512289 73.021707 \n", "L 55.847424 87.622432 \n", "L 56.517694 83.469846 \n", "L 56.852829 96.330971 \n", "L 57.187965 91.219496 \n", "L 57.5231 83.867246 \n", "L 57.858235 83.880657 \n", "L 58.19337 95.068723 \n", "L 58.528505 87.196899 \n", "L 58.86364 83.792928 \n", "L 59.198775 89.718911 \n", "L 59.869046 84.295774 \n", "L 60.539316 91.163729 \n", "L 61.209586 79.437162 \n", "L 61.544721 79.078591 \n", "L 62.214992 68.013005 \n", "L 62.550127 78.063536 \n", "L 62.885262 70.128862 \n", "L 63.220397 80.484221 \n", "L 63.555532 69.86478 \n", "L 63.890667 69.94935 \n", "L 64.225802 88.588234 \n", "L 64.560938 77.372989 \n", "L 64.896073 73.440114 \n", "L 65.231208 76.355613 \n", "L 65.566343 86.516495 \n", "L 65.901478 69.872941 \n", "L 66.236613 62.081146 \n", "L 66.571748 76.135968 \n", "L 66.906883 69.769698 \n", "L 67.242019 77.059099 \n", "L 67.577154 76.43303 \n", "L 67.912289 66.667174 \n", "L 68.247424 77.996395 \n", "L 68.582559 75.652563 \n", "L 68.917694 64.350454 \n", "L 69.587965 71.979858 \n", "L 70.258235 72.849209 \n", "L 70.928505 64.4818 \n", "L 71.26364 67.623049 \n", "L 71.598775 63.412705 \n", "L 72.269046 65.38846 \n", "L 72.604181 63.606342 \n", "L 72.939316 64.749536 \n", "L 73.274451 67.952233 \n", "L 73.609586 60.317497 \n", "L 73.944721 60.776206 \n", "L 74.279856 65.91684 \n", "L 74.614992 55.524426 \n", "L 74.950127 57.814974 \n", "L 75.285262 58.308295 \n", "L 75.620397 55.781326 \n", "L 75.955532 70.589912 \n", "L 76.290667 67.496138 \n", "L 76.960938 64.844504 \n", "L 77.296073 67.989381 \n", "L 77.631208 65.795616 \n", "L 77.966343 55.463719 \n", "L 78.301478 60.157337 \n", "L 78.636613 62.479113 \n", "L 78.971748 56.94969 \n", "L 79.306883 67.097713 \n", "L 79.642019 69.934337 \n", "L 79.977154 56.190132 \n", "L 80.312289 54.882305 \n", "L 80.647424 51.847075 \n", "L 81.317694 60.667706 \n", "L 81.652829 46.652076 \n", "L 82.3231 62.280269 \n", "L 82.658235 56.157004 \n", "L 82.99337 57.397499 \n", "L 83.66364 62.821866 \n", "L 83.998775 56.399835 \n", "L 84.33391 56.874821 \n", "L 84.669046 57.619787 \n", "L 85.004181 55.562474 \n", "L 85.339316 60.271874 \n", "L 85.674451 57.911345 \n", "L 86.009586 44.625335 \n", "L 86.344721 53.645182 \n", "L 86.679856 58.511013 \n", "L 87.014992 47.55546 \n", "L 87.350127 55.436597 \n", "L 87.685262 56.378203 \n", "L 88.020397 60.237417 \n", "L 88.355532 54.61763 \n", "L 88.690667 39.47281 \n", "L 89.025802 49.962536 \n", "L 89.360938 54.072503 \n", "L 89.696073 49.304803 \n", "L 90.031208 50.568268 \n", "L 90.366343 49.640923 \n", "L 90.701478 51.406867 \n", "L 91.036613 55.365016 \n", "L 91.371748 47.922774 \n", "L 91.706883 47.154607 \n", "L 92.042019 47.309022 \n", "L 92.377154 44.257121 \n", "L 93.047424 59.051167 \n", "L 93.382559 42.505804 \n", "L 93.717694 44.887985 \n", "L 94.052829 56.321849 \n", "L 94.387965 52.780397 \n", "L 94.7231 37.159556 \n", "L 95.058235 40.434097 \n", "L 95.39337 52.716413 \n", "L 95.728505 52.119525 \n", "L 96.06364 39.978518 \n", "L 96.398775 47.621332 \n", "L 96.73391 52.154668 \n", "L 97.069046 40.129634 \n", "L 97.739316 53.48761 \n", "L 98.074451 45.764504 \n", "L 98.409586 43.197608 \n", "L 98.744721 44.694593 \n", "L 99.079856 50.854587 \n", "L 99.414992 53.935448 \n", "L 99.750127 44.290801 \n", "L 100.085262 44.961263 \n", "L 100.420397 54.735522 \n", "L 100.755532 45.528928 \n", "L 101.090667 32.285006 \n", "L 101.425802 40.526474 \n", "L 102.766343 49.568476 \n", "L 103.101478 51.718207 \n", "L 103.436613 49.353724 \n", "L 104.106883 52.967682 \n", "L 104.442019 49.762873 \n", "L 105.112289 62.469999 \n", "L 105.447424 47.824628 \n", "L 105.782559 47.58426 \n", "L 106.117694 58.015976 \n", "L 106.787965 32.847367 \n", "L 107.1231 36.598211 \n", "L 107.458235 44.886731 \n", "L 107.79337 46.4941 \n", "L 108.128505 46.575342 \n", "L 108.46364 53.997921 \n", "L 108.798775 57.575419 \n", "L 109.13391 44.028289 \n", "L 109.469046 48.514237 \n", "L 110.139316 30.729816 \n", "L 110.474451 50.479847 \n", "L 110.809586 45.113953 \n", "L 111.144721 44.805983 \n", "L 111.479856 55.655879 \n", "L 111.814992 51.442957 \n", "L 112.150127 51.772626 \n", "L 112.485262 45.237983 \n", "L 113.155532 47.611492 \n", "L 113.490667 43.674871 \n", "L 113.825802 47.128769 \n", "L 114.160938 38.87781 \n", "L 114.831208 56.406617 \n", "L 115.166343 51.800259 \n", "L 115.501478 48.757655 \n", "L 115.836613 49.992855 \n", "L 116.171748 48.969897 \n", "L 116.506883 51.05601 \n", "L 116.842019 47.951138 \n", "L 117.177154 48.423812 \n", "L 117.512289 44.525894 \n", "L 117.847424 48.401912 \n", "L 118.182559 59.433253 \n", "L 118.517694 43.60883 \n", "L 118.852829 50.957478 \n", "L 119.187965 52.31486 \n", "L 119.5231 41.84417 \n", "L 119.858235 49.007764 \n", "L 120.528505 56.397317 \n", "L 120.86364 44.678613 \n", "L 121.198775 47.409892 \n", "L 121.53391 48.808447 \n", "L 121.869046 47.314872 \n", "L 122.204181 47.389782 \n", "L 122.874451 51.979161 \n", "L 123.209586 58.271005 \n", "L 123.544721 52.897863 \n", "L 123.879856 55.370975 \n", "L 124.214992 59.338491 \n", "L 124.550127 52.612741 \n", "L 124.885262 61.630006 \n", "L 125.220397 58.365398 \n", "L 125.555532 62.696545 \n", "L 125.890667 61.247595 \n", "L 126.225802 49.958333 \n", "L 126.560938 58.479355 \n", "L 126.896073 59.197584 \n", "L 127.231208 62.521921 \n", "L 127.566343 48.791752 \n", "L 127.901478 47.985416 \n", "L 128.236613 62.332783 \n", "L 128.906883 45.695763 \n", "L 129.242019 50.146346 \n", "L 129.577154 56.147646 \n", "L 129.912289 55.521446 \n", "L 130.247424 53.17724 \n", "L 130.582559 56.695207 \n", "L 130.917694 56.648129 \n", "L 131.252829 56.441821 \n", "L 131.587965 64.491498 \n", "L 131.9231 61.424772 \n", "L 132.258235 64.967139 \n", "L 132.59337 58.544733 \n", "L 132.928505 57.32854 \n", "L 133.26364 66.315123 \n", "L 133.598775 54.470583 \n", "L 133.93391 69.171038 \n", "L 134.269046 64.757073 \n", "L 134.604181 51.057006 \n", "L 134.939316 72.331906 \n", "L 135.274451 68.013763 \n", "L 135.609586 54.675134 \n", "L 135.944721 73.083916 \n", "L 136.279856 68.268444 \n", "L 136.614992 61.365049 \n", "L 136.950127 63.940896 \n", "L 137.285262 68.743807 \n", "L 137.620397 68.845321 \n", "L 137.955532 61.306479 \n", "L 138.290667 65.546578 \n", "L 138.625802 57.291422 \n", "L 138.960938 66.666604 \n", "L 139.296073 66.910272 \n", "L 139.631208 66.732737 \n", "L 139.966343 72.983952 \n", "L 140.301478 70.33641 \n", "L 140.636613 73.395822 \n", "L 140.971748 75.071425 \n", "L 141.306883 68.355181 \n", "L 141.642019 74.833491 \n", "L 142.312289 71.021816 \n", "L 142.647424 77.643958 \n", "L 142.982559 64.171026 \n", "L 143.317694 66.492959 \n", "L 143.652829 61.425499 \n", "L 143.987965 61.683598 \n", "L 144.3231 77.024545 \n", "L 144.658235 61.635466 \n", "L 144.99337 69.168476 \n", "L 145.328505 71.335851 \n", "L 145.66364 70.609735 \n", "L 145.998775 70.727017 \n", "L 146.33391 86.206246 \n", "L 146.669046 81.531733 \n", "L 147.004181 85.421205 \n", "L 147.339316 91.018087 \n", "L 147.674451 84.025518 \n", "L 148.009586 82.952398 \n", "L 148.344721 83.82011 \n", "L 149.014992 82.858062 \n", "L 149.350127 91.821511 \n", "L 150.020397 76.82831 \n", "L 150.355532 82.079685 \n", "L 150.690667 75.599009 \n", "L 151.025802 87.720741 \n", "L 151.360938 82.450251 \n", "L 151.696073 80.233705 \n", "L 152.031208 88.563805 \n", "L 152.366343 76.489282 \n", "L 152.701478 80.859556 \n", "L 153.036613 77.401751 \n", "L 153.706883 85.317978 \n", "L 154.042019 86.836991 \n", "L 154.377154 83.636666 \n", "L 154.712289 84.655928 \n", "L 155.047424 86.574014 \n", "L 155.382559 74.97919 \n", "L 155.717694 83.881064 \n", "L 156.052829 83.106647 \n", "L 156.387965 87.924933 \n", "L 156.7231 90.938671 \n", "L 157.058235 91.64751 \n", "L 157.39337 91.249252 \n", "L 157.728505 90.525716 \n", "L 158.06364 96.057571 \n", "L 158.398775 94.571065 \n", "L 158.73391 107.027122 \n", "L 159.069046 108.316736 \n", "L 159.404181 105.659171 \n", "L 159.739316 113.308856 \n", "L 160.074451 99.04198 \n", "L 160.744721 107.414785 \n", "L 161.079856 103.064031 \n", "L 161.414992 102.205816 \n", "L 161.750127 95.874415 \n", "L 162.085262 94.799253 \n", "L 162.420397 100.635423 \n", "L 162.755532 100.69968 \n", "L 163.090667 101.609745 \n", "L 163.425802 105.697964 \n", "L 164.096073 101.378136 \n", "L 164.431208 103.016008 \n", "L 164.766343 105.439249 \n", "L 165.101478 101.020926 \n", "L 165.436613 108.599732 \n", "L 165.771748 103.424665 \n", "L 166.106883 103.530027 \n", "L 166.442019 98.67127 \n", "L 166.777154 99.965676 \n", "L 167.112289 109.212533 \n", "L 167.447424 106.759127 \n", "L 167.782559 108.923476 \n", "L 168.452829 107.437818 \n", "L 168.787965 113.991959 \n", "L 169.1231 107.832218 \n", "L 169.79337 103.411634 \n", "L 170.128505 105.551075 \n", "L 170.798775 119.31331 \n", "L 171.469046 110.802076 \n", "L 171.804181 117.908752 \n", "L 172.139316 117.032332 \n", "L 172.474451 113.515492 \n", "L 172.809586 112.594156 \n", "L 173.144721 121.048287 \n", "L 173.479856 120.422889 \n", "L 173.814992 121.009177 \n", "L 174.150127 121.140752 \n", "L 174.485262 121.653181 \n", "L 174.820397 123.289644 \n", "L 175.155532 117.968908 \n", "L 175.490667 120.628881 \n", "L 175.825802 125.622904 \n", "L 176.160938 124.287679 \n", "L 176.496073 124.532497 \n", "L 176.831208 120.268793 \n", "L 177.501478 124.56956 \n", "L 177.836613 120.282229 \n", "L 178.171748 126.600585 \n", "L 178.506883 126.327487 \n", "L 178.842019 126.986681 \n", "L 179.177154 128.215332 \n", "L 179.512289 128.762456 \n", "L 179.847424 122.639256 \n", "L 180.182559 126.368486 \n", "L 180.517694 119.476781 \n", "L 180.852829 125.957072 \n", "L 181.187965 126.708884 \n", "L 181.5231 122.969704 \n", "L 181.858235 125.055111 \n", "L 182.19337 128.209291 \n", "L 182.528505 124.720797 \n", "L 182.86364 127.064144 \n", "L 183.198775 124.201401 \n", "L 183.53391 125.684361 \n", "L 183.869046 119.7508 \n", "L 184.204181 130.336051 \n", "L 184.539316 133.842031 \n", "L 184.874451 134.524542 \n", "L 185.209586 132.636825 \n", "L 185.879856 135.658029 \n", "L 186.214992 139.342749 \n", "L 186.550127 133.193523 \n", "L 186.885262 135.20174 \n", "L 187.220397 134.963448 \n", "L 187.890667 133.483466 \n", "L 188.225802 133.030839 \n", "L 188.560938 132.934973 \n", "L 188.896073 129.659067 \n", "L 189.231208 131.603437 \n", "L 189.566343 135.80704 \n", "L 189.901478 137.056677 \n", "L 190.236613 134.231587 \n", "L 190.571748 138.746715 \n", "L 190.906883 138.820931 \n", "L 191.242019 136.419717 \n", "L 191.577154 140.871267 \n", "L 191.912289 135.36235 \n", "L 192.247424 140.019227 \n", "L 192.582559 134.626091 \n", "L 192.917694 135.314326 \n", "L 193.252829 134.926191 \n", "L 193.587965 135.59372 \n", "L 193.9231 135.947464 \n", "L 194.258235 135.766291 \n", "L 194.928505 142.250546 \n", "L 195.26364 138.825982 \n", "L 195.598775 141.810988 \n", "L 195.93391 140.122345 \n", "L 196.269046 137.740288 \n", "L 196.604181 143.290765 \n", "L 196.939316 142.736867 \n", "L 197.274451 135.820019 \n", "L 197.609586 136.668284 \n", "L 197.944721 135.886942 \n", "L 198.279856 137.719609 \n", "L 198.614992 135.343547 \n", "L 199.285262 139.901506 \n", "L 199.620397 137.671649 \n", "L 199.955532 137.647908 \n", "L 200.290667 136.253762 \n", "L 200.960938 140.110998 \n", "L 201.296073 145.181302 \n", "L 201.631208 140.123408 \n", "L 202.301478 144.569792 \n", "L 202.636613 144.775633 \n", "L 202.971748 141.326305 \n", "L 203.306883 140.517306 \n", "L 203.642019 139.165938 \n", "L 204.312289 144.85397 \n", "L 204.647424 141.197208 \n", "L 204.982559 143.332036 \n", "L 205.317694 139.520525 \n", "L 205.652829 138.781312 \n", "L 205.987965 135.838021 \n", "L 206.658235 140.091297 \n", "L 206.99337 139.48668 \n", "L 207.66364 140.489066 \n", "L 207.998775 139.255394 \n", "L 208.33391 139.450418 \n", "L 208.669046 140.065933 \n", "L 209.004181 139.109892 \n", "L 209.674451 145.670873 \n", "L 210.344721 150.802695 \n", "L 210.679856 139.850758 \n", "L 211.014992 141.528939 \n", "L 211.350127 140.772347 \n", "L 211.685262 141.330607 \n", "L 212.020397 147.642309 \n", "L 212.355532 142.341982 \n", "L 212.690667 145.321398 \n", "L 213.025802 144.713972 \n", "L 213.360938 144.94899 \n", "L 213.696073 138.001028 \n", "L 214.031208 140.810386 \n", "L 214.366343 140.111918 \n", "L 214.701478 139.934184 \n", "L 215.036613 134.045891 \n", "L 215.371748 138.271785 \n", "L 215.706883 140.578295 \n", "L 216.042019 141.327699 \n", "L 216.377154 142.378511 \n", "L 216.712289 142.062143 \n", "L 217.047424 141.977297 \n", "L 217.717694 138.759079 \n", "L 218.052829 137.934346 \n", "L 218.387965 139.759139 \n", "L 219.058235 145.026427 \n", "L 219.39337 143.159672 \n", "L 219.728505 134.251441 \n", "L 220.06364 138.08595 \n", "L 220.398775 135.284045 \n", "L 220.73391 134.240793 \n", "L 221.069046 139.503328 \n", "L 221.404181 142.57716 \n", "L 221.739316 142.578516 \n", "L 222.074451 144.094499 \n", "L 222.409586 136.716169 \n", "L 222.744721 137.573942 \n", "L 223.079856 135.545786 \n", "L 223.414992 138.833582 \n", "L 223.750127 135.380439 \n", "L 224.085262 135.535522 \n", "L 224.420397 134.179987 \n", "L 224.755532 134.663273 \n", "L 225.425802 143.045674 \n", "L 225.760938 142.70787 \n", "L 226.096073 143.948872 \n", "L 226.431208 134.437468 \n", "L 226.766343 135.910114 \n", "L 227.101478 135.375588 \n", "L 227.436613 137.440755 \n", "L 228.106883 137.019222 \n", "L 228.442019 136.296759 \n", "L 228.777154 133.665418 \n", "L 229.112289 132.359864 \n", "L 229.447424 131.728509 \n", "L 229.782559 132.928711 \n", "L 230.117694 138.244323 \n", "L 230.452829 140.082497 \n", "L 230.787965 131.125373 \n", "L 231.1231 133.851319 \n", "L 231.458235 131.497461 \n", "L 231.79337 138.436751 \n", "L 232.128505 142.210044 \n", "L 232.46364 135.280612 \n", "L 232.798775 135.245143 \n", "L 233.469046 127.525447 \n", "L 233.804181 127.449817 \n", "L 234.139316 130.416947 \n", "L 234.474451 130.50161 \n", "L 234.809586 133.113027 \n", "L 235.144721 134.710382 \n", "L 235.479856 131.796182 \n", "L 235.814992 131.744533 \n", "L 236.150127 127.642889 \n", "L 236.485262 127.221409 \n", "L 237.155532 125.507303 \n", "L 237.490667 129.466912 \n", "L 237.825802 130.330062 \n", "L 238.160938 131.814355 \n", "L 238.496073 132.360779 \n", "L 238.831208 127.567677 \n", "L 239.166343 128.252801 \n", "L 239.501478 125.386515 \n", "L 239.836613 129.177326 \n", "L 240.171748 124.914765 \n", "L 240.506883 132.197944 \n", "L 240.842019 136.561222 \n", "L 241.177154 137.615094 \n", "L 241.512289 127.275419 \n", "L 241.847424 129.875129 \n", "L 242.182559 118.990867 \n", "L 242.852829 125.617403 \n", "L 243.187965 119.830154 \n", "L 243.5231 123.340383 \n", "L 243.858235 120.592651 \n", "L 244.19337 119.013199 \n", "L 244.528505 116.077384 \n", "L 244.86364 121.497955 \n", "L 245.198775 122.188699 \n", "L 245.53391 123.499669 \n", "L 245.869046 116.493259 \n", "L 246.204181 113.856074 \n", "L 246.539316 119.361666 \n", "L 246.874451 115.48854 \n", "L 247.209586 118.027792 \n", "L 247.544721 123.437454 \n", "L 247.879856 120.585777 \n", "L 248.214992 120.580825 \n", "L 248.550127 111.442719 \n", "L 248.885262 114.043285 \n", "L 249.220397 113.833321 \n", "L 249.890667 104.842681 \n", "L 250.225802 113.426688 \n", "L 250.560938 109.80106 \n", "L 250.896073 113.452815 \n", "L 251.231208 113.180136 \n", "L 251.566343 105.748184 \n", "L 251.901478 104.035595 \n", "L 252.236613 103.7974 \n", "L 252.571748 100.742824 \n", "L 252.906883 100.68058 \n", "L 253.577154 99.587165 \n", "L 253.912289 103.578012 \n", "L 254.247424 103.548878 \n", "L 254.917694 112.934139 \n", "L 255.252829 107.546713 \n", "L 255.587965 110.901383 \n", "L 255.9231 107.091059 \n", "L 256.59337 104.031441 \n", "L 256.928505 96.476742 \n", "L 257.598775 100.509428 \n", "L 257.93391 104.799169 \n", "L 258.269046 103.241828 \n", "L 258.604181 108.787219 \n", "L 258.939316 110.851363 \n", "L 259.274451 100.819627 \n", "L 259.609586 95.554398 \n", "L 259.944721 99.70492 \n", "L 260.279856 102.405595 \n", "L 260.614992 100.815979 \n", "L 260.950127 102.334009 \n", "L 261.285262 100.884267 \n", "L 261.620397 96.074715 \n", "L 261.955532 101.293811 \n", "L 262.290667 91.951016 \n", "L 262.625802 93.301872 \n", "L 262.960938 95.223911 \n", "L 263.296073 92.917271 \n", "L 263.631208 94.542206 \n", "L 263.966343 90.590196 \n", "L 264.301478 91.100039 \n", "L 264.636613 88.464074 \n", "L 264.971748 87.995444 \n", "L 265.306883 95.595726 \n", "L 265.642019 95.896754 \n", "L 265.977154 97.50474 \n", "L 266.312289 97.794453 \n", "L 266.647424 92.199772 \n", "L 266.982559 90.415151 \n", "L 267.317694 83.532643 \n", "L 267.652829 82.195839 \n", "L 267.987965 87.337593 \n", "L 268.658235 84.56 \n", "L 268.99337 91.074536 \n", "L 269.66364 67.69931 \n", "L 269.998775 83.123909 \n", "L 270.33391 83.095406 \n", "L 270.669046 81.164178 \n", "L 271.004181 90.419032 \n", "L 271.339316 84.943771 \n", "L 271.674451 82.382104 \n", "L 272.009586 78.732454 \n", "L 272.344721 77.244112 \n", "L 272.679856 81.006963 \n", "L 273.014992 74.004648 \n", "L 273.350127 81.472106 \n", "L 273.685262 77.672819 \n", "L 274.020397 82.592369 \n", "L 274.355532 84.345009 \n", "L 274.690667 81.150621 \n", "L 275.025802 84.058136 \n", "L 275.360937 83.746169 \n", "L 275.696073 76.399539 \n", "L 276.031208 77.161009 \n", "L 276.366343 80.344549 \n", "L 276.701478 75.722048 \n", "L 277.036613 79.170559 \n", "L 277.371748 75.47037 \n", "L 277.706883 63.588331 \n", "L 278.042019 77.892173 \n", "L 278.377154 70.556912 \n", "L 278.712289 74.551102 \n", "L 279.047424 74.132903 \n", "L 279.382559 65.281915 \n", "L 279.717694 71.948096 \n", "L 280.052829 67.634216 \n", "L 280.387965 66.06594 \n", "L 280.7231 70.972494 \n", "L 281.058235 68.281894 \n", "L 281.39337 62.310082 \n", "L 281.728505 68.824259 \n", "L 282.06364 66.367777 \n", "L 282.398775 56.976181 \n", "L 282.73391 70.016148 \n", "L 283.069046 63.307949 \n", "L 283.404181 59.811516 \n", "L 283.739316 59.325799 \n", "L 284.074451 61.912601 \n", "L 284.409586 67.167076 \n", "L 284.744721 64.084978 \n", "L 285.079856 69.842541 \n", "L 285.414992 59.514762 \n", "L 285.750127 59.829819 \n", "L 286.085262 68.284406 \n", "L 286.420397 59.879752 \n", "L 286.755532 56.856802 \n", "L 287.090667 66.240285 \n", "L 287.425802 56.694818 \n", "L 287.760938 64.588557 \n", "L 288.096073 62.278497 \n", "L 288.431208 69.928325 \n", "L 288.766343 69.056712 \n", "L 289.101478 56.7719 \n", "L 289.436613 67.364319 \n", "L 289.771748 60.872818 \n", "L 290.106883 60.543064 \n", "L 290.442019 60.739163 \n", "L 290.777154 62.368075 \n", "L 291.112289 67.992346 \n", "L 291.447424 58.296134 \n", "L 291.782559 56.526275 \n", "L 292.117694 59.414763 \n", "L 292.452829 50.172413 \n", "L 292.787965 52.213999 \n", "L 293.1231 60.815903 \n", "L 293.458235 50.878557 \n", "L 293.79337 52.047987 \n", "L 294.128505 57.330143 \n", "L 294.46364 57.055834 \n", "L 294.798775 58.836532 \n", "L 295.13391 41.810073 \n", "L 295.804181 53.686773 \n", "L 296.139316 46.245519 \n", "L 296.474451 55.982476 \n", "L 296.809586 46.277043 \n", "L 297.144721 39.998849 \n", "L 298.150127 52.606749 \n", "L 298.485262 54.018452 \n", "L 298.820397 48.309454 \n", "L 299.155532 40.337119 \n", "L 299.490667 50.979092 \n", "L 299.825802 56.739197 \n", "L 300.160938 47.348667 \n", "L 300.496073 48.055174 \n", "L 300.831208 51.831475 \n", "L 301.166343 50.726029 \n", "L 301.501478 43.639048 \n", "L 301.836613 45.452211 \n", "L 302.171748 63.519166 \n", "L 302.506883 57.508421 \n", "L 302.842019 34.414778 \n", "L 303.177154 48.679311 \n", "L 303.512289 57.881767 \n", "L 303.847424 51.481887 \n", "L 304.182559 47.360447 \n", "L 304.517694 62.057455 \n", "L 304.852829 54.548037 \n", "L 305.187965 50.662751 \n", "L 305.5231 59.560796 \n", "L 305.858235 43.261912 \n", "L 306.19337 55.370479 \n", "L 306.528505 54.911906 \n", "L 306.86364 43.499114 \n", "L 307.869046 55.97166 \n", "L 308.204181 50.611146 \n", "L 308.539316 48.715698 \n", "L 308.874451 61.243489 \n", "L 309.209586 57.639842 \n", "L 309.544721 39.16854 \n", "L 309.879856 46.903847 \n", "L 310.214992 46.285018 \n", "L 310.550127 40.431872 \n", "L 310.885262 52.167151 \n", "L 311.220397 46.128826 \n", "L 311.890667 58.489573 \n", "L 312.225802 44.056445 \n", "L 312.560938 48.222862 \n", "L 312.896073 61.516641 \n", "L 313.231208 59.31685 \n", "L 313.566343 42.29538 \n", "L 313.901478 52.78268 \n", "L 314.236613 50.065096 \n", "L 314.571748 51.138855 \n", "L 314.906883 52.594728 \n", "L 315.242019 46.447531 \n", "L 315.577154 51.223233 \n", "L 315.912289 57.807809 \n", "L 316.247424 53.345041 \n", "L 316.582559 45.882155 \n", "L 316.917694 58.356825 \n", "L 317.252829 51.79745 \n", "L 317.587965 42.340793 \n", "L 317.9231 49.166402 \n", "L 318.258235 50.269031 \n", "L 318.59337 42.014833 \n", "L 318.928505 41.847865 \n", "L 319.26364 56.444662 \n", "L 319.598775 59.892644 \n", "L 319.93391 42.505908 \n", "L 320.269046 43.876935 \n", "L 320.604181 43.790524 \n", "L 320.939316 44.48238 \n", "L 321.274451 44.687082 \n", "L 321.609586 45.653085 \n", "L 321.944721 39.994301 \n", "L 322.279856 40.284857 \n", "L 322.614992 38.673927 \n", "L 323.285262 55.173465 \n", "L 323.620397 33.36119 \n", "L 323.955532 43.186188 \n", "L 324.290667 47.06069 \n", "L 324.625802 47.336097 \n", "L 324.960938 47.337079 \n", "L 325.296073 39.398377 \n", "L 325.631208 47.424832 \n", "L 326.301478 56.508855 \n", "L 326.971748 43.497609 \n", "L 327.306883 48.865298 \n", "L 327.642019 42.811492 \n", "L 327.977154 44.416526 \n", "L 328.312289 51.415388 \n", "L 328.647424 53.238979 \n", "L 329.317694 48.188846 \n", "L 329.652829 49.882116 \n", "L 329.987965 44.783915 \n", "L 330.3231 52.946221 \n", "L 330.658235 58.244767 \n", "L 330.99337 46.319883 \n", "L 331.328505 52.587679 \n", "L 331.66364 53.405952 \n", "L 331.998775 57.590775 \n", "L 332.33391 55.751957 \n", "L 333.004181 46.658861 \n", "L 333.674451 55.962157 \n", "L 334.344721 44.466894 \n", "L 334.679856 54.76217 \n", "L 335.014992 53.187687 \n", "L 335.350127 43.439277 \n", "L 335.685262 46.665466 \n", "L 336.020397 54.598829 \n", "L 336.355532 55.856753 \n", "L 336.690667 45.496236 \n", "L 337.025802 59.459561 \n", "L 337.360937 58.611866 \n", "L 337.696073 55.222247 \n", "L 338.031208 62.044394 \n", "L 338.701478 47.173416 \n", "L 339.036613 53.759935 \n", "L 339.371748 50.351931 \n", "L 339.706883 53.625746 \n", "L 340.042019 60.435752 \n", "L 340.377154 61.867005 \n", "L 340.712289 60.590104 \n", "L 341.047424 55.11274 \n", "L 341.382559 59.094475 \n", "L 342.052829 49.58725 \n", "L 342.387965 56.090171 \n", "L 342.7231 54.927366 \n", "L 343.058235 49.231403 \n", "L 343.39337 51.673778 \n", "L 343.728505 70.199997 \n", "L 344.06364 56.799051 \n", "L 344.398775 57.671299 \n", "L 344.73391 78.813687 \n", "L 345.069046 65.45184 \n", "L 345.404181 62.699946 \n", "L 345.739316 69.069294 \n", "L 346.074451 61.03542 \n", "L 346.409586 57.129362 \n", "L 346.744721 67.458236 \n", "L 347.079856 66.769742 \n", "L 347.414992 68.05416 \n", "L 347.750127 72.652168 \n", "L 348.085262 78.994038 \n", "L 348.420397 75.545208 \n", "L 348.755532 76.107479 \n", "L 349.090667 63.352785 \n", "L 349.425802 66.611894 \n", "L 349.760938 71.793506 \n", "L 350.096073 72.688735 \n", "L 350.431208 79.829747 \n", "L 350.766343 73.157876 \n", "L 351.101478 69.059715 \n", "L 351.436613 69.62706 \n", "L 351.771748 71.115111 \n", "L 352.106883 71.885965 \n", "L 352.442019 74.428205 \n", "L 352.777154 65.378236 \n", "L 353.112289 59.464768 \n", "L 353.782559 71.924233 \n", "L 354.117694 66.234334 \n", "L 354.452829 74.197517 \n", "L 354.787965 77.583462 \n", "L 355.1231 79.649816 \n", "L 355.458235 74.657856 \n", "L 355.79337 83.713968 \n", "L 356.128505 86.516233 \n", "L 356.46364 70.925161 \n", "L 356.798775 81.228194 \n", "L 357.469046 81.94577 \n", "L 358.139316 89.139537 \n", "L 358.474451 79.110658 \n", "L 358.809586 82.343381 \n", "L 359.479856 77.226906 \n", "L 359.814992 77.90116 \n", "L 360.150127 80.454582 \n", "L 360.485262 84.274344 \n", "L 360.820397 78.472687 \n", "L 361.155532 67.219454 \n", "L 361.490667 77.609741 \n", "L 361.825802 82.831678 \n", "L 362.160938 75.705414 \n", "L 362.496073 83.278349 \n", "L 362.831208 81.556702 \n", "L 363.166343 81.231255 \n", "L 363.501478 85.770944 \n", "L 363.836613 82.788066 \n", "L 364.171748 82.178109 \n", "L 364.506883 86.270072 \n", "L 364.842019 84.652331 \n", "L 365.177154 96.810502 \n", "L 365.512289 96.512325 \n", "L 365.847424 98.796497 \n", "L 366.182559 102.397881 \n", "L 366.517694 93.019815 \n", "L 366.852829 90.490779 \n", "L 367.187965 95.41331 \n", "L 367.5231 95.342379 \n", "L 367.858235 95.923404 \n", "L 368.19337 92.852275 \n", "L 368.528505 98.370936 \n", "L 368.86364 101.507627 \n", "L 369.198775 95.856534 \n", "L 369.53391 97.437429 \n", "L 369.869046 91.950745 \n", "L 370.204181 93.244072 \n", "L 370.539316 94.984594 \n", "L 370.874451 97.660045 \n", "L 371.209586 105.817661 \n", "L 371.544721 104.841402 \n", "L 371.879856 99.822879 \n", "L 372.214992 105.450766 \n", "L 372.550127 102.59319 \n", "L 372.885262 103.061544 \n", "L 373.220397 108.337212 \n", "L 373.555532 104.924014 \n", "L 373.890667 105.971037 \n", "L 374.225802 99.670258 \n", "L 374.560938 109.861931 \n", "L 374.896073 114.519026 \n", "L 375.231208 109.408952 \n", "L 375.566343 120.264213 \n", "L 375.901478 115.362007 \n", "L 376.236613 108.624338 \n", "L 376.571748 113.830015 \n", "L 376.906883 113.045418 \n", "L 377.242019 112.651794 \n", "L 377.577154 114.591601 \n", "L 377.912289 120.711014 \n", "L 378.247424 113.708943 \n", "L 378.582559 110.246768 \n", "L 378.917694 109.535148 \n", "L 379.252829 111.531864 \n", "L 379.587965 110.646149 \n", "L 379.9231 108.008951 \n", "L 380.258235 115.392518 \n", "L 380.59337 113.185448 \n", "L 380.928505 99.971901 \n", "L 381.26364 99.430814 \n", "L 381.93391 109.922637 \n", "L 382.269046 111.158016 \n", "L 382.604181 120.016137 \n", "L 382.939316 120.183712 \n", "L 383.609586 111.170638 \n", "L 383.944721 115.842845 \n", "L 384.279856 118.642618 \n", "L 384.614992 116.700697 \n", "L 384.950127 126.380101 \n", "L 385.285262 125.072596 \n", "L 385.620397 118.929828 \n", "L 385.955532 117.423616 \n", "L 386.290667 113.092496 \n", "L 386.625802 117.783944 \n", "L 386.960938 117.949117 \n", "L 386.960938 117.949117 \n", "\" clip-path=\"url(#pda9f443d7f)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #bf00bf; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_27\">\n", "    <path d=\"M 254.582559 107.953242 \n", "L 254.917694 112.795489 \n", "L 255.252829 109.757727 \n", "L 255.587965 110.035567 \n", "L 255.9231 111.012174 \n", "L 256.258235 112.6722 \n", "L 256.59337 111.871173 \n", "L 256.928505 112.248498 \n", "L 257.598775 114.056593 \n", "L 257.93391 114.115509 \n", "L 258.269046 114.682241 \n", "L 258.939316 116.242083 \n", "L 259.274451 116.712535 \n", "L 260.614992 119.777334 \n", "L 261.955532 123.421169 \n", "L 263.296073 127.769524 \n", "L 263.631208 128.988907 \n", "L 264.301478 130.460173 \n", "L 264.971748 131.486611 \n", "L 265.642019 132.165877 \n", "L 266.647424 132.791525 \n", "L 267.652829 133.134508 \n", "L 268.99337 133.363642 \n", "L 271.339316 133.50407 \n", "L 277.371748 133.54853 \n", "L 386.960938 133.549767 \n", "L 386.960938 133.549767 \n", "\" clip-path=\"url(#pda9f443d7f)\" style=\"fill: none; stroke-dasharray: 9.6,2.4,1.5,2.4; stroke-dashoffset: 0; stroke: #008000; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"patch_3\">\n", "    <path d=\"M 52.**********.52 \n", "L 52.160938 7.2 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_4\">\n", "    <path d=\"M 386.**********.52 \n", "L 386.960938 7.2 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_5\">\n", "    <path d=\"M 52.**********.52 \n", "L 386.**********.52 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_6\">\n", "    <path d=\"M 52.160938 7.2 \n", "L 386.960938 7.2 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"legend_1\">\n", "    <g id=\"patch_7\">\n", "     <path d=\"M 164.40625 59.234375 \n", "L 274.715625 59.234375 \n", "Q 276.715625 59.234375 276.715625 57.234375 \n", "L 276.715625 14.2 \n", "Q 276.715625 12.2 274.715625 12.2 \n", "L 164.40625 12.2 \n", "Q 162.40625 12.2 162.40625 14.2 \n", "L 162.40625 57.234375 \n", "Q 162.40625 59.234375 164.40625 59.234375 \n", "z\n", "\" style=\"fill: #ffffff; opacity: 0.8; stroke: #cccccc; stroke-linejoin: miter\"/>\n", "    </g>\n", "    <g id=\"line2d_28\">\n", "     <path d=\"M 166.40625 20.298438 \n", "L 176.40625 20.298438 \n", "L 186.40625 20.298438 \n", "\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "    </g>\n", "    <g id=\"text_15\">\n", "     <!-- data -->\n", "     <g transform=\"translate(194.40625 23.798438)scale(0.1 -0.1)\">\n", "      <defs>\n", "       <path id=\"DejaVuSans-64\" d=\"M 2906 2969 \n", "L 2906 4863 \n", "L 3481 4863 \n", "L 3481 0 \n", "L 2906 0 \n", "L 2906 525 \n", "Q 2725 213 2448 61 \n", "Q 2172 -91 1784 -91 \n", "Q 1150 -91 751 415 \n", "Q 353 922 353 1747 \n", "Q 353 2572 751 3078 \n", "Q 1150 3584 1784 3584 \n", "Q 2172 3584 2448 3432 \n", "Q 2725 3281 2906 2969 \n", "z\n", "M 947 1747 \n", "Q 947 1113 1208 752 \n", "Q 1469 391 1925 391 \n", "Q 2381 391 2643 752 \n", "Q 2906 1113 2906 1747 \n", "Q 2906 2381 2643 2742 \n", "Q 2381 3103 1925 3103 \n", "Q 1469 3103 1208 2742 \n", "Q 947 2381 947 1747 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-61\" d=\"M 2194 1759 \n", "Q 1497 1759 1228 1600 \n", "Q 959 1441 959 1056 \n", "Q 959 750 1161 570 \n", "Q 1363 391 1709 391 \n", "Q 2188 391 2477 730 \n", "Q 2766 1069 2766 1631 \n", "L 2766 1759 \n", "L 2194 1759 \n", "z\n", "M 3341 1997 \n", "L 3341 0 \n", "L 2766 0 \n", "L 2766 531 \n", "Q 2569 213 2275 61 \n", "Q 1981 -91 1556 -91 \n", "Q 1019 -91 701 211 \n", "Q 384 513 384 1019 \n", "Q 384 1609 779 1909 \n", "Q 1175 2209 1959 2209 \n", "L 2766 2209 \n", "L 2766 2266 \n", "Q 2766 2663 2505 2880 \n", "Q 2244 3097 1772 3097 \n", "Q 1472 3097 1187 3025 \n", "Q 903 2953 641 2809 \n", "L 641 3341 \n", "Q 956 3463 1253 3523 \n", "Q 1550 3584 1831 3584 \n", "Q 2591 3584 2966 3190 \n", "Q 3341 2797 3341 1997 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "      </defs>\n", "      <use xlink:href=\"#DejaVuSans-64\"/>\n", "      <use xlink:href=\"#DejaVuSans-61\" x=\"63.476562\"/>\n", "      <use xlink:href=\"#DejaVuSans-74\" x=\"124.755859\"/>\n", "      <use xlink:href=\"#DejaVuSans-61\" x=\"163.964844\"/>\n", "     </g>\n", "    </g>\n", "    <g id=\"line2d_29\">\n", "     <path d=\"M 166.40625 34.976563 \n", "L 176.40625 34.976563 \n", "L 186.40625 34.976563 \n", "\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #bf00bf; stroke-width: 1.5\"/>\n", "    </g>\n", "    <g id=\"text_16\">\n", "     <!-- 1-step preds -->\n", "     <g transform=\"translate(194.40625 38.476563)scale(0.1 -0.1)\">\n", "      <defs>\n", "       <path id=\"DejaVuSans-2d\" d=\"M 313 2009 \n", "L 1997 2009 \n", "L 1997 1497 \n", "L 313 1497 \n", "L 313 2009 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-73\" d=\"M 2834 3397 \n", "L 2834 2853 \n", "Q 2591 2978 2328 3040 \n", "Q 2066 3103 1784 3103 \n", "Q 1356 3103 1142 2972 \n", "Q 928 2841 928 2578 \n", "Q 928 2378 1081 2264 \n", "Q 1234 2150 1697 2047 \n", "L 1894 2003 \n", "Q 2506 1872 2764 1633 \n", "Q 3022 1394 3022 966 \n", "Q 3022 478 2636 193 \n", "Q 2250 -91 1575 -91 \n", "Q 1294 -91 989 -36 \n", "Q 684 19 347 128 \n", "L 347 722 \n", "Q 666 556 975 473 \n", "Q 1284 391 1588 391 \n", "Q 1994 391 2212 530 \n", "Q 2431 669 2431 922 \n", "Q 2431 1156 2273 1281 \n", "Q 2116 1406 1581 1522 \n", "L 1381 1569 \n", "Q 847 1681 609 1914 \n", "Q 372 2147 372 2553 \n", "Q 372 3047 722 3315 \n", "Q 1072 3584 1716 3584 \n", "Q 2034 3584 2315 3537 \n", "Q 2597 3491 2834 3397 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-70\" d=\"M 1159 525 \n", "L 1159 -1331 \n", "L 581 -1331 \n", "L 581 3500 \n", "L 1159 3500 \n", "L 1159 2969 \n", "Q 1341 3281 1617 3432 \n", "Q 1894 3584 2278 3584 \n", "Q 2916 3584 3314 3078 \n", "Q 3713 2572 3713 1747 \n", "Q 3713 922 3314 415 \n", "Q 2916 -91 2278 -91 \n", "Q 1894 -91 1617 61 \n", "Q 1341 213 1159 525 \n", "z\n", "M 3116 1747 \n", "Q 3116 2381 2855 2742 \n", "Q 2594 3103 2138 3103 \n", "Q 1681 3103 1420 2742 \n", "Q 1159 2381 1159 1747 \n", "Q 1159 1113 1420 752 \n", "Q 1681 391 2138 391 \n", "Q 2594 391 2855 752 \n", "Q 3116 1113 3116 1747 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-20\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-72\" d=\"M 2631 2963 \n", "Q 2534 3019 2420 3045 \n", "Q 2306 3072 2169 3072 \n", "Q 1681 3072 1420 2755 \n", "Q 1159 2438 1159 1844 \n", "L 1159 0 \n", "L 581 0 \n", "L 581 3500 \n", "L 1159 3500 \n", "L 1159 2956 \n", "Q 1341 3275 1631 3429 \n", "Q 1922 3584 2338 3584 \n", "Q 2397 3584 2469 3576 \n", "Q 2541 3569 2628 3553 \n", "L 2631 2963 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "      </defs>\n", "      <use xlink:href=\"#DejaVuSans-31\"/>\n", "      <use xlink:href=\"#DejaVuSans-2d\" x=\"63.623047\"/>\n", "      <use xlink:href=\"#DejaVuSans-73\" x=\"99.707031\"/>\n", "      <use xlink:href=\"#DejaVuSans-74\" x=\"151.806641\"/>\n", "      <use xlink:href=\"#DejaVuSans-65\" x=\"191.015625\"/>\n", "      <use xlink:href=\"#DejaVuSans-70\" x=\"252.539062\"/>\n", "      <use xlink:href=\"#DejaVuSans-20\" x=\"316.015625\"/>\n", "      <use xlink:href=\"#DejaVuSans-70\" x=\"347.802734\"/>\n", "      <use xlink:href=\"#DejaVuSans-72\" x=\"411.279297\"/>\n", "      <use xlink:href=\"#DejaVuSans-65\" x=\"450.142578\"/>\n", "      <use xlink:href=\"#DejaVuSans-64\" x=\"511.666016\"/>\n", "      <use xlink:href=\"#DejaVuSans-73\" x=\"575.142578\"/>\n", "     </g>\n", "    </g>\n", "    <g id=\"line2d_30\">\n", "     <path d=\"M 166.40625 49.654688 \n", "L 176.40625 49.654688 \n", "L 186.40625 49.654688 \n", "\" style=\"fill: none; stroke-dasharray: 9.6,2.4,1.5,2.4; stroke-dashoffset: 0; stroke: #008000; stroke-width: 1.5\"/>\n", "    </g>\n", "    <g id=\"text_17\">\n", "     <!-- multistep preds -->\n", "     <g transform=\"translate(194.40625 53.154688)scale(0.1 -0.1)\">\n", "      <defs>\n", "       <path id=\"DejaVuSans-75\" d=\"M 544 1381 \n", "L 544 3500 \n", "L 1119 3500 \n", "L 1119 1403 \n", "Q 1119 906 1312 657 \n", "Q 1506 409 1894 409 \n", "Q 2359 409 2629 706 \n", "Q 2900 1003 2900 1516 \n", "L 2900 3500 \n", "L 3475 3500 \n", "L 3475 0 \n", "L 2900 0 \n", "L 2900 538 \n", "Q 2691 219 2414 64 \n", "Q 2138 -91 1772 -91 \n", "Q 1169 -91 856 284 \n", "Q 544 659 544 1381 \n", "z\n", "M 1991 3584 \n", "L 1991 3584 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-6c\" d=\"M 603 4863 \n", "L 1178 4863 \n", "L 1178 0 \n", "L 603 0 \n", "L 603 4863 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "      </defs>\n", "      <use xlink:href=\"#DejaVuSans-6d\"/>\n", "      <use xlink:href=\"#DejaVuSans-75\" x=\"97.412109\"/>\n", "      <use xlink:href=\"#DejaVuSans-6c\" x=\"160.791016\"/>\n", "      <use xlink:href=\"#DejaVuSans-74\" x=\"188.574219\"/>\n", "      <use xlink:href=\"#DejaVuSans-69\" x=\"227.783203\"/>\n", "      <use xlink:href=\"#DejaVuSans-73\" x=\"255.566406\"/>\n", "      <use xlink:href=\"#DejaVuSans-74\" x=\"307.666016\"/>\n", "      <use xlink:href=\"#DejaVuSans-65\" x=\"346.875\"/>\n", "      <use xlink:href=\"#DejaVuSans-70\" x=\"408.398438\"/>\n", "      <use xlink:href=\"#DejaVuSans-20\" x=\"471.875\"/>\n", "      <use xlink:href=\"#DejaVuSans-70\" x=\"503.662109\"/>\n", "      <use xlink:href=\"#DejaVuSans-72\" x=\"567.138672\"/>\n", "      <use xlink:href=\"#DejaVuSans-65\" x=\"606.001953\"/>\n", "      <use xlink:href=\"#DejaVuSans-64\" x=\"667.525391\"/>\n", "      <use xlink:href=\"#DejaVuSans-73\" x=\"731.001953\"/>\n", "     </g>\n", "    </g>\n", "   </g>\n", "  </g>\n", " </g>\n", " <defs>\n", "  <clipPath id=\"pda9f443d7f\">\n", "   <rect x=\"52.160938\" y=\"7.2\" width=\"334.8\" height=\"166.32\"/>\n", "  </clipPath>\n", " </defs>\n", "</svg>\n"], "text/plain": ["<Figure size 600x300 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["d2l.plot([time, time[tau:], time[n_train + tau:]],\n", "         [x.detach().numpy(), onestep_preds.detach().numpy(),\n", "          multistep_preds[n_train + tau:].detach().numpy()], 'time',\n", "         'x', legend=['data', '1-step preds', 'multistep preds'],\n", "         xlim=[1, 1000], figsize=(6, 3))"]}, {"cell_type": "markdown", "id": "630843d7", "metadata": {"origin_pos": 28}, "source": ["如上面的例子所示，绿线的预测显然并不理想。\n", "经过几个预测步骤之后，预测的结果很快就会衰减到一个常数。\n", "为什么这个算法效果这么差呢？事实是由于错误的累积：\n", "假设在步骤$1$之后，我们积累了一些错误$\\epsilon_1 = \\bar\\epsilon$。\n", "于是，步骤$2$的输入被扰动了$\\epsilon_1$，\n", "结果积累的误差是依照次序的$\\epsilon_2 = \\bar\\epsilon + c \\epsilon_1$，\n", "其中$c$为某个常数，后面的预测误差依此类推。\n", "因此误差可能会相当快地偏离真实的观测结果。\n", "例如，未来$24$小时的天气预报往往相当准确，\n", "但超过这一点，精度就会迅速下降。\n", "我们将在本章及后续章节中讨论如何改进这一点。\n", "\n", "基于$k = 1, 4, 16, 64$，通过对整个序列预测的计算，\n", "让我们[**更仔细地看一下$k$步预测**]的困难。\n"]}, {"cell_type": "code", "execution_count": 10, "id": "528b9245", "metadata": {"execution": {"iopub.execute_input": "2022-12-07T16:51:38.129204Z", "iopub.status.busy": "2022-12-07T16:51:38.128639Z", "iopub.status.idle": "2022-12-07T16:51:38.133239Z", "shell.execute_reply": "2022-12-07T16:51:38.132409Z"}, "origin_pos": 29, "tab": ["pytorch"]}, "outputs": [], "source": ["max_steps = 64"]}, {"cell_type": "code", "execution_count": 11, "id": "e5278e7c", "metadata": {"execution": {"iopub.execute_input": "2022-12-07T16:51:38.137203Z", "iopub.status.busy": "2022-12-07T16:51:38.136681Z", "iopub.status.idle": "2022-12-07T16:51:38.168627Z", "shell.execute_reply": "2022-12-07T16:51:38.167468Z"}, "origin_pos": 30, "tab": ["pytorch"]}, "outputs": [], "source": ["features = torch.zeros((T - tau - max_steps + 1, tau + max_steps))\n", "# 列i（i<tau）是来自x的观测，其时间步从（i）到（i+T-tau-max_steps+1）\n", "for i in range(tau):\n", "    features[:, i] = x[i: i + T - tau - max_steps + 1]\n", "\n", "# 列i（i>=tau）是来自（i-tau+1）步的预测，其时间步从（i）到（i+T-tau-max_steps+1）\n", "for i in range(tau, tau + max_steps):\n", "    features[:, i] = net(features[:, i - tau:i]).reshape(-1)"]}, {"cell_type": "code", "execution_count": 12, "id": "d428c9c1", "metadata": {"execution": {"iopub.execute_input": "2022-12-07T16:51:38.173044Z", "iopub.status.busy": "2022-12-07T16:51:38.172309Z", "iopub.status.idle": "2022-12-07T16:51:38.546231Z", "shell.execute_reply": "2022-12-07T16:51:38.545435Z"}, "origin_pos": 33, "tab": ["pytorch"]}, "outputs": [{"data": {"image/svg+xml": ["<?xml version=\"1.0\" encoding=\"utf-8\" standalone=\"no\"?>\n", "<!DOCTYPE svg PUBLIC \"-//W3C//DTD SVG 1.1//EN\"\n", "  \"http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd\">\n", "<svg xmlns:xlink=\"http://www.w3.org/1999/xlink\" width=\"406.885938pt\" height=\"211.07625pt\" viewBox=\"0 0 406.**********.07625\" xmlns=\"http://www.w3.org/2000/svg\" version=\"1.1\">\n", " <metadata>\n", "  <rdf:RDF xmlns:dc=\"http://purl.org/dc/elements/1.1/\" xmlns:cc=\"http://creativecommons.org/ns#\" xmlns:rdf=\"http://www.w3.org/1999/02/22-rdf-syntax-ns#\">\n", "   <cc:Work>\n", "    <dc:type rdf:resource=\"http://purl.org/dc/dcmitype/StillImage\"/>\n", "    <dc:date>2022-12-07T16:51:38.492716</dc:date>\n", "    <dc:format>image/svg+xml</dc:format>\n", "    <dc:creator>\n", "     <cc:Agent>\n", "      <dc:title>Matplotlib v3.5.1, https://matplotlib.org/</dc:title>\n", "     </cc:Agent>\n", "    </dc:creator>\n", "   </cc:Work>\n", "  </rdf:RDF>\n", " </metadata>\n", " <defs>\n", "  <style type=\"text/css\">*{stroke-linejoin: round; stroke-linecap: butt}</style>\n", " </defs>\n", " <g id=\"figure_1\">\n", "  <g id=\"patch_1\">\n", "   <path d=\"M 0 211.07625 \n", "L 406.**********.07625 \n", "L 406.885938 0 \n", "L 0 0 \n", "z\n", "\" style=\"fill: #ffffff\"/>\n", "  </g>\n", "  <g id=\"axes_1\">\n", "   <g id=\"patch_2\">\n", "    <path d=\"M 52.**********.52 \n", "L 386.**********.52 \n", "L 386.960938 7.2 \n", "L 52.160938 7.2 \n", "z\n", "\" style=\"fill: #ffffff\"/>\n", "   </g>\n", "   <g id=\"matplotlib.axis_1\">\n", "    <g id=\"xtick_1\">\n", "     <g id=\"line2d_1\">\n", "      <path d=\"M 117.775008 173.52 \n", "L 117.775008 7.2 \n", "\" clip-path=\"url(#p5912583a97)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_2\">\n", "      <defs>\n", "       <path id=\"m22ef0b31fe\" d=\"M 0 0 \n", "L 0 3.5 \n", "\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </defs>\n", "      <g>\n", "       <use xlink:href=\"#m22ef0b31fe\" x=\"117.775008\" y=\"173.52\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_1\">\n", "      <!-- 200 -->\n", "      <g transform=\"translate(108.231258 188.118438)scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-32\" d=\"M 1228 531 \n", "L 3431 531 \n", "L 3431 0 \n", "L 469 0 \n", "L 469 531 \n", "Q 828 903 1448 1529 \n", "Q 2069 2156 2228 2338 \n", "Q 2531 2678 2651 2914 \n", "Q 2772 3150 2772 3378 \n", "Q 2772 3750 2511 3984 \n", "Q 2250 4219 1831 4219 \n", "Q 1534 4219 1204 4116 \n", "Q 875 4013 500 3803 \n", "L 500 4441 \n", "Q 881 4594 1212 4672 \n", "Q 1544 4750 1819 4750 \n", "Q 2544 4750 2975 4387 \n", "Q 3406 4025 3406 3419 \n", "Q 3406 3131 3298 2873 \n", "Q 3191 2616 2906 2266 \n", "Q 2828 2175 2409 1742 \n", "Q 1991 1309 1228 531 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "        <path id=\"DejaVuSans-30\" d=\"M 2034 4250 \n", "Q 1547 4250 1301 3770 \n", "Q 1056 3291 1056 2328 \n", "Q 1056 1369 1301 889 \n", "Q 1547 409 2034 409 \n", "Q 2525 409 2770 889 \n", "Q 3016 1369 3016 2328 \n", "Q 3016 3291 2770 3770 \n", "Q 2525 4250 2034 4250 \n", "z\n", "M 2034 4750 \n", "Q 2819 4750 3233 4129 \n", "Q 3647 3509 3647 2328 \n", "Q 3647 1150 3233 529 \n", "Q 2819 -91 2034 -91 \n", "Q 1250 -91 836 529 \n", "Q 422 1150 422 2328 \n", "Q 422 3509 836 4129 \n", "Q 1250 4750 2034 4750 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-32\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"127.246094\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_2\">\n", "     <g id=\"line2d_3\">\n", "      <path d=\"M 185.07149 173.52 \n", "L 185.07149 7.2 \n", "\" clip-path=\"url(#p5912583a97)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_4\">\n", "      <g>\n", "       <use xlink:href=\"#m22ef0b31fe\" x=\"185.07149\" y=\"173.52\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_2\">\n", "      <!-- 400 -->\n", "      <g transform=\"translate(175.52774 188.118438)scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-34\" d=\"M 2419 4116 \n", "L 825 1625 \n", "L 2419 1625 \n", "L 2419 4116 \n", "z\n", "M 2253 4666 \n", "L 3047 4666 \n", "L 3047 1625 \n", "L 3713 1625 \n", "L 3713 1100 \n", "L 3047 1100 \n", "L 3047 0 \n", "L 2419 0 \n", "L 2419 1100 \n", "L 313 1100 \n", "L 313 1709 \n", "L 2253 4666 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-34\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"127.246094\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_3\">\n", "     <g id=\"line2d_5\">\n", "      <path d=\"M 252.367973 173.52 \n", "L 252.367973 7.2 \n", "\" clip-path=\"url(#p5912583a97)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_6\">\n", "      <g>\n", "       <use xlink:href=\"#m22ef0b31fe\" x=\"252.367973\" y=\"173.52\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_3\">\n", "      <!-- 600 -->\n", "      <g transform=\"translate(242.824223 188.118438)scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-36\" d=\"M 2113 2584 \n", "Q 1688 2584 1439 2293 \n", "Q 1191 2003 1191 1497 \n", "Q 1191 994 1439 701 \n", "Q 1688 409 2113 409 \n", "Q 2538 409 2786 701 \n", "Q 3034 994 3034 1497 \n", "Q 3034 2003 2786 2293 \n", "Q 2538 2584 2113 2584 \n", "z\n", "M 3366 4563 \n", "L 3366 3988 \n", "Q 3128 4100 2886 4159 \n", "Q 2644 4219 2406 4219 \n", "Q 1781 4219 1451 3797 \n", "Q 1122 3375 1075 2522 \n", "Q 1259 2794 1537 2939 \n", "Q 1816 3084 2150 3084 \n", "Q 2853 3084 3261 2657 \n", "Q 3669 2231 3669 1497 \n", "Q 3669 778 3244 343 \n", "Q 2819 -91 2113 -91 \n", "Q 1303 -91 875 529 \n", "Q 447 1150 447 2328 \n", "Q 447 3434 972 4092 \n", "Q 1497 4750 2381 4750 \n", "Q 2619 4750 2861 4703 \n", "Q 3103 4656 3366 4563 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-36\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"127.246094\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_4\">\n", "     <g id=\"line2d_7\">\n", "      <path d=\"M 319.664455 173.52 \n", "L 319.664455 7.2 \n", "\" clip-path=\"url(#p5912583a97)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_8\">\n", "      <g>\n", "       <use xlink:href=\"#m22ef0b31fe\" x=\"319.664455\" y=\"173.52\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_4\">\n", "      <!-- 800 -->\n", "      <g transform=\"translate(310.120705 188.118438)scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-38\" d=\"M 2034 2216 \n", "Q 1584 2216 1326 1975 \n", "Q 1069 1734 1069 1313 \n", "Q 1069 891 1326 650 \n", "Q 1584 409 2034 409 \n", "Q 2484 409 2743 651 \n", "Q 3003 894 3003 1313 \n", "Q 3003 1734 2745 1975 \n", "Q 2488 2216 2034 2216 \n", "z\n", "M 1403 2484 \n", "Q 997 2584 770 2862 \n", "Q 544 3141 544 3541 \n", "Q 544 4100 942 4425 \n", "Q 1341 4750 2034 4750 \n", "Q 2731 4750 3128 4425 \n", "Q 3525 4100 3525 3541 \n", "Q 3525 3141 3298 2862 \n", "Q 3072 2584 2669 2484 \n", "Q 3125 2378 3379 2068 \n", "Q 3634 1759 3634 1313 \n", "Q 3634 634 3220 271 \n", "Q 2806 -91 2034 -91 \n", "Q 1263 -91 848 271 \n", "Q 434 634 434 1313 \n", "Q 434 1759 690 2068 \n", "Q 947 2378 1403 2484 \n", "z\n", "M 1172 3481 \n", "Q 1172 3119 1398 2916 \n", "Q 1625 2713 2034 2713 \n", "Q 2441 2713 2670 2916 \n", "Q 2900 3119 2900 3481 \n", "Q 2900 3844 2670 4047 \n", "Q 2441 4250 2034 4250 \n", "Q 1625 4250 1398 4047 \n", "Q 1172 3844 1172 3481 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-38\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"127.246094\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_5\">\n", "     <g id=\"line2d_9\">\n", "      <path d=\"M 386.**********.52 \n", "L 386.960938 7.2 \n", "\" clip-path=\"url(#p5912583a97)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_10\">\n", "      <g>\n", "       <use xlink:href=\"#m22ef0b31fe\" x=\"386.960938\" y=\"173.52\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_5\">\n", "      <!-- 1000 -->\n", "      <g transform=\"translate(374.235938 188.118438)scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-31\" d=\"M 794 531 \n", "L 1825 531 \n", "L 1825 4091 \n", "L 703 3866 \n", "L 703 4441 \n", "L 1819 4666 \n", "L 2450 4666 \n", "L 2450 531 \n", "L 3481 531 \n", "L 3481 0 \n", "L 794 0 \n", "L 794 531 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-31\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"127.246094\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"190.869141\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"text_6\">\n", "     <!-- time -->\n", "     <g transform=\"translate(208.264844 201.796563)scale(0.1 -0.1)\">\n", "      <defs>\n", "       <path id=\"DejaVuSans-74\" d=\"M 1172 4494 \n", "L 1172 3500 \n", "L 2356 3500 \n", "L 2356 3053 \n", "L 1172 3053 \n", "L 1172 1153 \n", "Q 1172 725 1289 603 \n", "Q 1406 481 1766 481 \n", "L 2356 481 \n", "L 2356 0 \n", "L 1766 0 \n", "Q 1100 0 847 248 \n", "Q 594 497 594 1153 \n", "L 594 3053 \n", "L 172 3053 \n", "L 172 3500 \n", "L 594 3500 \n", "L 594 4494 \n", "L 1172 4494 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-69\" d=\"M 603 3500 \n", "L 1178 3500 \n", "L 1178 0 \n", "L 603 0 \n", "L 603 3500 \n", "z\n", "M 603 4863 \n", "L 1178 4863 \n", "L 1178 4134 \n", "L 603 4134 \n", "L 603 4863 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-6d\" d=\"M 3328 2828 \n", "Q 3544 3216 3844 3400 \n", "Q 4144 3584 4550 3584 \n", "Q 5097 3584 5394 3201 \n", "Q 5691 2819 5691 2113 \n", "L 5691 0 \n", "L 5113 0 \n", "L 5113 2094 \n", "Q 5113 2597 4934 2840 \n", "Q 4756 3084 4391 3084 \n", "Q 3944 3084 3684 2787 \n", "Q 3425 2491 3425 1978 \n", "L 3425 0 \n", "L 2847 0 \n", "L 2847 2094 \n", "Q 2847 2600 2669 2842 \n", "Q 2491 3084 2119 3084 \n", "Q 1678 3084 1418 2786 \n", "Q 1159 2488 1159 1978 \n", "L 1159 0 \n", "L 581 0 \n", "L 581 3500 \n", "L 1159 3500 \n", "L 1159 2956 \n", "Q 1356 3278 1631 3431 \n", "Q 1906 3584 2284 3584 \n", "Q 2666 3584 2933 3390 \n", "Q 3200 3197 3328 2828 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-65\" d=\"M 3597 1894 \n", "L 3597 1613 \n", "L 953 1613 \n", "Q 991 1019 1311 708 \n", "Q 1631 397 2203 397 \n", "Q 2534 397 2845 478 \n", "Q 3156 559 3463 722 \n", "L 3463 178 \n", "Q 3153 47 2828 -22 \n", "Q 2503 -91 2169 -91 \n", "Q 1331 -91 842 396 \n", "Q 353 884 353 1716 \n", "Q 353 2575 817 3079 \n", "Q 1281 3584 2069 3584 \n", "Q 2775 3584 3186 3129 \n", "Q 3597 2675 3597 1894 \n", "z\n", "M 3022 2063 \n", "Q 3016 2534 2758 2815 \n", "Q 2500 3097 2075 3097 \n", "Q 1594 3097 1305 2825 \n", "Q 1016 2553 972 2059 \n", "L 3022 2063 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "      </defs>\n", "      <use xlink:href=\"#DejaVuSans-74\"/>\n", "      <use xlink:href=\"#DejaVuSans-69\" x=\"39.208984\"/>\n", "      <use xlink:href=\"#DejaVuSans-6d\" x=\"66.992188\"/>\n", "      <use xlink:href=\"#DejaVuSans-65\" x=\"164.404297\"/>\n", "     </g>\n", "    </g>\n", "   </g>\n", "   <g id=\"matplotlib.axis_2\">\n", "    <g id=\"ytick_1\">\n", "     <g id=\"line2d_11\">\n", "      <path d=\"M 52.160938 155.699239 \n", "L 386.960938 155.699239 \n", "\" clip-path=\"url(#p5912583a97)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_12\">\n", "      <defs>\n", "       <path id=\"m24901bcae8\" d=\"M 0 0 \n", "L -3.5 0 \n", "\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </defs>\n", "      <g>\n", "       <use xlink:href=\"#m24901bcae8\" x=\"52.160938\" y=\"155.699239\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_7\">\n", "      <!-- −1.0 -->\n", "      <g transform=\"translate(20.878125 159.498457)scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-2212\" d=\"M 678 2272 \n", "L 4684 2272 \n", "L 4684 1741 \n", "L 678 1741 \n", "L 678 2272 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "        <path id=\"DejaVuSans-2e\" d=\"M 684 794 \n", "L 1344 794 \n", "L 1344 0 \n", "L 684 0 \n", "L 684 794 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-2212\"/>\n", "       <use xlink:href=\"#DejaVuSans-31\" x=\"83.789062\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"147.412109\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"179.199219\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_2\">\n", "     <g id=\"line2d_13\">\n", "      <path d=\"M 52.160938 125.104094 \n", "L 386.960938 125.104094 \n", "\" clip-path=\"url(#p5912583a97)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_14\">\n", "      <g>\n", "       <use xlink:href=\"#m24901bcae8\" x=\"52.160938\" y=\"125.104094\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_8\">\n", "      <!-- −0.5 -->\n", "      <g transform=\"translate(20.878125 128.903312)scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-35\" d=\"M 691 4666 \n", "L 3169 4666 \n", "L 3169 4134 \n", "L 1269 4134 \n", "L 1269 2991 \n", "Q 1406 3038 1543 3061 \n", "Q 1681 3084 1819 3084 \n", "Q 2600 3084 3056 2656 \n", "Q 3513 2228 3513 1497 \n", "Q 3513 744 3044 326 \n", "Q 2575 -91 1722 -91 \n", "Q 1428 -91 1123 -41 \n", "Q 819 9 494 109 \n", "L 494 744 \n", "Q 775 591 1075 516 \n", "Q 1375 441 1709 441 \n", "Q 2250 441 2565 725 \n", "Q 2881 1009 2881 1497 \n", "Q 2881 1984 2565 2268 \n", "Q 2250 2553 1709 2553 \n", "Q 1456 2553 1204 2497 \n", "Q 953 2441 691 2322 \n", "L 691 4666 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-2212\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"83.789062\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"147.412109\"/>\n", "       <use xlink:href=\"#DejaVuSans-35\" x=\"179.199219\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_3\">\n", "     <g id=\"line2d_15\">\n", "      <path d=\"M 52.160938 94.508949 \n", "L 386.960938 94.508949 \n", "\" clip-path=\"url(#p5912583a97)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_16\">\n", "      <g>\n", "       <use xlink:href=\"#m24901bcae8\" x=\"52.160938\" y=\"94.508949\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_9\">\n", "      <!-- 0.0 -->\n", "      <g transform=\"translate(29.257812 98.308168)scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"95.410156\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_4\">\n", "     <g id=\"line2d_17\">\n", "      <path d=\"M 52.160938 63.913804 \n", "L 386.960938 63.913804 \n", "\" clip-path=\"url(#p5912583a97)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_18\">\n", "      <g>\n", "       <use xlink:href=\"#m24901bcae8\" x=\"52.160938\" y=\"63.913804\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_10\">\n", "      <!-- 0.5 -->\n", "      <g transform=\"translate(29.257812 67.713023)scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-35\" x=\"95.410156\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_5\">\n", "     <g id=\"line2d_19\">\n", "      <path d=\"M 52.160938 33.318659 \n", "L 386.960938 33.318659 \n", "\" clip-path=\"url(#p5912583a97)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_20\">\n", "      <g>\n", "       <use xlink:href=\"#m24901bcae8\" x=\"52.160938\" y=\"33.318659\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_11\">\n", "      <!-- 1.0 -->\n", "      <g transform=\"translate(29.257812 37.117878)scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-31\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"95.410156\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"text_12\">\n", "     <!-- x -->\n", "     <g transform=\"translate(14.798438 93.319375)rotate(-90)scale(0.1 -0.1)\">\n", "      <defs>\n", "       <path id=\"DejaVuSans-78\" d=\"M 3513 3500 \n", "L 2247 1797 \n", "L 3578 0 \n", "L 2900 0 \n", "L 1881 1375 \n", "L 863 0 \n", "L 184 0 \n", "L 1544 1831 \n", "L 300 3500 \n", "L 978 3500 \n", "L 1906 2253 \n", "L 2834 3500 \n", "L 3513 3500 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "      </defs>\n", "      <use xlink:href=\"#DejaVuSans-78\"/>\n", "     </g>\n", "    </g>\n", "   </g>\n", "   <g id=\"line2d_21\">\n", "    <path d=\"M 52.160938 75.106781 \n", "L 52.49742 79.282075 \n", "L 52.833902 80.469756 \n", "L 53.170385 75.311741 \n", "L 53.506867 77.872137 \n", "L 53.84335 74.960309 \n", "L 54.179832 68.285791 \n", "L 54.516314 86.62079 \n", "L 55.189279 81.406141 \n", "L 55.525762 97.55662 \n", "L 55.862244 91.137837 \n", "L 56.198726 81.90518 \n", "L 56.535209 81.922021 \n", "L 56.871691 95.97154 \n", "L 57.208174 86.086423 \n", "L 57.544656 81.811854 \n", "L 57.881139 89.253464 \n", "L 58.554103 82.443308 \n", "L 59.227068 91.067807 \n", "L 59.900033 76.34206 \n", "L 60.236515 75.891781 \n", "L 60.90948 61.996067 \n", "L 61.245963 74.617116 \n", "L 61.582445 64.653075 \n", "L 61.918927 77.656914 \n", "L 62.25541 64.321451 \n", "L 62.591892 64.42765 \n", "L 62.928375 87.833605 \n", "L 63.264857 73.749955 \n", "L 63.60134 68.811211 \n", "L 63.937822 72.472376 \n", "L 64.274304 85.231999 \n", "L 64.610787 64.331699 \n", "L 64.947269 54.54708 \n", "L 65.283752 72.196554 \n", "L 65.620234 64.202051 \n", "L 65.956716 73.355785 \n", "L 66.293199 72.569593 \n", "L 66.629681 60.306027 \n", "L 66.966164 74.532803 \n", "L 67.302646 71.589514 \n", "L 67.639128 57.396784 \n", "L 68.312093 66.977481 \n", "L 68.985058 68.069175 \n", "L 69.658023 57.561723 \n", "L 69.994505 61.506376 \n", "L 70.330988 56.219198 \n", "L 71.003953 58.70027 \n", "L 71.340435 56.462358 \n", "L 71.676917 57.897935 \n", "L 72.0134 61.919753 \n", "L 72.349882 52.33236 \n", "L 72.686365 52.908388 \n", "L 73.022847 59.363788 \n", "L 73.359329 46.313417 \n", "L 73.695812 49.189794 \n", "L 74.032294 49.809286 \n", "L 74.368777 46.636021 \n", "L 74.705259 65.232042 \n", "L 75.041742 61.347007 \n", "L 75.714706 58.017192 \n", "L 76.051189 61.966401 \n", "L 76.387671 59.21156 \n", "L 76.724154 46.237183 \n", "L 77.060636 52.131238 \n", "L 77.397118 55.04683 \n", "L 77.733601 48.103205 \n", "L 78.070083 60.846681 \n", "L 78.406566 64.408798 \n", "L 78.743048 47.149383 \n", "L 79.07953 45.507066 \n", "L 79.416013 41.695548 \n", "L 80.088978 52.772139 \n", "L 80.42546 35.17188 \n", "L 81.098425 54.79713 \n", "L 81.434907 47.107783 \n", "L 81.77139 48.665546 \n", "L 82.444355 55.477246 \n", "L 82.780837 47.41272 \n", "L 83.117319 48.009187 \n", "L 83.453802 48.944686 \n", "L 83.790284 46.361196 \n", "L 84.126767 52.27507 \n", "L 84.463249 49.310813 \n", "L 84.799731 32.62678 \n", "L 85.136214 43.953538 \n", "L 85.472696 50.063852 \n", "L 85.809179 36.306313 \n", "L 86.145661 46.203125 \n", "L 86.482144 47.385555 \n", "L 86.818626 52.231799 \n", "L 87.155108 45.174698 \n", "L 87.491591 26.156449 \n", "L 87.828073 39.32902 \n", "L 88.164556 44.490151 \n", "L 88.501038 38.503066 \n", "L 88.83752 40.089675 \n", "L 89.174003 38.925152 \n", "L 89.510485 41.142753 \n", "L 89.846968 46.113235 \n", "L 90.18345 36.76757 \n", "L 90.519932 35.802937 \n", "L 90.856415 35.996846 \n", "L 91.192897 32.164392 \n", "L 91.865862 50.742155 \n", "L 92.202345 29.965159 \n", "L 92.538827 32.956606 \n", "L 92.875309 47.314788 \n", "L 93.211792 42.867577 \n", "L 93.548274 23.251558 \n", "L 93.884757 27.363593 \n", "L 94.221239 42.787228 \n", "L 94.557721 42.03768 \n", "L 94.894204 26.791497 \n", "L 95.230686 36.389032 \n", "L 95.567169 42.081811 \n", "L 95.903651 26.981262 \n", "L 96.576616 43.755665 \n", "L 96.913098 34.057302 \n", "L 97.249581 30.833899 \n", "L 97.586063 32.713752 \n", "L 97.922546 40.449222 \n", "L 98.259028 44.318042 \n", "L 98.59551 32.206685 \n", "L 98.931993 33.048626 \n", "L 99.268475 45.322743 \n", "L 99.604958 33.761476 \n", "L 99.94144 17.130295 \n", "L 100.277922 27.479597 \n", "L 101.623852 38.834176 \n", "L 101.960334 41.533721 \n", "L 102.296817 38.5645 \n", "L 102.969782 43.102761 \n", "L 103.306264 39.078292 \n", "L 103.979229 55.035385 \n", "L 104.315711 36.644323 \n", "L 104.652194 36.342479 \n", "L 104.988676 49.442204 \n", "L 105.661641 17.836486 \n", "L 105.998123 22.546644 \n", "L 106.334606 32.955031 \n", "L 106.671088 34.9735 \n", "L 107.007571 35.07552 \n", "L 107.344053 44.396494 \n", "L 107.680535 48.888971 \n", "L 108.017018 31.877035 \n", "L 108.3535 37.510306 \n", "L 109.026465 15.177352 \n", "L 109.362948 39.978639 \n", "L 109.69943 33.240368 \n", "L 110.035912 32.85363 \n", "L 110.372395 46.47849 \n", "L 110.708877 41.188073 \n", "L 111.04536 41.602059 \n", "L 111.381842 33.396119 \n", "L 112.054807 36.376675 \n", "L 112.391289 31.433226 \n", "L 112.727772 35.770492 \n", "L 113.064254 25.409271 \n", "L 113.737219 47.421236 \n", "L 114.073701 41.636758 \n", "L 114.410184 37.81598 \n", "L 114.746666 39.367094 \n", "L 115.083149 38.082504 \n", "L 115.419631 40.702161 \n", "L 115.756113 36.803189 \n", "L 116.092596 37.396753 \n", "L 116.429078 32.501907 \n", "L 116.765561 37.369253 \n", "L 117.102043 51.221962 \n", "L 117.438525 31.350295 \n", "L 117.775008 40.578428 \n", "L 118.11149 42.282974 \n", "L 118.447973 29.134307 \n", "L 118.784455 38.130057 \n", "L 119.45742 47.409558 \n", "L 119.793902 32.693685 \n", "L 120.130385 36.123514 \n", "L 120.466867 37.879762 \n", "L 120.80335 36.004191 \n", "L 121.139832 36.098261 \n", "L 121.812797 41.861417 \n", "L 122.149279 49.76246 \n", "L 122.485762 43.015085 \n", "L 122.822244 46.120719 \n", "L 123.158726 51.102964 \n", "L 123.495209 42.657041 \n", "L 123.831691 53.980556 \n", "L 124.168174 49.880994 \n", "L 124.504656 55.319872 \n", "L 124.841139 53.50034 \n", "L 125.177621 39.323743 \n", "L 125.514103 50.024097 \n", "L 125.850586 50.926019 \n", "L 126.187068 55.100586 \n", "L 126.523551 37.858798 \n", "L 126.860033 36.846234 \n", "L 127.196515 54.863075 \n", "L 127.86948 33.97098 \n", "L 128.205963 39.559842 \n", "L 128.542445 47.096031 \n", "L 128.878927 46.309675 \n", "L 129.21541 43.365915 \n", "L 129.551892 47.783635 \n", "L 129.888375 47.724518 \n", "L 130.224857 47.465444 \n", "L 130.56134 57.573901 \n", "L 130.897822 53.722832 \n", "L 131.234304 58.171193 \n", "L 131.570787 50.106196 \n", "L 131.907269 48.578949 \n", "L 132.243752 59.863936 \n", "L 132.580234 44.990043 \n", "L 132.916716 63.450278 \n", "L 133.253199 57.9074 \n", "L 133.589681 40.703412 \n", "L 133.926164 67.419568 \n", "L 134.262646 61.997019 \n", "L 134.599128 45.24691 \n", "L 134.935611 68.363911 \n", "L 135.272093 62.316837 \n", "L 135.608576 53.647834 \n", "L 135.945058 56.882478 \n", "L 136.281541 62.913778 \n", "L 136.618023 63.041256 \n", "L 136.954505 53.574284 \n", "L 137.290988 58.898829 \n", "L 137.62747 48.532338 \n", "L 137.963953 60.305312 \n", "L 138.300435 60.6113 \n", "L 138.636917 60.388359 \n", "L 138.9734 68.238381 \n", "L 139.309882 64.913705 \n", "L 139.646365 68.755591 \n", "L 139.982847 70.859744 \n", "L 140.319329 62.425758 \n", "L 140.655812 70.560956 \n", "L 141.328777 65.77441 \n", "L 141.665259 74.090227 \n", "L 142.001742 57.171466 \n", "L 142.338224 60.087255 \n", "L 142.674706 53.723744 \n", "L 143.011189 54.047855 \n", "L 143.347671 73.312394 \n", "L 143.684154 53.987413 \n", "L 144.020636 63.447061 \n", "L 144.357118 66.168763 \n", "L 144.693601 65.256935 \n", "L 145.030083 65.404213 \n", "L 145.366566 84.842402 \n", "L 145.703048 78.972337 \n", "L 146.03953 83.856578 \n", "L 146.376013 90.884915 \n", "L 146.712495 82.103932 \n", "L 147.048978 80.756351 \n", "L 147.38546 81.845989 \n", "L 147.721943 81.345688 \n", "L 148.058425 80.637887 \n", "L 148.394907 91.893823 \n", "L 149.067872 73.065969 \n", "L 149.404355 79.660433 \n", "L 149.740837 71.522263 \n", "L 150.077319 86.744243 \n", "L 150.413802 80.125775 \n", "L 150.750284 77.342326 \n", "L 151.086767 87.802927 \n", "L 151.423249 72.640232 \n", "L 151.759731 78.128245 \n", "L 152.096214 73.786074 \n", "L 152.769179 83.726951 \n", "L 153.105661 85.634465 \n", "L 153.442144 81.615626 \n", "L 153.778626 82.895575 \n", "L 154.115108 85.30423 \n", "L 154.451591 70.743919 \n", "L 154.788073 81.922531 \n", "L 155.124556 80.95005 \n", "L 155.461038 87.000658 \n", "L 155.79752 90.785188 \n", "L 156.134003 91.675319 \n", "L 156.470485 91.175203 \n", "L 156.806968 90.266616 \n", "L 157.14345 97.213295 \n", "L 157.479932 95.346601 \n", "L 157.816415 110.988412 \n", "L 158.152897 112.607857 \n", "L 158.48938 109.270594 \n", "L 158.825862 118.876759 \n", "L 159.162345 100.960995 \n", "L 159.835309 111.475224 \n", "L 160.171792 106.011724 \n", "L 160.508274 104.934012 \n", "L 160.844757 96.983295 \n", "L 161.181239 95.63315 \n", "L 161.517721 102.961976 \n", "L 161.854204 103.042667 \n", "L 162.190686 104.185489 \n", "L 162.527169 109.319309 \n", "L 163.200133 103.894645 \n", "L 163.536616 105.951418 \n", "L 163.873098 108.994425 \n", "L 164.209581 103.446075 \n", "L 164.546063 112.963231 \n", "L 164.882546 106.464592 \n", "L 165.219028 106.596903 \n", "L 165.55551 100.495473 \n", "L 165.891993 102.120935 \n", "L 166.228475 113.732763 \n", "L 166.564958 110.651875 \n", "L 166.90144 113.369777 \n", "L 167.574405 111.504148 \n", "L 167.910887 119.734572 \n", "L 168.24737 111.999419 \n", "L 168.920334 106.448229 \n", "L 169.256817 109.134853 \n", "L 169.929782 126.416909 \n", "L 170.602747 115.728846 \n", "L 170.939229 124.653122 \n", "L 171.275711 123.552549 \n", "L 171.612194 119.136244 \n", "L 171.948676 117.979268 \n", "L 172.285159 128.595623 \n", "L 172.621641 127.810273 \n", "L 172.958123 128.546509 \n", "L 173.294606 128.711736 \n", "L 173.631088 129.355223 \n", "L 173.967571 131.410227 \n", "L 174.304053 124.728663 \n", "L 174.640535 128.068949 \n", "L 174.977018 134.34024 \n", "L 175.3135 132.663519 \n", "L 175.649983 132.970951 \n", "L 175.986465 127.616766 \n", "L 176.65943 133.017493 \n", "L 176.995912 127.633638 \n", "L 177.332395 135.567973 \n", "L 177.668877 135.225028 \n", "L 178.00536 136.052817 \n", "L 178.341842 137.595706 \n", "L 178.678324 138.282763 \n", "L 179.014807 130.593496 \n", "L 179.351289 135.276512 \n", "L 179.687772 126.622189 \n", "L 180.024254 134.759875 \n", "L 180.360736 135.70397 \n", "L 180.697219 131.008459 \n", "L 181.033701 133.627229 \n", "L 181.370184 137.58812 \n", "L 181.706666 133.207412 \n", "L 182.043149 136.150092 \n", "L 182.379631 132.555175 \n", "L 182.716113 134.417415 \n", "L 183.052596 126.96629 \n", "L 183.389078 140.258819 \n", "L 183.725561 144.661487 \n", "L 184.062043 145.518556 \n", "L 184.398525 143.148038 \n", "L 185.07149 146.941944 \n", "L 185.407973 151.569066 \n", "L 185.744455 143.847116 \n", "L 186.080938 146.368954 \n", "L 186.41742 146.069717 \n", "L 187.090385 144.211215 \n", "L 187.426867 143.642824 \n", "L 187.76335 143.52244 \n", "L 188.099832 139.408691 \n", "L 188.436314 141.850351 \n", "L 188.772797 147.129065 \n", "L 189.109279 148.698309 \n", "L 189.445762 145.150675 \n", "L 189.782244 150.820589 \n", "L 190.118726 150.913787 \n", "L 190.455209 147.89844 \n", "L 190.791691 153.488517 \n", "L 191.128174 146.570642 \n", "L 191.464656 152.418559 \n", "L 191.801139 145.646077 \n", "L 192.137621 146.510335 \n", "L 192.474103 146.022931 \n", "L 192.810586 146.861187 \n", "L 193.147068 147.305404 \n", "L 193.483551 147.077894 \n", "L 194.156515 155.220559 \n", "L 194.492998 150.92013 \n", "L 194.82948 154.66858 \n", "L 195.165963 152.54805 \n", "L 195.502445 149.55676 \n", "L 195.838927 156.526823 \n", "L 196.17541 155.831261 \n", "L 196.511892 147.145364 \n", "L 196.848375 148.21058 \n", "L 197.184857 147.229404 \n", "L 197.52134 149.530792 \n", "L 197.857822 146.54703 \n", "L 198.530787 152.27073 \n", "L 198.867269 149.470565 \n", "L 199.203752 149.440753 \n", "L 199.540234 147.690041 \n", "L 200.213199 152.533801 \n", "L 200.549681 158.900883 \n", "L 200.886164 152.549385 \n", "L 201.559128 158.132974 \n", "L 201.895611 158.39146 \n", "L 202.232093 154.059935 \n", "L 202.568576 153.044026 \n", "L 202.905058 151.347034 \n", "L 203.578023 158.489833 \n", "L 203.914505 153.897819 \n", "L 204.250988 156.57865 \n", "L 204.58747 151.792309 \n", "L 204.923953 150.864035 \n", "L 205.260435 147.16797 \n", "L 205.9334 152.509062 \n", "L 206.269882 151.749808 \n", "L 206.942847 153.008564 \n", "L 207.279329 151.459368 \n", "L 207.615812 151.704272 \n", "L 207.952294 152.47721 \n", "L 208.288777 151.276653 \n", "L 208.961742 159.515666 \n", "L 209.634706 165.96 \n", "L 209.971189 152.207002 \n", "L 210.307671 154.314394 \n", "L 210.644154 153.364296 \n", "L 210.980636 154.065336 \n", "L 211.317118 161.991315 \n", "L 211.653601 155.33538 \n", "L 211.990083 159.07681 \n", "L 212.326566 158.314029 \n", "L 212.663048 158.609156 \n", "L 212.99953 149.884186 \n", "L 213.336013 153.412064 \n", "L 213.672495 152.534957 \n", "L 214.008978 152.311765 \n", "L 214.34546 144.917486 \n", "L 214.681943 150.224192 \n", "L 215.018425 153.120614 \n", "L 215.354907 154.061685 \n", "L 215.69139 155.381252 \n", "L 216.027872 154.98397 \n", "L 216.364355 154.877424 \n", "L 217.037319 150.836116 \n", "L 217.373802 149.80045 \n", "L 217.710284 152.09195 \n", "L 218.383249 158.706398 \n", "L 218.719731 156.362202 \n", "L 219.056214 145.175607 \n", "L 219.392696 149.990827 \n", "L 219.729179 146.472309 \n", "L 220.065661 145.162236 \n", "L 220.402144 151.770714 \n", "L 220.738626 155.630707 \n", "L 221.075108 155.632411 \n", "L 221.411591 157.53612 \n", "L 221.748073 148.270712 \n", "L 222.084556 149.347869 \n", "L 222.421038 146.800993 \n", "L 222.75752 150.929674 \n", "L 223.094003 146.593357 \n", "L 223.430485 146.788104 \n", "L 223.766968 145.085878 \n", "L 224.10345 145.692769 \n", "L 224.776415 156.219049 \n", "L 225.112897 155.794847 \n", "L 225.44938 157.353248 \n", "L 225.785862 145.409212 \n", "L 226.122345 147.258501 \n", "L 226.458827 146.587266 \n", "L 226.795309 149.180618 \n", "L 227.468274 148.651274 \n", "L 227.804757 147.744034 \n", "L 228.141239 144.439703 \n", "L 228.477721 142.800242 \n", "L 228.814204 142.007412 \n", "L 229.150686 143.514577 \n", "L 229.487169 150.189707 \n", "L 229.823651 152.49801 \n", "L 230.160133 141.250018 \n", "L 230.496616 144.673151 \n", "L 230.833098 141.717271 \n", "L 231.169581 150.43135 \n", "L 231.506063 155.169698 \n", "L 231.842546 146.467998 \n", "L 232.179028 146.423458 \n", "L 232.851993 136.729377 \n", "L 233.188475 136.634403 \n", "L 233.524958 140.360405 \n", "L 233.86144 140.466722 \n", "L 234.197922 143.746033 \n", "L 234.534405 145.751927 \n", "L 234.870887 142.092392 \n", "L 235.20737 142.027534 \n", "L 235.543852 136.876856 \n", "L 235.880334 136.347578 \n", "L 236.553299 134.195073 \n", "L 236.889782 139.16739 \n", "L 237.226264 140.251298 \n", "L 237.562747 142.115213 \n", "L 237.899229 142.801391 \n", "L 238.235711 136.782408 \n", "L 238.572194 137.642759 \n", "L 238.908676 134.043392 \n", "L 239.245159 138.80374 \n", "L 239.581641 133.450988 \n", "L 239.918123 142.596909 \n", "L 240.254606 148.076136 \n", "L 240.591088 149.399547 \n", "L 240.927571 136.415402 \n", "L 241.264053 139.680012 \n", "L 241.600535 126.011998 \n", "L 242.2735 134.333332 \n", "L 242.609983 127.06594 \n", "L 242.946465 131.473944 \n", "L 243.282948 128.023453 \n", "L 243.61943 126.040041 \n", "L 243.955912 122.353364 \n", "L 244.292395 129.160297 \n", "L 244.628877 130.027706 \n", "L 244.96536 131.673969 \n", "L 245.301842 122.875602 \n", "L 245.638324 119.563933 \n", "L 245.974807 126.477632 \n", "L 246.311289 121.613918 \n", "L 246.647772 124.802607 \n", "L 246.984254 131.595841 \n", "L 247.320736 128.01482 \n", "L 247.657219 128.008602 \n", "L 247.993701 116.53334 \n", "L 248.330184 119.799026 \n", "L 248.666666 119.53536 \n", "L 249.339631 108.24528 \n", "L 249.676113 119.024727 \n", "L 250.012596 114.471811 \n", "L 250.349078 119.057537 \n", "L 250.685561 118.715118 \n", "L 251.022043 109.382374 \n", "L 251.358525 107.231773 \n", "L 251.695008 106.932658 \n", "L 252.03149 103.096846 \n", "L 252.367973 103.018683 \n", "L 253.040938 101.645617 \n", "L 253.37742 106.65716 \n", "L 253.713902 106.620574 \n", "L 254.386867 118.406205 \n", "L 254.72335 111.640893 \n", "L 255.059832 115.853552 \n", "L 255.396314 111.068702 \n", "L 256.069279 107.226558 \n", "L 256.405762 97.739673 \n", "L 257.078726 102.803757 \n", "L 257.415209 108.190639 \n", "L 257.751691 106.234994 \n", "L 258.088174 113.198671 \n", "L 258.424656 115.790739 \n", "L 258.761139 103.193292 \n", "L 259.097621 96.581431 \n", "L 259.434103 101.793488 \n", "L 259.770586 105.184887 \n", "L 260.107068 103.188711 \n", "L 260.443551 105.094992 \n", "L 260.780033 103.274464 \n", "L 261.116515 97.234824 \n", "L 261.452998 103.788752 \n", "L 261.78948 92.05645 \n", "L 262.125963 93.7528 \n", "L 262.462445 96.166419 \n", "L 262.798927 93.269834 \n", "L 263.13541 95.310361 \n", "L 263.471892 90.347588 \n", "L 263.808375 90.987827 \n", "L 264.144857 87.67769 \n", "L 264.48134 87.089203 \n", "L 264.817822 96.633329 \n", "L 265.154304 97.011347 \n", "L 265.490787 99.030591 \n", "L 265.827269 99.394401 \n", "L 266.163752 92.368828 \n", "L 266.500234 90.127773 \n", "L 266.836716 81.484999 \n", "L 267.173199 79.806294 \n", "L 267.509681 86.263101 \n", "L 268.182646 82.775112 \n", "L 268.519128 90.955803 \n", "L 269.192093 61.602141 \n", "L 269.528576 80.971727 \n", "L 269.865058 80.935935 \n", "L 270.201541 78.510777 \n", "L 270.538023 90.132647 \n", "L 270.874505 83.257037 \n", "L 271.210988 80.040198 \n", "L 271.54747 75.457116 \n", "L 271.883953 73.588117 \n", "L 272.220435 78.313353 \n", "L 272.556917 69.520129 \n", "L 272.8934 78.89746 \n", "L 273.229882 74.126469 \n", "L 273.566365 80.304241 \n", "L 273.902847 82.505135 \n", "L 274.239329 78.493752 \n", "L 274.575812 82.144892 \n", "L 274.912294 81.753136 \n", "L 275.248777 72.527537 \n", "L 275.585259 73.483759 \n", "L 275.921742 77.481519 \n", "L 276.258224 71.67677 \n", "L 276.594706 76.00727 \n", "L 276.931189 71.360723 \n", "L 277.267671 56.439742 \n", "L 277.604154 74.401926 \n", "L 277.940636 65.190603 \n", "L 278.277118 70.206344 \n", "L 278.613601 69.681187 \n", "L 278.950083 58.566475 \n", "L 279.286566 66.937594 \n", "L 279.623048 61.5204 \n", "L 279.95953 59.551022 \n", "L 280.296013 65.712473 \n", "L 280.632495 62.333727 \n", "L 280.968978 54.834568 \n", "L 281.30546 63.014807 \n", "L 281.641943 59.930056 \n", "L 281.978425 48.136472 \n", "L 282.314907 64.511533 \n", "L 282.65139 56.087649 \n", "L 282.987872 51.69697 \n", "L 283.324355 51.087026 \n", "L 283.660837 54.335427 \n", "L 283.997319 60.933784 \n", "L 284.333802 57.06341 \n", "L 284.670284 64.293524 \n", "L 285.006767 51.324318 \n", "L 285.343249 51.719955 \n", "L 285.679731 62.336882 \n", "L 286.016214 51.782658 \n", "L 286.352696 47.98656 \n", "L 286.689179 59.769958 \n", "L 287.025661 47.783147 \n", "L 287.362144 57.695784 \n", "L 287.698626 54.794905 \n", "L 288.035108 64.401248 \n", "L 288.371591 63.306712 \n", "L 288.708073 47.879944 \n", "L 289.044556 61.181474 \n", "L 289.381038 53.02971 \n", "L 289.71752 52.615618 \n", "L 290.054003 52.861872 \n", "L 290.390485 54.907393 \n", "L 290.726968 61.970124 \n", "L 291.06345 49.794015 \n", "L 291.399932 47.571498 \n", "L 291.736415 51.198744 \n", "L 292.072897 39.592576 \n", "L 292.40938 42.156317 \n", "L 292.745862 52.958239 \n", "L 293.082345 40.479322 \n", "L 293.418827 41.947845 \n", "L 293.755309 48.580963 \n", "L 294.091792 48.236497 \n", "L 294.428274 50.472625 \n", "L 294.764757 29.091488 \n", "L 295.437721 44.005766 \n", "L 295.774204 34.661341 \n", "L 296.110686 46.888617 \n", "L 296.447169 34.700928 \n", "L 296.783651 26.817027 \n", "L 297.793098 42.649516 \n", "L 298.129581 44.422276 \n", "L 298.466063 37.253147 \n", "L 298.802546 27.241812 \n", "L 299.139028 40.605571 \n", "L 299.47551 47.838876 \n", "L 299.811993 36.04663 \n", "L 300.148475 36.933833 \n", "L 300.484958 41.675959 \n", "L 300.82144 40.287785 \n", "L 301.157922 31.388241 \n", "L 301.494405 33.665138 \n", "L 301.830887 56.352887 \n", "L 302.16737 48.804837 \n", "L 302.503852 19.804777 \n", "L 302.840334 37.717599 \n", "L 303.176817 49.273669 \n", "L 303.513299 41.236961 \n", "L 303.849782 36.061424 \n", "L 304.186264 54.517329 \n", "L 304.522747 45.087307 \n", "L 304.859229 40.208323 \n", "L 305.195711 51.382127 \n", "L 305.532194 30.914649 \n", "L 305.868676 46.120096 \n", "L 306.205159 45.544239 \n", "L 306.541641 31.212517 \n", "L 307.551088 46.875035 \n", "L 307.887571 40.143519 \n", "L 308.224053 37.763292 \n", "L 308.560535 53.495183 \n", "L 308.897018 48.96987 \n", "L 309.2335 25.774358 \n", "L 309.569983 35.488043 \n", "L 309.906465 34.710943 \n", "L 310.242948 27.360799 \n", "L 310.57943 42.097487 \n", "L 310.915912 34.514803 \n", "L 311.588877 50.036928 \n", "L 311.92536 31.912391 \n", "L 312.261842 37.144409 \n", "L 312.598324 53.838198 \n", "L 312.934807 51.075789 \n", "L 313.271289 29.700918 \n", "L 313.607772 42.870443 \n", "L 313.944254 39.457811 \n", "L 314.280736 40.806194 \n", "L 314.617219 42.63442 \n", "L 314.953701 34.91502 \n", "L 315.290184 40.912153 \n", "L 315.626666 49.180796 \n", "L 315.963149 43.576634 \n", "L 316.299631 34.205044 \n", "L 316.636113 49.870228 \n", "L 316.972596 41.633232 \n", "L 317.309078 29.757946 \n", "L 317.645561 38.329268 \n", "L 317.982043 39.713905 \n", "L 318.318525 29.348617 \n", "L 318.655008 29.138946 \n", "L 318.99149 47.469011 \n", "L 319.327973 51.798848 \n", "L 319.664455 29.96529 \n", "L 320.000938 31.68697 \n", "L 320.33742 31.578458 \n", "L 320.673902 32.447264 \n", "L 321.010385 32.70432 \n", "L 321.346867 33.917387 \n", "L 321.68335 26.811316 \n", "L 322.019832 27.176184 \n", "L 322.356314 25.153243 \n", "L 323.029279 45.872694 \n", "L 323.365762 18.481724 \n", "L 323.702244 30.819558 \n", "L 324.038726 35.685001 \n", "L 324.375209 36.030845 \n", "L 324.711691 36.032078 \n", "L 325.048174 26.062978 \n", "L 325.384656 36.142275 \n", "L 326.057621 47.549622 \n", "L 326.730586 31.210628 \n", "L 327.067068 37.951153 \n", "L 327.403551 30.34903 \n", "L 327.740033 32.364567 \n", "L 328.076515 41.153454 \n", "L 328.412998 43.443444 \n", "L 329.085963 37.101692 \n", "L 329.422445 39.228032 \n", "L 329.758927 32.825919 \n", "L 330.09541 43.075811 \n", "L 330.431892 49.72951 \n", "L 330.768375 34.754725 \n", "L 331.104857 42.625569 \n", "L 331.44134 43.653123 \n", "L 331.777822 48.908254 \n", "L 332.114304 46.599141 \n", "L 332.787269 35.1804 \n", "L 333.460234 46.863101 \n", "L 334.133199 32.427817 \n", "L 334.469681 45.356206 \n", "L 334.806164 43.379034 \n", "L 335.142646 31.137377 \n", "L 335.479128 35.188694 \n", "L 335.815611 45.15109 \n", "L 336.152093 46.73074 \n", "L 336.488576 33.720423 \n", "L 336.825058 51.254999 \n", "L 337.161541 50.190498 \n", "L 337.498023 45.933952 \n", "L 337.834505 54.500928 \n", "L 338.50747 35.826557 \n", "L 338.843953 44.09764 \n", "L 339.180435 39.818007 \n", "L 339.516917 43.929131 \n", "L 339.8534 52.480861 \n", "L 340.189882 54.27817 \n", "L 340.526365 52.674689 \n", "L 340.862847 45.796437 \n", "L 341.199329 50.796539 \n", "L 341.872294 38.857751 \n", "L 342.208777 47.023856 \n", "L 342.545259 45.563653 \n", "L 342.881742 38.410893 \n", "L 343.218224 41.477929 \n", "L 343.554706 64.742402 \n", "L 343.891189 47.914038 \n", "L 344.227671 49.009373 \n", "L 344.564154 75.559125 \n", "L 344.900636 58.77986 \n", "L 345.237118 55.324143 \n", "L 345.573601 63.322512 \n", "L 345.910083 53.2339 \n", "L 346.246566 48.32883 \n", "L 346.583048 61.299411 \n", "L 346.91953 60.434829 \n", "L 347.256013 62.047748 \n", "L 347.592495 67.82174 \n", "L 347.928978 75.785603 \n", "L 348.26546 71.454701 \n", "L 348.601943 72.160779 \n", "L 348.938425 56.143952 \n", "L 349.274907 60.236609 \n", "L 349.61139 66.743467 \n", "L 349.947872 67.867659 \n", "L 350.284355 76.835052 \n", "L 350.620837 68.456788 \n", "L 350.957319 63.310483 \n", "L 351.293802 64.022932 \n", "L 351.630284 65.891567 \n", "L 351.966767 66.859573 \n", "L 352.303249 70.052015 \n", "L 352.639731 58.687432 \n", "L 352.976214 51.261538 \n", "L 353.649179 66.907629 \n", "L 353.985661 59.762484 \n", "L 354.322144 69.762327 \n", "L 354.658626 74.014259 \n", "L 354.995108 76.609103 \n", "L 355.331591 70.340401 \n", "L 355.668073 81.712699 \n", "L 356.004556 85.23167 \n", "L 356.341038 65.653034 \n", "L 356.67752 78.591166 \n", "L 357.014003 79.155504 \n", "L 357.350485 79.492268 \n", "L 358.02345 88.525909 \n", "L 358.359932 75.93205 \n", "L 358.696415 79.991572 \n", "L 359.36938 73.566511 \n", "L 359.705862 74.41321 \n", "L 360.042345 77.619694 \n", "L 360.378827 82.416397 \n", "L 360.715309 75.130911 \n", "L 361.051792 60.999558 \n", "L 361.388274 74.047259 \n", "L 361.724757 80.604756 \n", "L 362.061239 71.655882 \n", "L 362.397721 81.165666 \n", "L 362.734204 79.003692 \n", "L 363.070686 78.59501 \n", "L 363.407169 84.295766 \n", "L 363.743651 80.549989 \n", "L 364.080133 79.78403 \n", "L 364.416616 84.922551 \n", "L 364.753098 82.891058 \n", "L 365.089581 98.158796 \n", "L 365.426063 97.784356 \n", "L 365.762546 100.652727 \n", "L 365.762546 100.652727 \n", "\" clip-path=\"url(#p5912583a97)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_22\">\n", "    <path d=\"M 53.170385 74.409067 \n", "L 53.506867 75.295015 \n", "L 53.84335 77.381509 \n", "L 54.179832 71.118909 \n", "L 54.516314 72.941074 \n", "L 54.852797 69.63273 \n", "L 55.189279 59.725626 \n", "L 55.525762 92.449655 \n", "L 55.862244 94.553702 \n", "L 56.198726 82.455909 \n", "L 56.535209 81.895812 \n", "L 56.871691 89.927819 \n", "L 57.208174 86.206637 \n", "L 57.544656 78.325753 \n", "L 57.881139 95.686526 \n", "L 58.217621 83.859635 \n", "L 58.554103 77.176068 \n", "L 58.890586 72.855389 \n", "L 59.227068 89.680909 \n", "L 59.563551 87.719579 \n", "L 59.900033 87.420913 \n", "L 60.236515 80.598643 \n", "L 60.572998 76.112912 \n", "L 60.90948 62.93417 \n", "L 61.245963 73.493708 \n", "L 61.582445 69.341747 \n", "L 61.918927 60.685214 \n", "L 62.25541 59.76751 \n", "L 62.591892 53.21376 \n", "L 62.928375 88.983221 \n", "L 63.264857 73.540736 \n", "L 63.60134 64.47292 \n", "L 64.274304 77.672787 \n", "L 64.610787 72.496499 \n", "L 64.947269 57.785892 \n", "L 65.283752 65.21106 \n", "L 65.620234 67.377647 \n", "L 65.956716 62.537961 \n", "L 66.293199 71.635662 \n", "L 66.629681 59.756138 \n", "L 66.966164 70.96737 \n", "L 67.302646 72.335685 \n", "L 67.639128 59.391259 \n", "L 67.975611 54.429234 \n", "L 68.312093 64.81515 \n", "L 68.985058 67.641414 \n", "L 69.658023 60.150874 \n", "L 69.994505 59.083889 \n", "L 70.330988 59.993091 \n", "L 70.66747 53.676359 \n", "L 71.003953 60.021485 \n", "L 71.340435 55.189195 \n", "L 71.676917 58.994448 \n", "L 72.0134 58.073316 \n", "L 72.349882 56.209153 \n", "L 72.686365 50.926377 \n", "L 73.022847 62.693482 \n", "L 73.359329 51.017339 \n", "L 73.695812 44.694549 \n", "L 74.032294 45.834799 \n", "L 74.368777 42.133427 \n", "L 74.705259 56.675856 \n", "L 75.041742 64.361391 \n", "L 75.378224 50.310623 \n", "L 75.714706 59.82839 \n", "L 76.051189 60.149619 \n", "L 76.387671 64.114569 \n", "L 76.724154 50.258902 \n", "L 77.397118 49.420248 \n", "L 77.733601 45.479103 \n", "L 78.406566 66.983725 \n", "L 78.743048 50.676928 \n", "L 79.07953 46.487054 \n", "L 79.416013 46.918313 \n", "L 79.752495 46.303143 \n", "L 80.088978 50.848301 \n", "L 80.42546 38.630507 \n", "L 80.761943 38.956795 \n", "L 81.098425 57.445635 \n", "L 81.434907 44.446869 \n", "L 81.77139 47.62532 \n", "L 82.107872 53.0839 \n", "L 82.444355 55.051495 \n", "L 82.780837 49.403025 \n", "L 83.117319 45.91684 \n", "L 83.453802 50.140141 \n", "L 83.790284 48.457738 \n", "L 84.126767 51.316243 \n", "L 84.463249 50.705658 \n", "L 84.799731 36.552486 \n", "L 85.136214 39.555123 \n", "L 85.472696 52.165759 \n", "L 85.809179 36.736912 \n", "L 86.145661 40.570219 \n", "L 86.482144 52.538221 \n", "L 86.818626 47.643115 \n", "L 87.155108 50.158333 \n", "L 87.491591 29.834165 \n", "L 88.164556 47.587582 \n", "L 88.501038 38.005986 \n", "L 88.83752 37.361995 \n", "L 89.174003 41.167072 \n", "L 89.510485 41.911708 \n", "L 89.846968 47.061463 \n", "L 90.18345 41.80522 \n", "L 90.519932 33.988538 \n", "L 90.856415 36.444411 \n", "L 91.192897 33.081407 \n", "L 91.865862 47.825531 \n", "L 92.202345 34.855045 \n", "L 92.538827 30.829588 \n", "L 92.875309 51.730831 \n", "L 93.211792 42.96727 \n", "L 93.548274 27.332927 \n", "L 93.884757 27.804557 \n", "L 94.221239 45.07564 \n", "L 94.557721 40.288558 \n", "L 94.894204 30.377113 \n", "L 95.230686 30.87129 \n", "L 95.567169 45.235647 \n", "L 95.903651 28.709434 \n", "L 96.240133 34.885719 \n", "L 96.576616 46.047294 \n", "L 97.249581 28.475858 \n", "L 97.586063 34.980393 \n", "L 98.259028 43.527838 \n", "L 98.59551 34.177865 \n", "L 98.931993 33.979872 \n", "L 99.268475 44.716038 \n", "L 99.604958 41.733888 \n", "L 99.94144 19.61906 \n", "L 100.277922 28.267397 \n", "L 100.614405 32.134821 \n", "L 100.950887 29.519548 \n", "L 101.28737 35.485184 \n", "L 101.623852 38.439028 \n", "L 101.960334 40.357173 \n", "L 102.296817 40.180987 \n", "L 102.633299 38.530595 \n", "L 102.969782 41.360853 \n", "L 103.306264 39.414347 \n", "L 103.979229 53.144368 \n", "L 104.315711 41.844293 \n", "L 104.652194 36.819047 \n", "L 104.988676 52.13831 \n", "L 105.325159 42.301323 \n", "L 105.661641 19.914886 \n", "L 105.998123 27.409898 \n", "L 106.334606 29.281304 \n", "L 106.671088 32.318137 \n", "L 107.007571 32.161307 \n", "L 107.344053 44.144492 \n", "L 107.680535 48.913943 \n", "L 108.3535 31.655743 \n", "L 108.689983 37.63348 \n", "L 109.026465 14.76 \n", "L 109.362948 33.973445 \n", "L 109.69943 36.053604 \n", "L 110.035912 27.955107 \n", "L 110.372395 44.517468 \n", "L 110.708877 46.301604 \n", "L 111.04536 37.779092 \n", "L 111.381842 40.003655 \n", "L 111.718324 33.155103 \n", "L 112.054807 42.406819 \n", "L 112.391289 32.463822 \n", "L 112.727772 32.23002 \n", "L 113.064254 28.342406 \n", "L 113.400736 31.044833 \n", "L 113.737219 48.545935 \n", "L 114.073701 41.11437 \n", "L 114.410184 38.184948 \n", "L 114.746666 39.599473 \n", "L 115.083149 42.039511 \n", "L 115.419631 40.085845 \n", "L 115.756113 40.840077 \n", "L 116.092596 33.118485 \n", "L 116.429078 35.844217 \n", "L 116.765561 35.007747 \n", "L 117.102043 48.546453 \n", "L 117.438525 38.024477 \n", "L 117.775008 32.108065 \n", "L 118.11149 48.763138 \n", "L 118.447973 27.592026 \n", "L 119.120937 45.771286 \n", "L 119.45742 44.842013 \n", "L 120.130385 32.951019 \n", "L 120.466867 43.330421 \n", "L 120.80335 33.325049 \n", "L 121.139832 36.546257 \n", "L 121.476314 35.530402 \n", "L 121.812797 42.990729 \n", "L 122.149279 43.693155 \n", "L 122.485762 47.137858 \n", "L 122.822244 39.958287 \n", "L 123.158726 51.972518 \n", "L 123.495209 42.672884 \n", "L 124.168174 54.339436 \n", "L 124.504656 49.252508 \n", "L 124.841139 55.930946 \n", "L 125.177621 40.650217 \n", "L 125.850586 57.20184 \n", "L 126.187068 48.592451 \n", "L 126.523551 44.686091 \n", "L 126.860033 35.090058 \n", "L 127.196515 56.036563 \n", "L 127.532998 46.136632 \n", "L 127.86948 32.461291 \n", "L 128.542445 46.42825 \n", "L 128.878927 45.807663 \n", "L 129.21541 42.777673 \n", "L 129.888375 46.481167 \n", "L 130.224857 45.68326 \n", "L 130.56134 53.207191 \n", "L 130.897822 56.984174 \n", "L 131.234304 51.883927 \n", "L 131.570787 56.150717 \n", "L 131.907269 45.544786 \n", "L 132.243752 56.297624 \n", "L 132.580234 49.157677 \n", "L 132.916716 48.482524 \n", "L 133.253199 64.552772 \n", "L 133.589681 39.62229 \n", "L 133.926164 60.050553 \n", "L 134.262646 63.276973 \n", "L 134.599128 43.664601 \n", "L 135.272093 69.510519 \n", "L 135.608576 44.247818 \n", "L 135.945058 57.412613 \n", "L 136.618023 64.702177 \n", "L 136.954505 55.12213 \n", "L 137.290988 54.526181 \n", "L 137.62747 53.340752 \n", "L 137.963953 49.697386 \n", "L 138.300435 63.835957 \n", "L 138.636917 47.722234 \n", "L 138.9734 60.657065 \n", "L 139.309882 63.078589 \n", "L 139.646365 66.354742 \n", "L 139.982847 68.158715 \n", "L 140.319329 62.405942 \n", "L 140.655812 58.626085 \n", "L 140.992294 70.034333 \n", "L 141.328777 64.945932 \n", "L 141.665259 70.370761 \n", "L 142.001742 65.449756 \n", "L 142.338224 54.520878 \n", "L 142.674706 57.664246 \n", "L 143.011189 50.485019 \n", "L 143.347671 69.196828 \n", "L 144.020636 51.051385 \n", "L 144.357118 71.054532 \n", "L 144.693601 51.60553 \n", "L 145.030083 63.807764 \n", "L 145.366566 67.782168 \n", "L 145.703048 69.986504 \n", "L 146.03953 59.660008 \n", "L 146.376013 89.564747 \n", "L 146.712495 83.304077 \n", "L 147.048978 87.00719 \n", "L 147.38546 81.699855 \n", "L 147.721943 69.257288 \n", "L 148.058425 67.438446 \n", "L 148.394907 86.92678 \n", "L 148.73139 89.396563 \n", "L 149.067872 76.991959 \n", "L 149.404355 81.788217 \n", "L 149.740837 73.692194 \n", "L 150.077319 69.789302 \n", "L 150.413802 77.747176 \n", "L 150.750284 67.926343 \n", "L 151.086767 89.287257 \n", "L 151.423249 80.281907 \n", "L 151.759731 74.280707 \n", "L 152.096214 71.515059 \n", "L 152.432696 67.619753 \n", "L 152.769179 79.88864 \n", "L 153.105661 76.178059 \n", "L 153.442144 75.919635 \n", "L 153.778626 79.392732 \n", "L 154.115108 88.671255 \n", "L 154.788073 75.493617 \n", "L 155.124556 76.647395 \n", "L 155.461038 65.786187 \n", "L 155.79752 82.924408 \n", "L 156.134003 83.780404 \n", "L 156.470485 90.092121 \n", "L 156.806968 87.420791 \n", "L 157.14345 91.531393 \n", "L 157.479932 89.255676 \n", "L 157.816415 90.698183 \n", "L 158.152897 102.37837 \n", "L 158.48938 100.168693 \n", "L 158.825862 123.631165 \n", "L 159.162345 110.035702 \n", "L 159.498827 102.301376 \n", "L 159.835309 115.061568 \n", "L 160.171792 104.533346 \n", "L 160.508274 114.306216 \n", "L 160.844757 104.052412 \n", "L 161.517721 97.301283 \n", "L 161.854204 99.312795 \n", "L 162.190686 93.814638 \n", "L 162.527169 110.544464 \n", "L 162.863651 104.297172 \n", "L 163.200133 107.289917 \n", "L 163.536616 102.955115 \n", "L 163.873098 110.892041 \n", "L 164.209581 96.120505 \n", "L 164.546063 109.441497 \n", "L 164.882546 113.621158 \n", "L 165.219028 111.535248 \n", "L 165.55551 106.753098 \n", "L 165.891993 94.737719 \n", "L 166.228475 106.124524 \n", "L 166.564958 109.614607 \n", "L 166.90144 110.126135 \n", "L 167.237922 113.378898 \n", "L 167.574405 104.50487 \n", "L 167.910887 117.035534 \n", "L 168.24737 121.906188 \n", "L 168.583852 115.873513 \n", "L 168.920334 113.441386 \n", "L 169.256817 99.004082 \n", "L 169.929782 117.316089 \n", "L 170.266264 121.6722 \n", "L 170.602747 111.82034 \n", "L 170.939229 129.039155 \n", "L 171.275711 126.032615 \n", "L 171.948676 115.848172 \n", "L 172.285159 125.50135 \n", "L 172.621641 127.667346 \n", "L 172.958123 124.41715 \n", "L 173.294606 128.152204 \n", "L 173.631088 129.474524 \n", "L 173.967571 136.679177 \n", "L 174.640535 125.217005 \n", "L 174.977018 131.803974 \n", "L 175.3135 132.231385 \n", "L 175.649983 137.680573 \n", "L 175.986465 138.296809 \n", "L 176.322948 127.497629 \n", "L 176.65943 135.995045 \n", "L 176.995912 124.645404 \n", "L 177.332395 137.868963 \n", "L 178.00536 129.450985 \n", "L 178.341842 137.246349 \n", "L 178.678324 141.680092 \n", "L 179.014807 135.02981 \n", "L 179.351289 140.695049 \n", "L 179.687772 133.113893 \n", "L 180.024254 132.081966 \n", "L 180.360736 136.833177 \n", "L 180.697219 129.565373 \n", "L 181.033701 138.804572 \n", "L 181.370184 138.905308 \n", "L 181.706666 131.340959 \n", "L 182.043149 133.184624 \n", "L 182.379631 140.411273 \n", "L 182.716113 140.666323 \n", "L 183.052596 125.165761 \n", "L 183.389078 134.580723 \n", "L 183.725561 137.599517 \n", "L 184.062043 138.083293 \n", "L 184.735008 146.239372 \n", "L 185.07149 143.174951 \n", "L 185.407973 144.576135 \n", "L 185.744455 144.055074 \n", "L 186.080938 149.114177 \n", "L 186.41742 145.580365 \n", "L 186.753902 143.715543 \n", "L 187.090385 146.093147 \n", "L 187.426867 145.257272 \n", "L 187.76335 144.734359 \n", "L 188.099832 143.181611 \n", "L 188.436314 144.650596 \n", "L 189.109279 140.643773 \n", "L 189.445762 143.771816 \n", "L 189.782244 148.480081 \n", "L 190.118726 144.255514 \n", "L 190.455209 145.710152 \n", "L 190.791691 150.267987 \n", "L 191.128174 144.286983 \n", "L 191.464656 150.004763 \n", "L 191.801139 145.110548 \n", "L 192.137621 149.138252 \n", "L 192.810586 144.91126 \n", "L 193.147068 145.185393 \n", "L 193.820033 146.3129 \n", "L 194.156515 146.000114 \n", "L 194.492998 145.965308 \n", "L 194.82948 151.628899 \n", "L 195.165963 148.462501 \n", "L 195.838927 150.655388 \n", "L 196.17541 145.354026 \n", "L 196.511892 150.699666 \n", "L 196.848375 152.911438 \n", "L 197.184857 146.058808 \n", "L 197.52134 146.26484 \n", "L 197.857822 145.779099 \n", "L 198.194304 147.9053 \n", "L 198.530787 146.748736 \n", "L 198.867269 144.914331 \n", "L 199.203752 149.78647 \n", "L 199.540234 147.821217 \n", "L 199.876716 148.246133 \n", "L 200.213199 145.019221 \n", "L 200.549681 148.432711 \n", "L 200.886164 147.077373 \n", "L 201.222646 153.539611 \n", "L 201.895611 149.143694 \n", "L 202.232093 152.484654 \n", "L 202.568576 154.079433 \n", "L 202.905058 151.045445 \n", "L 203.241541 149.47747 \n", "L 203.578023 149.138617 \n", "L 203.914505 148.437613 \n", "L 204.250988 153.708875 \n", "L 204.58747 149.647937 \n", "L 204.923953 152.18994 \n", "L 205.260435 149.811566 \n", "L 205.596917 149.221482 \n", "L 205.9334 146.465029 \n", "L 206.269882 145.931239 \n", "L 206.606365 149.746011 \n", "L 206.942847 149.683713 \n", "L 207.279329 148.552033 \n", "L 207.615812 149.384305 \n", "L 207.952294 149.802178 \n", "L 208.288777 147.910538 \n", "L 208.625259 149.651088 \n", "L 208.961742 148.655527 \n", "L 209.298224 149.049486 \n", "L 209.634706 154.190199 \n", "L 209.971189 154.244317 \n", "L 210.307671 158.46865 \n", "L 210.644154 151.345929 \n", "L 210.980636 148.319556 \n", "L 211.317118 151.090641 \n", "L 211.653601 147.200605 \n", "L 211.990083 155.361061 \n", "L 212.326566 152.076435 \n", "L 212.99953 152.708284 \n", "L 213.336013 154.07208 \n", "L 213.672495 147.710371 \n", "L 214.008978 148.547015 \n", "L 214.34546 150.093548 \n", "L 214.681943 150.1435 \n", "L 215.018425 144.972336 \n", "L 215.354907 144.579249 \n", "L 215.69139 150.6618 \n", "L 216.027872 150.445159 \n", "L 216.364355 150.765214 \n", "L 216.700837 150.479428 \n", "L 217.037319 151.411513 \n", "L 217.710284 149.190721 \n", "L 218.046767 146.987432 \n", "L 218.719731 149.604674 \n", "L 219.056214 153.657456 \n", "L 219.392696 153.096349 \n", "L 219.729179 144.613015 \n", "L 220.402144 147.803499 \n", "L 220.738626 143.767567 \n", "L 221.075108 145.602679 \n", "L 221.411591 152.081501 \n", "L 221.748073 150.470777 \n", "L 222.084556 153.010497 \n", "L 222.421038 146.909195 \n", "L 222.75752 147.896963 \n", "L 223.094003 144.817603 \n", "L 223.430485 148.535285 \n", "L 224.10345 145.574282 \n", "L 224.776415 144.581139 \n", "L 225.112897 145.801555 \n", "L 225.44938 152.19985 \n", "L 225.785862 150.784256 \n", "L 226.122345 152.960143 \n", "L 226.458827 146.19706 \n", "L 226.795309 145.05519 \n", "L 227.131792 145.674697 \n", "L 227.468274 146.955439 \n", "L 227.804757 147.567647 \n", "L 228.141239 146.841492 \n", "L 228.477721 146.940416 \n", "L 228.814204 145.746733 \n", "L 229.150686 143.104873 \n", "L 229.487169 143.359654 \n", "L 229.823651 142.188354 \n", "L 230.496616 150.253993 \n", "L 230.833098 143.82454 \n", "L 231.169581 141.439929 \n", "L 231.506063 144.514766 \n", "L 231.842546 145.114706 \n", "L 232.179028 151.366787 \n", "L 232.51551 147.214238 \n", "L 232.851993 145.302275 \n", "L 233.188475 144.492657 \n", "L 233.524958 141.099431 \n", "L 233.86144 133.963573 \n", "L 234.534405 144.433864 \n", "L 234.870887 142.057324 \n", "L 235.20737 145.537583 \n", "L 235.543852 143.415722 \n", "L 235.880334 143.579538 \n", "L 236.216817 141.387672 \n", "L 236.553299 134.977038 \n", "L 236.889782 138.607636 \n", "L 237.226264 138.981148 \n", "L 237.562747 139.782148 \n", "L 237.899229 141.682696 \n", "L 238.235711 141.932407 \n", "L 238.572194 144.233022 \n", "L 238.908676 135.144067 \n", "L 239.245159 140.584786 \n", "L 239.581641 126.991602 \n", "L 239.918123 140.754367 \n", "L 240.254606 140.489546 \n", "L 240.591088 140.708558 \n", "L 240.927571 143.297374 \n", "L 241.264053 150.4212 \n", "L 241.600535 137.298961 \n", "L 241.937018 136.033052 \n", "L 242.2735 138.272912 \n", "L 242.609983 125.44105 \n", "L 243.282948 135.994191 \n", "L 243.61943 137.799404 \n", "L 243.955912 121.29406 \n", "L 244.292395 124.91009 \n", "L 244.628877 125.750258 \n", "L 244.96536 136.617385 \n", "L 245.638324 125.813672 \n", "L 245.974807 130.572353 \n", "L 246.311289 126.368197 \n", "L 246.647772 115.623186 \n", "L 246.984254 127.262711 \n", "L 247.320736 127.897566 \n", "L 247.657219 138.215424 \n", "L 247.993701 122.158489 \n", "L 248.330184 126.991872 \n", "L 248.666666 122.799226 \n", "L 249.003149 125.304494 \n", "L 249.339631 109.298919 \n", "L 249.676113 118.753884 \n", "L 250.012596 110.61734 \n", "L 250.349078 115.792267 \n", "L 250.685561 124.380152 \n", "L 251.022043 117.910496 \n", "L 251.358525 114.337262 \n", "L 251.695008 114.621384 \n", "L 252.03149 106.838452 \n", "L 252.704455 104.885158 \n", "L 253.040938 102.744349 \n", "L 253.37742 101.594063 \n", "L 253.713902 100.886357 \n", "L 254.050385 101.522389 \n", "L 254.386867 115.762696 \n", "L 254.72335 112.147256 \n", "L 255.059832 114.766296 \n", "L 255.396314 116.427297 \n", "L 255.732797 111.119442 \n", "L 256.069279 119.869964 \n", "L 256.405762 102.041205 \n", "L 256.742244 103.990603 \n", "L 257.078726 102.071479 \n", "L 257.415209 103.223102 \n", "L 257.751691 93.46673 \n", "L 258.088174 102.576615 \n", "L 258.424656 120.29027 \n", "L 258.761139 116.59337 \n", "L 259.097621 101.751779 \n", "L 259.434103 105.207757 \n", "L 259.770586 100.773044 \n", "L 260.107068 102.734138 \n", "L 260.443551 101.558028 \n", "L 260.780033 107.168311 \n", "L 261.116515 98.87332 \n", "L 261.452998 107.821074 \n", "L 262.125963 91.718956 \n", "L 262.462445 100.038745 \n", "L 262.798927 88.961719 \n", "L 263.13541 95.421066 \n", "L 263.471892 94.321666 \n", "L 263.808375 95.646116 \n", "L 264.144857 90.473679 \n", "L 264.48134 82.808333 \n", "L 265.154304 90.746631 \n", "L 265.490787 89.75294 \n", "L 265.827269 101.669704 \n", "L 266.163752 96.795918 \n", "L 266.500234 99.811332 \n", "L 267.173199 85.550995 \n", "L 267.509681 82.927313 \n", "L 267.846164 76.23315 \n", "L 268.182646 74.604424 \n", "L 268.519128 89.624223 \n", "L 268.855611 88.268012 \n", "L 269.192093 75.346415 \n", "L 269.528576 74.93701 \n", "L 269.865058 68.926904 \n", "L 270.201541 59.08735 \n", "L 270.538023 84.191199 \n", "L 270.874505 87.507566 \n", "L 271.210988 79.403876 \n", "L 271.54747 83.641365 \n", "L 271.883953 76.094618 \n", "L 272.220435 77.664712 \n", "L 272.556917 70.23791 \n", "L 272.8934 68.018935 \n", "L 273.229882 76.370757 \n", "L 273.566365 68.886559 \n", "L 273.902847 75.588708 \n", "L 274.239329 69.258831 \n", "L 274.575812 79.124984 \n", "L 274.912294 84.592455 \n", "L 275.585259 70.328544 \n", "L 275.921742 74.396608 \n", "L 276.258224 70.936332 \n", "L 276.594706 71.64516 \n", "L 276.931189 73.411081 \n", "L 277.267671 62.123757 \n", "L 277.604154 59.459965 \n", "L 277.940636 70.665821 \n", "L 278.277118 60.023553 \n", "L 278.613601 76.583857 \n", "L 278.950083 60.819695 \n", "L 279.286566 66.165039 \n", "L 279.623048 60.873896 \n", "L 279.95953 58.362894 \n", "L 280.296013 63.934845 \n", "L 280.632495 63.672015 \n", "L 280.968978 56.375678 \n", "L 281.30546 59.694861 \n", "L 281.641943 60.74404 \n", "L 281.978425 48.920173 \n", "L 282.65139 65.655383 \n", "L 282.987872 45.614572 \n", "L 283.324355 55.233378 \n", "L 283.660837 47.197074 \n", "L 283.997319 59.540507 \n", "L 284.333802 58.577599 \n", "L 284.670284 59.158792 \n", "L 285.006767 56.677847 \n", "L 285.343249 48.941717 \n", "L 285.679731 62.283304 \n", "L 286.352696 48.442864 \n", "L 286.689179 55.351242 \n", "L 287.025661 53.22472 \n", "L 287.362144 40.679373 \n", "L 287.698626 56.768225 \n", "L 288.035108 54.783609 \n", "L 288.371591 68.874658 \n", "L 288.708073 51.175668 \n", "L 289.044556 58.735313 \n", "L 289.381038 57.721581 \n", "L 289.71752 46.161189 \n", "L 290.054003 53.702331 \n", "L 290.390485 55.10888 \n", "L 290.726968 60.233505 \n", "L 291.06345 55.870825 \n", "L 291.399932 47.094244 \n", "L 291.736415 52.492342 \n", "L 292.072897 43.911496 \n", "L 292.40938 41.885324 \n", "L 292.745862 50.61765 \n", "L 293.418827 36.839245 \n", "L 293.755309 51.788052 \n", "L 294.091792 49.906536 \n", "L 294.428274 49.393017 \n", "L 294.764757 36.480949 \n", "L 295.101239 32.533864 \n", "L 295.437721 48.311622 \n", "L 295.774204 37.309818 \n", "L 296.110686 41.855041 \n", "L 296.447169 44.118079 \n", "L 296.783651 22.625803 \n", "L 297.120133 34.464413 \n", "L 297.456616 35.989909 \n", "L 297.793098 44.754239 \n", "L 298.466063 39.106332 \n", "L 298.802546 28.528225 \n", "L 299.47551 47.422874 \n", "L 299.811993 39.16619 \n", "L 300.148475 36.738575 \n", "L 300.484958 45.676433 \n", "L 301.157922 30.760728 \n", "L 301.494405 31.597737 \n", "L 301.830887 50.581484 \n", "L 302.16737 52.505286 \n", "L 302.503852 24.791387 \n", "L 302.840334 35.688648 \n", "L 303.176817 53.500392 \n", "L 303.513299 36.844862 \n", "L 303.849782 36.025156 \n", "L 304.522747 54.473851 \n", "L 304.859229 33.372689 \n", "L 305.195711 52.365116 \n", "L 305.532194 36.564959 \n", "L 305.868676 38.357301 \n", "L 306.205159 53.337237 \n", "L 306.541641 30.299508 \n", "L 307.214606 42.9797 \n", "L 307.551088 42.827067 \n", "L 308.224053 36.717202 \n", "L 308.560535 49.700982 \n", "L 308.897018 55.540525 \n", "L 309.2335 31.492063 \n", "L 309.906465 40.287752 \n", "L 310.242948 27.614887 \n", "L 310.915912 39.622363 \n", "L 311.252395 34.393372 \n", "L 311.588877 51.82122 \n", "L 311.92536 29.405237 \n", "L 312.261842 34.752908 \n", "L 312.598324 52.340012 \n", "L 312.934807 53.266965 \n", "L 313.271289 32.844097 \n", "L 313.607772 38.618887 \n", "L 313.944254 48.317487 \n", "L 314.280736 34.799928 \n", "L 314.617219 44.921224 \n", "L 314.953701 34.30546 \n", "L 315.626666 46.488404 \n", "L 315.963149 45.00635 \n", "L 316.299631 35.243497 \n", "L 316.636113 44.896426 \n", "L 316.972596 49.803476 \n", "L 317.309078 31.246619 \n", "L 317.645561 41.208403 \n", "L 317.982043 39.499634 \n", "L 318.318525 27.362732 \n", "L 318.655008 26.372365 \n", "L 318.99149 45.617271 \n", "L 319.327973 53.916974 \n", "L 319.664455 35.48282 \n", "L 320.000938 31.347144 \n", "L 320.33742 38.223382 \n", "L 320.673902 31.7001 \n", "L 321.010385 37.642022 \n", "L 321.346867 33.447348 \n", "L 321.68335 32.093709 \n", "L 322.019832 21.112912 \n", "L 322.356314 28.325104 \n", "L 322.692797 30.208757 \n", "L 323.029279 47.407475 \n", "L 323.365762 23.512852 \n", "L 323.702244 26.95425 \n", "L 324.038726 43.852116 \n", "L 324.375209 33.596045 \n", "L 324.711691 35.310839 \n", "L 325.048174 26.662917 \n", "L 325.384656 31.272361 \n", "L 325.721139 44.48348 \n", "L 326.057621 43.78415 \n", "L 326.394103 45.062791 \n", "L 326.730586 32.788754 \n", "L 327.067068 37.963371 \n", "L 327.403551 32.407691 \n", "L 327.740033 29.329958 \n", "L 328.076515 41.073277 \n", "L 328.412998 44.081738 \n", "L 328.74948 41.695205 \n", "L 329.085963 38.239692 \n", "L 329.422445 36.41678 \n", "L 329.758927 35.517863 \n", "L 330.09541 37.821188 \n", "L 330.431892 50.858221 \n", "L 330.768375 35.996656 \n", "L 331.104857 37.03408 \n", "L 331.44134 47.660181 \n", "L 331.777822 45.177562 \n", "L 332.114304 51.373468 \n", "L 332.450787 37.353045 \n", "L 332.787269 39.06708 \n", "L 333.123752 39.174863 \n", "L 333.460234 49.771162 \n", "L 333.796716 37.675605 \n", "L 334.133199 35.46446 \n", "L 334.469681 38.543127 \n", "L 334.806164 50.969057 \n", "L 335.142646 27.458968 \n", "L 335.479128 37.835945 \n", "L 335.815611 40.485165 \n", "L 336.152093 46.400998 \n", "L 336.488576 33.562067 \n", "L 337.161541 54.111305 \n", "L 337.498023 44.718153 \n", "L 337.834505 53.987938 \n", "L 338.170988 50.831837 \n", "L 338.50747 37.408942 \n", "L 338.843953 41.120957 \n", "L 339.180435 41.656625 \n", "L 339.516917 38.175282 \n", "L 339.8534 51.708469 \n", "L 340.189882 52.63005 \n", "L 340.526365 52.783952 \n", "L 340.862847 49.375861 \n", "L 341.535812 49.010467 \n", "L 341.872294 40.208096 \n", "L 342.208777 46.542124 \n", "L 342.545259 45.888045 \n", "L 342.881742 38.017963 \n", "L 343.218224 41.770134 \n", "L 343.554706 50.876691 \n", "L 343.891189 48.826873 \n", "L 344.227671 31.487351 \n", "L 344.564154 75.662797 \n", "L 345.237118 54.231288 \n", "L 345.573601 63.492451 \n", "L 345.910083 57.439391 \n", "L 346.246566 48.48132 \n", "L 346.583048 57.082726 \n", "L 346.91953 54.691101 \n", "L 347.256013 44.929266 \n", "L 347.928978 71.989633 \n", "L 348.26546 71.543059 \n", "L 348.601943 65.581016 \n", "L 348.938425 66.642873 \n", "L 349.274907 46.96987 \n", "L 349.61139 61.994583 \n", "L 349.947872 61.207665 \n", "L 350.284355 75.917494 \n", "L 351.293802 59.079268 \n", "L 351.966767 66.238961 \n", "L 352.303249 67.544766 \n", "L 352.639731 63.645032 \n", "L 352.976214 53.566381 \n", "L 353.312696 56.535452 \n", "L 353.985661 55.078615 \n", "L 354.322144 63.14924 \n", "L 354.658626 74.443829 \n", "L 354.995108 60.590897 \n", "L 355.331591 57.942143 \n", "L 355.668073 69.725453 \n", "L 356.004556 87.578081 \n", "L 356.341038 74.296113 \n", "L 356.67752 75.244304 \n", "L 357.014003 72.810291 \n", "L 357.350485 62.155848 \n", "L 357.686968 78.547888 \n", "L 358.02345 82.774315 \n", "L 358.359932 81.442587 \n", "L 358.696415 77.40074 \n", "L 359.032897 87.623483 \n", "L 359.36938 64.630498 \n", "L 359.705862 67.076995 \n", "L 360.378827 80.801099 \n", "L 361.388274 60.13693 \n", "L 361.724757 81.115023 \n", "L 362.061239 67.441437 \n", "L 362.397721 71.161833 \n", "L 362.734204 78.201317 \n", "L 363.070686 71.321071 \n", "L 363.407169 78.744294 \n", "L 363.743651 75.635228 \n", "L 364.080133 76.132695 \n", "L 364.416616 85.119687 \n", "L 364.753098 80.405783 \n", "L 365.089581 73.268339 \n", "L 365.426063 80.636753 \n", "L 365.762546 85.462338 \n", "L 366.099028 112.513213 \n", "L 366.43551 98.875462 \n", "L 366.771993 91.868884 \n", "L 366.771993 91.868884 \n", "\" clip-path=\"url(#p5912583a97)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #bf00bf; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_23\">\n", "    <path d=\"M 57.208174 59.040367 \n", "L 57.881139 60.134242 \n", "L 58.217621 57.821442 \n", "L 58.554103 58.445982 \n", "L 58.890586 57.105098 \n", "L 59.227068 53.5367 \n", "L 59.900033 82.147235 \n", "L 60.572998 69.681063 \n", "L 60.90948 71.009995 \n", "L 61.245963 70.328471 \n", "L 61.582445 64.76461 \n", "L 61.918927 82.07076 \n", "L 62.591892 63.34565 \n", "L 62.928375 60.247047 \n", "L 63.60134 73.144705 \n", "L 63.937822 78.498249 \n", "L 64.274304 67.03271 \n", "L 64.947269 55.178907 \n", "L 65.283752 58.057155 \n", "L 65.620234 56.8287 \n", "L 65.956716 53.373545 \n", "L 66.293199 53.823747 \n", "L 66.629681 50.846561 \n", "L 66.966164 64.430419 \n", "L 67.639128 56.398426 \n", "L 67.975611 58.707477 \n", "L 68.312093 59.589165 \n", "L 68.648576 58.290734 \n", "L 68.985058 55.142117 \n", "L 69.321541 56.784955 \n", "L 69.994505 53.427101 \n", "L 70.330988 57.374183 \n", "L 70.66747 53.254434 \n", "L 71.003953 57.118082 \n", "L 71.340435 57.900601 \n", "L 71.676917 53.280015 \n", "L 72.0134 52.474004 \n", "L 72.349882 54.716519 \n", "L 72.686365 54.635069 \n", "L 73.022847 55.617011 \n", "L 73.359329 54.737235 \n", "L 73.695812 53.457679 \n", "L 74.032294 53.00353 \n", "L 74.368777 52.878416 \n", "L 74.705259 50.539559 \n", "L 75.041742 52.609425 \n", "L 75.378224 50.88997 \n", "L 75.714706 52.068341 \n", "L 76.051189 51.954996 \n", "L 76.387671 51.202727 \n", "L 76.724154 49.630124 \n", "L 77.060636 53.348962 \n", "L 77.397118 49.382316 \n", "L 77.733601 47.211572 \n", "L 78.070083 47.759166 \n", "L 78.406566 45.624237 \n", "L 78.743048 50.252041 \n", "L 79.07953 53.140042 \n", "L 79.416013 49.011467 \n", "L 79.752495 52.83338 \n", "L 80.088978 52.755891 \n", "L 80.42546 53.90442 \n", "L 80.761943 49.674281 \n", "L 81.098425 49.414266 \n", "L 81.434907 48.94446 \n", "L 81.77139 46.785962 \n", "L 82.444355 54.372447 \n", "L 82.780837 49.31787 \n", "L 83.117319 48.431354 \n", "L 83.453802 48.453 \n", "L 83.790284 46.969655 \n", "L 84.126767 48.314008 \n", "L 84.463249 44.44765 \n", "L 84.799731 44.655326 \n", "L 85.136214 50.645216 \n", "L 85.472696 45.88772 \n", "L 85.809179 47.466961 \n", "L 86.145661 49.757707 \n", "L 86.482144 49.941852 \n", "L 86.818626 48.449232 \n", "L 87.155108 47.50312 \n", "L 87.491591 48.837527 \n", "L 87.828073 47.819116 \n", "L 88.164556 48.74577 \n", "L 88.501038 48.718532 \n", "L 88.83752 44.16902 \n", "L 89.174003 45.067634 \n", "L 89.510485 48.711351 \n", "L 89.846968 43.188565 \n", "L 90.18345 44.837326 \n", "L 90.519932 48.979422 \n", "L 90.856415 46.817784 \n", "L 91.192897 48.300156 \n", "L 91.52938 42.270132 \n", "L 92.202345 46.901492 \n", "L 92.538827 42.918532 \n", "L 92.875309 43.555093 \n", "L 93.211792 44.988168 \n", "L 93.548274 44.854031 \n", "L 93.884757 46.624266 \n", "L 94.221239 45.099095 \n", "L 94.557721 42.727374 \n", "L 94.894204 43.550516 \n", "L 95.230686 41.826053 \n", "L 95.903651 46.48736 \n", "L 96.240133 42.520521 \n", "L 96.576616 41.618792 \n", "L 96.913098 48.54139 \n", "L 97.586063 40.211642 \n", "L 97.922546 40.988366 \n", "L 98.259028 45.679624 \n", "L 98.931993 40.593214 \n", "L 99.268475 41.53844 \n", "L 99.604958 45.77857 \n", "L 99.94144 40.045405 \n", "L 100.277922 42.249321 \n", "L 100.614405 46.188055 \n", "L 100.950887 42.355809 \n", "L 101.28737 40.51481 \n", "L 101.623852 42.734971 \n", "L 101.960334 43.32433 \n", "L 102.296817 44.735321 \n", "L 102.633299 42.202472 \n", "L 102.969782 42.433867 \n", "L 103.306264 45.918776 \n", "L 103.642747 44.423786 \n", "L 103.979229 37.975232 \n", "L 104.315711 40.663839 \n", "L 104.652194 40.90166 \n", "L 104.988676 39.11631 \n", "L 105.325159 41.765268 \n", "L 105.998123 43.93768 \n", "L 106.334606 44.243124 \n", "L 106.671088 43.868591 \n", "L 107.007571 44.870265 \n", "L 107.344053 44.120056 \n", "L 107.680535 46.115336 \n", "L 108.017018 48.826304 \n", "L 108.3535 45.329556 \n", "L 108.689983 44.072828 \n", "L 109.026465 49.11771 \n", "L 109.362948 45.125589 \n", "L 109.69943 38.603007 \n", "L 110.035912 40.848732 \n", "L 110.372395 39.864787 \n", "L 110.708877 39.928573 \n", "L 111.04536 40.450057 \n", "L 111.381842 44.922095 \n", "L 111.718324 46.894741 \n", "L 112.391289 42.244226 \n", "L 112.727772 44.001528 \n", "L 113.064254 35.838768 \n", "L 113.400736 41.523859 \n", "L 113.737219 41.803517 \n", "L 114.073701 38.716728 \n", "L 114.410184 45.46315 \n", "L 114.746666 45.915537 \n", "L 115.083149 43.432007 \n", "L 115.419631 44.868999 \n", "L 115.756113 42.329035 \n", "L 116.092596 44.995565 \n", "L 116.429078 41.594863 \n", "L 116.765561 41.389735 \n", "L 117.102043 40.252341 \n", "L 117.438525 40.571842 \n", "L 117.775008 46.37442 \n", "L 118.11149 43.896594 \n", "L 118.447973 43.734117 \n", "L 118.784455 44.787221 \n", "L 119.120937 45.070209 \n", "L 119.45742 44.290177 \n", "L 119.793902 44.611596 \n", "L 120.130385 42.155066 \n", "L 120.466867 42.939675 \n", "L 120.80335 42.422305 \n", "L 121.139832 46.640004 \n", "L 121.476314 43.47662 \n", "L 121.812797 41.780656 \n", "L 122.149279 47.678289 \n", "L 122.485762 40.176803 \n", "L 123.158726 46.15113 \n", "L 123.831691 44.125705 \n", "L 124.168174 42.445585 \n", "L 124.504656 45.697784 \n", "L 124.841139 41.806591 \n", "L 125.177621 42.850675 \n", "L 125.514103 42.714473 \n", "L 125.850586 44.838753 \n", "L 126.187068 45.310167 \n", "L 126.523551 46.64034 \n", "L 126.860033 44.773237 \n", "L 127.196515 48.818579 \n", "L 127.532998 45.771633 \n", "L 127.86948 47.495702 \n", "L 128.205963 49.868397 \n", "L 128.542445 47.888238 \n", "L 128.878927 50.688384 \n", "L 129.21541 45.831203 \n", "L 129.551892 47.983693 \n", "L 129.888375 50.860479 \n", "L 130.224857 47.505261 \n", "L 130.56134 46.949358 \n", "L 130.897822 43.847553 \n", "L 131.234304 50.315999 \n", "L 131.907269 42.123849 \n", "L 132.243752 45.303347 \n", "L 132.580234 46.414259 \n", "L 133.253199 45.457493 \n", "L 133.589681 46.477455 \n", "L 133.926164 46.979437 \n", "L 134.262646 46.582527 \n", "L 134.599128 49.221572 \n", "L 134.935611 50.614601 \n", "L 135.272093 49.192409 \n", "L 135.608576 51.111338 \n", "L 135.945058 47.628205 \n", "L 136.281541 50.944489 \n", "L 136.618023 48.423676 \n", "L 136.954505 47.965679 \n", "L 137.290988 53.728197 \n", "L 137.62747 45.632341 \n", "L 137.963953 52.436933 \n", "L 138.300435 53.446362 \n", "L 138.636917 46.575981 \n", "L 138.9734 52.265547 \n", "L 139.309882 55.607715 \n", "L 139.646365 47.338229 \n", "L 139.982847 52.156622 \n", "L 140.655812 53.741141 \n", "L 140.992294 51.15505 \n", "L 141.328777 51.119997 \n", "L 141.665259 50.652612 \n", "L 142.001742 48.776563 \n", "L 142.338224 53.445982 \n", "L 142.674706 48.206159 \n", "L 143.011189 52.590628 \n", "L 144.020636 56.061904 \n", "L 144.357118 54.075041 \n", "L 144.693601 53.02782 \n", "L 145.030083 56.528234 \n", "L 145.366566 54.717241 \n", "L 145.703048 56.867743 \n", "L 146.03953 55.294764 \n", "L 146.376013 51.643793 \n", "L 146.712495 52.822208 \n", "L 147.048978 49.321134 \n", "L 147.38546 55.197515 \n", "L 148.058425 49.588983 \n", "L 148.394907 56.852571 \n", "L 148.73139 49.910303 \n", "L 149.067872 53.944324 \n", "L 149.404355 55.670947 \n", "L 149.740837 56.634212 \n", "L 150.077319 53.336048 \n", "L 150.413802 65.331699 \n", "L 150.750284 66.007745 \n", "L 151.086767 75.212051 \n", "L 151.759731 58.31032 \n", "L 152.096214 56.934681 \n", "L 152.432696 63.937861 \n", "L 152.769179 74.664034 \n", "L 153.105661 65.339275 \n", "L 153.442144 65.070109 \n", "L 153.778626 58.877306 \n", "L 154.115108 57.129822 \n", "L 154.451591 60.038594 \n", "L 154.788073 56.576392 \n", "L 155.124556 68.447717 \n", "L 155.461038 63.894189 \n", "L 155.79752 60.897384 \n", "L 156.134003 59.545686 \n", "L 156.470485 56.143029 \n", "L 156.806968 60.760741 \n", "L 157.14345 59.963494 \n", "L 157.479932 60.436017 \n", "L 157.816415 61.793059 \n", "L 158.152897 70.250486 \n", "L 158.48938 66.648439 \n", "L 158.825862 61.510683 \n", "L 159.162345 60.326699 \n", "L 159.498827 55.600894 \n", "L 159.835309 62.515293 \n", "L 160.171792 65.644449 \n", "L 160.508274 78.290561 \n", "L 160.844757 77.503665 \n", "L 161.181239 80.483914 \n", "L 161.517721 78.976623 \n", "L 161.854204 80.284123 \n", "L 162.190686 94.462298 \n", "L 162.527169 97.007739 \n", "L 162.863651 127.795006 \n", "L 163.200133 121.745914 \n", "L 163.536616 109.205777 \n", "L 163.873098 116.215419 \n", "L 164.209581 105.111413 \n", "L 164.546063 118.236292 \n", "L 165.219028 102.205698 \n", "L 165.55551 93.856142 \n", "L 165.891993 92.718633 \n", "L 166.228475 88.943293 \n", "L 166.564958 107.163672 \n", "L 166.90144 106.312878 \n", "L 167.237922 110.163621 \n", "L 167.574405 105.692369 \n", "L 167.910887 110.710019 \n", "L 168.24737 98.456943 \n", "L 168.920334 115.124384 \n", "L 169.256817 117.867732 \n", "L 169.593299 115.256961 \n", "L 169.929782 95.949816 \n", "L 170.266264 100.371027 \n", "L 171.275711 121.61019 \n", "L 171.612194 110.468533 \n", "L 172.285159 129.24488 \n", "L 172.621641 129.078377 \n", "L 172.958123 125.816886 \n", "L 173.294606 103.868763 \n", "L 173.631088 106.021823 \n", "L 174.304053 132.347349 \n", "L 174.640535 127.565136 \n", "L 174.977018 138.960042 \n", "L 175.3135 139.016909 \n", "L 175.649983 138.182432 \n", "L 175.986465 131.628637 \n", "L 176.65943 139.483156 \n", "L 176.995912 140.508548 \n", "L 177.332395 142.073175 \n", "L 177.668877 142.001193 \n", "L 178.00536 143.332044 \n", "L 178.341842 143.170027 \n", "L 178.678324 141.840263 \n", "L 179.351289 142.551407 \n", "L 179.687772 143.574399 \n", "L 180.024254 143.807391 \n", "L 180.360736 142.793199 \n", "L 180.697219 143.193938 \n", "L 181.033701 141.543281 \n", "L 181.370184 143.495695 \n", "L 181.706666 143.479126 \n", "L 182.043149 142.830233 \n", "L 182.716113 143.995412 \n", "L 183.052596 143.721699 \n", "L 183.389078 143.898079 \n", "L 184.062043 143.102364 \n", "L 184.398525 143.487573 \n", "L 184.735008 142.949202 \n", "L 185.07149 143.716046 \n", "L 185.407973 143.819547 \n", "L 185.744455 143.31894 \n", "L 186.080938 143.245087 \n", "L 186.41742 143.885605 \n", "L 186.753902 143.965998 \n", "L 187.090385 142.713533 \n", "L 187.426867 142.893225 \n", "L 187.76335 143.493689 \n", "L 188.099832 143.678487 \n", "L 188.772797 144.417827 \n", "L 189.109279 144.252348 \n", "L 189.782244 144.286538 \n", "L 190.118726 144.649123 \n", "L 190.791691 144.25105 \n", "L 191.128174 144.419596 \n", "L 191.801139 144.339441 \n", "L 192.137621 144.218426 \n", "L 192.474103 144.299945 \n", "L 193.147068 143.988472 \n", "L 193.483551 144.197279 \n", "L 193.820033 144.615393 \n", "L 194.156515 144.369483 \n", "L 194.492998 144.384502 \n", "L 194.82948 144.767942 \n", "L 195.165963 144.398763 \n", "L 195.502445 144.701183 \n", "L 195.838927 144.466922 \n", "L 196.17541 144.640074 \n", "L 196.511892 144.584771 \n", "L 196.848375 144.364234 \n", "L 198.530787 144.441019 \n", "L 198.867269 144.86858 \n", "L 199.203752 144.724686 \n", "L 199.540234 144.719813 \n", "L 199.876716 144.843476 \n", "L 200.213199 144.480497 \n", "L 200.886164 145.034026 \n", "L 201.222646 144.575857 \n", "L 201.559128 144.436249 \n", "L 201.895611 144.433061 \n", "L 202.232093 144.576445 \n", "L 202.568576 144.535118 \n", "L 202.905058 144.369796 \n", "L 203.241541 144.711578 \n", "L 204.250988 144.410941 \n", "L 204.58747 144.599652 \n", "L 204.923953 144.566769 \n", "L 205.260435 145.02134 \n", "L 205.596917 144.940791 \n", "L 205.9334 144.732856 \n", "L 206.606365 145.135852 \n", "L 207.279329 144.761064 \n", "L 207.952294 144.664317 \n", "L 208.288777 145.05592 \n", "L 208.625259 144.843666 \n", "L 208.961742 144.936043 \n", "L 210.307671 144.435198 \n", "L 210.644154 144.724369 \n", "L 210.980636 144.771735 \n", "L 211.317118 144.685438 \n", "L 211.990083 144.772855 \n", "L 212.326566 144.638221 \n", "L 212.663048 144.737222 \n", "L 213.336013 144.702617 \n", "L 213.672495 145.104005 \n", "L 214.008978 145.170486 \n", "L 214.34546 145.489874 \n", "L 215.018425 144.65428 \n", "L 215.354907 144.852923 \n", "L 215.69139 144.616422 \n", "L 216.027872 145.152848 \n", "L 216.700837 144.980233 \n", "L 217.373802 145.132045 \n", "L 217.710284 144.710976 \n", "L 218.046767 144.62686 \n", "L 218.383249 144.788578 \n", "L 218.719731 144.804794 \n", "L 219.056214 144.448051 \n", "L 219.392696 144.301954 \n", "L 219.729179 144.772556 \n", "L 220.402144 144.862325 \n", "L 221.748073 144.734209 \n", "L 222.084556 144.55919 \n", "L 222.75752 144.747685 \n", "L 223.094003 145.06959 \n", "L 223.430485 145.079189 \n", "L 223.766968 144.463588 \n", "L 224.10345 144.412239 \n", "L 224.439932 144.590614 \n", "L 224.776415 144.310361 \n", "L 225.112897 144.375238 \n", "L 225.44938 144.902058 \n", "L 225.785862 144.872771 \n", "L 226.122345 145.022591 \n", "L 226.458827 144.639312 \n", "L 227.131792 144.393463 \n", "L 227.468274 144.603657 \n", "L 228.141239 144.417415 \n", "L 228.814204 144.322813 \n", "L 229.150686 144.408975 \n", "L 229.487169 144.913944 \n", "L 229.823651 144.896277 \n", "L 230.160133 145.022325 \n", "L 230.496616 144.589152 \n", "L 230.833098 144.34269 \n", "L 232.179028 144.529523 \n", "L 232.851993 144.438109 \n", "L 233.188475 144.222456 \n", "L 233.86144 144.125345 \n", "L 234.534405 144.774179 \n", "L 235.20737 144.005508 \n", "L 235.543852 144.262335 \n", "L 235.880334 144.351728 \n", "L 236.216817 144.850577 \n", "L 237.562747 144.056204 \n", "L 237.899229 143.542628 \n", "L 238.235711 143.735989 \n", "L 238.572194 144.231545 \n", "L 238.908676 144.144507 \n", "L 239.245159 144.358552 \n", "L 240.254606 144.073379 \n", "L 240.591088 143.6224 \n", "L 242.609983 144.258195 \n", "L 242.946465 143.779836 \n", "L 243.282948 143.853794 \n", "L 243.61943 142.968992 \n", "L 243.955912 143.792382 \n", "L 244.292395 143.967092 \n", "L 244.628877 143.967416 \n", "L 244.96536 144.192067 \n", "L 245.301842 144.739187 \n", "L 245.974807 143.469132 \n", "L 246.311289 143.682688 \n", "L 246.647772 142.428955 \n", "L 246.984254 142.741179 \n", "L 247.320736 143.291531 \n", "L 247.657219 143.56771 \n", "L 248.666666 138.283832 \n", "L 249.003149 143.131804 \n", "L 249.339631 143.296185 \n", "L 249.676113 142.197038 \n", "L 250.012596 142.088997 \n", "L 250.349078 140.654018 \n", "L 250.685561 131.901917 \n", "L 251.022043 138.932119 \n", "L 251.358525 140.087498 \n", "L 251.695008 143.512731 \n", "L 252.367973 141.198176 \n", "L 252.704455 136.646964 \n", "L 253.040938 137.292326 \n", "L 253.37742 124.742187 \n", "L 253.713902 126.14623 \n", "L 254.050385 116.757921 \n", "L 254.386867 121.178665 \n", "L 254.72335 134.52142 \n", "L 255.059832 131.525662 \n", "L 255.732797 122.482913 \n", "L 256.069279 111.507762 \n", "L 256.742244 105.728411 \n", "L 257.078726 102.424983 \n", "L 257.751691 99.014411 \n", "L 258.088174 99.054967 \n", "L 258.424656 116.054529 \n", "L 258.761139 118.762652 \n", "L 259.097621 124.053525 \n", "L 259.434103 125.611273 \n", "L 259.770586 118.476264 \n", "L 260.107068 126.922575 \n", "L 260.443551 111.024819 \n", "L 261.116515 99.551099 \n", "L 261.452998 99.750837 \n", "L 261.78948 92.797993 \n", "L 262.125963 97.805497 \n", "L 262.462445 120.304232 \n", "L 262.798927 126.189391 \n", "L 263.13541 113.448487 \n", "L 263.471892 106.66198 \n", "L 263.808375 95.920885 \n", "L 264.48134 101.50032 \n", "L 264.817822 107.197971 \n", "L 265.154304 99.571248 \n", "L 265.490787 106.353419 \n", "L 265.827269 100.353401 \n", "L 266.163752 88.988216 \n", "L 266.500234 93.281184 \n", "L 266.836716 81.329885 \n", "L 267.173199 86.666768 \n", "L 267.509681 87.004852 \n", "L 267.846164 88.581096 \n", "L 268.182646 84.259177 \n", "L 268.519128 70.472288 \n", "L 268.855611 69.275141 \n", "L 269.192093 75.758632 \n", "L 269.528576 79.356933 \n", "L 269.865058 94.707763 \n", "L 270.201541 93.56421 \n", "L 270.538023 96.669108 \n", "L 270.874505 87.841357 \n", "L 271.54747 66.938112 \n", "L 271.883953 60.440894 \n", "L 272.220435 59.221827 \n", "L 272.556917 70.544708 \n", "L 272.8934 75.086656 \n", "L 273.229882 63.619119 \n", "L 273.566365 61.110342 \n", "L 274.239329 52.788913 \n", "L 274.575812 62.557915 \n", "L 274.912294 68.865919 \n", "L 275.248777 65.033108 \n", "L 275.585259 67.948007 \n", "L 275.921742 60.505264 \n", "L 276.258224 60.223286 \n", "L 276.594706 57.458955 \n", "L 276.931189 56.490533 \n", "L 277.267671 59.386835 \n", "L 277.604154 56.82766 \n", "L 277.940636 59.357394 \n", "L 278.277118 56.998132 \n", "L 278.613601 60.648071 \n", "L 278.950083 65.692804 \n", "L 279.623048 58.836552 \n", "L 279.95953 58.986165 \n", "L 280.296013 57.577428 \n", "L 280.632495 57.911251 \n", "L 280.968978 58.661216 \n", "L 281.30546 54.708097 \n", "L 281.641943 54.024713 \n", "L 281.978425 56.645489 \n", "L 282.314907 52.698268 \n", "L 282.65139 59.137259 \n", "L 282.987872 53.779696 \n", "L 283.324355 55.439931 \n", "L 283.997319 52.100273 \n", "L 284.333802 54.387572 \n", "L 284.670284 54.02386 \n", "L 285.006767 51.679726 \n", "L 285.343249 52.93021 \n", "L 285.679731 53.037435 \n", "L 286.016214 48.909304 \n", "L 286.689179 54.271393 \n", "L 287.025661 47.701314 \n", "L 287.362144 51.201833 \n", "L 287.698626 48.177102 \n", "L 288.035108 51.496788 \n", "L 288.371591 51.542408 \n", "L 288.708073 52.002764 \n", "L 289.044556 51.739124 \n", "L 289.381038 48.965118 \n", "L 289.71752 53.349207 \n", "L 290.054003 50.373534 \n", "L 290.390485 48.395866 \n", "L 290.726968 51.068969 \n", "L 291.06345 49.768448 \n", "L 291.399932 45.640577 \n", "L 291.736415 51.071441 \n", "L 292.072897 49.958071 \n", "L 292.40938 55.104518 \n", "L 292.745862 49.938318 \n", "L 293.082345 52.440719 \n", "L 293.418827 52.104029 \n", "L 293.755309 47.433608 \n", "L 294.091792 50.425831 \n", "L 294.428274 50.379902 \n", "L 294.764757 52.044696 \n", "L 295.101239 50.910993 \n", "L 295.437721 48.255441 \n", "L 295.774204 50.110988 \n", "L 296.110686 46.655727 \n", "L 296.447169 45.664455 \n", "L 296.783651 48.516834 \n", "L 297.456616 43.754389 \n", "L 297.793098 48.887297 \n", "L 298.129581 47.762007 \n", "L 298.466063 47.794431 \n", "L 298.802546 44.29966 \n", "L 299.139028 42.703776 \n", "L 299.47551 47.435504 \n", "L 299.811993 42.881206 \n", "L 300.484958 46.010901 \n", "L 300.82144 38.843068 \n", "L 301.157922 42.633673 \n", "L 301.494405 42.389506 \n", "L 301.830887 44.721367 \n", "L 302.16737 44.524551 \n", "L 302.503852 44.092669 \n", "L 302.840334 41.049544 \n", "L 303.513299 46.314114 \n", "L 303.849782 43.413807 \n", "L 304.186264 43.501939 \n", "L 304.522747 46.602423 \n", "L 304.859229 43.389991 \n", "L 305.195711 41.268487 \n", "L 305.532194 41.617151 \n", "L 305.868676 47.329647 \n", "L 306.205159 47.827026 \n", "L 306.541641 40.204143 \n", "L 307.214606 48.978922 \n", "L 307.551088 42.345914 \n", "L 307.887571 43.046794 \n", "L 308.560535 49.002815 \n", "L 308.897018 42.728968 \n", "L 309.2335 49.148004 \n", "L 309.569983 44.001893 \n", "L 309.906465 43.809918 \n", "L 310.242948 49.269909 \n", "L 310.57943 41.252578 \n", "L 311.252395 45.513938 \n", "L 311.588877 44.629208 \n", "L 312.261842 43.460652 \n", "L 312.598324 47.755202 \n", "L 312.934807 49.430985 \n", "L 313.271289 42.428031 \n", "L 313.607772 44.225869 \n", "L 313.944254 44.836101 \n", "L 314.280736 39.411874 \n", "L 314.617219 41.952882 \n", "L 314.953701 43.634326 \n", "L 315.290184 41.690774 \n", "L 315.626666 48.049383 \n", "L 315.963149 41.241348 \n", "L 316.299631 42.714918 \n", "L 316.636113 48.741838 \n", "L 316.972596 48.116237 \n", "L 317.309078 42.740358 \n", "L 317.982043 47.590697 \n", "L 318.318525 42.187672 \n", "L 318.655008 46.014374 \n", "L 318.99149 42.712737 \n", "L 319.664455 46.563296 \n", "L 320.000938 45.743202 \n", "L 320.33742 43.210916 \n", "L 320.673902 46.440552 \n", "L 321.010385 47.743538 \n", "L 321.346867 41.67834 \n", "L 321.68335 45.275676 \n", "L 322.019832 44.283721 \n", "L 322.356314 39.702182 \n", "L 322.692797 39.686394 \n", "L 323.029279 45.591434 \n", "L 323.365762 47.858396 \n", "L 323.702244 43.010821 \n", "L 324.038726 42.562687 \n", "L 324.375209 44.336103 \n", "L 324.711691 40.878796 \n", "L 325.048174 42.775324 \n", "L 325.384656 41.652792 \n", "L 325.721139 41.19908 \n", "L 326.057621 37.769842 \n", "L 326.394103 39.69975 \n", "L 326.730586 39.911511 \n", "L 327.067068 45.369723 \n", "L 327.403551 38.728195 \n", "L 327.740033 39.874601 \n", "L 328.076515 45.580281 \n", "L 328.412998 40.785241 \n", "L 328.74948 42.149788 \n", "L 329.085963 39.826232 \n", "L 329.422445 40.956062 \n", "L 329.758927 45.107586 \n", "L 330.09541 44.632061 \n", "L 330.431892 45.871461 \n", "L 330.768375 42.55161 \n", "L 331.104857 44.099843 \n", "L 331.777822 40.176811 \n", "L 332.114304 44.191319 \n", "L 332.450787 44.905497 \n", "L 333.123752 44.051058 \n", "L 333.796716 42.874474 \n", "L 334.133199 43.314595 \n", "L 334.469681 47.580551 \n", "L 334.806164 43.027559 \n", "L 335.142646 43.66195 \n", "L 335.479128 47.333334 \n", "L 335.815611 45.755253 \n", "L 336.152093 48.301837 \n", "L 336.488576 44.236938 \n", "L 336.825058 44.71525 \n", "L 337.161541 44.504605 \n", "L 337.498023 47.294754 \n", "L 337.834505 43.613938 \n", "L 338.170988 43.07742 \n", "L 338.50747 44.270591 \n", "L 338.843953 47.665531 \n", "L 339.180435 40.506166 \n", "L 339.516917 43.78702 \n", "L 339.8534 44.583487 \n", "L 340.189882 45.671571 \n", "L 340.526365 42.303201 \n", "L 341.199329 49.267513 \n", "L 341.535812 45.80766 \n", "L 341.872294 49.780433 \n", "L 342.208777 48.91507 \n", "L 342.545259 44.413639 \n", "L 342.881742 45.747236 \n", "L 343.218224 45.204547 \n", "L 343.554706 43.526204 \n", "L 343.891189 48.244958 \n", "L 344.564154 49.093733 \n", "L 344.900636 48.678573 \n", "L 345.237118 48.615666 \n", "L 345.573601 48.380431 \n", "L 345.910083 45.160872 \n", "L 346.246566 47.217528 \n", "L 346.583048 46.694986 \n", "L 346.91953 43.888537 \n", "L 347.256013 45.417848 \n", "L 347.592495 48.147103 \n", "L 347.928978 47.310594 \n", "L 348.26546 42.295415 \n", "L 348.601943 57.630488 \n", "L 349.274907 50.33673 \n", "L 349.61139 54.888992 \n", "L 350.284355 48.732337 \n", "L 350.620837 51.669298 \n", "L 350.957319 50.37682 \n", "L 351.293802 47.02553 \n", "L 351.966767 56.512526 \n", "L 352.303249 57.537815 \n", "L 352.639731 55.423493 \n", "L 352.976214 56.120467 \n", "L 353.312696 50.236482 \n", "L 353.649179 53.773736 \n", "L 353.985661 52.62311 \n", "L 354.322144 58.067958 \n", "L 354.658626 57.437721 \n", "L 354.995108 55.271331 \n", "L 355.331591 54.01348 \n", "L 355.668073 53.954821 \n", "L 356.341038 55.578464 \n", "L 356.67752 54.514882 \n", "L 357.014003 51.347711 \n", "L 357.350485 52.124315 \n", "L 358.02345 50.359445 \n", "L 358.359932 53.402208 \n", "L 358.696415 58.019052 \n", "L 359.032897 53.434056 \n", "L 359.36938 52.882617 \n", "L 359.705862 56.574062 \n", "L 360.042345 64.054025 \n", "L 360.378827 60.90307 \n", "L 360.715309 60.803005 \n", "L 361.051792 59.093598 \n", "L 361.388274 54.043223 \n", "L 361.724757 60.183469 \n", "L 362.061239 63.350694 \n", "L 362.397721 65.295807 \n", "L 362.734204 62.533672 \n", "L 363.070686 68.466792 \n", "L 363.407169 56.789664 \n", "L 363.743651 56.95539 \n", "L 364.080133 58.219321 \n", "L 364.416616 61.305848 \n", "L 365.426063 54.984845 \n", "L 365.762546 60.708892 \n", "L 366.099028 56.383286 \n", "L 366.43551 57.661785 \n", "L 366.771993 60.218913 \n", "L 367.108475 57.887519 \n", "L 367.444958 60.925658 \n", "L 367.78144 59.552845 \n", "L 368.117922 59.833912 \n", "L 368.454405 65.880811 \n", "L 368.790887 64.024679 \n", "L 369.12737 59.773667 \n", "L 369.463852 62.096902 \n", "L 369.800334 66.288954 \n", "L 370.136817 105.935425 \n", "L 370.473299 102.397017 \n", "L 370.809782 92.126516 \n", "L 370.809782 92.126516 \n", "\" clip-path=\"url(#p5912583a97)\" style=\"fill: none; stroke-dasharray: 9.6,2.4,1.5,2.4; stroke-dashoffset: 0; stroke: #008000; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_24\">\n", "    <path d=\"M 73.359329 47.90407 \n", "L 74.368777 47.863546 \n", "L 75.041742 47.834682 \n", "L 75.378224 47.76173 \n", "L 75.714706 48.216382 \n", "L 76.051189 48.960872 \n", "L 76.387671 48.474978 \n", "L 76.724154 48.286909 \n", "L 77.397118 48.305929 \n", "L 77.733601 48.114162 \n", "L 78.070083 48.915636 \n", "L 78.406566 48.331511 \n", "L 78.743048 48.06825 \n", "L 79.07953 47.961146 \n", "L 80.088978 48.653367 \n", "L 80.42546 48.191709 \n", "L 81.098425 47.802594 \n", "L 81.77139 47.800945 \n", "L 82.780837 47.642036 \n", "L 83.117319 48.047465 \n", "L 83.790284 47.876238 \n", "L 84.799731 47.866114 \n", "L 85.472696 47.836703 \n", "L 85.809179 47.698316 \n", "L 86.145661 47.693888 \n", "L 86.482144 47.817343 \n", "L 86.818626 47.729325 \n", "L 87.491591 47.835142 \n", "L 88.501038 47.705056 \n", "L 91.52938 47.613664 \n", "L 92.538827 47.631305 \n", "L 94.894204 47.51927 \n", "L 95.230686 47.617318 \n", "L 95.567169 47.590092 \n", "L 95.903651 47.680543 \n", "L 98.259028 47.548499 \n", "L 98.59551 47.661825 \n", "L 100.277922 47.48755 \n", "L 100.950887 47.43596 \n", "L 101.28737 47.51032 \n", "L 101.623852 47.435843 \n", "L 102.633299 47.547664 \n", "L 105.661641 47.46187 \n", "L 105.998123 47.37201 \n", "L 107.344053 47.51749 \n", "L 108.017018 47.417191 \n", "L 109.69943 47.385234 \n", "L 110.372395 47.419744 \n", "L 111.718324 47.338302 \n", "L 112.391289 47.362994 \n", "L 122.149279 47.350961 \n", "L 124.504656 47.449688 \n", "L 126.187068 47.303602 \n", "L 126.860033 47.183145 \n", "L 128.878927 47.381492 \n", "L 129.21541 47.192931 \n", "L 129.888375 47.220693 \n", "L 130.224857 47.209219 \n", "L 130.56134 47.367509 \n", "L 132.243752 47.379187 \n", "L 133.253199 47.270183 \n", "L 133.589681 47.253212 \n", "L 134.262646 47.345545 \n", "L 134.935611 47.416046 \n", "L 136.954505 47.315416 \n", "L 137.62747 47.36233 \n", "L 143.011189 47.42825 \n", "L 143.347671 47.503601 \n", "L 144.020636 47.48999 \n", "L 145.030083 47.583585 \n", "L 145.703048 47.523548 \n", "L 146.712495 47.519383 \n", "L 147.048978 47.441876 \n", "L 147.38546 47.514073 \n", "L 148.058425 47.399506 \n", "L 149.404355 47.436248 \n", "L 151.759731 47.615717 \n", "L 152.096214 47.54491 \n", "L 152.769179 47.526462 \n", "L 155.124556 47.652175 \n", "L 155.461038 47.701354 \n", "L 155.79752 47.577042 \n", "L 156.134003 47.671443 \n", "L 157.14345 47.652561 \n", "L 158.48938 47.636269 \n", "L 158.825862 47.554798 \n", "L 159.498827 47.665287 \n", "L 160.508274 47.745558 \n", "L 161.517721 47.727884 \n", "L 162.190686 47.769878 \n", "L 163.200133 47.566108 \n", "L 163.536616 47.683567 \n", "L 164.209581 47.636543 \n", "L 164.546063 47.776987 \n", "L 164.882546 47.612209 \n", "L 165.891993 47.789507 \n", "L 166.228475 47.742739 \n", "L 166.564958 48.113381 \n", "L 166.90144 48.146002 \n", "L 167.237922 48.481153 \n", "L 168.24737 47.838468 \n", "L 168.583852 48.065493 \n", "L 168.920334 48.455874 \n", "L 169.256817 48.136078 \n", "L 169.593299 48.117645 \n", "L 169.929782 47.896612 \n", "L 170.266264 47.838898 \n", "L 170.602747 47.932905 \n", "L 170.939229 47.830637 \n", "L 171.275711 48.237708 \n", "L 171.948676 47.983452 \n", "L 172.958123 47.940882 \n", "L 173.967571 47.997673 \n", "L 174.304053 48.299612 \n", "L 175.649983 47.788559 \n", "L 176.322948 48.133058 \n", "L 176.65943 48.627556 \n", "L 176.995912 48.585821 \n", "L 177.332395 48.791962 \n", "L 177.668877 48.677636 \n", "L 178.00536 48.782654 \n", "L 178.678324 56.271958 \n", "L 179.014807 144.292373 \n", "L 179.351289 144.287588 \n", "L 179.687772 142.783822 \n", "L 180.024254 144.248603 \n", "L 180.360736 114.913662 \n", "L 180.697219 144.274509 \n", "L 181.033701 143.95747 \n", "L 181.370184 88.686168 \n", "L 181.706666 52.246231 \n", "L 182.043149 51.505972 \n", "L 182.379631 50.136377 \n", "L 182.716113 129.568571 \n", "L 183.052596 125.540025 \n", "L 183.389078 143.509879 \n", "L 183.725561 119.081846 \n", "L 184.062043 143.663529 \n", "L 184.398525 60.584157 \n", "L 184.735008 131.370487 \n", "L 185.07149 144.231888 \n", "L 185.744455 144.24121 \n", "L 186.080938 54.459626 \n", "L 186.41742 67.728758 \n", "L 186.753902 134.668935 \n", "L 187.090385 144.235309 \n", "L 187.426867 144.286859 \n", "L 187.76335 143.683695 \n", "L 188.099832 144.279819 \n", "L 189.109279 144.291553 \n", "L 189.445762 103.869248 \n", "L 189.782244 118.801757 \n", "L 190.118726 144.276471 \n", "L 192.474103 144.293839 \n", "L 271.883953 144.288091 \n", "L 272.220435 143.965684 \n", "L 272.556917 139.18598 \n", "L 272.8934 118.836187 \n", "L 273.229882 90.71279 \n", "L 273.902847 61.953059 \n", "L 274.239329 62.040115 \n", "L 274.575812 144.249799 \n", "L 276.258224 144.292016 \n", "L 276.594706 143.912478 \n", "L 276.931189 116.335886 \n", "L 277.267671 63.767667 \n", "L 277.604154 66.046628 \n", "L 277.940636 51.713805 \n", "L 278.277118 57.249251 \n", "L 278.613601 144.282748 \n", "L 278.950083 144.291863 \n", "L 279.286566 144.189565 \n", "L 279.623048 125.021112 \n", "L 279.95953 54.140319 \n", "L 280.296013 62.239924 \n", "L 280.632495 83.270112 \n", "L 280.968978 131.238669 \n", "L 281.30546 65.746236 \n", "L 281.641943 123.160217 \n", "L 281.978425 72.166884 \n", "L 282.314907 50.165259 \n", "L 282.65139 51.718638 \n", "L 282.987872 48.88637 \n", "L 283.324355 49.579456 \n", "L 283.660837 49.655953 \n", "L 283.997319 50.010489 \n", "L 284.670284 48.311608 \n", "L 285.006767 48.270391 \n", "L 285.679731 48.714276 \n", "L 286.016214 52.856944 \n", "L 286.352696 52.193999 \n", "L 286.689179 55.46877 \n", "L 287.025661 49.844052 \n", "L 287.362144 48.49642 \n", "L 288.035108 47.956809 \n", "L 288.371591 47.912915 \n", "L 288.708073 48.308978 \n", "L 289.044556 48.472779 \n", "L 289.381038 48.079571 \n", "L 290.390485 47.714167 \n", "L 291.06345 48.256225 \n", "L 291.399932 48.122868 \n", "L 291.736415 48.226759 \n", "L 292.072897 47.958699 \n", "L 294.764757 47.952699 \n", "L 295.101239 48.133762 \n", "L 296.447169 47.836163 \n", "L 297.456616 47.80003 \n", "L 298.466063 47.676476 \n", "L 298.802546 47.875564 \n", "L 299.139028 47.742524 \n", "L 299.811993 47.718098 \n", "L 300.484958 47.713054 \n", "L 308.224053 47.534617 \n", "L 308.560535 47.694483 \n", "L 308.897018 47.627228 \n", "L 309.569983 47.63407 \n", "L 309.906465 47.543633 \n", "L 310.915912 47.613259 \n", "L 311.92536 47.59085 \n", "L 312.598324 47.462308 \n", "L 313.271289 47.43759 \n", "L 314.280736 47.460692 \n", "L 314.953701 47.459503 \n", "L 315.963149 47.323976 \n", "L 316.636113 47.423217 \n", "L 316.972596 47.305061 \n", "L 318.655008 47.403923 \n", "L 319.327973 47.356494 \n", "L 321.346867 47.33534 \n", "L 322.019832 47.390206 \n", "L 322.692797 47.384155 \n", "L 323.365762 47.41787 \n", "L 323.702244 47.312286 \n", "L 324.711691 47.470517 \n", "L 325.048174 47.401877 \n", "L 325.384656 47.525062 \n", "L 326.057621 47.399247 \n", "L 326.394103 47.47415 \n", "L 326.730586 47.347238 \n", "L 327.403551 47.381959 \n", "L 328.412998 47.384994 \n", "L 329.085963 47.492729 \n", "L 330.431892 47.244769 \n", "L 331.104857 47.315572 \n", "L 331.44134 47.296884 \n", "L 331.777822 47.4415 \n", "L 332.114304 47.347387 \n", "L 334.133199 47.437375 \n", "L 334.469681 47.330256 \n", "L 334.806164 47.42515 \n", "L 335.479128 47.390993 \n", "L 337.161541 47.445694 \n", "L 337.498023 47.367874 \n", "L 337.834505 47.428987 \n", "L 338.843953 47.271251 \n", "L 339.8534 47.400983 \n", "L 340.526365 47.377739 \n", "L 340.862847 47.262297 \n", "L 341.872294 47.297227 \n", "L 342.545259 47.226777 \n", "L 342.881742 47.197519 \n", "L 343.218224 47.329476 \n", "L 343.891189 47.294163 \n", "L 344.227671 47.339491 \n", "L 344.564154 47.239473 \n", "L 344.900636 47.319475 \n", "L 345.573601 47.277204 \n", "L 346.91953 47.394269 \n", "L 347.592495 47.310218 \n", "L 348.26546 47.328042 \n", "L 349.947872 47.346833 \n", "L 351.293802 47.405447 \n", "L 352.639731 47.440497 \n", "L 357.686968 47.438342 \n", "L 358.02345 47.552423 \n", "L 360.378827 47.484731 \n", "L 361.388274 47.549691 \n", "L 364.416616 47.411834 \n", "L 364.753098 47.727589 \n", "L 365.426063 47.668077 \n", "L 365.762546 47.751646 \n", "L 366.43551 47.590814 \n", "L 367.108475 47.575864 \n", "L 367.444958 47.527297 \n", "L 368.790887 47.802357 \n", "L 369.12737 47.808054 \n", "L 369.800334 47.685769 \n", "L 370.136817 47.600939 \n", "L 370.473299 47.805212 \n", "L 371.482747 47.766917 \n", "L 372.155711 47.711712 \n", "L 372.828676 47.749946 \n", "L 374.174606 47.584439 \n", "L 375.184053 47.736564 \n", "L 375.857018 47.774758 \n", "L 376.1935 48.069355 \n", "L 376.866465 47.975563 \n", "L 377.53943 47.750734 \n", "L 378.548877 48.128207 \n", "L 378.88536 48.035411 \n", "L 379.221842 48.241049 \n", "L 379.558324 47.866559 \n", "L 380.231289 47.83594 \n", "L 380.567772 47.971832 \n", "L 381.577219 47.793676 \n", "L 381.913701 47.901379 \n", "L 382.250184 47.791203 \n", "L 382.923149 47.936775 \n", "L 383.259631 47.865202 \n", "L 383.596113 47.966843 \n", "L 384.269078 47.934842 \n", "L 384.605561 48.1404 \n", "L 385.615008 48.005383 \n", "L 385.95149 48.155466 \n", "L 386.287973 118.599781 \n", "L 386.624455 93.74331 \n", "L 386.960938 51.352001 \n", "L 386.960938 51.352001 \n", "\" clip-path=\"url(#p5912583a97)\" style=\"fill: none; stroke-dasharray: 1.5,2.475; stroke-dashoffset: 0; stroke: #ff0000; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"patch_3\">\n", "    <path d=\"M 52.**********.52 \n", "L 52.160938 7.2 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_4\">\n", "    <path d=\"M 386.**********.52 \n", "L 386.960938 7.2 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_5\">\n", "    <path d=\"M 52.**********.52 \n", "L 386.**********.52 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_6\">\n", "    <path d=\"M 52.160938 7.2 \n", "L 386.960938 7.2 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"legend_1\">\n", "    <g id=\"patch_7\">\n", "     <path d=\"M 278.873438 168.52 \n", "L 379.960938 168.52 \n", "Q 381.960938 168.52 381.960938 166.52 \n", "L 381.960938 108.8075 \n", "Q 381.960938 106.8075 379.960938 106.8075 \n", "L 278.873438 106.8075 \n", "Q 276.873438 106.8075 276.873438 108.8075 \n", "L 276.873438 166.52 \n", "Q 276.873438 168.52 278.873438 168.52 \n", "z\n", "\" style=\"fill: #ffffff; opacity: 0.8; stroke: #cccccc; stroke-linejoin: miter\"/>\n", "    </g>\n", "    <g id=\"line2d_25\">\n", "     <path d=\"M 280.873438 114.905938 \n", "L 290.873438 114.905938 \n", "L 300.873438 114.905938 \n", "\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "    </g>\n", "    <g id=\"text_13\">\n", "     <!-- 1-step preds -->\n", "     <g transform=\"translate(308.873438 118.405938)scale(0.1 -0.1)\">\n", "      <defs>\n", "       <path id=\"DejaVuSans-2d\" d=\"M 313 2009 \n", "L 1997 2009 \n", "L 1997 1497 \n", "L 313 1497 \n", "L 313 2009 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-73\" d=\"M 2834 3397 \n", "L 2834 2853 \n", "Q 2591 2978 2328 3040 \n", "Q 2066 3103 1784 3103 \n", "Q 1356 3103 1142 2972 \n", "Q 928 2841 928 2578 \n", "Q 928 2378 1081 2264 \n", "Q 1234 2150 1697 2047 \n", "L 1894 2003 \n", "Q 2506 1872 2764 1633 \n", "Q 3022 1394 3022 966 \n", "Q 3022 478 2636 193 \n", "Q 2250 -91 1575 -91 \n", "Q 1294 -91 989 -36 \n", "Q 684 19 347 128 \n", "L 347 722 \n", "Q 666 556 975 473 \n", "Q 1284 391 1588 391 \n", "Q 1994 391 2212 530 \n", "Q 2431 669 2431 922 \n", "Q 2431 1156 2273 1281 \n", "Q 2116 1406 1581 1522 \n", "L 1381 1569 \n", "Q 847 1681 609 1914 \n", "Q 372 2147 372 2553 \n", "Q 372 3047 722 3315 \n", "Q 1072 3584 1716 3584 \n", "Q 2034 3584 2315 3537 \n", "Q 2597 3491 2834 3397 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-70\" d=\"M 1159 525 \n", "L 1159 -1331 \n", "L 581 -1331 \n", "L 581 3500 \n", "L 1159 3500 \n", "L 1159 2969 \n", "Q 1341 3281 1617 3432 \n", "Q 1894 3584 2278 3584 \n", "Q 2916 3584 3314 3078 \n", "Q 3713 2572 3713 1747 \n", "Q 3713 922 3314 415 \n", "Q 2916 -91 2278 -91 \n", "Q 1894 -91 1617 61 \n", "Q 1341 213 1159 525 \n", "z\n", "M 3116 1747 \n", "Q 3116 2381 2855 2742 \n", "Q 2594 3103 2138 3103 \n", "Q 1681 3103 1420 2742 \n", "Q 1159 2381 1159 1747 \n", "Q 1159 1113 1420 752 \n", "Q 1681 391 2138 391 \n", "Q 2594 391 2855 752 \n", "Q 3116 1113 3116 1747 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-20\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-72\" d=\"M 2631 2963 \n", "Q 2534 3019 2420 3045 \n", "Q 2306 3072 2169 3072 \n", "Q 1681 3072 1420 2755 \n", "Q 1159 2438 1159 1844 \n", "L 1159 0 \n", "L 581 0 \n", "L 581 3500 \n", "L 1159 3500 \n", "L 1159 2956 \n", "Q 1341 3275 1631 3429 \n", "Q 1922 3584 2338 3584 \n", "Q 2397 3584 2469 3576 \n", "Q 2541 3569 2628 3553 \n", "L 2631 2963 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-64\" d=\"M 2906 2969 \n", "L 2906 4863 \n", "L 3481 4863 \n", "L 3481 0 \n", "L 2906 0 \n", "L 2906 525 \n", "Q 2725 213 2448 61 \n", "Q 2172 -91 1784 -91 \n", "Q 1150 -91 751 415 \n", "Q 353 922 353 1747 \n", "Q 353 2572 751 3078 \n", "Q 1150 3584 1784 3584 \n", "Q 2172 3584 2448 3432 \n", "Q 2725 3281 2906 2969 \n", "z\n", "M 947 1747 \n", "Q 947 1113 1208 752 \n", "Q 1469 391 1925 391 \n", "Q 2381 391 2643 752 \n", "Q 2906 1113 2906 1747 \n", "Q 2906 2381 2643 2742 \n", "Q 2381 3103 1925 3103 \n", "Q 1469 3103 1208 2742 \n", "Q 947 2381 947 1747 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "      </defs>\n", "      <use xlink:href=\"#DejaVuSans-31\"/>\n", "      <use xlink:href=\"#DejaVuSans-2d\" x=\"63.623047\"/>\n", "      <use xlink:href=\"#DejaVuSans-73\" x=\"99.707031\"/>\n", "      <use xlink:href=\"#DejaVuSans-74\" x=\"151.806641\"/>\n", "      <use xlink:href=\"#DejaVuSans-65\" x=\"191.015625\"/>\n", "      <use xlink:href=\"#DejaVuSans-70\" x=\"252.539062\"/>\n", "      <use xlink:href=\"#DejaVuSans-20\" x=\"316.015625\"/>\n", "      <use xlink:href=\"#DejaVuSans-70\" x=\"347.802734\"/>\n", "      <use xlink:href=\"#DejaVuSans-72\" x=\"411.279297\"/>\n", "      <use xlink:href=\"#DejaVuSans-65\" x=\"450.142578\"/>\n", "      <use xlink:href=\"#DejaVuSans-64\" x=\"511.666016\"/>\n", "      <use xlink:href=\"#DejaVuSans-73\" x=\"575.142578\"/>\n", "     </g>\n", "    </g>\n", "    <g id=\"line2d_26\">\n", "     <path d=\"M 280.873438 129.584063 \n", "L 290.873438 129.584063 \n", "L 300.873438 129.584063 \n", "\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #bf00bf; stroke-width: 1.5\"/>\n", "    </g>\n", "    <g id=\"text_14\">\n", "     <!-- 4-step preds -->\n", "     <g transform=\"translate(308.873438 133.084063)scale(0.1 -0.1)\">\n", "      <use xlink:href=\"#DejaVuSans-34\"/>\n", "      <use xlink:href=\"#DejaVuSans-2d\" x=\"63.623047\"/>\n", "      <use xlink:href=\"#DejaVuSans-73\" x=\"99.707031\"/>\n", "      <use xlink:href=\"#DejaVuSans-74\" x=\"151.806641\"/>\n", "      <use xlink:href=\"#DejaVuSans-65\" x=\"191.015625\"/>\n", "      <use xlink:href=\"#DejaVuSans-70\" x=\"252.539062\"/>\n", "      <use xlink:href=\"#DejaVuSans-20\" x=\"316.015625\"/>\n", "      <use xlink:href=\"#DejaVuSans-70\" x=\"347.802734\"/>\n", "      <use xlink:href=\"#DejaVuSans-72\" x=\"411.279297\"/>\n", "      <use xlink:href=\"#DejaVuSans-65\" x=\"450.142578\"/>\n", "      <use xlink:href=\"#DejaVuSans-64\" x=\"511.666016\"/>\n", "      <use xlink:href=\"#DejaVuSans-73\" x=\"575.142578\"/>\n", "     </g>\n", "    </g>\n", "    <g id=\"line2d_27\">\n", "     <path d=\"M 280.873438 144.262188 \n", "L 290.873438 144.262188 \n", "L 300.873438 144.262188 \n", "\" style=\"fill: none; stroke-dasharray: 9.6,2.4,1.5,2.4; stroke-dashoffset: 0; stroke: #008000; stroke-width: 1.5\"/>\n", "    </g>\n", "    <g id=\"text_15\">\n", "     <!-- 16-step preds -->\n", "     <g transform=\"translate(308.873438 147.762188)scale(0.1 -0.1)\">\n", "      <use xlink:href=\"#DejaVuSans-31\"/>\n", "      <use xlink:href=\"#DejaVuSans-36\" x=\"63.623047\"/>\n", "      <use xlink:href=\"#DejaVuSans-2d\" x=\"127.246094\"/>\n", "      <use xlink:href=\"#DejaVuSans-73\" x=\"163.330078\"/>\n", "      <use xlink:href=\"#DejaVuSans-74\" x=\"215.429688\"/>\n", "      <use xlink:href=\"#DejaVuSans-65\" x=\"254.638672\"/>\n", "      <use xlink:href=\"#DejaVuSans-70\" x=\"316.162109\"/>\n", "      <use xlink:href=\"#DejaVuSans-20\" x=\"379.638672\"/>\n", "      <use xlink:href=\"#DejaVuSans-70\" x=\"411.425781\"/>\n", "      <use xlink:href=\"#DejaVuSans-72\" x=\"474.902344\"/>\n", "      <use xlink:href=\"#DejaVuSans-65\" x=\"513.765625\"/>\n", "      <use xlink:href=\"#DejaVuSans-64\" x=\"575.289062\"/>\n", "      <use xlink:href=\"#DejaVuSans-73\" x=\"638.765625\"/>\n", "     </g>\n", "    </g>\n", "    <g id=\"line2d_28\">\n", "     <path d=\"M 280.873438 158.940313 \n", "L 290.873438 158.940313 \n", "L 300.873438 158.940313 \n", "\" style=\"fill: none; stroke-dasharray: 1.5,2.475; stroke-dashoffset: 0; stroke: #ff0000; stroke-width: 1.5\"/>\n", "    </g>\n", "    <g id=\"text_16\">\n", "     <!-- 64-step preds -->\n", "     <g transform=\"translate(308.873438 162.440313)scale(0.1 -0.1)\">\n", "      <use xlink:href=\"#DejaVuSans-36\"/>\n", "      <use xlink:href=\"#DejaVuSans-34\" x=\"63.623047\"/>\n", "      <use xlink:href=\"#DejaVuSans-2d\" x=\"127.246094\"/>\n", "      <use xlink:href=\"#DejaVuSans-73\" x=\"163.330078\"/>\n", "      <use xlink:href=\"#DejaVuSans-74\" x=\"215.429688\"/>\n", "      <use xlink:href=\"#DejaVuSans-65\" x=\"254.638672\"/>\n", "      <use xlink:href=\"#DejaVuSans-70\" x=\"316.162109\"/>\n", "      <use xlink:href=\"#DejaVuSans-20\" x=\"379.638672\"/>\n", "      <use xlink:href=\"#DejaVuSans-70\" x=\"411.425781\"/>\n", "      <use xlink:href=\"#DejaVuSans-72\" x=\"474.902344\"/>\n", "      <use xlink:href=\"#DejaVuSans-65\" x=\"513.765625\"/>\n", "      <use xlink:href=\"#DejaVuSans-64\" x=\"575.289062\"/>\n", "      <use xlink:href=\"#DejaVuSans-73\" x=\"638.765625\"/>\n", "     </g>\n", "    </g>\n", "   </g>\n", "  </g>\n", " </g>\n", " <defs>\n", "  <clipPath id=\"p5912583a97\">\n", "   <rect x=\"52.160938\" y=\"7.2\" width=\"334.8\" height=\"166.32\"/>\n", "  </clipPath>\n", " </defs>\n", "</svg>\n"], "text/plain": ["<Figure size 600x300 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["steps = (1, 4, 16, 64)\n", "d2l.plot([time[tau + i - 1: T - max_steps + i] for i in steps],\n", "         [features[:, tau + i - 1].detach().numpy() for i in steps], 'time', 'x',\n", "         legend=[f'{i}-step preds' for i in steps], xlim=[5, 1000],\n", "         figsize=(6, 3))"]}, {"cell_type": "markdown", "id": "ebcb82ec", "metadata": {"origin_pos": 34}, "source": ["以上例子清楚地说明了当我们试图预测更远的未来时，预测的质量是如何变化的。\n", "虽然“$4$步预测”看起来仍然不错，但超过这个跨度的任何预测几乎都是无用的。\n", "\n", "## 小结\n", "\n", "* 内插法（在现有观测值之间进行估计）和外推法（对超出已知观测范围进行预测）在实践的难度上差别很大。因此，对于所拥有的序列数据，在训练时始终要尊重其时间顺序，即最好不要基于未来的数据进行训练。\n", "* 序列模型的估计需要专门的统计工具，两种较流行的选择是自回归模型和隐变量自回归模型。\n", "* 对于时间是向前推进的因果模型，正向估计通常比反向估计更容易。\n", "* 对于直到时间步$t$的观测序列，其在时间步$t+k$的预测输出是“$k$步预测”。随着我们对预测时间$k$值的增加，会造成误差的快速累积和预测质量的极速下降。\n", "\n", "## 练习\n", "\n", "1. 改进本节实验中的模型。\n", "    1. 是否包含了过去$4$个以上的观测结果？真实值需要是多少个？\n", "    1. 如果没有噪音，需要多少个过去的观测结果？提示：把$\\sin$和$\\cos$写成微分方程。\n", "    1. 可以在保持特征总数不变的情况下合并旧的观察结果吗？这能提高正确度吗？为什么？\n", "    1. 改变神经网络架构并评估其性能。\n", "1. 一位投资者想要找到一种好的证券来购买。他查看过去的回报，以决定哪一种可能是表现良好的。这一策略可能会出什么问题呢？\n", "1. 时间是向前推进的因果模型在多大程度上适用于文本呢？\n", "1. 举例说明什么时候可能需要隐变量自回归模型来捕捉数据的动力学模型。\n"]}, {"cell_type": "markdown", "id": "85f49a55", "metadata": {"origin_pos": 36, "tab": ["pytorch"]}, "source": ["[Discussions](https://discuss.d2l.ai/t/2091)\n"]}], "metadata": {"language_info": {"name": "python"}}, "nbformat": 4, "nbformat_minor": 5}