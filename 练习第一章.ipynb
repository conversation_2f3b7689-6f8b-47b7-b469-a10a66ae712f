{"cells": [{"cell_type": "markdown", "id": "77eb689f", "metadata": {}, "source": ["1. 证明一个矩阵A的转置的转置是A，即(A\n", "⊤)\n", "⊤ = A。\n"]}, {"cell_type": "code", "execution_count": 5, "id": "186c9de8", "metadata": {}, "outputs": [{"data": {"text/plain": ["tensor([[ 0,  1,  2,  3],\n", "        [ 4,  5,  6,  7],\n", "        [ 8,  9, 10, 11]])"]}, "execution_count": 5, "metadata": {}, "output_type": "execute_result"}], "source": ["import torch\n", "\n", "A = torch.arange(12)\n", "A = A.reshape(3,4)\n", "A"]}, {"cell_type": "code", "execution_count": 8, "id": "2675c17b", "metadata": {}, "outputs": [{"data": {"text/plain": ["tensor([[True, True, True, True],\n", "        [True, True, True, True],\n", "        [True, True, True, True]])"]}, "execution_count": 8, "metadata": {}, "output_type": "execute_result"}], "source": ["B = A.T\n", "C = B.T\n", "A == C"]}, {"cell_type": "markdown", "id": "2b27d47b", "metadata": {}, "source": ["2. 给出两个矩阵A和B，证明“它们转置的和”等于“它们和的转置”，即A\n", "⊤ + B\n", "⊤ = (A + B)\n", "⊤。"]}, {"cell_type": "code", "execution_count": 17, "id": "5cc890bc", "metadata": {}, "outputs": [{"data": {"text/plain": ["True"]}, "execution_count": 17, "metadata": {}, "output_type": "execute_result"}], "source": ["A = torch.tensor([[1,2,3,4],[5,6,7,8],[9,10,11,12]])\n", "B = torch.tensor([[2, 1, 4, 3], [1, 2, 3, 4], [4, 3, 2, 1]])\n", "(A+B).T == A.T + B.T\n", "are_equal = torch.allclose((A+B).T, A.T + B.T)\n", "are_equal"]}, {"cell_type": "markdown", "id": "656250b7", "metadata": {}, "source": ["4. 本节中定义了形状(2, 3, 4)的张量X。len(X)的输出结果是什么？"]}, {"cell_type": "code", "execution_count": 23, "id": "06d04b39", "metadata": {}, "outputs": [{"data": {"text/plain": ["2"]}, "execution_count": 23, "metadata": {}, "output_type": "execute_result"}], "source": ["X = torch.arange(24).reshape(2, 3, 4)\n", "len(X)"]}, {"cell_type": "markdown", "id": "7473e9be", "metadata": {}, "source": ["6. 运行A/A.sum(axis=1)，看看会发生什么。请分析一下原因？"]}, {"cell_type": "code", "execution_count": 30, "id": "0535b022", "metadata": {}, "outputs": [{"data": {"text/plain": ["(tensor([[ 0,  1,  2,  3],\n", "         [ 4,  5,  6,  7],\n", "         [ 8,  9, 10, 11]]),\n", " tensor([ 6, 22, 38]),\n", " tensor([[0.0000, 0.1667, 0.3333, 0.5000],\n", "         [0.1818, 0.2273, 0.2727, 0.3182],\n", "         [0.2105, 0.2368, 0.2632, 0.2895]]))"]}, "execution_count": 30, "metadata": {}, "output_type": "execute_result"}], "source": ["Y = torch.arange(12).reshape(3,4)\n", "Y1 = Y.sum(axis=1)\n", "Y,Y1,Y/Y.sum(axis=1,keepdim=True)"]}, {"cell_type": "markdown", "id": "80dccdcf", "metadata": {}, "source": ["7. 考虑一个具有形状(2, 3, 4)的张量，在轴0、1、2上的求和输出是什么形状?\n"]}, {"cell_type": "code", "execution_count": 36, "id": "d2d9a2bf", "metadata": {}, "outputs": [{"data": {"text/plain": ["(tensor([[[ 0,  1,  2,  3],\n", "          [ 4,  5,  6,  7],\n", "          [ 8,  9, 10, 11]],\n", " \n", "         [[12, 13, 14, 15],\n", "          [16, 17, 18, 19],\n", "          [20, 21, 22, 23]]]),\n", " torch.<PERSON><PERSON>([3, 4]),\n", " torch.<PERSON><PERSON>([2, 4]),\n", " torch.<PERSON><PERSON>([2, 3]))"]}, "execution_count": 36, "metadata": {}, "output_type": "execute_result"}], "source": ["Y = torch.arange(24).reshape(2,3,4)\n", "Y1 = Y.sum(axis=0)\n", "Y2 = Y.sum(axis=1)\n", "Y3 = Y.sum(axis=2)\n", "Y,Y1.shape,Y2.shape,Y3.shape"]}, {"cell_type": "markdown", "id": "c9c845ac", "metadata": {}, "source": ["8. 为linalg.norm函数提供3个或更多轴的张量，并观察其输出。对于任意形状的张量这个函数计算得到\n", "什么?\n"]}, {"cell_type": "code", "execution_count": null, "id": "849287ec", "metadata": {}, "outputs": [{"data": {"text/plain": ["tensor([ 9.8995, 19.6214])"]}, "execution_count": 40, "metadata": {}, "output_type": "execute_result"}], "source": ["A = torch.tensor([\n", "    [3.0, 4.0],\n", "    [5.0, 12.0],\n", "    [8.0, 15.0]\n", "]) \n", "torch.linalg.norm(A,dim=-1)"]}, {"cell_type": "code", "execution_count": null, "id": "1dc1e883", "metadata": {}, "outputs": [{"data": {"text/plain": ["tensor([2.4495, 2.4495, 2.4495, 2.4495])"]}, "execution_count": 47, "metadata": {}, "output_type": "execute_result"}], "source": ["A1 = torch.ones(2, 3, 4)\n", "torch.linalg.norm(A1,dim=(0,1)) #以0,1维度求范数：把两个矩阵看做整体，沿行方向计算范数（列）"]}, {"cell_type": "code", "execution_count": null, "id": "6e9a3ef0", "metadata": {}, "outputs": [{"data": {"text/plain": ["(tensor([[[1., 1., 1., 1.],\n", "          [1., 1., 1., 1.],\n", "          [1., 1., 1., 1.]],\n", " \n", "         [[1., 1., 1., 1.],\n", "          [1., 1., 1., 1.],\n", "          [1., 1., 1., 1.]]]),\n", " tensor([[1.4142, 1.4142, 1.4142, 1.4142],\n", "         [1.4142, 1.4142, 1.4142, 1.4142],\n", "         [1.4142, 1.4142, 1.4142, 1.4142]]))"]}, "execution_count": 49, "metadata": {}, "output_type": "execute_result"}], "source": ["A1 = torch.ones(2, 3, 4)\n", "A1,torch.linalg.norm(A1,dim=0) #收集两组矩阵在各个位置对应的数，然后组合求范数"]}, {"cell_type": "code", "execution_count": null, "id": "137ede1c", "metadata": {}, "outputs": [{"data": {"text/plain": ["tensor(4.8990)"]}, "execution_count": 55, "metadata": {}, "output_type": "execute_result"}], "source": ["A2 = torch.ones(2, 3, 4)\n", "torch.linalg.norm(A2) #取每个元素求范数"]}, {"cell_type": "code", "execution_count": 56, "id": "dbfccc4e", "metadata": {}, "outputs": [{"data": {"text/plain": ["tensor([3.4641, 3.4641])"]}, "execution_count": 56, "metadata": {}, "output_type": "execute_result"}], "source": ["A2 = torch.ones(2, 3, 4)\n", "torch.linalg.norm(A2,dim=(1,2)) #分别对每个矩阵 取各自元素求范数"]}], "metadata": {"kernelspec": {"display_name": "d2l", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.23"}}, "nbformat": 4, "nbformat_minor": 5}