{"cells": [{"cell_type": "markdown", "id": "90ef67aa", "metadata": {"origin_pos": 0}, "source": ["# 实战 Kaggle 比赛：图像分类 (CIFAR-10)\n", ":label:`sec_kaggle_cifar10`\n", "\n", "之前几节中，我们一直在使用深度学习框架的高级API直接获取张量格式的图像数据集。\n", "但是在实践中，图像数据集通常以图像文件的形式出现。\n", "本节将从原始图像文件开始，然后逐步组织、读取并将它们转换为张量格式。\n", "\n", "我们在 :numref:`sec_image_augmentation`中对CIFAR-10数据集做了一个实验。CIFAR-10是计算机视觉领域中的一个重要的数据集。\n", "本节将运用我们在前几节中学到的知识来参加CIFAR-10图像分类问题的Kaggle竞赛，(**比赛的网址是https://www.kaggle.com/c/cifar-10**)。\n", "\n", " :numref:`fig_kaggle_cifar10`显示了竞赛网站页面上的信息。\n", "为了能提交结果，首先需要注册一个Kaggle账户。\n", "\n", "![CIFAR-10 图像分类竞赛页面上的信息。竞赛用的数据集可通过点击“Data”选项卡获取。](../img/kaggle-cifar10.png)\n", ":width:`600px`\n", ":label:`fig_kaggle_cifar10`\n", "\n", "首先，导入竞赛所需的包和模块。\n"]}, {"cell_type": "code", "execution_count": 1, "id": "1a942a6d", "metadata": {"execution": {"iopub.execute_input": "2022-12-07T16:28:21.538579Z", "iopub.status.busy": "2022-12-07T16:28:21.537924Z", "iopub.status.idle": "2022-12-07T16:28:25.817556Z", "shell.execute_reply": "2022-12-07T16:28:25.816777Z"}, "origin_pos": 2, "tab": ["pytorch"]}, "outputs": [], "source": ["import collections\n", "import math\n", "import os\n", "import shutil\n", "import pandas as pd\n", "import torch\n", "import torchvision\n", "from torch import nn\n", "from d2l import torch as d2l"]}, {"cell_type": "markdown", "id": "d467e311", "metadata": {"origin_pos": 4}, "source": ["## 获取并组织数据集\n", "\n", "比赛数据集分为训练集和测试集，其中训练集包含50000张、测试集包含300000张图像。\n", "在测试集中，10000张图像将被用于评估，而剩下的290000张图像将不会被进行评估，包含它们只是为了防止手动标记测试集并提交标记结果。\n", "两个数据集中的图像都是png格式，高度和宽度均为32像素并有三个颜色通道（RGB）。\n", "这些图片共涵盖10个类别：飞机、汽车、鸟类、猫、鹿、狗、青蛙、马、船和卡车。\n", " :numref:`fig_kaggle_cifar10`的左上角显示了数据集中飞机、汽车和鸟类的一些图像。\n", "\n", "### 下载数据集\n", "\n", "登录Kaggle后，我们可以点击 :numref:`fig_kaggle_cifar10`中显示的CIFAR-10图像分类竞赛网页上的“Data”选项卡，然后单击“Download All”按钮下载数据集。\n", "在`../data`中解压下载的文件并在其中解压缩`train.7z`和`test.7z`后，在以下路径中可以找到整个数据集：\n", "\n", "* `../data/cifar-10/train/[1-50000].png`\n", "* `../data/cifar-10/test/[1-300000].png`\n", "* `../data/cifar-10/trainLabels.csv`\n", "* `../data/cifar-10/sampleSubmission.csv`\n", "\n", "`train`和`test`文件夹分别包含训练和测试图像，`trainLabels.csv`含有训练图像的标签，\n", "`sample_submission.csv`是提交文件的范例。\n", "\n", "为了便于入门，[**我们提供包含前1000个训练图像和5个随机测试图像的数据集的小规模样本**]。\n", "要使用Kaggle竞赛的完整数据集，需要将以下`demo`变量设置为`False`。\n"]}, {"cell_type": "code", "execution_count": 2, "id": "172f6be8", "metadata": {"execution": {"iopub.execute_input": "2022-12-07T16:28:25.821531Z", "iopub.status.busy": "2022-12-07T16:28:25.820807Z", "iopub.status.idle": "2022-12-07T16:28:26.344672Z", "shell.execute_reply": "2022-12-07T16:28:26.343935Z"}, "origin_pos": 5, "tab": ["pytorch"]}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Downloading ../data/kaggle_cifar10_tiny.zip from http://d2l-data.s3-accelerate.amazonaws.com/kaggle_cifar10_tiny.zip...\n"]}], "source": ["#@save\n", "d2l.DATA_HUB['cifar10_tiny'] = (d2l.DATA_URL + 'kaggle_cifar10_tiny.zip',\n", "                                '2068874e4b9a9f0fb07ebe0ad2b29754449ccacd')\n", "\n", "# 如果使用完整的Kaggle竞赛的数据集，设置demo为False\n", "demo = True\n", "\n", "if demo:\n", "    data_dir = d2l.download_extract('cifar10_tiny')\n", "else:\n", "    data_dir = '../data/cifar-10/'"]}, {"cell_type": "markdown", "id": "2b6e49f8", "metadata": {"origin_pos": 6}, "source": ["### [**整理数据集**]\n", "\n", "我们需要整理数据集来训练和测试模型。\n", "首先，我们用以下函数读取CSV文件中的标签，它返回一个字典，该字典将文件名中不带扩展名的部分映射到其标签。\n"]}, {"cell_type": "code", "execution_count": 3, "id": "9b0f0f80", "metadata": {"execution": {"iopub.execute_input": "2022-12-07T16:28:26.348382Z", "iopub.status.busy": "2022-12-07T16:28:26.347629Z", "iopub.status.idle": "2022-12-07T16:28:26.355399Z", "shell.execute_reply": "2022-12-07T16:28:26.354612Z"}, "origin_pos": 7, "tab": ["pytorch"]}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["# 训练样本 : 1000\n", "# 类别 : 10\n"]}], "source": ["#@save\n", "def read_csv_labels(fname):\n", "    \"\"\"读取fname来给标签字典返回一个文件名\"\"\"\n", "    with open(fname, 'r') as f:\n", "        # 跳过文件头行(列名)\n", "        lines = f.readlines()[1:]\n", "    tokens = [l.rstrip().split(',') for l in lines]\n", "    return dict(((name, label) for name, label in tokens))\n", "\n", "labels = read_csv_labels(os.path.join(data_dir, 'trainLabels.csv'))\n", "print('# 训练样本 :', len(labels))\n", "print('# 类别 :', len(set(labels.values())))"]}, {"cell_type": "markdown", "id": "26467088", "metadata": {"origin_pos": 8}, "source": ["接下来，我们定义`reorg_train_valid`函数来[**将验证集从原始的训练集中拆分出来**]。\n", "此函数中的参数`valid_ratio`是验证集中的样本数与原始训练集中的样本数之比。\n", "更具体地说，令$n$等于样本最少的类别中的图像数量，而$r$是比率。\n", "验证集将为每个类别拆分出$\\max(\\lfloor nr\\rfloor,1)$张图像。\n", "让我们以`valid_ratio=0.1`为例，由于原始的训练集有50000张图像，因此`train_valid_test/train`路径中将有45000张图像用于训练，而剩下5000张图像将作为路径`train_valid_test/valid`中的验证集。\n", "组织数据集后，同类别的图像将被放置在同一文件夹下。\n"]}, {"cell_type": "code", "execution_count": 4, "id": "65f7d909", "metadata": {"execution": {"iopub.execute_input": "2022-12-07T16:28:26.358606Z", "iopub.status.busy": "2022-12-07T16:28:26.358203Z", "iopub.status.idle": "2022-12-07T16:28:26.365459Z", "shell.execute_reply": "2022-12-07T16:28:26.364762Z"}, "origin_pos": 9, "tab": ["pytorch"]}, "outputs": [], "source": ["#@save\n", "def copyfile(filename, target_dir):\n", "    \"\"\"将文件复制到目标目录\"\"\"\n", "    os.makedirs(target_dir, exist_ok=True)\n", "    shutil.copy(filename, target_dir)\n", "\n", "#@save\n", "def reorg_train_valid(data_dir, labels, valid_ratio):\n", "    \"\"\"将验证集从原始的训练集中拆分出来\"\"\"\n", "    # 训练数据集中样本最少的类别中的样本数\n", "    n = collections.Counter(labels.values()).most_common()[-1][1]\n", "    # 验证集中每个类别的样本数\n", "    n_valid_per_label = max(1, math.floor(n * valid_ratio))\n", "    label_count = {}\n", "    for train_file in os.listdir(os.path.join(data_dir, 'train')):\n", "        label = labels[train_file.split('.')[0]]\n", "        fname = os.path.join(data_dir, 'train', train_file)\n", "        copyfile(fname, os.path.join(data_dir, 'train_valid_test',\n", "                                     'train_valid', label))\n", "        if label not in label_count or label_count[label] < n_valid_per_label:\n", "            copyfile(fname, os.path.join(data_dir, 'train_valid_test',\n", "                                         'valid', label))\n", "            label_count[label] = label_count.get(label, 0) + 1\n", "        else:\n", "            copyfile(fname, os.path.join(data_dir, 'train_valid_test',\n", "                                         'train', label))\n", "    return n_valid_per_label"]}, {"cell_type": "markdown", "id": "5ea0c2a7", "metadata": {"origin_pos": 10}, "source": ["下面的`reorg_test`函数用来[**在预测期间整理测试集，以方便读取**]。\n"]}, {"cell_type": "code", "execution_count": 5, "id": "c488da32", "metadata": {"execution": {"iopub.execute_input": "2022-12-07T16:28:26.368594Z", "iopub.status.busy": "2022-12-07T16:28:26.368199Z", "iopub.status.idle": "2022-12-07T16:28:26.372590Z", "shell.execute_reply": "2022-12-07T16:28:26.371867Z"}, "origin_pos": 11, "tab": ["pytorch"]}, "outputs": [], "source": ["#@save\n", "def reorg_test(data_dir):\n", "    \"\"\"在预测期间整理测试集，以方便读取\"\"\"\n", "    for test_file in os.listdir(os.path.join(data_dir, 'test')):\n", "        copyfile(os.path.join(data_dir, 'test', test_file),\n", "                 os.path.join(data_dir, 'train_valid_test', 'test',\n", "                              'unknown'))"]}, {"cell_type": "markdown", "id": "13cfe857", "metadata": {"origin_pos": 12}, "source": ["最后，我们使用一个函数来[**调用前面定义的函数**]`read_csv_labels`、`reorg_train_valid`和`reorg_test`。\n"]}, {"cell_type": "code", "execution_count": 6, "id": "2b6a05f0", "metadata": {"execution": {"iopub.execute_input": "2022-12-07T16:28:26.375662Z", "iopub.status.busy": "2022-12-07T16:28:26.375273Z", "iopub.status.idle": "2022-12-07T16:28:26.379277Z", "shell.execute_reply": "2022-12-07T16:28:26.378527Z"}, "origin_pos": 13, "tab": ["pytorch"]}, "outputs": [], "source": ["def reorg_cifar10_data(data_dir, valid_ratio):\n", "    labels = read_csv_labels(os.path.join(data_dir, 'trainLabels.csv'))\n", "    reorg_train_valid(data_dir, labels, valid_ratio)\n", "    reorg_test(data_dir)"]}, {"cell_type": "markdown", "id": "941c937d", "metadata": {"origin_pos": 14}, "source": ["在这里，我们只将样本数据集的批量大小设置为32。\n", "在实际训练和测试中，应该使用Kaggle竞赛的完整数据集，并将`batch_size`设置为更大的整数，例如128。\n", "我们将10％的训练样本作为调整超参数的验证集。\n"]}, {"cell_type": "code", "execution_count": 7, "id": "4b857d8f", "metadata": {"execution": {"iopub.execute_input": "2022-12-07T16:28:26.382306Z", "iopub.status.busy": "2022-12-07T16:28:26.381910Z", "iopub.status.idle": "2022-12-07T16:28:26.592571Z", "shell.execute_reply": "2022-12-07T16:28:26.591816Z"}, "origin_pos": 15, "tab": ["pytorch"]}, "outputs": [], "source": ["batch_size = 32 if demo else 128\n", "valid_ratio = 0.1\n", "reorg_cifar10_data(data_dir, valid_ratio)"]}, {"cell_type": "markdown", "id": "5eb3d886", "metadata": {"origin_pos": 16}, "source": ["## [**图像增广**]\n", "\n", "我们使用图像增广来解决过拟合的问题。例如在训练中，我们可以随机水平翻转图像。\n", "我们还可以对彩色图像的三个RGB通道执行标准化。\n", "下面，我们列出了其中一些可以调整的操作。\n"]}, {"cell_type": "code", "execution_count": 8, "id": "41be3595", "metadata": {"execution": {"iopub.execute_input": "2022-12-07T16:28:26.595905Z", "iopub.status.busy": "2022-12-07T16:28:26.595493Z", "iopub.status.idle": "2022-12-07T16:28:26.600687Z", "shell.execute_reply": "2022-12-07T16:28:26.599992Z"}, "origin_pos": 18, "tab": ["pytorch"]}, "outputs": [], "source": ["transform_train = torchvision.transforms.Compose([\n", "    # 在高度和宽度上将图像放大到40像素的正方形\n", "    torchvision.<PERSON>.<PERSON><PERSON><PERSON>(40),\n", "    # 随机裁剪出一个高度和宽度均为40像素的正方形图像，\n", "    # 生成一个面积为原始图像面积0.64～1倍的小正方形，\n", "    # 然后将其缩放为高度和宽度均为32像素的正方形\n", "    torchvision.transforms.RandomResizedCrop(32, scale=(0.64, 1.0),\n", "                                                   ratio=(1.0, 1.0)),\n", "    torchvision.transforms.RandomHorizontalFlip(),\n", "    torchvision.transforms.<PERSON><PERSON><PERSON><PERSON>(),\n", "    # 标准化图像的每个通道\n", "    torchvision.transforms.Normalize([0.4914, 0.4822, 0.4465],\n", "                                     [0.2023, 0.1994, 0.2010])])"]}, {"cell_type": "markdown", "id": "bf67e53d", "metadata": {"origin_pos": 20}, "source": ["在测试期间，我们只对图像执行标准化，以消除评估结果中的随机性。\n"]}, {"cell_type": "code", "execution_count": 9, "id": "4ce1e413", "metadata": {"execution": {"iopub.execute_input": "2022-12-07T16:28:26.603799Z", "iopub.status.busy": "2022-12-07T16:28:26.603403Z", "iopub.status.idle": "2022-12-07T16:28:26.607386Z", "shell.execute_reply": "2022-12-07T16:28:26.606691Z"}, "origin_pos": 22, "tab": ["pytorch"]}, "outputs": [], "source": ["transform_test = torchvision.transforms.Compose([\n", "    torchvision.transforms.<PERSON><PERSON><PERSON><PERSON>(),\n", "    torchvision.transforms.Normalize([0.4914, 0.4822, 0.4465],\n", "                                     [0.2023, 0.1994, 0.2010])])"]}, {"cell_type": "markdown", "id": "4cf4de04", "metadata": {"origin_pos": 24}, "source": ["## 读取数据集\n", "\n", "接下来，我们[**读取由原始图像组成的数据集**]，每个样本都包括一张图片和一个标签。\n"]}, {"cell_type": "code", "execution_count": 10, "id": "9fad2b0e", "metadata": {"execution": {"iopub.execute_input": "2022-12-07T16:28:26.610379Z", "iopub.status.busy": "2022-12-07T16:28:26.609987Z", "iopub.status.idle": "2022-12-07T16:28:26.621565Z", "shell.execute_reply": "2022-12-07T16:28:26.620826Z"}, "origin_pos": 26, "tab": ["pytorch"]}, "outputs": [], "source": ["train_ds, train_valid_ds = [torchvision.datasets.ImageFolder(\n", "    os.path.join(data_dir, 'train_valid_test', folder),\n", "    transform=transform_train) for folder in ['train', 'train_valid']]\n", "\n", "valid_ds, test_ds = [torchvision.datasets.ImageFolder(\n", "    os.path.join(data_dir, 'train_valid_test', folder),\n", "    transform=transform_test) for folder in ['valid', 'test']]"]}, {"cell_type": "markdown", "id": "c4a2b5b7", "metadata": {"origin_pos": 28}, "source": ["在训练期间，我们需要[**指定上面定义的所有图像增广操作**]。\n", "当验证集在超参数调整过程中用于模型评估时，不应引入图像增广的随机性。\n", "在最终预测之前，我们根据训练集和验证集组合而成的训练模型进行训练，以充分利用所有标记的数据。\n"]}, {"cell_type": "code", "execution_count": 11, "id": "78c9ccc4", "metadata": {"execution": {"iopub.execute_input": "2022-12-07T16:28:26.624769Z", "iopub.status.busy": "2022-12-07T16:28:26.624350Z", "iopub.status.idle": "2022-12-07T16:28:26.629428Z", "shell.execute_reply": "2022-12-07T16:28:26.628695Z"}, "origin_pos": 30, "tab": ["pytorch"]}, "outputs": [], "source": ["train_iter, train_valid_iter = [torch.utils.data.DataLoader(\n", "    dataset, batch_size, shuffle=True, drop_last=True)\n", "    for dataset in (train_ds, train_valid_ds)]\n", "\n", "valid_iter = torch.utils.data.DataLoader(valid_ds, batch_size, shuffle=False,\n", "                                         drop_last=True)\n", "\n", "test_iter = torch.utils.data.DataLoader(test_ds, batch_size, shuffle=False,\n", "                                        drop_last=False)"]}, {"cell_type": "markdown", "id": "8116a7ca", "metadata": {"origin_pos": 32}, "source": ["## 定义[**模型**]\n"]}, {"cell_type": "markdown", "id": "eefb976b", "metadata": {"origin_pos": 38, "tab": ["pytorch"]}, "source": ["我们定义了 :numref:`sec_resnet`中描述的Resnet-18模型。\n"]}, {"cell_type": "code", "execution_count": 12, "id": "5eb7d639", "metadata": {"execution": {"iopub.execute_input": "2022-12-07T16:28:26.632529Z", "iopub.status.busy": "2022-12-07T16:28:26.632121Z", "iopub.status.idle": "2022-12-07T16:28:26.636196Z", "shell.execute_reply": "2022-12-07T16:28:26.635470Z"}, "origin_pos": 41, "tab": ["pytorch"]}, "outputs": [], "source": ["def get_net():\n", "    num_classes = 10\n", "    net = d2l.resnet18(num_classes, 3)\n", "    return net\n", "\n", "loss = nn.CrossEntropyLoss(reduction=\"none\")"]}, {"cell_type": "markdown", "id": "d01c23f0", "metadata": {"origin_pos": 43}, "source": ["## 定义[**训练函数**]\n", "\n", "我们将根据模型在验证集上的表现来选择模型并调整超参数。\n", "下面我们定义了模型训练函数`train`。\n"]}, {"cell_type": "code", "execution_count": 13, "id": "08035b17", "metadata": {"execution": {"iopub.execute_input": "2022-12-07T16:28:26.639283Z", "iopub.status.busy": "2022-12-07T16:28:26.638874Z", "iopub.status.idle": "2022-12-07T16:28:26.648249Z", "shell.execute_reply": "2022-12-07T16:28:26.647558Z"}, "origin_pos": 45, "tab": ["pytorch"]}, "outputs": [], "source": ["def train(net, train_iter, valid_iter, num_epochs, lr, wd, devices, lr_period,\n", "          lr_decay):\n", "    trainer = torch.optim.SGD(net.parameters(), lr=lr, momentum=0.9,\n", "                              weight_decay=wd)\n", "    scheduler = torch.optim.lr_scheduler.Step<PERSON>(trainer, lr_period, lr_decay)\n", "    num_batches, timer = len(train_iter), d2l.Timer()\n", "    legend = ['train loss', 'train acc']\n", "    if valid_iter is not None:\n", "        legend.append('valid acc')\n", "    animator = d2l.Animator(xlabel='epoch', xlim=[1, num_epochs],\n", "                            legend=legend)\n", "    net = nn.DataParallel(net, device_ids=devices).to(devices[0])\n", "    for epoch in range(num_epochs):\n", "        net.train()\n", "        metric = d2l.Accumulator(3)\n", "        for i, (features, labels) in enumerate(train_iter):\n", "            timer.start()\n", "            l, acc = d2l.train_batch_ch13(net, features, labels,\n", "                                          loss, trainer, devices)\n", "            metric.add(l, acc, labels.shape[0])\n", "            timer.stop()\n", "            if (i + 1) % (num_batches // 5) == 0 or i == num_batches - 1:\n", "                animator.add(epoch + (i + 1) / num_batches,\n", "                             (metric[0] / metric[2], metric[1] / metric[2],\n", "                              None))\n", "        if valid_iter is not None:\n", "            valid_acc = d2l.evaluate_accuracy_gpu(net, valid_iter)\n", "            animator.add(epoch + 1, (None, None, valid_acc))\n", "        scheduler.step()\n", "    measures = (f'train loss {metric[0] / metric[2]:.3f}, '\n", "                f'train acc {metric[1] / metric[2]:.3f}')\n", "    if valid_iter is not None:\n", "        measures += f', valid acc {valid_acc:.3f}'\n", "    print(measures + f'\\n{metric[2] * num_epochs / timer.sum():.1f}'\n", "          f' examples/sec on {str(devices)}')"]}, {"cell_type": "markdown", "id": "65fff7f0", "metadata": {"origin_pos": 47}, "source": ["## [**训练和验证模型**]\n", "\n", "现在，我们可以训练和验证模型了，而以下所有超参数都可以调整。\n", "例如，我们可以增加周期的数量。当`lr_period`和`lr_decay`分别设置为4和0.9时，优化算法的学习速率将在每4个周期乘以0.9。\n", "为便于演示，我们在这里只训练20个周期。\n"]}, {"cell_type": "code", "execution_count": 14, "id": "c11b21dc", "metadata": {"execution": {"iopub.execute_input": "2022-12-07T16:28:26.651319Z", "iopub.status.busy": "2022-12-07T16:28:26.650914Z", "iopub.status.idle": "2022-12-07T16:29:22.027165Z", "shell.execute_reply": "2022-12-07T16:29:22.026298Z"}, "origin_pos": 49, "tab": ["pytorch"]}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["train loss 0.668, train acc 0.781, valid acc 0.453\n", "1022.7 examples/sec on [device(type='cuda', index=0), device(type='cuda', index=1)]\n"]}, {"data": {"image/svg+xml": ["<?xml version=\"1.0\" encoding=\"utf-8\" standalone=\"no\"?>\n", "<!DOCTYPE svg PUBLIC \"-//W3C//DTD SVG 1.1//EN\"\n", "  \"http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd\">\n", "<svg xmlns:xlink=\"http://www.w3.org/1999/xlink\" width=\"238.965625pt\" height=\"183.35625pt\" viewBox=\"0 0 238.**********.35625\" xmlns=\"http://www.w3.org/2000/svg\" version=\"1.1\">\n", " <metadata>\n", "  <rdf:RDF xmlns:dc=\"http://purl.org/dc/elements/1.1/\" xmlns:cc=\"http://creativecommons.org/ns#\" xmlns:rdf=\"http://www.w3.org/1999/02/22-rdf-syntax-ns#\">\n", "   <cc:Work>\n", "    <dc:type rdf:resource=\"http://purl.org/dc/dcmitype/StillImage\"/>\n", "    <dc:date>2022-12-07T16:29:21.986917</dc:date>\n", "    <dc:format>image/svg+xml</dc:format>\n", "    <dc:creator>\n", "     <cc:Agent>\n", "      <dc:title>Matplotlib v3.5.1, https://matplotlib.org/</dc:title>\n", "     </cc:Agent>\n", "    </dc:creator>\n", "   </cc:Work>\n", "  </rdf:RDF>\n", " </metadata>\n", " <defs>\n", "  <style type=\"text/css\">*{stroke-linejoin: round; stroke-linecap: butt}</style>\n", " </defs>\n", " <g id=\"figure_1\">\n", "  <g id=\"patch_1\">\n", "   <path d=\"M 0 183.35625 \n", "L 238.**********.35625 \n", "L 238.965625 0 \n", "L 0 0 \n", "z\n", "\" style=\"fill: #ffffff\"/>\n", "  </g>\n", "  <g id=\"axes_1\">\n", "   <g id=\"patch_2\">\n", "    <path d=\"M 30.**********.8 \n", "L 225.**********.8 \n", "L 225.403125 7.2 \n", "L 30.103125 7.2 \n", "z\n", "\" style=\"fill: #ffffff\"/>\n", "   </g>\n", "   <g id=\"matplotlib.axis_1\">\n", "    <g id=\"xtick_1\">\n", "     <g id=\"line2d_1\">\n", "      <path d=\"M 71.218914 145.8 \n", "L 71.218914 7.2 \n", "\" clip-path=\"url(#pe4a8f62e91)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_2\">\n", "      <defs>\n", "       <path id=\"m10d2ff9215\" d=\"M 0 0 \n", "L 0 3.5 \n", "\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </defs>\n", "      <g>\n", "       <use xlink:href=\"#m10d2ff9215\" x=\"71.218914\" y=\"145.8\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_1\">\n", "      <!-- 5 -->\n", "      <g transform=\"translate(68.037664 160.398438)scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-35\" d=\"M 691 4666 \n", "L 3169 4666 \n", "L 3169 4134 \n", "L 1269 4134 \n", "L 1269 2991 \n", "Q 1406 3038 1543 3061 \n", "Q 1681 3084 1819 3084 \n", "Q 2600 3084 3056 2656 \n", "Q 3513 2228 3513 1497 \n", "Q 3513 744 3044 326 \n", "Q 2575 -91 1722 -91 \n", "Q 1428 -91 1123 -41 \n", "Q 819 9 494 109 \n", "L 494 744 \n", "Q 775 591 1075 516 \n", "Q 1375 441 1709 441 \n", "Q 2250 441 2565 725 \n", "Q 2881 1009 2881 1497 \n", "Q 2881 1984 2565 2268 \n", "Q 2250 2553 1709 2553 \n", "Q 1456 2553 1204 2497 \n", "Q 953 2441 691 2322 \n", "L 691 4666 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-35\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_2\">\n", "     <g id=\"line2d_3\">\n", "      <path d=\"M 122.613651 145.8 \n", "L 122.613651 7.2 \n", "\" clip-path=\"url(#pe4a8f62e91)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_4\">\n", "      <g>\n", "       <use xlink:href=\"#m10d2ff9215\" x=\"122.613651\" y=\"145.8\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_2\">\n", "      <!-- 10 -->\n", "      <g transform=\"translate(116.251151 160.398438)scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-31\" d=\"M 794 531 \n", "L 1825 531 \n", "L 1825 4091 \n", "L 703 3866 \n", "L 703 4441 \n", "L 1819 4666 \n", "L 2450 4666 \n", "L 2450 531 \n", "L 3481 531 \n", "L 3481 0 \n", "L 794 0 \n", "L 794 531 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "        <path id=\"DejaVuSans-30\" d=\"M 2034 4250 \n", "Q 1547 4250 1301 3770 \n", "Q 1056 3291 1056 2328 \n", "Q 1056 1369 1301 889 \n", "Q 1547 409 2034 409 \n", "Q 2525 409 2770 889 \n", "Q 3016 1369 3016 2328 \n", "Q 3016 3291 2770 3770 \n", "Q 2525 4250 2034 4250 \n", "z\n", "M 2034 4750 \n", "Q 2819 4750 3233 4129 \n", "Q 3647 3509 3647 2328 \n", "Q 3647 1150 3233 529 \n", "Q 2819 -91 2034 -91 \n", "Q 1250 -91 836 529 \n", "Q 422 1150 422 2328 \n", "Q 422 3509 836 4129 \n", "Q 1250 4750 2034 4750 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-31\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"63.623047\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_3\">\n", "     <g id=\"line2d_5\">\n", "      <path d=\"M 174.008388 145.8 \n", "L 174.008388 7.2 \n", "\" clip-path=\"url(#pe4a8f62e91)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_6\">\n", "      <g>\n", "       <use xlink:href=\"#m10d2ff9215\" x=\"174.008388\" y=\"145.8\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_3\">\n", "      <!-- 15 -->\n", "      <g transform=\"translate(167.645888 160.398438)scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-31\"/>\n", "       <use xlink:href=\"#DejaVuSans-35\" x=\"63.623047\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_4\">\n", "     <g id=\"line2d_7\">\n", "      <path d=\"M 225.**********.8 \n", "L 225.403125 7.2 \n", "\" clip-path=\"url(#pe4a8f62e91)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_8\">\n", "      <g>\n", "       <use xlink:href=\"#m10d2ff9215\" x=\"225.403125\" y=\"145.8\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_4\">\n", "      <!-- 20 -->\n", "      <g transform=\"translate(219.040625 160.398438)scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-32\" d=\"M 1228 531 \n", "L 3431 531 \n", "L 3431 0 \n", "L 469 0 \n", "L 469 531 \n", "Q 828 903 1448 1529 \n", "Q 2069 2156 2228 2338 \n", "Q 2531 2678 2651 2914 \n", "Q 2772 3150 2772 3378 \n", "Q 2772 3750 2511 3984 \n", "Q 2250 4219 1831 4219 \n", "Q 1534 4219 1204 4116 \n", "Q 875 4013 500 3803 \n", "L 500 4441 \n", "Q 881 4594 1212 4672 \n", "Q 1544 4750 1819 4750 \n", "Q 2544 4750 2975 4387 \n", "Q 3406 4025 3406 3419 \n", "Q 3406 3131 3298 2873 \n", "Q 3191 2616 2906 2266 \n", "Q 2828 2175 2409 1742 \n", "Q 1991 1309 1228 531 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-32\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"63.623047\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"text_5\">\n", "     <!-- epoch -->\n", "     <g transform=\"translate(112.525 174.076563)scale(0.1 -0.1)\">\n", "      <defs>\n", "       <path id=\"DejaVuSans-65\" d=\"M 3597 1894 \n", "L 3597 1613 \n", "L 953 1613 \n", "Q 991 1019 1311 708 \n", "Q 1631 397 2203 397 \n", "Q 2534 397 2845 478 \n", "Q 3156 559 3463 722 \n", "L 3463 178 \n", "Q 3153 47 2828 -22 \n", "Q 2503 -91 2169 -91 \n", "Q 1331 -91 842 396 \n", "Q 353 884 353 1716 \n", "Q 353 2575 817 3079 \n", "Q 1281 3584 2069 3584 \n", "Q 2775 3584 3186 3129 \n", "Q 3597 2675 3597 1894 \n", "z\n", "M 3022 2063 \n", "Q 3016 2534 2758 2815 \n", "Q 2500 3097 2075 3097 \n", "Q 1594 3097 1305 2825 \n", "Q 1016 2553 972 2059 \n", "L 3022 2063 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-70\" d=\"M 1159 525 \n", "L 1159 -1331 \n", "L 581 -1331 \n", "L 581 3500 \n", "L 1159 3500 \n", "L 1159 2969 \n", "Q 1341 3281 1617 3432 \n", "Q 1894 3584 2278 3584 \n", "Q 2916 3584 3314 3078 \n", "Q 3713 2572 3713 1747 \n", "Q 3713 922 3314 415 \n", "Q 2916 -91 2278 -91 \n", "Q 1894 -91 1617 61 \n", "Q 1341 213 1159 525 \n", "z\n", "M 3116 1747 \n", "Q 3116 2381 2855 2742 \n", "Q 2594 3103 2138 3103 \n", "Q 1681 3103 1420 2742 \n", "Q 1159 2381 1159 1747 \n", "Q 1159 1113 1420 752 \n", "Q 1681 391 2138 391 \n", "Q 2594 391 2855 752 \n", "Q 3116 1113 3116 1747 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-6f\" d=\"M 1959 3097 \n", "Q 1497 3097 1228 2736 \n", "Q 959 2375 959 1747 \n", "Q 959 1119 1226 758 \n", "Q 1494 397 1959 397 \n", "Q 2419 397 2687 759 \n", "Q 2956 1122 2956 1747 \n", "Q 2956 2369 2687 2733 \n", "Q 2419 3097 1959 3097 \n", "z\n", "M 1959 3584 \n", "Q 2709 3584 3137 3096 \n", "Q 3566 2609 3566 1747 \n", "Q 3566 888 3137 398 \n", "Q 2709 -91 1959 -91 \n", "Q 1206 -91 779 398 \n", "Q 353 888 353 1747 \n", "Q 353 2609 779 3096 \n", "Q 1206 3584 1959 3584 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-63\" d=\"M 3122 3366 \n", "L 3122 2828 \n", "Q 2878 2963 2633 3030 \n", "Q 2388 3097 2138 3097 \n", "Q 1578 3097 1268 2742 \n", "Q 959 2388 959 1747 \n", "Q 959 1106 1268 751 \n", "Q 1578 397 2138 397 \n", "Q 2388 397 2633 464 \n", "Q 2878 531 3122 666 \n", "L 3122 134 \n", "Q 2881 22 2623 -34 \n", "Q 2366 -91 2075 -91 \n", "Q 1284 -91 818 406 \n", "Q 353 903 353 1747 \n", "Q 353 2603 823 3093 \n", "Q 1294 3584 2113 3584 \n", "Q 2378 3584 2631 3529 \n", "Q 2884 3475 3122 3366 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-68\" d=\"M 3513 2113 \n", "L 3513 0 \n", "L 2938 0 \n", "L 2938 2094 \n", "Q 2938 2591 2744 2837 \n", "Q 2550 3084 2163 3084 \n", "Q 1697 3084 1428 2787 \n", "Q 1159 2491 1159 1978 \n", "L 1159 0 \n", "L 581 0 \n", "L 581 4863 \n", "L 1159 4863 \n", "L 1159 2956 \n", "Q 1366 3272 1645 3428 \n", "Q 1925 3584 2291 3584 \n", "Q 2894 3584 3203 3211 \n", "Q 3513 2838 3513 2113 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "      </defs>\n", "      <use xlink:href=\"#DejaVuSans-65\"/>\n", "      <use xlink:href=\"#DejaVuSans-70\" x=\"61.523438\"/>\n", "      <use xlink:href=\"#DejaVuSans-6f\" x=\"125\"/>\n", "      <use xlink:href=\"#DejaVuSans-63\" x=\"186.181641\"/>\n", "      <use xlink:href=\"#DejaVuSans-68\" x=\"241.162109\"/>\n", "     </g>\n", "    </g>\n", "   </g>\n", "   <g id=\"matplotlib.axis_2\">\n", "    <g id=\"ytick_1\">\n", "     <g id=\"line2d_9\">\n", "      <path d=\"M 30.103125 119.137197 \n", "L 225.403125 119.137197 \n", "\" clip-path=\"url(#pe4a8f62e91)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_10\">\n", "      <defs>\n", "       <path id=\"m73fd19bb4a\" d=\"M 0 0 \n", "L -3.5 0 \n", "\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </defs>\n", "      <g>\n", "       <use xlink:href=\"#m73fd19bb4a\" x=\"30.103125\" y=\"119.137197\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_6\">\n", "      <!-- 0.5 -->\n", "      <g transform=\"translate(7.2 122.936415)scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-2e\" d=\"M 684 794 \n", "L 1344 794 \n", "L 1344 0 \n", "L 684 0 \n", "L 684 794 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-35\" x=\"95.410156\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_2\">\n", "     <g id=\"line2d_11\">\n", "      <path d=\"M 30.103125 90.047478 \n", "L 225.403125 90.047478 \n", "\" clip-path=\"url(#pe4a8f62e91)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_12\">\n", "      <g>\n", "       <use xlink:href=\"#m73fd19bb4a\" x=\"30.103125\" y=\"90.047478\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_7\">\n", "      <!-- 1.0 -->\n", "      <g transform=\"translate(7.2 93.846696)scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-31\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"95.410156\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_3\">\n", "     <g id=\"line2d_13\">\n", "      <path d=\"M 30.103125 60.957759 \n", "L 225.403125 60.957759 \n", "\" clip-path=\"url(#pe4a8f62e91)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_14\">\n", "      <g>\n", "       <use xlink:href=\"#m73fd19bb4a\" x=\"30.103125\" y=\"60.957759\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_8\">\n", "      <!-- 1.5 -->\n", "      <g transform=\"translate(7.2 64.756977)scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-31\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-35\" x=\"95.410156\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_4\">\n", "     <g id=\"line2d_15\">\n", "      <path d=\"M 30.103125 31.86804 \n", "L 225.403125 31.86804 \n", "\" clip-path=\"url(#pe4a8f62e91)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_16\">\n", "      <g>\n", "       <use xlink:href=\"#m73fd19bb4a\" x=\"30.103125\" y=\"31.86804\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_9\">\n", "      <!-- 2.0 -->\n", "      <g transform=\"translate(7.2 35.667258)scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-32\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"95.410156\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "   </g>\n", "   <g id=\"line2d_17\">\n", "    <path d=\"M 21.659704 13.5 \n", "L 23.49523 19.264971 \n", "L 25.330757 22.249132 \n", "L 27.166283 22.828411 \n", "L 29.001809 22.401748 \n", "L 30.103125 22.745616 \n", "L 31.938651 34.180548 \n", "L 33.774178 32.322441 \n", "L 35.609704 31.114138 \n", "L 37.44523 29.889435 \n", "L 39.280757 30.090035 \n", "L 40.382072 30.251643 \n", "L 42.217599 51.244948 \n", "L 44.053125 47.111825 \n", "L 45.888651 44.665866 \n", "L 47.724178 45.832725 \n", "L 49.559704 45.959278 \n", "L 50.66102 44.390305 \n", "L 52.496546 28.288635 \n", "L 54.332072 39.574507 \n", "L 56.167599 41.83466 \n", "L 58.003125 43.192944 \n", "L 59.838651 44.343132 \n", "L 60.939967 44.730757 \n", "L 62.775493 52.665383 \n", "L 64.61102 54.987272 \n", "L 66.446546 53.245098 \n", "L 68.282072 53.992701 \n", "L 70.117599 54.679347 \n", "L 71.218914 55.375881 \n", "L 73.054441 62.503866 \n", "L 74.889967 61.389416 \n", "L 76.725493 60.093334 \n", "L 78.56102 59.369801 \n", "L 80.396546 58.868969 \n", "L 81.497862 57.764654 \n", "L 83.333388 58.456461 \n", "L 85.168914 59.204777 \n", "L 87.004441 58.758633 \n", "L 88.839967 61.483791 \n", "L 90.675493 60.120101 \n", "L 91.776809 59.91297 \n", "L 93.612336 59.219933 \n", "L 95.447862 62.306193 \n", "L 97.283388 59.807631 \n", "L 99.118914 63.081625 \n", "L 100.954441 64.740117 \n", "L 102.055757 65.701893 \n", "L 103.891283 73.563043 \n", "L 105.726809 73.314339 \n", "L 107.562336 71.826933 \n", "L 109.397862 71.222491 \n", "L 111.233388 70.973657 \n", "L 112.334704 71.595392 \n", "L 114.17023 74.359458 \n", "L 116.005757 76.010643 \n", "L 117.841283 74.852584 \n", "L 119.676809 73.712741 \n", "L 121.512336 72.894698 \n", "L 122.613651 73.081919 \n", "L 124.449178 83.862004 \n", "L 126.284704 84.416998 \n", "L 128.12023 81.714613 \n", "L 129.955757 79.90921 \n", "L 131.791283 80.233816 \n", "L 132.892599 80.524906 \n", "L 134.728125 84.701618 \n", "L 136.563651 87.28335 \n", "L 138.399178 83.847762 \n", "L 140.234704 81.241161 \n", "L 142.07023 81.295183 \n", "L 143.171546 80.182198 \n", "L 145.007072 88.337983 \n", "L 146.842599 88.595147 \n", "L 148.678125 87.485025 \n", "L 150.513651 86.003313 \n", "L 152.349178 85.541094 \n", "L 153.450493 84.929397 \n", "L 155.28602 76.765197 \n", "L 157.121546 80.824198 \n", "L 158.957072 84.809213 \n", "L 160.792599 87.25092 \n", "L 162.628125 86.236193 \n", "L 163.729441 85.595474 \n", "L 165.564967 93.414268 \n", "L 167.400493 93.121142 \n", "L 169.23602 92.476194 \n", "L 171.071546 92.461451 \n", "L 172.907072 92.273261 \n", "L 174.008388 92.726923 \n", "L 175.843914 92.07454 \n", "L 177.679441 99.000211 \n", "L 179.514967 97.476025 \n", "L 181.350493 98.159557 \n", "L 183.18602 97.297828 \n", "L 184.287336 97.616771 \n", "L 186.122862 109.985355 \n", "L 187.958388 107.904094 \n", "L 189.793914 108.879978 \n", "L 191.629441 106.10683 \n", "L 193.464967 105.42888 \n", "L 194.566283 104.108034 \n", "L 196.401809 111.706695 \n", "L 198.237336 107.796555 \n", "L 200.072862 107.206047 \n", "L 201.908388 106.122518 \n", "L 203.743914 105.571017 \n", "L 204.84523 105.663155 \n", "L 206.680757 107.398116 \n", "L 208.516283 104.282148 \n", "L 210.351809 104.297554 \n", "L 212.187336 103.171237 \n", "L 214.022862 103.525974 \n", "L 215.124178 103.35109 \n", "L 216.959704 116.609424 \n", "L 218.79523 112.850759 \n", "L 220.630757 111.880869 \n", "L 222.466283 112.552231 \n", "L 224.301809 110.592595 \n", "L 225.403125 109.359236 \n", "\" clip-path=\"url(#pe4a8f62e91)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_18\">\n", "    <path d=\"M 21.659704 139.5 \n", "L 23.49523 136.409217 \n", "L 25.330757 135.136542 \n", "L 27.166283 135.227448 \n", "L 29.001809 135.718337 \n", "L 30.103125 135.694961 \n", "L 31.938651 132.22757 \n", "L 33.774178 133.500245 \n", "L 35.609704 132.591192 \n", "L 37.44523 132.954813 \n", "L 39.280757 133.027538 \n", "L 40.382072 133.227529 \n", "L 42.217599 126.046005 \n", "L 44.053125 127.31868 \n", "L 45.888651 127.621698 \n", "L 47.724178 127.045964 \n", "L 49.559704 126.555075 \n", "L 50.66102 127.57841 \n", "L 52.496546 127.864112 \n", "L 54.332072 127.136869 \n", "L 56.167599 127.136869 \n", "L 58.003125 126.955059 \n", "L 59.838651 126.627799 \n", "L 60.939967 126.799221 \n", "L 62.775493 125.318762 \n", "L 64.61102 122.773412 \n", "L 66.446546 123.864276 \n", "L 68.282072 124.227898 \n", "L 70.117599 123.718827 \n", "L 71.218914 123.617533 \n", "L 73.054441 120.955304 \n", "L 74.889967 122.227979 \n", "L 76.725493 123.015826 \n", "L 78.56102 123.227938 \n", "L 80.396546 122.91886 \n", "L 81.497862 123.877263 \n", "L 83.333388 123.500655 \n", "L 85.168914 123.137033 \n", "L 87.004441 123.500655 \n", "L 88.839967 122.137074 \n", "L 90.675493 122.118893 \n", "L 91.776809 122.059155 \n", "L 93.612336 122.40979 \n", "L 95.447862 122.046169 \n", "L 97.283388 122.773412 \n", "L 99.118914 121.318926 \n", "L 100.954441 120.737131 \n", "L 102.055757 120.241048 \n", "L 103.891283 116.955468 \n", "L 105.726809 117.682711 \n", "L 107.562336 119.137197 \n", "L 109.397862 118.864481 \n", "L 111.233388 118.191781 \n", "L 112.334704 118.098278 \n", "L 114.17023 115.13736 \n", "L 116.005757 114.95555 \n", "L 117.841283 115.743396 \n", "L 119.676809 116.228225 \n", "L 121.512336 116.1555 \n", "L 122.613651 116.474968 \n", "L 124.449178 112.955631 \n", "L 126.284704 112.955631 \n", "L 128.12023 114.046496 \n", "L 129.955757 114.410117 \n", "L 131.791283 114.410117 \n", "L 132.892599 114.462063 \n", "L 134.728125 112.59201 \n", "L 136.563651 111.864767 \n", "L 138.399178 114.28891 \n", "L 140.234704 115.13736 \n", "L 142.07023 115.13736 \n", "L 143.171546 115.565914 \n", "L 145.007072 111.501145 \n", "L 146.842599 110.955713 \n", "L 148.678125 111.501145 \n", "L 150.513651 112.773821 \n", "L 152.349178 112.810183 \n", "L 153.450493 112.838753 \n", "L 155.28602 116.955468 \n", "L 157.121546 114.95555 \n", "L 158.957072 112.834424 \n", "L 160.792599 111.592051 \n", "L 162.628125 111.937491 \n", "L 163.729441 112.449159 \n", "L 165.564967 109.319417 \n", "L 167.400493 109.501227 \n", "L 169.23602 109.440624 \n", "L 171.071546 109.228511 \n", "L 172.907072 109.610314 \n", "L 174.008388 109.78693 \n", "L 175.843914 107.501309 \n", "L 177.679441 105.865012 \n", "L 179.514967 107.137688 \n", "L 181.350493 107.319498 \n", "L 183.18602 107.719482 \n", "L 184.287336 107.774025 \n", "L 186.122862 102.410608 \n", "L 187.958388 103.501473 \n", "L 189.793914 103.743887 \n", "L 191.629441 104.319621 \n", "L 193.464967 104.883234 \n", "L 194.566283 105.306594 \n", "L 196.401809 102.77423 \n", "L 198.237336 102.95604 \n", "L 200.072862 103.62268 \n", "L 201.908388 104.774148 \n", "L 203.743914 105.101407 \n", "L 204.84523 105.046864 \n", "L 206.680757 102.77423 \n", "L 208.516283 105.31958 \n", "L 210.351809 105.440787 \n", "L 212.187336 106.319539 \n", "L 214.022862 105.82865 \n", "L 215.124178 106.150715 \n", "L 216.959704 99.501636 \n", "L 218.79523 101.683365 \n", "L 220.630757 101.683365 \n", "L 222.466283 101.228838 \n", "L 224.301809 102.337884 \n", "L 225.403125 102.77423 \n", "\" clip-path=\"url(#pe4a8f62e91)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #bf00bf; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_19\">\n", "    <path d=\"M 30.103125 136.409217 \n", "L 40.382072 130.045841 \n", "L 50.66102 138.227325 \n", "L 60.939967 130.045841 \n", "L 71.218914 126.409626 \n", "L 81.497862 121.864358 \n", "L 91.776809 126.409626 \n", "L 102.055757 119.137197 \n", "L 112.334704 123.682465 \n", "L 122.613651 119.137197 \n", "L 132.892599 123.682465 \n", "L 143.171546 134.59111 \n", "L 153.450493 120.955304 \n", "L 163.729441 126.409626 \n", "L 174.008388 116.410036 \n", "L 184.287336 117.319089 \n", "L 194.566283 122.773412 \n", "L 204.84523 119.137197 \n", "L 215.124178 121.864358 \n", "L 225.403125 121.864358 \n", "\" clip-path=\"url(#pe4a8f62e91)\" style=\"fill: none; stroke-dasharray: 9.6,2.4,1.5,2.4; stroke-dashoffset: 0; stroke: #008000; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"patch_3\">\n", "    <path d=\"M 30.**********.8 \n", "L 30.103125 7.2 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_4\">\n", "    <path d=\"M 225.**********.8 \n", "L 225.403125 7.2 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_5\">\n", "    <path d=\"M 30.**********.8 \n", "L 225.**********.8 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_6\">\n", "    <path d=\"M 30.103125 7.2 \n", "L 225.403125 7.2 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"legend_1\">\n", "    <g id=\"patch_7\">\n", "     <path d=\"M 140.634375 59.234375 \n", "L 218.403125 59.234375 \n", "Q 220.403125 59.234375 220.403125 57.234375 \n", "L 220.403125 14.2 \n", "Q 220.403125 12.2 218.403125 12.2 \n", "L 140.634375 12.2 \n", "Q 138.634375 12.2 138.634375 14.2 \n", "L 138.634375 57.234375 \n", "Q 138.634375 59.234375 140.634375 59.234375 \n", "z\n", "\" style=\"fill: #ffffff; opacity: 0.8; stroke: #cccccc; stroke-linejoin: miter\"/>\n", "    </g>\n", "    <g id=\"line2d_20\">\n", "     <path d=\"M 142.634375 20.298438 \n", "L 152.634375 20.298438 \n", "L 162.634375 20.298438 \n", "\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "    </g>\n", "    <g id=\"text_10\">\n", "     <!-- train loss -->\n", "     <g transform=\"translate(170.634375 23.798438)scale(0.1 -0.1)\">\n", "      <defs>\n", "       <path id=\"DejaVuSans-74\" d=\"M 1172 4494 \n", "L 1172 3500 \n", "L 2356 3500 \n", "L 2356 3053 \n", "L 1172 3053 \n", "L 1172 1153 \n", "Q 1172 725 1289 603 \n", "Q 1406 481 1766 481 \n", "L 2356 481 \n", "L 2356 0 \n", "L 1766 0 \n", "Q 1100 0 847 248 \n", "Q 594 497 594 1153 \n", "L 594 3053 \n", "L 172 3053 \n", "L 172 3500 \n", "L 594 3500 \n", "L 594 4494 \n", "L 1172 4494 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-72\" d=\"M 2631 2963 \n", "Q 2534 3019 2420 3045 \n", "Q 2306 3072 2169 3072 \n", "Q 1681 3072 1420 2755 \n", "Q 1159 2438 1159 1844 \n", "L 1159 0 \n", "L 581 0 \n", "L 581 3500 \n", "L 1159 3500 \n", "L 1159 2956 \n", "Q 1341 3275 1631 3429 \n", "Q 1922 3584 2338 3584 \n", "Q 2397 3584 2469 3576 \n", "Q 2541 3569 2628 3553 \n", "L 2631 2963 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-61\" d=\"M 2194 1759 \n", "Q 1497 1759 1228 1600 \n", "Q 959 1441 959 1056 \n", "Q 959 750 1161 570 \n", "Q 1363 391 1709 391 \n", "Q 2188 391 2477 730 \n", "Q 2766 1069 2766 1631 \n", "L 2766 1759 \n", "L 2194 1759 \n", "z\n", "M 3341 1997 \n", "L 3341 0 \n", "L 2766 0 \n", "L 2766 531 \n", "Q 2569 213 2275 61 \n", "Q 1981 -91 1556 -91 \n", "Q 1019 -91 701 211 \n", "Q 384 513 384 1019 \n", "Q 384 1609 779 1909 \n", "Q 1175 2209 1959 2209 \n", "L 2766 2209 \n", "L 2766 2266 \n", "Q 2766 2663 2505 2880 \n", "Q 2244 3097 1772 3097 \n", "Q 1472 3097 1187 3025 \n", "Q 903 2953 641 2809 \n", "L 641 3341 \n", "Q 956 3463 1253 3523 \n", "Q 1550 3584 1831 3584 \n", "Q 2591 3584 2966 3190 \n", "Q 3341 2797 3341 1997 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-69\" d=\"M 603 3500 \n", "L 1178 3500 \n", "L 1178 0 \n", "L 603 0 \n", "L 603 3500 \n", "z\n", "M 603 4863 \n", "L 1178 4863 \n", "L 1178 4134 \n", "L 603 4134 \n", "L 603 4863 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-6e\" d=\"M 3513 2113 \n", "L 3513 0 \n", "L 2938 0 \n", "L 2938 2094 \n", "Q 2938 2591 2744 2837 \n", "Q 2550 3084 2163 3084 \n", "Q 1697 3084 1428 2787 \n", "Q 1159 2491 1159 1978 \n", "L 1159 0 \n", "L 581 0 \n", "L 581 3500 \n", "L 1159 3500 \n", "L 1159 2956 \n", "Q 1366 3272 1645 3428 \n", "Q 1925 3584 2291 3584 \n", "Q 2894 3584 3203 3211 \n", "Q 3513 2838 3513 2113 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-20\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-6c\" d=\"M 603 4863 \n", "L 1178 4863 \n", "L 1178 0 \n", "L 603 0 \n", "L 603 4863 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-73\" d=\"M 2834 3397 \n", "L 2834 2853 \n", "Q 2591 2978 2328 3040 \n", "Q 2066 3103 1784 3103 \n", "Q 1356 3103 1142 2972 \n", "Q 928 2841 928 2578 \n", "Q 928 2378 1081 2264 \n", "Q 1234 2150 1697 2047 \n", "L 1894 2003 \n", "Q 2506 1872 2764 1633 \n", "Q 3022 1394 3022 966 \n", "Q 3022 478 2636 193 \n", "Q 2250 -91 1575 -91 \n", "Q 1294 -91 989 -36 \n", "Q 684 19 347 128 \n", "L 347 722 \n", "Q 666 556 975 473 \n", "Q 1284 391 1588 391 \n", "Q 1994 391 2212 530 \n", "Q 2431 669 2431 922 \n", "Q 2431 1156 2273 1281 \n", "Q 2116 1406 1581 1522 \n", "L 1381 1569 \n", "Q 847 1681 609 1914 \n", "Q 372 2147 372 2553 \n", "Q 372 3047 722 3315 \n", "Q 1072 3584 1716 3584 \n", "Q 2034 3584 2315 3537 \n", "Q 2597 3491 2834 3397 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "      </defs>\n", "      <use xlink:href=\"#DejaVuSans-74\"/>\n", "      <use xlink:href=\"#DejaVuSans-72\" x=\"39.208984\"/>\n", "      <use xlink:href=\"#DejaVuSans-61\" x=\"80.322266\"/>\n", "      <use xlink:href=\"#DejaVuSans-69\" x=\"141.601562\"/>\n", "      <use xlink:href=\"#DejaVuSans-6e\" x=\"169.384766\"/>\n", "      <use xlink:href=\"#DejaVuSans-20\" x=\"232.763672\"/>\n", "      <use xlink:href=\"#DejaVuSans-6c\" x=\"264.550781\"/>\n", "      <use xlink:href=\"#DejaVuSans-6f\" x=\"292.333984\"/>\n", "      <use xlink:href=\"#DejaVuSans-73\" x=\"353.515625\"/>\n", "      <use xlink:href=\"#DejaVuSans-73\" x=\"405.615234\"/>\n", "     </g>\n", "    </g>\n", "    <g id=\"line2d_21\">\n", "     <path d=\"M 142.634375 34.976563 \n", "L 152.634375 34.976563 \n", "L 162.634375 34.976563 \n", "\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #bf00bf; stroke-width: 1.5\"/>\n", "    </g>\n", "    <g id=\"text_11\">\n", "     <!-- train acc -->\n", "     <g transform=\"translate(170.634375 38.476563)scale(0.1 -0.1)\">\n", "      <use xlink:href=\"#DejaVuSans-74\"/>\n", "      <use xlink:href=\"#DejaVuSans-72\" x=\"39.208984\"/>\n", "      <use xlink:href=\"#DejaVuSans-61\" x=\"80.322266\"/>\n", "      <use xlink:href=\"#DejaVuSans-69\" x=\"141.601562\"/>\n", "      <use xlink:href=\"#DejaVuSans-6e\" x=\"169.384766\"/>\n", "      <use xlink:href=\"#DejaVuSans-20\" x=\"232.763672\"/>\n", "      <use xlink:href=\"#DejaVuSans-61\" x=\"264.550781\"/>\n", "      <use xlink:href=\"#DejaVuSans-63\" x=\"325.830078\"/>\n", "      <use xlink:href=\"#DejaVuSans-63\" x=\"380.810547\"/>\n", "     </g>\n", "    </g>\n", "    <g id=\"line2d_22\">\n", "     <path d=\"M 142.634375 49.654688 \n", "L 152.634375 49.654688 \n", "L 162.634375 49.654688 \n", "\" style=\"fill: none; stroke-dasharray: 9.6,2.4,1.5,2.4; stroke-dashoffset: 0; stroke: #008000; stroke-width: 1.5\"/>\n", "    </g>\n", "    <g id=\"text_12\">\n", "     <!-- valid acc -->\n", "     <g transform=\"translate(170.634375 53.154688)scale(0.1 -0.1)\">\n", "      <defs>\n", "       <path id=\"DejaVuSans-76\" d=\"M 191 3500 \n", "L 800 3500 \n", "L 1894 563 \n", "L 2988 3500 \n", "L 3597 3500 \n", "L 2284 0 \n", "L 1503 0 \n", "L 191 3500 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-64\" d=\"M 2906 2969 \n", "L 2906 4863 \n", "L 3481 4863 \n", "L 3481 0 \n", "L 2906 0 \n", "L 2906 525 \n", "Q 2725 213 2448 61 \n", "Q 2172 -91 1784 -91 \n", "Q 1150 -91 751 415 \n", "Q 353 922 353 1747 \n", "Q 353 2572 751 3078 \n", "Q 1150 3584 1784 3584 \n", "Q 2172 3584 2448 3432 \n", "Q 2725 3281 2906 2969 \n", "z\n", "M 947 1747 \n", "Q 947 1113 1208 752 \n", "Q 1469 391 1925 391 \n", "Q 2381 391 2643 752 \n", "Q 2906 1113 2906 1747 \n", "Q 2906 2381 2643 2742 \n", "Q 2381 3103 1925 3103 \n", "Q 1469 3103 1208 2742 \n", "Q 947 2381 947 1747 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "      </defs>\n", "      <use xlink:href=\"#DejaVuSans-76\"/>\n", "      <use xlink:href=\"#DejaVuSans-61\" x=\"59.179688\"/>\n", "      <use xlink:href=\"#DejaVuSans-6c\" x=\"120.458984\"/>\n", "      <use xlink:href=\"#DejaVuSans-69\" x=\"148.242188\"/>\n", "      <use xlink:href=\"#DejaVuSans-64\" x=\"176.025391\"/>\n", "      <use xlink:href=\"#DejaVuSans-20\" x=\"239.501953\"/>\n", "      <use xlink:href=\"#DejaVuSans-61\" x=\"271.289062\"/>\n", "      <use xlink:href=\"#DejaVuSans-63\" x=\"332.568359\"/>\n", "      <use xlink:href=\"#DejaVuSans-63\" x=\"387.548828\"/>\n", "     </g>\n", "    </g>\n", "   </g>\n", "  </g>\n", " </g>\n", " <defs>\n", "  <clipPath id=\"pe4a8f62e91\">\n", "   <rect x=\"30.103125\" y=\"7.2\" width=\"195.3\" height=\"138.6\"/>\n", "  </clipPath>\n", " </defs>\n", "</svg>\n"], "text/plain": ["<Figure size 350x250 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["devices, num_epochs, lr, wd = d2l.try_all_gpus(), 20, 2e-4, 5e-4\n", "lr_period, lr_decay, net = 4, 0.9, get_net()\n", "train(net, train_iter, valid_iter, num_epochs, lr, wd, devices, lr_period,\n", "      lr_decay)"]}, {"cell_type": "markdown", "id": "08255b2d", "metadata": {"origin_pos": 51}, "source": ["## 在 Kaggle 上[**对测试集进行分类并提交结果**]\n", "\n", "在获得具有超参数的满意的模型后，我们使用所有标记的数据（包括验证集）来重新训练模型并对测试集进行分类。\n"]}, {"cell_type": "code", "execution_count": 15, "id": "50ae26b9", "metadata": {"execution": {"iopub.execute_input": "2022-12-07T16:29:22.030416Z", "iopub.status.busy": "2022-12-07T16:29:22.030145Z", "iopub.status.idle": "2022-12-07T16:30:09.012586Z", "shell.execute_reply": "2022-12-07T16:30:09.011795Z"}, "origin_pos": 53, "tab": ["pytorch"]}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["train loss 0.824, train acc 0.702\n", "1289.6 examples/sec on [device(type='cuda', index=0), device(type='cuda', index=1)]\n"]}, {"data": {"image/svg+xml": ["<?xml version=\"1.0\" encoding=\"utf-8\" standalone=\"no\"?>\n", "<!DOCTYPE svg PUBLIC \"-//W3C//DTD SVG 1.1//EN\"\n", "  \"http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd\">\n", "<svg xmlns:xlink=\"http://www.w3.org/1999/xlink\" width=\"238.965625pt\" height=\"183.35625pt\" viewBox=\"0 0 238.**********.35625\" xmlns=\"http://www.w3.org/2000/svg\" version=\"1.1\">\n", " <metadata>\n", "  <rdf:RDF xmlns:dc=\"http://purl.org/dc/elements/1.1/\" xmlns:cc=\"http://creativecommons.org/ns#\" xmlns:rdf=\"http://www.w3.org/1999/02/22-rdf-syntax-ns#\">\n", "   <cc:Work>\n", "    <dc:type rdf:resource=\"http://purl.org/dc/dcmitype/StillImage\"/>\n", "    <dc:date>2022-12-07T16:30:08.977437</dc:date>\n", "    <dc:format>image/svg+xml</dc:format>\n", "    <dc:creator>\n", "     <cc:Agent>\n", "      <dc:title>Matplotlib v3.5.1, https://matplotlib.org/</dc:title>\n", "     </cc:Agent>\n", "    </dc:creator>\n", "   </cc:Work>\n", "  </rdf:RDF>\n", " </metadata>\n", " <defs>\n", "  <style type=\"text/css\">*{stroke-linejoin: round; stroke-linecap: butt}</style>\n", " </defs>\n", " <g id=\"figure_1\">\n", "  <g id=\"patch_1\">\n", "   <path d=\"M 0 183.35625 \n", "L 238.**********.35625 \n", "L 238.965625 0 \n", "L 0 0 \n", "z\n", "\" style=\"fill: #ffffff\"/>\n", "  </g>\n", "  <g id=\"axes_1\">\n", "   <g id=\"patch_2\">\n", "    <path d=\"M 30.**********.8 \n", "L 225.**********.8 \n", "L 225.403125 7.2 \n", "L 30.103125 7.2 \n", "z\n", "\" style=\"fill: #ffffff\"/>\n", "   </g>\n", "   <g id=\"matplotlib.axis_1\">\n", "    <g id=\"xtick_1\">\n", "     <g id=\"line2d_1\">\n", "      <path d=\"M 71.218914 145.8 \n", "L 71.218914 7.2 \n", "\" clip-path=\"url(#p62659bab81)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_2\">\n", "      <defs>\n", "       <path id=\"md826b73e26\" d=\"M 0 0 \n", "L 0 3.5 \n", "\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </defs>\n", "      <g>\n", "       <use xlink:href=\"#md826b73e26\" x=\"71.218914\" y=\"145.8\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_1\">\n", "      <!-- 5 -->\n", "      <g transform=\"translate(68.037664 160.398438)scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-35\" d=\"M 691 4666 \n", "L 3169 4666 \n", "L 3169 4134 \n", "L 1269 4134 \n", "L 1269 2991 \n", "Q 1406 3038 1543 3061 \n", "Q 1681 3084 1819 3084 \n", "Q 2600 3084 3056 2656 \n", "Q 3513 2228 3513 1497 \n", "Q 3513 744 3044 326 \n", "Q 2575 -91 1722 -91 \n", "Q 1428 -91 1123 -41 \n", "Q 819 9 494 109 \n", "L 494 744 \n", "Q 775 591 1075 516 \n", "Q 1375 441 1709 441 \n", "Q 2250 441 2565 725 \n", "Q 2881 1009 2881 1497 \n", "Q 2881 1984 2565 2268 \n", "Q 2250 2553 1709 2553 \n", "Q 1456 2553 1204 2497 \n", "Q 953 2441 691 2322 \n", "L 691 4666 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-35\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_2\">\n", "     <g id=\"line2d_3\">\n", "      <path d=\"M 122.613651 145.8 \n", "L 122.613651 7.2 \n", "\" clip-path=\"url(#p62659bab81)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_4\">\n", "      <g>\n", "       <use xlink:href=\"#md826b73e26\" x=\"122.613651\" y=\"145.8\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_2\">\n", "      <!-- 10 -->\n", "      <g transform=\"translate(116.251151 160.398438)scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-31\" d=\"M 794 531 \n", "L 1825 531 \n", "L 1825 4091 \n", "L 703 3866 \n", "L 703 4441 \n", "L 1819 4666 \n", "L 2450 4666 \n", "L 2450 531 \n", "L 3481 531 \n", "L 3481 0 \n", "L 794 0 \n", "L 794 531 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "        <path id=\"DejaVuSans-30\" d=\"M 2034 4250 \n", "Q 1547 4250 1301 3770 \n", "Q 1056 3291 1056 2328 \n", "Q 1056 1369 1301 889 \n", "Q 1547 409 2034 409 \n", "Q 2525 409 2770 889 \n", "Q 3016 1369 3016 2328 \n", "Q 3016 3291 2770 3770 \n", "Q 2525 4250 2034 4250 \n", "z\n", "M 2034 4750 \n", "Q 2819 4750 3233 4129 \n", "Q 3647 3509 3647 2328 \n", "Q 3647 1150 3233 529 \n", "Q 2819 -91 2034 -91 \n", "Q 1250 -91 836 529 \n", "Q 422 1150 422 2328 \n", "Q 422 3509 836 4129 \n", "Q 1250 4750 2034 4750 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-31\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"63.623047\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_3\">\n", "     <g id=\"line2d_5\">\n", "      <path d=\"M 174.008388 145.8 \n", "L 174.008388 7.2 \n", "\" clip-path=\"url(#p62659bab81)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_6\">\n", "      <g>\n", "       <use xlink:href=\"#md826b73e26\" x=\"174.008388\" y=\"145.8\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_3\">\n", "      <!-- 15 -->\n", "      <g transform=\"translate(167.645888 160.398438)scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-31\"/>\n", "       <use xlink:href=\"#DejaVuSans-35\" x=\"63.623047\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_4\">\n", "     <g id=\"line2d_7\">\n", "      <path d=\"M 225.**********.8 \n", "L 225.403125 7.2 \n", "\" clip-path=\"url(#p62659bab81)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_8\">\n", "      <g>\n", "       <use xlink:href=\"#md826b73e26\" x=\"225.403125\" y=\"145.8\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_4\">\n", "      <!-- 20 -->\n", "      <g transform=\"translate(219.040625 160.398438)scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-32\" d=\"M 1228 531 \n", "L 3431 531 \n", "L 3431 0 \n", "L 469 0 \n", "L 469 531 \n", "Q 828 903 1448 1529 \n", "Q 2069 2156 2228 2338 \n", "Q 2531 2678 2651 2914 \n", "Q 2772 3150 2772 3378 \n", "Q 2772 3750 2511 3984 \n", "Q 2250 4219 1831 4219 \n", "Q 1534 4219 1204 4116 \n", "Q 875 4013 500 3803 \n", "L 500 4441 \n", "Q 881 4594 1212 4672 \n", "Q 1544 4750 1819 4750 \n", "Q 2544 4750 2975 4387 \n", "Q 3406 4025 3406 3419 \n", "Q 3406 3131 3298 2873 \n", "Q 3191 2616 2906 2266 \n", "Q 2828 2175 2409 1742 \n", "Q 1991 1309 1228 531 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-32\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"63.623047\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"text_5\">\n", "     <!-- epoch -->\n", "     <g transform=\"translate(112.525 174.076563)scale(0.1 -0.1)\">\n", "      <defs>\n", "       <path id=\"DejaVuSans-65\" d=\"M 3597 1894 \n", "L 3597 1613 \n", "L 953 1613 \n", "Q 991 1019 1311 708 \n", "Q 1631 397 2203 397 \n", "Q 2534 397 2845 478 \n", "Q 3156 559 3463 722 \n", "L 3463 178 \n", "Q 3153 47 2828 -22 \n", "Q 2503 -91 2169 -91 \n", "Q 1331 -91 842 396 \n", "Q 353 884 353 1716 \n", "Q 353 2575 817 3079 \n", "Q 1281 3584 2069 3584 \n", "Q 2775 3584 3186 3129 \n", "Q 3597 2675 3597 1894 \n", "z\n", "M 3022 2063 \n", "Q 3016 2534 2758 2815 \n", "Q 2500 3097 2075 3097 \n", "Q 1594 3097 1305 2825 \n", "Q 1016 2553 972 2059 \n", "L 3022 2063 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-70\" d=\"M 1159 525 \n", "L 1159 -1331 \n", "L 581 -1331 \n", "L 581 3500 \n", "L 1159 3500 \n", "L 1159 2969 \n", "Q 1341 3281 1617 3432 \n", "Q 1894 3584 2278 3584 \n", "Q 2916 3584 3314 3078 \n", "Q 3713 2572 3713 1747 \n", "Q 3713 922 3314 415 \n", "Q 2916 -91 2278 -91 \n", "Q 1894 -91 1617 61 \n", "Q 1341 213 1159 525 \n", "z\n", "M 3116 1747 \n", "Q 3116 2381 2855 2742 \n", "Q 2594 3103 2138 3103 \n", "Q 1681 3103 1420 2742 \n", "Q 1159 2381 1159 1747 \n", "Q 1159 1113 1420 752 \n", "Q 1681 391 2138 391 \n", "Q 2594 391 2855 752 \n", "Q 3116 1113 3116 1747 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-6f\" d=\"M 1959 3097 \n", "Q 1497 3097 1228 2736 \n", "Q 959 2375 959 1747 \n", "Q 959 1119 1226 758 \n", "Q 1494 397 1959 397 \n", "Q 2419 397 2687 759 \n", "Q 2956 1122 2956 1747 \n", "Q 2956 2369 2687 2733 \n", "Q 2419 3097 1959 3097 \n", "z\n", "M 1959 3584 \n", "Q 2709 3584 3137 3096 \n", "Q 3566 2609 3566 1747 \n", "Q 3566 888 3137 398 \n", "Q 2709 -91 1959 -91 \n", "Q 1206 -91 779 398 \n", "Q 353 888 353 1747 \n", "Q 353 2609 779 3096 \n", "Q 1206 3584 1959 3584 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-63\" d=\"M 3122 3366 \n", "L 3122 2828 \n", "Q 2878 2963 2633 3030 \n", "Q 2388 3097 2138 3097 \n", "Q 1578 3097 1268 2742 \n", "Q 959 2388 959 1747 \n", "Q 959 1106 1268 751 \n", "Q 1578 397 2138 397 \n", "Q 2388 397 2633 464 \n", "Q 2878 531 3122 666 \n", "L 3122 134 \n", "Q 2881 22 2623 -34 \n", "Q 2366 -91 2075 -91 \n", "Q 1284 -91 818 406 \n", "Q 353 903 353 1747 \n", "Q 353 2603 823 3093 \n", "Q 1294 3584 2113 3584 \n", "Q 2378 3584 2631 3529 \n", "Q 2884 3475 3122 3366 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-68\" d=\"M 3513 2113 \n", "L 3513 0 \n", "L 2938 0 \n", "L 2938 2094 \n", "Q 2938 2591 2744 2837 \n", "Q 2550 3084 2163 3084 \n", "Q 1697 3084 1428 2787 \n", "Q 1159 2491 1159 1978 \n", "L 1159 0 \n", "L 581 0 \n", "L 581 4863 \n", "L 1159 4863 \n", "L 1159 2956 \n", "Q 1366 3272 1645 3428 \n", "Q 1925 3584 2291 3584 \n", "Q 2894 3584 3203 3211 \n", "Q 3513 2838 3513 2113 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "      </defs>\n", "      <use xlink:href=\"#DejaVuSans-65\"/>\n", "      <use xlink:href=\"#DejaVuSans-70\" x=\"61.523438\"/>\n", "      <use xlink:href=\"#DejaVuSans-6f\" x=\"125\"/>\n", "      <use xlink:href=\"#DejaVuSans-63\" x=\"186.181641\"/>\n", "      <use xlink:href=\"#DejaVuSans-68\" x=\"241.162109\"/>\n", "     </g>\n", "    </g>\n", "   </g>\n", "   <g id=\"matplotlib.axis_2\">\n", "    <g id=\"ytick_1\">\n", "     <g id=\"line2d_9\">\n", "      <path d=\"M 30.103125 119.908795 \n", "L 225.403125 119.908795 \n", "\" clip-path=\"url(#p62659bab81)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_10\">\n", "      <defs>\n", "       <path id=\"m9f4b971d01\" d=\"M 0 0 \n", "L -3.5 0 \n", "\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </defs>\n", "      <g>\n", "       <use xlink:href=\"#m9f4b971d01\" x=\"30.103125\" y=\"119.908795\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_6\">\n", "      <!-- 0.5 -->\n", "      <g transform=\"translate(7.2 123.708014)scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-2e\" d=\"M 684 794 \n", "L 1344 794 \n", "L 1344 0 \n", "L 684 0 \n", "L 684 794 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-35\" x=\"95.410156\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_2\">\n", "     <g id=\"line2d_11\">\n", "      <path d=\"M 30.103125 90.521989 \n", "L 225.403125 90.521989 \n", "\" clip-path=\"url(#p62659bab81)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_12\">\n", "      <g>\n", "       <use xlink:href=\"#m9f4b971d01\" x=\"30.103125\" y=\"90.521989\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_7\">\n", "      <!-- 1.0 -->\n", "      <g transform=\"translate(7.2 94.321207)scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-31\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"95.410156\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_3\">\n", "     <g id=\"line2d_13\">\n", "      <path d=\"M 30.103125 61.135182 \n", "L 225.403125 61.135182 \n", "\" clip-path=\"url(#p62659bab81)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_14\">\n", "      <g>\n", "       <use xlink:href=\"#m9f4b971d01\" x=\"30.103125\" y=\"61.135182\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_8\">\n", "      <!-- 1.5 -->\n", "      <g transform=\"translate(7.2 64.9344)scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-31\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-35\" x=\"95.410156\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_4\">\n", "     <g id=\"line2d_15\">\n", "      <path d=\"M 30.103125 31.748375 \n", "L 225.403125 31.748375 \n", "\" clip-path=\"url(#p62659bab81)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_16\">\n", "      <g>\n", "       <use xlink:href=\"#m9f4b971d01\" x=\"30.103125\" y=\"31.748375\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_9\">\n", "      <!-- 2.0 -->\n", "      <g transform=\"translate(7.2 35.547594)scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-32\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"95.410156\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "   </g>\n", "   <g id=\"line2d_17\">\n", "    <path d=\"M 21.813651 13.5 \n", "L 23.803125 17.8941 \n", "L 25.792599 18.266019 \n", "L 27.782072 17.616008 \n", "L 29.771546 19.932068 \n", "L 30.103125 19.631217 \n", "L 32.092599 32.468332 \n", "L 34.082072 31.147829 \n", "L 36.071546 31.033524 \n", "L 38.06102 30.100771 \n", "L 40.050493 31.096474 \n", "L 40.382072 30.679494 \n", "L 42.371546 32.296541 \n", "L 44.36102 29.37247 \n", "L 46.350493 34.648067 \n", "L 48.339967 34.272686 \n", "L 50.329441 33.676456 \n", "L 50.66102 34.294464 \n", "L 52.650493 29.473629 \n", "L 54.639967 38.790053 \n", "L 56.629441 41.67883 \n", "L 58.618914 42.895393 \n", "L 60.608388 41.184443 \n", "L 60.939967 41.266784 \n", "L 62.929441 53.338468 \n", "L 64.918914 49.841422 \n", "L 66.908388 51.222929 \n", "L 68.897862 52.443357 \n", "L 70.887336 52.896857 \n", "L 71.218914 52.133944 \n", "L 73.208388 56.187029 \n", "L 75.197862 57.341288 \n", "L 77.187336 57.183968 \n", "L 79.176809 55.762464 \n", "L 81.166283 56.322925 \n", "L 81.497862 56.124419 \n", "L 83.487336 56.365256 \n", "L 85.476809 57.087525 \n", "L 87.466283 55.566906 \n", "L 89.455757 54.888433 \n", "L 91.44523 54.376277 \n", "L 91.776809 54.3546 \n", "L 93.766283 61.276164 \n", "L 95.755757 63.474598 \n", "L 97.74523 64.654556 \n", "L 99.734704 63.121007 \n", "L 101.724178 62.530621 \n", "L 102.055757 62.365939 \n", "L 104.04523 75.84821 \n", "L 106.034704 71.955672 \n", "L 108.024178 71.302501 \n", "L 110.013651 70.344833 \n", "L 112.003125 68.986473 \n", "L 112.334704 68.731179 \n", "L 114.324178 76.510282 \n", "L 116.313651 75.710512 \n", "L 118.303125 74.254243 \n", "L 120.292599 75.082482 \n", "L 122.282072 72.90563 \n", "L 122.613651 72.735518 \n", "L 124.603125 75.596438 \n", "L 126.592599 72.299325 \n", "L 128.582072 71.287998 \n", "L 130.571546 71.678102 \n", "L 132.56102 70.797194 \n", "L 132.892599 70.815156 \n", "L 134.882072 73.421423 \n", "L 136.871546 72.194378 \n", "L 138.86102 71.619179 \n", "L 140.850493 73.281206 \n", "L 142.839967 74.445806 \n", "L 143.171546 74.808 \n", "L 145.16102 78.324335 \n", "L 147.150493 84.76538 \n", "L 149.139967 85.679573 \n", "L 151.129441 84.919775 \n", "L 153.118914 82.982495 \n", "L 153.450493 82.476344 \n", "L 155.439967 86.54726 \n", "L 157.429441 89.351413 \n", "L 159.418914 84.926883 \n", "L 161.408388 84.537644 \n", "L 163.397862 84.4443 \n", "L 163.729441 84.441287 \n", "L 165.718914 89.138545 \n", "L 167.708388 88.497873 \n", "L 169.697862 88.835357 \n", "L 171.687336 88.537855 \n", "L 173.676809 88.677391 \n", "L 174.008388 88.461352 \n", "L 175.997862 95.075807 \n", "L 177.987336 94.619025 \n", "L 179.976809 92.074972 \n", "L 181.966283 90.343714 \n", "L 183.955757 90.410474 \n", "L 184.287336 89.43586 \n", "L 186.276809 96.813024 \n", "L 188.266283 96.733044 \n", "L 190.255757 97.411026 \n", "L 192.24523 95.564599 \n", "L 194.234704 94.964774 \n", "L 194.566283 95.334089 \n", "L 196.555757 102.137504 \n", "L 198.54523 99.423311 \n", "L 200.534704 100.824371 \n", "L 202.524178 100.69602 \n", "L 204.513651 100.014606 \n", "L 204.84523 99.262431 \n", "L 206.834704 104.228527 \n", "L 208.824178 106.619282 \n", "L 210.813651 105.029237 \n", "L 212.803125 103.745707 \n", "L 214.792599 104.529381 \n", "L 215.124178 103.707823 \n", "L 217.113651 105.333505 \n", "L 219.103125 105.9524 \n", "L 221.092599 103.254464 \n", "L 223.082072 100.799523 \n", "L 225.071546 101.148067 \n", "L 225.403125 100.839156 \n", "\" clip-path=\"url(#p62659bab81)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_18\">\n", "    <path d=\"M 21.813651 139.5 \n", "L 23.803125 139.040831 \n", "L 25.792599 138.071475 \n", "L 27.782072 137.663325 \n", "L 29.771546 137.112322 \n", "L 30.103125 137.268341 \n", "L 32.092599 133.989974 \n", "L 34.082072 133.224692 \n", "L 36.071546 131.847186 \n", "L 38.06102 132.535939 \n", "L 40.050493 132.765523 \n", "L 40.382072 132.824771 \n", "L 42.371546 135.214424 \n", "L 44.36102 133.836917 \n", "L 46.350493 132.561448 \n", "L 48.339967 132.382883 \n", "L 50.329441 132.275743 \n", "L 50.66102 132.054552 \n", "L 52.650493 130.928848 \n", "L 54.639967 130.01051 \n", "L 56.629441 128.990135 \n", "L 58.618914 128.939116 \n", "L 60.608388 129.76562 \n", "L 60.939967 129.625401 \n", "L 62.929441 126.337159 \n", "L 64.918914 126.796328 \n", "L 66.908388 127.15346 \n", "L 68.897862 126.872856 \n", "L 70.887336 127.194275 \n", "L 71.218914 127.492488 \n", "L 73.208388 125.112709 \n", "L 75.197862 125.265765 \n", "L 77.187336 125.418822 \n", "L 79.176809 125.418822 \n", "L 81.166283 125.908602 \n", "L 81.497862 125.892802 \n", "L 83.487336 123.276034 \n", "L 85.476809 124.041315 \n", "L 87.466283 124.704559 \n", "L 89.455757 125.112709 \n", "L 91.44523 125.418822 \n", "L 91.776809 125.418822 \n", "L 93.766283 123.582146 \n", "L 95.755757 122.051583 \n", "L 97.74523 122.153621 \n", "L 99.734704 123.199506 \n", "L 101.724178 122.969921 \n", "L 102.055757 122.871175 \n", "L 104.04523 119.908795 \n", "L 106.034704 119.143514 \n", "L 108.024178 119.092495 \n", "L 110.013651 119.832267 \n", "L 112.003125 120.643466 \n", "L 112.334704 120.797509 \n", "L 114.324178 121.745471 \n", "L 116.313651 120.061852 \n", "L 118.303125 119.602683 \n", "L 120.292599 118.148648 \n", "L 122.282072 118.868013 \n", "L 122.613651 118.723844 \n", "L 124.603125 118.378233 \n", "L 126.592599 118.531289 \n", "L 128.582072 119.194533 \n", "L 130.571546 118.684345 \n", "L 132.56102 119.174125 \n", "L 132.892599 119.197824 \n", "L 134.882072 118.07212 \n", "L 136.871546 119.449627 \n", "L 138.86102 119.29657 \n", "L 140.850493 118.301704 \n", "L 142.839967 118.133343 \n", "L 143.171546 118.131368 \n", "L 145.16102 116.235445 \n", "L 147.150493 114.551825 \n", "L 149.139967 113.684506 \n", "L 151.129441 113.55696 \n", "L 153.118914 114.582437 \n", "L 153.450493 114.813502 \n", "L 155.439967 111.643756 \n", "L 157.429441 112.409037 \n", "L 159.418914 113.174319 \n", "L 161.408388 113.327375 \n", "L 163.397862 113.908989 \n", "L 163.729441 113.865541 \n", "L 165.718914 113.174319 \n", "L 167.708388 113.633488 \n", "L 169.697862 113.072281 \n", "L 171.687336 113.174319 \n", "L 173.676809 112.684539 \n", "L 174.008388 112.858332 \n", "L 175.997862 108.888743 \n", "L 177.987336 110.419306 \n", "L 179.976809 110.725418 \n", "L 181.966283 111.720284 \n", "L 183.955757 111.643756 \n", "L 184.287336 111.969618 \n", "L 186.276809 108.276518 \n", "L 188.266283 108.276518 \n", "L 190.255757 109.092818 \n", "L 192.24523 109.347912 \n", "L 194.234704 109.745858 \n", "L 194.566283 109.421971 \n", "L 196.555757 105.521505 \n", "L 198.54523 107.205124 \n", "L 200.534704 106.95003 \n", "L 202.524178 107.128596 \n", "L 204.513651 108.154073 \n", "L 204.84523 108.355514 \n", "L 206.834704 107.970405 \n", "L 208.824178 105.521505 \n", "L 210.813651 106.235767 \n", "L 212.803125 106.822483 \n", "L 214.792599 106.8684 \n", "L 215.124178 107.052067 \n", "L 217.113651 105.521505 \n", "L 219.103125 106.899011 \n", "L 221.092599 107.76633 \n", "L 223.082072 108.735687 \n", "L 225.071546 108.031628 \n", "L 225.403125 108.059277 \n", "\" clip-path=\"url(#p62659bab81)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #bf00bf; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_19\"/>\n", "   <g id=\"patch_3\">\n", "    <path d=\"M 30.**********.8 \n", "L 30.103125 7.2 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_4\">\n", "    <path d=\"M 225.**********.8 \n", "L 225.403125 7.2 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_5\">\n", "    <path d=\"M 30.**********.8 \n", "L 225.**********.8 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_6\">\n", "    <path d=\"M 30.103125 7.2 \n", "L 225.403125 7.2 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"legend_1\">\n", "    <g id=\"patch_7\">\n", "     <path d=\"M 140.634375 44.55625 \n", "L 218.403125 44.55625 \n", "Q 220.403125 44.55625 220.403125 42.55625 \n", "L 220.403125 14.2 \n", "Q 220.403125 12.2 218.403125 12.2 \n", "L 140.634375 12.2 \n", "Q 138.634375 12.2 138.634375 14.2 \n", "L 138.634375 42.55625 \n", "Q 138.634375 44.55625 140.634375 44.55625 \n", "z\n", "\" style=\"fill: #ffffff; opacity: 0.8; stroke: #cccccc; stroke-linejoin: miter\"/>\n", "    </g>\n", "    <g id=\"line2d_20\">\n", "     <path d=\"M 142.634375 20.298438 \n", "L 152.634375 20.298438 \n", "L 162.634375 20.298438 \n", "\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "    </g>\n", "    <g id=\"text_10\">\n", "     <!-- train loss -->\n", "     <g transform=\"translate(170.634375 23.798438)scale(0.1 -0.1)\">\n", "      <defs>\n", "       <path id=\"DejaVuSans-74\" d=\"M 1172 4494 \n", "L 1172 3500 \n", "L 2356 3500 \n", "L 2356 3053 \n", "L 1172 3053 \n", "L 1172 1153 \n", "Q 1172 725 1289 603 \n", "Q 1406 481 1766 481 \n", "L 2356 481 \n", "L 2356 0 \n", "L 1766 0 \n", "Q 1100 0 847 248 \n", "Q 594 497 594 1153 \n", "L 594 3053 \n", "L 172 3053 \n", "L 172 3500 \n", "L 594 3500 \n", "L 594 4494 \n", "L 1172 4494 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-72\" d=\"M 2631 2963 \n", "Q 2534 3019 2420 3045 \n", "Q 2306 3072 2169 3072 \n", "Q 1681 3072 1420 2755 \n", "Q 1159 2438 1159 1844 \n", "L 1159 0 \n", "L 581 0 \n", "L 581 3500 \n", "L 1159 3500 \n", "L 1159 2956 \n", "Q 1341 3275 1631 3429 \n", "Q 1922 3584 2338 3584 \n", "Q 2397 3584 2469 3576 \n", "Q 2541 3569 2628 3553 \n", "L 2631 2963 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-61\" d=\"M 2194 1759 \n", "Q 1497 1759 1228 1600 \n", "Q 959 1441 959 1056 \n", "Q 959 750 1161 570 \n", "Q 1363 391 1709 391 \n", "Q 2188 391 2477 730 \n", "Q 2766 1069 2766 1631 \n", "L 2766 1759 \n", "L 2194 1759 \n", "z\n", "M 3341 1997 \n", "L 3341 0 \n", "L 2766 0 \n", "L 2766 531 \n", "Q 2569 213 2275 61 \n", "Q 1981 -91 1556 -91 \n", "Q 1019 -91 701 211 \n", "Q 384 513 384 1019 \n", "Q 384 1609 779 1909 \n", "Q 1175 2209 1959 2209 \n", "L 2766 2209 \n", "L 2766 2266 \n", "Q 2766 2663 2505 2880 \n", "Q 2244 3097 1772 3097 \n", "Q 1472 3097 1187 3025 \n", "Q 903 2953 641 2809 \n", "L 641 3341 \n", "Q 956 3463 1253 3523 \n", "Q 1550 3584 1831 3584 \n", "Q 2591 3584 2966 3190 \n", "Q 3341 2797 3341 1997 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-69\" d=\"M 603 3500 \n", "L 1178 3500 \n", "L 1178 0 \n", "L 603 0 \n", "L 603 3500 \n", "z\n", "M 603 4863 \n", "L 1178 4863 \n", "L 1178 4134 \n", "L 603 4134 \n", "L 603 4863 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-6e\" d=\"M 3513 2113 \n", "L 3513 0 \n", "L 2938 0 \n", "L 2938 2094 \n", "Q 2938 2591 2744 2837 \n", "Q 2550 3084 2163 3084 \n", "Q 1697 3084 1428 2787 \n", "Q 1159 2491 1159 1978 \n", "L 1159 0 \n", "L 581 0 \n", "L 581 3500 \n", "L 1159 3500 \n", "L 1159 2956 \n", "Q 1366 3272 1645 3428 \n", "Q 1925 3584 2291 3584 \n", "Q 2894 3584 3203 3211 \n", "Q 3513 2838 3513 2113 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-20\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-6c\" d=\"M 603 4863 \n", "L 1178 4863 \n", "L 1178 0 \n", "L 603 0 \n", "L 603 4863 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-73\" d=\"M 2834 3397 \n", "L 2834 2853 \n", "Q 2591 2978 2328 3040 \n", "Q 2066 3103 1784 3103 \n", "Q 1356 3103 1142 2972 \n", "Q 928 2841 928 2578 \n", "Q 928 2378 1081 2264 \n", "Q 1234 2150 1697 2047 \n", "L 1894 2003 \n", "Q 2506 1872 2764 1633 \n", "Q 3022 1394 3022 966 \n", "Q 3022 478 2636 193 \n", "Q 2250 -91 1575 -91 \n", "Q 1294 -91 989 -36 \n", "Q 684 19 347 128 \n", "L 347 722 \n", "Q 666 556 975 473 \n", "Q 1284 391 1588 391 \n", "Q 1994 391 2212 530 \n", "Q 2431 669 2431 922 \n", "Q 2431 1156 2273 1281 \n", "Q 2116 1406 1581 1522 \n", "L 1381 1569 \n", "Q 847 1681 609 1914 \n", "Q 372 2147 372 2553 \n", "Q 372 3047 722 3315 \n", "Q 1072 3584 1716 3584 \n", "Q 2034 3584 2315 3537 \n", "Q 2597 3491 2834 3397 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "      </defs>\n", "      <use xlink:href=\"#DejaVuSans-74\"/>\n", "      <use xlink:href=\"#DejaVuSans-72\" x=\"39.208984\"/>\n", "      <use xlink:href=\"#DejaVuSans-61\" x=\"80.322266\"/>\n", "      <use xlink:href=\"#DejaVuSans-69\" x=\"141.601562\"/>\n", "      <use xlink:href=\"#DejaVuSans-6e\" x=\"169.384766\"/>\n", "      <use xlink:href=\"#DejaVuSans-20\" x=\"232.763672\"/>\n", "      <use xlink:href=\"#DejaVuSans-6c\" x=\"264.550781\"/>\n", "      <use xlink:href=\"#DejaVuSans-6f\" x=\"292.333984\"/>\n", "      <use xlink:href=\"#DejaVuSans-73\" x=\"353.515625\"/>\n", "      <use xlink:href=\"#DejaVuSans-73\" x=\"405.615234\"/>\n", "     </g>\n", "    </g>\n", "    <g id=\"line2d_21\">\n", "     <path d=\"M 142.634375 34.976562 \n", "L 152.634375 34.976562 \n", "L 162.634375 34.976562 \n", "\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #bf00bf; stroke-width: 1.5\"/>\n", "    </g>\n", "    <g id=\"text_11\">\n", "     <!-- train acc -->\n", "     <g transform=\"translate(170.634375 38.476562)scale(0.1 -0.1)\">\n", "      <use xlink:href=\"#DejaVuSans-74\"/>\n", "      <use xlink:href=\"#DejaVuSans-72\" x=\"39.208984\"/>\n", "      <use xlink:href=\"#DejaVuSans-61\" x=\"80.322266\"/>\n", "      <use xlink:href=\"#DejaVuSans-69\" x=\"141.601562\"/>\n", "      <use xlink:href=\"#DejaVuSans-6e\" x=\"169.384766\"/>\n", "      <use xlink:href=\"#DejaVuSans-20\" x=\"232.763672\"/>\n", "      <use xlink:href=\"#DejaVuSans-61\" x=\"264.550781\"/>\n", "      <use xlink:href=\"#DejaVuSans-63\" x=\"325.830078\"/>\n", "      <use xlink:href=\"#DejaVuSans-63\" x=\"380.810547\"/>\n", "     </g>\n", "    </g>\n", "   </g>\n", "  </g>\n", " </g>\n", " <defs>\n", "  <clipPath id=\"p62659bab81\">\n", "   <rect x=\"30.103125\" y=\"7.2\" width=\"195.3\" height=\"138.6\"/>\n", "  </clipPath>\n", " </defs>\n", "</svg>\n"], "text/plain": ["<Figure size 350x250 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["net, preds = get_net(), []\n", "train(net, train_valid_iter, None, num_epochs, lr, wd, devices, lr_period,\n", "      lr_decay)\n", "\n", "for X, _ in test_iter:\n", "    y_hat = net(X.to(devices[0]))\n", "    preds.extend(y_hat.argmax(dim=1).type(torch.int32).cpu().numpy())\n", "sorted_ids = list(range(1, len(test_ds) + 1))\n", "sorted_ids.sort(key=lambda x: str(x))\n", "df = pd.DataFrame({'id': sorted_ids, 'label': preds})\n", "df['label'] = df['label'].apply(lambda x: train_valid_ds.classes[x])\n", "df.to_csv('submission.csv', index=False)"]}, {"cell_type": "markdown", "id": "ca9aa8ed", "metadata": {"origin_pos": 55}, "source": ["向Kaggle提交结果的方法与 :numref:`sec_kaggle_house`中的方法类似，上面的代码将生成一个\n", "`submission.csv`文件，其格式符合Kaggle竞赛的要求。\n", "\n", "## 小结\n", "\n", "* 将包含原始图像文件的数据集组织为所需格式后，我们可以读取它们。\n"]}, {"cell_type": "markdown", "id": "c643aeee", "metadata": {"origin_pos": 57, "tab": ["pytorch"]}, "source": ["* 我们可以在图像分类竞赛中使用卷积神经网络和图像增广。\n"]}, {"cell_type": "markdown", "id": "fb5c2fb5", "metadata": {"origin_pos": 59}, "source": ["## 练习\n", "\n", "1. 在这场Kaggle竞赛中使用完整的CIFAR-10数据集。将超参数设为`batch_size = 128`，`num_epochs = 100`，`lr = 0.1`，`lr_period = 50`，`lr_decay = 0.1`。看看在这场比赛中能达到什么准确度和排名。能进一步改进吗？\n", "1. 不使用图像增广时，能获得怎样的准确度？\n"]}, {"cell_type": "markdown", "id": "289a2bed", "metadata": {"origin_pos": 61, "tab": ["pytorch"]}, "source": ["[Discussions](https://discuss.d2l.ai/t/2831)\n"]}], "metadata": {"language_info": {"name": "python"}}, "nbformat": 4, "nbformat_minor": 5}