{"cells": [{"cell_type": "markdown", "id": "81a28a92", "metadata": {"origin_pos": 0}, "source": ["# 实战Kaggle比赛：预测房价\n", ":label:`sec_kaggle_house`\n", "\n", "之前几节我们学习了一些训练深度网络的基本工具和网络正则化的技术（如权重衰减、暂退法等）。\n", "本节我们将通过Kaggle比赛，将所学知识付诸实践。\n", "Kaggle的房价预测比赛是一个很好的起点。\n", "此数据集由<PERSON>于2011年收集 :cite:`<PERSON>-Co<PERSON>.2011`，\n", "涵盖了2006-2010年期间亚利桑那州埃姆斯市的房价。\n", "这个数据集是相当通用的，不会需要使用复杂模型架构。\n", "它比哈里森和鲁宾菲尔德的[波士顿房价](https://archive.ics.uci.edu/ml/machine-learning-databases/housing/housing.names)\n", "数据集要大得多，也有更多的特征。\n", "\n", "本节我们将详细介绍数据预处理、模型设计和超参数选择。\n", "通过亲身实践，你将获得一手经验，这些经验将有益数据科学家的职业成长。\n", "\n", "## 下载和缓存数据集\n", "\n", "在整本书中，我们将下载不同的数据集，并训练和测试模型。\n", "这里我们(**实现几个函数来方便下载数据**)。\n", "首先，我们建立字典`DATA_HUB`，\n", "它可以将数据集名称的字符串映射到数据集相关的二元组上，\n", "这个二元组包含数据集的url和验证文件完整性的sha-1密钥。\n", "所有类似的数据集都托管在地址为`DATA_URL`的站点上。\n"]}, {"cell_type": "code", "execution_count": 1, "id": "e77e804a", "metadata": {"execution": {"iopub.execute_input": "2022-12-07T16:58:08.770045Z", "iopub.status.busy": "2022-12-07T16:58:08.769489Z", "iopub.status.idle": "2022-12-07T16:58:08.826735Z", "shell.execute_reply": "2022-12-07T16:58:08.825976Z"}, "origin_pos": 1, "tab": ["pytorch"]}, "outputs": [], "source": ["import hashlib\n", "import os\n", "import tarfile\n", "import zipfile\n", "import requests\n", "\n", "#@save\n", "DATA_HUB = dict()\n", "DATA_URL = 'http://d2l-data.s3-accelerate.amazonaws.com/'"]}, {"cell_type": "markdown", "id": "a2853cb9", "metadata": {"origin_pos": 2}, "source": ["下面的`download`函数用来下载数据集，\n", "将数据集缓存在本地目录（默认情况下为`../data`）中，\n", "并返回下载文件的名称。\n", "如果缓存目录中已经存在此数据集文件，并且其sha-1与存储在`DATA_HUB`中的相匹配，\n", "我们将使用缓存的文件，以避免重复的下载。\n"]}, {"cell_type": "code", "execution_count": 2, "id": "356b9f06", "metadata": {"execution": {"iopub.execute_input": "2022-12-07T16:58:08.830467Z", "iopub.status.busy": "2022-12-07T16:58:08.830041Z", "iopub.status.idle": "2022-12-07T16:58:08.837352Z", "shell.execute_reply": "2022-12-07T16:58:08.836620Z"}, "origin_pos": 3, "tab": ["pytorch"]}, "outputs": [], "source": ["def download(name, cache_dir=os.path.join('..', 'data')):  #@save\n", "    \"\"\"下载一个DATA_HUB中的文件，返回本地文件名\"\"\"\n", "    assert name in DATA_HUB, f\"{name} 不存在于 {DATA_HUB}\"\n", "    url, sha1_hash = DATA_HUB[name]\n", "    os.makedirs(cache_dir, exist_ok=True)\n", "    fname = os.path.join(cache_dir, url.split('/')[-1])\n", "    if os.path.exists(fname):\n", "        sha1 = hashlib.sha1()\n", "        with open(fname, 'rb') as f:\n", "            while True:\n", "                data = f.read(1048576)\n", "                if not data:\n", "                    break\n", "                sha1.update(data)\n", "        if sha1.hexdigest() == sha1_hash:\n", "            return fname  # 命中缓存\n", "    print(f'正在从{url}下载{fname}...')\n", "    r = requests.get(url, stream=True, verify=True)\n", "    with open(fname, 'wb') as f:\n", "        f.write(r.content)\n", "    return fname"]}, {"cell_type": "markdown", "id": "827e8ddb", "metadata": {"origin_pos": 4}, "source": ["我们还需实现两个实用函数：\n", "一个将下载并解压缩一个zip或tar文件，\n", "另一个是将本书中使用的所有数据集从`DATA_HUB`下载到缓存目录中。\n"]}, {"cell_type": "code", "execution_count": 4, "id": "2f3da160", "metadata": {"execution": {"iopub.execute_input": "2022-12-07T16:58:08.840617Z", "iopub.status.busy": "2022-12-07T16:58:08.840196Z", "iopub.status.idle": "2022-12-07T16:58:08.845984Z", "shell.execute_reply": "2022-12-07T16:58:08.845294Z"}, "origin_pos": 5, "tab": ["pytorch"]}, "outputs": [], "source": ["def download_extract(name, folder=None):  #@save\n", "    \"\"\"下载并解压zip/tar文件\"\"\"\n", "    fname = download(name)\n", "    base_dir = os.path.dirname(fname)\n", "    data_dir, ext = os.path.splitext(fname)\n", "    if ext == '.zip':\n", "        fp = zipfile.ZipFile(fname, 'r')\n", "    elif ext in ('.tar', '.gz'):\n", "        fp = tarfile.open(fname, 'r')\n", "    else:\n", "        assert False, '只有zip/tar文件可以被解压缩'\n", "    fp.extractall(base_dir)\n", "    return os.path.join(base_dir, folder) if folder else data_dir\n", "\n", "def download_all():  #@save\n", "    \"\"\"下载DATA_HUB中的所有文件\"\"\"\n", "    for name in DATA_HUB:\n", "        download(name)"]}, {"cell_type": "markdown", "id": "681a2cf9", "metadata": {"origin_pos": 6}, "source": ["## Kaggle\n", "\n", "[Kaggle](https://www.kaggle.com)是一个当今流行举办机器学习比赛的平台，\n", "每场比赛都以至少一个数据集为中心。\n", "许多比赛有赞助方，他们为获胜的解决方案提供奖金。\n", "该平台帮助用户通过论坛和共享代码进行互动，促进协作和竞争。\n", "虽然排行榜的追逐往往令人失去理智：\n", "有些研究人员短视地专注于预处理步骤，而不是考虑基础性问题。\n", "但一个客观的平台有巨大的价值：该平台促进了竞争方法之间的直接定量比较，以及代码共享。\n", "这便于每个人都可以学习哪些方法起作用，哪些没有起作用。\n", "如果我们想参加Kaggle比赛，首先需要注册一个账户（见 :numref:`fig_kaggle`）。\n", "\n", "![Kaggle网站](../img/kaggle.png)\n", ":width:`400px`\n", ":label:`fig_kaggle`\n", "\n", "在房价预测比赛页面（如 :numref:`fig_house_pricing` 所示）的\"Data\"选项卡下可以找到数据集。我们可以通过下面的网址提交预测，并查看排名：\n", "\n", ">https://www.kaggle.com/c/house-prices-advanced-regression-techniques\n", "\n", "![房价预测比赛页面](../img/house-pricing.png)\n", ":width:`400px`\n", ":label:`fig_house_pricing`\n", "\n", "## 访问和读取数据集\n", "\n", "注意，竞赛数据分为训练集和测试集。\n", "每条记录都包括房屋的属性值和属性，如街道类型、施工年份、屋顶类型、地下室状况等。\n", "这些特征由各种数据类型组成。\n", "例如，建筑年份由整数表示，屋顶类型由离散类别表示，其他特征由浮点数表示。\n", "这就是现实让事情变得复杂的地方：例如，一些数据完全丢失了，缺失值被简单地标记为“NA”。\n", "每套房子的价格只出现在训练集中（毕竟这是一场比赛）。\n", "我们将希望划分训练集以创建验证集，但是在将预测结果上传到Kaggle之后，\n", "我们只能在官方测试集中评估我们的模型。\n", "在 :numref:`fig_house_pricing` 中，\"Data\"选项卡有下载数据的链接。\n", "\n", "开始之前，我们将[**使用`pandas`读入并处理数据**]，\n", "这是我们在 :numref:`sec_pandas`中引入的。\n", "因此，在继续操作之前，我们需要确保已安装`pandas`。\n", "幸运的是，如果我们正在用Jupyter阅读该书，可以在不离开笔记本的情况下安装`pandas`。\n"]}, {"cell_type": "code", "execution_count": 5, "id": "5f97aaa0", "metadata": {"execution": {"iopub.execute_input": "2022-12-07T16:58:08.849212Z", "iopub.status.busy": "2022-12-07T16:58:08.848803Z", "iopub.status.idle": "2022-12-07T16:58:11.308665Z", "shell.execute_reply": "2022-12-07T16:58:11.307847Z"}, "origin_pos": 8, "tab": ["pytorch"]}, "outputs": [], "source": ["# 如果没有安装pandas，请取消下一行的注释\n", "# !pip install pandas\n", "\n", "%matplotlib inline\n", "import numpy as np\n", "import pandas as pd\n", "import torch\n", "from torch import nn\n", "from d2l import torch as d2l"]}, {"cell_type": "markdown", "id": "54c52b31", "metadata": {"origin_pos": 11}, "source": ["为方便起见，我们可以使用上面定义的脚本下载并缓存Kaggle房屋数据集。\n"]}, {"cell_type": "code", "execution_count": 6, "id": "7b6cc29f", "metadata": {"execution": {"iopub.execute_input": "2022-12-07T16:58:11.312652Z", "iopub.status.busy": "2022-12-07T16:58:11.312099Z", "iopub.status.idle": "2022-12-07T16:58:11.316331Z", "shell.execute_reply": "2022-12-07T16:58:11.315618Z"}, "origin_pos": 12, "tab": ["pytorch"]}, "outputs": [], "source": ["DATA_HUB['kaggle_house_train'] = (  #@save\n", "    DATA_URL + 'kaggle_house_pred_train.csv',\n", "    '585e9cc93e70b39160e7921475f9bcd7d31219ce')\n", "\n", "DATA_HUB['kaggle_house_test'] = (  #@save\n", "    DATA_URL + 'kaggle_house_pred_test.csv',\n", "    'fa19780a7b011d9b009e8bff8e99922a8ee2eb90')"]}, {"cell_type": "markdown", "id": "d656e46d", "metadata": {"origin_pos": 13}, "source": ["我们使用`pandas`分别加载包含训练数据和测试数据的两个CSV文件。\n"]}, {"cell_type": "code", "execution_count": 9, "id": "79f5e240", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["正在从http://d2l-data.s3-accelerate.amazonaws.com/kaggle_house_pred_test.csv下载..\\data\\kaggle_house_pred_test.csv...\n"]}, {"data": {"text/plain": ["'..\\\\data\\\\kaggle_house_pred_test.csv'"]}, "execution_count": 9, "metadata": {}, "output_type": "execute_result"}], "source": ["download('kaggle_house_test')"]}, {"cell_type": "code", "execution_count": null, "id": "c44a403e", "metadata": {"execution": {"iopub.execute_input": "2022-12-07T16:58:11.319488Z", "iopub.status.busy": "2022-12-07T16:58:11.319067Z", "iopub.status.idle": "2022-12-07T16:58:11.659100Z", "shell.execute_reply": "2022-12-07T16:58:11.658271Z"}, "origin_pos": 14, "tab": ["pytorch"]}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["正在从http://d2l-data.s3-accelerate.amazonaws.com/kaggle_house_pred_train.csv下载..\\data\\kaggle_house_pred_train.csv...\n"]}], "source": ["# train_data = pd.read_csv(download('kaggle_house_train'))\n", "# test_data = pd.read_csv(download('kaggle_house_test'))"]}, {"cell_type": "markdown", "id": "49958d10", "metadata": {"origin_pos": 15}, "source": ["训练数据集包括1460个样本，每个样本80个特征和1个标签，\n", "而测试数据集包含1459个样本，每个样本80个特征。\n"]}, {"cell_type": "code", "execution_count": 12, "id": "878482ce", "metadata": {"execution": {"iopub.execute_input": "2022-12-07T16:58:11.663888Z", "iopub.status.busy": "2022-12-07T16:58:11.663613Z", "iopub.status.idle": "2022-12-07T16:58:11.668108Z", "shell.execute_reply": "2022-12-07T16:58:11.667361Z"}, "origin_pos": 16, "tab": ["pytorch"]}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["(1460, 81)\n", "(1459, 80)\n"]}], "source": ["print(train_data.shape)\n", "print(test_data.shape)"]}, {"cell_type": "markdown", "id": "c1d1caf0", "metadata": {"origin_pos": 17}, "source": ["让我们看看[**前四个和最后两个特征，以及相应标签**]（房价）。\n"]}, {"cell_type": "code", "execution_count": 13, "id": "5a2b4c7f", "metadata": {"execution": {"iopub.execute_input": "2022-12-07T16:58:11.672773Z", "iopub.status.busy": "2022-12-07T16:58:11.672504Z", "iopub.status.idle": "2022-12-07T16:58:11.682544Z", "shell.execute_reply": "2022-12-07T16:58:11.681821Z"}, "origin_pos": 18, "tab": ["pytorch"]}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["   Id  MSSubClass MSZoning  LotFrontage SaleType SaleCondition  SalePrice\n", "0   1          60       RL         65.0       WD        Normal     208500\n", "1   2          20       RL         80.0       WD        Normal     181500\n", "2   3          60       RL         68.0       WD        Normal     223500\n", "3   4          70       RL         60.0       WD       Abnorml     140000\n"]}], "source": ["print(train_data.iloc[0:4, [0, 1, 2, 3, -3, -2, -1]])"]}, {"cell_type": "markdown", "id": "75857453", "metadata": {"origin_pos": 19}, "source": ["我们可以看到，(**在每个样本中，第一个特征是ID，**)\n", "这有助于模型识别每个训练样本。\n", "虽然这很方便，但它不携带任何用于预测的信息。\n", "因此，在将数据提供给模型之前，(**我们将其从数据集中删除**)。\n"]}, {"cell_type": "code", "execution_count": 15, "id": "6ae7518f", "metadata": {"execution": {"iopub.execute_input": "2022-12-07T16:58:11.685836Z", "iopub.status.busy": "2022-12-07T16:58:11.685412Z", "iopub.status.idle": "2022-12-07T16:58:11.704825Z", "shell.execute_reply": "2022-12-07T16:58:11.704052Z"}, "origin_pos": 20, "tab": ["pytorch"]}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["(2919, 79)\n"]}], "source": ["all_features = pd.concat((train_data.iloc[:, 1:-1], test_data.iloc[:, 1:]))\n", "print(all_features.shape)"]}, {"cell_type": "markdown", "id": "1f0ffb9c", "metadata": {"origin_pos": 21}, "source": ["## 数据预处理\n", "\n", "如上所述，我们有各种各样的数据类型。\n", "在开始建模之前，我们需要对数据进行预处理。\n", "首先，我们[**将所有缺失的值替换为相应特征的平均值。**]然后，为了将所有特征放在一个共同的尺度上，\n", "我们(**通过将特征重新缩放到零均值和单位方差来标准化数据**)：\n", "\n", "$$x \\leftarrow \\frac{x - \\mu}{\\sigma},$$\n", "\n", "其中$\\mu$和$\\sigma$分别表示均值和标准差。\n", "现在，这些特征具有零均值和单位方差，即 $E[\\frac{x-\\mu}{\\sigma}] = \\frac{\\mu - \\mu}{\\sigma} = 0$和$E[(x-\\mu)^2] = (\\sigma^2 + \\mu^2) - 2\\mu^2+\\mu^2 = \\sigma^2$。\n", "直观地说，我们标准化数据有两个原因：\n", "首先，它方便优化。\n", "其次，因为我们不知道哪些特征是相关的，\n", "所以我们不想让惩罚分配给一个特征的系数比分配给其他任何特征的系数更大。\n"]}, {"cell_type": "code", "execution_count": 16, "id": "e3f04a25", "metadata": {"execution": {"iopub.execute_input": "2022-12-07T16:58:11.707930Z", "iopub.status.busy": "2022-12-07T16:58:11.707662Z", "iopub.status.idle": "2022-12-07T16:58:11.760252Z", "shell.execute_reply": "2022-12-07T16:58:11.759456Z"}, "origin_pos": 22, "tab": ["pytorch"]}, "outputs": [], "source": ["# 若无法获得测试数据，则可根据训练数据计算均值和标准差\n", "numeric_features = all_features.dtypes[all_features.dtypes != 'object'].index\n", "all_features[numeric_features] = all_features[numeric_features].apply(\n", "    lambda x: (x - x.mean()) / (x.std()))\n", "# 在标准化数据之后，所有均值消失，因此我们可以将缺失值设置为0\n", "all_features[numeric_features] = all_features[numeric_features].fillna(0)"]}, {"cell_type": "markdown", "id": "ddc49117", "metadata": {"origin_pos": 23}, "source": ["接下来，我们[**处理离散值。**]\n", "这包括诸如“MSZoning”之类的特征。\n", "(**我们用独热编码替换它们**)，\n", "方法与前面将多类别标签转换为向量的方式相同\n", "（请参见 :numref:`subsec_classification-problem`）。\n", "例如，“MSZoning”包含值“RL”和“Rm”。\n", "我们将创建两个新的指示器特征“MSZoning_RL”和“MSZoning_RM”，其值为0或1。\n", "根据独热编码，如果“MSZoning”的原始值为“RL”，\n", "则：“MSZoning_RL”为1，“MSZoning_RM”为0。\n", "`pandas`软件包会自动为我们实现这一点。\n"]}, {"cell_type": "code", "execution_count": 17, "id": "abd82847", "metadata": {"execution": {"iopub.execute_input": "2022-12-07T16:58:11.763952Z", "iopub.status.busy": "2022-12-07T16:58:11.763302Z", "iopub.status.idle": "2022-12-07T16:58:11.817682Z", "shell.execute_reply": "2022-12-07T16:58:11.816915Z"}, "origin_pos": 24, "tab": ["pytorch"]}, "outputs": [{"data": {"text/plain": ["(2919, 331)"]}, "execution_count": 17, "metadata": {}, "output_type": "execute_result"}], "source": ["# “Dummy_na=True”将“na”（缺失值）视为有效的特征值，并为其创建指示符特征\n", "all_features = pd.get_dummies(all_features, dummy_na=True)\n", "all_features.shape"]}, {"cell_type": "markdown", "id": "1383759e", "metadata": {"origin_pos": 25}, "source": ["可以看到此转换会将特征的总数量从79个增加到331个。\n", "最后，通过`values`属性，我们可以\n", "[**从`pandas`格式中提取NumPy格式，并将其转换为张量表示**]用于训练。\n"]}, {"cell_type": "code", "execution_count": 18, "id": "be9f058a", "metadata": {"execution": {"iopub.execute_input": "2022-12-07T16:58:11.822849Z", "iopub.status.busy": "2022-12-07T16:58:11.822284Z", "iopub.status.idle": "2022-12-07T16:58:11.856262Z", "shell.execute_reply": "2022-12-07T16:58:11.855484Z"}, "origin_pos": 26, "tab": ["pytorch"]}, "outputs": [], "source": ["n_train = train_data.shape[0]\n", "train_features = torch.tensor(all_features[:n_train].values, dtype=torch.float32)\n", "test_features = torch.tensor(all_features[n_train:].values, dtype=torch.float32)\n", "train_labels = torch.tensor(\n", "    train_data.SalePrice.values.reshape(-1, 1), dtype=torch.float32)"]}, {"cell_type": "markdown", "id": "cae76ca3", "metadata": {"origin_pos": 27}, "source": ["## [**训练**]\n", "\n", "首先，我们训练一个带有损失平方的线性模型。\n", "显然线性模型很难让我们在竞赛中获胜，但线性模型提供了一种健全性检查，\n", "以查看数据中是否存在有意义的信息。\n", "如果我们在这里不能做得比随机猜测更好，那么我们很可能存在数据处理错误。\n", "如果一切顺利，线性模型将作为*基线*（baseline）模型，\n", "让我们直观地知道最好的模型有超出简单的模型多少。\n"]}, {"cell_type": "code", "execution_count": 19, "id": "be979080", "metadata": {"execution": {"iopub.execute_input": "2022-12-07T16:58:11.859991Z", "iopub.status.busy": "2022-12-07T16:58:11.859487Z", "iopub.status.idle": "2022-12-07T16:58:11.863808Z", "shell.execute_reply": "2022-12-07T16:58:11.863058Z"}, "origin_pos": 29, "tab": ["pytorch"]}, "outputs": [], "source": ["loss = nn.<PERSON><PERSON><PERSON>()\n", "in_features = train_features.shape[1]\n", "\n", "def get_net():\n", "    net = nn.Sequential(nn.Linear(in_features,1))\n", "    return net"]}, {"cell_type": "markdown", "id": "f2088ef1", "metadata": {"origin_pos": 31}, "source": ["房价就像股票价格一样，我们关心的是相对数量，而不是绝对数量。\n", "因此，[**我们更关心相对误差$\\frac{y - \\hat{y}}{y}$，**]\n", "而不是绝对误差$y - \\hat{y}$。\n", "例如，如果我们在俄亥俄州农村地区估计一栋房子的价格时，\n", "假设我们的预测偏差了10万美元，\n", "然而那里一栋典型的房子的价值是12.5万美元，\n", "那么模型可能做得很糟糕。\n", "另一方面，如果我们在加州豪宅区的预测出现同样的10万美元的偏差，\n", "（在那里，房价中位数超过400万美元）\n", "这可能是一个不错的预测。\n", "\n", "(**解决这个问题的一种方法是用价格预测的对数来衡量差异**)。\n", "事实上，这也是比赛中官方用来评价提交质量的误差指标。\n", "即将$\\delta$ for $|\\log y - \\log \\hat{y}| \\leq \\delta$\n", "转换为$e^{-\\delta} \\leq \\frac{\\hat{y}}{y} \\leq e^\\delta$。\n", "这使得预测价格的对数与真实标签价格的对数之间出现以下均方根误差：\n", "\n", "$$\\sqrt{\\frac{1}{n}\\sum_{i=1}^n\\left(\\log y_i -\\log \\hat{y}_i\\right)^2}.$$\n"]}, {"cell_type": "code", "execution_count": 20, "id": "86d513c0", "metadata": {"execution": {"iopub.execute_input": "2022-12-07T16:58:11.866927Z", "iopub.status.busy": "2022-12-07T16:58:11.866615Z", "iopub.status.idle": "2022-12-07T16:58:11.871155Z", "shell.execute_reply": "2022-12-07T16:58:11.870369Z"}, "origin_pos": 33, "tab": ["pytorch"]}, "outputs": [], "source": ["def log_rmse(net, features, labels):\n", "    # 为了在取对数时进一步稳定该值，将小于1的值设置为1\n", "    clipped_preds = torch.clamp(net(features), 1, float('inf'))\n", "    rmse = torch.sqrt(loss(torch.log(clipped_preds),\n", "                           torch.log(labels)))\n", "    return rmse.item()"]}, {"cell_type": "markdown", "id": "a264435a", "metadata": {"origin_pos": 36}, "source": ["与前面的部分不同，[**我们的训练函数将借助Adam优化器**]\n", "（我们将在后面章节更详细地描述它）。\n", "Adam优化器的主要吸引力在于它对初始学习率不那么敏感。\n"]}, {"cell_type": "code", "execution_count": 21, "id": "e3cc555f", "metadata": {"execution": {"iopub.execute_input": "2022-12-07T16:58:11.874441Z", "iopub.status.busy": "2022-12-07T16:58:11.873956Z", "iopub.status.idle": "2022-12-07T16:58:11.879898Z", "shell.execute_reply": "2022-12-07T16:58:11.879181Z"}, "origin_pos": 38, "tab": ["pytorch"]}, "outputs": [], "source": ["def train(net, train_features, train_labels, test_features, test_labels,\n", "          num_epochs, learning_rate, weight_decay, batch_size):\n", "    train_ls, test_ls = [], []\n", "    train_iter = d2l.load_array((train_features, train_labels), batch_size)\n", "    # 这里使用的是Adam优化算法\n", "    optimizer = torch.optim.Adam(net.parameters(),\n", "                                 lr = learning_rate,\n", "                                 weight_decay = weight_decay)\n", "    for epoch in range(num_epochs):\n", "        for X, y in train_iter:\n", "            optimizer.zero_grad()\n", "            l = loss(net(X), y)\n", "            l.backward()\n", "            optimizer.step()\n", "        train_ls.append(log_rmse(net, train_features, train_labels))\n", "        if test_labels is not None:\n", "            test_ls.append(log_rmse(net, test_features, test_labels))\n", "    return train_ls, test_ls"]}, {"cell_type": "markdown", "id": "ff14bef2", "metadata": {"origin_pos": 41}, "source": ["## $K$折交叉验证\n", "\n", "本书在讨论模型选择的部分（ :numref:`sec_model_selection`）\n", "中介绍了[**K折交叉验证**]，\n", "它有助于模型选择和超参数调整。\n", "我们首先需要定义一个函数，在$K$折交叉验证过程中返回第$i$折的数据。\n", "具体地说，它选择第$i$个切片作为验证数据，其余部分作为训练数据。\n", "注意，这并不是处理数据的最有效方法，如果我们的数据集大得多，会有其他解决办法。\n"]}, {"cell_type": "code", "execution_count": 22, "id": "c414b140", "metadata": {"execution": {"iopub.execute_input": "2022-12-07T16:58:11.884516Z", "iopub.status.busy": "2022-12-07T16:58:11.883911Z", "iopub.status.idle": "2022-12-07T16:58:11.889553Z", "shell.execute_reply": "2022-12-07T16:58:11.888828Z"}, "origin_pos": 42, "tab": ["pytorch"]}, "outputs": [], "source": ["def get_k_fold_data(k, i, X, y):\n", "    assert k > 1\n", "    fold_size = X.shape[0] // k\n", "    X_train, y_train = None, None\n", "    for j in range(k):\n", "        idx = slice(j * fold_size, (j + 1) * fold_size)\n", "        X_part, y_part = X[idx, :], y[idx]\n", "        if j == i:\n", "            X_valid, y_valid = X_part, y_part\n", "        elif <PERSON>_train is None:\n", "            X_train, y_train = X_part, y_part\n", "        else:\n", "            X_train = torch.cat([X_train, X_part], 0)\n", "            y_train = torch.cat([y_train, y_part], 0)\n", "    return X_train, y_train, X_valid, y_valid"]}, {"cell_type": "markdown", "id": "216ad38d", "metadata": {"origin_pos": 43}, "source": ["当我们在$K$折交叉验证中训练$K$次后，[**返回训练和验证误差的平均值**]。\n"]}, {"cell_type": "code", "execution_count": 24, "id": "ed08a37a", "metadata": {"execution": {"iopub.execute_input": "2022-12-07T16:58:11.894038Z", "iopub.status.busy": "2022-12-07T16:58:11.893447Z", "iopub.status.idle": "2022-12-07T16:58:11.899693Z", "shell.execute_reply": "2022-12-07T16:58:11.898908Z"}, "origin_pos": 44, "tab": ["pytorch"]}, "outputs": [], "source": ["def k_fold(k, X_train, y_train, num_epochs, learning_rate, weight_decay,\n", "           batch_size):\n", "    train_l_sum, valid_l_sum = 0, 0\n", "    for i in range(k):\n", "        data = get_k_fold_data(k, i, X_train, y_train)\n", "        net = get_net()\n", "        train_ls, valid_ls = train(net, *data, num_epochs, learning_rate,\n", "                                   weight_decay, batch_size)\n", "        train_l_sum += train_ls[-1]\n", "        valid_l_sum += valid_ls[-1]\n", "        if i == 0:\n", "            d2l.plot(list(range(1, num_epochs + 1)), [train_ls, valid_ls],\n", "                     xlabel='epoch', ylabel='rmse', xlim=[1, num_epochs],\n", "                     legend=['train', 'valid'], yscale='log')\n", "        print(f'折{i + 1}，训练log rmse{float(train_ls[-1]):f}, '\n", "              f'验证log rmse{float(valid_ls[-1]):f}')\n", "    return train_l_sum / k, valid_l_sum / k"]}, {"cell_type": "markdown", "id": "5d5cc155", "metadata": {"origin_pos": 45}, "source": ["## [**模型选择**]\n", "\n", "在本例中，我们选择了一组未调优的超参数，并将其留给读者来改进模型。\n", "找到一组调优的超参数可能需要时间，这取决于一个人优化了多少变量。\n", "有了足够大的数据集和合理设置的超参数，$K$折交叉验证往往对多次测试具有相当的稳定性。\n", "然而，如果我们尝试了不合理的超参数，我们可能会发现验证效果不再代表真正的误差。\n"]}, {"cell_type": "code", "execution_count": null, "id": "466ade55", "metadata": {"execution": {"iopub.execute_input": "2022-12-07T16:58:11.904149Z", "iopub.status.busy": "2022-12-07T16:58:11.903621Z", "iopub.status.idle": "2022-12-07T16:58:22.807409Z", "shell.execute_reply": "2022-12-07T16:58:22.806607Z"}, "origin_pos": 46, "tab": ["pytorch"]}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["折1，训练log rmse0.170158, 验证log rmse0.156702\n", "折2，训练log rmse0.162328, 验证log rmse0.190687\n", "折3，训练log rmse0.163497, 验证log rmse0.168302\n", "折4，训练log rmse0.167954, 验证log rmse0.155199\n", "折5，训练log rmse0.164268, 验证log rmse0.183363\n", "5-折验证: 平均训练log rmse: 0.165641, 平均验证log rmse: 0.170850\n"]}, {"data": {"image/svg+xml": ["<?xml version=\"1.0\" encoding=\"utf-8\" standalone=\"no\"?>\n", "<!DOCTYPE svg PUBLIC \"-//W3C//DTD SVG 1.1//EN\"\n", "  \"http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd\">\n", "<svg xmlns:xlink=\"http://www.w3.org/1999/xlink\" width=\"257.521875pt\" height=\"183.35625pt\" viewBox=\"0 0 257.**********.35625\" xmlns=\"http://www.w3.org/2000/svg\" version=\"1.1\">\n", " <metadata>\n", "  <rdf:RDF xmlns:dc=\"http://purl.org/dc/elements/1.1/\" xmlns:cc=\"http://creativecommons.org/ns#\" xmlns:rdf=\"http://www.w3.org/1999/02/22-rdf-syntax-ns#\">\n", "   <cc:Work>\n", "    <dc:type rdf:resource=\"http://purl.org/dc/dcmitype/StillImage\"/>\n", "    <dc:date>2025-07-21T23:10:34.046165</dc:date>\n", "    <dc:format>image/svg+xml</dc:format>\n", "    <dc:creator>\n", "     <cc:Agent>\n", "      <dc:title>Matplotlib v3.5.1, https://matplotlib.org/</dc:title>\n", "     </cc:Agent>\n", "    </dc:creator>\n", "   </cc:Work>\n", "  </rdf:RDF>\n", " </metadata>\n", " <defs>\n", "  <style type=\"text/css\">*{stroke-linejoin: round; stroke-linecap: butt}</style>\n", " </defs>\n", " <g id=\"figure_1\">\n", "  <g id=\"patch_1\">\n", "   <path d=\"M 0 183.35625 \n", "L 257.**********.35625 \n", "L 257.521875 0 \n", "L 0 0 \n", "z\n", "\" style=\"fill: #ffffff\"/>\n", "  </g>\n", "  <g id=\"axes_1\">\n", "   <g id=\"patch_2\">\n", "    <path d=\"M 45.**********.8 \n", "L 240.**********.8 \n", "L 240.778125 7.2 \n", "L 45.478125 7.2 \n", "z\n", "\" style=\"fill: #ffffff\"/>\n", "   </g>\n", "   <g id=\"matplotlib.axis_1\">\n", "    <g id=\"xtick_1\">\n", "     <g id=\"line2d_1\">\n", "      <path d=\"M 82.959943 145.8 \n", "L 82.959943 7.2 \n", "\" clip-path=\"url(#p7024c6bc03)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_2\">\n", "      <defs>\n", "       <path id=\"m71383136c4\" d=\"M 0 0 \n", "L 0 3.5 \n", "\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </defs>\n", "      <g>\n", "       <use xlink:href=\"#m71383136c4\" x=\"82.959943\" y=\"145.8\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_1\">\n", "      <!-- 20 -->\n", "      <g transform=\"translate(76.597443 160.398438)scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-32\" d=\"M 1228 531 \n", "L 3431 531 \n", "L 3431 0 \n", "L 469 0 \n", "L 469 531 \n", "Q 828 903 1448 1529 \n", "Q 2069 2156 2228 2338 \n", "Q 2531 2678 2651 2914 \n", "Q 2772 3150 2772 3378 \n", "Q 2772 3750 2511 3984 \n", "Q 2250 4219 1831 4219 \n", "Q 1534 4219 1204 4116 \n", "Q 875 4013 500 3803 \n", "L 500 4441 \n", "Q 881 4594 1212 4672 \n", "Q 1544 4750 1819 4750 \n", "Q 2544 4750 2975 4387 \n", "Q 3406 4025 3406 3419 \n", "Q 3406 3131 3298 2873 \n", "Q 3191 2616 2906 2266 \n", "Q 2828 2175 2409 1742 \n", "Q 1991 1309 1228 531 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "        <path id=\"DejaVuSans-30\" d=\"M 2034 4250 \n", "Q 1547 4250 1301 3770 \n", "Q 1056 3291 1056 2328 \n", "Q 1056 1369 1301 889 \n", "Q 1547 409 2034 409 \n", "Q 2525 409 2770 889 \n", "Q 3016 1369 3016 2328 \n", "Q 3016 3291 2770 3770 \n", "Q 2525 4250 2034 4250 \n", "z\n", "M 2034 4750 \n", "Q 2819 4750 3233 4129 \n", "Q 3647 3509 3647 2328 \n", "Q 3647 1150 3233 529 \n", "Q 2819 -91 2034 -91 \n", "Q 1250 -91 836 529 \n", "Q 422 1150 422 2328 \n", "Q 422 3509 836 4129 \n", "Q 1250 4750 2034 4750 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-32\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"63.623047\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_2\">\n", "     <g id=\"line2d_3\">\n", "      <path d=\"M 122.414489 145.8 \n", "L 122.414489 7.2 \n", "\" clip-path=\"url(#p7024c6bc03)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_4\">\n", "      <g>\n", "       <use xlink:href=\"#m71383136c4\" x=\"122.414489\" y=\"145.8\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_2\">\n", "      <!-- 40 -->\n", "      <g transform=\"translate(116.051989 160.398438)scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-34\" d=\"M 2419 4116 \n", "L 825 1625 \n", "L 2419 1625 \n", "L 2419 4116 \n", "z\n", "M 2253 4666 \n", "L 3047 4666 \n", "L 3047 1625 \n", "L 3713 1625 \n", "L 3713 1100 \n", "L 3047 1100 \n", "L 3047 0 \n", "L 2419 0 \n", "L 2419 1100 \n", "L 313 1100 \n", "L 313 1709 \n", "L 2253 4666 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-34\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"63.623047\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_3\">\n", "     <g id=\"line2d_5\">\n", "      <path d=\"M 161.869034 145.8 \n", "L 161.869034 7.2 \n", "\" clip-path=\"url(#p7024c6bc03)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_6\">\n", "      <g>\n", "       <use xlink:href=\"#m71383136c4\" x=\"161.869034\" y=\"145.8\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_3\">\n", "      <!-- 60 -->\n", "      <g transform=\"translate(155.506534 160.398438)scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-36\" d=\"M 2113 2584 \n", "Q 1688 2584 1439 2293 \n", "Q 1191 2003 1191 1497 \n", "Q 1191 994 1439 701 \n", "Q 1688 409 2113 409 \n", "Q 2538 409 2786 701 \n", "Q 3034 994 3034 1497 \n", "Q 3034 2003 2786 2293 \n", "Q 2538 2584 2113 2584 \n", "z\n", "M 3366 4563 \n", "L 3366 3988 \n", "Q 3128 4100 2886 4159 \n", "Q 2644 4219 2406 4219 \n", "Q 1781 4219 1451 3797 \n", "Q 1122 3375 1075 2522 \n", "Q 1259 2794 1537 2939 \n", "Q 1816 3084 2150 3084 \n", "Q 2853 3084 3261 2657 \n", "Q 3669 2231 3669 1497 \n", "Q 3669 778 3244 343 \n", "Q 2819 -91 2113 -91 \n", "Q 1303 -91 875 529 \n", "Q 447 1150 447 2328 \n", "Q 447 3434 972 4092 \n", "Q 1497 4750 2381 4750 \n", "Q 2619 4750 2861 4703 \n", "Q 3103 4656 3366 4563 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-36\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"63.623047\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_4\">\n", "     <g id=\"line2d_7\">\n", "      <path d=\"M 201.32358 145.8 \n", "L 201.32358 7.2 \n", "\" clip-path=\"url(#p7024c6bc03)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_8\">\n", "      <g>\n", "       <use xlink:href=\"#m71383136c4\" x=\"201.32358\" y=\"145.8\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_4\">\n", "      <!-- 80 -->\n", "      <g transform=\"translate(194.96108 160.398438)scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-38\" d=\"M 2034 2216 \n", "Q 1584 2216 1326 1975 \n", "Q 1069 1734 1069 1313 \n", "Q 1069 891 1326 650 \n", "Q 1584 409 2034 409 \n", "Q 2484 409 2743 651 \n", "Q 3003 894 3003 1313 \n", "Q 3003 1734 2745 1975 \n", "Q 2488 2216 2034 2216 \n", "z\n", "M 1403 2484 \n", "Q 997 2584 770 2862 \n", "Q 544 3141 544 3541 \n", "Q 544 4100 942 4425 \n", "Q 1341 4750 2034 4750 \n", "Q 2731 4750 3128 4425 \n", "Q 3525 4100 3525 3541 \n", "Q 3525 3141 3298 2862 \n", "Q 3072 2584 2669 2484 \n", "Q 3125 2378 3379 2068 \n", "Q 3634 1759 3634 1313 \n", "Q 3634 634 3220 271 \n", "Q 2806 -91 2034 -91 \n", "Q 1263 -91 848 271 \n", "Q 434 634 434 1313 \n", "Q 434 1759 690 2068 \n", "Q 947 2378 1403 2484 \n", "z\n", "M 1172 3481 \n", "Q 1172 3119 1398 2916 \n", "Q 1625 2713 2034 2713 \n", "Q 2441 2713 2670 2916 \n", "Q 2900 3119 2900 3481 \n", "Q 2900 3844 2670 4047 \n", "Q 2441 4250 2034 4250 \n", "Q 1625 4250 1398 4047 \n", "Q 1172 3844 1172 3481 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-38\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"63.623047\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_5\">\n", "     <g id=\"line2d_9\">\n", "      <path d=\"M 240.**********.8 \n", "L 240.778125 7.2 \n", "\" clip-path=\"url(#p7024c6bc03)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_10\">\n", "      <g>\n", "       <use xlink:href=\"#m71383136c4\" x=\"240.778125\" y=\"145.8\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_5\">\n", "      <!-- 100 -->\n", "      <g transform=\"translate(231.234375 160.398438)scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-31\" d=\"M 794 531 \n", "L 1825 531 \n", "L 1825 4091 \n", "L 703 3866 \n", "L 703 4441 \n", "L 1819 4666 \n", "L 2450 4666 \n", "L 2450 531 \n", "L 3481 531 \n", "L 3481 0 \n", "L 794 0 \n", "L 794 531 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-31\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"127.246094\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"text_6\">\n", "     <!-- epoch -->\n", "     <g transform=\"translate(127.9 174.076563)scale(0.1 -0.1)\">\n", "      <defs>\n", "       <path id=\"DejaVuSans-65\" d=\"M 3597 1894 \n", "L 3597 1613 \n", "L 953 1613 \n", "Q 991 1019 1311 708 \n", "Q 1631 397 2203 397 \n", "Q 2534 397 2845 478 \n", "Q 3156 559 3463 722 \n", "L 3463 178 \n", "Q 3153 47 2828 -22 \n", "Q 2503 -91 2169 -91 \n", "Q 1331 -91 842 396 \n", "Q 353 884 353 1716 \n", "Q 353 2575 817 3079 \n", "Q 1281 3584 2069 3584 \n", "Q 2775 3584 3186 3129 \n", "Q 3597 2675 3597 1894 \n", "z\n", "M 3022 2063 \n", "Q 3016 2534 2758 2815 \n", "Q 2500 3097 2075 3097 \n", "Q 1594 3097 1305 2825 \n", "Q 1016 2553 972 2059 \n", "L 3022 2063 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-70\" d=\"M 1159 525 \n", "L 1159 -1331 \n", "L 581 -1331 \n", "L 581 3500 \n", "L 1159 3500 \n", "L 1159 2969 \n", "Q 1341 3281 1617 3432 \n", "Q 1894 3584 2278 3584 \n", "Q 2916 3584 3314 3078 \n", "Q 3713 2572 3713 1747 \n", "Q 3713 922 3314 415 \n", "Q 2916 -91 2278 -91 \n", "Q 1894 -91 1617 61 \n", "Q 1341 213 1159 525 \n", "z\n", "M 3116 1747 \n", "Q 3116 2381 2855 2742 \n", "Q 2594 3103 2138 3103 \n", "Q 1681 3103 1420 2742 \n", "Q 1159 2381 1159 1747 \n", "Q 1159 1113 1420 752 \n", "Q 1681 391 2138 391 \n", "Q 2594 391 2855 752 \n", "Q 3116 1113 3116 1747 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-6f\" d=\"M 1959 3097 \n", "Q 1497 3097 1228 2736 \n", "Q 959 2375 959 1747 \n", "Q 959 1119 1226 758 \n", "Q 1494 397 1959 397 \n", "Q 2419 397 2687 759 \n", "Q 2956 1122 2956 1747 \n", "Q 2956 2369 2687 2733 \n", "Q 2419 3097 1959 3097 \n", "z\n", "M 1959 3584 \n", "Q 2709 3584 3137 3096 \n", "Q 3566 2609 3566 1747 \n", "Q 3566 888 3137 398 \n", "Q 2709 -91 1959 -91 \n", "Q 1206 -91 779 398 \n", "Q 353 888 353 1747 \n", "Q 353 2609 779 3096 \n", "Q 1206 3584 1959 3584 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-63\" d=\"M 3122 3366 \n", "L 3122 2828 \n", "Q 2878 2963 2633 3030 \n", "Q 2388 3097 2138 3097 \n", "Q 1578 3097 1268 2742 \n", "Q 959 2388 959 1747 \n", "Q 959 1106 1268 751 \n", "Q 1578 397 2138 397 \n", "Q 2388 397 2633 464 \n", "Q 2878 531 3122 666 \n", "L 3122 134 \n", "Q 2881 22 2623 -34 \n", "Q 2366 -91 2075 -91 \n", "Q 1284 -91 818 406 \n", "Q 353 903 353 1747 \n", "Q 353 2603 823 3093 \n", "Q 1294 3584 2113 3584 \n", "Q 2378 3584 2631 3529 \n", "Q 2884 3475 3122 3366 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-68\" d=\"M 3513 2113 \n", "L 3513 0 \n", "L 2938 0 \n", "L 2938 2094 \n", "Q 2938 2591 2744 2837 \n", "Q 2550 3084 2163 3084 \n", "Q 1697 3084 1428 2787 \n", "Q 1159 2491 1159 1978 \n", "L 1159 0 \n", "L 581 0 \n", "L 581 4863 \n", "L 1159 4863 \n", "L 1159 2956 \n", "Q 1366 3272 1645 3428 \n", "Q 1925 3584 2291 3584 \n", "Q 2894 3584 3203 3211 \n", "Q 3513 2838 3513 2113 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "      </defs>\n", "      <use xlink:href=\"#DejaVuSans-65\"/>\n", "      <use xlink:href=\"#DejaVuSans-70\" x=\"61.523438\"/>\n", "      <use xlink:href=\"#DejaVuSans-6f\" x=\"125\"/>\n", "      <use xlink:href=\"#DejaVuSans-63\" x=\"186.181641\"/>\n", "      <use xlink:href=\"#DejaVuSans-68\" x=\"241.162109\"/>\n", "     </g>\n", "    </g>\n", "   </g>\n", "   <g id=\"matplotlib.axis_2\">\n", "    <g id=\"ytick_1\">\n", "     <g id=\"line2d_11\">\n", "      <path d=\"M 45.478125 65.950418 \n", "L 240.778125 65.950418 \n", "\" clip-path=\"url(#p7024c6bc03)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_12\">\n", "      <defs>\n", "       <path id=\"m65bdfd111f\" d=\"M 0 0 \n", "L -3.5 0 \n", "\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </defs>\n", "      <g>\n", "       <use xlink:href=\"#m65bdfd111f\" x=\"45.478125\" y=\"65.950418\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_7\">\n", "      <!-- $\\mathdefault{10^{0}}$ -->\n", "      <g transform=\"translate(20.878125 69.749637)scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-31\" transform=\"translate(0 0.765625)\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" transform=\"translate(63.623047 0.765625)\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" transform=\"translate(128.203125 39.046875)scale(0.7)\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_2\">\n", "     <g id=\"line2d_13\">\n", "      <defs>\n", "       <path id=\"m85c6a9ba73\" d=\"M 0 0 \n", "L -2 0 \n", "\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </defs>\n", "      <g>\n", "       <use xlink:href=\"#m85c6a9ba73\" x=\"45.478125\" y=\"129.787215\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_3\">\n", "     <g id=\"line2d_14\">\n", "      <g>\n", "       <use xlink:href=\"#m85c6a9ba73\" x=\"45.478125\" y=\"113.704834\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_4\">\n", "     <g id=\"line2d_15\">\n", "      <g>\n", "       <use xlink:href=\"#m85c6a9ba73\" x=\"45.478125\" y=\"102.294203\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_5\">\n", "     <g id=\"line2d_16\">\n", "      <g>\n", "       <use xlink:href=\"#m85c6a9ba73\" x=\"45.478125\" y=\"93.44343\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_6\">\n", "     <g id=\"line2d_17\">\n", "      <g>\n", "       <use xlink:href=\"#m85c6a9ba73\" x=\"45.478125\" y=\"86.211822\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_7\">\n", "     <g id=\"line2d_18\">\n", "      <g>\n", "       <use xlink:href=\"#m85c6a9ba73\" x=\"45.478125\" y=\"80.097584\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_8\">\n", "     <g id=\"line2d_19\">\n", "      <g>\n", "       <use xlink:href=\"#m85c6a9ba73\" x=\"45.478125\" y=\"74.801191\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_9\">\n", "     <g id=\"line2d_20\">\n", "      <g>\n", "       <use xlink:href=\"#m85c6a9ba73\" x=\"45.478125\" y=\"70.129441\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_10\">\n", "     <g id=\"line2d_21\">\n", "      <g>\n", "       <use xlink:href=\"#m85c6a9ba73\" x=\"45.478125\" y=\"38.457406\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_11\">\n", "     <g id=\"line2d_22\">\n", "      <g>\n", "       <use xlink:href=\"#m85c6a9ba73\" x=\"45.478125\" y=\"22.375025\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_12\">\n", "     <g id=\"line2d_23\">\n", "      <g>\n", "       <use xlink:href=\"#m85c6a9ba73\" x=\"45.478125\" y=\"10.964394\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"text_8\">\n", "     <!-- rmse -->\n", "     <g transform=\"translate(14.798437 89.019531)rotate(-90)scale(0.1 -0.1)\">\n", "      <defs>\n", "       <path id=\"DejaVuSans-72\" d=\"M 2631 2963 \n", "Q 2534 3019 2420 3045 \n", "Q 2306 3072 2169 3072 \n", "Q 1681 3072 1420 2755 \n", "Q 1159 2438 1159 1844 \n", "L 1159 0 \n", "L 581 0 \n", "L 581 3500 \n", "L 1159 3500 \n", "L 1159 2956 \n", "Q 1341 3275 1631 3429 \n", "Q 1922 3584 2338 3584 \n", "Q 2397 3584 2469 3576 \n", "Q 2541 3569 2628 3553 \n", "L 2631 2963 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-6d\" d=\"M 3328 2828 \n", "Q 3544 3216 3844 3400 \n", "Q 4144 3584 4550 3584 \n", "Q 5097 3584 5394 3201 \n", "Q 5691 2819 5691 2113 \n", "L 5691 0 \n", "L 5113 0 \n", "L 5113 2094 \n", "Q 5113 2597 4934 2840 \n", "Q 4756 3084 4391 3084 \n", "Q 3944 3084 3684 2787 \n", "Q 3425 2491 3425 1978 \n", "L 3425 0 \n", "L 2847 0 \n", "L 2847 2094 \n", "Q 2847 2600 2669 2842 \n", "Q 2491 3084 2119 3084 \n", "Q 1678 3084 1418 2786 \n", "Q 1159 2488 1159 1978 \n", "L 1159 0 \n", "L 581 0 \n", "L 581 3500 \n", "L 1159 3500 \n", "L 1159 2956 \n", "Q 1356 3278 1631 3431 \n", "Q 1906 3584 2284 3584 \n", "Q 2666 3584 2933 3390 \n", "Q 3200 3197 3328 2828 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-73\" d=\"M 2834 3397 \n", "L 2834 2853 \n", "Q 2591 2978 2328 3040 \n", "Q 2066 3103 1784 3103 \n", "Q 1356 3103 1142 2972 \n", "Q 928 2841 928 2578 \n", "Q 928 2378 1081 2264 \n", "Q 1234 2150 1697 2047 \n", "L 1894 2003 \n", "Q 2506 1872 2764 1633 \n", "Q 3022 1394 3022 966 \n", "Q 3022 478 2636 193 \n", "Q 2250 -91 1575 -91 \n", "Q 1294 -91 989 -36 \n", "Q 684 19 347 128 \n", "L 347 722 \n", "Q 666 556 975 473 \n", "Q 1284 391 1588 391 \n", "Q 1994 391 2212 530 \n", "Q 2431 669 2431 922 \n", "Q 2431 1156 2273 1281 \n", "Q 2116 1406 1581 1522 \n", "L 1381 1569 \n", "Q 847 1681 609 1914 \n", "Q 372 2147 372 2553 \n", "Q 372 3047 722 3315 \n", "Q 1072 3584 1716 3584 \n", "Q 2034 3584 2315 3537 \n", "Q 2597 3491 2834 3397 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "      </defs>\n", "      <use xlink:href=\"#DejaVuSans-72\"/>\n", "      <use xlink:href=\"#DejaVuSans-6d\" x=\"39.363281\"/>\n", "      <use xlink:href=\"#DejaVuSans-73\" x=\"136.775391\"/>\n", "      <use xlink:href=\"#DejaVuSans-65\" x=\"188.875\"/>\n", "     </g>\n", "    </g>\n", "   </g>\n", "   <g id=\"line2d_24\">\n", "    <path d=\"M 45.478125 13.649016 \n", "L 47.450852 21.699416 \n", "L 49.42358 27.258481 \n", "L 51.396307 31.708612 \n", "L 53.369034 35.522914 \n", "L 55.341761 38.888708 \n", "L 57.314489 41.948938 \n", "L 59.287216 44.789564 \n", "L 61.259943 47.449683 \n", "L 63.23267 49.977225 \n", "L 65.205398 52.361621 \n", "L 67.178125 54.681381 \n", "L 69.150852 56.921666 \n", "L 71.12358 59.076232 \n", "L 73.096307 61.17782 \n", "L 75.069034 63.224918 \n", "L 77.041761 65.224161 \n", "L 79.014489 67.186893 \n", "L 80.987216 69.103718 \n", "L 82.959943 70.992577 \n", "L 84.93267 72.86759 \n", "L 86.905398 74.710599 \n", "L 88.878125 76.516442 \n", "L 90.850852 78.308427 \n", "L 92.82358 80.086623 \n", "L 94.796307 81.859298 \n", "L 96.769034 83.587158 \n", "L 98.741761 85.321166 \n", "L 100.714489 87.023332 \n", "L 102.687216 88.732423 \n", "L 104.659943 90.402061 \n", "L 106.63267 92.062329 \n", "L 108.605398 93.706007 \n", "L 110.578125 95.366969 \n", "L 112.550852 96.971117 \n", "L 114.52358 98.568125 \n", "L 116.496307 100.13723 \n", "L 118.469034 101.698715 \n", "L 120.441761 103.244229 \n", "L 122.414489 104.768523 \n", "L 124.387216 106.282887 \n", "L 126.359943 107.740392 \n", "L 128.33267 109.200127 \n", "L 130.305398 110.626295 \n", "L 132.278125 112.003277 \n", "L 134.250852 113.384083 \n", "L 136.22358 114.698604 \n", "L 138.196307 115.993509 \n", "L 140.169034 117.213838 \n", "L 142.141761 118.44662 \n", "L 144.114489 119.629058 \n", "L 146.087216 120.747302 \n", "L 148.059943 121.81385 \n", "L 150.03267 122.886878 \n", "L 152.005398 123.880009 \n", "L 153.978125 124.810284 \n", "L 155.950852 125.700217 \n", "L 157.92358 126.583166 \n", "L 159.896307 127.399853 \n", "L 161.869034 128.128304 \n", "L 163.841761 128.82279 \n", "L 165.814489 129.501447 \n", "L 167.787216 130.15907 \n", "L 169.759943 130.727557 \n", "L 171.73267 131.24679 \n", "L 173.705398 131.72062 \n", "L 175.678125 132.182908 \n", "L 177.650852 132.601365 \n", "L 179.62358 132.990841 \n", "L 181.596307 133.338233 \n", "L 183.569034 133.652899 \n", "L 185.541761 133.954161 \n", "L 187.514489 134.222037 \n", "L 189.487216 134.463492 \n", "L 191.459943 134.673137 \n", "L 193.43267 134.863224 \n", "L 195.405398 135.036576 \n", "L 197.378125 135.193156 \n", "L 199.350852 135.330909 \n", "L 201.32358 135.457491 \n", "L 203.296307 135.573589 \n", "L 205.269034 135.670239 \n", "L 207.241761 135.762489 \n", "L 209.214489 135.837334 \n", "L 211.187216 135.902916 \n", "L 213.159943 135.954754 \n", "L 215.13267 136.003604 \n", "L 217.105398 136.047164 \n", "L 219.078125 136.080533 \n", "L 221.050852 136.103738 \n", "L 223.02358 136.122857 \n", "L 224.996307 136.144022 \n", "L 226.969034 136.168485 \n", "L 228.941761 136.191251 \n", "L 230.914489 136.200668 \n", "L 232.887216 136.193537 \n", "L 234.859943 136.177869 \n", "L 236.83267 136.187456 \n", "L 238.805398 136.194408 \n", "L 240.778125 136.196579 \n", "\" clip-path=\"url(#p7024c6bc03)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_25\">\n", "    <path d=\"M 45.478125 13.5 \n", "L 47.450852 21.513271 \n", "L 49.42358 27.047572 \n", "L 51.396307 31.476431 \n", "L 53.369034 35.272027 \n", "L 55.341761 38.618939 \n", "L 57.314489 41.660518 \n", "L 59.287216 44.487244 \n", "L 61.259943 47.129334 \n", "L 63.23267 49.638061 \n", "L 65.205398 52.007988 \n", "L 67.178125 54.313519 \n", "L 69.150852 56.539332 \n", "L 71.12358 58.678752 \n", "L 73.096307 60.762352 \n", "L 75.069034 62.797237 \n", "L 77.041761 64.78184 \n", "L 79.014489 66.737199 \n", "L 80.987216 68.643315 \n", "L 82.959943 70.521157 \n", "L 84.93267 72.385568 \n", "L 86.905398 74.217023 \n", "L 88.878125 76.018892 \n", "L 90.850852 77.805489 \n", "L 92.82358 79.57333 \n", "L 94.796307 81.343155 \n", "L 96.769034 83.066592 \n", "L 98.741761 84.797216 \n", "L 100.714489 86.500266 \n", "L 102.687216 88.21162 \n", "L 104.659943 89.881983 \n", "L 106.63267 91.544822 \n", "L 108.605398 93.194063 \n", "L 110.578125 94.865134 \n", "L 112.550852 96.482608 \n", "L 114.52358 98.097279 \n", "L 116.496307 99.686213 \n", "L 118.469034 101.269339 \n", "L 120.441761 102.840996 \n", "L 122.414489 104.401074 \n", "L 124.387216 105.953822 \n", "L 126.359943 107.452191 \n", "L 128.33267 108.961421 \n", "L 130.305398 110.442104 \n", "L 132.278125 111.876763 \n", "L 134.250852 113.324085 \n", "L 136.22358 114.707696 \n", "L 138.196307 116.082471 \n", "L 140.169034 117.385806 \n", "L 142.141761 118.711619 \n", "L 144.114489 119.989841 \n", "L 146.087216 121.207611 \n", "L 148.059943 122.381334 \n", "L 150.03267 123.566411 \n", "L 152.005398 124.67521 \n", "L 153.978125 125.728415 \n", "L 155.950852 126.742258 \n", "L 157.92358 127.742548 \n", "L 159.896307 128.683571 \n", "L 161.869034 129.535067 \n", "L 163.841761 130.351614 \n", "L 165.814489 131.155753 \n", "L 167.787216 131.939072 \n", "L 169.759943 132.628397 \n", "L 171.73267 133.261454 \n", "L 173.705398 133.841329 \n", "L 175.678125 134.413768 \n", "L 177.650852 134.933542 \n", "L 179.62358 135.415684 \n", "L 181.596307 135.864236 \n", "L 183.569034 136.25394 \n", "L 185.541761 136.637134 \n", "L 187.514489 136.988077 \n", "L 189.487216 137.298165 \n", "L 191.459943 137.566163 \n", "L 193.43267 137.810195 \n", "L 195.405398 138.039892 \n", "L 197.378125 138.245298 \n", "L 199.350852 138.419823 \n", "L 201.32358 138.589306 \n", "L 203.296307 138.730583 \n", "L 205.269034 138.844183 \n", "L 207.241761 138.971067 \n", "L 209.214489 139.06335 \n", "L 211.187216 139.149022 \n", "L 213.159943 139.214008 \n", "L 215.13267 139.269432 \n", "L 217.105398 139.31677 \n", "L 219.078125 139.364037 \n", "L 221.050852 139.391036 \n", "L 223.02358 139.421788 \n", "L 224.996307 139.439968 \n", "L 226.969034 139.474024 \n", "L 228.941761 139.493677 \n", "L 230.914489 139.5 \n", "L 232.887216 139.494028 \n", "L 234.859943 139.47167 \n", "L 236.83267 139.474111 \n", "L 238.805398 139.483023 \n", "L 240.778125 139.464141 \n", "\" clip-path=\"url(#p7024c6bc03)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #bf00bf; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"patch_3\">\n", "    <path d=\"M 45.**********.8 \n", "L 45.478125 7.2 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_4\">\n", "    <path d=\"M 240.**********.8 \n", "L 240.778125 7.2 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_5\">\n", "    <path d=\"M 45.**********.8 \n", "L 240.**********.8 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_6\">\n", "    <path d=\"M 45.478125 7.2 \n", "L 240.778125 7.2 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"legend_1\">\n", "    <g id=\"patch_7\">\n", "     <path d=\"M 177.826562 44.55625 \n", "L 233.778125 44.55625 \n", "Q 235.778125 44.55625 235.778125 42.55625 \n", "L 235.778125 14.2 \n", "Q 235.778125 12.2 233.778125 12.2 \n", "L 177.826562 12.2 \n", "Q 175.826562 12.2 175.826562 14.2 \n", "L 175.826562 42.55625 \n", "Q 175.826562 44.55625 177.826562 44.55625 \n", "z\n", "\" style=\"fill: #ffffff; opacity: 0.8; stroke: #cccccc; stroke-linejoin: miter\"/>\n", "    </g>\n", "    <g id=\"line2d_26\">\n", "     <path d=\"M 179.826562 20.298438 \n", "L 189.826562 20.298438 \n", "L 199.826562 20.298438 \n", "\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "    </g>\n", "    <g id=\"text_9\">\n", "     <!-- train -->\n", "     <g transform=\"translate(207.826562 23.798438)scale(0.1 -0.1)\">\n", "      <defs>\n", "       <path id=\"DejaVuSans-74\" d=\"M 1172 4494 \n", "L 1172 3500 \n", "L 2356 3500 \n", "L 2356 3053 \n", "L 1172 3053 \n", "L 1172 1153 \n", "Q 1172 725 1289 603 \n", "Q 1406 481 1766 481 \n", "L 2356 481 \n", "L 2356 0 \n", "L 1766 0 \n", "Q 1100 0 847 248 \n", "Q 594 497 594 1153 \n", "L 594 3053 \n", "L 172 3053 \n", "L 172 3500 \n", "L 594 3500 \n", "L 594 4494 \n", "L 1172 4494 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-61\" d=\"M 2194 1759 \n", "Q 1497 1759 1228 1600 \n", "Q 959 1441 959 1056 \n", "Q 959 750 1161 570 \n", "Q 1363 391 1709 391 \n", "Q 2188 391 2477 730 \n", "Q 2766 1069 2766 1631 \n", "L 2766 1759 \n", "L 2194 1759 \n", "z\n", "M 3341 1997 \n", "L 3341 0 \n", "L 2766 0 \n", "L 2766 531 \n", "Q 2569 213 2275 61 \n", "Q 1981 -91 1556 -91 \n", "Q 1019 -91 701 211 \n", "Q 384 513 384 1019 \n", "Q 384 1609 779 1909 \n", "Q 1175 2209 1959 2209 \n", "L 2766 2209 \n", "L 2766 2266 \n", "Q 2766 2663 2505 2880 \n", "Q 2244 3097 1772 3097 \n", "Q 1472 3097 1187 3025 \n", "Q 903 2953 641 2809 \n", "L 641 3341 \n", "Q 956 3463 1253 3523 \n", "Q 1550 3584 1831 3584 \n", "Q 2591 3584 2966 3190 \n", "Q 3341 2797 3341 1997 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-69\" d=\"M 603 3500 \n", "L 1178 3500 \n", "L 1178 0 \n", "L 603 0 \n", "L 603 3500 \n", "z\n", "M 603 4863 \n", "L 1178 4863 \n", "L 1178 4134 \n", "L 603 4134 \n", "L 603 4863 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-6e\" d=\"M 3513 2113 \n", "L 3513 0 \n", "L 2938 0 \n", "L 2938 2094 \n", "Q 2938 2591 2744 2837 \n", "Q 2550 3084 2163 3084 \n", "Q 1697 3084 1428 2787 \n", "Q 1159 2491 1159 1978 \n", "L 1159 0 \n", "L 581 0 \n", "L 581 3500 \n", "L 1159 3500 \n", "L 1159 2956 \n", "Q 1366 3272 1645 3428 \n", "Q 1925 3584 2291 3584 \n", "Q 2894 3584 3203 3211 \n", "Q 3513 2838 3513 2113 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "      </defs>\n", "      <use xlink:href=\"#DejaVuSans-74\"/>\n", "      <use xlink:href=\"#DejaVuSans-72\" x=\"39.208984\"/>\n", "      <use xlink:href=\"#DejaVuSans-61\" x=\"80.322266\"/>\n", "      <use xlink:href=\"#DejaVuSans-69\" x=\"141.601562\"/>\n", "      <use xlink:href=\"#DejaVuSans-6e\" x=\"169.384766\"/>\n", "     </g>\n", "    </g>\n", "    <g id=\"line2d_27\">\n", "     <path d=\"M 179.826562 34.976562 \n", "L 189.826562 34.976562 \n", "L 199.826562 34.976562 \n", "\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #bf00bf; stroke-width: 1.5\"/>\n", "    </g>\n", "    <g id=\"text_10\">\n", "     <!-- valid -->\n", "     <g transform=\"translate(207.826562 38.476562)scale(0.1 -0.1)\">\n", "      <defs>\n", "       <path id=\"DejaVuSans-76\" d=\"M 191 3500 \n", "L 800 3500 \n", "L 1894 563 \n", "L 2988 3500 \n", "L 3597 3500 \n", "L 2284 0 \n", "L 1503 0 \n", "L 191 3500 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-6c\" d=\"M 603 4863 \n", "L 1178 4863 \n", "L 1178 0 \n", "L 603 0 \n", "L 603 4863 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-64\" d=\"M 2906 2969 \n", "L 2906 4863 \n", "L 3481 4863 \n", "L 3481 0 \n", "L 2906 0 \n", "L 2906 525 \n", "Q 2725 213 2448 61 \n", "Q 2172 -91 1784 -91 \n", "Q 1150 -91 751 415 \n", "Q 353 922 353 1747 \n", "Q 353 2572 751 3078 \n", "Q 1150 3584 1784 3584 \n", "Q 2172 3584 2448 3432 \n", "Q 2725 3281 2906 2969 \n", "z\n", "M 947 1747 \n", "Q 947 1113 1208 752 \n", "Q 1469 391 1925 391 \n", "Q 2381 391 2643 752 \n", "Q 2906 1113 2906 1747 \n", "Q 2906 2381 2643 2742 \n", "Q 2381 3103 1925 3103 \n", "Q 1469 3103 1208 2742 \n", "Q 947 2381 947 1747 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "      </defs>\n", "      <use xlink:href=\"#DejaVuSans-76\"/>\n", "      <use xlink:href=\"#DejaVuSans-61\" x=\"59.179688\"/>\n", "      <use xlink:href=\"#DejaVuSans-6c\" x=\"120.458984\"/>\n", "      <use xlink:href=\"#DejaVuSans-69\" x=\"148.242188\"/>\n", "      <use xlink:href=\"#DejaVuSans-64\" x=\"176.025391\"/>\n", "     </g>\n", "    </g>\n", "   </g>\n", "  </g>\n", " </g>\n", " <defs>\n", "  <clipPath id=\"p7024c6bc03\">\n", "   <rect x=\"45.478125\" y=\"7.2\" width=\"195.3\" height=\"138.6\"/>\n", "  </clipPath>\n", " </defs>\n", "</svg>\n"], "text/plain": ["<Figure size 350x250 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}, {"ename": "", "evalue": "", "output_type": "error", "traceback": ["\u001b[1;31m在当前单元格或上一个单元格中执行代码时 Kernel 崩溃。\n", "\u001b[1;31m请查看单元格中的代码，以确定故障的可能原因。\n", "\u001b[1;31m单击<a href='https://aka.ms/vscodeJupyterKernelCrash'>此处</a>了解详细信息。\n", "\u001b[1;31m有关更多详细信息，请查看 Jupyter <a href='command:jupyter.viewOutput'>log</a>。"]}], "source": ["k, num_epochs, lr, weight_decay, batch_size = 5, 100, 5, 1e-5, 64\n", "train_l, valid_l = k_fold(k, train_features, train_labels, num_epochs, lr,\n", "                          weight_decay, batch_size)\n", "print(f'{k}-折验证: 平均训练log rmse: {float(train_l):f}, '\n", "      f'平均验证log rmse: {float(valid_l):f}')"]}, {"cell_type": "markdown", "id": "941f5ad4", "metadata": {"origin_pos": 47}, "source": ["请注意，有时一组超参数的训练误差可能非常低，但$K$折交叉验证的误差要高得多，\n", "这表明模型过拟合了。\n", "在整个训练过程中，我们希望监控训练误差和验证误差这两个数字。\n", "较少的过拟合可能表明现有数据可以支撑一个更强大的模型，\n", "较大的过拟合可能意味着我们可以通过正则化技术来获益。\n", "\n", "##  [**提交Kaggle预测**]\n", "\n", "既然我们知道应该选择什么样的超参数，\n", "我们不妨使用所有数据对其进行训练\n", "（而不是仅使用交叉验证中使用的$1-1/K$的数据）。\n", "然后，我们通过这种方式获得的模型可以应用于测试集。\n", "将预测保存在CSV文件中可以简化将结果上传到Kaggle的过程。\n"]}, {"cell_type": "code", "execution_count": 26, "id": "8775a92c", "metadata": {"execution": {"iopub.execute_input": "2022-12-07T16:58:22.811018Z", "iopub.status.busy": "2022-12-07T16:58:22.810375Z", "iopub.status.idle": "2022-12-07T16:58:22.816674Z", "shell.execute_reply": "2022-12-07T16:58:22.815967Z"}, "origin_pos": 48, "tab": ["pytorch"]}, "outputs": [], "source": ["def train_and_pred(train_features, test_features, train_labels, test_data,\n", "                   num_epochs, lr, weight_decay, batch_size):\n", "    net = get_net()\n", "    train_ls, _ = train(net, train_features, train_labels, None, None,\n", "                        num_epochs, lr, weight_decay, batch_size)\n", "    d2l.plot(np.arange(1, num_epochs + 1), [train_ls], xlabel='epoch',\n", "             ylabel='log rmse', xlim=[1, num_epochs], yscale='log')\n", "    print(f'训练log rmse：{float(train_ls[-1]):f}')\n", "    # 将网络应用于测试集。\n", "    preds = net(test_features).detach().numpy()\n", "    # 将其重新格式化以导出到Kaggle\n", "    test_data['SalePrice'] = pd.Series(preds.reshape(1, -1)[0])\n", "    submission = pd.concat([test_data['Id'], test_data['SalePrice']], axis=1)\n", "    submission.to_csv('submission.csv', index=False)"]}, {"cell_type": "markdown", "id": "df93ada1", "metadata": {"origin_pos": 49}, "source": ["如果测试集上的预测与$K$倍交叉验证过程中的预测相似，\n", "那就是时候把它们上传到Kaggle了。\n", "下面的代码将生成一个名为`submission.csv`的文件。\n"]}, {"cell_type": "code", "execution_count": 27, "id": "704f694c", "metadata": {"execution": {"iopub.execute_input": "2022-12-07T16:58:22.820338Z", "iopub.status.busy": "2022-12-07T16:58:22.819572Z", "iopub.status.idle": "2022-12-07T16:58:25.874145Z", "shell.execute_reply": "2022-12-07T16:58:25.873334Z"}, "origin_pos": 50, "tab": ["pytorch"]}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["训练log rmse：0.162726\n"]}, {"data": {"image/svg+xml": ["<?xml version=\"1.0\" encoding=\"utf-8\" standalone=\"no\"?>\n", "<!DOCTYPE svg PUBLIC \"-//W3C//DTD SVG 1.1//EN\"\n", "  \"http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd\">\n", "<svg xmlns:xlink=\"http://www.w3.org/1999/xlink\" width=\"257.521875pt\" height=\"183.35625pt\" viewBox=\"0 0 257.**********.35625\" xmlns=\"http://www.w3.org/2000/svg\" version=\"1.1\">\n", " <metadata>\n", "  <rdf:RDF xmlns:dc=\"http://purl.org/dc/elements/1.1/\" xmlns:cc=\"http://creativecommons.org/ns#\" xmlns:rdf=\"http://www.w3.org/1999/02/22-rdf-syntax-ns#\">\n", "   <cc:Work>\n", "    <dc:type rdf:resource=\"http://purl.org/dc/dcmitype/StillImage\"/>\n", "    <dc:date>2025-07-21T23:00:56.281023</dc:date>\n", "    <dc:format>image/svg+xml</dc:format>\n", "    <dc:creator>\n", "     <cc:Agent>\n", "      <dc:title>Matplotlib v3.5.1, https://matplotlib.org/</dc:title>\n", "     </cc:Agent>\n", "    </dc:creator>\n", "   </cc:Work>\n", "  </rdf:RDF>\n", " </metadata>\n", " <defs>\n", "  <style type=\"text/css\">*{stroke-linejoin: round; stroke-linecap: butt}</style>\n", " </defs>\n", " <g id=\"figure_1\">\n", "  <g id=\"patch_1\">\n", "   <path d=\"M 0 183.35625 \n", "L 257.**********.35625 \n", "L 257.521875 0 \n", "L 0 0 \n", "z\n", "\" style=\"fill: #ffffff\"/>\n", "  </g>\n", "  <g id=\"axes_1\">\n", "   <g id=\"patch_2\">\n", "    <path d=\"M 45.**********.8 \n", "L 240.**********.8 \n", "L 240.778125 7.2 \n", "L 45.478125 7.2 \n", "z\n", "\" style=\"fill: #ffffff\"/>\n", "   </g>\n", "   <g id=\"matplotlib.axis_1\">\n", "    <g id=\"xtick_1\">\n", "     <g id=\"line2d_1\">\n", "      <path d=\"M 82.959943 145.8 \n", "L 82.959943 7.2 \n", "\" clip-path=\"url(#pb1711fb4a3)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_2\">\n", "      <defs>\n", "       <path id=\"mffe2e7c8ae\" d=\"M 0 0 \n", "L 0 3.5 \n", "\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </defs>\n", "      <g>\n", "       <use xlink:href=\"#mffe2e7c8ae\" x=\"82.959943\" y=\"145.8\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_1\">\n", "      <!-- 20 -->\n", "      <g transform=\"translate(76.597443 160.398438)scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-32\" d=\"M 1228 531 \n", "L 3431 531 \n", "L 3431 0 \n", "L 469 0 \n", "L 469 531 \n", "Q 828 903 1448 1529 \n", "Q 2069 2156 2228 2338 \n", "Q 2531 2678 2651 2914 \n", "Q 2772 3150 2772 3378 \n", "Q 2772 3750 2511 3984 \n", "Q 2250 4219 1831 4219 \n", "Q 1534 4219 1204 4116 \n", "Q 875 4013 500 3803 \n", "L 500 4441 \n", "Q 881 4594 1212 4672 \n", "Q 1544 4750 1819 4750 \n", "Q 2544 4750 2975 4387 \n", "Q 3406 4025 3406 3419 \n", "Q 3406 3131 3298 2873 \n", "Q 3191 2616 2906 2266 \n", "Q 2828 2175 2409 1742 \n", "Q 1991 1309 1228 531 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "        <path id=\"DejaVuSans-30\" d=\"M 2034 4250 \n", "Q 1547 4250 1301 3770 \n", "Q 1056 3291 1056 2328 \n", "Q 1056 1369 1301 889 \n", "Q 1547 409 2034 409 \n", "Q 2525 409 2770 889 \n", "Q 3016 1369 3016 2328 \n", "Q 3016 3291 2770 3770 \n", "Q 2525 4250 2034 4250 \n", "z\n", "M 2034 4750 \n", "Q 2819 4750 3233 4129 \n", "Q 3647 3509 3647 2328 \n", "Q 3647 1150 3233 529 \n", "Q 2819 -91 2034 -91 \n", "Q 1250 -91 836 529 \n", "Q 422 1150 422 2328 \n", "Q 422 3509 836 4129 \n", "Q 1250 4750 2034 4750 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-32\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"63.623047\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_2\">\n", "     <g id=\"line2d_3\">\n", "      <path d=\"M 122.414489 145.8 \n", "L 122.414489 7.2 \n", "\" clip-path=\"url(#pb1711fb4a3)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_4\">\n", "      <g>\n", "       <use xlink:href=\"#mffe2e7c8ae\" x=\"122.414489\" y=\"145.8\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_2\">\n", "      <!-- 40 -->\n", "      <g transform=\"translate(116.051989 160.398438)scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-34\" d=\"M 2419 4116 \n", "L 825 1625 \n", "L 2419 1625 \n", "L 2419 4116 \n", "z\n", "M 2253 4666 \n", "L 3047 4666 \n", "L 3047 1625 \n", "L 3713 1625 \n", "L 3713 1100 \n", "L 3047 1100 \n", "L 3047 0 \n", "L 2419 0 \n", "L 2419 1100 \n", "L 313 1100 \n", "L 313 1709 \n", "L 2253 4666 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-34\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"63.623047\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_3\">\n", "     <g id=\"line2d_5\">\n", "      <path d=\"M 161.869034 145.8 \n", "L 161.869034 7.2 \n", "\" clip-path=\"url(#pb1711fb4a3)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_6\">\n", "      <g>\n", "       <use xlink:href=\"#mffe2e7c8ae\" x=\"161.869034\" y=\"145.8\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_3\">\n", "      <!-- 60 -->\n", "      <g transform=\"translate(155.506534 160.398438)scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-36\" d=\"M 2113 2584 \n", "Q 1688 2584 1439 2293 \n", "Q 1191 2003 1191 1497 \n", "Q 1191 994 1439 701 \n", "Q 1688 409 2113 409 \n", "Q 2538 409 2786 701 \n", "Q 3034 994 3034 1497 \n", "Q 3034 2003 2786 2293 \n", "Q 2538 2584 2113 2584 \n", "z\n", "M 3366 4563 \n", "L 3366 3988 \n", "Q 3128 4100 2886 4159 \n", "Q 2644 4219 2406 4219 \n", "Q 1781 4219 1451 3797 \n", "Q 1122 3375 1075 2522 \n", "Q 1259 2794 1537 2939 \n", "Q 1816 3084 2150 3084 \n", "Q 2853 3084 3261 2657 \n", "Q 3669 2231 3669 1497 \n", "Q 3669 778 3244 343 \n", "Q 2819 -91 2113 -91 \n", "Q 1303 -91 875 529 \n", "Q 447 1150 447 2328 \n", "Q 447 3434 972 4092 \n", "Q 1497 4750 2381 4750 \n", "Q 2619 4750 2861 4703 \n", "Q 3103 4656 3366 4563 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-36\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"63.623047\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_4\">\n", "     <g id=\"line2d_7\">\n", "      <path d=\"M 201.32358 145.8 \n", "L 201.32358 7.2 \n", "\" clip-path=\"url(#pb1711fb4a3)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_8\">\n", "      <g>\n", "       <use xlink:href=\"#mffe2e7c8ae\" x=\"201.32358\" y=\"145.8\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_4\">\n", "      <!-- 80 -->\n", "      <g transform=\"translate(194.96108 160.398438)scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-38\" d=\"M 2034 2216 \n", "Q 1584 2216 1326 1975 \n", "Q 1069 1734 1069 1313 \n", "Q 1069 891 1326 650 \n", "Q 1584 409 2034 409 \n", "Q 2484 409 2743 651 \n", "Q 3003 894 3003 1313 \n", "Q 3003 1734 2745 1975 \n", "Q 2488 2216 2034 2216 \n", "z\n", "M 1403 2484 \n", "Q 997 2584 770 2862 \n", "Q 544 3141 544 3541 \n", "Q 544 4100 942 4425 \n", "Q 1341 4750 2034 4750 \n", "Q 2731 4750 3128 4425 \n", "Q 3525 4100 3525 3541 \n", "Q 3525 3141 3298 2862 \n", "Q 3072 2584 2669 2484 \n", "Q 3125 2378 3379 2068 \n", "Q 3634 1759 3634 1313 \n", "Q 3634 634 3220 271 \n", "Q 2806 -91 2034 -91 \n", "Q 1263 -91 848 271 \n", "Q 434 634 434 1313 \n", "Q 434 1759 690 2068 \n", "Q 947 2378 1403 2484 \n", "z\n", "M 1172 3481 \n", "Q 1172 3119 1398 2916 \n", "Q 1625 2713 2034 2713 \n", "Q 2441 2713 2670 2916 \n", "Q 2900 3119 2900 3481 \n", "Q 2900 3844 2670 4047 \n", "Q 2441 4250 2034 4250 \n", "Q 1625 4250 1398 4047 \n", "Q 1172 3844 1172 3481 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-38\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"63.623047\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_5\">\n", "     <g id=\"line2d_9\">\n", "      <path d=\"M 240.**********.8 \n", "L 240.778125 7.2 \n", "\" clip-path=\"url(#pb1711fb4a3)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_10\">\n", "      <g>\n", "       <use xlink:href=\"#mffe2e7c8ae\" x=\"240.778125\" y=\"145.8\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_5\">\n", "      <!-- 100 -->\n", "      <g transform=\"translate(231.234375 160.398438)scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-31\" d=\"M 794 531 \n", "L 1825 531 \n", "L 1825 4091 \n", "L 703 3866 \n", "L 703 4441 \n", "L 1819 4666 \n", "L 2450 4666 \n", "L 2450 531 \n", "L 3481 531 \n", "L 3481 0 \n", "L 794 0 \n", "L 794 531 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-31\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"127.246094\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"text_6\">\n", "     <!-- epoch -->\n", "     <g transform=\"translate(127.9 174.076563)scale(0.1 -0.1)\">\n", "      <defs>\n", "       <path id=\"DejaVuSans-65\" d=\"M 3597 1894 \n", "L 3597 1613 \n", "L 953 1613 \n", "Q 991 1019 1311 708 \n", "Q 1631 397 2203 397 \n", "Q 2534 397 2845 478 \n", "Q 3156 559 3463 722 \n", "L 3463 178 \n", "Q 3153 47 2828 -22 \n", "Q 2503 -91 2169 -91 \n", "Q 1331 -91 842 396 \n", "Q 353 884 353 1716 \n", "Q 353 2575 817 3079 \n", "Q 1281 3584 2069 3584 \n", "Q 2775 3584 3186 3129 \n", "Q 3597 2675 3597 1894 \n", "z\n", "M 3022 2063 \n", "Q 3016 2534 2758 2815 \n", "Q 2500 3097 2075 3097 \n", "Q 1594 3097 1305 2825 \n", "Q 1016 2553 972 2059 \n", "L 3022 2063 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-70\" d=\"M 1159 525 \n", "L 1159 -1331 \n", "L 581 -1331 \n", "L 581 3500 \n", "L 1159 3500 \n", "L 1159 2969 \n", "Q 1341 3281 1617 3432 \n", "Q 1894 3584 2278 3584 \n", "Q 2916 3584 3314 3078 \n", "Q 3713 2572 3713 1747 \n", "Q 3713 922 3314 415 \n", "Q 2916 -91 2278 -91 \n", "Q 1894 -91 1617 61 \n", "Q 1341 213 1159 525 \n", "z\n", "M 3116 1747 \n", "Q 3116 2381 2855 2742 \n", "Q 2594 3103 2138 3103 \n", "Q 1681 3103 1420 2742 \n", "Q 1159 2381 1159 1747 \n", "Q 1159 1113 1420 752 \n", "Q 1681 391 2138 391 \n", "Q 2594 391 2855 752 \n", "Q 3116 1113 3116 1747 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-6f\" d=\"M 1959 3097 \n", "Q 1497 3097 1228 2736 \n", "Q 959 2375 959 1747 \n", "Q 959 1119 1226 758 \n", "Q 1494 397 1959 397 \n", "Q 2419 397 2687 759 \n", "Q 2956 1122 2956 1747 \n", "Q 2956 2369 2687 2733 \n", "Q 2419 3097 1959 3097 \n", "z\n", "M 1959 3584 \n", "Q 2709 3584 3137 3096 \n", "Q 3566 2609 3566 1747 \n", "Q 3566 888 3137 398 \n", "Q 2709 -91 1959 -91 \n", "Q 1206 -91 779 398 \n", "Q 353 888 353 1747 \n", "Q 353 2609 779 3096 \n", "Q 1206 3584 1959 3584 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-63\" d=\"M 3122 3366 \n", "L 3122 2828 \n", "Q 2878 2963 2633 3030 \n", "Q 2388 3097 2138 3097 \n", "Q 1578 3097 1268 2742 \n", "Q 959 2388 959 1747 \n", "Q 959 1106 1268 751 \n", "Q 1578 397 2138 397 \n", "Q 2388 397 2633 464 \n", "Q 2878 531 3122 666 \n", "L 3122 134 \n", "Q 2881 22 2623 -34 \n", "Q 2366 -91 2075 -91 \n", "Q 1284 -91 818 406 \n", "Q 353 903 353 1747 \n", "Q 353 2603 823 3093 \n", "Q 1294 3584 2113 3584 \n", "Q 2378 3584 2631 3529 \n", "Q 2884 3475 3122 3366 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-68\" d=\"M 3513 2113 \n", "L 3513 0 \n", "L 2938 0 \n", "L 2938 2094 \n", "Q 2938 2591 2744 2837 \n", "Q 2550 3084 2163 3084 \n", "Q 1697 3084 1428 2787 \n", "Q 1159 2491 1159 1978 \n", "L 1159 0 \n", "L 581 0 \n", "L 581 4863 \n", "L 1159 4863 \n", "L 1159 2956 \n", "Q 1366 3272 1645 3428 \n", "Q 1925 3584 2291 3584 \n", "Q 2894 3584 3203 3211 \n", "Q 3513 2838 3513 2113 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "      </defs>\n", "      <use xlink:href=\"#DejaVuSans-65\"/>\n", "      <use xlink:href=\"#DejaVuSans-70\" x=\"61.523438\"/>\n", "      <use xlink:href=\"#DejaVuSans-6f\" x=\"125\"/>\n", "      <use xlink:href=\"#DejaVuSans-63\" x=\"186.181641\"/>\n", "      <use xlink:href=\"#DejaVuSans-68\" x=\"241.162109\"/>\n", "     </g>\n", "    </g>\n", "   </g>\n", "   <g id=\"matplotlib.axis_2\">\n", "    <g id=\"ytick_1\">\n", "     <g id=\"line2d_11\">\n", "      <path d=\"M 45.478125 65.269453 \n", "L 240.778125 65.269453 \n", "\" clip-path=\"url(#pb1711fb4a3)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_12\">\n", "      <defs>\n", "       <path id=\"m4d761a2c51\" d=\"M 0 0 \n", "L -3.5 0 \n", "\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </defs>\n", "      <g>\n", "       <use xlink:href=\"#m4d761a2c51\" x=\"45.478125\" y=\"65.269453\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_7\">\n", "      <!-- $\\mathdefault{10^{0}}$ -->\n", "      <g transform=\"translate(20.878125 69.068671)scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-31\" transform=\"translate(0 0.765625)\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" transform=\"translate(63.623047 0.765625)\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" transform=\"translate(128.203125 39.046875)scale(0.7)\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_2\">\n", "     <g id=\"line2d_13\">\n", "      <defs>\n", "       <path id=\"m3f8c5bfd15\" d=\"M 0 0 \n", "L -2 0 \n", "\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </defs>\n", "      <g>\n", "       <use xlink:href=\"#m3f8c5bfd15\" x=\"45.478125\" y=\"131.067912\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_3\">\n", "     <g id=\"line2d_14\">\n", "      <g>\n", "       <use xlink:href=\"#m3f8c5bfd15\" x=\"45.478125\" y=\"114.49133\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_4\">\n", "     <g id=\"line2d_15\">\n", "      <g>\n", "       <use xlink:href=\"#m3f8c5bfd15\" x=\"45.478125\" y=\"102.730058\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_5\">\n", "     <g id=\"line2d_16\">\n", "      <g>\n", "       <use xlink:href=\"#m3f8c5bfd15\" x=\"45.478125\" y=\"93.607307\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_6\">\n", "     <g id=\"line2d_17\">\n", "      <g>\n", "       <use xlink:href=\"#m3f8c5bfd15\" x=\"45.478125\" y=\"86.153476\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_7\">\n", "     <g id=\"line2d_18\">\n", "      <g>\n", "       <use xlink:href=\"#m3f8c5bfd15\" x=\"45.478125\" y=\"79.851352\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_8\">\n", "     <g id=\"line2d_19\">\n", "      <g>\n", "       <use xlink:href=\"#m3f8c5bfd15\" x=\"45.478125\" y=\"74.392204\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_9\">\n", "     <g id=\"line2d_20\">\n", "      <g>\n", "       <use xlink:href=\"#m3f8c5bfd15\" x=\"45.478125\" y=\"69.576894\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_10\">\n", "     <g id=\"line2d_21\">\n", "      <g>\n", "       <use xlink:href=\"#m3f8c5bfd15\" x=\"45.478125\" y=\"36.931599\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_11\">\n", "     <g id=\"line2d_22\">\n", "      <g>\n", "       <use xlink:href=\"#m3f8c5bfd15\" x=\"45.478125\" y=\"20.355017\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_12\">\n", "     <g id=\"line2d_23\">\n", "      <g>\n", "       <use xlink:href=\"#m3f8c5bfd15\" x=\"45.478125\" y=\"8.593744\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"text_8\">\n", "     <!-- log rmse -->\n", "     <g transform=\"translate(14.798438 98.23125)rotate(-90)scale(0.1 -0.1)\">\n", "      <defs>\n", "       <path id=\"DejaVuSans-6c\" d=\"M 603 4863 \n", "L 1178 4863 \n", "L 1178 0 \n", "L 603 0 \n", "L 603 4863 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-67\" d=\"M 2906 1791 \n", "Q 2906 2416 2648 2759 \n", "Q 2391 3103 1925 3103 \n", "Q 1463 3103 1205 2759 \n", "Q 947 2416 947 1791 \n", "Q 947 1169 1205 825 \n", "Q 1463 481 1925 481 \n", "Q 2391 481 2648 825 \n", "Q 2906 1169 2906 1791 \n", "z\n", "M 3481 434 \n", "Q 3481 -459 3084 -895 \n", "Q 2688 -1331 1869 -1331 \n", "Q 1566 -1331 1297 -1286 \n", "Q 1028 -1241 775 -1147 \n", "L 775 -588 \n", "Q 1028 -725 1275 -790 \n", "Q 1522 -856 1778 -856 \n", "Q 2344 -856 2625 -561 \n", "Q 2906 -266 2906 331 \n", "L 2906 616 \n", "Q 2728 306 2450 153 \n", "Q 2172 0 1784 0 \n", "Q 1141 0 747 490 \n", "Q 353 981 353 1791 \n", "Q 353 2603 747 3093 \n", "Q 1141 3584 1784 3584 \n", "Q 2172 3584 2450 3431 \n", "Q 2728 3278 2906 2969 \n", "L 2906 3500 \n", "L 3481 3500 \n", "L 3481 434 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-20\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-72\" d=\"M 2631 2963 \n", "Q 2534 3019 2420 3045 \n", "Q 2306 3072 2169 3072 \n", "Q 1681 3072 1420 2755 \n", "Q 1159 2438 1159 1844 \n", "L 1159 0 \n", "L 581 0 \n", "L 581 3500 \n", "L 1159 3500 \n", "L 1159 2956 \n", "Q 1341 3275 1631 3429 \n", "Q 1922 3584 2338 3584 \n", "Q 2397 3584 2469 3576 \n", "Q 2541 3569 2628 3553 \n", "L 2631 2963 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-6d\" d=\"M 3328 2828 \n", "Q 3544 3216 3844 3400 \n", "Q 4144 3584 4550 3584 \n", "Q 5097 3584 5394 3201 \n", "Q 5691 2819 5691 2113 \n", "L 5691 0 \n", "L 5113 0 \n", "L 5113 2094 \n", "Q 5113 2597 4934 2840 \n", "Q 4756 3084 4391 3084 \n", "Q 3944 3084 3684 2787 \n", "Q 3425 2491 3425 1978 \n", "L 3425 0 \n", "L 2847 0 \n", "L 2847 2094 \n", "Q 2847 2600 2669 2842 \n", "Q 2491 3084 2119 3084 \n", "Q 1678 3084 1418 2786 \n", "Q 1159 2488 1159 1978 \n", "L 1159 0 \n", "L 581 0 \n", "L 581 3500 \n", "L 1159 3500 \n", "L 1159 2956 \n", "Q 1356 3278 1631 3431 \n", "Q 1906 3584 2284 3584 \n", "Q 2666 3584 2933 3390 \n", "Q 3200 3197 3328 2828 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-73\" d=\"M 2834 3397 \n", "L 2834 2853 \n", "Q 2591 2978 2328 3040 \n", "Q 2066 3103 1784 3103 \n", "Q 1356 3103 1142 2972 \n", "Q 928 2841 928 2578 \n", "Q 928 2378 1081 2264 \n", "Q 1234 2150 1697 2047 \n", "L 1894 2003 \n", "Q 2506 1872 2764 1633 \n", "Q 3022 1394 3022 966 \n", "Q 3022 478 2636 193 \n", "Q 2250 -91 1575 -91 \n", "Q 1294 -91 989 -36 \n", "Q 684 19 347 128 \n", "L 347 722 \n", "Q 666 556 975 473 \n", "Q 1284 391 1588 391 \n", "Q 1994 391 2212 530 \n", "Q 2431 669 2431 922 \n", "Q 2431 1156 2273 1281 \n", "Q 2116 1406 1581 1522 \n", "L 1381 1569 \n", "Q 847 1681 609 1914 \n", "Q 372 2147 372 2553 \n", "Q 372 3047 722 3315 \n", "Q 1072 3584 1716 3584 \n", "Q 2034 3584 2315 3537 \n", "Q 2597 3491 2834 3397 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "      </defs>\n", "      <use xlink:href=\"#DejaVuSans-6c\"/>\n", "      <use xlink:href=\"#DejaVuSans-6f\" x=\"27.783203\"/>\n", "      <use xlink:href=\"#DejaVuSans-67\" x=\"88.964844\"/>\n", "      <use xlink:href=\"#DejaVuSans-20\" x=\"152.441406\"/>\n", "      <use xlink:href=\"#DejaVuSans-72\" x=\"184.228516\"/>\n", "      <use xlink:href=\"#DejaVuSans-6d\" x=\"223.591797\"/>\n", "      <use xlink:href=\"#DejaVuSans-73\" x=\"321.003906\"/>\n", "      <use xlink:href=\"#DejaVuSans-65\" x=\"373.103516\"/>\n", "     </g>\n", "    </g>\n", "   </g>\n", "   <g id=\"line2d_24\">\n", "    <path d=\"M 45.478125 13.5 \n", "L 47.450852 22.282648 \n", "L 49.42358 28.385309 \n", "L 51.396307 33.301727 \n", "L 53.369034 37.534856 \n", "L 55.341761 41.314771 \n", "L 57.314489 44.762823 \n", "L 59.287216 47.990391 \n", "L 61.259943 51.019443 \n", "L 63.23267 53.907758 \n", "L 65.205398 56.653524 \n", "L 67.178125 59.313396 \n", "L 69.150852 61.900704 \n", "L 71.12358 64.405532 \n", "L 73.096307 66.838934 \n", "L 75.069034 69.237899 \n", "L 77.041761 71.588851 \n", "L 79.014489 73.89474 \n", "L 80.987216 76.183799 \n", "L 82.959943 78.424334 \n", "L 84.93267 80.636819 \n", "L 86.905398 82.818003 \n", "L 88.878125 84.993017 \n", "L 90.850852 87.13548 \n", "L 92.82358 89.248629 \n", "L 94.796307 91.336535 \n", "L 96.769034 93.400298 \n", "L 98.741761 95.47972 \n", "L 100.714489 97.522226 \n", "L 102.687216 99.530119 \n", "L 104.659943 101.484157 \n", "L 106.63267 103.449464 \n", "L 108.605398 105.345712 \n", "L 110.578125 107.219135 \n", "L 112.550852 109.083955 \n", "L 114.52358 110.896301 \n", "L 116.496307 112.662925 \n", "L 118.469034 114.385623 \n", "L 120.441761 116.051397 \n", "L 122.414489 117.69206 \n", "L 124.387216 119.241403 \n", "L 126.359943 120.754172 \n", "L 128.33267 122.202269 \n", "L 130.305398 123.577476 \n", "L 132.278125 124.868488 \n", "L 134.250852 126.102393 \n", "L 136.22358 127.280279 \n", "L 138.196307 128.376635 \n", "L 140.169034 129.389613 \n", "L 142.141761 130.359473 \n", "L 144.114489 131.214425 \n", "L 146.087216 132.035685 \n", "L 148.059943 132.760694 \n", "L 150.03267 133.452195 \n", "L 152.005398 134.06067 \n", "L 153.978125 134.618353 \n", "L 155.950852 135.120607 \n", "L 157.92358 135.547071 \n", "L 159.896307 135.957475 \n", "L 161.869034 136.324224 \n", "L 163.841761 136.643072 \n", "L 165.814489 136.917289 \n", "L 167.787216 137.179144 \n", "L 169.759943 137.396192 \n", "L 171.73267 137.578645 \n", "L 173.705398 137.751667 \n", "L 175.678125 137.895515 \n", "L 177.650852 138.019336 \n", "L 179.62358 138.125383 \n", "L 181.596307 138.219846 \n", "L 183.569034 138.307825 \n", "L 185.541761 138.370652 \n", "L 187.514489 138.435555 \n", "L 189.487216 138.483292 \n", "L 191.459943 138.5354 \n", "L 193.43267 138.572262 \n", "L 195.405398 138.598449 \n", "L 197.378125 138.616917 \n", "L 199.350852 138.657411 \n", "L 201.32358 138.653208 \n", "L 203.296307 138.702592 \n", "L 205.269034 138.715892 \n", "L 207.241761 138.724141 \n", "L 209.214489 138.762388 \n", "L 211.187216 138.786894 \n", "L 213.159943 138.817393 \n", "L 215.13267 138.811024 \n", "L 217.105398 138.852348 \n", "L 219.078125 138.888099 \n", "L 221.050852 138.94279 \n", "L 223.02358 138.994977 \n", "L 224.996307 139.055328 \n", "L 226.969034 139.091291 \n", "L 228.941761 139.12453 \n", "L 230.914489 139.167857 \n", "L 232.887216 139.261744 \n", "L 234.859943 139.338616 \n", "L 236.83267 139.421012 \n", "L 238.805398 139.451398 \n", "L 240.778125 139.5 \n", "\" clip-path=\"url(#pb1711fb4a3)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_3\">\n", "    <path d=\"M 45.**********.8 \n", "L 45.478125 7.2 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_4\">\n", "    <path d=\"M 240.**********.8 \n", "L 240.778125 7.2 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_5\">\n", "    <path d=\"M 45.**********.8 \n", "L 240.**********.8 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_6\">\n", "    <path d=\"M 45.478125 7.2 \n", "L 240.778125 7.2 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "  </g>\n", " </g>\n", " <defs>\n", "  <clipPath id=\"pb1711fb4a3\">\n", "   <rect x=\"45.478125\" y=\"7.2\" width=\"195.3\" height=\"138.6\"/>\n", "  </clipPath>\n", " </defs>\n", "</svg>\n"], "text/plain": ["<Figure size 350x250 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["train_and_pred(train_features, test_features, train_labels, test_data,\n", "               num_epochs, lr, weight_decay, batch_size)"]}, {"cell_type": "markdown", "id": "3b363042", "metadata": {"origin_pos": 51}, "source": ["接下来，如 :numref:`fig_kaggle_submit2`中所示，\n", "我们可以提交预测到Kaggle上，并查看在测试集上的预测与实际房价（标签）的比较情况。\n", "步骤非常简单。\n", "\n", "* 登录Kaggle网站，访问房价预测竞赛页面。\n", "* 点击“Submit Predictions”或“Late Submission”按钮（在撰写本文时，该按钮位于右侧）。\n", "* 点击页面底部虚线框中的“Upload Submission File”按钮，选择要上传的预测文件。\n", "* 点击页面底部的“Make Submission”按钮，即可查看结果。\n", "\n", "![向Kaggle提交数据](../img/kaggle-submit2.png)\n", ":width:`400px`\n", ":label:`fig_kaggle_submit2`\n", "\n", "## 小结\n", "\n", "* 真实数据通常混合了不同的数据类型，需要进行预处理。\n", "* 常用的预处理方法：将实值数据重新缩放为零均值和单位方法；用均值替换缺失值。\n", "* 将类别特征转化为指标特征，可以使我们把这个特征当作一个独热向量来对待。\n", "* 我们可以使用$K$折交叉验证来选择模型并调整超参数。\n", "* 对数对于相对误差很有用。\n", "\n", "## 练习\n", "\n", "1. 把预测提交给Kaggle，它有多好？\n", "1. 能通过直接最小化价格的对数来改进模型吗？如果试图预测价格的对数而不是价格，会发生什么？\n", "1. 用平均值替换缺失值总是好主意吗？提示：能构造一个不随机丢失值的情况吗？\n", "1. 通过$K$折交叉验证调整超参数，从而提高Kaggle的得分。\n", "1. 通过改进模型（例如，层、权重衰减和dropout）来提高分数。\n", "1. 如果我们没有像本节所做的那样标准化连续的数值特征，会发生什么？\n"]}, {"cell_type": "markdown", "id": "8351e40a", "metadata": {"origin_pos": 53, "tab": ["pytorch"]}, "source": ["[Discussions](https://discuss.d2l.ai/t/1824)\n"]}], "metadata": {"kernelspec": {"display_name": "d2l", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.23"}}, "nbformat": 4, "nbformat_minor": 5}