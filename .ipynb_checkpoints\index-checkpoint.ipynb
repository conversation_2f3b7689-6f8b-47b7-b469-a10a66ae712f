{"cells": [{"cell_type": "markdown", "id": "8e709cee", "metadata": {"origin_pos": 0}, "source": ["《动手学深度学习》\n", "========================\n", "\n", "```eval_rst\n", ".. raw:: html\n", "   :file: frontpage.html\n", "```\n", "\n", ":begin_tab:toc\n", " - [chapter_preface/index](chapter_preface/index.ipynb)\n", " - [chapter_installation/index](chapter_installation/index.ipynb)\n", " - [chapter_notation/index](chapter_notation/index.ipynb)\n", ":end_tab:\n", "\n", ":begin_tab:toc\n", " - [chapter_introduction/index](chapter_introduction/index.ipynb)\n", " - [chapter_preliminaries/index](chapter_preliminaries/index.ipynb)\n", " - [chapter_linear-networks/index](chapter_linear-networks/index.ipynb)\n", " - [chapter_multilayer-perceptrons/index](chapter_multilayer-perceptrons/index.ipynb)\n", " - [chapter_deep-learning-computation/index](chapter_deep-learning-computation/index.ipynb)\n", " - [chapter_convolutional-neural-networks/index](chapter_convolutional-neural-networks/index.ipynb)\n", " - [chapter_convolutional-modern/index](chapter_convolutional-modern/index.ipynb)\n", " - [chapter_recurrent-neural-networks/index](chapter_recurrent-neural-networks/index.ipynb)\n", " - [chapter_recurrent-modern/index](chapter_recurrent-modern/index.ipynb)\n", " - [chapter_attention-mechanisms/index](chapter_attention-mechanisms/index.ipynb)\n", " - [chapter_optimization/index](chapter_optimization/index.ipynb)\n", " - [chapter_computational-performance/index](chapter_computational-performance/index.ipynb)\n", " - [chapter_computer-vision/index](chapter_computer-vision/index.ipynb)\n", " - [chapter_natural-language-processing-pretraining/index](chapter_natural-language-processing-pretraining/index.ipynb)\n", " - [chapter_natural-language-processing-applications/index](chapter_natural-language-processing-applications/index.ipynb)\n", " - [chapter_appendix-tools-for-deep-learning/index](chapter_appendix-tools-for-deep-learning/index.ipynb)\n", ":end_tab:\n", "\n", ":begin_tab:toc\n", " - [chapter_references/zreferences](chapter_references/zreferences.ipynb)\n", ":end_tab:\n"]}], "metadata": {"language_info": {"name": "python"}}, "nbformat": 4, "nbformat_minor": 5}