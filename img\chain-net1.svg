<?xml version="1.0" encoding="UTF-8"?>
<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="141pt" height="110pt" viewBox="0 0 141 110" version="1.1">
<defs>
<g>
<symbol overflow="visible" id="glyph0-0">
<path style="stroke:none;" d=""/>
</symbol>
<symbol overflow="visible" id="glyph0-1">
<path style="stroke:none;" d="M 3.625 -3.828125 C 3.625 -3.9375 3.609375 -3.953125 3.5625 -3.953125 C 3.515625 -3.953125 3.484375 -3.9375 3.4375 -3.875 L 1.703125 -1.140625 C 1.671875 -1.921875 1.546875 -3.046875 1.421875 -3.65625 C 1.359375 -3.953125 1.34375 -3.96875 1.25 -3.96875 C 1.1875 -3.96875 1.109375 -3.9375 0.984375 -3.90625 C 0.71875 -3.828125 0.40625 -3.796875 0.140625 -3.75 L 0.140625 -3.640625 L 0.390625 -3.640625 C 0.640625 -3.640625 0.734375 -3.59375 0.8125 -3.265625 C 0.921875 -2.828125 1.015625 -1.859375 1.046875 -1.078125 L 1.09375 -0.265625 C 1.109375 0.09375 1.125 0.15625 1.203125 0.15625 C 1.3125 0.15625 1.453125 -0.0625 1.78125 -0.6875 C 1.953125 -1.015625 2.109375 -1.3125 2.28125 -1.578125 L 3.078125 -2.9375 L 3.34375 -0.15625 C 3.359375 0.078125 3.375 0.15625 3.4375 0.15625 C 3.515625 0.15625 3.59375 0.078125 3.84375 -0.21875 L 3.921875 -0.328125 C 5.171875 -1.84375 5.828125 -2.890625 5.828125 -3.46875 C 5.828125 -3.75 5.640625 -3.96875 5.359375 -3.96875 C 5.171875 -3.96875 5.015625 -3.828125 5.015625 -3.6875 C 5.015625 -3.5625 5.0625 -3.453125 5.203125 -3.328125 C 5.34375 -3.1875 5.40625 -3.109375 5.40625 -3 C 5.40625 -2.625 5.09375 -2.140625 3.921875 -0.671875 Z M 3.625 -3.828125 "/>
</symbol>
<symbol overflow="visible" id="glyph0-2">
<path style="stroke:none;" d="M 2.1875 -3.1875 C 2.0625 -3.8125 1.953125 -3.96875 1.734375 -3.96875 C 1.515625 -3.96875 1.21875 -3.90625 0.671875 -3.703125 L 0.578125 -3.671875 L 0.609375 -3.53125 L 0.765625 -3.578125 C 0.9375 -3.625 1.046875 -3.640625 1.109375 -3.640625 C 1.34375 -3.640625 1.40625 -3.5625 1.53125 -3.03125 L 1.78125 -1.90625 L 1.046875 -0.859375 C 0.859375 -0.59375 0.6875 -0.421875 0.578125 -0.421875 C 0.53125 -0.421875 0.4375 -0.453125 0.34375 -0.5 C 0.234375 -0.5625 0.140625 -0.609375 0.0625 -0.609375 C -0.109375 -0.609375 -0.25 -0.453125 -0.25 -0.28125 C -0.25 -0.046875 -0.078125 0.09375 0.203125 0.09375 C 0.484375 0.09375 0.671875 0.015625 1.0625 -0.515625 L 1.859375 -1.578125 L 2.109375 -0.515625 C 2.21875 -0.0625 2.359375 0.09375 2.640625 0.09375 C 2.984375 0.09375 3.21875 -0.109375 3.75 -0.921875 L 3.609375 -1.015625 C 3.53125 -0.921875 3.5 -0.859375 3.421875 -0.75 C 3.21875 -0.484375 3.109375 -0.390625 3 -0.390625 C 2.875 -0.390625 2.796875 -0.515625 2.734375 -0.765625 L 2.4375 -1.96875 C 2.390625 -2.1875 2.359375 -2.3125 2.359375 -2.375 C 2.765625 -3.0625 3.09375 -3.46875 3.25 -3.46875 C 3.46875 -3.46875 3.546875 -3.3125 3.71875 -3.3125 C 3.890625 -3.3125 4.015625 -3.453125 4.015625 -3.640625 C 4.015625 -3.828125 3.875 -3.96875 3.65625 -3.96875 C 3.265625 -3.96875 2.921875 -3.640625 2.296875 -2.6875 Z M 2.1875 -3.1875 "/>
</symbol>
<symbol overflow="visible" id="glyph0-3">
<path style="stroke:none;" d="M 2.1875 -1.671875 C 2.03125 -2.46875 1.5625 -3.96875 1.421875 -3.96875 L 1.390625 -3.96875 C 0.90625 -3.890625 0.609375 -3.84375 0.421875 -3.8125 C 0.328125 -3.78125 0.234375 -3.765625 0.140625 -3.75 L 0.140625 -3.59375 C 0.234375 -3.625 0.3125 -3.640625 0.40625 -3.640625 C 0.78125 -3.640625 0.9375 -3.5 1.109375 -3.0625 C 1.34375 -2.453125 1.84375 -0.4375 1.84375 -0.078125 C 1.84375 0.03125 1.8125 0.140625 1.75 0.25 C 1.6875 0.359375 1.28125 0.890625 1.109375 1.0625 C 0.90625 1.28125 0.796875 1.359375 0.6875 1.359375 C 0.453125 1.359375 0.453125 1.109375 0.140625 1.109375 C -0.0625 1.109375 -0.21875 1.265625 -0.21875 1.46875 C -0.21875 1.703125 -0.03125 1.859375 0.25 1.859375 C 0.75 1.859375 1.71875 0.8125 2.65625 -0.734375 C 3.40625 -1.96875 3.828125 -2.96875 3.828125 -3.46875 C 3.828125 -3.75 3.640625 -3.96875 3.359375 -3.96875 C 3.15625 -3.96875 3 -3.828125 3 -3.640625 C 3 -3.484375 3.0625 -3.40625 3.234375 -3.28125 C 3.40625 -3.171875 3.46875 -3.125 3.46875 -3 C 3.46875 -2.59375 3.140625 -1.953125 2.375 -0.6875 Z M 2.1875 -1.671875 "/>
</symbol>
<symbol overflow="visible" id="glyph0-4">
<path style="stroke:none;" d="M 3.421875 -3.75 L 3.421875 -3.859375 L 0.875 -3.859375 L 0.578125 -2.8125 L 0.734375 -2.765625 C 0.90625 -3.234375 1.078125 -3.3125 1.546875 -3.3125 L 2.765625 -3.3125 L -0.015625 0.03125 L 0.0625 0.109375 C 0.203125 0 0.328125 -0.046875 0.46875 -0.046875 C 0.703125 -0.046875 0.984375 0.078125 1.40625 0.328125 C 1.859375 0.609375 2.171875 0.734375 2.4375 0.734375 C 2.9375 0.734375 3.265625 0.40625 3.265625 0.046875 C 3.265625 -0.15625 3.140625 -0.28125 2.953125 -0.28125 C 2.8125 -0.28125 2.65625 -0.140625 2.65625 0.015625 C 2.65625 0.09375 2.671875 0.15625 2.734375 0.265625 C 2.75 0.328125 2.765625 0.375 2.765625 0.40625 C 2.765625 0.484375 2.671875 0.546875 2.53125 0.546875 C 2.296875 0.546875 2.140625 0.4375 1.90625 0.09375 C 1.53125 -0.4375 1.34375 -0.546875 0.8125 -0.65625 Z M 3.421875 -3.75 "/>
</symbol>
<symbol overflow="visible" id="glyph0-5">
<path style="stroke:none;" d="M 4.171875 -1 L 3.921875 -0.734375 C 3.65625 -0.4375 3.53125 -0.375 3.453125 -0.375 C 3.390625 -0.375 3.328125 -0.421875 3.328125 -0.484375 C 3.328125 -0.671875 3.703125 -2.21875 4.125 -3.75 C 4.15625 -3.84375 4.171875 -3.859375 4.1875 -3.9375 L 4.125 -3.96875 L 3.578125 -3.90625 L 3.546875 -3.875 L 3.453125 -3.453125 C 3.375 -3.78125 3.109375 -3.96875 2.734375 -3.96875 C 1.53125 -3.96875 0.15625 -2.34375 0.15625 -0.9375 C 0.15625 -0.265625 0.546875 0.09375 1.046875 0.09375 C 1.6875 0.09375 2.203125 -0.296875 2.875 -1.3125 C 2.6875 -0.578125 2.671875 -0.5 2.671875 -0.28125 C 2.671875 -0.015625 2.78125 0.09375 3.03125 0.09375 C 3.390625 0.09375 3.609375 -0.078125 4.28125 -0.90625 Z M 3.28125 -3.25 C 3.28125 -2.609375 3.015625 -1.84375 2.59375 -1.21875 C 2.328125 -0.84375 1.90625 -0.34375 1.453125 -0.34375 C 1.125 -0.34375 0.90625 -0.515625 0.90625 -1.015625 C 0.90625 -1.578125 1.21875 -2.375 1.609375 -2.9375 C 1.984375 -3.484375 2.375 -3.765625 2.78125 -3.765625 C 3.09375 -3.765625 3.28125 -3.5625 3.28125 -3.25 Z M 3.28125 -3.25 "/>
</symbol>
<symbol overflow="visible" id="glyph0-6">
<path style="stroke:none;" d="M 1.46875 -2.609375 L 2.21875 -5.359375 L 2.40625 -6.109375 L 2.359375 -6.140625 C 1.921875 -6.0625 1.59375 -6.015625 0.984375 -5.9375 L 0.984375 -5.78125 C 1.515625 -5.765625 1.5625 -5.765625 1.5625 -5.5625 C 1.5625 -5.484375 1.53125 -5.359375 1.484375 -5.140625 L 0.203125 -0.4375 L 0.203125 -0.375 C 0.203125 -0.109375 0.859375 0.09375 1.375 0.09375 C 2.765625 0.09375 4.25 -1.46875 4.25 -2.890625 C 4.25 -3.578125 3.8125 -3.96875 3.21875 -3.96875 C 2.578125 -3.96875 2.09375 -3.625 1.46875 -2.609375 Z M 3.484375 -2.75 C 3.484375 -2.09375 3.046875 -1.203125 2.5 -0.640625 C 2.171875 -0.296875 1.78125 -0.109375 1.390625 -0.109375 C 1.109375 -0.109375 0.984375 -0.203125 0.984375 -0.40625 C 0.984375 -0.953125 1.234375 -1.828125 1.625 -2.5 C 2.015625 -3.15625 2.4375 -3.53125 2.859375 -3.53125 C 3.265625 -3.53125 3.484375 -3.296875 3.484375 -2.75 Z M 3.484375 -2.75 "/>
</symbol>
<symbol overflow="visible" id="glyph0-7">
<path style="stroke:none;" d="M 4.15625 -1.078125 C 3.6875 -0.46875 3.546875 -0.34375 3.40625 -0.34375 C 3.34375 -0.34375 3.296875 -0.390625 3.296875 -0.5 C 3.296875 -0.609375 3.421875 -1.03125 3.46875 -1.203125 L 4.1875 -3.890625 L 3.515625 -3.890625 C 3.078125 -2.6875 2.953125 -2.34375 2.53125 -1.703125 C 1.96875 -0.859375 1.5625 -0.375 1.25 -0.375 C 1.109375 -0.375 1.078125 -0.46875 1.078125 -0.609375 C 1.078125 -0.640625 1.09375 -0.71875 1.09375 -0.75 L 1.90625 -3.9375 L 1.859375 -3.96875 C 1.34375 -3.859375 1.03125 -3.78125 0.515625 -3.71875 L 0.515625 -3.59375 C 0.859375 -3.59375 0.875 -3.59375 0.96875 -3.546875 C 1.015625 -3.515625 1.0625 -3.453125 1.0625 -3.390625 C 1.0625 -3.3125 1.015625 -3.078125 0.9375 -2.78125 L 0.625 -1.53125 C 0.453125 -0.875 0.375 -0.546875 0.375 -0.375 C 0.375 -0.046875 0.546875 0.09375 0.859375 0.09375 C 1.5 0.09375 1.9375 -0.375 3 -2.125 C 2.71875 -1.15625 2.59375 -0.546875 2.59375 -0.34375 C 2.59375 -0.078125 2.765625 0.078125 3.03125 0.078125 C 3.453125 0.078125 3.6875 -0.09375 4.28125 -0.984375 Z M 4.15625 -1.078125 "/>
</symbol>
<symbol overflow="visible" id="glyph0-8">
<path style="stroke:none;" d="M 1.859375 -0.625 C 1.8125 -1.84375 1.75 -2.34375 1.640625 -2.9375 C 1.453125 -3.921875 1.453125 -3.96875 1.375 -3.96875 C 1.328125 -3.96875 1.265625 -3.953125 1.1875 -3.9375 C 0.90625 -3.875 0.578125 -3.8125 0.1875 -3.75 L 0.1875 -3.625 C 0.28125 -3.640625 0.375 -3.640625 0.453125 -3.640625 C 0.796875 -3.640625 0.890625 -3.484375 1.015625 -2.796875 C 1.125 -2.109375 1.21875 -0.921875 1.21875 -0.296875 L 1.21875 -0.078125 C 1.21875 0.078125 1.25 0.15625 1.3125 0.15625 C 1.453125 0.15625 1.703125 -0.140625 2.203125 -0.671875 C 2.359375 -0.84375 2.859375 -1.40625 3.265625 -2.046875 C 3.578125 -2.53125 3.828125 -3.046875 3.828125 -3.4375 C 3.828125 -3.6875 3.625 -3.96875 3.328125 -3.96875 C 3.140625 -3.96875 2.984375 -3.859375 2.984375 -3.65625 C 2.984375 -3.515625 3.046875 -3.40625 3.21875 -3.265625 C 3.328125 -3.15625 3.375 -3.09375 3.375 -3 C 3.375 -2.578125 2.796875 -1.578125 2.140625 -0.921875 Z M 1.859375 -0.625 "/>
</symbol>
<symbol overflow="visible" id="glyph0-9">
<path style="stroke:none;" d="M 0.375 -3.859375 L 0.3125 -3.5625 L 1.125 -3.5625 L 0.328125 0.21875 C 0.125 1.1875 -0.15625 1.671875 -0.546875 1.671875 C -0.640625 1.671875 -0.71875 1.609375 -0.71875 1.53125 C -0.71875 1.4375 -0.640625 1.390625 -0.640625 1.265625 C -0.640625 1.078125 -0.78125 0.9375 -0.984375 0.9375 C -1.171875 0.9375 -1.328125 1.109375 -1.328125 1.3125 C -1.328125 1.625 -1.015625 1.859375 -0.609375 1.859375 C 0.203125 1.859375 0.8125 0.984375 1.1875 -0.6875 L 1.84375 -3.5625 L 2.8125 -3.5625 L 2.859375 -3.859375 L 1.90625 -3.859375 C 2.15625 -5.28125 2.5 -5.90625 3 -5.90625 C 3.125 -5.90625 3.1875 -5.859375 3.1875 -5.78125 C 3.1875 -5.703125 3.109375 -5.65625 3.109375 -5.515625 C 3.109375 -5.296875 3.28125 -5.15625 3.4375 -5.15625 C 3.640625 -5.15625 3.8125 -5.34375 3.8125 -5.546875 C 3.8125 -5.875 3.484375 -6.109375 3.046875 -6.109375 C 2.5 -6.109375 2.125 -5.796875 1.859375 -5.40625 C 1.5625 -4.984375 1.40625 -4.484375 1.203125 -3.859375 Z M 0.375 -3.859375 "/>
</symbol>
</g>
</defs>
<g id="surface1">
<path style="fill-rule:nonzero;fill:rgb(69.804382%,85.098267%,100%);fill-opacity:1;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 23.65625 4.34375 C 26.78125 7.46875 26.78125 12.53125 23.65625 15.65625 C 20.53125 18.78125 15.46875 18.78125 12.34375 15.65625 C 9.21875 12.53125 9.21875 7.46875 12.34375 4.34375 C 15.46875 1.21875 20.53125 1.21875 23.65625 4.34375 " transform="matrix(1,0,0,1,-9,2)"/>
<g style="fill:rgb(0%,0%,0%);fill-opacity:1;">
  <use xlink:href="#glyph0-1" x="5.9965" y="14"/>
</g>
<path style="fill-rule:nonzero;fill:rgb(69.804382%,85.098267%,100%);fill-opacity:1;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 23.65625 33.34375 C 26.78125 36.46875 26.78125 41.53125 23.65625 44.65625 C 20.53125 47.78125 15.46875 47.78125 12.34375 44.65625 C 9.21875 41.53125 9.21875 36.46875 12.34375 33.34375 C 15.46875 30.21875 20.53125 30.21875 23.65625 33.34375 " transform="matrix(1,0,0,1,-9,2)"/>
<g style="fill:rgb(0%,0%,0%);fill-opacity:1;">
  <use xlink:href="#glyph0-2" x="7" y="43"/>
</g>
<path style="fill-rule:nonzero;fill:rgb(69.804382%,85.098267%,100%);fill-opacity:1;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 23.65625 62.34375 C 26.78125 65.46875 26.78125 70.53125 23.65625 73.65625 C 20.53125 76.78125 15.46875 76.78125 12.34375 73.65625 C 9.21875 70.53125 9.21875 65.46875 12.34375 62.34375 C 15.46875 59.21875 20.53125 59.21875 23.65625 62.34375 " transform="matrix(1,0,0,1,-9,2)"/>
<g style="fill:rgb(0%,0%,0%);fill-opacity:1;">
  <use xlink:href="#glyph0-3" x="7" y="72"/>
</g>
<path style="fill-rule:nonzero;fill:rgb(69.804382%,85.098267%,100%);fill-opacity:1;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 23.65625 91.34375 C 26.78125 94.46875 26.78125 99.53125 23.65625 102.65625 C 20.53125 105.78125 15.46875 105.78125 12.34375 102.65625 C 9.21875 99.53125 9.21875 94.46875 12.34375 91.34375 C 15.46875 88.21875 20.53125 88.21875 23.65625 91.34375 " transform="matrix(1,0,0,1,-9,2)"/>
<g style="fill:rgb(0%,0%,0%);fill-opacity:1;">
  <use xlink:href="#glyph0-4" x="7.2475" y="101"/>
</g>
<path style="fill-rule:nonzero;fill:rgb(69.804382%,85.098267%,100%);fill-opacity:1;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 64.65625 33.34375 C 67.78125 36.46875 67.78125 41.53125 64.65625 44.65625 C 61.53125 47.78125 56.46875 47.78125 53.34375 44.65625 C 50.21875 41.53125 50.21875 36.46875 53.34375 33.34375 C 56.46875 30.21875 61.53125 30.21875 64.65625 33.34375 " transform="matrix(1,0,0,1,-9,2)"/>
<g style="fill:rgb(0%,0%,0%);fill-opacity:1;">
  <use xlink:href="#glyph0-5" x="47.7435" y="43"/>
</g>
<path style="fill-rule:nonzero;fill:rgb(69.804382%,85.098267%,100%);fill-opacity:1;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 64.65625 62.34375 C 67.78125 65.46875 67.78125 70.53125 64.65625 73.65625 C 61.53125 76.78125 56.46875 76.78125 53.34375 73.65625 C 50.21875 70.53125 50.21875 65.46875 53.34375 62.34375 C 56.46875 59.21875 61.53125 59.21875 64.65625 62.34375 " transform="matrix(1,0,0,1,-9,2)"/>
<g style="fill:rgb(0%,0%,0%);fill-opacity:1;">
  <use xlink:href="#glyph0-6" x="47.748" y="72"/>
</g>
<path style="fill-rule:nonzero;fill:rgb(69.804382%,85.098267%,100%);fill-opacity:1;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 105.65625 33.34375 C 108.78125 36.46875 108.78125 41.53125 105.65625 44.65625 C 102.53125 47.78125 97.46875 47.78125 94.34375 44.65625 C 91.21875 41.53125 91.21875 36.46875 94.34375 33.34375 C 97.46875 30.21875 102.53125 30.21875 105.65625 33.34375 " transform="matrix(1,0,0,1,-9,2)"/>
<g style="fill:rgb(0%,0%,0%);fill-opacity:1;">
  <use xlink:href="#glyph0-7" x="88.748" y="43"/>
</g>
<path style="fill-rule:nonzero;fill:rgb(69.804382%,85.098267%,100%);fill-opacity:1;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 105.65625 62.34375 C 108.78125 65.46875 108.78125 70.53125 105.65625 73.65625 C 102.53125 76.78125 97.46875 76.78125 94.34375 73.65625 C 91.21875 70.53125 91.21875 65.46875 94.34375 62.34375 C 97.46875 59.21875 102.53125 59.21875 105.65625 62.34375 " transform="matrix(1,0,0,1,-9,2)"/>
<g style="fill:rgb(0%,0%,0%);fill-opacity:1;">
  <use xlink:href="#glyph0-8" x="89" y="72"/>
</g>
<path style="fill-rule:nonzero;fill:rgb(69.804382%,85.098267%,100%);fill-opacity:1;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 146.65625 48.34375 C 149.78125 51.46875 149.78125 56.53125 146.65625 59.65625 C 143.53125 62.78125 138.46875 62.78125 135.34375 59.65625 C 132.21875 56.53125 132.21875 51.46875 135.34375 48.34375 C 138.46875 45.21875 143.53125 45.21875 146.65625 48.34375 " transform="matrix(1,0,0,1,-9,2)"/>
<g style="fill:rgb(0%,0%,0%);fill-opacity:1;">
  <use xlink:href="#glyph0-9" x="130.747" y="58"/>
</g>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 24.53125 14.621094 L 47.652344 30.972656 " transform="matrix(1,0,0,1,-9,2)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:butt;stroke-linejoin:miter;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 50.917969 33.28125 L 47.652344 30.972656 M 48.515625 29.746094 L 50.917969 33.28125 L 46.785156 32.195312 " transform="matrix(1,0,0,1,-9,2)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 26 39 L 45.101562 39 " transform="matrix(1,0,0,1,-9,2)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:butt;stroke-linejoin:miter;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 49.101562 39 L 45.101562 39 M 45.101562 37.5 L 49.101562 39 L 45.101562 40.5 " transform="matrix(1,0,0,1,-9,2)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 24.53125 63.378906 L 47.652344 47.027344 " transform="matrix(1,0,0,1,-9,2)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:butt;stroke-linejoin:miter;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 50.917969 44.71875 L 47.652344 47.027344 M 46.785156 45.804688 L 50.917969 44.71875 L 48.515625 48.253906 " transform="matrix(1,0,0,1,-9,2)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 22.617188 90.464844 L 50.976562 50.351562 " transform="matrix(1,0,0,1,-9,2)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:butt;stroke-linejoin:miter;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 53.285156 47.085938 L 50.976562 50.351562 M 49.75 49.484375 L 53.285156 47.085938 L 52.199219 51.21875 " transform="matrix(1,0,0,1,-9,2)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 22.617188 16.535156 L 50.976562 56.648438 " transform="matrix(1,0,0,1,-9,2)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:butt;stroke-linejoin:miter;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 53.285156 59.914062 L 50.976562 56.648438 M 52.199219 55.78125 L 53.285156 59.914062 L 49.75 57.515625 " transform="matrix(1,0,0,1,-9,2)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 24.53125 43.621094 L 47.652344 59.972656 " transform="matrix(1,0,0,1,-9,2)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:butt;stroke-linejoin:miter;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 50.917969 62.28125 L 47.652344 59.972656 M 48.515625 58.746094 L 50.917969 62.28125 L 46.785156 61.195312 " transform="matrix(1,0,0,1,-9,2)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 26 68 L 45.101562 68 " transform="matrix(1,0,0,1,-9,2)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:butt;stroke-linejoin:miter;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 49.101562 68 L 45.101562 68 M 45.101562 66.5 L 49.101562 68 L 45.101562 69.5 " transform="matrix(1,0,0,1,-9,2)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 24.53125 92.378906 L 47.652344 76.027344 " transform="matrix(1,0,0,1,-9,2)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:butt;stroke-linejoin:miter;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 50.917969 73.71875 L 47.652344 76.027344 M 46.785156 74.804688 L 50.917969 73.71875 L 48.515625 77.253906 " transform="matrix(1,0,0,1,-9,2)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 65.53125 63.378906 L 88.652344 47.027344 " transform="matrix(1,0,0,1,-9,2)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:butt;stroke-linejoin:miter;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 91.917969 44.71875 L 88.652344 47.027344 M 87.785156 45.804688 L 91.917969 44.71875 L 89.515625 48.253906 " transform="matrix(1,0,0,1,-9,2)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 107.574219 65.414062 L 127.84375 58.492188 " transform="matrix(1,0,0,1,-9,2)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:butt;stroke-linejoin:miter;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 131.628906 57.199219 L 127.84375 58.492188 M 127.359375 57.074219 L 131.628906 57.199219 L 128.328125 59.910156 " transform="matrix(1,0,0,1,-9,2)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 67 39 L 86.101562 39 " transform="matrix(1,0,0,1,-9,2)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:butt;stroke-linejoin:miter;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 90.101562 39 L 86.101562 39 M 86.101562 37.5 L 90.101562 39 L 86.101562 40.5 " transform="matrix(1,0,0,1,-9,2)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 65.53125 43.621094 L 88.652344 59.972656 " transform="matrix(1,0,0,1,-9,2)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:butt;stroke-linejoin:miter;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 91.917969 62.28125 L 88.652344 59.972656 M 89.515625 58.746094 L 91.917969 62.28125 L 87.785156 61.195312 " transform="matrix(1,0,0,1,-9,2)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 107.515625 41.75 L 127.945312 49.222656 " transform="matrix(1,0,0,1,-9,2)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:butt;stroke-linejoin:miter;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 131.699219 50.597656 L 127.945312 49.222656 M 128.460938 47.816406 L 131.699219 50.597656 L 127.429688 50.632812 " transform="matrix(1,0,0,1,-9,2)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 67 67.902344 L 87.101562 67.65625 " transform="matrix(1,0,0,1,-9,2)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:butt;stroke-linejoin:miter;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 91.101562 67.609375 L 87.101562 67.65625 M 87.082031 66.15625 L 91.101562 67.609375 L 87.117188 69.15625 " transform="matrix(1,0,0,1,-9,2)"/>
</g>
</svg>
