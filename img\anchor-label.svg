<?xml version="1.0" encoding="UTF-8"?>
<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="373pt" height="246pt" viewBox="0 0 373 246" version="1.1">
<defs>
<g>
<symbol overflow="visible" id="glyph0-0">
<path style="stroke:none;" d="M 1.125 0 L 1.125 -5.625 L 5.625 -5.625 L 5.625 0 Z M 1.265625 -0.140625 L 5.484375 -0.140625 L 5.484375 -5.484375 L 1.265625 -5.484375 Z M 1.265625 -0.140625 "/>
</symbol>
<symbol overflow="visible" id="glyph0-1">
<path style="stroke:none;" d="M 4.484375 -4.859375 L 3.6875 -4.796875 C 3.625 -5.109375 3.523438 -5.335938 3.390625 -5.484375 C 3.179688 -5.703125 2.921875 -5.8125 2.609375 -5.8125 C 2.347656 -5.8125 2.125 -5.742188 1.9375 -5.609375 C 1.6875 -5.421875 1.488281 -5.15625 1.34375 -4.8125 C 1.207031 -4.46875 1.132812 -3.972656 1.125 -3.328125 C 1.3125 -3.609375 1.539062 -3.816406 1.8125 -3.953125 C 2.09375 -4.097656 2.382812 -4.171875 2.6875 -4.171875 C 3.21875 -4.171875 3.664062 -3.976562 4.03125 -3.59375 C 4.40625 -3.207031 4.59375 -2.707031 4.59375 -2.09375 C 4.59375 -1.6875 4.503906 -1.304688 4.328125 -0.953125 C 4.148438 -0.609375 3.910156 -0.34375 3.609375 -0.15625 C 3.304688 0.0195312 2.960938 0.109375 2.578125 0.109375 C 1.921875 0.109375 1.382812 -0.128906 0.96875 -0.609375 C 0.550781 -1.097656 0.34375 -1.898438 0.34375 -3.015625 C 0.34375 -4.253906 0.570312 -5.160156 1.03125 -5.734375 C 1.425781 -6.222656 1.96875 -6.46875 2.65625 -6.46875 C 3.15625 -6.46875 3.566406 -6.320312 3.890625 -6.03125 C 4.210938 -5.75 4.410156 -5.359375 4.484375 -4.859375 Z M 1.25 -2.09375 C 1.25 -1.8125 1.304688 -1.546875 1.421875 -1.296875 C 1.535156 -1.054688 1.695312 -0.867188 1.90625 -0.734375 C 2.113281 -0.609375 2.332031 -0.546875 2.5625 -0.546875 C 2.894531 -0.546875 3.179688 -0.675781 3.421875 -0.9375 C 3.660156 -1.207031 3.78125 -1.578125 3.78125 -2.046875 C 3.78125 -2.492188 3.660156 -2.84375 3.421875 -3.09375 C 3.191406 -3.351562 2.894531 -3.484375 2.53125 -3.484375 C 2.175781 -3.484375 1.875 -3.351562 1.625 -3.09375 C 1.375 -2.84375 1.25 -2.507812 1.25 -2.09375 Z M 1.25 -2.09375 "/>
</symbol>
<symbol overflow="visible" id="glyph0-2">
<path style="stroke:none;" d="M 0.421875 -5.59375 L 0.421875 -6.359375 L 4.59375 -6.359375 L 4.59375 -5.75 C 4.1875 -5.3125 3.78125 -4.726562 3.375 -4 C 2.96875 -3.28125 2.65625 -2.535156 2.4375 -1.765625 C 2.28125 -1.234375 2.179688 -0.644531 2.140625 0 L 1.328125 0 C 1.335938 -0.507812 1.4375 -1.117188 1.625 -1.828125 C 1.8125 -2.546875 2.082031 -3.238281 2.4375 -3.90625 C 2.800781 -4.570312 3.179688 -5.132812 3.578125 -5.59375 Z M 0.421875 -5.59375 "/>
</symbol>
<symbol overflow="visible" id="glyph0-3">
<path style="stroke:none;" d="M 1.59375 -3.5 C 1.257812 -3.613281 1.015625 -3.78125 0.859375 -4 C 0.703125 -4.226562 0.625 -4.5 0.625 -4.8125 C 0.625 -5.28125 0.789062 -5.671875 1.125 -5.984375 C 1.46875 -6.304688 1.914062 -6.46875 2.46875 -6.46875 C 3.03125 -6.46875 3.484375 -6.300781 3.828125 -5.96875 C 4.171875 -5.644531 4.34375 -5.25 4.34375 -4.78125 C 4.34375 -4.488281 4.265625 -4.226562 4.109375 -4 C 3.953125 -3.78125 3.710938 -3.613281 3.390625 -3.5 C 3.785156 -3.363281 4.085938 -3.148438 4.296875 -2.859375 C 4.503906 -2.578125 4.609375 -2.238281 4.609375 -1.84375 C 4.609375 -1.289062 4.410156 -0.828125 4.015625 -0.453125 C 3.628906 -0.078125 3.117188 0.109375 2.484375 0.109375 C 1.847656 0.109375 1.332031 -0.078125 0.9375 -0.453125 C 0.550781 -0.828125 0.359375 -1.296875 0.359375 -1.859375 C 0.359375 -2.273438 0.460938 -2.625 0.671875 -2.90625 C 0.890625 -3.195312 1.195312 -3.394531 1.59375 -3.5 Z M 1.4375 -4.828125 C 1.4375 -4.523438 1.53125 -4.273438 1.71875 -4.078125 C 1.914062 -3.890625 2.171875 -3.796875 2.484375 -3.796875 C 2.796875 -3.796875 3.046875 -3.890625 3.234375 -4.078125 C 3.429688 -4.273438 3.53125 -4.515625 3.53125 -4.796875 C 3.53125 -5.078125 3.429688 -5.316406 3.234375 -5.515625 C 3.035156 -5.710938 2.785156 -5.8125 2.484375 -5.8125 C 2.179688 -5.8125 1.929688 -5.710938 1.734375 -5.515625 C 1.535156 -5.328125 1.4375 -5.097656 1.4375 -4.828125 Z M 1.171875 -1.859375 C 1.171875 -1.628906 1.222656 -1.410156 1.328125 -1.203125 C 1.441406 -0.992188 1.601562 -0.832031 1.8125 -0.71875 C 2.019531 -0.601562 2.25 -0.546875 2.5 -0.546875 C 2.875 -0.546875 3.179688 -0.664062 3.421875 -0.90625 C 3.671875 -1.144531 3.796875 -1.453125 3.796875 -1.828125 C 3.796875 -2.210938 3.671875 -2.53125 3.421875 -2.78125 C 3.171875 -3.03125 2.851562 -3.15625 2.46875 -3.15625 C 2.09375 -3.15625 1.78125 -3.03125 1.53125 -2.78125 C 1.289062 -2.53125 1.171875 -2.222656 1.171875 -1.859375 Z M 1.171875 -1.859375 "/>
</symbol>
<symbol overflow="visible" id="glyph0-4">
<path style="stroke:none;" d="M 0.5 -1.484375 L 1.25 -1.5625 C 1.3125 -1.207031 1.429688 -0.945312 1.609375 -0.78125 C 1.796875 -0.625 2.035156 -0.546875 2.328125 -0.546875 C 2.566406 -0.546875 2.773438 -0.597656 2.953125 -0.703125 C 3.140625 -0.816406 3.289062 -0.96875 3.40625 -1.15625 C 3.53125 -1.34375 3.628906 -1.59375 3.703125 -1.90625 C 3.785156 -2.21875 3.828125 -2.539062 3.828125 -2.875 C 3.828125 -2.90625 3.820312 -2.957031 3.8125 -3.03125 C 3.65625 -2.78125 3.441406 -2.578125 3.171875 -2.421875 C 2.898438 -2.265625 2.601562 -2.1875 2.28125 -2.1875 C 1.75 -2.1875 1.296875 -2.378906 0.921875 -2.765625 C 0.554688 -3.148438 0.375 -3.660156 0.375 -4.296875 C 0.375 -4.953125 0.566406 -5.476562 0.953125 -5.875 C 1.335938 -6.269531 1.820312 -6.46875 2.40625 -6.46875 C 2.820312 -6.46875 3.203125 -6.351562 3.546875 -6.125 C 3.890625 -5.90625 4.148438 -5.585938 4.328125 -5.171875 C 4.515625 -4.753906 4.609375 -4.148438 4.609375 -3.359375 C 4.609375 -2.535156 4.519531 -1.878906 4.34375 -1.390625 C 4.164062 -0.898438 3.898438 -0.523438 3.546875 -0.265625 C 3.191406 -0.015625 2.773438 0.109375 2.296875 0.109375 C 1.796875 0.109375 1.382812 -0.03125 1.0625 -0.3125 C 0.75 -0.59375 0.5625 -0.984375 0.5 -1.484375 Z M 3.734375 -4.328125 C 3.734375 -4.785156 3.609375 -5.144531 3.359375 -5.40625 C 3.117188 -5.675781 2.832031 -5.8125 2.5 -5.8125 C 2.144531 -5.8125 1.835938 -5.664062 1.578125 -5.375 C 1.316406 -5.09375 1.1875 -4.722656 1.1875 -4.265625 C 1.1875 -3.859375 1.304688 -3.523438 1.546875 -3.265625 C 1.796875 -3.015625 2.101562 -2.890625 2.46875 -2.890625 C 2.84375 -2.890625 3.144531 -3.015625 3.375 -3.265625 C 3.613281 -3.523438 3.734375 -3.878906 3.734375 -4.328125 Z M 3.734375 -4.328125 "/>
</symbol>
<symbol overflow="visible" id="glyph0-5">
<path style="stroke:none;" d="M 2.90625 0 L 2.90625 -1.546875 L 0.109375 -1.546875 L 0.109375 -2.265625 L 3.046875 -6.4375 L 3.703125 -6.4375 L 3.703125 -2.265625 L 4.578125 -2.265625 L 4.578125 -1.546875 L 3.703125 -1.546875 L 3.703125 0 Z M 2.90625 -2.265625 L 2.90625 -5.171875 L 0.890625 -2.265625 Z M 2.90625 -2.265625 "/>
</symbol>
<symbol overflow="visible" id="glyph0-6">
<path style="stroke:none;" d="M 0.375 -1.6875 L 1.203125 -1.765625 C 1.265625 -1.359375 1.40625 -1.050781 1.625 -0.84375 C 1.851562 -0.644531 2.125 -0.546875 2.4375 -0.546875 C 2.820312 -0.546875 3.144531 -0.6875 3.40625 -0.96875 C 3.675781 -1.257812 3.8125 -1.640625 3.8125 -2.109375 C 3.8125 -2.566406 3.679688 -2.925781 3.421875 -3.1875 C 3.171875 -3.445312 2.84375 -3.578125 2.4375 -3.578125 C 2.175781 -3.578125 1.941406 -3.515625 1.734375 -3.390625 C 1.535156 -3.273438 1.375 -3.128906 1.25 -2.953125 L 0.515625 -3.046875 L 1.140625 -6.359375 L 4.34375 -6.359375 L 4.34375 -5.59375 L 1.765625 -5.59375 L 1.421875 -3.875 C 1.804688 -4.132812 2.210938 -4.265625 2.640625 -4.265625 C 3.203125 -4.265625 3.675781 -4.070312 4.0625 -3.6875 C 4.445312 -3.300781 4.640625 -2.800781 4.640625 -2.1875 C 4.640625 -1.601562 4.472656 -1.097656 4.140625 -0.671875 C 3.722656 -0.148438 3.15625 0.109375 2.4375 0.109375 C 1.851562 0.109375 1.375 -0.0507812 1 -0.375 C 0.632812 -0.707031 0.425781 -1.144531 0.375 -1.6875 Z M 0.375 -1.6875 "/>
</symbol>
<symbol overflow="visible" id="glyph0-7">
<path style="stroke:none;" d="M 3.359375 0 L 2.5625 0 L 2.5625 -5.046875 C 2.375 -4.859375 2.125 -4.671875 1.8125 -4.484375 C 1.5 -4.304688 1.222656 -4.175781 0.984375 -4.09375 L 0.984375 -4.859375 C 1.421875 -5.054688 1.804688 -5.300781 2.140625 -5.59375 C 2.472656 -5.894531 2.707031 -6.1875 2.84375 -6.46875 L 3.359375 -6.46875 Z M 3.359375 0 "/>
</symbol>
<symbol overflow="visible" id="glyph0-8">
<path style="stroke:none;" d="M 0.375 -1.703125 L 1.171875 -1.8125 C 1.265625 -1.363281 1.414062 -1.039062 1.625 -0.84375 C 1.84375 -0.644531 2.113281 -0.546875 2.4375 -0.546875 C 2.800781 -0.546875 3.109375 -0.671875 3.359375 -0.921875 C 3.617188 -1.179688 3.75 -1.503906 3.75 -1.890625 C 3.75 -2.253906 3.628906 -2.550781 3.390625 -2.78125 C 3.160156 -3.019531 2.863281 -3.140625 2.5 -3.140625 C 2.34375 -3.140625 2.15625 -3.109375 1.9375 -3.046875 L 2.03125 -3.75 C 2.082031 -3.738281 2.125 -3.734375 2.15625 -3.734375 C 2.488281 -3.734375 2.789062 -3.820312 3.0625 -4 C 3.332031 -4.175781 3.46875 -4.445312 3.46875 -4.8125 C 3.46875 -5.101562 3.367188 -5.34375 3.171875 -5.53125 C 2.972656 -5.71875 2.71875 -5.8125 2.40625 -5.8125 C 2.101562 -5.8125 1.847656 -5.710938 1.640625 -5.515625 C 1.441406 -5.328125 1.3125 -5.039062 1.25 -4.65625 L 0.453125 -4.796875 C 0.554688 -5.328125 0.773438 -5.738281 1.109375 -6.03125 C 1.453125 -6.320312 1.878906 -6.46875 2.390625 -6.46875 C 2.742188 -6.46875 3.066406 -6.390625 3.359375 -6.234375 C 3.660156 -6.085938 3.890625 -5.882812 4.046875 -5.625 C 4.203125 -5.363281 4.28125 -5.085938 4.28125 -4.796875 C 4.28125 -4.515625 4.203125 -4.257812 4.046875 -4.03125 C 3.898438 -3.800781 3.679688 -3.617188 3.390625 -3.484375 C 3.773438 -3.398438 4.070312 -3.21875 4.28125 -2.9375 C 4.488281 -2.664062 4.59375 -2.320312 4.59375 -1.90625 C 4.59375 -1.34375 4.382812 -0.863281 3.96875 -0.46875 C 3.5625 -0.0820312 3.046875 0.109375 2.421875 0.109375 C 1.859375 0.109375 1.390625 -0.0546875 1.015625 -0.390625 C 0.640625 -0.722656 0.425781 -1.160156 0.375 -1.703125 Z M 0.375 -1.703125 "/>
</symbol>
<symbol overflow="visible" id="glyph0-9">
<path style="stroke:none;" d="M 4.53125 -0.765625 L 4.53125 0 L 0.265625 0 C 0.265625 -0.1875 0.296875 -0.367188 0.359375 -0.546875 C 0.472656 -0.835938 0.648438 -1.125 0.890625 -1.40625 C 1.128906 -1.6875 1.472656 -2.007812 1.921875 -2.375 C 2.617188 -2.957031 3.085938 -3.414062 3.328125 -3.75 C 3.578125 -4.082031 3.703125 -4.398438 3.703125 -4.703125 C 3.703125 -5.015625 3.585938 -5.273438 3.359375 -5.484375 C 3.140625 -5.703125 2.851562 -5.8125 2.5 -5.8125 C 2.113281 -5.8125 1.804688 -5.695312 1.578125 -5.46875 C 1.347656 -5.238281 1.234375 -4.921875 1.234375 -4.515625 L 0.421875 -4.609375 C 0.472656 -5.210938 0.679688 -5.671875 1.046875 -5.984375 C 1.410156 -6.304688 1.898438 -6.46875 2.515625 -6.46875 C 3.128906 -6.46875 3.613281 -6.296875 3.96875 -5.953125 C 4.332031 -5.609375 4.515625 -5.1875 4.515625 -4.6875 C 4.515625 -4.425781 4.460938 -4.171875 4.359375 -3.921875 C 4.253906 -3.671875 4.078125 -3.40625 3.828125 -3.125 C 3.585938 -2.851562 3.1875 -2.476562 2.625 -2 C 2.144531 -1.601562 1.835938 -1.332031 1.703125 -1.1875 C 1.566406 -1.039062 1.457031 -0.898438 1.375 -0.765625 Z M 4.53125 -0.765625 "/>
</symbol>
<symbol overflow="visible" id="glyph1-0">
<path style="stroke:none;" d=""/>
</symbol>
<symbol overflow="visible" id="glyph1-1">
<path style="stroke:none;" d="M 2.1875 -3.1875 C 2.0625 -3.8125 1.953125 -3.96875 1.734375 -3.96875 C 1.515625 -3.96875 1.21875 -3.90625 0.671875 -3.703125 L 0.578125 -3.671875 L 0.609375 -3.53125 L 0.765625 -3.578125 C 0.9375 -3.625 1.046875 -3.640625 1.109375 -3.640625 C 1.34375 -3.640625 1.40625 -3.5625 1.53125 -3.03125 L 1.78125 -1.90625 L 1.046875 -0.859375 C 0.859375 -0.59375 0.6875 -0.421875 0.578125 -0.421875 C 0.53125 -0.421875 0.4375 -0.453125 0.34375 -0.5 C 0.234375 -0.5625 0.140625 -0.609375 0.0625 -0.609375 C -0.109375 -0.609375 -0.25 -0.453125 -0.25 -0.28125 C -0.25 -0.046875 -0.078125 0.09375 0.203125 0.09375 C 0.484375 0.09375 0.671875 0.015625 1.0625 -0.515625 L 1.859375 -1.578125 L 2.109375 -0.515625 C 2.21875 -0.0625 2.359375 0.09375 2.640625 0.09375 C 2.984375 0.09375 3.21875 -0.109375 3.75 -0.921875 L 3.609375 -1.015625 C 3.53125 -0.921875 3.5 -0.859375 3.421875 -0.75 C 3.21875 -0.484375 3.109375 -0.390625 3 -0.390625 C 2.875 -0.390625 2.796875 -0.515625 2.734375 -0.765625 L 2.4375 -1.96875 C 2.390625 -2.1875 2.359375 -2.3125 2.359375 -2.375 C 2.765625 -3.0625 3.09375 -3.46875 3.25 -3.46875 C 3.46875 -3.46875 3.546875 -3.3125 3.71875 -3.3125 C 3.890625 -3.3125 4.015625 -3.453125 4.015625 -3.640625 C 4.015625 -3.828125 3.875 -3.96875 3.65625 -3.96875 C 3.265625 -3.96875 2.921875 -3.640625 2.296875 -2.6875 Z M 2.1875 -3.1875 "/>
</symbol>
<symbol overflow="visible" id="glyph2-0">
<path style="stroke:none;" d=""/>
</symbol>
<symbol overflow="visible" id="glyph2-1">
<path style="stroke:none;" d="M 2.84375 -0.828125 L 2.765625 -0.859375 C 2.5625 -0.515625 2.4375 -0.453125 2.1875 -0.453125 L 0.78125 -0.453125 L 1.765625 -1.515625 C 2.296875 -2.078125 2.53125 -2.53125 2.53125 -3 C 2.53125 -3.59375 2.109375 -4.0625 1.421875 -4.0625 C 0.6875 -4.0625 0.3125 -3.5625 0.1875 -2.859375 L 0.3125 -2.828125 C 0.546875 -3.421875 0.75 -3.609375 1.1875 -3.609375 C 1.703125 -3.609375 2.015625 -3.3125 2.015625 -2.765625 C 2.015625 -2.25 1.8125 -1.796875 1.234375 -1.203125 L 0.171875 -0.078125 L 0.171875 0 L 2.515625 0 Z M 2.84375 -0.828125 "/>
</symbol>
<symbol overflow="visible" id="glyph2-2">
<path style="stroke:none;" d="M 0.359375 -3.0625 C 0.609375 -3.5 0.890625 -3.703125 1.265625 -3.703125 C 1.65625 -3.703125 1.90625 -3.484375 1.90625 -3.078125 C 1.90625 -2.71875 1.71875 -2.453125 1.421875 -2.28125 C 1.296875 -2.203125 1.140625 -2.140625 0.921875 -2.0625 L 0.921875 -1.984375 C 1.265625 -1.984375 1.390625 -1.96875 1.53125 -1.921875 C 1.9375 -1.796875 2.15625 -1.5 2.15625 -1.046875 C 2.15625 -0.546875 1.8125 -0.125 1.375 -0.125 C 1.203125 -0.125 1.078125 -0.15625 0.859375 -0.3125 C 0.6875 -0.4375 0.59375 -0.46875 0.484375 -0.46875 C 0.359375 -0.46875 0.25 -0.390625 0.25 -0.265625 C 0.25 -0.046875 0.484375 0.078125 0.921875 0.078125 C 1.453125 0.078125 2.015625 -0.09375 2.3125 -0.46875 C 2.484375 -0.703125 2.59375 -1 2.59375 -1.3125 C 2.59375 -1.625 2.484375 -1.90625 2.328125 -2.09375 C 2.203125 -2.21875 2.09375 -2.296875 1.828125 -2.40625 C 2.21875 -2.640625 2.375 -2.953125 2.375 -3.234375 C 2.375 -3.71875 2 -4.0625 1.453125 -4.0625 C 0.828125 -4.0625 0.4375 -3.65625 0.265625 -3.078125 Z M 0.359375 -3.0625 "/>
</symbol>
<symbol overflow="visible" id="glyph2-3">
<path style="stroke:none;" d="M 2.6875 -3.875 L 2.6875 -3.96875 L 0.484375 -3.96875 L 0.125 -3.09375 L 0.234375 -3.03125 C 0.484375 -3.4375 0.578125 -3.53125 0.9375 -3.53125 L 2.21875 -3.53125 L 1.03125 0.046875 L 1.421875 0.046875 Z M 2.6875 -3.875 "/>
</symbol>
<symbol overflow="visible" id="glyph2-4">
<path style="stroke:none;" d="M 2.359375 0 L 2.359375 -0.09375 C 1.90625 -0.09375 1.796875 -0.203125 1.796875 -0.453125 L 1.796875 -4.03125 L 1.734375 -4.0625 L 0.671875 -3.515625 L 0.671875 -3.421875 L 0.828125 -3.484375 C 0.9375 -3.53125 1.03125 -3.5625 1.09375 -3.5625 C 1.21875 -3.5625 1.28125 -3.46875 1.28125 -3.265625 L 1.28125 -0.5625 C 1.28125 -0.234375 1.15625 -0.109375 0.703125 -0.09375 L 0.703125 0 Z M 2.359375 0 "/>
</symbol>
<symbol overflow="visible" id="glyph2-5">
<path style="stroke:none;" d="M 2.625 -4.09375 L 2.578125 -4.125 C 2.484375 -4 2.421875 -3.96875 2.28125 -3.96875 L 1.046875 -3.96875 L 0.390625 -2.546875 C 0.390625 -2.546875 0.390625 -2.53125 0.390625 -2.515625 C 0.390625 -2.484375 0.40625 -2.46875 0.453125 -2.46875 C 1.03125 -2.46875 1.453125 -2.28125 1.734375 -2.046875 C 2 -1.84375 2.140625 -1.53125 2.140625 -1.15625 C 2.140625 -0.640625 1.75 -0.140625 1.328125 -0.140625 C 1.203125 -0.140625 1.0625 -0.1875 0.90625 -0.328125 C 0.703125 -0.484375 0.59375 -0.515625 0.453125 -0.515625 C 0.28125 -0.515625 0.1875 -0.4375 0.1875 -0.28125 C 0.1875 -0.0625 0.5 0.078125 0.953125 0.078125 C 1.359375 0.078125 1.6875 0 1.96875 -0.203125 C 2.375 -0.515625 2.5625 -0.875 2.5625 -1.453125 C 2.5625 -1.78125 2.5 -2 2.34375 -2.21875 C 2 -2.6875 1.703125 -2.828125 0.84375 -2.984375 L 1.09375 -3.5 L 2.25 -3.5 C 2.34375 -3.5 2.390625 -3.53125 2.40625 -3.578125 Z M 2.625 -4.09375 "/>
</symbol>
<symbol overflow="visible" id="glyph2-6">
<path style="stroke:none;" d="M 2.84375 -1 L 2.84375 -1.390625 L 2.21875 -1.390625 L 2.21875 -4.0625 L 1.953125 -4.0625 L 0.078125 -1.390625 L 0.078125 -1 L 1.75 -1 L 1.75 0 L 2.21875 0 L 2.21875 -1 Z M 1.75 -1.390625 L 0.3125 -1.390625 L 1.75 -3.4375 Z M 1.75 -1.390625 "/>
</symbol>
<symbol overflow="visible" id="glyph2-7">
<path style="stroke:none;" d="M 0.359375 0.125 C 1.140625 0.03125 1.53125 -0.125 2.03125 -0.578125 C 2.515625 -1.015625 2.75 -1.671875 2.75 -2.359375 C 2.75 -2.875 2.609375 -3.296875 2.375 -3.59375 C 2.140625 -3.890625 1.8125 -4.0625 1.421875 -4.0625 C 0.71875 -4.0625 0.1875 -3.453125 0.1875 -2.640625 C 0.1875 -1.90625 0.609375 -1.421875 1.265625 -1.421875 C 1.609375 -1.421875 1.90625 -1.515625 2.15625 -1.765625 C 1.921875 -0.78125 1.25 -0.15625 0.34375 0.015625 Z M 2.171875 -2.125 C 2.171875 -1.8125 1.734375 -1.6875 1.46875 -1.6875 C 1.015625 -1.6875 0.734375 -2.125 0.734375 -2.84375 C 0.734375 -3.171875 0.828125 -3.515625 0.9375 -3.6875 C 1.046875 -3.8125 1.203125 -3.890625 1.375 -3.890625 C 1.90625 -3.890625 2.171875 -3.375 2.171875 -2.359375 Z M 2.171875 -2.125 "/>
</symbol>
<symbol overflow="visible" id="glyph3-0">
<path style="stroke:none;" d=""/>
</symbol>
<symbol overflow="visible" id="glyph3-1">
<path style="stroke:none;" d="M 8.25 -4.25 L 4.046875 -4.25 L 4.046875 0.796875 L 4.65625 0.796875 L 4.65625 0.40625 L 7.625 0.40625 L 7.625 0.796875 L 8.25 0.796875 Z M 4.65625 -0.1875 L 4.65625 -1.640625 L 5.859375 -1.640625 L 5.859375 -0.1875 Z M 6.4375 -0.1875 L 6.4375 -1.640625 L 7.625 -1.640625 L 7.625 -0.1875 Z M 4.65625 -2.21875 L 4.65625 -3.65625 L 5.859375 -3.65625 L 5.859375 -2.21875 Z M 6.4375 -3.65625 L 7.625 -3.65625 L 7.625 -2.21875 L 6.4375 -2.21875 Z M 4.828125 -7.25 L 4.828125 -6.265625 L 3.703125 -6.265625 L 3.703125 -5.65625 L 4.828125 -5.65625 L 4.828125 -4.703125 L 5.4375 -4.703125 L 5.4375 -5.65625 L 6.78125 -5.65625 L 6.78125 -4.703125 L 7.390625 -4.703125 L 7.390625 -5.65625 L 8.609375 -5.65625 L 8.609375 -6.265625 L 7.390625 -6.265625 L 7.390625 -7.25 L 6.78125 -7.25 L 6.78125 -6.265625 L 5.4375 -6.265625 L 5.4375 -7.25 Z M 1.5 -7.328125 C 1.296875 -6.25 0.953125 -5.28125 0.484375 -4.390625 L 1.015625 -4.0625 C 1.265625 -4.53125 1.5 -5.046875 1.703125 -5.625 L 3.421875 -5.625 L 3.421875 -6.25 L 1.890625 -6.25 C 1.984375 -6.546875 2.0625 -6.859375 2.140625 -7.203125 Z M 1.375 -4.53125 L 1.375 -3.953125 L 1.875 -3.953125 L 1.875 -2.828125 L 0.703125 -2.828125 L 0.703125 -2.234375 L 1.875 -2.234375 L 1.875 -0.1875 C 1.875 -0.046875 1.796875 0.046875 1.640625 0.109375 L 1.921875 0.671875 C 2.59375 0.453125 3.1875 0.171875 3.71875 -0.15625 L 3.59375 -0.734375 C 3.234375 -0.484375 2.875 -0.28125 2.515625 -0.125 L 2.515625 -2.234375 L 3.640625 -2.234375 L 3.640625 -2.828125 L 2.515625 -2.828125 L 2.515625 -3.953125 L 3.421875 -3.953125 L 3.421875 -4.53125 Z M 1.375 -4.53125 "/>
</symbol>
<symbol overflow="visible" id="glyph3-2">
<path style="stroke:none;" d="M 2.28125 -3.734375 C 2.5 -3.375 2.765625 -2.921875 3.0625 -2.359375 L 3.421875 -2.875 C 3.03125 -3.453125 2.65625 -3.96875 2.28125 -4.421875 L 2.28125 -4.96875 L 3.234375 -4.96875 L 3.234375 -5.59375 L 2.28125 -5.59375 L 2.28125 -7.359375 L 1.640625 -7.359375 L 1.640625 -5.59375 L 0.5 -5.59375 L 0.5 -4.96875 L 1.625 -4.96875 C 1.375 -3.8125 0.921875 -2.78125 0.3125 -1.875 L 0.59375 -1.171875 C 1.03125 -1.890625 1.375 -2.6875 1.640625 -3.546875 L 1.640625 0.875 L 2.28125 0.875 Z M 4.9375 -5.53125 L 4.9375 -4.953125 L 6.265625 -4.953125 L 6.265625 -3.484375 L 5.125 -3.484375 L 5.125 -2.90625 L 6.265625 -2.90625 L 6.265625 -1.296875 L 4.828125 -1.296875 L 4.828125 -0.71875 L 8.359375 -0.71875 L 8.359375 -1.296875 L 6.875 -1.296875 L 6.875 -2.90625 L 8.0625 -2.90625 L 8.0625 -3.484375 L 6.875 -3.484375 L 6.875 -4.953125 L 8.203125 -4.953125 L 8.203125 -5.53125 Z M 3.734375 -7.046875 L 3.734375 0.6875 L 8.65625 0.6875 L 8.65625 0.078125 L 4.359375 0.078125 L 4.359375 -6.4375 L 8.484375 -6.4375 L 8.484375 -7.046875 Z M 3.734375 -7.046875 "/>
</symbol>
<symbol overflow="visible" id="glyph3-3">
<path style="stroke:none;" d="M 1.140625 -6.640625 L 1.140625 -6.046875 L 4.171875 -6.046875 L 4.171875 -5.265625 L 0.765625 -5.265625 L 0.765625 -3.625 L 1.390625 -3.625 L 1.390625 -4.703125 L 4.078125 -4.703125 C 3.5625 -4.25 3 -3.859375 2.40625 -3.515625 C 2.28125 -3.453125 2.140625 -3.390625 2 -3.359375 L 2.140625 -2.875 C 2.921875 -2.890625 3.609375 -2.921875 4.21875 -2.9375 C 3.421875 -2.484375 2.671875 -2.15625 1.9375 -1.953125 C 1.75 -1.953125 1.5625 -1.96875 1.375 -1.96875 L 1.5 -1.390625 C 2.328125 -1.40625 3.234375 -1.4375 4.234375 -1.484375 L 4.234375 0.046875 C 4.234375 0.234375 4.125 0.328125 3.921875 0.328125 C 3.6875 0.328125 3.4375 0.3125 3.1875 0.28125 L 3.328125 0.890625 L 4.140625 0.890625 C 4.625 0.890625 4.875 0.65625 4.875 0.203125 L 4.875 -1.515625 C 5.53125 -1.546875 6.203125 -1.59375 6.9375 -1.640625 C 7.125 -1.421875 7.28125 -1.203125 7.421875 -0.984375 L 7.90625 -1.34375 C 7.5625 -1.828125 7.0625 -2.390625 6.40625 -3.03125 L 5.9375 -2.734375 C 6.140625 -2.515625 6.359375 -2.296875 6.546875 -2.09375 C 5.375 -2 4.21875 -1.9375 3.046875 -1.90625 C 4.28125 -2.34375 5.40625 -2.9375 6.46875 -3.734375 L 5.953125 -4.09375 C 5.625 -3.859375 5.296875 -3.625 4.984375 -3.40625 C 4.484375 -3.375 3.8125 -3.359375 2.984375 -3.34375 C 3.5625 -3.65625 4.125 -4.015625 4.65625 -4.453125 L 4.171875 -4.703125 L 7.609375 -4.703125 L 7.609375 -3.625 L 8.234375 -3.625 L 8.234375 -5.265625 L 4.828125 -5.265625 L 4.828125 -6.046875 L 7.859375 -6.046875 L 7.859375 -6.640625 L 4.828125 -6.640625 L 4.828125 -7.359375 L 4.171875 -7.359375 L 4.171875 -6.640625 Z M 5.828125 -1.03125 L 5.515625 -0.5625 C 6.375 -0.25 7.21875 0.1875 8.046875 0.734375 L 8.375 0.203125 C 7.609375 -0.28125 6.765625 -0.6875 5.828125 -1.03125 Z M 3 -1.046875 C 2.359375 -0.5625 1.546875 -0.171875 0.5625 0.125 L 0.84375 0.703125 C 1.859375 0.34375 2.703125 -0.09375 3.375 -0.625 Z M 3 -1.046875 "/>
</symbol>
<symbol overflow="visible" id="glyph3-4">
<path style="stroke:none;" d="M 6.953125 -7.359375 L 6.953125 0.90625 L 7.609375 0.90625 L 7.609375 -7.359375 Z M 1.671875 -4.390625 L 4.765625 -4.390625 L 4.765625 -7 L 0.84375 -7 L 0.84375 -6.40625 L 4.125 -6.40625 L 4.125 -5 L 1.125 -5 L 0.65625 -2.3125 L 4.203125 -2.3125 C 4.203125 -1.265625 4.15625 -0.5625 4.046875 -0.234375 C 3.9375 0.078125 3.53125 0.25 2.859375 0.25 C 2.5625 0.25 2.28125 0.21875 1.984375 0.203125 L 2.15625 0.8125 C 2.4375 0.828125 2.71875 0.84375 3 0.84375 C 3.984375 0.8125 4.53125 0.546875 4.65625 0.03125 C 4.78125 -0.4375 4.84375 -1.40625 4.84375 -2.890625 L 1.34375 -2.890625 Z M 1.671875 -4.390625 "/>
</symbol>
<symbol overflow="visible" id="glyph3-5">
<path style="stroke:none;" d="M 0.859375 -6.703125 L 0.859375 -6.125 L 4.1875 -6.125 C 4.15625 -5.9375 4.109375 -5.734375 4.0625 -5.53125 L 1.859375 -5.53125 L 1.859375 -1.296875 L 0.515625 -1.296875 L 0.515625 -0.71875 L 3.171875 -0.71875 C 2.53125 -0.265625 1.734375 0.09375 0.734375 0.375 L 1.015625 0.921875 C 2.03125 0.609375 2.875 0.203125 3.5625 -0.28125 L 3.1875 -0.71875 L 5.765625 -0.71875 L 5.46875 -0.28125 C 6.34375 0.015625 7.1875 0.40625 8.015625 0.9375 L 8.359375 0.421875 C 7.59375 -0.015625 6.734375 -0.390625 5.796875 -0.71875 L 8.484375 -0.71875 L 8.484375 -1.296875 L 7.296875 -1.296875 L 7.296875 -5.53125 L 4.71875 -5.53125 C 4.765625 -5.734375 4.8125 -5.9375 4.84375 -6.125 L 8.140625 -6.125 L 8.140625 -6.703125 L 4.953125 -6.703125 C 4.984375 -6.890625 5 -7.09375 5.015625 -7.296875 L 4.359375 -7.375 L 4.28125 -6.703125 Z M 2.46875 -1.296875 L 2.46875 -1.875 L 6.671875 -1.875 L 6.671875 -1.296875 Z M 2.46875 -2.359375 L 2.46875 -2.921875 L 6.671875 -2.921875 L 6.671875 -2.359375 Z M 2.46875 -3.40625 L 2.46875 -3.953125 L 6.671875 -3.953125 L 6.671875 -3.40625 Z M 2.46875 -4.453125 L 2.46875 -5.015625 L 6.671875 -5.015625 L 6.671875 -4.453125 Z M 2.46875 -4.453125 "/>
</symbol>
<symbol overflow="visible" id="glyph3-6">
<path style="stroke:none;" d="M 4.546875 -0.90625 C 5.75 -0.4375 6.921875 0.1875 8.078125 0.953125 L 8.421875 0.390625 C 7.359375 -0.28125 6.1875 -0.875 4.90625 -1.375 C 4.984375 -1.5 5.0625 -1.640625 5.140625 -1.78125 L 8.3125 -1.78125 L 8.3125 -2.390625 L 5.421875 -2.390625 C 5.609375 -2.953125 5.71875 -3.59375 5.75 -4.296875 L 5.75 -5.359375 L 5.125 -5.359375 L 5.125 -4.296875 C 5.09375 -3.59375 4.96875 -2.953125 4.734375 -2.390625 L 0.703125 -2.390625 L 0.703125 -1.78125 L 4.421875 -1.78125 C 4.328125 -1.640625 4.234375 -1.5 4.125 -1.375 C 3.46875 -0.65625 2.296875 -0.078125 0.625 0.359375 L 0.984375 0.90625 C 2.625 0.484375 3.8125 -0.125 4.546875 -0.90625 Z M 1.90625 -4.125 L 1.578125 -3.640625 C 2.1875 -3.40625 2.796875 -3.0625 3.359375 -2.625 L 3.703125 -3.140625 C 3.15625 -3.515625 2.5625 -3.859375 1.90625 -4.125 Z M 2.703125 -5.421875 L 2.375 -4.9375 C 3 -4.6875 3.625 -4.328125 4.234375 -3.875 L 4.5625 -4.390625 C 4 -4.796875 3.390625 -5.140625 2.703125 -5.421875 Z M 8.15625 -6.46875 L 4.890625 -6.46875 C 4.78125 -6.828125 4.65625 -7.140625 4.546875 -7.40625 L 3.875 -7.296875 C 4.015625 -7.046875 4.140625 -6.78125 4.25 -6.46875 L 0.828125 -6.46875 L 0.828125 -4.75 L 1.453125 -4.75 L 1.453125 -5.875 L 7.53125 -5.875 L 7.53125 -4.75 L 8.15625 -4.75 Z M 8.15625 -6.46875 "/>
</symbol>
<symbol overflow="visible" id="glyph3-7">
<path style="stroke:none;" d="M 4.90625 -7.34375 L 4.90625 -5.90625 L 3.1875 -5.90625 L 3.1875 -5.296875 L 4.890625 -5.296875 C 4.828125 -3.390625 4.1875 -1.984375 3 -1.09375 L 3.484375 -0.65625 C 4.78125 -1.640625 5.46875 -3.1875 5.546875 -5.296875 L 7.515625 -5.296875 C 7.484375 -3.5 7.40625 -2.390625 7.28125 -1.984375 C 7.140625 -1.546875 6.796875 -1.328125 6.25 -1.328125 C 6.078125 -1.328125 5.875 -1.34375 5.609375 -1.359375 L 5.78125 -0.75 C 6.015625 -0.734375 6.234375 -0.71875 6.40625 -0.71875 C 7.046875 -0.71875 7.5 -0.96875 7.765625 -1.453125 C 8.015625 -1.9375 8.140625 -3.421875 8.15625 -5.90625 L 5.5625 -5.90625 L 5.5625 -7.34375 Z M 1.109375 -7.203125 L 0.65625 -6.78125 C 1.3125 -6.265625 1.8125 -5.796875 2.171875 -5.34375 L 2.640625 -5.828125 C 2.234375 -6.28125 1.71875 -6.734375 1.109375 -7.203125 Z M 6.578125 0.734375 L 8.484375 0.734375 L 8.640625 0.078125 C 8.421875 0.09375 8.109375 0.109375 7.703125 0.109375 C 7.296875 0.109375 6.890625 0.125 6.5 0.125 C 5.765625 0.125 5.140625 0.109375 4.640625 0.109375 C 4.09375 0.09375 3.65625 0.03125 3.359375 -0.09375 C 3.109375 -0.21875 2.859375 -0.40625 2.625 -0.65625 C 2.546875 -0.75 2.46875 -0.8125 2.390625 -0.859375 L 2.390625 -4.25 L 0.484375 -4.25 L 0.484375 -3.625 L 1.765625 -3.625 L 1.765625 -0.90625 C 1.390625 -0.734375 0.96875 -0.265625 0.484375 0.5 L 0.96875 0.9375 C 1.46875 0.09375 1.84375 -0.328125 2.0625 -0.328125 C 2.171875 -0.328125 2.296875 -0.25 2.4375 -0.09375 C 2.71875 0.21875 3.046875 0.453125 3.421875 0.5625 C 3.796875 0.65625 4.296875 0.71875 4.90625 0.71875 C 5.515625 0.734375 6.0625 0.734375 6.578125 0.734375 Z M 6.578125 0.734375 "/>
</symbol>
<symbol overflow="visible" id="glyph3-8">
<path style="stroke:none;" d="M 1.40625 -7 L 1.40625 -3.421875 L 3.09375 -3.421875 C 2.5 -2.796875 1.578125 -2.265625 0.328125 -1.859375 L 0.671875 -1.28125 C 2.109375 -1.859375 3.140625 -2.5625 3.765625 -3.421875 L 5.21875 -3.421875 C 5.4375 -3.09375 5.734375 -2.765625 6.125 -2.46875 L 5.59375 -2.46875 L 5.59375 0.921875 L 6.234375 0.921875 L 6.234375 -2.359375 C 6.796875 -1.921875 7.515625 -1.53125 8.390625 -1.21875 L 8.671875 -1.78125 C 7.421875 -2.1875 6.484375 -2.734375 5.890625 -3.421875 L 7.625 -3.421875 L 7.625 -7 Z M 7 -4 L 4.8125 -4 L 4.8125 -4.953125 L 7 -4.953125 Z M 4.1875 -4 L 2.03125 -4 L 2.03125 -4.953125 L 4.1875 -4.953125 Z M 2.03125 -5.5 L 2.03125 -6.421875 L 4.1875 -6.421875 L 4.1875 -5.5 Z M 4.8125 -6.421875 L 7 -6.421875 L 7 -5.5 L 4.8125 -5.5 Z M 2.90625 -2.46875 C 2.890625 -1.8125 2.796875 -1.28125 2.59375 -0.84375 C 2.328125 -0.34375 1.765625 0.078125 0.96875 0.40625 L 1.390625 0.921875 C 2.34375 0.515625 2.96875 -0.015625 3.234375 -0.671875 C 3.453125 -1.15625 3.5625 -1.765625 3.5625 -2.46875 Z M 2.90625 -2.46875 "/>
</symbol>
</g>
</defs>
<g id="surface1">
<rect x="0" y="0" width="373" height="246" style="fill:rgb(100%,100%,100%);fill-opacity:1;stroke:none;"/>
<rect x="0" y="0" width="373" height="246" style="fill:rgb(100%,100%,100%);fill-opacity:1;stroke:none;"/>
<g style="fill:rgb(0%,0%,0%);fill-opacity:1;">
  <use xlink:href="#glyph0-1" x="51.65198" y="169.3168"/>
</g>
<g style="fill:rgb(0%,0%,0%);fill-opacity:1;">
  <use xlink:href="#glyph0-2" x="51.65198" y="191.6261"/>
</g>
<g style="fill:rgb(0%,0%,0%);fill-opacity:1;">
  <use xlink:href="#glyph0-3" x="51.65198" y="213.9354"/>
</g>
<g style="fill:rgb(0%,0%,0%);fill-opacity:1;">
  <use xlink:href="#glyph0-4" x="51.65198" y="236.2448"/>
</g>
<g style="fill:rgb(0%,0%,0%);fill-opacity:1;">
  <use xlink:href="#glyph0-5" x="51.65198" y="124.69812"/>
</g>
<g style="fill:rgb(0%,0%,0%);fill-opacity:1;">
  <use xlink:href="#glyph0-6" x="51.65198" y="147.0074"/>
</g>
<g style="fill:rgb(0%,0%,0%);fill-opacity:1;">
  <use xlink:href="#glyph0-7" x="51.65198" y="57.77012"/>
</g>
<g style="fill:rgb(0%,0%,0%);fill-opacity:1;">
  <use xlink:href="#glyph0-8" x="51.65198" y="102.38878"/>
</g>
<g style="fill:rgb(0%,0%,0%);fill-opacity:1;">
  <use xlink:href="#glyph0-9" x="51.65198" y="80.07945"/>
</g>
<path style=" stroke:none;fill-rule:nonzero;fill:rgb(69.804382%,85.098267%,100%);fill-opacity:1;" d="M 132.238281 155.308594 L 154.546875 155.308594 L 154.546875 133 L 132.238281 133 Z M 132.238281 155.308594 "/>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 119.238281 95 L 141.546875 95 L 141.546875 117.308594 L 119.238281 117.308594 Z M 119.238281 95 " transform="matrix(1,0,0,1,13,38)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 96.929688 50.382812 L 119.238281 50.382812 L 119.238281 72.691406 L 96.929688 72.691406 Z M 96.929688 50.382812 " transform="matrix(1,0,0,1,13,38)"/>
<path style=" stroke:none;fill-rule:nonzero;fill:rgb(69.804382%,85.098267%,100%);fill-opacity:1;" d="M 132.238281 244.546875 L 154.546875 244.546875 L 154.546875 222.238281 L 132.238281 222.238281 Z M 132.238281 244.546875 "/>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 119.238281 184.238281 L 141.546875 184.238281 L 141.546875 206.546875 L 119.238281 206.546875 Z M 119.238281 184.238281 " transform="matrix(1,0,0,1,13,38)"/>
<g style="fill:rgb(0%,0%,0%);fill-opacity:1;">
  <use xlink:href="#glyph0-5" x="140.8893" y="35.460783"/>
</g>
<path style=" stroke:none;fill-rule:nonzero;fill:rgb(69.804382%,85.098267%,100%);fill-opacity:1;" d="M 132.238281 177.617188 L 154.546875 177.617188 L 154.546875 155.308594 L 132.238281 155.308594 Z M 132.238281 177.617188 "/>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 119.238281 117.308594 L 141.546875 117.308594 L 141.546875 139.617188 L 119.238281 139.617188 Z M 119.238281 117.308594 " transform="matrix(1,0,0,1,13,38)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 96.929688 28.070312 L 119.238281 28.070312 L 119.238281 50.382812 L 96.929688 50.382812 Z M 96.929688 28.070312 " transform="matrix(1,0,0,1,13,38)"/>
<g style="fill:rgb(0%,0%,0%);fill-opacity:1;">
  <use xlink:href="#glyph1-1" x="116.0847" y="78.72667"/>
</g>
<g style="fill:rgb(0%,0%,0%);fill-opacity:1;">
  <use xlink:href="#glyph2-1" x="120.0807" y="81.72667"/>
  <use xlink:href="#glyph2-2" x="123.0807" y="81.72667"/>
</g>
<path style=" stroke:none;fill-rule:nonzero;fill:rgb(69.804382%,85.098267%,100%);fill-opacity:1;" d="M 87.617188 110.691406 L 109.929688 110.691406 L 109.929688 88.382812 L 87.617188 88.382812 Z M 87.617188 110.691406 "/>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 74.617188 50.382812 L 96.929688 50.382812 L 96.929688 72.691406 L 74.617188 72.691406 Z M 74.617188 50.382812 " transform="matrix(1,0,0,1,13,38)"/>
<path style=" stroke:none;fill-rule:nonzero;fill:rgb(69.804382%,85.098267%,100%);fill-opacity:1;" d="M 65.308594 177.617188 L 87.617188 177.617188 L 87.617188 155.308594 L 65.308594 155.308594 Z M 65.308594 177.617188 "/>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 52.308594 117.308594 L 74.617188 117.308594 L 74.617188 139.617188 L 52.308594 139.617188 Z M 52.308594 117.308594 " transform="matrix(1,0,0,1,13,38)"/>
<path style=" stroke:none;fill-rule:nonzero;fill:rgb(69.804382%,85.098267%,100%);fill-opacity:1;" d="M 65.308594 199.929688 L 87.617188 199.929688 L 87.617188 177.617188 L 65.308594 177.617188 Z M 65.308594 199.929688 "/>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 52.308594 139.617188 L 74.617188 139.617188 L 74.617188 161.929688 L 52.308594 161.929688 Z M 52.308594 139.617188 " transform="matrix(1,0,0,1,13,38)"/>
<g style="fill:rgb(0%,0%,0%);fill-opacity:1;">
  <use xlink:href="#glyph1-1" x="71.466" y="190.2733"/>
</g>
<g style="fill:rgb(0%,0%,0%);fill-opacity:1;">
  <use xlink:href="#glyph2-3" x="75.462" y="193.2733"/>
  <use xlink:href="#glyph2-4" x="78.462" y="193.2733"/>
</g>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 96.929688 184.238281 L 119.238281 184.238281 L 119.238281 206.546875 L 96.929688 206.546875 Z M 96.929688 184.238281 " transform="matrix(1,0,0,1,13,38)"/>
<path style=" stroke:none;fill-rule:nonzero;fill:rgb(69.804382%,85.098267%,100%);fill-opacity:1;" d="M 132.238281 133 L 154.546875 133 L 154.546875 110.691406 L 132.238281 110.691406 Z M 132.238281 133 "/>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 119.238281 72.691406 L 141.546875 72.691406 L 141.546875 95 L 119.238281 95 Z M 119.238281 72.691406 " transform="matrix(1,0,0,1,13,38)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 96.929688 95 L 119.238281 95 L 119.238281 117.308594 L 96.929688 117.308594 Z M 96.929688 95 " transform="matrix(1,0,0,1,13,38)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 96.929688 117.308594 L 119.238281 117.308594 L 119.238281 139.617188 L 96.929688 139.617188 Z M 96.929688 117.308594 " transform="matrix(1,0,0,1,13,38)"/>
<path style=" stroke:none;fill-rule:nonzero;fill:rgb(69.804382%,85.098267%,100%);fill-opacity:1;" d="M 132.238281 110.691406 L 154.546875 110.691406 L 154.546875 88.382812 L 132.238281 88.382812 Z M 132.238281 110.691406 "/>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 119.238281 50.382812 L 141.546875 50.382812 L 141.546875 72.691406 L 119.238281 72.691406 Z M 119.238281 50.382812 " transform="matrix(1,0,0,1,13,38)"/>
<path style=" stroke:none;fill-rule:nonzero;fill:rgb(69.804382%,85.098267%,100%);fill-opacity:1;" d="M 87.617188 177.617188 L 109.929688 177.617188 L 109.929688 155.308594 L 87.617188 155.308594 Z M 87.617188 177.617188 "/>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 74.617188 117.308594 L 96.929688 117.308594 L 96.929688 139.617188 L 74.617188 139.617188 Z M 74.617188 117.308594 " transform="matrix(1,0,0,1,13,38)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 74.617188 28.070312 L 96.929688 28.070312 L 96.929688 50.382812 L 74.617188 50.382812 Z M 74.617188 28.070312 " transform="matrix(1,0,0,1,13,38)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 96.929688 72.691406 L 119.238281 72.691406 L 119.238281 95 L 96.929688 95 Z M 96.929688 72.691406 " transform="matrix(1,0,0,1,13,38)"/>
<path style=" stroke:none;fill-rule:nonzero;fill:rgb(69.804382%,85.098267%,100%);fill-opacity:1;" d="M 87.617188 133 L 109.929688 133 L 109.929688 110.691406 L 87.617188 110.691406 Z M 87.617188 133 "/>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 74.617188 72.691406 L 96.929688 72.691406 L 96.929688 95 L 74.617188 95 Z M 74.617188 72.691406 " transform="matrix(1,0,0,1,13,38)"/>
<path style=" stroke:none;fill-rule:nonzero;fill:rgb(69.804382%,85.098267%,100%);fill-opacity:1;" d="M 65.308594 222.238281 L 87.617188 222.238281 L 87.617188 199.929688 L 65.308594 199.929688 Z M 65.308594 222.238281 "/>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 52.308594 161.929688 L 74.617188 161.929688 L 74.617188 184.238281 L 52.308594 184.238281 Z M 52.308594 161.929688 " transform="matrix(1,0,0,1,13,38)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 74.617188 5.761719 L 96.929688 5.761719 L 96.929688 28.070312 L 74.617188 28.070312 Z M 74.617188 5.761719 " transform="matrix(1,0,0,1,13,38)"/>
<path style=" stroke:none;fill-rule:nonzero;fill:rgb(69.804382%,85.098267%,100%);fill-opacity:1;" d="M 87.617188 222.238281 L 109.929688 222.238281 L 109.929688 199.929688 L 87.617188 199.929688 Z M 87.617188 222.238281 "/>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 74.617188 161.929688 L 96.929688 161.929688 L 96.929688 184.238281 L 74.617188 184.238281 Z M 74.617188 161.929688 " transform="matrix(1,0,0,1,13,38)"/>
<g style="fill:rgb(0%,0%,0%);fill-opacity:1;">
  <use xlink:href="#glyph0-8" x="118.58" y="35.460783"/>
</g>
<path style=" stroke:none;fill-rule:nonzero;fill:rgb(69.804382%,85.098267%,100%);fill-opacity:1;" d="M 65.308594 244.546875 L 87.617188 244.546875 L 87.617188 222.238281 L 65.308594 222.238281 Z M 65.308594 244.546875 "/>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 52.308594 184.238281 L 74.617188 184.238281 L 74.617188 206.546875 L 52.308594 206.546875 Z M 52.308594 184.238281 " transform="matrix(1,0,0,1,13,38)"/>
<path style=" stroke:none;fill-rule:nonzero;fill:rgb(69.804382%,85.098267%,100%);fill-opacity:1;" d="M 65.308594 133 L 87.617188 133 L 87.617188 110.691406 L 65.308594 110.691406 Z M 65.308594 133 "/>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 52.308594 72.691406 L 74.617188 72.691406 L 74.617188 95 L 52.308594 95 Z M 52.308594 72.691406 " transform="matrix(1,0,0,1,13,38)"/>
<path style=" stroke:none;fill-rule:nonzero;fill:rgb(69.804382%,85.098267%,100%);fill-opacity:1;" d="M 87.617188 199.929688 L 109.929688 199.929688 L 109.929688 177.617188 L 87.617188 177.617188 Z M 87.617188 199.929688 "/>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 74.617188 139.617188 L 96.929688 139.617188 L 96.929688 161.929688 L 74.617188 161.929688 Z M 74.617188 139.617188 " transform="matrix(1,0,0,1,13,38)"/>
<path style=" stroke:none;fill-rule:nonzero;fill:rgb(69.804382%,85.098267%,100%);fill-opacity:1;" d="M 132.238281 199.929688 L 154.546875 199.929688 L 154.546875 177.617188 L 132.238281 177.617188 Z M 132.238281 199.929688 "/>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 119.238281 139.617188 L 141.546875 139.617188 L 141.546875 161.929688 L 119.238281 161.929688 Z M 119.238281 139.617188 " transform="matrix(1,0,0,1,13,38)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 96.929688 139.617188 L 119.238281 139.617188 L 119.238281 161.929688 L 96.929688 161.929688 Z M 96.929688 139.617188 " transform="matrix(1,0,0,1,13,38)"/>
<path style=" stroke:none;fill-rule:nonzero;fill:rgb(69.804382%,85.098267%,100%);fill-opacity:1;" d="M 132.238281 66.070312 L 154.546875 66.070312 L 154.546875 43.761719 L 132.238281 43.761719 Z M 132.238281 66.070312 "/>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 119.238281 5.761719 L 141.546875 5.761719 L 141.546875 28.070312 L 119.238281 28.070312 Z M 119.238281 5.761719 " transform="matrix(1,0,0,1,13,38)"/>
<path style=" stroke:none;fill-rule:nonzero;fill:rgb(69.804382%,85.098267%,100%);fill-opacity:1;" d="M 65.308594 155.308594 L 87.617188 155.308594 L 87.617188 133 L 65.308594 133 Z M 65.308594 155.308594 "/>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 52.308594 95 L 74.617188 95 L 74.617188 117.308594 L 52.308594 117.308594 Z M 52.308594 95 " transform="matrix(1,0,0,1,13,38)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 96.929688 161.929688 L 119.238281 161.929688 L 119.238281 184.238281 L 96.929688 184.238281 Z M 96.929688 161.929688 " transform="matrix(1,0,0,1,13,38)"/>
<path style=" stroke:none;fill-rule:nonzero;fill:rgb(69.804382%,85.098267%,100%);fill-opacity:1;" d="M 87.617188 155.308594 L 109.929688 155.308594 L 109.929688 133 L 87.617188 133 Z M 87.617188 155.308594 "/>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 74.617188 95 L 96.929688 95 L 96.929688 117.308594 L 74.617188 117.308594 Z M 74.617188 95 " transform="matrix(1,0,0,1,13,38)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 96.929688 5.761719 L 119.238281 5.761719 L 119.238281 28.070312 L 96.929688 28.070312 Z M 96.929688 5.761719 " transform="matrix(1,0,0,1,13,38)"/>
<path style=" stroke:none;fill-rule:nonzero;fill:rgb(69.804382%,85.098267%,100%);fill-opacity:1;" d="M 87.617188 244.546875 L 109.929688 244.546875 L 109.929688 222.238281 L 87.617188 222.238281 Z M 87.617188 244.546875 "/>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 74.617188 184.238281 L 96.929688 184.238281 L 96.929688 206.546875 L 74.617188 206.546875 Z M 74.617188 184.238281 " transform="matrix(1,0,0,1,13,38)"/>
<g style="fill:rgb(0%,0%,0%);fill-opacity:1;">
  <use xlink:href="#glyph0-7" x="73.96131" y="35.460783"/>
</g>
<g style="fill:rgb(0%,0%,0%);fill-opacity:1;">
  <use xlink:href="#glyph0-9" x="96.27065" y="35.460783"/>
</g>
<path style=" stroke:none;fill-rule:nonzero;fill:rgb(69.804382%,85.098267%,100%);fill-opacity:1;" d="M 65.308594 66.070312 L 87.617188 66.070312 L 87.617188 43.761719 L 65.308594 43.761719 Z M 65.308594 66.070312 "/>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 52.308594 5.761719 L 74.617188 5.761719 L 74.617188 28.070312 L 52.308594 28.070312 Z M 52.308594 5.761719 " transform="matrix(1,0,0,1,13,38)"/>
<path style=" stroke:none;fill-rule:nonzero;fill:rgb(69.804382%,85.098267%,100%);fill-opacity:1;" d="M 65.308594 110.691406 L 87.617188 110.691406 L 87.617188 88.382812 L 65.308594 88.382812 Z M 65.308594 110.691406 "/>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 52.308594 50.382812 L 74.617188 50.382812 L 74.617188 72.691406 L 52.308594 72.691406 Z M 52.308594 50.382812 " transform="matrix(1,0,0,1,13,38)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 52.308594 28.070312 L 74.617188 28.070312 L 74.617188 50.382812 L 52.308594 50.382812 Z M 52.308594 28.070312 " transform="matrix(1,0,0,1,13,38)"/>
<path style=" stroke:none;fill-rule:nonzero;fill:rgb(69.804382%,85.098267%,100%);fill-opacity:1;" d="M 132.238281 222.238281 L 154.546875 222.238281 L 154.546875 199.929688 L 132.238281 199.929688 Z M 132.238281 222.238281 "/>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 119.238281 161.929688 L 141.546875 161.929688 L 141.546875 184.238281 L 119.238281 184.238281 Z M 119.238281 161.929688 " transform="matrix(1,0,0,1,13,38)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 119.238281 28.070312 L 141.546875 28.070312 L 141.546875 50.382812 L 119.238281 50.382812 Z M 119.238281 28.070312 " transform="matrix(1,0,0,1,13,38)"/>
<g style="fill:rgb(0%,0%,0%);fill-opacity:1;">
  <use xlink:href="#glyph3-1" x="5.5" y="127.5"/>
  <use xlink:href="#glyph3-2" x="14.5" y="127.5"/>
  <use xlink:href="#glyph3-3" x="23.5" y="127.5"/>
  <use xlink:href="#glyph3-4" x="32.5" y="127.5"/>
</g>
<g style="fill:rgb(0%,0%,0%);fill-opacity:1;">
  <use xlink:href="#glyph3-5" x="179.6062" y="15.45333"/>
  <use xlink:href="#glyph3-6" x="188.6062" y="15.45333"/>
  <use xlink:href="#glyph3-7" x="197.6062" y="15.45333"/>
  <use xlink:href="#glyph3-8" x="206.6062" y="15.45333"/>
  <use xlink:href="#glyph3-2" x="215.6062" y="15.45333"/>
  <use xlink:href="#glyph3-3" x="224.6062" y="15.45333"/>
  <use xlink:href="#glyph3-4" x="233.6062" y="15.45333"/>
</g>
<g style="fill:rgb(0%,0%,0%);fill-opacity:1;">
  <use xlink:href="#glyph0-8" x="227.3426" y="35.532783"/>
</g>
<g style="fill:rgb(0%,0%,0%);fill-opacity:1;">
  <use xlink:href="#glyph0-9" x="205.0333" y="35.532783"/>
</g>
<g style="fill:rgb(0%,0%,0%);fill-opacity:1;">
  <use xlink:href="#glyph0-5" x="249.652" y="35.532783"/>
</g>
<g style="fill:rgb(0%,0%,0%);fill-opacity:1;">
  <use xlink:href="#glyph0-7" x="182.724" y="35.532783"/>
</g>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 228 139.691406 L 250.308594 139.691406 L 250.308594 162 L 228 162 Z M 228 139.691406 " transform="matrix(1,0,0,1,13,38)"/>
<path style=" stroke:none;fill-rule:nonzero;fill:rgb(69.804382%,85.098267%,100%);fill-opacity:1;" d="M 241 222.308594 L 263.308594 222.308594 L 263.308594 200 L 241 200 Z M 241 222.308594 "/>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 228 162 L 250.308594 162 L 250.308594 184.308594 L 228 184.308594 Z M 228 162 " transform="matrix(1,0,0,1,13,38)"/>
<path style=" stroke:none;fill-rule:nonzero;fill:rgb(69.804382%,85.098267%,100%);fill-opacity:1;" d="M 196.382812 177.691406 L 218.691406 177.691406 L 218.691406 155.382812 L 196.382812 155.382812 Z M 196.382812 177.691406 "/>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 183.382812 117.382812 L 205.691406 117.382812 L 205.691406 139.691406 L 183.382812 139.691406 Z M 183.382812 117.382812 " transform="matrix(1,0,0,1,13,38)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 205.691406 117.382812 L 228 117.382812 L 228 139.691406 L 205.691406 139.691406 Z M 205.691406 117.382812 " transform="matrix(1,0,0,1,13,38)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 205.691406 162 L 228 162 L 228 184.308594 L 205.691406 184.308594 Z M 205.691406 162 " transform="matrix(1,0,0,1,13,38)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 183.382812 139.691406 L 205.691406 139.691406 L 205.691406 162 L 183.382812 162 Z M 183.382812 139.691406 " transform="matrix(1,0,0,1,13,38)"/>
<path style=" stroke:none;fill-rule:nonzero;fill:rgb(69.804382%,85.098267%,100%);fill-opacity:1;" d="M 241 110.761719 L 263.308594 110.761719 L 263.308594 88.453125 L 241 88.453125 Z M 241 110.761719 "/>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 228 50.453125 L 250.308594 50.453125 L 250.308594 72.761719 L 228 72.761719 Z M 228 50.453125 " transform="matrix(1,0,0,1,13,38)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 228 28.144531 L 250.308594 28.144531 L 250.308594 50.453125 L 228 50.453125 Z M 228 28.144531 " transform="matrix(1,0,0,1,13,38)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 205.691406 5.835938 L 228 5.835938 L 228 28.144531 L 205.691406 28.144531 Z M 205.691406 5.835938 " transform="matrix(1,0,0,1,13,38)"/>
<path style=" stroke:none;fill-rule:nonzero;fill:rgb(69.804382%,85.098267%,100%);fill-opacity:1;" d="M 196.382812 155.382812 L 218.691406 155.382812 L 218.691406 133.070312 L 196.382812 133.070312 Z M 196.382812 155.382812 "/>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 183.382812 95.070312 L 205.691406 95.070312 L 205.691406 117.382812 L 183.382812 117.382812 Z M 183.382812 95.070312 " transform="matrix(1,0,0,1,13,38)"/>
<path style=" stroke:none;fill-rule:nonzero;fill:rgb(69.804382%,85.098267%,100%);fill-opacity:1;" d="M 196.382812 222.308594 L 218.691406 222.308594 L 218.691406 200 L 196.382812 200 Z M 196.382812 222.308594 "/>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 183.382812 162 L 205.691406 162 L 205.691406 184.308594 L 183.382812 184.308594 Z M 183.382812 162 " transform="matrix(1,0,0,1,13,38)"/>
<path style=" stroke:none;fill-rule:nonzero;fill:rgb(69.804382%,85.098267%,100%);fill-opacity:1;" d="M 196.382812 244.617188 L 218.691406 244.617188 L 218.691406 222.308594 L 196.382812 222.308594 Z M 196.382812 244.617188 "/>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 183.382812 184.308594 L 205.691406 184.308594 L 205.691406 206.617188 L 183.382812 206.617188 Z M 183.382812 184.308594 " transform="matrix(1,0,0,1,13,38)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 205.691406 95.070312 L 228 95.070312 L 228 117.382812 L 205.691406 117.382812 Z M 205.691406 95.070312 " transform="matrix(1,0,0,1,13,38)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 161.070312 28.144531 L 183.382812 28.144531 L 183.382812 50.453125 L 161.070312 50.453125 Z M 161.070312 28.144531 " transform="matrix(1,0,0,1,13,38)"/>
<path style=" stroke:none;fill-rule:nonzero;fill:rgb(69.804382%,85.098267%,100%);fill-opacity:1;" d="M 241 155.382812 L 263.308594 155.382812 L 263.308594 133.070312 L 241 133.070312 Z M 241 155.382812 "/>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 228 95.070312 L 250.308594 95.070312 L 250.308594 117.382812 L 228 117.382812 Z M 228 95.070312 " transform="matrix(1,0,0,1,13,38)"/>
<g style="fill:rgb(0%,0%,0%);fill-opacity:1;">
  <use xlink:href="#glyph1-1" x="247.1567" y="145.7267"/>
</g>
<g style="fill:rgb(0%,0%,0%);fill-opacity:1;">
  <use xlink:href="#glyph2-5" x="251.1527" y="148.7267"/>
  <use xlink:href="#glyph2-6" x="254.1527" y="148.7267"/>
</g>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 161.070312 95.070312 L 183.382812 95.070312 L 183.382812 117.382812 L 161.070312 117.382812 Z M 161.070312 95.070312 " transform="matrix(1,0,0,1,13,38)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 161.070312 72.761719 L 183.382812 72.761719 L 183.382812 95.070312 L 161.070312 95.070312 Z M 161.070312 72.761719 " transform="matrix(1,0,0,1,13,38)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 205.691406 50.453125 L 228 50.453125 L 228 72.761719 L 205.691406 72.761719 Z M 205.691406 50.453125 " transform="matrix(1,0,0,1,13,38)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 205.691406 28.144531 L 228 28.144531 L 228 50.453125 L 205.691406 50.453125 Z M 205.691406 28.144531 " transform="matrix(1,0,0,1,13,38)"/>
<g style="fill:rgb(0%,0%,0%);fill-opacity:1;">
  <use xlink:href="#glyph1-1" x="224.8473" y="78.79867"/>
</g>
<g style="fill:rgb(0%,0%,0%);fill-opacity:1;">
  <use xlink:href="#glyph2-1" x="228.8433" y="81.79867"/>
  <use xlink:href="#glyph2-2" x="231.8433" y="81.79867"/>
</g>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 205.691406 72.761719 L 228 72.761719 L 228 95.070312 L 205.691406 95.070312 Z M 205.691406 72.761719 " transform="matrix(1,0,0,1,13,38)"/>
<path style=" stroke:none;fill-rule:nonzero;fill:rgb(69.804382%,85.098267%,100%);fill-opacity:1;" d="M 241 244.617188 L 263.308594 244.617188 L 263.308594 222.308594 L 241 222.308594 Z M 241 244.617188 "/>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 228 184.308594 L 250.308594 184.308594 L 250.308594 206.617188 L 228 206.617188 Z M 228 184.308594 " transform="matrix(1,0,0,1,13,38)"/>
<path style=" stroke:none;fill-rule:nonzero;fill:rgb(69.804382%,85.098267%,100%);fill-opacity:1;" d="M 196.382812 66.144531 L 218.691406 66.144531 L 218.691406 43.835938 L 196.382812 43.835938 Z M 196.382812 66.144531 "/>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 183.382812 5.835938 L 205.691406 5.835938 L 205.691406 28.144531 L 183.382812 28.144531 Z M 183.382812 5.835938 " transform="matrix(1,0,0,1,13,38)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 161.070312 50.453125 L 183.382812 50.453125 L 183.382812 72.761719 L 161.070312 72.761719 Z M 161.070312 50.453125 " transform="matrix(1,0,0,1,13,38)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 161.070312 184.308594 L 183.382812 184.308594 L 183.382812 206.617188 L 161.070312 206.617188 Z M 161.070312 184.308594 " transform="matrix(1,0,0,1,13,38)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 161.070312 117.382812 L 183.382812 117.382812 L 183.382812 139.691406 L 161.070312 139.691406 Z M 161.070312 117.382812 " transform="matrix(1,0,0,1,13,38)"/>
<path style=" stroke:none;fill-rule:nonzero;fill:rgb(69.804382%,85.098267%,100%);fill-opacity:1;" d="M 241 66.144531 L 263.308594 66.144531 L 263.308594 43.835938 L 241 43.835938 Z M 241 66.144531 "/>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 228 5.835938 L 250.308594 5.835938 L 250.308594 28.144531 L 228 28.144531 Z M 228 5.835938 " transform="matrix(1,0,0,1,13,38)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 161.070312 139.691406 L 183.382812 139.691406 L 183.382812 162 L 161.070312 162 Z M 161.070312 139.691406 " transform="matrix(1,0,0,1,13,38)"/>
<g style="fill:rgb(0%,0%,0%);fill-opacity:1;">
  <use xlink:href="#glyph1-1" x="180.2287" y="190.3453"/>
</g>
<g style="fill:rgb(0%,0%,0%);fill-opacity:1;">
  <use xlink:href="#glyph2-3" x="184.2247" y="193.3453"/>
  <use xlink:href="#glyph2-4" x="187.2247" y="193.3453"/>
</g>
<path style=" stroke:none;fill-rule:nonzero;fill:rgb(69.804382%,85.098267%,100%);fill-opacity:1;" d="M 241 133.070312 L 263.308594 133.070312 L 263.308594 110.761719 L 241 110.761719 Z M 241 133.070312 "/>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 228 72.761719 L 250.308594 72.761719 L 250.308594 95.070312 L 228 95.070312 Z M 228 72.761719 " transform="matrix(1,0,0,1,13,38)"/>
<path style=" stroke:none;fill-rule:nonzero;fill:rgb(69.804382%,85.098267%,100%);fill-opacity:1;" d="M 241 177.691406 L 263.308594 177.691406 L 263.308594 155.382812 L 241 155.382812 Z M 241 177.691406 "/>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 228 117.382812 L 250.308594 117.382812 L 250.308594 139.691406 L 228 139.691406 Z M 228 117.382812 " transform="matrix(1,0,0,1,13,38)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 161.070312 162 L 183.382812 162 L 183.382812 184.308594 L 161.070312 184.308594 Z M 161.070312 162 " transform="matrix(1,0,0,1,13,38)"/>
<path style=" stroke:none;fill-rule:nonzero;fill:rgb(69.804382%,85.098267%,100%);fill-opacity:1;" d="M 196.382812 110.761719 L 218.691406 110.761719 L 218.691406 88.453125 L 196.382812 88.453125 Z M 196.382812 110.761719 "/>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 183.382812 50.453125 L 205.691406 50.453125 L 205.691406 72.761719 L 183.382812 72.761719 Z M 183.382812 50.453125 " transform="matrix(1,0,0,1,13,38)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 161.070312 5.835938 L 183.382812 5.835938 L 183.382812 28.144531 L 161.070312 28.144531 Z M 161.070312 5.835938 " transform="matrix(1,0,0,1,13,38)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 205.691406 139.691406 L 228 139.691406 L 228 162 L 205.691406 162 Z M 205.691406 139.691406 " transform="matrix(1,0,0,1,13,38)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 205.691406 184.308594 L 228 184.308594 L 228 206.617188 L 205.691406 206.617188 Z M 205.691406 184.308594 " transform="matrix(1,0,0,1,13,38)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 183.382812 28.144531 L 205.691406 28.144531 L 205.691406 50.453125 L 183.382812 50.453125 Z M 183.382812 28.144531 " transform="matrix(1,0,0,1,13,38)"/>
<path style=" stroke:none;fill-rule:nonzero;fill:rgb(69.804382%,85.098267%,100%);fill-opacity:1;" d="M 196.382812 133.070312 L 218.691406 133.070312 L 218.691406 110.761719 L 196.382812 110.761719 Z M 196.382812 133.070312 "/>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 183.382812 72.761719 L 205.691406 72.761719 L 205.691406 95.070312 L 183.382812 95.070312 Z M 183.382812 72.761719 " transform="matrix(1,0,0,1,13,38)"/>
<g style="fill:rgb(0%,0%,0%);fill-opacity:1;">
  <use xlink:href="#glyph0-8" x="335.9613" y="35.460783"/>
</g>
<g style="fill:rgb(0%,0%,0%);fill-opacity:1;">
  <use xlink:href="#glyph0-9" x="313.652" y="35.460783"/>
</g>
<g style="fill:rgb(0%,0%,0%);fill-opacity:1;">
  <use xlink:href="#glyph0-5" x="358.2706" y="35.460783"/>
</g>
<g style="fill:rgb(0%,0%,0%);fill-opacity:1;">
  <use xlink:href="#glyph0-7" x="291.3426" y="35.460783"/>
</g>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 336.617188 139.617188 L 358.929688 139.617188 L 358.929688 161.929688 L 336.617188 161.929688 Z M 336.617188 139.617188 " transform="matrix(1,0,0,1,13,38)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 336.617188 161.929688 L 358.929688 161.929688 L 358.929688 184.238281 L 336.617188 184.238281 Z M 336.617188 161.929688 " transform="matrix(1,0,0,1,13,38)"/>
<path style=" stroke:none;fill-rule:nonzero;fill:rgb(69.804382%,85.098267%,100%);fill-opacity:1;" d="M 305 177.617188 L 327.308594 177.617188 L 327.308594 155.308594 L 305 155.308594 Z M 305 177.617188 "/>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 292 117.308594 L 314.308594 117.308594 L 314.308594 139.617188 L 292 139.617188 Z M 292 117.308594 " transform="matrix(1,0,0,1,13,38)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 314.308594 117.308594 L 336.617188 117.308594 L 336.617188 139.617188 L 314.308594 139.617188 Z M 314.308594 117.308594 " transform="matrix(1,0,0,1,13,38)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 314.308594 161.929688 L 336.617188 161.929688 L 336.617188 184.238281 L 314.308594 184.238281 Z M 314.308594 161.929688 " transform="matrix(1,0,0,1,13,38)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 292 139.617188 L 314.308594 139.617188 L 314.308594 161.929688 L 292 161.929688 Z M 292 139.617188 " transform="matrix(1,0,0,1,13,38)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 336.617188 50.382812 L 358.929688 50.382812 L 358.929688 72.691406 L 336.617188 72.691406 Z M 336.617188 50.382812 " transform="matrix(1,0,0,1,13,38)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 336.617188 28.070312 L 358.929688 28.070312 L 358.929688 50.382812 L 336.617188 50.382812 Z M 336.617188 28.070312 " transform="matrix(1,0,0,1,13,38)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 314.308594 5.761719 L 336.617188 5.761719 L 336.617188 28.070312 L 314.308594 28.070312 Z M 314.308594 5.761719 " transform="matrix(1,0,0,1,13,38)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 292 95 L 314.308594 95 L 314.308594 117.308594 L 292 117.308594 Z M 292 95 " transform="matrix(1,0,0,1,13,38)"/>
<path style=" stroke:none;fill-rule:nonzero;fill:rgb(69.804382%,85.098267%,100%);fill-opacity:1;" d="M 305 222.238281 L 327.308594 222.238281 L 327.308594 199.929688 L 305 199.929688 Z M 305 222.238281 "/>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 292 161.929688 L 314.308594 161.929688 L 314.308594 184.238281 L 292 184.238281 Z M 292 161.929688 " transform="matrix(1,0,0,1,13,38)"/>
<path style=" stroke:none;fill-rule:nonzero;fill:rgb(69.804382%,85.098267%,100%);fill-opacity:1;" d="M 305 244.546875 L 327.308594 244.546875 L 327.308594 222.238281 L 305 222.238281 Z M 305 244.546875 "/>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 292 184.238281 L 314.308594 184.238281 L 314.308594 206.546875 L 292 206.546875 Z M 292 184.238281 " transform="matrix(1,0,0,1,13,38)"/>
<g style="fill:rgb(0%,0%,0%);fill-opacity:1;">
  <use xlink:href="#glyph1-1" x="311.1567" y="234.892"/>
</g>
<g style="fill:rgb(0%,0%,0%);fill-opacity:1;">
  <use xlink:href="#glyph2-7" x="315.1527" y="237.892"/>
  <use xlink:href="#glyph2-1" x="318.1527" y="237.892"/>
</g>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 314.308594 95 L 336.617188 95 L 336.617188 117.308594 L 314.308594 117.308594 Z M 314.308594 95 " transform="matrix(1,0,0,1,13,38)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 269.691406 28.070312 L 292 28.070312 L 292 50.382812 L 269.691406 50.382812 Z M 269.691406 28.070312 " transform="matrix(1,0,0,1,13,38)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 336.617188 95 L 358.929688 95 L 358.929688 117.308594 L 336.617188 117.308594 Z M 336.617188 95 " transform="matrix(1,0,0,1,13,38)"/>
<g style="fill:rgb(0%,0%,0%);fill-opacity:1;">
  <use xlink:href="#glyph1-1" x="355.7753" y="145.6547"/>
</g>
<g style="fill:rgb(0%,0%,0%);fill-opacity:1;">
  <use xlink:href="#glyph2-5" x="359.7713" y="148.6547"/>
  <use xlink:href="#glyph2-6" x="362.7713" y="148.6547"/>
</g>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 269.691406 95 L 292 95 L 292 117.308594 L 269.691406 117.308594 Z M 269.691406 95 " transform="matrix(1,0,0,1,13,38)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 269.691406 72.691406 L 292 72.691406 L 292 95 L 269.691406 95 Z M 269.691406 72.691406 " transform="matrix(1,0,0,1,13,38)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 314.308594 50.382812 L 336.617188 50.382812 L 336.617188 72.691406 L 314.308594 72.691406 Z M 314.308594 50.382812 " transform="matrix(1,0,0,1,13,38)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 314.308594 28.070312 L 336.617188 28.070312 L 336.617188 50.382812 L 314.308594 50.382812 Z M 314.308594 28.070312 " transform="matrix(1,0,0,1,13,38)"/>
<g style="fill:rgb(0%,0%,0%);fill-opacity:1;">
  <use xlink:href="#glyph1-1" x="333.466" y="78.72667"/>
</g>
<g style="fill:rgb(0%,0%,0%);fill-opacity:1;">
  <use xlink:href="#glyph2-1" x="337.462" y="81.72667"/>
  <use xlink:href="#glyph2-2" x="340.462" y="81.72667"/>
</g>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 314.308594 72.691406 L 336.617188 72.691406 L 336.617188 95 L 314.308594 95 Z M 314.308594 72.691406 " transform="matrix(1,0,0,1,13,38)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 336.617188 184.238281 L 358.929688 184.238281 L 358.929688 206.546875 L 336.617188 206.546875 Z M 336.617188 184.238281 " transform="matrix(1,0,0,1,13,38)"/>
<path style=" stroke:none;fill-rule:nonzero;fill:rgb(69.804382%,85.098267%,100%);fill-opacity:1;" d="M 305 66.070312 L 327.308594 66.070312 L 327.308594 43.761719 L 305 43.761719 Z M 305 66.070312 "/>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 292 5.761719 L 314.308594 5.761719 L 314.308594 28.070312 L 292 28.070312 Z M 292 5.761719 " transform="matrix(1,0,0,1,13,38)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 269.691406 50.382812 L 292 50.382812 L 292 72.691406 L 269.691406 72.691406 Z M 269.691406 50.382812 " transform="matrix(1,0,0,1,13,38)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 269.691406 184.238281 L 292 184.238281 L 292 206.546875 L 269.691406 206.546875 Z M 269.691406 184.238281 " transform="matrix(1,0,0,1,13,38)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 269.691406 117.308594 L 292 117.308594 L 292 139.617188 L 269.691406 139.617188 Z M 269.691406 117.308594 " transform="matrix(1,0,0,1,13,38)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 336.617188 5.761719 L 358.929688 5.761719 L 358.929688 28.070312 L 336.617188 28.070312 Z M 336.617188 5.761719 " transform="matrix(1,0,0,1,13,38)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 269.691406 139.617188 L 292 139.617188 L 292 161.929688 L 269.691406 161.929688 Z M 269.691406 139.617188 " transform="matrix(1,0,0,1,13,38)"/>
<g style="fill:rgb(0%,0%,0%);fill-opacity:1;">
  <use xlink:href="#glyph1-1" x="288.8473" y="190.2733"/>
</g>
<g style="fill:rgb(0%,0%,0%);fill-opacity:1;">
  <use xlink:href="#glyph2-3" x="292.8433" y="193.2733"/>
  <use xlink:href="#glyph2-4" x="295.8433" y="193.2733"/>
</g>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 336.617188 72.691406 L 358.929688 72.691406 L 358.929688 95 L 336.617188 95 Z M 336.617188 72.691406 " transform="matrix(1,0,0,1,13,38)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 336.617188 117.308594 L 358.929688 117.308594 L 358.929688 139.617188 L 336.617188 139.617188 Z M 336.617188 117.308594 " transform="matrix(1,0,0,1,13,38)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 269.691406 161.929688 L 292 161.929688 L 292 184.238281 L 269.691406 184.238281 Z M 269.691406 161.929688 " transform="matrix(1,0,0,1,13,38)"/>
<path style=" stroke:none;fill-rule:nonzero;fill:rgb(69.804382%,85.098267%,100%);fill-opacity:1;" d="M 305 110.691406 L 327.308594 110.691406 L 327.308594 88.382812 L 305 88.382812 Z M 305 110.691406 "/>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 292 50.382812 L 314.308594 50.382812 L 314.308594 72.691406 L 292 72.691406 Z M 292 50.382812 " transform="matrix(1,0,0,1,13,38)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 269.691406 5.761719 L 292 5.761719 L 292 28.070312 L 269.691406 28.070312 Z M 269.691406 5.761719 " transform="matrix(1,0,0,1,13,38)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 314.308594 139.617188 L 336.617188 139.617188 L 336.617188 161.929688 L 314.308594 161.929688 Z M 314.308594 139.617188 " transform="matrix(1,0,0,1,13,38)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 314.308594 184.238281 L 336.617188 184.238281 L 336.617188 206.546875 L 314.308594 206.546875 Z M 314.308594 184.238281 " transform="matrix(1,0,0,1,13,38)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 292 28.070312 L 314.308594 28.070312 L 314.308594 50.382812 L 292 50.382812 Z M 292 28.070312 " transform="matrix(1,0,0,1,13,38)"/>
<path style=" stroke:none;fill-rule:nonzero;fill:rgb(69.804382%,85.098267%,100%);fill-opacity:1;" d="M 305 133 L 327.308594 133 L 327.308594 110.691406 L 305 110.691406 Z M 305 133 "/>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 292 72.691406 L 314.308594 72.691406 L 314.308594 95 L 292 95 Z M 292 72.691406 " transform="matrix(1,0,0,1,13,38)"/>
<path style=" stroke:none;fill-rule:nonzero;fill:rgb(69.804382%,85.098267%,100%);fill-opacity:1;" d="M 87.617188 66.070312 L 109.929688 66.070312 L 109.929688 43.761719 L 87.617188 43.761719 Z M 87.617188 66.070312 "/>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 74.617188 5.761719 L 96.929688 5.761719 L 96.929688 28.070312 L 74.617188 28.070312 Z M 74.617188 5.761719 " transform="matrix(1,0,0,1,13,38)"/>
</g>
</svg>
