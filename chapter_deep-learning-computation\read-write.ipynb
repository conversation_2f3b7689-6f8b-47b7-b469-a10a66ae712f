{"cells": [{"cell_type": "markdown", "id": "c051b9d4", "metadata": {"origin_pos": 0}, "source": ["# 读写文件\n", "\n", "到目前为止，我们讨论了如何处理数据，\n", "以及如何构建、训练和测试深度学习模型。\n", "然而，有时我们希望保存训练的模型，\n", "以备将来在各种环境中使用（比如在部署中进行预测）。\n", "此外，当运行一个耗时较长的训练过程时，\n", "最佳的做法是定期保存中间结果，\n", "以确保在服务器电源被不小心断掉时，我们不会损失几天的计算结果。\n", "因此，现在是时候学习如何加载和存储权重向量和整个模型了。\n", "\n", "## (**加载和保存张量**)\n", "\n", "对于单个张量，我们可以直接调用`load`和`save`函数分别读写它们。\n", "这两个函数都要求我们提供一个名称，`save`要求将要保存的变量作为输入。\n"]}, {"cell_type": "code", "execution_count": 1, "id": "7e6b0eed", "metadata": {"execution": {"iopub.execute_input": "2022-12-07T16:57:14.320840Z", "iopub.status.busy": "2022-12-07T16:57:14.320234Z", "iopub.status.idle": "2022-12-07T16:57:15.480792Z", "shell.execute_reply": "2022-12-07T16:57:15.479939Z"}, "origin_pos": 2, "tab": ["pytorch"]}, "outputs": [], "source": ["import torch\n", "from torch import nn\n", "from torch.nn import functional as F\n", "\n", "x = torch.arange(4)\n", "torch.save(x, 'x-file')"]}, {"cell_type": "markdown", "id": "0d4f0647", "metadata": {"origin_pos": 5}, "source": ["我们现在可以将存储在文件中的数据读回内存。\n"]}, {"cell_type": "code", "execution_count": 2, "id": "a3dc6cc1", "metadata": {"execution": {"iopub.execute_input": "2022-12-07T16:57:15.484795Z", "iopub.status.busy": "2022-12-07T16:57:15.484187Z", "iopub.status.idle": "2022-12-07T16:57:15.496200Z", "shell.execute_reply": "2022-12-07T16:57:15.495423Z"}, "origin_pos": 7, "tab": ["pytorch"]}, "outputs": [{"data": {"text/plain": ["tensor([0, 1, 2, 3])"]}, "execution_count": 2, "metadata": {}, "output_type": "execute_result"}], "source": ["x2 = torch.load('x-file')\n", "x2"]}, {"cell_type": "markdown", "id": "731e471f", "metadata": {"origin_pos": 10}, "source": ["我们可以[**存储一个张量列表，然后把它们读回内存。**]\n"]}, {"cell_type": "code", "execution_count": 3, "id": "816aa5cd", "metadata": {"execution": {"iopub.execute_input": "2022-12-07T16:57:15.499724Z", "iopub.status.busy": "2022-12-07T16:57:15.499071Z", "iopub.status.idle": "2022-12-07T16:57:15.509309Z", "shell.execute_reply": "2022-12-07T16:57:15.508580Z"}, "origin_pos": 12, "tab": ["pytorch"]}, "outputs": [{"data": {"text/plain": ["(tensor([0, 1, 2, 3]), tensor([0., 0., 0., 0.]))"]}, "execution_count": 3, "metadata": {}, "output_type": "execute_result"}], "source": ["y = torch.zeros(4)\n", "torch.save([x, y],'x-files')\n", "x2, y2 = torch.load('x-files')\n", "(x2, y2)"]}, {"cell_type": "markdown", "id": "d41437ae", "metadata": {"origin_pos": 15}, "source": ["我们甚至可以(**写入或读取从字符串映射到张量的字典**)。\n", "当我们要读取或写入模型中的所有权重时，这很方便。\n"]}, {"cell_type": "code", "execution_count": 4, "id": "fa201bf7", "metadata": {"execution": {"iopub.execute_input": "2022-12-07T16:57:15.512722Z", "iopub.status.busy": "2022-12-07T16:57:15.512205Z", "iopub.status.idle": "2022-12-07T16:57:15.519128Z", "shell.execute_reply": "2022-12-07T16:57:15.518294Z"}, "origin_pos": 17, "tab": ["pytorch"]}, "outputs": [{"data": {"text/plain": ["{'x': tensor([0, 1, 2, 3]), 'y': tensor([0., 0., 0., 0.])}"]}, "execution_count": 4, "metadata": {}, "output_type": "execute_result"}], "source": ["mydict = {'x': x, 'y': y}\n", "torch.save(my<PERSON>, 'mydict')\n", "mydict2 = torch.load('mydict')\n", "mydict2"]}, {"cell_type": "markdown", "id": "1b23a493", "metadata": {"origin_pos": 20}, "source": ["## [**加载和保存模型参数**]\n", "\n", "保存单个权重向量（或其他张量）确实有用，\n", "但是如果我们想保存整个模型，并在以后加载它们，\n", "单独保存每个向量则会变得很麻烦。\n", "毕竟，我们可能有数百个参数散布在各处。\n", "因此，深度学习框架提供了内置函数来保存和加载整个网络。\n", "需要注意的一个重要细节是，这将保存模型的参数而不是保存整个模型。\n", "例如，如果我们有一个3层多层感知机，我们需要单独指定架构。\n", "因为模型本身可以包含任意代码，所以模型本身难以序列化。\n", "因此，为了恢复模型，我们需要用代码生成架构，\n", "然后从磁盘加载参数。\n", "让我们从熟悉的多层感知机开始尝试一下。\n"]}, {"cell_type": "code", "execution_count": 5, "id": "7ca27a1b", "metadata": {"execution": {"iopub.execute_input": "2022-12-07T16:57:15.522400Z", "iopub.status.busy": "2022-12-07T16:57:15.521895Z", "iopub.status.idle": "2022-12-07T16:57:15.528483Z", "shell.execute_reply": "2022-12-07T16:57:15.527720Z"}, "origin_pos": 22, "tab": ["pytorch"]}, "outputs": [], "source": ["class MLP(nn.Mo<PERSON>le):\n", "    def __init__(self):\n", "        super().__init__()\n", "        self.hidden = nn.<PERSON><PERSON>(20, 256)\n", "        self.output = nn.Linear(256, 10)\n", "\n", "    def forward(self, x):\n", "        return self.output(<PERSON><PERSON>relu(self.hidden(x)))\n", "\n", "net = MLP()\n", "X = torch.randn(size=(2, 20))\n", "Y = net(X)"]}, {"cell_type": "markdown", "id": "3f6d6c38", "metadata": {"origin_pos": 25}, "source": ["接下来，我们[**将模型的参数存储在一个叫做“mlp.params”的文件中。**]\n"]}, {"cell_type": "code", "execution_count": 6, "id": "970045b3", "metadata": {"execution": {"iopub.execute_input": "2022-12-07T16:57:15.531616Z", "iopub.status.busy": "2022-12-07T16:57:15.531209Z", "iopub.status.idle": "2022-12-07T16:57:15.535898Z", "shell.execute_reply": "2022-12-07T16:57:15.535152Z"}, "origin_pos": 27, "tab": ["pytorch"]}, "outputs": [], "source": ["torch.save(net.state_dict(), 'mlp.params')"]}, {"cell_type": "markdown", "id": "70324442", "metadata": {"origin_pos": 30}, "source": ["为了恢复模型，我们[**实例化了原始多层感知机模型的一个备份。**]\n", "这里我们不需要随机初始化模型参数，而是(**直接读取文件中存储的参数。**)\n"]}, {"cell_type": "code", "execution_count": 7, "id": "90e3ee76", "metadata": {"execution": {"iopub.execute_input": "2022-12-07T16:57:15.539128Z", "iopub.status.busy": "2022-12-07T16:57:15.538719Z", "iopub.status.idle": "2022-12-07T16:57:15.545525Z", "shell.execute_reply": "2022-12-07T16:57:15.544793Z"}, "origin_pos": 32, "tab": ["pytorch"]}, "outputs": [{"data": {"text/plain": ["MLP(\n", "  (hidden): Linear(in_features=20, out_features=256, bias=True)\n", "  (output): Linear(in_features=256, out_features=10, bias=True)\n", ")"]}, "execution_count": 7, "metadata": {}, "output_type": "execute_result"}], "source": ["clone = MLP()\n", "clone.load_state_dict(torch.load('mlp.params'))\n", "clone.eval()"]}, {"cell_type": "markdown", "id": "debe75e6", "metadata": {"origin_pos": 35}, "source": ["由于两个实例具有相同的模型参数，在输入相同的`X`时，\n", "两个实例的计算结果应该相同。\n", "让我们来验证一下。\n"]}, {"cell_type": "code", "execution_count": null, "id": "e2c4d08d", "metadata": {"execution": {"iopub.execute_input": "2022-12-07T16:57:15.549346Z", "iopub.status.busy": "2022-12-07T16:57:15.548468Z", "iopub.status.idle": "2022-12-07T16:57:15.555963Z", "shell.execute_reply": "2022-12-07T16:57:15.555173Z"}, "origin_pos": 37, "tab": ["pytorch"]}, "outputs": [{"data": {"text/plain": ["tensor([[True, True, True, True, True, True, True, True, True, True],\n", "        [True, True, True, True, True, True, True, True, True, True]])"]}, "execution_count": 8, "metadata": {}, "output_type": "execute_result"}, {"ename": "", "evalue": "", "output_type": "error", "traceback": ["\u001b[1;31m在当前单元格或上一个单元格中执行代码时 Kernel 崩溃。\n", "\u001b[1;31m请查看单元格中的代码，以确定故障的可能原因。\n", "\u001b[1;31m单击<a href='https://aka.ms/vscodeJupyterKernelCrash'>此处</a>了解详细信息。\n", "\u001b[1;31m有关更多详细信息，请查看 Jupyter <a href='command:jupyter.viewOutput'>log</a>。"]}], "source": ["Y_clone = clone(X)\n", "Y_clone == Y"]}, {"cell_type": "markdown", "id": "5c2a18cb", "metadata": {"origin_pos": 39}, "source": ["## 小结\n", "\n", "* `save`和`load`函数可用于张量对象的文件读写。\n", "* 我们可以通过参数字典保存和加载网络的全部参数。\n", "* 保存架构必须在代码中完成，而不是在参数中完成。\n", "\n", "## 练习\n", "\n", "1. 即使不需要将经过训练的模型部署到不同的设备上，存储模型参数还有什么实际的好处？\n", "1. 假设我们只想复用网络的一部分，以将其合并到不同的网络架构中。比如想在一个新的网络中使用之前网络的前两层，该怎么做？\n", "1. 如何同时保存网络架构和参数？需要对架构加上什么限制？\n"]}, {"cell_type": "markdown", "id": "d0edd9a9", "metadata": {"origin_pos": 41, "tab": ["pytorch"]}, "source": ["[Discussions](https://discuss.d2l.ai/t/1839)\n"]}], "metadata": {"kernelspec": {"display_name": "d2l", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.23"}}, "nbformat": 4, "nbformat_minor": 5}