<?xml version="1.0" encoding="UTF-8"?>
<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="409pt" height="259pt" viewBox="0 0 409 259" version="1.1">
<defs>
<g>
<symbol overflow="visible" id="glyph0-0">
<path style="stroke:none;" d="M 1.125 0 L 1.125 -5.625 L 5.625 -5.625 L 5.625 0 Z M 1.265625 -0.140625 L 5.484375 -0.140625 L 5.484375 -5.484375 L 1.265625 -5.484375 Z M 1.265625 -0.140625 "/>
</symbol>
<symbol overflow="visible" id="glyph0-1">
<path style="stroke:none;" d="M 0.375 -1.703125 L 1.171875 -1.8125 C 1.265625 -1.363281 1.414062 -1.039062 1.625 -0.84375 C 1.84375 -0.644531 2.113281 -0.546875 2.4375 -0.546875 C 2.800781 -0.546875 3.109375 -0.671875 3.359375 -0.921875 C 3.617188 -1.179688 3.75 -1.503906 3.75 -1.890625 C 3.75 -2.253906 3.628906 -2.550781 3.390625 -2.78125 C 3.160156 -3.019531 2.863281 -3.140625 2.5 -3.140625 C 2.34375 -3.140625 2.15625 -3.109375 1.9375 -3.046875 L 2.03125 -3.75 C 2.082031 -3.738281 2.125 -3.734375 2.15625 -3.734375 C 2.488281 -3.734375 2.789062 -3.820312 3.0625 -4 C 3.332031 -4.175781 3.46875 -4.445312 3.46875 -4.8125 C 3.46875 -5.101562 3.367188 -5.34375 3.171875 -5.53125 C 2.972656 -5.71875 2.71875 -5.8125 2.40625 -5.8125 C 2.101562 -5.8125 1.847656 -5.710938 1.640625 -5.515625 C 1.441406 -5.328125 1.3125 -5.039062 1.25 -4.65625 L 0.453125 -4.796875 C 0.554688 -5.328125 0.773438 -5.738281 1.109375 -6.03125 C 1.453125 -6.320312 1.878906 -6.46875 2.390625 -6.46875 C 2.742188 -6.46875 3.066406 -6.390625 3.359375 -6.234375 C 3.660156 -6.085938 3.890625 -5.882812 4.046875 -5.625 C 4.203125 -5.363281 4.28125 -5.085938 4.28125 -4.796875 C 4.28125 -4.515625 4.203125 -4.257812 4.046875 -4.03125 C 3.898438 -3.800781 3.679688 -3.617188 3.390625 -3.484375 C 3.773438 -3.398438 4.070312 -3.21875 4.28125 -2.9375 C 4.488281 -2.664062 4.59375 -2.320312 4.59375 -1.90625 C 4.59375 -1.34375 4.382812 -0.863281 3.96875 -0.46875 C 3.5625 -0.0820312 3.046875 0.109375 2.421875 0.109375 C 1.859375 0.109375 1.390625 -0.0546875 1.015625 -0.390625 C 0.640625 -0.722656 0.425781 -1.160156 0.375 -1.703125 Z M 0.375 -1.703125 "/>
</symbol>
<symbol overflow="visible" id="glyph0-2">
<path style="stroke:none;" d=""/>
</symbol>
<symbol overflow="visible" id="glyph0-3">
<path style="stroke:none;" d="M 2.25 -1.046875 L 2.25 -2.8125 L 0.5 -2.8125 L 0.5 -3.546875 L 2.25 -3.546875 L 2.25 -5.296875 L 3 -5.296875 L 3 -3.546875 L 4.75 -3.546875 L 4.75 -2.8125 L 3 -2.8125 L 3 -1.046875 Z M 2.25 -1.046875 "/>
</symbol>
<symbol overflow="visible" id="glyph0-4">
<path style="stroke:none;" d="M 0.703125 0 L 0.703125 -6.4375 L 3.5625 -6.4375 C 4.132812 -6.4375 4.570312 -6.378906 4.875 -6.265625 C 5.175781 -6.148438 5.414062 -5.945312 5.59375 -5.65625 C 5.769531 -5.363281 5.859375 -5.039062 5.859375 -4.6875 C 5.859375 -4.226562 5.707031 -3.84375 5.40625 -3.53125 C 5.113281 -3.21875 4.660156 -3.019531 4.046875 -2.9375 C 4.265625 -2.820312 4.4375 -2.710938 4.5625 -2.609375 C 4.8125 -2.378906 5.046875 -2.09375 5.265625 -1.75 L 6.390625 0 L 5.3125 0 L 4.453125 -1.34375 C 4.210938 -1.726562 4.007812 -2.019531 3.84375 -2.21875 C 3.6875 -2.425781 3.539062 -2.570312 3.40625 -2.65625 C 3.28125 -2.738281 3.15625 -2.796875 3.03125 -2.828125 C 2.925781 -2.847656 2.765625 -2.859375 2.546875 -2.859375 L 1.5625 -2.859375 L 1.5625 0 Z M 1.5625 -3.59375 L 3.390625 -3.59375 C 3.785156 -3.59375 4.09375 -3.632812 4.3125 -3.71875 C 4.53125 -3.800781 4.695312 -3.929688 4.8125 -4.109375 C 4.925781 -4.285156 4.984375 -4.476562 4.984375 -4.6875 C 4.984375 -4.988281 4.867188 -5.238281 4.640625 -5.4375 C 4.421875 -5.632812 4.070312 -5.734375 3.59375 -5.734375 L 1.5625 -5.734375 Z M 1.5625 -3.59375 "/>
</symbol>
<symbol overflow="visible" id="glyph0-5">
<path style="stroke:none;" d="M 3.78125 -1.5 L 4.609375 -1.40625 C 4.472656 -0.925781 4.226562 -0.550781 3.875 -0.28125 C 3.53125 -0.0195312 3.085938 0.109375 2.546875 0.109375 C 1.867188 0.109375 1.328125 -0.0976562 0.921875 -0.515625 C 0.523438 -0.941406 0.328125 -1.535156 0.328125 -2.296875 C 0.328125 -3.078125 0.53125 -3.679688 0.9375 -4.109375 C 1.34375 -4.546875 1.867188 -4.765625 2.515625 -4.765625 C 3.140625 -4.765625 3.644531 -4.550781 4.03125 -4.125 C 4.425781 -3.707031 4.625 -3.113281 4.625 -2.34375 C 4.625 -2.289062 4.625 -2.21875 4.625 -2.125 L 1.140625 -2.125 C 1.171875 -1.613281 1.316406 -1.222656 1.578125 -0.953125 C 1.835938 -0.679688 2.164062 -0.546875 2.5625 -0.546875 C 2.851562 -0.546875 3.097656 -0.617188 3.296875 -0.765625 C 3.503906 -0.921875 3.664062 -1.164062 3.78125 -1.5 Z M 1.1875 -2.78125 L 3.796875 -2.78125 C 3.765625 -3.175781 3.664062 -3.472656 3.5 -3.671875 C 3.25 -3.972656 2.921875 -4.125 2.515625 -4.125 C 2.148438 -4.125 1.84375 -4 1.59375 -3.75 C 1.351562 -3.507812 1.21875 -3.1875 1.1875 -2.78125 Z M 1.1875 -2.78125 "/>
</symbol>
<symbol overflow="visible" id="glyph0-6">
<path style="stroke:none;" d="M 0.65625 0 L 0.65625 -6.4375 L 1.515625 -6.4375 L 1.515625 -0.765625 L 4.6875 -0.765625 L 4.6875 0 Z M 0.65625 0 "/>
</symbol>
<symbol overflow="visible" id="glyph0-7">
<path style="stroke:none;" d="M 4.921875 -6.4375 L 5.78125 -6.4375 L 5.78125 -2.71875 C 5.78125 -2.070312 5.703125 -1.554688 5.546875 -1.171875 C 5.398438 -0.796875 5.132812 -0.488281 4.75 -0.25 C 4.375 -0.0078125 3.875 0.109375 3.25 0.109375 C 2.644531 0.109375 2.148438 0.00390625 1.765625 -0.203125 C 1.390625 -0.410156 1.117188 -0.710938 0.953125 -1.109375 C 0.785156 -1.503906 0.703125 -2.039062 0.703125 -2.71875 L 0.703125 -6.4375 L 1.5625 -6.4375 L 1.5625 -2.71875 C 1.5625 -2.164062 1.613281 -1.753906 1.71875 -1.484375 C 1.820312 -1.222656 2 -1.019531 2.25 -0.875 C 2.5 -0.726562 2.8125 -0.65625 3.1875 -0.65625 C 3.8125 -0.65625 4.253906 -0.796875 4.515625 -1.078125 C 4.785156 -1.367188 4.921875 -1.914062 4.921875 -2.71875 Z M 4.921875 -6.4375 "/>
</symbol>
<symbol overflow="visible" id="glyph0-8">
<path style="stroke:none;" d="M 3.359375 0 L 2.5625 0 L 2.5625 -5.046875 C 2.375 -4.859375 2.125 -4.671875 1.8125 -4.484375 C 1.5 -4.304688 1.222656 -4.175781 0.984375 -4.09375 L 0.984375 -4.859375 C 1.421875 -5.054688 1.804688 -5.300781 2.140625 -5.59375 C 2.472656 -5.894531 2.707031 -6.1875 2.84375 -6.46875 L 3.359375 -6.46875 Z M 3.359375 0 "/>
</symbol>
<symbol overflow="visible" id="glyph1-0">
<path style="stroke:none;" d=""/>
</symbol>
<symbol overflow="visible" id="glyph1-1">
<path style="stroke:none;" d="M 3.75 -2.25 L 5.53125 -4.03125 C 5.625 -4.140625 5.65625 -4.15625 5.65625 -4.21875 C 5.65625 -4.328125 5.5625 -4.40625 5.46875 -4.40625 C 5.40625 -4.40625 5.359375 -4.375 5.28125 -4.28125 L 3.484375 -2.5 L 1.6875 -4.28125 C 1.59375 -4.390625 1.5625 -4.40625 1.5 -4.40625 C 1.40625 -4.40625 1.3125 -4.328125 1.3125 -4.21875 C 1.3125 -4.15625 1.359375 -4.109375 1.4375 -4.03125 L 3.21875 -2.25 L 1.4375 -0.453125 C 1.34375 -0.375 1.3125 -0.3125 1.3125 -0.265625 C 1.3125 -0.15625 1.40625 -0.078125 1.5 -0.078125 C 1.5625 -0.078125 1.59375 -0.09375 1.6875 -0.203125 L 3.484375 -1.984375 L 5.28125 -0.203125 C 5.359375 -0.109375 5.421875 -0.078125 5.46875 -0.078125 C 5.578125 -0.078125 5.65625 -0.15625 5.65625 -0.265625 C 5.65625 -0.328125 5.625 -0.34375 5.53125 -0.453125 Z M 3.75 -2.25 "/>
</symbol>
<symbol overflow="visible" id="glyph2-0">
<path style="stroke:none;" d="M 1.5 0 L 1.5 -7.5 L 7.5 -7.5 L 7.5 0 Z M 1.6875 -0.1875 L 7.3125 -0.1875 L 7.3125 -7.3125 L 1.6875 -7.3125 Z M 1.6875 -0.1875 "/>
</symbol>
<symbol overflow="visible" id="glyph2-1">
<path style="stroke:none;" d=""/>
</symbol>
<symbol overflow="visible" id="glyph3-0">
<path style="stroke:none;" d=""/>
</symbol>
<symbol overflow="visible" id="glyph3-1">
<path style="stroke:none;" d="M 2.359375 -7.234375 L 1.78125 -7.03125 C 2.046875 -6.671875 2.296875 -6.25 2.53125 -5.78125 L 3.078125 -6.03125 C 2.875 -6.453125 2.640625 -6.84375 2.359375 -7.234375 Z M 6.78125 -7.234375 C 6.5625 -6.78125 6.296875 -6.390625 5.953125 -6.0625 L 6.46875 -5.765625 C 6.828125 -6.125 7.125 -6.546875 7.359375 -7.015625 Z M 1.15625 -5.71875 L 1.15625 -5.125 L 3.765625 -5.125 C 3.625 -4.8125 3.4375 -4.5 3.234375 -4.21875 L 0.640625 -4.21875 L 0.640625 -3.625 L 2.78125 -3.625 C 2.15625 -2.921875 1.359375 -2.34375 0.390625 -1.921875 L 0.75 -1.359375 C 1.34375 -1.640625 1.890625 -1.984375 2.375 -2.375 L 2.375 0.078125 C 2.375 0.546875 2.640625 0.78125 3.1875 0.78125 L 6.515625 0.78125 C 7 0.78125 7.3125 0.703125 7.5 0.578125 C 7.6875 0.40625 7.8125 -0.03125 7.890625 -0.734375 L 7.25 -0.9375 C 7.203125 -0.484375 7.125 -0.1875 7.03125 -0.015625 C 6.953125 0.109375 6.71875 0.1875 6.34375 0.1875 L 3.453125 0.1875 C 3.171875 0.1875 3.03125 0.078125 3.03125 -0.125 L 3.03125 -2.15625 L 5.921875 -2.15625 L 5.921875 -1.484375 C 5.921875 -1.296875 5.796875 -1.203125 5.5625 -1.203125 C 5.1875 -1.203125 4.78125 -1.21875 4.328125 -1.234375 L 4.46875 -0.671875 C 4.90625 -0.65625 5.34375 -0.640625 5.8125 -0.640625 C 6.296875 -0.671875 6.546875 -0.890625 6.5625 -1.3125 L 6.5625 -2.53125 C 7.015625 -2.15625 7.5625 -1.796875 8.203125 -1.453125 L 8.625 -1.984375 C 7.53125 -2.46875 6.71875 -3 6.234375 -3.625 L 8.359375 -3.625 L 8.359375 -4.21875 L 3.984375 -4.21875 C 4.15625 -4.5 4.296875 -4.8125 4.453125 -5.125 L 7.859375 -5.125 L 7.859375 -5.71875 L 4.671875 -5.71875 C 4.828125 -6.203125 4.96875 -6.734375 5.0625 -7.296875 L 4.421875 -7.375 C 4.328125 -6.78125 4.1875 -6.234375 4.015625 -5.71875 Z M 3.578125 -3.625 L 5.578125 -3.625 C 5.78125 -3.3125 6.03125 -3.015625 6.34375 -2.734375 L 2.78125 -2.734375 C 3.0625 -3 3.328125 -3.296875 3.578125 -3.625 Z M 3.578125 -3.625 "/>
</symbol>
<symbol overflow="visible" id="glyph3-2">
<path style="stroke:none;" d="M 0.6875 -5.96875 C 1.125 -6.015625 1.53125 -6.09375 1.921875 -6.171875 L 1.921875 -4.890625 L 0.59375 -4.890625 L 0.59375 -4.265625 L 1.890625 -4.265625 C 1.578125 -3.21875 1.046875 -2.296875 0.328125 -1.484375 L 0.59375 -0.8125 C 1.140625 -1.515625 1.578125 -2.328125 1.921875 -3.203125 L 1.921875 0.859375 L 2.53125 0.859375 L 2.53125 -3.25 C 2.796875 -2.890625 3.109375 -2.390625 3.484375 -1.765625 L 3.84375 -2.296875 C 3.390625 -2.859375 2.96875 -3.359375 2.53125 -3.8125 L 2.53125 -4.265625 L 3.640625 -4.265625 L 3.640625 -4.890625 L 2.53125 -4.890625 L 2.53125 -6.3125 C 2.90625 -6.40625 3.265625 -6.5 3.59375 -6.625 L 3.359375 -7.234375 C 2.5625 -6.90625 1.625 -6.703125 0.5625 -6.59375 Z M 4.171875 -6.8125 L 4.171875 -2.625 L 8.0625 -2.625 L 8.0625 -6.8125 Z M 7.421875 -3.234375 L 4.828125 -3.234375 L 4.828125 -6.1875 L 7.421875 -6.1875 Z M 4.953125 -2.046875 C 4.546875 -1.046875 4.046875 -0.1875 3.46875 0.53125 L 4.015625 0.921875 C 4.59375 0.15625 5.109375 -0.734375 5.53125 -1.765625 Z M 7.15625 -2.09375 L 6.640625 -1.765625 C 7.265625 -0.796875 7.78125 0.09375 8.15625 0.90625 L 8.703125 0.515625 C 8.34375 -0.21875 7.828125 -1.09375 7.15625 -2.09375 Z M 7.15625 -2.09375 "/>
</symbol>
<symbol overflow="visible" id="glyph3-3">
<path style="stroke:none;" d="M 2.921875 -3.90625 L 2.921875 -3.328125 L 7.796875 -3.328125 L 7.796875 -3.90625 Z M 2.28125 -2.4375 L 2.28125 -1.84375 L 4.25 -1.84375 C 3.625 -0.828125 3.09375 -0.203125 2.703125 0.03125 C 2.65625 0.046875 2.625 0.078125 2.5625 0.09375 L 2.703125 0.640625 C 4.40625 0.53125 5.96875 0.375 7.390625 0.1875 C 7.578125 0.40625 7.734375 0.640625 7.890625 0.875 L 8.40625 0.515625 C 8.015625 -0.0625 7.4375 -0.75 6.703125 -1.53125 L 6.21875 -1.234375 C 6.484375 -0.921875 6.75 -0.625 7 -0.328125 C 5.84375 -0.15625 4.671875 -0.03125 3.484375 0.046875 C 3.90625 -0.328125 4.390625 -0.96875 4.9375 -1.84375 L 8.390625 -1.84375 L 8.390625 -2.4375 Z M 7.296875 -6.390625 L 7.296875 -5.34375 L 2 -5.34375 L 2 -6.390625 Z M 2 -4.765625 L 7.921875 -4.765625 L 7.921875 -7 L 1.375 -7 L 1.375 -4.015625 C 1.359375 -2.203125 1.046875 -0.734375 0.421875 0.40625 L 0.890625 0.84375 C 1.625 -0.515625 2 -2.140625 2 -4.015625 Z M 2 -4.765625 "/>
</symbol>
<symbol overflow="visible" id="glyph3-4">
<path style="stroke:none;" d="M 3.0625 -3.421875 C 2.765625 -3.296875 2.484375 -3.171875 2.1875 -3.0625 L 2.1875 -5.0625 L 3.078125 -5.0625 L 3.078125 -5.6875 L 2.1875 -5.6875 L 2.1875 -7.359375 L 1.5625 -7.359375 L 1.5625 -5.6875 L 0.546875 -5.6875 L 0.546875 -5.0625 L 1.5625 -5.0625 L 1.5625 -2.859375 C 1.1875 -2.75 0.8125 -2.65625 0.4375 -2.578125 L 0.578125 -1.96875 C 0.90625 -2.046875 1.234375 -2.140625 1.5625 -2.234375 L 1.5625 -0.09375 C 1.5625 0.140625 1.453125 0.25 1.234375 0.25 C 1.015625 0.25 0.78125 0.234375 0.546875 0.203125 L 0.671875 0.84375 L 1.453125 0.84375 C 1.9375 0.84375 2.1875 0.578125 2.1875 0.0625 L 2.1875 -2.453125 C 2.46875 -2.5625 2.765625 -2.6875 3.0625 -2.8125 Z M 4.140625 -4.046875 L 5.40625 -4.046875 L 5.40625 -4.671875 L 4.140625 -4.671875 L 4.140625 -7.265625 L 3.5 -7.265625 L 3.5 -0.203125 C 3.5 -0.015625 3.40625 0.125 3.203125 0.1875 L 3.359375 0.796875 C 4.140625 0.609375 4.828125 0.390625 5.4375 0.140625 L 5.3125 -0.453125 C 4.953125 -0.28125 4.578125 -0.140625 4.140625 -0.03125 Z M 7.375 0.78125 C 7.734375 0.78125 7.984375 0.671875 8.15625 0.5 C 8.34375 0.28125 8.46875 -0.3125 8.53125 -1.3125 L 7.9375 -1.5 C 7.90625 -0.6875 7.828125 -0.203125 7.71875 -0.03125 C 7.625 0.09375 7.484375 0.15625 7.265625 0.15625 L 6.828125 0.15625 C 6.59375 0.15625 6.484375 0.03125 6.484375 -0.203125 L 6.484375 -3.953125 C 7.09375 -4.15625 7.75 -4.453125 8.4375 -4.828125 L 8.0625 -5.359375 C 7.546875 -5.046875 7.015625 -4.78125 6.484375 -4.609375 L 6.484375 -7.328125 L 5.84375 -7.328125 L 5.84375 -0.078125 C 5.84375 0.484375 6.109375 0.78125 6.625 0.78125 Z M 7.375 0.78125 "/>
</symbol>
<symbol overflow="visible" id="glyph3-5">
<path style="stroke:none;" d="M 1.375 -3.578125 L 1.375 -1.28125 L 4.171875 -1.28125 L 4.171875 -0.78125 L 1.15625 -0.78125 L 1.15625 -0.34375 L 4.171875 -0.34375 L 4.171875 0.1875 L 0.484375 0.1875 L 0.484375 0.6875 L 8.515625 0.6875 L 8.515625 0.1875 L 4.8125 0.1875 L 4.8125 -0.34375 L 7.859375 -0.34375 L 7.859375 -0.78125 L 4.8125 -0.78125 L 4.8125 -1.28125 L 7.640625 -1.28125 L 7.640625 -3.578125 Z M 7.015625 -1.6875 L 4.8125 -1.6875 L 4.8125 -2.234375 L 7.015625 -2.234375 Z M 4.171875 -1.6875 L 2 -1.6875 L 2 -2.234375 L 4.171875 -2.234375 Z M 2 -2.625 L 2 -3.171875 L 4.171875 -3.171875 L 4.171875 -2.625 Z M 4.8125 -3.171875 L 7.015625 -3.171875 L 7.015625 -2.625 L 4.8125 -2.625 Z M 1.578125 -7.203125 L 1.578125 -4.984375 L 7.453125 -4.984375 L 7.453125 -7.203125 Z M 6.828125 -5.375 L 2.203125 -5.375 L 2.203125 -5.90625 L 6.828125 -5.90625 Z M 2.203125 -6.296875 L 2.203125 -6.8125 L 6.828125 -6.8125 L 6.828125 -6.296875 Z M 0.46875 -4.515625 L 0.46875 -4.03125 L 8.53125 -4.03125 L 8.53125 -4.515625 Z M 0.46875 -4.515625 "/>
</symbol>
<symbol overflow="visible" id="glyph3-6">
<path style="stroke:none;" d="M 0.640625 -6.046875 L 0.640625 -5.421875 L 1.921875 -5.421875 L 1.921875 -3.9375 L 0.515625 -3.9375 L 0.515625 -3.3125 L 1.890625 -3.3125 C 1.765625 -1.609375 1.265625 -0.34375 0.390625 0.5 L 0.875 0.921875 C 1.609375 0.21875 2.109375 -0.78125 2.359375 -2.046875 C 2.71875 -1.65625 3.15625 -1.125 3.65625 -0.4375 L 4.015625 -1 C 3.484375 -1.609375 2.96875 -2.15625 2.46875 -2.640625 C 2.5 -2.859375 2.515625 -3.078125 2.53125 -3.3125 L 3.890625 -3.3125 L 3.890625 -3.9375 L 2.578125 -3.9375 L 2.578125 -5.421875 L 3.765625 -5.421875 L 3.765625 -6.046875 L 2.578125 -6.046875 L 2.578125 -7.28125 L 1.921875 -7.28125 L 1.921875 -6.046875 Z M 7.953125 -6.96875 L 4.296875 -6.96875 L 4.296875 -2.03125 L 4.921875 -2.03125 L 4.921875 -6.375 L 7.328125 -6.375 L 7.328125 -2.03125 L 7.953125 -2.03125 Z M 7.703125 0.78125 C 7.984375 0.78125 8.203125 0.71875 8.34375 0.578125 C 8.484375 0.40625 8.59375 -0.03125 8.65625 -0.78125 L 8.09375 -0.953125 C 8.0625 -0.390625 8 -0.046875 7.921875 0.0625 C 7.859375 0.15625 7.75 0.203125 7.59375 0.203125 L 7.140625 0.203125 C 6.9375 0.203125 6.84375 0.09375 6.84375 -0.09375 L 6.84375 -1.984375 L 6.265625 -1.984375 L 6.265625 0.03125 C 6.265625 0.53125 6.484375 0.78125 6.953125 0.78125 Z M 5.828125 -5.625 L 5.828125 -4.03125 C 5.8125 -2.890625 5.609375 -1.96875 5.234375 -1.265625 C 4.84375 -0.578125 4.15625 -0.03125 3.15625 0.375 L 3.5 0.921875 C 4.515625 0.5 5.25 -0.078125 5.703125 -0.8125 C 6.15625 -1.625 6.40625 -2.6875 6.421875 -4.03125 L 6.421875 -5.625 Z M 5.828125 -5.625 "/>
</symbol>
<symbol overflow="visible" id="glyph3-7">
<path style="stroke:none;" d="M 0.546875 -6.546875 L 0.546875 -5.953125 L 2.65625 -5.953125 L 2.65625 -5.09375 L 3.328125 -5.09375 L 3.328125 -5.953125 L 5.671875 -5.953125 L 5.671875 -5.09375 L 6.34375 -5.09375 L 6.34375 -5.953125 L 8.453125 -5.953125 L 8.453125 -6.546875 L 6.34375 -6.546875 L 6.34375 -7.359375 L 5.671875 -7.359375 L 5.671875 -6.546875 L 3.328125 -6.546875 L 3.328125 -7.359375 L 2.65625 -7.359375 L 2.65625 -6.546875 Z M 1.703125 -5.1875 L 1.265625 -4.765625 C 1.875 -4.34375 2.34375 -3.9375 2.6875 -3.5625 L 3.125 -4 C 2.75 -4.390625 2.28125 -4.78125 1.703125 -5.1875 Z M 1.140625 -3.65625 L 0.703125 -3.234375 C 1.3125 -2.8125 1.78125 -2.421875 2.125 -2.046875 L 2.5625 -2.484375 C 2.171875 -2.875 1.703125 -3.265625 1.140625 -3.65625 Z M 2.203125 -1.703125 C 1.859375 -0.9375 1.390625 -0.140625 0.8125 0.640625 L 1.40625 0.96875 C 1.96875 0.1875 2.40625 -0.625 2.75 -1.453125 Z M 7.046875 -4.203125 L 7.046875 -2.515625 C 7.046875 -2.296875 6.921875 -2.1875 6.6875 -2.1875 C 6.359375 -2.1875 6.015625 -2.203125 5.625 -2.234375 L 5.78125 -1.625 C 6.140625 -1.609375 6.53125 -1.59375 6.921875 -1.59375 C 7.421875 -1.609375 7.671875 -1.859375 7.6875 -2.328125 L 7.6875 -4.78125 L 3.78125 -4.78125 L 3.78125 -0.109375 C 3.78125 0.453125 4.078125 0.734375 4.65625 0.734375 L 7.078125 0.734375 C 7.546875 0.734375 7.859375 0.65625 8.03125 0.5 C 8.203125 0.296875 8.328125 -0.234375 8.40625 -1.09375 L 7.78125 -1.296875 C 7.734375 -0.71875 7.671875 -0.328125 7.5625 -0.140625 C 7.484375 0.03125 7.265625 0.125 6.90625 0.125 L 4.90625 0.125 C 4.59375 0.125 4.453125 -0.03125 4.453125 -0.328125 L 4.453125 -4.203125 Z M 7.046875 -4.203125 "/>
</symbol>
<symbol overflow="visible" id="glyph3-8">
<path style="stroke:none;" d="M 3.125 -7.421875 C 2.546875 -6 1.625 -4.734375 0.34375 -3.640625 L 0.546875 -2.96875 C 1.03125 -3.359375 1.46875 -3.78125 1.875 -4.21875 L 1.875 0.921875 L 2.515625 0.921875 L 2.515625 -5.015625 C 3 -5.671875 3.40625 -6.375 3.71875 -7.140625 Z M 7.265625 0.75 C 7.703125 0.75 8 0.640625 8.15625 0.4375 C 8.3125 0.21875 8.421875 -0.40625 8.484375 -1.453125 L 7.890625 -1.640625 C 7.84375 -0.75 7.78125 -0.234375 7.6875 -0.09375 C 7.578125 0.046875 7.390625 0.140625 7.15625 0.140625 L 5.703125 0.140625 C 5.40625 0.140625 5.25 -0.015625 5.25 -0.28125 L 5.25 -3.328125 C 6.375 -4.03125 7.359375 -4.828125 8.1875 -5.734375 L 7.734375 -6.25 C 7.03125 -5.4375 6.203125 -4.71875 5.25 -4.09375 L 5.25 -7.3125 L 4.609375 -7.3125 L 4.609375 -3.703125 C 4.15625 -3.421875 3.65625 -3.1875 3.15625 -2.953125 L 3.453125 -2.34375 C 3.84375 -2.53125 4.234375 -2.734375 4.609375 -2.9375 L 4.609375 -0.203125 C 4.609375 0.4375 4.921875 0.75 5.546875 0.75 Z M 7.265625 0.75 "/>
</symbol>
<symbol overflow="visible" id="glyph4-0">
<path style="stroke:none;" d=""/>
</symbol>
<symbol overflow="visible" id="glyph4-1">
<path style="stroke:none;" d="M 4.359375 0 L 4.359375 -0.21875 C 4.21875 -0.265625 4.15625 -0.296875 4.09375 -0.390625 L 2.75 -2.453125 L 3.65625 -3.578125 C 3.828125 -3.78125 4.015625 -3.890625 4.296875 -3.9375 L 4.296875 -4.15625 L 2.78125 -4.15625 L 2.78125 -3.9375 L 2.96875 -3.921875 C 3.171875 -3.890625 3.234375 -3.859375 3.234375 -3.71875 C 3.234375 -3.578125 3.140625 -3.453125 2.890625 -3.140625 L 2.578125 -2.734375 C 2.53125 -2.78125 2.5 -2.828125 2.453125 -2.875 C 2.15625 -3.265625 1.9375 -3.640625 1.9375 -3.765625 C 1.9375 -3.875 2.0625 -3.921875 2.359375 -3.9375 L 2.359375 -4.15625 L 0.109375 -4.15625 L 0.109375 -3.9375 C 0.34375 -3.890625 0.390625 -3.859375 0.578125 -3.578125 L 1.734375 -1.8125 C 1.59375 -1.640625 1.671875 -1.734375 1.53125 -1.5625 L 1.140625 -1.046875 C 0.640625 -0.375 0.453125 -0.234375 0.125 -0.21875 L 0.125 0 L 1.640625 0 L 1.640625 -0.21875 C 1.328125 -0.234375 1.203125 -0.296875 1.203125 -0.4375 C 1.203125 -0.578125 1.421875 -0.9375 1.765625 -1.359375 C 1.828125 -1.421875 1.875 -1.484375 1.921875 -1.53125 C 2.03125 -1.34375 2.15625 -1.15625 2.296875 -0.96875 C 2.515625 -0.65625 2.59375 -0.5 2.59375 -0.390625 C 2.59375 -0.28125 2.46875 -0.234375 2.1875 -0.21875 L 2.1875 0 Z M 4.359375 0 "/>
</symbol>
</g>
<clipPath id="clip1">
  <path d="M 41.5 198.125 L 46.25 198.125 L 46.25 202.875 L 41.5 202.875 Z M 41.5 198.125 "/>
</clipPath>
<clipPath id="clip2">
  <path d="M 41.5 109.777344 L 46.25 109.777344 L 46.25 114.527344 L 41.5 114.527344 Z M 41.5 109.777344 "/>
</clipPath>
<clipPath id="clip3">
  <path d="M 223.65625 198.125 L 228.40625 198.125 L 228.40625 202.875 L 223.65625 202.875 Z M 223.65625 198.125 "/>
</clipPath>
<clipPath id="clip4">
  <path d="M 223.65625 109.777344 L 228.40625 109.777344 L 228.40625 114.527344 L 223.65625 114.527344 Z M 223.65625 109.777344 "/>
</clipPath>
<clipPath id="clip5">
  <path d="M 343.5 140.730469 L 348.25 140.730469 L 348.25 145.480469 L 343.5 145.480469 Z M 343.5 140.730469 "/>
</clipPath>
</defs>
<g id="surface1">
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-dasharray:4,4;stroke-miterlimit:10;" d="M 112.601562 -71.800781 L 228.5 -71.800781 L 228.5 83.398438 L 112.601562 83.398438 Z M 112.601562 -71.800781 " transform="matrix(1,0,0,1,-112,136)"/>
<path style=" stroke:none;fill-rule:nonzero;fill:rgb(69.804382%,85.098267%,100%);fill-opacity:1;" d="M 11.925781 207.949219 L 105.925781 207.949219 L 105.925781 189.949219 L 11.925781 189.949219 Z M 11.925781 207.949219 "/>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 123.925781 53.949219 L 217.925781 53.949219 L 217.925781 71.949219 L 123.925781 71.949219 Z M 123.925781 53.949219 " transform="matrix(1,0,0,1,-112,136)"/>
<g style="fill:rgb(0%,0%,0%);fill-opacity:1;">
  <use xlink:href="#glyph0-1" x="33.7521" y="202.75371"/>
  <use xlink:href="#glyph0-2" x="38.7579" y="202.75371"/>
</g>
<g clip-path="url(#clip1)" clip-rule="nonzero">
<g style="fill:rgb(0%,0%,0%);fill-opacity:1;">
  <use xlink:href="#glyph1-1" x="40.362" y="202.73691"/>
</g>
<g style="fill:rgb(0%,0%,0%);fill-opacity:1;">
  <use xlink:href="#glyph1-1" x="40.362" y="202.73691"/>
</g>
</g>
<g style="fill:rgb(0%,0%,0%);fill-opacity:1;">
  <use xlink:href="#glyph2-1" x="46.258" y="202.75371"/>
</g>
<g style="fill:rgb(0%,0%,0%);fill-opacity:1;">
  <use xlink:href="#glyph0-1" x="49.592" y="202.75371"/>
  <use xlink:href="#glyph0-2" x="54.5978" y="202.75371"/>
</g>
<g style="fill:rgb(0%,0%,0%);fill-opacity:1;">
  <use xlink:href="#glyph3-1" x="57.0979" y="202.75371"/>
  <use xlink:href="#glyph3-2" x="66.0979" y="202.75371"/>
  <use xlink:href="#glyph3-3" x="75.0979" y="202.75371"/>
</g>
<g style="fill:rgb(0%,0%,0%);fill-opacity:1;">
  <use xlink:href="#glyph4-1" x="56.675" y="249.05"/>
</g>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 170.925781 99.050781 L 170.925781 77.851562 " transform="matrix(1,0,0,1,-112,136)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:butt;stroke-linejoin:miter;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 170.925781 73.851562 L 170.925781 77.851562 M 169.425781 77.851562 L 170.925781 73.851562 L 172.425781 77.851562 " transform="matrix(1,0,0,1,-112,136)"/>
<path style="fill-rule:nonzero;fill:rgb(39.99939%,74.902344%,100%);fill-opacity:1;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 175.324219 -93.621094 C 177.960938 -90.988281 177.960938 -86.714844 175.324219 -84.078125 C 172.6875 -81.441406 168.414062 -81.441406 165.777344 -84.078125 C 163.140625 -86.714844 163.140625 -90.988281 165.777344 -93.621094 C 168.414062 -96.257812 172.6875 -96.257812 175.324219 -93.621094 " transform="matrix(1,0,0,1,-112,136)"/>
<g style="fill:rgb(0%,0%,0%);fill-opacity:1;">
  <use xlink:href="#glyph0-3" x="55.9221" y="50.00278"/>
</g>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 123.925781 25.351562 L 217.925781 25.351562 L 217.925781 43.351562 L 123.925781 43.351562 Z M 123.925781 25.351562 " transform="matrix(1,0,0,1,-112,136)"/>
<g style="fill:rgb(0%,0%,0%);fill-opacity:1;">
  <use xlink:href="#glyph3-4" x="31.925" y="173.85"/>
  <use xlink:href="#glyph3-5" x="40.925" y="173.85"/>
  <use xlink:href="#glyph3-6" x="49.925" y="173.85"/>
  <use xlink:href="#glyph3-7" x="58.925" y="173.85"/>
  <use xlink:href="#glyph3-8" x="67.925" y="173.85"/>
  <use xlink:href="#glyph3-3" x="76.925" y="173.85"/>
</g>
<path style=" stroke:none;fill-rule:nonzero;fill:rgb(39.99939%,74.902344%,100%);fill-opacity:1;" d="M 11.925781 30.101562 L 105.925781 30.101562 L 105.925781 12.101562 L 11.925781 12.101562 Z M 11.925781 30.101562 "/>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 123.925781 -123.898438 L 217.925781 -123.898438 L 217.925781 -105.898438 L 123.925781 -105.898438 Z M 123.925781 -123.898438 " transform="matrix(1,0,0,1,-112,136)"/>
<g style="fill:rgb(0%,0%,0%);fill-opacity:1;">
  <use xlink:href="#glyph0-4" x="47.4201" y="23.9528"/>
  <use xlink:href="#glyph0-5" x="53.9199" y="23.9528"/>
  <use xlink:href="#glyph0-6" x="58.9257" y="23.9528"/>
  <use xlink:href="#glyph0-7" x="63.9315" y="23.9528"/>
</g>
<path style=" stroke:none;fill-rule:nonzero;fill:rgb(39.99939%,74.902344%,100%);fill-opacity:1;" d="M 11.925781 150.75 L 105.925781 150.75 L 105.925781 132.75 L 11.925781 132.75 Z M 11.925781 150.75 "/>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 123.925781 -3.25 L 217.925781 -3.25 L 217.925781 14.75 L 123.925781 14.75 Z M 123.925781 -3.25 " transform="matrix(1,0,0,1,-112,136)"/>
<g style="fill:rgb(0%,0%,0%);fill-opacity:1;">
  <use xlink:href="#glyph0-4" x="47.4201" y="144.60278"/>
  <use xlink:href="#glyph0-5" x="53.9199" y="144.60278"/>
  <use xlink:href="#glyph0-6" x="58.9257" y="144.60278"/>
  <use xlink:href="#glyph0-7" x="63.9315" y="144.60278"/>
</g>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 170.925781 53.949219 L 170.925781 49.25 " transform="matrix(1,0,0,1,-112,136)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:butt;stroke-linejoin:miter;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 170.925781 45.25 L 170.925781 49.25 M 169.425781 49.25 L 170.925781 45.25 L 172.425781 49.25 " transform="matrix(1,0,0,1,-112,136)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 170.925781 25.351562 L 170.925781 20.648438 " transform="matrix(1,0,0,1,-112,136)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:butt;stroke-linejoin:miter;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 170.925781 16.648438 L 170.925781 20.648438 M 169.425781 20.648438 L 170.925781 16.648438 L 172.425781 20.648438 " transform="matrix(1,0,0,1,-112,136)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 170.828125 -62.75 L 170.683594 -76.199219 " transform="matrix(1,0,0,1,-112,136)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:butt;stroke-linejoin:miter;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 170.640625 -80.199219 L 170.683594 -76.199219 M 169.183594 -76.183594 L 170.640625 -80.199219 L 172.183594 -76.214844 " transform="matrix(1,0,0,1,-112,136)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 170.648438 -95.597656 L 170.710938 -100 " transform="matrix(1,0,0,1,-112,136)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:butt;stroke-linejoin:miter;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 170.769531 -104 L 170.710938 -100 M 169.210938 -100.023438 L 170.769531 -104 L 172.210938 -99.980469 " transform="matrix(1,0,0,1,-112,136)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 170.925781 91.5 L 180.914062 91.5 C 180.921875 91.5 180.929688 91.5 180.9375 91.5 L 237.035156 91.023438 C 238.675781 91.011719 240.003906 89.675781 240.007812 88.035156 L 240.5 -80.09375 C 240.5 -80.097656 240.5 -80.101562 240.5 -80.105469 L 240.5 -85.851562 C 240.5 -87.507812 239.15625 -88.851562 237.5 -88.851562 L 183.199219 -88.851562 " transform="matrix(1,0,0,1,-112,136)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:butt;stroke-linejoin:miter;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 179.199219 -88.851562 L 183.199219 -88.851562 M 183.199219 -87.351562 L 179.199219 -88.851562 L 183.199219 -90.351562 " transform="matrix(1,0,0,1,-112,136)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 170.738281 -123.898438 L 170.625 -129.351562 " transform="matrix(1,0,0,1,-112,136)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:butt;stroke-linejoin:miter;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 170.539062 -133.351562 L 170.625 -129.351562 M 169.125 -129.320312 L 170.539062 -133.351562 L 172.121094 -129.382812 " transform="matrix(1,0,0,1,-112,136)"/>
<path style=" stroke:none;fill-rule:nonzero;fill:rgb(69.804382%,85.098267%,100%);fill-opacity:1;" d="M 11.925781 119.851562 L 105.925781 119.851562 L 105.925781 101.851562 L 11.925781 101.851562 Z M 11.925781 119.851562 "/>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 123.925781 -34.148438 L 217.925781 -34.148438 L 217.925781 -16.148438 L 123.925781 -16.148438 Z M 123.925781 -34.148438 " transform="matrix(1,0,0,1,-112,136)"/>
<g style="fill:rgb(0%,0%,0%);fill-opacity:1;">
  <use xlink:href="#glyph0-1" x="33.7521" y="114.6537"/>
  <use xlink:href="#glyph0-2" x="38.7579" y="114.6537"/>
</g>
<g clip-path="url(#clip2)" clip-rule="nonzero">
<g style="fill:rgb(0%,0%,0%);fill-opacity:1;">
  <use xlink:href="#glyph1-1" x="40.362" y="114.39061"/>
</g>
<g style="fill:rgb(0%,0%,0%);fill-opacity:1;">
  <use xlink:href="#glyph1-1" x="40.362" y="114.39061"/>
</g>
</g>
<g style="fill:rgb(0%,0%,0%);fill-opacity:1;">
  <use xlink:href="#glyph2-1" x="46.258" y="114.6537"/>
</g>
<g style="fill:rgb(0%,0%,0%);fill-opacity:1;">
  <use xlink:href="#glyph0-1" x="49.592" y="114.6537"/>
  <use xlink:href="#glyph0-2" x="54.5978" y="114.6537"/>
</g>
<g style="fill:rgb(0%,0%,0%);fill-opacity:1;">
  <use xlink:href="#glyph3-1" x="57.0979" y="114.6537"/>
  <use xlink:href="#glyph3-2" x="66.0979" y="114.6537"/>
  <use xlink:href="#glyph3-3" x="75.0979" y="114.6537"/>
</g>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 170.925781 -3.25 L 170.925781 -10.25 " transform="matrix(1,0,0,1,-112,136)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:butt;stroke-linejoin:miter;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 170.925781 -14.25 L 170.925781 -10.25 M 169.425781 -10.25 L 170.925781 -14.25 L 172.425781 -10.25 " transform="matrix(1,0,0,1,-112,136)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 123.925781 -62.75 L 217.925781 -62.75 L 217.925781 -44.75 L 123.925781 -44.75 Z M 123.925781 -62.75 " transform="matrix(1,0,0,1,-112,136)"/>
<g style="fill:rgb(0%,0%,0%);fill-opacity:1;">
  <use xlink:href="#glyph3-4" x="31.925" y="85.74999"/>
  <use xlink:href="#glyph3-5" x="40.925" y="85.74999"/>
  <use xlink:href="#glyph3-6" x="49.925" y="85.74999"/>
  <use xlink:href="#glyph3-7" x="58.925" y="85.74999"/>
  <use xlink:href="#glyph3-8" x="67.925" y="85.74999"/>
  <use xlink:href="#glyph3-3" x="76.925" y="85.74999"/>
</g>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 170.925781 -34.148438 L 170.925781 -38.851562 " transform="matrix(1,0,0,1,-112,136)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:butt;stroke-linejoin:miter;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 170.925781 -42.851562 L 170.925781 -38.851562 M 169.425781 -38.851562 L 170.925781 -42.851562 L 172.425781 -38.851562 " transform="matrix(1,0,0,1,-112,136)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-dasharray:4,4;stroke-miterlimit:10;" d="M 295 -71.800781 L 410.898438 -71.800781 L 410.898438 83.398438 L 295 83.398438 Z M 295 -71.800781 " transform="matrix(1,0,0,1,-112,136)"/>
<path style=" stroke:none;fill-rule:nonzero;fill:rgb(69.804382%,85.098267%,100%);fill-opacity:1;" d="M 194.324219 207.949219 L 288.324219 207.949219 L 288.324219 189.949219 L 194.324219 189.949219 Z M 194.324219 207.949219 "/>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 306.324219 53.949219 L 400.324219 53.949219 L 400.324219 71.949219 L 306.324219 71.949219 Z M 306.324219 53.949219 " transform="matrix(1,0,0,1,-112,136)"/>
<g style="fill:rgb(0%,0%,0%);fill-opacity:1;">
  <use xlink:href="#glyph0-1" x="216.1522" y="202.75371"/>
  <use xlink:href="#glyph0-2" x="221.158" y="202.75371"/>
</g>
<g clip-path="url(#clip3)" clip-rule="nonzero">
<g style="fill:rgb(0%,0%,0%);fill-opacity:1;">
  <use xlink:href="#glyph1-1" x="222.52001" y="202.73691"/>
</g>
<g style="fill:rgb(0%,0%,0%);fill-opacity:1;">
  <use xlink:href="#glyph1-1" x="222.52001" y="202.73691"/>
</g>
</g>
<g style="fill:rgb(0%,0%,0%);fill-opacity:1;">
  <use xlink:href="#glyph2-1" x="228.658" y="202.75371"/>
</g>
<g style="fill:rgb(0%,0%,0%);fill-opacity:1;">
  <use xlink:href="#glyph0-1" x="231.992" y="202.75371"/>
  <use xlink:href="#glyph0-2" x="236.9978" y="202.75371"/>
</g>
<g style="fill:rgb(0%,0%,0%);fill-opacity:1;">
  <use xlink:href="#glyph3-1" x="239.4979" y="202.75371"/>
  <use xlink:href="#glyph3-2" x="248.4979" y="202.75371"/>
  <use xlink:href="#glyph3-3" x="257.4979" y="202.75371"/>
</g>
<g style="fill:rgb(0%,0%,0%);fill-opacity:1;">
  <use xlink:href="#glyph4-1" x="239.075" y="249.05"/>
</g>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 353.324219 99.050781 L 353.324219 77.851562 " transform="matrix(1,0,0,1,-112,136)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:butt;stroke-linejoin:miter;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 353.324219 73.851562 L 353.324219 77.851562 M 351.824219 77.851562 L 353.324219 73.851562 L 354.824219 77.851562 " transform="matrix(1,0,0,1,-112,136)"/>
<path style="fill-rule:nonzero;fill:rgb(39.99939%,74.902344%,100%);fill-opacity:1;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 357.722656 -93.621094 C 360.359375 -90.988281 360.359375 -86.714844 357.722656 -84.078125 C 355.085938 -81.441406 350.8125 -81.441406 348.175781 -84.078125 C 345.539062 -86.714844 345.539062 -90.988281 348.175781 -93.621094 C 350.8125 -96.257812 355.085938 -96.257812 357.722656 -93.621094 " transform="matrix(1,0,0,1,-112,136)"/>
<g style="fill:rgb(0%,0%,0%);fill-opacity:1;">
  <use xlink:href="#glyph0-3" x="238.3221" y="50.00278"/>
</g>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 306.324219 25.351562 L 400.324219 25.351562 L 400.324219 43.351562 L 306.324219 43.351562 Z M 306.324219 25.351562 " transform="matrix(1,0,0,1,-112,136)"/>
<g style="fill:rgb(0%,0%,0%);fill-opacity:1;">
  <use xlink:href="#glyph3-4" x="214.325" y="173.85"/>
  <use xlink:href="#glyph3-5" x="223.325" y="173.85"/>
  <use xlink:href="#glyph3-6" x="232.325" y="173.85"/>
  <use xlink:href="#glyph3-7" x="241.325" y="173.85"/>
  <use xlink:href="#glyph3-8" x="250.325" y="173.85"/>
  <use xlink:href="#glyph3-3" x="259.325" y="173.85"/>
</g>
<path style=" stroke:none;fill-rule:nonzero;fill:rgb(39.99939%,74.902344%,100%);fill-opacity:1;" d="M 194.324219 30.101562 L 288.324219 30.101562 L 288.324219 12.101562 L 194.324219 12.101562 Z M 194.324219 30.101562 "/>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 306.324219 -123.898438 L 400.324219 -123.898438 L 400.324219 -105.898438 L 306.324219 -105.898438 Z M 306.324219 -123.898438 " transform="matrix(1,0,0,1,-112,136)"/>
<g style="fill:rgb(0%,0%,0%);fill-opacity:1;">
  <use xlink:href="#glyph0-4" x="229.8201" y="23.9528"/>
  <use xlink:href="#glyph0-5" x="236.3199" y="23.9528"/>
  <use xlink:href="#glyph0-6" x="241.3257" y="23.9528"/>
  <use xlink:href="#glyph0-7" x="246.3315" y="23.9528"/>
</g>
<path style=" stroke:none;fill-rule:nonzero;fill:rgb(39.99939%,74.902344%,100%);fill-opacity:1;" d="M 194.324219 150.75 L 288.324219 150.75 L 288.324219 132.75 L 194.324219 132.75 Z M 194.324219 150.75 "/>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 306.324219 -3.25 L 400.324219 -3.25 L 400.324219 14.75 L 306.324219 14.75 Z M 306.324219 -3.25 " transform="matrix(1,0,0,1,-112,136)"/>
<g style="fill:rgb(0%,0%,0%);fill-opacity:1;">
  <use xlink:href="#glyph0-4" x="229.8201" y="144.60278"/>
  <use xlink:href="#glyph0-5" x="236.3199" y="144.60278"/>
  <use xlink:href="#glyph0-6" x="241.3257" y="144.60278"/>
  <use xlink:href="#glyph0-7" x="246.3315" y="144.60278"/>
</g>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 353.324219 53.949219 L 353.324219 49.25 " transform="matrix(1,0,0,1,-112,136)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:butt;stroke-linejoin:miter;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 353.324219 45.25 L 353.324219 49.25 M 351.824219 49.25 L 353.324219 45.25 L 354.824219 49.25 " transform="matrix(1,0,0,1,-112,136)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 353.324219 25.351562 L 353.324219 20.648438 " transform="matrix(1,0,0,1,-112,136)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:butt;stroke-linejoin:miter;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 353.324219 16.648438 L 353.324219 20.648438 M 351.824219 20.648438 L 353.324219 16.648438 L 354.824219 20.648438 " transform="matrix(1,0,0,1,-112,136)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 353.230469 -62.75 L 353.085938 -76.199219 " transform="matrix(1,0,0,1,-112,136)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:butt;stroke-linejoin:miter;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 353.042969 -80.199219 L 353.085938 -76.199219 M 351.585938 -76.183594 L 353.042969 -80.199219 L 354.585938 -76.214844 " transform="matrix(1,0,0,1,-112,136)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 353.046875 -95.597656 L 353.109375 -100 " transform="matrix(1,0,0,1,-112,136)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:butt;stroke-linejoin:miter;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 353.167969 -104 L 353.109375 -100 M 351.609375 -100.023438 L 353.167969 -104 L 354.609375 -99.980469 " transform="matrix(1,0,0,1,-112,136)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 353.324219 91.5 L 470.074219 91.5 C 471.730469 91.5 473.074219 90.15625 473.074219 88.5 L 473.074219 20.699219 " transform="matrix(1,0,0,1,-112,136)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:butt;stroke-linejoin:miter;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 473.074219 16.699219 L 473.074219 20.699219 M 471.574219 20.699219 L 473.074219 16.699219 L 474.574219 20.699219 " transform="matrix(1,0,0,1,-112,136)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 353.136719 -123.898438 L 353.023438 -129.351562 " transform="matrix(1,0,0,1,-112,136)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:butt;stroke-linejoin:miter;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 352.941406 -133.351562 L 353.023438 -129.351562 M 351.523438 -129.320312 L 352.941406 -133.351562 L 354.523438 -129.382812 " transform="matrix(1,0,0,1,-112,136)"/>
<path style=" stroke:none;fill-rule:nonzero;fill:rgb(69.804382%,85.098267%,100%);fill-opacity:1;" d="M 194.324219 119.851562 L 288.324219 119.851562 L 288.324219 101.851562 L 194.324219 101.851562 Z M 194.324219 119.851562 "/>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 306.324219 -34.148438 L 400.324219 -34.148438 L 400.324219 -16.148438 L 306.324219 -16.148438 Z M 306.324219 -34.148438 " transform="matrix(1,0,0,1,-112,136)"/>
<g style="fill:rgb(0%,0%,0%);fill-opacity:1;">
  <use xlink:href="#glyph0-1" x="216.1522" y="114.6537"/>
  <use xlink:href="#glyph0-2" x="221.158" y="114.6537"/>
</g>
<g clip-path="url(#clip4)" clip-rule="nonzero">
<g style="fill:rgb(0%,0%,0%);fill-opacity:1;">
  <use xlink:href="#glyph1-1" x="222.52001" y="114.39061"/>
</g>
<g style="fill:rgb(0%,0%,0%);fill-opacity:1;">
  <use xlink:href="#glyph1-1" x="222.52001" y="114.39061"/>
</g>
</g>
<g style="fill:rgb(0%,0%,0%);fill-opacity:1;">
  <use xlink:href="#glyph2-1" x="228.658" y="114.6537"/>
</g>
<g style="fill:rgb(0%,0%,0%);fill-opacity:1;">
  <use xlink:href="#glyph0-1" x="231.992" y="114.6537"/>
  <use xlink:href="#glyph0-2" x="236.9978" y="114.6537"/>
</g>
<g style="fill:rgb(0%,0%,0%);fill-opacity:1;">
  <use xlink:href="#glyph3-1" x="239.4979" y="114.6537"/>
  <use xlink:href="#glyph3-2" x="248.4979" y="114.6537"/>
  <use xlink:href="#glyph3-3" x="257.4979" y="114.6537"/>
</g>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 353.324219 -3.25 L 353.324219 -10.25 " transform="matrix(1,0,0,1,-112,136)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:butt;stroke-linejoin:miter;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 353.324219 -14.25 L 353.324219 -10.25 M 351.824219 -10.25 L 353.324219 -14.25 L 354.824219 -10.25 " transform="matrix(1,0,0,1,-112,136)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 306.324219 -62.75 L 400.324219 -62.75 L 400.324219 -44.75 L 306.324219 -44.75 Z M 306.324219 -62.75 " transform="matrix(1,0,0,1,-112,136)"/>
<g style="fill:rgb(0%,0%,0%);fill-opacity:1;">
  <use xlink:href="#glyph3-4" x="214.325" y="85.74999"/>
  <use xlink:href="#glyph3-5" x="223.325" y="85.74999"/>
  <use xlink:href="#glyph3-6" x="232.325" y="85.74999"/>
  <use xlink:href="#glyph3-7" x="241.325" y="85.74999"/>
  <use xlink:href="#glyph3-8" x="250.325" y="85.74999"/>
  <use xlink:href="#glyph3-3" x="259.325" y="85.74999"/>
</g>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 353.324219 -34.148438 L 353.324219 -38.851562 " transform="matrix(1,0,0,1,-112,136)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:butt;stroke-linejoin:miter;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 353.324219 -42.851562 L 353.324219 -38.851562 M 351.824219 -38.851562 L 353.324219 -42.851562 L 354.824219 -38.851562 " transform="matrix(1,0,0,1,-112,136)"/>
<path style=" stroke:none;fill-rule:nonzero;fill:rgb(69.804382%,85.098267%,100%);fill-opacity:1;" d="M 314.074219 150.800781 L 408.074219 150.800781 L 408.074219 132.800781 L 314.074219 132.800781 Z M 314.074219 150.800781 "/>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 426.074219 -3.199219 L 520.074219 -3.199219 L 520.074219 14.800781 L 426.074219 14.800781 Z M 426.074219 -3.199219 " transform="matrix(1,0,0,1,-112,136)"/>
<g style="fill:rgb(0%,0%,0%);fill-opacity:1;">
  <use xlink:href="#glyph0-8" x="335.9022" y="145.603711"/>
  <use xlink:href="#glyph0-2" x="340.908" y="145.603711"/>
</g>
<g clip-path="url(#clip5)" clip-rule="nonzero">
<g style="fill:rgb(0%,0%,0%);fill-opacity:1;">
  <use xlink:href="#glyph1-1" x="342.362" y="145.34061"/>
</g>
<g style="fill:rgb(0%,0%,0%);fill-opacity:1;">
  <use xlink:href="#glyph1-1" x="342.362" y="145.34061"/>
</g>
</g>
<g style="fill:rgb(0%,0%,0%);fill-opacity:1;">
  <use xlink:href="#glyph2-1" x="348.408" y="145.603711"/>
</g>
<g style="fill:rgb(0%,0%,0%);fill-opacity:1;">
  <use xlink:href="#glyph0-8" x="351.742" y="145.603711"/>
  <use xlink:href="#glyph0-2" x="356.7478" y="145.603711"/>
</g>
<g style="fill:rgb(0%,0%,0%);fill-opacity:1;">
  <use xlink:href="#glyph3-1" x="359.2479" y="145.603711"/>
  <use xlink:href="#glyph3-2" x="368.2479" y="145.603711"/>
  <use xlink:href="#glyph3-3" x="377.2479" y="145.603711"/>
</g>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 473.074219 -3.199219 L 473.074219 -85.851562 C 473.074219 -87.507812 471.730469 -88.851562 470.074219 -88.851562 L 365.601562 -88.851562 " transform="matrix(1,0,0,1,-112,136)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:butt;stroke-linejoin:miter;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 361.601562 -88.851562 L 365.601562 -88.851562 M 365.601562 -87.351562 L 361.601562 -88.851562 L 365.601562 -90.351562 " transform="matrix(1,0,0,1,-112,136)"/>
</g>
</svg>
