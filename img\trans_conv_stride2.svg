<?xml version="1.0" encoding="UTF-8"?>
<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="395pt" height="260pt" viewBox="0 0 395 260" version="1.1">
<defs>
<g>
<symbol overflow="visible" id="glyph0-0">
<path style="stroke:none;" d="M 1.125 0 L 1.125 -5.625 L 5.625 -5.625 L 5.625 0 Z M 1.265625 -0.140625 L 5.484375 -0.140625 L 5.484375 -5.484375 L 1.265625 -5.484375 Z M 1.265625 -0.140625 "/>
</symbol>
<symbol overflow="visible" id="glyph0-1">
<path style="stroke:none;" d="M 0.375 -1.703125 L 1.171875 -1.8125 C 1.265625 -1.363281 1.414062 -1.039062 1.625 -0.84375 C 1.84375 -0.644531 2.113281 -0.546875 2.4375 -0.546875 C 2.800781 -0.546875 3.109375 -0.671875 3.359375 -0.921875 C 3.617188 -1.179688 3.75 -1.503906 3.75 -1.890625 C 3.75 -2.253906 3.628906 -2.550781 3.390625 -2.78125 C 3.160156 -3.019531 2.863281 -3.140625 2.5 -3.140625 C 2.34375 -3.140625 2.15625 -3.109375 1.9375 -3.046875 L 2.03125 -3.75 C 2.082031 -3.738281 2.125 -3.734375 2.15625 -3.734375 C 2.488281 -3.734375 2.789062 -3.820312 3.0625 -4 C 3.332031 -4.175781 3.46875 -4.445312 3.46875 -4.8125 C 3.46875 -5.101562 3.367188 -5.34375 3.171875 -5.53125 C 2.972656 -5.71875 2.71875 -5.8125 2.40625 -5.8125 C 2.101562 -5.8125 1.847656 -5.710938 1.640625 -5.515625 C 1.441406 -5.328125 1.3125 -5.039062 1.25 -4.65625 L 0.453125 -4.796875 C 0.554688 -5.328125 0.773438 -5.738281 1.109375 -6.03125 C 1.453125 -6.320312 1.878906 -6.46875 2.390625 -6.46875 C 2.742188 -6.46875 3.066406 -6.390625 3.359375 -6.234375 C 3.660156 -6.085938 3.890625 -5.882812 4.046875 -5.625 C 4.203125 -5.363281 4.28125 -5.085938 4.28125 -4.796875 C 4.28125 -4.515625 4.203125 -4.257812 4.046875 -4.03125 C 3.898438 -3.800781 3.679688 -3.617188 3.390625 -3.484375 C 3.773438 -3.398438 4.070312 -3.21875 4.28125 -2.9375 C 4.488281 -2.664062 4.59375 -2.320312 4.59375 -1.90625 C 4.59375 -1.34375 4.382812 -0.863281 3.96875 -0.46875 C 3.5625 -0.0820312 3.046875 0.109375 2.421875 0.109375 C 1.859375 0.109375 1.390625 -0.0546875 1.015625 -0.390625 C 0.640625 -0.722656 0.425781 -1.160156 0.375 -1.703125 Z M 0.375 -1.703125 "/>
</symbol>
<symbol overflow="visible" id="glyph0-2">
<path style="stroke:none;" d="M 4.53125 -0.765625 L 4.53125 0 L 0.265625 0 C 0.265625 -0.1875 0.296875 -0.367188 0.359375 -0.546875 C 0.472656 -0.835938 0.648438 -1.125 0.890625 -1.40625 C 1.128906 -1.6875 1.472656 -2.007812 1.921875 -2.375 C 2.617188 -2.957031 3.085938 -3.414062 3.328125 -3.75 C 3.578125 -4.082031 3.703125 -4.398438 3.703125 -4.703125 C 3.703125 -5.015625 3.585938 -5.273438 3.359375 -5.484375 C 3.140625 -5.703125 2.851562 -5.8125 2.5 -5.8125 C 2.113281 -5.8125 1.804688 -5.695312 1.578125 -5.46875 C 1.347656 -5.238281 1.234375 -4.921875 1.234375 -4.515625 L 0.421875 -4.609375 C 0.472656 -5.210938 0.679688 -5.671875 1.046875 -5.984375 C 1.410156 -6.304688 1.898438 -6.46875 2.515625 -6.46875 C 3.128906 -6.46875 3.613281 -6.296875 3.96875 -5.953125 C 4.332031 -5.609375 4.515625 -5.1875 4.515625 -4.6875 C 4.515625 -4.425781 4.460938 -4.171875 4.359375 -3.921875 C 4.253906 -3.671875 4.078125 -3.40625 3.828125 -3.125 C 3.585938 -2.851562 3.1875 -2.476562 2.625 -2 C 2.144531 -1.601562 1.835938 -1.332031 1.703125 -1.1875 C 1.566406 -1.039062 1.457031 -0.898438 1.375 -0.765625 Z M 4.53125 -0.765625 "/>
</symbol>
<symbol overflow="visible" id="glyph0-3">
<path style="stroke:none;" d="M 3.359375 0 L 2.5625 0 L 2.5625 -5.046875 C 2.375 -4.859375 2.125 -4.671875 1.8125 -4.484375 C 1.5 -4.304688 1.222656 -4.175781 0.984375 -4.09375 L 0.984375 -4.859375 C 1.421875 -5.054688 1.804688 -5.300781 2.140625 -5.59375 C 2.472656 -5.894531 2.707031 -6.1875 2.84375 -6.46875 L 3.359375 -6.46875 Z M 3.359375 0 "/>
</symbol>
<symbol overflow="visible" id="glyph0-4">
<path style="stroke:none;" d="M 0.375 -3.171875 C 0.375 -3.929688 0.453125 -4.546875 0.609375 -5.015625 C 0.765625 -5.484375 0.992188 -5.84375 1.296875 -6.09375 C 1.609375 -6.34375 2 -6.46875 2.46875 -6.46875 C 2.820312 -6.46875 3.128906 -6.394531 3.390625 -6.25 C 3.648438 -6.113281 3.863281 -5.914062 4.03125 -5.65625 C 4.195312 -5.394531 4.328125 -5.078125 4.421875 -4.703125 C 4.523438 -4.328125 4.578125 -3.816406 4.578125 -3.171875 C 4.578125 -2.421875 4.5 -1.8125 4.34375 -1.34375 C 4.1875 -0.882812 3.953125 -0.523438 3.640625 -0.265625 C 3.335938 -0.015625 2.945312 0.109375 2.46875 0.109375 C 1.851562 0.109375 1.367188 -0.113281 1.015625 -0.5625 C 0.585938 -1.09375 0.375 -1.960938 0.375 -3.171875 Z M 1.1875 -3.171875 C 1.1875 -2.117188 1.304688 -1.414062 1.546875 -1.0625 C 1.796875 -0.71875 2.101562 -0.546875 2.46875 -0.546875 C 2.832031 -0.546875 3.140625 -0.71875 3.390625 -1.0625 C 3.640625 -1.414062 3.765625 -2.117188 3.765625 -3.171875 C 3.765625 -4.234375 3.640625 -4.9375 3.390625 -5.28125 C 3.140625 -5.632812 2.832031 -5.8125 2.46875 -5.8125 C 2.101562 -5.8125 1.8125 -5.660156 1.59375 -5.359375 C 1.320312 -4.960938 1.1875 -4.234375 1.1875 -3.171875 Z M 1.1875 -3.171875 "/>
</symbol>
<symbol overflow="visible" id="glyph0-5">
<path style="stroke:none;" d="M 4.75 -3.78125 L 0.5 -3.78125 L 0.5 -4.53125 L 4.75 -4.53125 Z M 4.75 -1.828125 L 0.5 -1.828125 L 0.5 -2.578125 L 4.75 -2.578125 Z M 4.75 -1.828125 "/>
</symbol>
<symbol overflow="visible" id="glyph0-6">
<path style="stroke:none;" d="M 4.484375 -4.859375 L 3.6875 -4.796875 C 3.625 -5.109375 3.523438 -5.335938 3.390625 -5.484375 C 3.179688 -5.703125 2.921875 -5.8125 2.609375 -5.8125 C 2.347656 -5.8125 2.125 -5.742188 1.9375 -5.609375 C 1.6875 -5.421875 1.488281 -5.15625 1.34375 -4.8125 C 1.207031 -4.46875 1.132812 -3.972656 1.125 -3.328125 C 1.3125 -3.609375 1.539062 -3.816406 1.8125 -3.953125 C 2.09375 -4.097656 2.382812 -4.171875 2.6875 -4.171875 C 3.21875 -4.171875 3.664062 -3.976562 4.03125 -3.59375 C 4.40625 -3.207031 4.59375 -2.707031 4.59375 -2.09375 C 4.59375 -1.6875 4.503906 -1.304688 4.328125 -0.953125 C 4.148438 -0.609375 3.910156 -0.34375 3.609375 -0.15625 C 3.304688 0.0195312 2.960938 0.109375 2.578125 0.109375 C 1.921875 0.109375 1.382812 -0.128906 0.96875 -0.609375 C 0.550781 -1.097656 0.34375 -1.898438 0.34375 -3.015625 C 0.34375 -4.253906 0.570312 -5.160156 1.03125 -5.734375 C 1.425781 -6.222656 1.96875 -6.46875 2.65625 -6.46875 C 3.15625 -6.46875 3.566406 -6.320312 3.890625 -6.03125 C 4.210938 -5.75 4.410156 -5.359375 4.484375 -4.859375 Z M 1.25 -2.09375 C 1.25 -1.8125 1.304688 -1.546875 1.421875 -1.296875 C 1.535156 -1.054688 1.695312 -0.867188 1.90625 -0.734375 C 2.113281 -0.609375 2.332031 -0.546875 2.5625 -0.546875 C 2.894531 -0.546875 3.179688 -0.675781 3.421875 -0.9375 C 3.660156 -1.207031 3.78125 -1.578125 3.78125 -2.046875 C 3.78125 -2.492188 3.660156 -2.84375 3.421875 -3.09375 C 3.191406 -3.351562 2.894531 -3.484375 2.53125 -3.484375 C 2.175781 -3.484375 1.875 -3.351562 1.625 -3.09375 C 1.375 -2.84375 1.25 -2.507812 1.25 -2.09375 Z M 1.25 -2.09375 "/>
</symbol>
<symbol overflow="visible" id="glyph0-7">
<path style="stroke:none;" d="M 2.90625 0 L 2.90625 -1.546875 L 0.109375 -1.546875 L 0.109375 -2.265625 L 3.046875 -6.4375 L 3.703125 -6.4375 L 3.703125 -2.265625 L 4.578125 -2.265625 L 4.578125 -1.546875 L 3.703125 -1.546875 L 3.703125 0 Z M 2.90625 -2.265625 L 2.90625 -5.171875 L 0.890625 -2.265625 Z M 2.90625 -2.265625 "/>
</symbol>
<symbol overflow="visible" id="glyph0-8">
<path style="stroke:none;" d="M 0.5 -1.484375 L 1.25 -1.5625 C 1.3125 -1.207031 1.429688 -0.945312 1.609375 -0.78125 C 1.796875 -0.625 2.035156 -0.546875 2.328125 -0.546875 C 2.566406 -0.546875 2.773438 -0.597656 2.953125 -0.703125 C 3.140625 -0.816406 3.289062 -0.96875 3.40625 -1.15625 C 3.53125 -1.34375 3.628906 -1.59375 3.703125 -1.90625 C 3.785156 -2.21875 3.828125 -2.539062 3.828125 -2.875 C 3.828125 -2.90625 3.820312 -2.957031 3.8125 -3.03125 C 3.65625 -2.78125 3.441406 -2.578125 3.171875 -2.421875 C 2.898438 -2.265625 2.601562 -2.1875 2.28125 -2.1875 C 1.75 -2.1875 1.296875 -2.378906 0.921875 -2.765625 C 0.554688 -3.148438 0.375 -3.660156 0.375 -4.296875 C 0.375 -4.953125 0.566406 -5.476562 0.953125 -5.875 C 1.335938 -6.269531 1.820312 -6.46875 2.40625 -6.46875 C 2.820312 -6.46875 3.203125 -6.351562 3.546875 -6.125 C 3.890625 -5.90625 4.148438 -5.585938 4.328125 -5.171875 C 4.515625 -4.753906 4.609375 -4.148438 4.609375 -3.359375 C 4.609375 -2.535156 4.519531 -1.878906 4.34375 -1.390625 C 4.164062 -0.898438 3.898438 -0.523438 3.546875 -0.265625 C 3.191406 -0.015625 2.773438 0.109375 2.296875 0.109375 C 1.796875 0.109375 1.382812 -0.03125 1.0625 -0.3125 C 0.75 -0.59375 0.5625 -0.984375 0.5 -1.484375 Z M 3.734375 -4.328125 C 3.734375 -4.785156 3.609375 -5.144531 3.359375 -5.40625 C 3.117188 -5.675781 2.832031 -5.8125 2.5 -5.8125 C 2.144531 -5.8125 1.835938 -5.664062 1.578125 -5.375 C 1.316406 -5.09375 1.1875 -4.722656 1.1875 -4.265625 C 1.1875 -3.859375 1.304688 -3.523438 1.546875 -3.265625 C 1.796875 -3.015625 2.101562 -2.890625 2.46875 -2.890625 C 2.84375 -2.890625 3.144531 -3.015625 3.375 -3.265625 C 3.613281 -3.523438 3.734375 -3.878906 3.734375 -4.328125 Z M 3.734375 -4.328125 "/>
</symbol>
<symbol overflow="visible" id="glyph0-9">
<path style="stroke:none;" d="M 2.25 -1.046875 L 2.25 -2.8125 L 0.5 -2.8125 L 0.5 -3.546875 L 2.25 -3.546875 L 2.25 -5.296875 L 3 -5.296875 L 3 -3.546875 L 4.75 -3.546875 L 4.75 -2.8125 L 3 -2.8125 L 3 -1.046875 Z M 2.25 -1.046875 "/>
</symbol>
<symbol overflow="visible" id="glyph0-10">
<path style="stroke:none;" d="M 2.109375 1.890625 C 1.671875 1.335938 1.300781 0.695312 1 -0.03125 C 0.695312 -0.769531 0.546875 -1.535156 0.546875 -2.328125 C 0.546875 -3.023438 0.660156 -3.695312 0.890625 -4.34375 C 1.148438 -5.082031 1.554688 -5.816406 2.109375 -6.546875 L 2.671875 -6.546875 C 2.316406 -5.941406 2.082031 -5.507812 1.96875 -5.25 C 1.789062 -4.84375 1.648438 -4.421875 1.546875 -3.984375 C 1.421875 -3.429688 1.359375 -2.878906 1.359375 -2.328125 C 1.359375 -0.921875 1.796875 0.484375 2.671875 1.890625 Z M 2.109375 1.890625 "/>
</symbol>
<symbol overflow="visible" id="glyph0-11">
<path style="stroke:none;" d="M 1.109375 1.890625 L 0.546875 1.890625 C 1.421875 0.484375 1.859375 -0.921875 1.859375 -2.328125 C 1.859375 -2.878906 1.796875 -3.425781 1.671875 -3.96875 C 1.566406 -4.40625 1.425781 -4.828125 1.25 -5.234375 C 1.132812 -5.492188 0.898438 -5.929688 0.546875 -6.546875 L 1.109375 -6.546875 C 1.660156 -5.816406 2.066406 -5.082031 2.328125 -4.34375 C 2.554688 -3.695312 2.671875 -3.023438 2.671875 -2.328125 C 2.671875 -1.535156 2.519531 -0.769531 2.21875 -0.03125 C 1.914062 0.695312 1.546875 1.335938 1.109375 1.890625 Z M 1.109375 1.890625 "/>
</symbol>
<symbol overflow="visible" id="glyph1-0">
<path style="stroke:none;" d=""/>
</symbol>
<symbol overflow="visible" id="glyph1-1">
<path style="stroke:none;" d="M 2.0625 -4.84375 L 2.0625 -3.515625 L 1.171875 -3.515625 C 1.390625 -4.09375 1.609375 -4.75 1.796875 -5.484375 L 3.53125 -5.484375 L 3.53125 -6.09375 L 1.953125 -6.09375 C 2.03125 -6.453125 2.109375 -6.84375 2.203125 -7.25 L 1.609375 -7.375 C 1.515625 -6.9375 1.4375 -6.5 1.34375 -6.09375 L 0.453125 -6.09375 L 0.453125 -5.484375 L 1.203125 -5.484375 C 1 -4.6875 0.78125 -4 0.515625 -3.46875 L 0.65625 -2.921875 L 2.0625 -2.921875 L 2.0625 -1.578125 C 1.53125 -1.484375 0.984375 -1.390625 0.390625 -1.328125 L 0.46875 -0.6875 C 1.015625 -0.78125 1.546875 -0.859375 2.0625 -0.96875 L 2.0625 0.90625 L 2.65625 0.90625 L 2.65625 -1.09375 L 3.515625 -1.3125 L 3.515625 -1.921875 C 3.25 -1.859375 2.96875 -1.78125 2.65625 -1.703125 L 2.65625 -2.921875 L 3.484375 -2.921875 L 3.484375 -3.515625 L 2.65625 -3.515625 L 2.65625 -4.84375 Z M 4.71875 -5.3125 L 4.71875 -4.78125 L 7.296875 -4.78125 L 7.296875 -5.3125 L 4.71875 -5.3125 C 5.171875 -5.75 5.609375 -6.234375 6.03125 -6.75 C 6.71875 -5.875 7.484375 -5.125 8.328125 -4.515625 L 8.671875 -5.015625 C 7.859375 -5.59375 7.0625 -6.359375 6.265625 -7.328125 L 5.8125 -7.328125 C 5.078125 -6.4375 4.25 -5.640625 3.328125 -4.96875 L 3.6875 -4.421875 C 4.03125 -4.703125 4.375 -5 4.71875 -5.3125 Z M 5.34375 0.84375 C 5.78125 0.84375 6.015625 0.609375 6.015625 0.140625 L 6.015625 -4.0625 L 3.890625 -4.0625 L 3.890625 0.90625 L 4.453125 0.90625 L 4.453125 -0.78125 L 5.46875 -0.78125 L 5.46875 0.03125 C 5.46875 0.21875 5.359375 0.328125 5.171875 0.328125 L 4.765625 0.3125 L 4.90625 0.84375 Z M 4.453125 -1.28125 L 4.453125 -2.140625 L 5.46875 -2.140625 L 5.46875 -1.28125 Z M 4.453125 -2.640625 L 4.453125 -3.53125 L 5.46875 -3.53125 L 5.46875 -2.640625 Z M 6.546875 -3.8125 L 6.546875 -0.578125 L 7.0625 -0.578125 L 7.0625 -3.8125 Z M 7.53125 0.859375 C 7.96875 0.859375 8.1875 0.59375 8.1875 0.078125 L 8.1875 -4.21875 L 7.625 -4.21875 L 7.625 -0.0625 C 7.625 0.1875 7.53125 0.328125 7.328125 0.328125 C 7.109375 0.328125 6.875 0.3125 6.640625 0.28125 L 6.765625 0.859375 Z M 7.53125 0.859375 "/>
</symbol>
<symbol overflow="visible" id="glyph1-2">
<path style="stroke:none;" d="M 6.9375 -4.21875 L 4.828125 -4.21875 L 4.828125 -7.359375 L 4.171875 -7.359375 L 4.171875 -4.21875 L 2.0625 -4.21875 L 2.0625 -6.671875 L 1.40625 -6.671875 L 1.40625 -3.59375 L 4.171875 -3.59375 L 4.171875 -0.28125 L 1.625 -0.28125 L 1.625 -2.75 L 0.96875 -2.75 L 0.96875 0.84375 L 1.625 0.84375 L 1.625 0.328125 L 7.375 0.328125 L 7.375 0.84375 L 8.03125 0.84375 L 8.03125 -2.75 L 7.375 -2.75 L 7.375 -0.28125 L 4.828125 -0.28125 L 4.828125 -3.59375 L 7.59375 -3.59375 L 7.59375 -6.671875 L 6.9375 -6.671875 Z M 6.9375 -4.21875 "/>
</symbol>
<symbol overflow="visible" id="glyph1-3">
<path style="stroke:none;" d="M 2.34375 -3.6875 C 2.625 -3.296875 2.9375 -2.78125 3.296875 -2.15625 L 3.65625 -2.703125 C 3.21875 -3.328125 2.78125 -3.890625 2.34375 -4.390625 L 2.34375 -4.96875 L 3.40625 -4.96875 L 3.40625 -5.59375 L 2.34375 -5.59375 L 2.34375 -7.34375 L 1.734375 -7.34375 L 1.734375 -5.59375 L 0.484375 -5.59375 L 0.484375 -4.96875 L 1.703125 -4.96875 C 1.4375 -3.78125 0.96875 -2.71875 0.328125 -1.78125 L 0.609375 -1.125 C 1.0625 -1.859375 1.4375 -2.6875 1.734375 -3.609375 L 1.734375 0.90625 L 2.34375 0.90625 Z M 3.640625 -6.359375 L 3.640625 -5.78125 L 5.375 -5.78125 C 4.8125 -4.796875 4.34375 -4.1875 3.984375 -3.953125 C 3.953125 -3.9375 3.921875 -3.921875 3.859375 -3.90625 L 4 -3.375 C 4.703125 -3.40625 5.375 -3.4375 6.015625 -3.46875 C 5.28125 -2.640625 4.359375 -2.046875 3.28125 -1.65625 L 3.625 -1.109375 C 5.375 -1.765625 6.6875 -2.984375 7.59375 -4.765625 L 7.0625 -5.046875 C 6.890625 -4.6875 6.671875 -4.359375 6.4375 -4.03125 C 5.890625 -3.984375 5.3125 -3.953125 4.75 -3.921875 C 5.125 -4.28125 5.546875 -4.90625 6.046875 -5.78125 L 8.515625 -5.78125 L 8.515625 -6.359375 L 6.515625 -6.359375 C 6.390625 -6.734375 6.25 -7.078125 6.125 -7.359375 L 5.453125 -7.25 C 5.609375 -6.984375 5.765625 -6.6875 5.890625 -6.359375 Z M 6.515625 -0.921875 C 7.1875 -0.296875 7.71875 0.296875 8.109375 0.859375 L 8.578125 0.390625 C 8.125 -0.1875 7.5625 -0.75 6.921875 -1.328125 C 7.5 -1.921875 8.015625 -2.625 8.453125 -3.421875 L 7.90625 -3.71875 C 6.84375 -1.75 5.265625 -0.40625 3.15625 0.28125 L 3.5 0.84375 C 4.65625 0.4375 5.65625 -0.15625 6.515625 -0.921875 Z M 6.515625 -0.921875 "/>
</symbol>
<symbol overflow="visible" id="glyph1-4">
<path style="stroke:none;" d="M 4.28125 -7.34375 L 4.28125 -3.625 L 3.53125 -3.625 L 3.53125 -3 L 4.28125 -3 L 4.28125 -0.015625 C 4.28125 0.140625 4.1875 0.25 4 0.3125 L 4.296875 0.90625 C 5.140625 0.671875 5.890625 0.359375 6.53125 -0.015625 L 6.421875 -0.640625 C 5.90625 -0.328125 5.40625 -0.09375 4.90625 0.078125 L 4.90625 -3 L 5.875 -3 C 6.40625 -1.34375 7.1875 -0.09375 8.234375 0.78125 L 8.671875 0.28125 C 7.71875 -0.484375 6.96875 -1.578125 6.4375 -3 L 8.515625 -3 L 8.515625 -3.625 L 4.90625 -3.625 L 4.90625 -7.34375 Z M 7.71875 -6.890625 C 7.203125 -6.09375 6.421875 -5.375 5.359375 -4.75 L 5.734375 -4.25 C 6.828125 -4.890625 7.625 -5.609375 8.171875 -6.421875 Z M 1.375 -4.390625 L 3.03125 -4.390625 L 3.03125 -7 L 0.5 -7 L 0.5 -6.390625 L 2.40625 -6.390625 L 2.40625 -4.984375 L 0.796875 -4.984375 L 0.515625 -2.359375 L 2.53125 -2.359375 C 2.53125 -1.28125 2.484375 -0.578125 2.40625 -0.25 C 2.328125 0.078125 2.015625 0.25 1.5 0.25 C 1.234375 0.25 1 0.21875 0.765625 0.203125 L 0.921875 0.796875 C 1.15625 0.8125 1.40625 0.84375 1.640625 0.84375 C 2.453125 0.796875 2.90625 0.53125 3.015625 0.015625 C 3.109375 -0.453125 3.171875 -1.453125 3.171875 -2.9375 L 1.203125 -2.9375 Z M 1.375 -4.390625 "/>
</symbol>
<symbol overflow="visible" id="glyph1-5">
<path style="stroke:none;" d="M 1.375 -3.578125 L 1.375 -1.28125 L 4.171875 -1.28125 L 4.171875 -0.78125 L 1.15625 -0.78125 L 1.15625 -0.34375 L 4.171875 -0.34375 L 4.171875 0.1875 L 0.484375 0.1875 L 0.484375 0.6875 L 8.515625 0.6875 L 8.515625 0.1875 L 4.8125 0.1875 L 4.8125 -0.34375 L 7.859375 -0.34375 L 7.859375 -0.78125 L 4.8125 -0.78125 L 4.8125 -1.28125 L 7.640625 -1.28125 L 7.640625 -3.578125 Z M 7.015625 -1.6875 L 4.8125 -1.6875 L 4.8125 -2.234375 L 7.015625 -2.234375 Z M 4.171875 -1.6875 L 2 -1.6875 L 2 -2.234375 L 4.171875 -2.234375 Z M 2 -2.625 L 2 -3.171875 L 4.171875 -3.171875 L 4.171875 -2.625 Z M 4.8125 -3.171875 L 7.015625 -3.171875 L 7.015625 -2.625 L 4.8125 -2.625 Z M 1.578125 -7.203125 L 1.578125 -4.984375 L 7.453125 -4.984375 L 7.453125 -7.203125 Z M 6.828125 -5.375 L 2.203125 -5.375 L 2.203125 -5.90625 L 6.828125 -5.90625 Z M 2.203125 -6.296875 L 2.203125 -6.8125 L 6.828125 -6.8125 L 6.828125 -6.296875 Z M 0.46875 -4.515625 L 0.46875 -4.03125 L 8.53125 -4.03125 L 8.53125 -4.515625 Z M 0.46875 -4.515625 "/>
</symbol>
<symbol overflow="visible" id="glyph1-6">
<path style="stroke:none;" d="M 4.109375 -5.265625 C 3.53125 -2.875 2.28125 -1.03125 0.34375 0.21875 L 0.734375 0.78125 C 2.625 -0.453125 3.875 -2.21875 4.46875 -4.515625 C 4.515625 -4.40625 4.546875 -4.28125 4.609375 -4.125 C 5.53125 -1.75 6.71875 -0.109375 8.1875 0.765625 L 8.59375 0.25 C 7.15625 -0.640625 6.03125 -2.171875 5.1875 -4.3125 C 4.640625 -5.78125 3.953125 -6.796875 3.15625 -7.359375 L 2.625 -7.015625 C 3.1875 -6.59375 3.6875 -6.015625 4.109375 -5.265625 Z M 4.109375 -5.265625 "/>
</symbol>
<symbol overflow="visible" id="glyph1-7">
<path style="stroke:none;" d="M 2.171875 -4.875 L 2.171875 -3.515625 L 1.1875 -3.515625 C 1.4375 -4.09375 1.671875 -4.765625 1.890625 -5.546875 L 3.765625 -5.546875 L 3.765625 -6.171875 L 2.046875 -6.171875 C 2.140625 -6.53125 2.21875 -6.890625 2.296875 -7.28125 L 1.6875 -7.375 C 1.59375 -6.953125 1.515625 -6.546875 1.421875 -6.171875 L 0.359375 -6.171875 L 0.359375 -5.546875 L 1.265625 -5.546875 C 1.03125 -4.71875 0.796875 -4.046875 0.53125 -3.5 L 0.671875 -2.921875 L 2.171875 -2.921875 L 2.171875 -1.578125 C 1.609375 -1.46875 1 -1.390625 0.34375 -1.3125 L 0.4375 -0.671875 C 1.046875 -0.75 1.625 -0.84375 2.171875 -0.9375 L 2.171875 0.890625 L 2.796875 0.890625 L 2.796875 -1.0625 C 3.234375 -1.15625 3.640625 -1.265625 4.046875 -1.375 L 4.046875 -1.984375 C 3.65625 -1.875 3.234375 -1.78125 2.796875 -1.6875 L 2.796875 -2.921875 L 3.796875 -2.921875 L 3.796875 -3.515625 L 2.796875 -3.515625 L 2.796875 -4.875 Z M 4.171875 -6.171875 L 4.171875 -5.546875 L 5.46875 -5.546875 C 5.390625 -5.203125 5.3125 -4.859375 5.234375 -4.515625 L 3.921875 -4.515625 L 3.921875 -3.890625 L 5.078125 -3.890625 C 4.921875 -3.296875 4.75 -2.734375 4.578125 -2.171875 L 7.59375 -2.171875 C 7.265625 -1.640625 6.84375 -1.15625 6.3125 -0.671875 C 5.890625 -0.90625 5.453125 -1.140625 4.984375 -1.328125 L 4.640625 -0.8125 C 5.640625 -0.40625 6.59375 0.171875 7.53125 0.890625 L 7.890625 0.34375 C 7.5625 0.09375 7.203125 -0.140625 6.84375 -0.375 C 7.484375 -0.984375 7.953125 -1.59375 8.25 -2.203125 L 8.25 -2.75 L 5.375 -2.75 C 5.5 -3.09375 5.609375 -3.46875 5.71875 -3.890625 L 8.65625 -3.890625 L 8.65625 -4.515625 L 5.875 -4.515625 L 6.109375 -5.546875 L 8.421875 -5.546875 L 8.421875 -6.171875 L 6.234375 -6.171875 C 6.3125 -6.546875 6.375 -6.9375 6.4375 -7.328125 L 5.8125 -7.390625 C 5.734375 -6.96875 5.65625 -6.5625 5.59375 -6.171875 Z M 4.171875 -6.171875 "/>
</symbol>
<symbol overflow="visible" id="glyph1-8">
<path style="stroke:none;" d="M 0.640625 -4.8125 L 0.640625 -4.3125 L 4.0625 -4.3125 C 4.03125 -4.140625 4 -3.96875 3.96875 -3.796875 L 1.78125 -3.796875 L 1.78125 0.171875 L 0.484375 0.171875 L 0.484375 0.6875 L 8.53125 0.6875 L 8.53125 0.171875 L 7.234375 0.171875 L 7.234375 -3.796875 L 4.609375 -3.796875 C 4.65625 -3.953125 4.6875 -4.125 4.71875 -4.3125 L 8.375 -4.3125 L 8.375 -4.8125 L 4.78125 -4.8125 L 4.84375 -5.328125 L 7.765625 -5.328125 L 7.765625 -7.015625 L 1.234375 -7.015625 L 1.234375 -5.328125 L 4.1875 -5.328125 L 4.140625 -4.8125 Z M 2.40625 0.171875 L 2.40625 -0.421875 L 6.59375 -0.421875 L 6.59375 0.171875 Z M 2.40625 -0.828125 L 2.40625 -1.390625 L 6.59375 -1.390625 L 6.59375 -0.828125 Z M 2.40625 -1.8125 L 2.40625 -2.375 L 6.59375 -2.375 L 6.59375 -1.8125 Z M 2.40625 -2.78125 L 2.40625 -3.34375 L 6.59375 -3.34375 L 6.59375 -2.78125 Z M 7.140625 -5.78125 L 5.8125 -5.78125 L 5.8125 -6.53125 L 7.140625 -6.53125 Z M 5.21875 -5.78125 L 3.78125 -5.78125 L 3.78125 -6.53125 L 5.21875 -6.53125 Z M 3.203125 -5.78125 L 1.859375 -5.78125 L 1.859375 -6.53125 L 3.203125 -6.53125 Z M 3.203125 -5.78125 "/>
</symbol>
<symbol overflow="visible" id="glyph1-9">
<path style="stroke:none;" d="M 2.359375 -7.234375 L 1.78125 -7.03125 C 2.046875 -6.671875 2.296875 -6.25 2.53125 -5.78125 L 3.078125 -6.03125 C 2.875 -6.453125 2.640625 -6.84375 2.359375 -7.234375 Z M 6.78125 -7.234375 C 6.5625 -6.78125 6.296875 -6.390625 5.953125 -6.0625 L 6.46875 -5.765625 C 6.828125 -6.125 7.125 -6.546875 7.359375 -7.015625 Z M 1.15625 -5.71875 L 1.15625 -5.125 L 3.765625 -5.125 C 3.625 -4.8125 3.4375 -4.5 3.234375 -4.21875 L 0.640625 -4.21875 L 0.640625 -3.625 L 2.78125 -3.625 C 2.15625 -2.921875 1.359375 -2.34375 0.390625 -1.921875 L 0.75 -1.359375 C 1.34375 -1.640625 1.890625 -1.984375 2.375 -2.375 L 2.375 0.078125 C 2.375 0.546875 2.640625 0.78125 3.1875 0.78125 L 6.515625 0.78125 C 7 0.78125 7.3125 0.703125 7.5 0.578125 C 7.6875 0.40625 7.8125 -0.03125 7.890625 -0.734375 L 7.25 -0.9375 C 7.203125 -0.484375 7.125 -0.1875 7.03125 -0.015625 C 6.953125 0.109375 6.71875 0.1875 6.34375 0.1875 L 3.453125 0.1875 C 3.171875 0.1875 3.03125 0.078125 3.03125 -0.125 L 3.03125 -2.15625 L 5.921875 -2.15625 L 5.921875 -1.484375 C 5.921875 -1.296875 5.796875 -1.203125 5.5625 -1.203125 C 5.1875 -1.203125 4.78125 -1.21875 4.328125 -1.234375 L 4.46875 -0.671875 C 4.90625 -0.65625 5.34375 -0.640625 5.8125 -0.640625 C 6.296875 -0.671875 6.546875 -0.890625 6.5625 -1.3125 L 6.5625 -2.53125 C 7.015625 -2.15625 7.5625 -1.796875 8.203125 -1.453125 L 8.625 -1.984375 C 7.53125 -2.46875 6.71875 -3 6.234375 -3.625 L 8.359375 -3.625 L 8.359375 -4.21875 L 3.984375 -4.21875 C 4.15625 -4.5 4.296875 -4.8125 4.453125 -5.125 L 7.859375 -5.125 L 7.859375 -5.71875 L 4.671875 -5.71875 C 4.828125 -6.203125 4.96875 -6.734375 5.0625 -7.296875 L 4.421875 -7.375 C 4.328125 -6.78125 4.1875 -6.234375 4.015625 -5.71875 Z M 3.578125 -3.625 L 5.578125 -3.625 C 5.78125 -3.3125 6.03125 -3.015625 6.34375 -2.734375 L 2.78125 -2.734375 C 3.0625 -3 3.328125 -3.296875 3.578125 -3.625 Z M 3.578125 -3.625 "/>
</symbol>
<symbol overflow="visible" id="glyph1-10">
<path style="stroke:none;" d="M 0.6875 -5.96875 C 1.125 -6.015625 1.53125 -6.09375 1.921875 -6.171875 L 1.921875 -4.890625 L 0.59375 -4.890625 L 0.59375 -4.265625 L 1.890625 -4.265625 C 1.578125 -3.21875 1.046875 -2.296875 0.328125 -1.484375 L 0.59375 -0.8125 C 1.140625 -1.515625 1.578125 -2.328125 1.921875 -3.203125 L 1.921875 0.859375 L 2.53125 0.859375 L 2.53125 -3.25 C 2.796875 -2.890625 3.109375 -2.390625 3.484375 -1.765625 L 3.84375 -2.296875 C 3.390625 -2.859375 2.96875 -3.359375 2.53125 -3.8125 L 2.53125 -4.265625 L 3.640625 -4.265625 L 3.640625 -4.890625 L 2.53125 -4.890625 L 2.53125 -6.3125 C 2.90625 -6.40625 3.265625 -6.5 3.59375 -6.625 L 3.359375 -7.234375 C 2.5625 -6.90625 1.625 -6.703125 0.5625 -6.59375 Z M 4.171875 -6.8125 L 4.171875 -2.625 L 8.0625 -2.625 L 8.0625 -6.8125 Z M 7.421875 -3.234375 L 4.828125 -3.234375 L 4.828125 -6.1875 L 7.421875 -6.1875 Z M 4.953125 -2.046875 C 4.546875 -1.046875 4.046875 -0.1875 3.46875 0.53125 L 4.015625 0.921875 C 4.59375 0.15625 5.109375 -0.734375 5.53125 -1.765625 Z M 7.15625 -2.09375 L 6.640625 -1.765625 C 7.265625 -0.796875 7.78125 0.09375 8.15625 0.90625 L 8.703125 0.515625 C 8.34375 -0.21875 7.828125 -1.09375 7.15625 -2.09375 Z M 7.15625 -2.09375 "/>
</symbol>
<symbol overflow="visible" id="glyph1-11">
<path style="stroke:none;" d="M 4.21875 -7.375 L 4.21875 -4.421875 L 2.53125 -4.421875 L 2.53125 -6.671875 L 1.875 -6.671875 L 1.875 -4.421875 L 0.5 -4.421875 L 0.5 -3.78125 L 4.28125 -3.78125 L 4.28125 -1.078125 L 4.921875 -1.078125 L 4.921875 -3.78125 L 8.5 -3.78125 L 8.5 -4.421875 L 4.875 -4.421875 L 4.875 -5.65625 L 7.609375 -5.65625 L 7.609375 -6.28125 L 4.875 -6.28125 L 4.875 -7.375 Z M 7.15625 -3.125 C 6.171875 -0.84375 4.109375 0.3125 1 0.3125 L 1.234375 0.9375 C 4.4375 0.921875 6.609375 -0.328125 7.75 -2.8125 Z M 2.65625 -3.390625 C 2.140625 -2.59375 1.5 -1.90625 0.734375 -1.3125 L 1.140625 -0.78125 C 1.921875 -1.40625 2.578125 -2.140625 3.125 -2.96875 Z M 2.65625 -3.390625 "/>
</symbol>
<symbol overflow="visible" id="glyph1-12">
<path style="stroke:none;" d="M 3.859375 -7 L 3.859375 -6.421875 L 8.484375 -6.421875 L 8.484375 -7 Z M 4.375 -5.78125 L 4.375 -3.796875 L 7.953125 -3.796875 L 7.953125 -5.78125 Z M 7.34375 -4.328125 L 4.984375 -4.328125 L 4.984375 -5.25 L 7.34375 -5.25 Z M 8.3125 -3.15625 L 4 -3.15625 L 4 0.90625 L 4.609375 0.90625 L 4.609375 0.515625 L 7.703125 0.515625 L 7.703125 0.90625 L 8.3125 0.90625 Z M 4.609375 -0.0625 L 4.609375 -1.078125 L 5.859375 -1.078125 L 5.859375 -0.0625 Z M 6.4375 -0.0625 L 6.4375 -1.078125 L 7.703125 -1.078125 L 7.703125 -0.0625 Z M 4.609375 -1.609375 L 4.609375 -2.609375 L 5.859375 -2.609375 L 5.859375 -1.609375 Z M 6.4375 -2.609375 L 7.703125 -2.609375 L 7.703125 -1.609375 L 6.4375 -1.609375 Z M 2.890625 -0.671875 C 3.296875 -0.671875 3.515625 -0.890625 3.515625 -1.328125 L 3.515625 -5.90625 L 2.359375 -5.90625 L 2.359375 -7.359375 L 1.8125 -7.359375 L 1.8125 -5.90625 L 0.640625 -5.90625 L 0.640625 -0.59375 L 1.171875 -0.59375 L 1.171875 -5.34375 L 1.8125 -5.34375 L 1.8125 0.875 L 2.359375 0.875 L 2.359375 -0.71875 L 2.359375 -0.671875 Z M 2.359375 -5.34375 L 2.984375 -5.34375 L 2.984375 -1.4375 C 2.984375 -1.28125 2.890625 -1.1875 2.71875 -1.1875 L 2.359375 -1.203125 Z M 2.359375 -5.34375 "/>
</symbol>
</g>
</defs>
<g id="surface1">
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 867.960938 162 L 885.960938 162 L 885.960938 180 L 867.960938 180 Z M 867.960938 162 " transform="matrix(1,0,0,1,-717,-115)"/>
<g style="fill:rgb(0%,0%,0%);fill-opacity:1;">
  <use xlink:href="#glyph0-1" x="157.4595" y="58.8528"/>
</g>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 849.960938 162 L 867.960938 162 L 867.960938 180 L 849.960938 180 Z M 849.960938 162 " transform="matrix(1,0,0,1,-717,-115)"/>
<g style="fill:rgb(0%,0%,0%);fill-opacity:1;">
  <use xlink:href="#glyph0-2" x="139.4595" y="58.8528"/>
</g>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 867.960938 144 L 885.960938 144 L 885.960938 162 L 867.960938 162 Z M 867.960938 144 " transform="matrix(1,0,0,1,-717,-115)"/>
<g style="fill:rgb(0%,0%,0%);fill-opacity:1;">
  <use xlink:href="#glyph0-3" x="157.4595" y="40.8528"/>
</g>
<path style=" stroke:none;fill-rule:nonzero;fill:rgb(69.804382%,85.098267%,100%);fill-opacity:1;" d="M 132.960938 47 L 150.960938 47 L 150.960938 29 L 132.960938 29 Z M 132.960938 47 "/>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 849.960938 144 L 867.960938 144 L 867.960938 162 L 849.960938 162 Z M 849.960938 144 " transform="matrix(1,0,0,1,-717,-115)"/>
<g style="fill:rgb(0%,0%,0%);fill-opacity:1;">
  <use xlink:href="#glyph0-4" x="139.4595" y="40.8528"/>
</g>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 796 259 L 814 259 L 814 277 L 796 277 Z M 796 259 " transform="matrix(1,0,0,1,-717,-115)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 742 259 L 760 259 L 760 277 L 742 277 Z M 742 259 " transform="matrix(1,0,0,1,-717,-115)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 778 259 L 796 259 L 796 277 L 778 277 Z M 778 259 " transform="matrix(1,0,0,1,-717,-115)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 760 259 L 778 259 L 778 277 L 760 277 Z M 760 259 " transform="matrix(1,0,0,1,-717,-115)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 796 241 L 814 241 L 814 259 L 796 259 Z M 796 241 " transform="matrix(1,0,0,1,-717,-115)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 796 223 L 814 223 L 814 241 L 796 241 Z M 796 223 " transform="matrix(1,0,0,1,-717,-115)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 796 205 L 814 205 L 814 223 L 796 223 Z M 796 205 " transform="matrix(1,0,0,1,-717,-115)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 760 241 L 778 241 L 778 259 L 760 259 Z M 760 241 " transform="matrix(1,0,0,1,-717,-115)"/>
<path style=" stroke:none;fill-rule:nonzero;fill:rgb(69.804382%,85.098267%,100%);fill-opacity:1;" d="M 43 108 L 61 108 L 61 90 L 43 90 Z M 43 108 "/>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 760 205 L 778 205 L 778 223 L 760 223 Z M 760 205 " transform="matrix(1,0,0,1,-717,-115)"/>
<g style="fill:rgb(0%,0%,0%);fill-opacity:1;">
  <use xlink:href="#glyph0-4" x="49.4973" y="101.8528"/>
</g>
<path style=" stroke:none;fill-rule:nonzero;fill:rgb(69.804382%,85.098267%,100%);fill-opacity:1;" d="M 43 126 L 61 126 L 61 108 L 43 108 Z M 43 126 "/>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 760 223 L 778 223 L 778 241 L 760 241 Z M 760 223 " transform="matrix(1,0,0,1,-717,-115)"/>
<g style="fill:rgb(0%,0%,0%);fill-opacity:1;">
  <use xlink:href="#glyph0-4" x="49.4973" y="119.8528"/>
</g>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 742 241 L 760 241 L 760 259 L 742 259 Z M 742 241 " transform="matrix(1,0,0,1,-717,-115)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 778 223 L 796 223 L 796 241 L 778 241 Z M 778 223 " transform="matrix(1,0,0,1,-717,-115)"/>
<path style=" stroke:none;fill-rule:nonzero;fill:rgb(69.804382%,85.098267%,100%);fill-opacity:1;" d="M 25 126 L 43 126 L 43 108 L 25 108 Z M 25 126 "/>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 742 223 L 760 223 L 760 241 L 742 241 Z M 742 223 " transform="matrix(1,0,0,1,-717,-115)"/>
<g style="fill:rgb(0%,0%,0%);fill-opacity:1;">
  <use xlink:href="#glyph0-4" x="31.4973" y="119.8528"/>
</g>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 778 241 L 796 241 L 796 259 L 778 259 Z M 778 241 " transform="matrix(1,0,0,1,-717,-115)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 778 205 L 796 205 L 796 223 L 778 223 Z M 778 205 " transform="matrix(1,0,0,1,-717,-115)"/>
<path style=" stroke:none;fill-rule:nonzero;fill:rgb(69.804382%,85.098267%,100%);fill-opacity:1;" d="M 25 108 L 43 108 L 43 90 L 25 90 Z M 25 108 "/>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 742 205 L 760 205 L 760 223 L 742 223 Z M 742 205 " transform="matrix(1,0,0,1,-717,-115)"/>
<g style="fill:rgb(0%,0%,0%);fill-opacity:1;">
  <use xlink:href="#glyph0-4" x="31.4973" y="101.8528"/>
</g>
<path style=" stroke:none;fill-rule:nonzero;fill:rgb(69.804382%,85.098267%,100%);fill-opacity:1;" d="M 249.960938 65 L 267.960938 65 L 267.960938 47 L 249.960938 47 Z M 249.960938 65 "/>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 966.960938 162 L 984.960938 162 L 984.960938 180 L 966.960938 180 Z M 966.960938 162 " transform="matrix(1,0,0,1,-717,-115)"/>
<g style="fill:rgb(0%,0%,0%);fill-opacity:1;">
  <use xlink:href="#glyph0-2" x="256.4595" y="58.8528"/>
</g>
<path style=" stroke:none;fill-rule:nonzero;fill:rgb(69.804382%,85.098267%,100%);fill-opacity:1;" d="M 267.960938 65 L 285.960938 65 L 285.960938 47 L 267.960938 47 Z M 267.960938 65 "/>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 984.960938 162 L 1002.960938 162 L 1002.960938 180 L 984.960938 180 Z M 984.960938 162 " transform="matrix(1,0,0,1,-717,-115)"/>
<g style="fill:rgb(0%,0%,0%);fill-opacity:1;">
  <use xlink:href="#glyph0-1" x="274.4595" y="58.8528"/>
</g>
<path style=" stroke:none;fill-rule:nonzero;fill:rgb(69.804382%,85.098267%,100%);fill-opacity:1;" d="M 267.960938 47 L 285.960938 47 L 285.960938 29 L 267.960938 29 Z M 267.960938 47 "/>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 984.960938 144 L 1002.960938 144 L 1002.960938 162 L 984.960938 162 Z M 984.960938 144 " transform="matrix(1,0,0,1,-717,-115)"/>
<g style="fill:rgb(0%,0%,0%);fill-opacity:1;">
  <use xlink:href="#glyph0-3" x="274.4595" y="40.8528"/>
</g>
<path style=" stroke:none;fill-rule:nonzero;fill:rgb(69.804382%,85.098267%,100%);fill-opacity:1;" d="M 249.960938 47 L 267.960938 47 L 267.960938 29 L 249.960938 29 Z M 249.960938 47 "/>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 966.960938 144 L 984.960938 144 L 984.960938 162 L 966.960938 162 Z M 966.960938 144 " transform="matrix(1,0,0,1,-717,-115)"/>
<g style="fill:rgb(0%,0%,0%);fill-opacity:1;">
  <use xlink:href="#glyph0-4" x="256.4595" y="40.8528"/>
</g>
<g style="fill:rgb(0%,0%,0%);fill-opacity:1;">
  <use xlink:href="#glyph1-1" x="116.7441" y="227.5"/>
  <use xlink:href="#glyph1-2" x="125.7441" y="227.5"/>
</g>
<g style="fill:rgb(0%,0%,0%);fill-opacity:1;">
  <use xlink:href="#glyph1-3" x="254.4622" y="15"/>
  <use xlink:href="#glyph1-4" x="263.4622" y="15"/>
  <use xlink:href="#glyph1-5" x="272.4622" y="15"/>
</g>
<g style="fill:rgb(0%,0%,0%);fill-opacity:1;">
  <use xlink:href="#glyph1-1" x="141.9622" y="15"/>
  <use xlink:href="#glyph1-6" x="150.9622" y="15"/>
</g>
<g style="fill:rgb(0%,0%,0%);fill-opacity:1;">
  <use xlink:href="#glyph0-5" x="5" y="128.5"/>
</g>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 894.6875 259 L 912.6875 259 L 912.6875 277 L 894.6875 277 Z M 894.6875 259 " transform="matrix(1,0,0,1,-717,-115)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 858.6875 259 L 876.6875 259 L 876.6875 277 L 858.6875 277 Z M 858.6875 259 " transform="matrix(1,0,0,1,-717,-115)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 876.6875 259 L 894.6875 259 L 894.6875 277 L 876.6875 277 Z M 876.6875 259 " transform="matrix(1,0,0,1,-717,-115)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 840.6875 259 L 858.6875 259 L 858.6875 277 L 840.6875 277 Z M 840.6875 259 " transform="matrix(1,0,0,1,-717,-115)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 894.6875 205 L 912.6875 205 L 912.6875 223 L 894.6875 223 Z M 894.6875 205 " transform="matrix(1,0,0,1,-717,-115)"/>
<g style="fill:rgb(0%,0%,0%);fill-opacity:1;">
  <use xlink:href="#glyph0-3" x="184.1846" y="101.8528"/>
</g>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 894.6875 223 L 912.6875 223 L 912.6875 241 L 894.6875 241 Z M 894.6875 223 " transform="matrix(1,0,0,1,-717,-115)"/>
<g style="fill:rgb(0%,0%,0%);fill-opacity:1;">
  <use xlink:href="#glyph0-1" x="184.1846" y="119.8528"/>
</g>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 894.6875 241 L 912.6875 241 L 912.6875 259 L 894.6875 259 Z M 894.6875 241 " transform="matrix(1,0,0,1,-717,-115)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 858.6875 241 L 876.6875 241 L 876.6875 259 L 858.6875 259 Z M 858.6875 241 " transform="matrix(1,0,0,1,-717,-115)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 858.6875 205 L 876.6875 205 L 876.6875 223 L 858.6875 223 Z M 858.6875 205 " transform="matrix(1,0,0,1,-717,-115)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 858.6875 223 L 876.6875 223 L 876.6875 241 L 858.6875 241 Z M 858.6875 223 " transform="matrix(1,0,0,1,-717,-115)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 840.6875 241 L 858.6875 241 L 858.6875 259 L 840.6875 259 Z M 840.6875 241 " transform="matrix(1,0,0,1,-717,-115)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 876.6875 223 L 894.6875 223 L 894.6875 241 L 876.6875 241 Z M 876.6875 223 " transform="matrix(1,0,0,1,-717,-115)"/>
<g style="fill:rgb(0%,0%,0%);fill-opacity:1;">
  <use xlink:href="#glyph0-2" x="166.1846" y="119.8528"/>
</g>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 840.6875 223 L 858.6875 223 L 858.6875 241 L 840.6875 241 Z M 840.6875 223 " transform="matrix(1,0,0,1,-717,-115)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 876.6875 241 L 894.6875 241 L 894.6875 259 L 876.6875 259 Z M 876.6875 241 " transform="matrix(1,0,0,1,-717,-115)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 876.6875 205 L 894.6875 205 L 894.6875 223 L 876.6875 223 Z M 876.6875 205 " transform="matrix(1,0,0,1,-717,-115)"/>
<g style="fill:rgb(0%,0%,0%);fill-opacity:1;">
  <use xlink:href="#glyph0-4" x="166.1846" y="101.8528"/>
</g>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 840.6875 205 L 858.6875 205 L 858.6875 223 L 840.6875 223 Z M 840.6875 205 " transform="matrix(1,0,0,1,-717,-115)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 994.671875 205 L 1012.671875 205 L 1012.671875 223 L 994.671875 223 Z M 994.671875 205 " transform="matrix(1,0,0,1,-717,-115)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 940.671875 205 L 958.671875 205 L 958.671875 223 L 940.671875 223 Z M 940.671875 205 " transform="matrix(1,0,0,1,-717,-115)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 976.671875 205 L 994.671875 205 L 994.671875 223 L 976.671875 223 Z M 976.671875 205 " transform="matrix(1,0,0,1,-717,-115)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 958.671875 205 L 976.671875 205 L 976.671875 223 L 958.671875 223 Z M 958.671875 205 " transform="matrix(1,0,0,1,-717,-115)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 994.671875 223 L 1012.671875 223 L 1012.671875 241 L 994.671875 241 Z M 994.671875 223 " transform="matrix(1,0,0,1,-717,-115)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 994.671875 241 L 1012.671875 241 L 1012.671875 259 L 994.671875 259 Z M 994.671875 241 " transform="matrix(1,0,0,1,-717,-115)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 994.671875 259 L 1012.671875 259 L 1012.671875 277 L 994.671875 277 Z M 994.671875 259 " transform="matrix(1,0,0,1,-717,-115)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 958.671875 259 L 976.671875 259 L 976.671875 277 L 958.671875 277 Z M 958.671875 259 " transform="matrix(1,0,0,1,-717,-115)"/>
<g style="fill:rgb(0%,0%,0%);fill-opacity:1;">
  <use xlink:href="#glyph0-6" x="248.1682" y="155.8528"/>
</g>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 958.671875 223 L 976.671875 223 L 976.671875 241 L 958.671875 241 Z M 958.671875 223 " transform="matrix(1,0,0,1,-717,-115)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 958.671875 241 L 976.671875 241 L 976.671875 259 L 958.671875 259 Z M 958.671875 241 " transform="matrix(1,0,0,1,-717,-115)"/>
<g style="fill:rgb(0%,0%,0%);fill-opacity:1;">
  <use xlink:href="#glyph0-2" x="248.1682" y="137.8528"/>
</g>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 940.671875 259 L 958.671875 259 L 958.671875 277 L 940.671875 277 Z M 940.671875 259 " transform="matrix(1,0,0,1,-717,-115)"/>
<g style="fill:rgb(0%,0%,0%);fill-opacity:1;">
  <use xlink:href="#glyph0-7" x="230.1682" y="155.8528"/>
</g>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 976.671875 241 L 994.671875 241 L 994.671875 259 L 976.671875 259 Z M 976.671875 241 " transform="matrix(1,0,0,1,-717,-115)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 940.671875 241 L 958.671875 241 L 958.671875 259 L 940.671875 259 Z M 940.671875 241 " transform="matrix(1,0,0,1,-717,-115)"/>
<g style="fill:rgb(0%,0%,0%);fill-opacity:1;">
  <use xlink:href="#glyph0-4" x="230.1682" y="137.8528"/>
</g>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 976.671875 259 L 994.671875 259 L 994.671875 277 L 976.671875 277 Z M 976.671875 259 " transform="matrix(1,0,0,1,-717,-115)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 976.671875 223 L 994.671875 223 L 994.671875 241 L 976.671875 241 Z M 976.671875 223 " transform="matrix(1,0,0,1,-717,-115)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 940.671875 223 L 958.671875 223 L 958.671875 241 L 940.671875 241 Z M 940.671875 223 " transform="matrix(1,0,0,1,-717,-115)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 1093.425781 223 L 1111.425781 223 L 1111.425781 241 L 1093.425781 241 Z M 1093.425781 223 " transform="matrix(1,0,0,1,-717,-115)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 1093.425781 259 L 1111.425781 259 L 1111.425781 277 L 1093.425781 277 Z M 1093.425781 259 " transform="matrix(1,0,0,1,-717,-115)"/>
<g style="fill:rgb(0%,0%,0%);fill-opacity:1;">
  <use xlink:href="#glyph0-8" x="382.922" y="155.8528"/>
</g>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 1093.425781 241 L 1111.425781 241 L 1111.425781 259 L 1093.425781 259 Z M 1093.425781 241 " transform="matrix(1,0,0,1,-717,-115)"/>
<g style="fill:rgb(0%,0%,0%);fill-opacity:1;">
  <use xlink:href="#glyph0-1" x="382.922" y="137.8528"/>
</g>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 1093.425781 205 L 1111.425781 205 L 1111.425781 223 L 1093.425781 223 Z M 1093.425781 205 " transform="matrix(1,0,0,1,-717,-115)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 1039.425781 259 L 1057.425781 259 L 1057.425781 277 L 1039.425781 277 Z M 1039.425781 259 " transform="matrix(1,0,0,1,-717,-115)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 1057.425781 259 L 1075.425781 259 L 1075.425781 277 L 1057.425781 277 Z M 1057.425781 259 " transform="matrix(1,0,0,1,-717,-115)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 1075.425781 259 L 1093.425781 259 L 1093.425781 277 L 1075.425781 277 Z M 1075.425781 259 " transform="matrix(1,0,0,1,-717,-115)"/>
<g style="fill:rgb(0%,0%,0%);fill-opacity:1;">
  <use xlink:href="#glyph0-6" x="364.922" y="155.8528"/>
</g>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 1057.425781 241 L 1075.425781 241 L 1075.425781 259 L 1057.425781 259 Z M 1057.425781 241 " transform="matrix(1,0,0,1,-717,-115)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 1057.425781 205 L 1075.425781 205 L 1075.425781 223 L 1057.425781 223 Z M 1057.425781 205 " transform="matrix(1,0,0,1,-717,-115)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 1039.425781 241 L 1057.425781 241 L 1057.425781 259 L 1039.425781 259 Z M 1039.425781 241 " transform="matrix(1,0,0,1,-717,-115)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 1075.425781 223 L 1093.425781 223 L 1093.425781 241 L 1075.425781 241 Z M 1075.425781 223 " transform="matrix(1,0,0,1,-717,-115)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 1039.425781 223 L 1057.425781 223 L 1057.425781 241 L 1039.425781 241 Z M 1039.425781 223 " transform="matrix(1,0,0,1,-717,-115)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 1075.425781 241 L 1093.425781 241 L 1093.425781 259 L 1075.425781 259 Z M 1075.425781 241 " transform="matrix(1,0,0,1,-717,-115)"/>
<g style="fill:rgb(0%,0%,0%);fill-opacity:1;">
  <use xlink:href="#glyph0-4" x="364.922" y="137.8528"/>
</g>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 1075.425781 205 L 1093.425781 205 L 1093.425781 223 L 1075.425781 223 Z M 1075.425781 205 " transform="matrix(1,0,0,1,-717,-115)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 1039.425781 205 L 1057.425781 205 L 1057.425781 223 L 1039.425781 223 Z M 1039.425781 205 " transform="matrix(1,0,0,1,-717,-115)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 778 356 L 796 356 L 796 374 L 778 374 Z M 778 356 " transform="matrix(1,0,0,1,-717,-115)"/>
<g style="fill:rgb(0%,0%,0%);fill-opacity:1;">
  <use xlink:href="#glyph0-6" x="67.4973" y="252.8528"/>
</g>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 742 356 L 760 356 L 760 374 L 742 374 Z M 742 356 " transform="matrix(1,0,0,1,-717,-115)"/>
<g style="fill:rgb(0%,0%,0%);fill-opacity:1;">
  <use xlink:href="#glyph0-7" x="31.4973" y="252.8528"/>
</g>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 796 356 L 814 356 L 814 374 L 796 374 Z M 796 356 " transform="matrix(1,0,0,1,-717,-115)"/>
<g style="fill:rgb(0%,0%,0%);fill-opacity:1;">
  <use xlink:href="#glyph0-8" x="85.4973" y="252.8528"/>
</g>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 760 356 L 778 356 L 778 374 L 760 374 Z M 760 356 " transform="matrix(1,0,0,1,-717,-115)"/>
<g style="fill:rgb(0%,0%,0%);fill-opacity:1;">
  <use xlink:href="#glyph0-6" x="49.4973" y="252.8528"/>
</g>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 796 302 L 814 302 L 814 320 L 796 320 Z M 796 302 " transform="matrix(1,0,0,1,-717,-115)"/>
<g style="fill:rgb(0%,0%,0%);fill-opacity:1;">
  <use xlink:href="#glyph0-3" x="85.4973" y="198.8528"/>
</g>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 796 320 L 814 320 L 814 338 L 796 338 Z M 796 320 " transform="matrix(1,0,0,1,-717,-115)"/>
<g style="fill:rgb(0%,0%,0%);fill-opacity:1;">
  <use xlink:href="#glyph0-1" x="85.4973" y="216.8528"/>
</g>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 796 338 L 814 338 L 814 356 L 796 356 Z M 796 338 " transform="matrix(1,0,0,1,-717,-115)"/>
<g style="fill:rgb(0%,0%,0%);fill-opacity:1;">
  <use xlink:href="#glyph0-1" x="85.4973" y="234.8528"/>
</g>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 760 338 L 778 338 L 778 356 L 760 356 Z M 760 338 " transform="matrix(1,0,0,1,-717,-115)"/>
<g style="fill:rgb(0%,0%,0%);fill-opacity:1;">
  <use xlink:href="#glyph0-2" x="49.4973" y="234.8528"/>
</g>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 760 302 L 778 302 L 778 320 L 760 320 Z M 760 302 " transform="matrix(1,0,0,1,-717,-115)"/>
<g style="fill:rgb(0%,0%,0%);fill-opacity:1;">
  <use xlink:href="#glyph0-4" x="49.4973" y="198.8528"/>
</g>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 760 320 L 778 320 L 778 338 L 760 338 Z M 760 320 " transform="matrix(1,0,0,1,-717,-115)"/>
<g style="fill:rgb(0%,0%,0%);fill-opacity:1;">
  <use xlink:href="#glyph0-4" x="49.4973" y="216.8528"/>
</g>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 742 338 L 760 338 L 760 356 L 742 356 Z M 742 338 " transform="matrix(1,0,0,1,-717,-115)"/>
<g style="fill:rgb(0%,0%,0%);fill-opacity:1;">
  <use xlink:href="#glyph0-4" x="31.4973" y="234.8528"/>
</g>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 778 320 L 796 320 L 796 338 L 778 338 Z M 778 320 " transform="matrix(1,0,0,1,-717,-115)"/>
<g style="fill:rgb(0%,0%,0%);fill-opacity:1;">
  <use xlink:href="#glyph0-2" x="67.4973" y="216.8528"/>
</g>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 742 320 L 760 320 L 760 338 L 742 338 Z M 742 320 " transform="matrix(1,0,0,1,-717,-115)"/>
<g style="fill:rgb(0%,0%,0%);fill-opacity:1;">
  <use xlink:href="#glyph0-4" x="31.4973" y="216.8528"/>
</g>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 778 338 L 796 338 L 796 356 L 778 356 Z M 778 338 " transform="matrix(1,0,0,1,-717,-115)"/>
<g style="fill:rgb(0%,0%,0%);fill-opacity:1;">
  <use xlink:href="#glyph0-4" x="67.4973" y="234.8528"/>
</g>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 778 302 L 796 302 L 796 320 L 778 320 Z M 778 302 " transform="matrix(1,0,0,1,-717,-115)"/>
<g style="fill:rgb(0%,0%,0%);fill-opacity:1;">
  <use xlink:href="#glyph0-4" x="67.4973" y="198.8528"/>
</g>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 742 302 L 760 302 L 760 320 L 742 320 Z M 742 302 " transform="matrix(1,0,0,1,-717,-115)"/>
<g style="fill:rgb(0%,0%,0%);fill-opacity:1;">
  <use xlink:href="#glyph0-4" x="31.4973" y="198.8528"/>
</g>
<g style="fill:rgb(0%,0%,0%);fill-opacity:1;">
  <use xlink:href="#glyph0-9" x="107.0345" y="128.5"/>
</g>
<g style="fill:rgb(0%,0%,0%);fill-opacity:1;">
  <use xlink:href="#glyph0-9" x="207.0842" y="128.5"/>
</g>
<g style="fill:rgb(0%,0%,0%);fill-opacity:1;">
  <use xlink:href="#glyph0-9" x="306.836" y="128.5"/>
</g>
<g style="fill:rgb(0%,0%,0%);fill-opacity:1;">
  <use xlink:href="#glyph0-5" x="5" y="225.5"/>
</g>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 896.460938 144 L 956.460938 144 L 956.460938 180 L 896.460938 180 Z M 896.460938 144 " transform="matrix(1,0,0,1,-717,-115)"/>
<g style="fill:rgb(0%,0%,0%);fill-opacity:1;">
  <use xlink:href="#glyph1-7" x="191.4622" y="44"/>
  <use xlink:href="#glyph1-8" x="200.4622" y="44"/>
  <use xlink:href="#glyph1-9" x="209.4622" y="44"/>
  <use xlink:href="#glyph1-10" x="218.4622" y="44"/>
</g>
<g style="fill:rgb(0%,0%,0%);fill-opacity:1;">
  <use xlink:href="#glyph0-10" x="194.9624" y="57"/>
</g>
<g style="fill:rgb(0%,0%,0%);fill-opacity:1;">
  <use xlink:href="#glyph1-11" x="197.9595" y="57"/>
  <use xlink:href="#glyph1-12" x="206.9595" y="57"/>
</g>
<g style="fill:rgb(0%,0%,0%);fill-opacity:1;">
  <use xlink:href="#glyph0-2" x="215.9595" y="57"/>
  <use xlink:href="#glyph0-11" x="220.9653" y="57"/>
</g>
</g>
</svg>
