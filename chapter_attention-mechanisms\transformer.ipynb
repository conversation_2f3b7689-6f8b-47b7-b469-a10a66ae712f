{"cells": [{"cell_type": "markdown", "id": "052c827a", "metadata": {"origin_pos": 0}, "source": ["# Transformer\n", ":label:`sec_transformer`\n", "\n", " :numref:`subsec_cnn-rnn-self-attention`中比较了卷积神经网络（CNN）、循环神经网络（RNN）和自注意力（self-attention）。值得注意的是，自注意力同时具有并行计算和最短的最大路径长度这两个优势。因此，使用自注意力来设计深度架构是很有吸引力的。对比之前仍然依赖循环神经网络实现输入表示的自注意力模型 :cite:`Cheng.Dong.Lapata.2016,Lin.Feng.Santos.ea.2017,Paulus.Xiong.Socher.2017`，Transformer模型完全基于注意力机制，没有任何卷积层或循环神经网络层 :cite:`Vaswani.Shazeer.Parmar.ea.2017`。尽管Transformer最初是应用于在文本数据上的序列到序列学习，但现在已经推广到各种现代的深度学习中，例如语言、视觉、语音和强化学习领域。\n", "\n", "## 模型\n", "\n", "Transformer作为编码器－解码器架构的一个实例，其整体架构图在 :numref:`fig_transformer`中展示。正如所见到的，Transformer是由编码器和解码器组成的。与 :numref:`fig_s2s_attention_details`中基于Bahdanau注意力实现的序列到序列的学习相比，Transformer的编码器和解码器是基于自注意力的模块叠加而成的，源（输入）序列和目标（输出）序列的*嵌入*（embedding）表示将加上*位置编码*（positional encoding），再分别输入到编码器和解码器中。\n", "\n", "![transformer架构](../img/transformer.svg)\n", ":width:`500px`\n", ":label:`fig_transformer`\n", "\n", "图 :numref:`fig_transformer`中概述了Transformer的架构。从宏观角度来看，Transformer的编码器是由多个相同的层叠加而成的，每个层都有两个子层（子层表示为$\\mathrm{sublayer}$）。第一个子层是*多头自注意力*（multi-head self-attention）汇聚；第二个子层是*基于位置的前馈网络*（positionwise feed-forward network）。具体来说，在计算编码器的自注意力时，查询、键和值都来自前一个编码器层的输出。受 :numref:`sec_resnet`中残差网络的启发，每个子层都采用了*残差连接*（residual connection）。在Transformer中，对于序列中任何位置的任何输入$\\mathbf{x} \\in \\mathbb{R}^d$，都要求满足$\\mathrm{sublayer}(\\mathbf{x}) \\in \\mathbb{R}^d$，以便残差连接满足$\\mathbf{x} + \\mathrm{sublayer}(\\mathbf{x}) \\in \\mathbb{R}^d$。在残差连接的加法计算之后，紧接着应用*层规范化*（layer normalization） :cite:`Ba.Kiros.Hinton.2016`。因此，输入序列对应的每个位置，Transformer编码器都将输出一个$d$维表示向量。\n", "\n", "Transformer解码器也是由多个相同的层叠加而成的，并且层中使用了残差连接和层规范化。除了编码器中描述的两个子层之外，解码器还在这两个子层之间插入了第三个子层，称为*编码器－解码器注意力*（encoder-decoder attention）层。在编码器－解码器注意力中，查询来自前一个解码器层的输出，而键和值来自整个编码器的输出。在解码器自注意力中，查询、键和值都来自上一个解码器层的输出。但是，解码器中的每个位置只能考虑该位置之前的所有位置。这种*掩蔽*（masked）注意力保留了*自回归*（auto-regressive）属性，确保预测仅依赖于已生成的输出词元。\n", "\n", "在此之前已经描述并实现了基于缩放点积多头注意力 :numref:`sec_multihead-attention`和位置编码 :numref:`subsec_positional-encoding`。接下来将实现Transformer模型的剩余部分。\n"]}, {"cell_type": "code", "execution_count": 1, "id": "21717762", "metadata": {"execution": {"iopub.execute_input": "2022-12-07T16:34:06.154575Z", "iopub.status.busy": "2022-12-07T16:34:06.154014Z", "iopub.status.idle": "2022-12-07T16:34:08.548184Z", "shell.execute_reply": "2022-12-07T16:34:08.547347Z"}, "origin_pos": 2, "tab": ["pytorch"]}, "outputs": [], "source": ["import math\n", "import pandas as pd\n", "import torch\n", "from torch import nn\n", "from d2l import torch as d2l"]}, {"cell_type": "markdown", "id": "ddb02294", "metadata": {"origin_pos": 5}, "source": ["## [**基于位置的前馈网络**]\n", "\n", "基于位置的前馈网络对序列中的所有位置的表示进行变换时使用的是同一个多层感知机（MLP），这就是称前馈网络是*基于位置的*（positionwise）的原因。在下面的实现中，输入`X`的形状（批量大小，时间步数或序列长度，隐单元数或特征维度）将被一个两层的感知机转换成形状为（批量大小，时间步数，`ffn_num_outputs`）的输出张量。\n"]}, {"cell_type": "code", "execution_count": 2, "id": "2f9b363a", "metadata": {"execution": {"iopub.execute_input": "2022-12-07T16:34:08.552408Z", "iopub.status.busy": "2022-12-07T16:34:08.551836Z", "iopub.status.idle": "2022-12-07T16:34:08.557818Z", "shell.execute_reply": "2022-12-07T16:34:08.557081Z"}, "origin_pos": 7, "tab": ["pytorch"]}, "outputs": [], "source": ["#@save\n", "class PositionWiseFFN(nn.Module):\n", "    \"\"\"基于位置的前馈网络\"\"\"\n", "    def __init__(self, ffn_num_input, ffn_num_hiddens, ffn_num_outputs,\n", "                 **kwargs):\n", "        super(Position<PERSON>iseFF<PERSON>, self).__init__(**kwargs)\n", "        self.dense1 = nn.Linear(ffn_num_input, ffn_num_hiddens)\n", "        self.relu = nn.ReLU()\n", "        self.dense2 = nn.Linear(ffn_num_hiddens, ffn_num_outputs)\n", "\n", "    def forward(self, X):\n", "        return self.dense2(self.relu(self.dense1(X)))"]}, {"cell_type": "markdown", "id": "42e83791", "metadata": {"origin_pos": 10}, "source": ["下面的例子显示，[**改变张量的最里层维度的尺寸**]，会改变成基于位置的前馈网络的输出尺寸。因为用同一个多层感知机对所有位置上的输入进行变换，所以当所有这些位置的输入相同时，它们的输出也是相同的。\n"]}, {"cell_type": "code", "execution_count": 3, "id": "7c6a7f54", "metadata": {"execution": {"iopub.execute_input": "2022-12-07T16:34:08.562217Z", "iopub.status.busy": "2022-12-07T16:34:08.561939Z", "iopub.status.idle": "2022-12-07T16:34:08.591126Z", "shell.execute_reply": "2022-12-07T16:34:08.590345Z"}, "origin_pos": 12, "tab": ["pytorch"]}, "outputs": [{"data": {"text/plain": ["tensor([[ 0.3407, -0.0869, -0.3967,  0.7588,  0.3862,  0.2616,  0.1842, -0.0328],\n", "        [ 0.3407, -0.0869, -0.3967,  0.7588,  0.3862,  0.2616,  0.1842, -0.0328],\n", "        [ 0.3407, -0.0869, -0.3967,  0.7588,  0.3862,  0.2616,  0.1842, -0.0328]],\n", "       grad_fn=<SelectBackward0>)"]}, "execution_count": 3, "metadata": {}, "output_type": "execute_result"}], "source": ["ffn = PositionWiseFFN(4, 4, 8)\n", "ffn.eval()\n", "ffn(torch.ones((2, 3, 4)))[0]"]}, {"cell_type": "markdown", "id": "865b7bb3", "metadata": {"origin_pos": 15}, "source": ["## 残差连接和层规范化\n", "\n", "现在让我们关注 :numref:`fig_transformer`中的*加法和规范化*（add&norm）组件。正如在本节开头所述，这是由残差连接和紧随其后的层规范化组成的。两者都是构建有效的深度架构的关键。\n", "\n", " :numref:`sec_batch_norm`中解释了在一个小批量的样本内基于批量规范化对数据进行重新中心化和重新缩放的调整。层规范化和批量规范化的目标相同，但层规范化是基于特征维度进行规范化。尽管批量规范化在计算机视觉中被广泛应用，但在自然语言处理任务中（输入通常是变长序列）批量规范化通常不如层规范化的效果好。\n", "\n", "以下代码[**对比不同维度的层规范化和批量规范化的效果**]。\n"]}, {"cell_type": "code", "execution_count": 4, "id": "d990bbcc", "metadata": {"execution": {"iopub.execute_input": "2022-12-07T16:34:08.594474Z", "iopub.status.busy": "2022-12-07T16:34:08.594019Z", "iopub.status.idle": "2022-12-07T16:34:08.602137Z", "shell.execute_reply": "2022-12-07T16:34:08.601386Z"}, "origin_pos": 17, "tab": ["pytorch"]}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["layer norm: tensor([[-1.0000,  1.0000],\n", "        [-1.0000,  1.0000]], grad_fn=<NativeLayerNormBackward0>) \n", "batch norm: tensor([[-1.0000, -1.0000],\n", "        [ 1.0000,  1.0000]], grad_fn=<NativeBatchNormBackward0>)\n"]}], "source": ["ln = nn.<PERSON>erNorm(2)\n", "bn = nn.BatchNorm1d(2)\n", "X = torch.tensor([[1, 2], [2, 3]], dtype=torch.float32)\n", "# 在训练模式下计算X的均值和方差\n", "print('layer norm:', ln(X), '\\nbatch norm:', bn(X))"]}, {"cell_type": "markdown", "id": "7390f866", "metadata": {"origin_pos": 20}, "source": ["现在可以[**使用残差连接和层规范化**]来实现`AddNorm`类。暂退法也被作为正则化方法使用。\n"]}, {"cell_type": "code", "execution_count": 5, "id": "12486ba3", "metadata": {"execution": {"iopub.execute_input": "2022-12-07T16:34:08.605650Z", "iopub.status.busy": "2022-12-07T16:34:08.604998Z", "iopub.status.idle": "2022-12-07T16:34:08.612316Z", "shell.execute_reply": "2022-12-07T16:34:08.611287Z"}, "origin_pos": 22, "tab": ["pytorch"]}, "outputs": [], "source": ["#@save\n", "class AddNorm(nn.Module):\n", "    \"\"\"残差连接后进行层规范化\"\"\"\n", "    def __init__(self, normalized_shape, dropout, **kwargs):\n", "        super(<PERSON><PERSON><PERSON><PERSON>, self).__init__(**kwargs)\n", "        self.dropout = nn.Dropout(dropout)\n", "        self.ln = nn.LayerNorm(normalized_shape)\n", "\n", "    def forward(self, X, Y):\n", "        return self.ln(self.dropout(Y) + X)"]}, {"cell_type": "markdown", "id": "94d6cc2a", "metadata": {"origin_pos": 25}, "source": ["残差连接要求两个输入的形状相同，以便[**加法操作后输出张量的形状相同**]。\n"]}, {"cell_type": "code", "execution_count": 6, "id": "69f3c25d", "metadata": {"execution": {"iopub.execute_input": "2022-12-07T16:34:08.616421Z", "iopub.status.busy": "2022-12-07T16:34:08.615605Z", "iopub.status.idle": "2022-12-07T16:34:08.622590Z", "shell.execute_reply": "2022-12-07T16:34:08.621808Z"}, "origin_pos": 27, "tab": ["pytorch"]}, "outputs": [{"data": {"text/plain": ["torch.<PERSON><PERSON>([2, 3, 4])"]}, "execution_count": 6, "metadata": {}, "output_type": "execute_result"}], "source": ["add_norm = AddNorm([3, 4], 0.5)\n", "add_norm.eval()\n", "add_norm(torch.ones((2, 3, 4)), torch.ones((2, 3, 4))).shape"]}, {"cell_type": "markdown", "id": "ee1f802e", "metadata": {"origin_pos": 29}, "source": ["## 编码器\n", "\n", "有了组成Transformer编码器的基础组件，现在可以先[**实现编码器中的一个层**]。下面的`EncoderBlock`类包含两个子层：多头自注意力和基于位置的前馈网络，这两个子层都使用了残差连接和紧随的层规范化。\n"]}, {"cell_type": "code", "execution_count": 7, "id": "83ed468f", "metadata": {"execution": {"iopub.execute_input": "2022-12-07T16:34:08.625957Z", "iopub.status.busy": "2022-12-07T16:34:08.625406Z", "iopub.status.idle": "2022-12-07T16:34:08.631697Z", "shell.execute_reply": "2022-12-07T16:34:08.630939Z"}, "origin_pos": 31, "tab": ["pytorch"]}, "outputs": [], "source": ["#@save\n", "class EncoderBlock(nn.Module):\n", "    \"\"\"Transformer编码器块\"\"\"\n", "    def __init__(self, key_size, query_size, value_size, num_hiddens,\n", "                 norm_shape, ffn_num_input, ffn_num_hiddens, num_heads,\n", "                 dropout, use_bias=False, **kwargs):\n", "        super(<PERSON><PERSON><PERSON><PERSON><PERSON>, self).__init__(**kwargs)\n", "        self.attention = d2l.MultiHeadAttention(\n", "            key_size, query_size, value_size, num_hiddens, num_heads, dropout,\n", "            use_bias)\n", "        self.addnorm1 = AddNorm(norm_shape, dropout)\n", "        self.ffn = PositionWiseFFN(\n", "            ffn_num_input, ffn_num_hiddens, num_hiddens)\n", "        self.addnorm2 = AddNorm(norm_shape, dropout)\n", "\n", "    def forward(self, X, valid_lens):\n", "        Y = self.addnorm1(X, self.attention(X, X, X, valid_lens))\n", "        return self.addnorm2(Y, self.ffn(Y))"]}, {"cell_type": "markdown", "id": "6d9bcdf1", "metadata": {"origin_pos": 34}, "source": ["正如从代码中所看到的，[**Transformer编码器中的任何层都不会改变其输入的形状**]。\n"]}, {"cell_type": "code", "execution_count": 8, "id": "5085cb17", "metadata": {"execution": {"iopub.execute_input": "2022-12-07T16:34:08.635003Z", "iopub.status.busy": "2022-12-07T16:34:08.634358Z", "iopub.status.idle": "2022-12-07T16:34:08.645596Z", "shell.execute_reply": "2022-12-07T16:34:08.644834Z"}, "origin_pos": 36, "tab": ["pytorch"]}, "outputs": [{"data": {"text/plain": ["torch.Size([2, 100, 24])"]}, "execution_count": 8, "metadata": {}, "output_type": "execute_result"}], "source": ["X = torch.ones((2, 100, 24))\n", "valid_lens = torch.tensor([3, 2])\n", "encoder_blk = EncoderBlock(24, 24, 24, 24, [100, 24], 24, 48, 8, 0.5)\n", "encoder_blk.eval()\n", "encoder_blk(X, valid_lens).shape"]}, {"cell_type": "markdown", "id": "e2d1c83b", "metadata": {"origin_pos": 38}, "source": ["下面实现的[**Transformer编码器**]的代码中，堆叠了`num_layers`个`EncoderBlock`类的实例。由于这里使用的是值范围在$-1$和$1$之间的固定位置编码，因此通过学习得到的输入的嵌入表示的值需要先乘以嵌入维度的平方根进行重新缩放，然后再与位置编码相加。\n"]}, {"cell_type": "code", "execution_count": 9, "id": "ec32176b", "metadata": {"execution": {"iopub.execute_input": "2022-12-07T16:34:08.648913Z", "iopub.status.busy": "2022-12-07T16:34:08.648382Z", "iopub.status.idle": "2022-12-07T16:34:08.656029Z", "shell.execute_reply": "2022-12-07T16:34:08.655265Z"}, "origin_pos": 40, "tab": ["pytorch"]}, "outputs": [], "source": ["#@save\n", "class TransformerEncoder(d2l.Encoder):\n", "    \"\"\"Transformer编码器\"\"\"\n", "    def __init__(self, vocab_size, key_size, query_size, value_size,\n", "                 num_hiddens, norm_shape, ffn_num_input, ffn_num_hiddens,\n", "                 num_heads, num_layers, dropout, use_bias=False, **kwargs):\n", "        super(<PERSON><PERSON><PERSON><PERSON><PERSON>, self).__init__(**kwargs)\n", "        self.num_hiddens = num_hiddens\n", "        self.embedding = nn.Embedding(vocab_size, num_hiddens)\n", "        self.pos_encoding = d2l.PositionalEncoding(num_hiddens, dropout)\n", "        self.blks = nn.Sequential()\n", "        for i in range(num_layers):\n", "            self.blks.add_module(\"block\"+str(i),\n", "                EncoderBlock(key_size, query_size, value_size, num_hiddens,\n", "                             norm_shape, ffn_num_input, ffn_num_hiddens,\n", "                             num_heads, dropout, use_bias))\n", "\n", "    def forward(self, X, valid_lens, *args):\n", "        # 因为位置编码值在-1和1之间，\n", "        # 因此嵌入值乘以嵌入维度的平方根进行缩放，\n", "        # 然后再与位置编码相加。\n", "        X = self.pos_encoding(self.embedding(X) * math.sqrt(self.num_hiddens))\n", "        self.attention_weights = [None] * len(self.blks)\n", "        for i, blk in enumerate(self.blks):\n", "            X = blk(X, valid_lens)\n", "            self.attention_weights[\n", "                i] = blk.attention.attention.attention_weights\n", "        return X"]}, {"cell_type": "markdown", "id": "356c8394", "metadata": {"origin_pos": 43}, "source": ["下面我们指定了超参数来[**创建一个两层的Transformer编码器**]。\n", "Transformer编码器输出的形状是（批量大小，时间步数目，`num_hiddens`）。\n"]}, {"cell_type": "code", "execution_count": 10, "id": "f679b985", "metadata": {"execution": {"iopub.execute_input": "2022-12-07T16:34:08.659422Z", "iopub.status.busy": "2022-12-07T16:34:08.658787Z", "iopub.status.idle": "2022-12-07T16:34:08.672205Z", "shell.execute_reply": "2022-12-07T16:34:08.671418Z"}, "origin_pos": 45, "tab": ["pytorch"]}, "outputs": [{"data": {"text/plain": ["torch.Size([2, 100, 24])"]}, "execution_count": 10, "metadata": {}, "output_type": "execute_result"}], "source": ["encoder = TransformerEncoder(\n", "    200, 24, 24, 24, 24, [100, 24], 24, 48, 8, 2, 0.5)\n", "encoder.eval()\n", "encoder(torch.ones((2, 100), dtype=torch.long), valid_lens).shape"]}, {"cell_type": "markdown", "id": "eebc024b", "metadata": {"origin_pos": 48}, "source": ["## 解码器\n", "\n", "如 :numref:`fig_transformer`所示，[**Transformer解码器也是由多个相同的层组成**]。在`DecoderBlock`类中实现的每个层包含了三个子层：解码器自注意力、“编码器-解码器”注意力和基于位置的前馈网络。这些子层也都被残差连接和紧随的层规范化围绕。\n", "\n", "正如在本节前面所述，在掩蔽多头解码器自注意力层（第一个子层）中，查询、键和值都来自上一个解码器层的输出。关于*序列到序列模型*（sequence-to-sequence model），在训练阶段，其输出序列的所有位置（时间步）的词元都是已知的；然而，在预测阶段，其输出序列的词元是逐个生成的。因此，在任何解码器时间步中，只有生成的词元才能用于解码器的自注意力计算中。为了在解码器中保留自回归的属性，其掩蔽自注意力设定了参数`dec_valid_lens`，以便任何查询都只会与解码器中所有已经生成词元的位置（即直到该查询位置为止）进行注意力计算。\n"]}, {"cell_type": "code", "execution_count": 11, "id": "128aaa6e", "metadata": {"execution": {"iopub.execute_input": "2022-12-07T16:34:08.675578Z", "iopub.status.busy": "2022-12-07T16:34:08.675261Z", "iopub.status.idle": "2022-12-07T16:34:08.684497Z", "shell.execute_reply": "2022-12-07T16:34:08.683736Z"}, "origin_pos": 50, "tab": ["pytorch"]}, "outputs": [], "source": ["class DecoderBlock(nn.Module):\n", "    \"\"\"解码器中第i个块\"\"\"\n", "    def __init__(self, key_size, query_size, value_size, num_hiddens,\n", "                 norm_shape, ffn_num_input, ffn_num_hiddens, num_heads,\n", "                 dropout, i, **kwargs):\n", "        super(<PERSON><PERSON><PERSON><PERSON><PERSON>, self).__init__(**kwargs)\n", "        self.i = i\n", "        self.attention1 = d2l.MultiHeadAttention(\n", "            key_size, query_size, value_size, num_hiddens, num_heads, dropout)\n", "        self.addnorm1 = AddNorm(norm_shape, dropout)\n", "        self.attention2 = d2l.MultiHeadAttention(\n", "            key_size, query_size, value_size, num_hiddens, num_heads, dropout)\n", "        self.addnorm2 = AddNorm(norm_shape, dropout)\n", "        self.ffn = PositionWiseFFN(ffn_num_input, ffn_num_hiddens,\n", "                                   num_hiddens)\n", "        self.addnorm3 = AddNorm(norm_shape, dropout)\n", "\n", "    def forward(self, X, state):\n", "        enc_outputs, enc_valid_lens = state[0], state[1]\n", "        # 训练阶段，输出序列的所有词元都在同一时间处理，\n", "        # 因此state[2][self.i]初始化为None。\n", "        # 预测阶段，输出序列是通过词元一个接着一个解码的，\n", "        # 因此state[2][self.i]包含着直到当前时间步第i个块解码的输出表示\n", "        if state[2][self.i] is None:\n", "            key_values = X\n", "        else:\n", "            key_values = torch.cat((state[2][self.i], X), axis=1)\n", "        state[2][self.i] = key_values\n", "        if self.training:\n", "            batch_size, num_steps, _ = X.shape\n", "            # dec_valid_lens的开头:(batch_size,num_steps),\n", "            # 其中每一行是[1,2,...,num_steps]\n", "            dec_valid_lens = torch.arange(\n", "                1, num_steps + 1, device=X.device).repeat(batch_size, 1)\n", "        else:\n", "            dec_valid_lens = None\n", "\n", "        # 自注意力\n", "        X2 = self.attention1(X, key_values, key_values, dec_valid_lens)\n", "        Y = self.addnorm1(X, X2)\n", "        # 编码器－解码器注意力。\n", "        # enc_outputs的开头:(batch_size,num_steps,num_hiddens)\n", "        Y2 = self.attention2(Y, enc_outputs, enc_outputs, enc_valid_lens)\n", "        Z = self.addnorm2(Y, Y2)\n", "        return self.addnorm3(Z, self.ffn(Z)), state"]}, {"cell_type": "markdown", "id": "f1302469", "metadata": {"origin_pos": 53}, "source": ["为了便于在“编码器－解码器”注意力中进行缩放点积计算和残差连接中进行加法计算，[**编码器和解码器的特征维度都是`num_hiddens`。**]\n"]}, {"cell_type": "code", "execution_count": 12, "id": "d685bab5", "metadata": {"execution": {"iopub.execute_input": "2022-12-07T16:34:08.687705Z", "iopub.status.busy": "2022-12-07T16:34:08.687189Z", "iopub.status.idle": "2022-12-07T16:34:08.700689Z", "shell.execute_reply": "2022-12-07T16:34:08.699912Z"}, "origin_pos": 55, "tab": ["pytorch"]}, "outputs": [{"data": {"text/plain": ["torch.Size([2, 100, 24])"]}, "execution_count": 12, "metadata": {}, "output_type": "execute_result"}], "source": ["decoder_blk = DecoderBlock(24, 24, 24, 24, [100, 24], 24, 48, 8, 0.5, 0)\n", "decoder_blk.eval()\n", "X = torch.ones((2, 100, 24))\n", "state = [encoder_blk(X, valid_lens), valid_lens, [None]]\n", "decoder_blk(X, state)[0].shape"]}, {"cell_type": "markdown", "id": "09042fad", "metadata": {"origin_pos": 57}, "source": ["现在我们构建了由`num_layers`个`DecoderBlock`实例组成的完整的[**Transformer解码器**]。最后，通过一个全连接层计算所有`vocab_size`个可能的输出词元的预测值。解码器的自注意力权重和编码器解码器注意力权重都被存储下来，方便日后可视化的需要。\n"]}, {"cell_type": "code", "execution_count": 13, "id": "48bc9555", "metadata": {"execution": {"iopub.execute_input": "2022-12-07T16:34:08.704417Z", "iopub.status.busy": "2022-12-07T16:34:08.703820Z", "iopub.status.idle": "2022-12-07T16:34:08.713484Z", "shell.execute_reply": "2022-12-07T16:34:08.712520Z"}, "origin_pos": 59, "tab": ["pytorch"]}, "outputs": [], "source": ["class TransformerDecoder(d2l.AttentionDecoder):\n", "    def __init__(self, vocab_size, key_size, query_size, value_size,\n", "                 num_hiddens, norm_shape, ffn_num_input, ffn_num_hiddens,\n", "                 num_heads, num_layers, dropout, **kwargs):\n", "        super(TransformerDecoder, self).__init__(**kwargs)\n", "        self.num_hiddens = num_hiddens\n", "        self.num_layers = num_layers\n", "        self.embedding = nn.Embedding(vocab_size, num_hiddens)\n", "        self.pos_encoding = d2l.PositionalEncoding(num_hiddens, dropout)\n", "        self.blks = nn.Sequential()\n", "        for i in range(num_layers):\n", "            self.blks.add_module(\"block\"+str(i),\n", "                DecoderBlock(key_size, query_size, value_size, num_hiddens,\n", "                             norm_shape, ffn_num_input, ffn_num_hiddens,\n", "                             num_heads, dropout, i))\n", "        self.dense = nn.Linear(num_hiddens, vocab_size)\n", "\n", "    def init_state(self, enc_outputs, enc_valid_lens, *args):\n", "        return [enc_outputs, enc_valid_lens, [None] * self.num_layers]\n", "\n", "    def forward(self, X, state):\n", "        X = self.pos_encoding(self.embedding(X) * math.sqrt(self.num_hiddens))\n", "        self._attention_weights = [[None] * len(self.blks) for _ in range (2)]\n", "        for i, blk in enumerate(self.blks):\n", "            X, state = blk(X, state)\n", "            # 解码器自注意力权重\n", "            self._attention_weights[0][\n", "                i] = blk.attention1.attention.attention_weights\n", "            # “编码器－解码器”自注意力权重\n", "            self._attention_weights[1][\n", "                i] = blk.attention2.attention.attention_weights\n", "        return self.dense(X), state\n", "\n", "    @property\n", "    def attention_weights(self):\n", "        return self._attention_weights"]}, {"cell_type": "markdown", "id": "9cbd01db", "metadata": {"origin_pos": 62}, "source": ["## [**训练**]\n", "\n", "依照Transformer架构来实例化编码器－解码器模型。在这里，指定Transformer的编码器和解码器都是2层，都使用4头注意力。与 :numref:`sec_seq2seq_training`类似，为了进行序列到序列的学习，下面在“英语－法语”机器翻译数据集上训练Transformer模型。\n"]}, {"cell_type": "code", "execution_count": 14, "id": "2f2a9c7f", "metadata": {"execution": {"iopub.execute_input": "2022-12-07T16:34:08.716972Z", "iopub.status.busy": "2022-12-07T16:34:08.716452Z", "iopub.status.idle": "2022-12-07T16:35:34.541221Z", "shell.execute_reply": "2022-12-07T16:35:34.540446Z"}, "origin_pos": 64, "tab": ["pytorch"]}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["loss 0.032, 5679.3 tokens/sec on cuda:0\n"]}, {"data": {"image/svg+xml": ["<?xml version=\"1.0\" encoding=\"utf-8\" standalone=\"no\"?>\n", "<!DOCTYPE svg PUBLIC \"-//W3C//DTD SVG 1.1//EN\"\n", "  \"http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd\">\n", "<svg xmlns:xlink=\"http://www.w3.org/1999/xlink\" width=\"262.1875pt\" height=\"183.35625pt\" viewBox=\"0 0 262.1875 183.35625\" xmlns=\"http://www.w3.org/2000/svg\" version=\"1.1\">\n", " <metadata>\n", "  <rdf:RDF xmlns:dc=\"http://purl.org/dc/elements/1.1/\" xmlns:cc=\"http://creativecommons.org/ns#\" xmlns:rdf=\"http://www.w3.org/1999/02/22-rdf-syntax-ns#\">\n", "   <cc:Work>\n", "    <dc:type rdf:resource=\"http://purl.org/dc/dcmitype/StillImage\"/>\n", "    <dc:date>2022-12-07T16:35:34.513599</dc:date>\n", "    <dc:format>image/svg+xml</dc:format>\n", "    <dc:creator>\n", "     <cc:Agent>\n", "      <dc:title>Matplotlib v3.5.1, https://matplotlib.org/</dc:title>\n", "     </cc:Agent>\n", "    </dc:creator>\n", "   </cc:Work>\n", "  </rdf:RDF>\n", " </metadata>\n", " <defs>\n", "  <style type=\"text/css\">*{stroke-linejoin: round; stroke-linecap: butt}</style>\n", " </defs>\n", " <g id=\"figure_1\">\n", "  <g id=\"patch_1\">\n", "   <path d=\"M 0 183.35625 \n", "L 262.1875 183.35625 \n", "L 262.1875 0 \n", "L 0 0 \n", "z\n", "\" style=\"fill: #ffffff\"/>\n", "  </g>\n", "  <g id=\"axes_1\">\n", "   <g id=\"patch_2\">\n", "    <path d=\"M 50.14375 145.8 \n", "L 245.44375 145.8 \n", "L 245.44375 7.2 \n", "L 50.14375 7.2 \n", "z\n", "\" style=\"fill: #ffffff\"/>\n", "   </g>\n", "   <g id=\"matplotlib.axis_1\">\n", "    <g id=\"xtick_1\">\n", "     <g id=\"line2d_1\">\n", "      <path d=\"M 91.259539 145.8 \n", "L 91.259539 7.2 \n", "\" clip-path=\"url(#p30295328bf)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_2\">\n", "      <defs>\n", "       <path id=\"m2dd983abac\" d=\"M 0 0 \n", "L 0 3.5 \n", "\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </defs>\n", "      <g>\n", "       <use xlink:href=\"#m2dd983abac\" x=\"91.259539\" y=\"145.8\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_1\">\n", "      <!-- 50 -->\n", "      <g transform=\"translate(84.897039 160.398438)scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-35\" d=\"M 691 4666 \n", "L 3169 4666 \n", "L 3169 4134 \n", "L 1269 4134 \n", "L 1269 2991 \n", "Q 1406 3038 1543 3061 \n", "Q 1681 3084 1819 3084 \n", "Q 2600 3084 3056 2656 \n", "Q 3513 2228 3513 1497 \n", "Q 3513 744 3044 326 \n", "Q 2575 -91 1722 -91 \n", "Q 1428 -91 1123 -41 \n", "Q 819 9 494 109 \n", "L 494 744 \n", "Q 775 591 1075 516 \n", "Q 1375 441 1709 441 \n", "Q 2250 441 2565 725 \n", "Q 2881 1009 2881 1497 \n", "Q 2881 1984 2565 2268 \n", "Q 2250 2553 1709 2553 \n", "Q 1456 2553 1204 2497 \n", "Q 953 2441 691 2322 \n", "L 691 4666 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "        <path id=\"DejaVuSans-30\" d=\"M 2034 4250 \n", "Q 1547 4250 1301 3770 \n", "Q 1056 3291 1056 2328 \n", "Q 1056 1369 1301 889 \n", "Q 1547 409 2034 409 \n", "Q 2525 409 2770 889 \n", "Q 3016 1369 3016 2328 \n", "Q 3016 3291 2770 3770 \n", "Q 2525 4250 2034 4250 \n", "z\n", "M 2034 4750 \n", "Q 2819 4750 3233 4129 \n", "Q 3647 3509 3647 2328 \n", "Q 3647 1150 3233 529 \n", "Q 2819 -91 2034 -91 \n", "Q 1250 -91 836 529 \n", "Q 422 1150 422 2328 \n", "Q 422 3509 836 4129 \n", "Q 1250 4750 2034 4750 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-35\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"63.623047\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_2\">\n", "     <g id=\"line2d_3\">\n", "      <path d=\"M 142.654276 145.8 \n", "L 142.654276 7.2 \n", "\" clip-path=\"url(#p30295328bf)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_4\">\n", "      <g>\n", "       <use xlink:href=\"#m2dd983abac\" x=\"142.654276\" y=\"145.8\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_2\">\n", "      <!-- 100 -->\n", "      <g transform=\"translate(133.110526 160.398438)scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-31\" d=\"M 794 531 \n", "L 1825 531 \n", "L 1825 4091 \n", "L 703 3866 \n", "L 703 4441 \n", "L 1819 4666 \n", "L 2450 4666 \n", "L 2450 531 \n", "L 3481 531 \n", "L 3481 0 \n", "L 794 0 \n", "L 794 531 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-31\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"127.246094\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_3\">\n", "     <g id=\"line2d_5\">\n", "      <path d=\"M 194.049013 145.8 \n", "L 194.049013 7.2 \n", "\" clip-path=\"url(#p30295328bf)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_6\">\n", "      <g>\n", "       <use xlink:href=\"#m2dd983abac\" x=\"194.049013\" y=\"145.8\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_3\">\n", "      <!-- 150 -->\n", "      <g transform=\"translate(184.505263 160.398438)scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-31\"/>\n", "       <use xlink:href=\"#DejaVuSans-35\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"127.246094\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_4\">\n", "     <g id=\"line2d_7\">\n", "      <path d=\"M 245.44375 145.8 \n", "L 245.44375 7.2 \n", "\" clip-path=\"url(#p30295328bf)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_8\">\n", "      <g>\n", "       <use xlink:href=\"#m2dd983abac\" x=\"245.44375\" y=\"145.8\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_4\">\n", "      <!-- 200 -->\n", "      <g transform=\"translate(235.9 160.398438)scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-32\" d=\"M 1228 531 \n", "L 3431 531 \n", "L 3431 0 \n", "L 469 0 \n", "L 469 531 \n", "Q 828 903 1448 1529 \n", "Q 2069 2156 2228 2338 \n", "Q 2531 2678 2651 2914 \n", "Q 2772 3150 2772 3378 \n", "Q 2772 3750 2511 3984 \n", "Q 2250 4219 1831 4219 \n", "Q 1534 4219 1204 4116 \n", "Q 875 4013 500 3803 \n", "L 500 4441 \n", "Q 881 4594 1212 4672 \n", "Q 1544 4750 1819 4750 \n", "Q 2544 4750 2975 4387 \n", "Q 3406 4025 3406 3419 \n", "Q 3406 3131 3298 2873 \n", "Q 3191 2616 2906 2266 \n", "Q 2828 2175 2409 1742 \n", "Q 1991 1309 1228 531 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-32\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"127.246094\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"text_5\">\n", "     <!-- epoch -->\n", "     <g transform=\"translate(132.565625 174.076563)scale(0.1 -0.1)\">\n", "      <defs>\n", "       <path id=\"DejaVuSans-65\" d=\"M 3597 1894 \n", "L 3597 1613 \n", "L 953 1613 \n", "Q 991 1019 1311 708 \n", "Q 1631 397 2203 397 \n", "Q 2534 397 2845 478 \n", "Q 3156 559 3463 722 \n", "L 3463 178 \n", "Q 3153 47 2828 -22 \n", "Q 2503 -91 2169 -91 \n", "Q 1331 -91 842 396 \n", "Q 353 884 353 1716 \n", "Q 353 2575 817 3079 \n", "Q 1281 3584 2069 3584 \n", "Q 2775 3584 3186 3129 \n", "Q 3597 2675 3597 1894 \n", "z\n", "M 3022 2063 \n", "Q 3016 2534 2758 2815 \n", "Q 2500 3097 2075 3097 \n", "Q 1594 3097 1305 2825 \n", "Q 1016 2553 972 2059 \n", "L 3022 2063 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-70\" d=\"M 1159 525 \n", "L 1159 -1331 \n", "L 581 -1331 \n", "L 581 3500 \n", "L 1159 3500 \n", "L 1159 2969 \n", "Q 1341 3281 1617 3432 \n", "Q 1894 3584 2278 3584 \n", "Q 2916 3584 3314 3078 \n", "Q 3713 2572 3713 1747 \n", "Q 3713 922 3314 415 \n", "Q 2916 -91 2278 -91 \n", "Q 1894 -91 1617 61 \n", "Q 1341 213 1159 525 \n", "z\n", "M 3116 1747 \n", "Q 3116 2381 2855 2742 \n", "Q 2594 3103 2138 3103 \n", "Q 1681 3103 1420 2742 \n", "Q 1159 2381 1159 1747 \n", "Q 1159 1113 1420 752 \n", "Q 1681 391 2138 391 \n", "Q 2594 391 2855 752 \n", "Q 3116 1113 3116 1747 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-6f\" d=\"M 1959 3097 \n", "Q 1497 3097 1228 2736 \n", "Q 959 2375 959 1747 \n", "Q 959 1119 1226 758 \n", "Q 1494 397 1959 397 \n", "Q 2419 397 2687 759 \n", "Q 2956 1122 2956 1747 \n", "Q 2956 2369 2687 2733 \n", "Q 2419 3097 1959 3097 \n", "z\n", "M 1959 3584 \n", "Q 2709 3584 3137 3096 \n", "Q 3566 2609 3566 1747 \n", "Q 3566 888 3137 398 \n", "Q 2709 -91 1959 -91 \n", "Q 1206 -91 779 398 \n", "Q 353 888 353 1747 \n", "Q 353 2609 779 3096 \n", "Q 1206 3584 1959 3584 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-63\" d=\"M 3122 3366 \n", "L 3122 2828 \n", "Q 2878 2963 2633 3030 \n", "Q 2388 3097 2138 3097 \n", "Q 1578 3097 1268 2742 \n", "Q 959 2388 959 1747 \n", "Q 959 1106 1268 751 \n", "Q 1578 397 2138 397 \n", "Q 2388 397 2633 464 \n", "Q 2878 531 3122 666 \n", "L 3122 134 \n", "Q 2881 22 2623 -34 \n", "Q 2366 -91 2075 -91 \n", "Q 1284 -91 818 406 \n", "Q 353 903 353 1747 \n", "Q 353 2603 823 3093 \n", "Q 1294 3584 2113 3584 \n", "Q 2378 3584 2631 3529 \n", "Q 2884 3475 3122 3366 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-68\" d=\"M 3513 2113 \n", "L 3513 0 \n", "L 2938 0 \n", "L 2938 2094 \n", "Q 2938 2591 2744 2837 \n", "Q 2550 3084 2163 3084 \n", "Q 1697 3084 1428 2787 \n", "Q 1159 2491 1159 1978 \n", "L 1159 0 \n", "L 581 0 \n", "L 581 4863 \n", "L 1159 4863 \n", "L 1159 2956 \n", "Q 1366 3272 1645 3428 \n", "Q 1925 3584 2291 3584 \n", "Q 2894 3584 3203 3211 \n", "Q 3513 2838 3513 2113 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "      </defs>\n", "      <use xlink:href=\"#DejaVuSans-65\"/>\n", "      <use xlink:href=\"#DejaVuSans-70\" x=\"61.523438\"/>\n", "      <use xlink:href=\"#DejaVuSans-6f\" x=\"125\"/>\n", "      <use xlink:href=\"#DejaVuSans-63\" x=\"186.181641\"/>\n", "      <use xlink:href=\"#DejaVuSans-68\" x=\"241.162109\"/>\n", "     </g>\n", "    </g>\n", "   </g>\n", "   <g id=\"matplotlib.axis_2\">\n", "    <g id=\"ytick_1\">\n", "     <g id=\"line2d_9\">\n", "      <path d=\"M 50.14375 124.646263 \n", "L 245.44375 124.646263 \n", "\" clip-path=\"url(#p30295328bf)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_10\">\n", "      <defs>\n", "       <path id=\"maa99309bcf\" d=\"M 0 0 \n", "L -3.5 0 \n", "\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </defs>\n", "      <g>\n", "       <use xlink:href=\"#maa99309bcf\" x=\"50.14375\" y=\"124.646263\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_6\">\n", "      <!-- 0.05 -->\n", "      <g transform=\"translate(20.878125 128.445482)scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-2e\" d=\"M 684 794 \n", "L 1344 794 \n", "L 1344 0 \n", "L 684 0 \n", "L 684 794 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"95.410156\"/>\n", "       <use xlink:href=\"#DejaVuSans-35\" x=\"159.033203\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_2\">\n", "     <g id=\"line2d_11\">\n", "      <path d=\"M 50.14375 83.99765 \n", "L 245.44375 83.99765 \n", "\" clip-path=\"url(#p30295328bf)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_12\">\n", "      <g>\n", "       <use xlink:href=\"#maa99309bcf\" x=\"50.14375\" y=\"83.99765\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_7\">\n", "      <!-- 0.10 -->\n", "      <g transform=\"translate(20.878125 87.796869)scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-31\" x=\"95.410156\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"159.033203\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_3\">\n", "     <g id=\"line2d_13\">\n", "      <path d=\"M 50.14375 43.349037 \n", "L 245.44375 43.349037 \n", "\" clip-path=\"url(#p30295328bf)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_14\">\n", "      <g>\n", "       <use xlink:href=\"#maa99309bcf\" x=\"50.14375\" y=\"43.349037\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_8\">\n", "      <!-- 0.15 -->\n", "      <g transform=\"translate(20.878125 47.148255)scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-31\" x=\"95.410156\"/>\n", "       <use xlink:href=\"#DejaVuSans-35\" x=\"159.033203\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"text_9\">\n", "     <!-- loss -->\n", "     <g transform=\"translate(14.798437 86.157813)rotate(-90)scale(0.1 -0.1)\">\n", "      <defs>\n", "       <path id=\"DejaVuSans-6c\" d=\"M 603 4863 \n", "L 1178 4863 \n", "L 1178 0 \n", "L 603 0 \n", "L 603 4863 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-73\" d=\"M 2834 3397 \n", "L 2834 2853 \n", "Q 2591 2978 2328 3040 \n", "Q 2066 3103 1784 3103 \n", "Q 1356 3103 1142 2972 \n", "Q 928 2841 928 2578 \n", "Q 928 2378 1081 2264 \n", "Q 1234 2150 1697 2047 \n", "L 1894 2003 \n", "Q 2506 1872 2764 1633 \n", "Q 3022 1394 3022 966 \n", "Q 3022 478 2636 193 \n", "Q 2250 -91 1575 -91 \n", "Q 1294 -91 989 -36 \n", "Q 684 19 347 128 \n", "L 347 722 \n", "Q 666 556 975 473 \n", "Q 1284 391 1588 391 \n", "Q 1994 391 2212 530 \n", "Q 2431 669 2431 922 \n", "Q 2431 1156 2273 1281 \n", "Q 2116 1406 1581 1522 \n", "L 1381 1569 \n", "Q 847 1681 609 1914 \n", "Q 372 2147 372 2553 \n", "Q 372 3047 722 3315 \n", "Q 1072 3584 1716 3584 \n", "Q 2034 3584 2315 3537 \n", "Q 2597 3491 2834 3397 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "      </defs>\n", "      <use xlink:href=\"#DejaVuSans-6c\"/>\n", "      <use xlink:href=\"#DejaVuSans-6f\" x=\"27.783203\"/>\n", "      <use xlink:href=\"#DejaVuSans-73\" x=\"88.964844\"/>\n", "      <use xlink:href=\"#DejaVuSans-73\" x=\"141.064453\"/>\n", "     </g>\n", "    </g>\n", "   </g>\n", "   <g id=\"line2d_15\">\n", "    <path d=\"M 50.14375 13.5 \n", "L 60.422697 55.830458 \n", "L 70.701645 87.000295 \n", "L 80.980592 102.24174 \n", "L 91.259539 113.237329 \n", "L 101.538487 119.786048 \n", "L 111.817434 122.354522 \n", "L 122.096382 126.457999 \n", "L 132.375329 130.695746 \n", "L 142.654276 132.336598 \n", "L 152.933224 133.216436 \n", "L 163.212171 134.415944 \n", "L 173.491118 135.749591 \n", "L 183.770066 137.420407 \n", "L 194.049013 136.780696 \n", "L 204.327961 137.67044 \n", "L 214.606908 139.5 \n", "L 224.885855 138.556807 \n", "L 235.164803 139.296232 \n", "L 245.44375 138.985022 \n", "\" clip-path=\"url(#p30295328bf)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_3\">\n", "    <path d=\"M 50.14375 145.8 \n", "L 50.14375 7.2 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_4\">\n", "    <path d=\"M 245.44375 145.8 \n", "L 245.44375 7.2 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_5\">\n", "    <path d=\"M 50.14375 145.8 \n", "L 245.44375 145.8 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_6\">\n", "    <path d=\"M 50.14375 7.2 \n", "L 245.44375 7.2 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "  </g>\n", " </g>\n", " <defs>\n", "  <clipPath id=\"p30295328bf\">\n", "   <rect x=\"50.14375\" y=\"7.2\" width=\"195.3\" height=\"138.6\"/>\n", "  </clipPath>\n", " </defs>\n", "</svg>\n"], "text/plain": ["<Figure size 350x250 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["num_hiddens, num_layers, dropout, batch_size, num_steps = 32, 2, 0.1, 64, 10\n", "lr, num_epochs, device = 0.005, 200, d2l.try_gpu()\n", "ffn_num_input, ffn_num_hiddens, num_heads = 32, 64, 4\n", "key_size, query_size, value_size = 32, 32, 32\n", "norm_shape = [32]\n", "\n", "train_iter, src_vocab, tgt_vocab = d2l.load_data_nmt(batch_size, num_steps)\n", "\n", "encoder = TransformerEncoder(\n", "    len(src_vocab), key_size, query_size, value_size, num_hiddens,\n", "    norm_shape, ffn_num_input, ffn_num_hiddens, num_heads,\n", "    num_layers, dropout)\n", "decoder = TransformerDecoder(\n", "    len(tgt_vocab), key_size, query_size, value_size, num_hiddens,\n", "    norm_shape, ffn_num_input, ffn_num_hiddens, num_heads,\n", "    num_layers, dropout)\n", "net = d2l.EncoderDecoder(encoder, decoder)\n", "d2l.train_seq2seq(net, train_iter, lr, num_epochs, tgt_vocab, device)"]}, {"cell_type": "markdown", "id": "1174f82b", "metadata": {"origin_pos": 67}, "source": ["训练结束后，使用Transformer模型[**将一些英语句子翻译成法语**]，并且计算它们的BLEU分数。\n"]}, {"cell_type": "code", "execution_count": 15, "id": "8c06cb1f", "metadata": {"execution": {"iopub.execute_input": "2022-12-07T16:35:34.544662Z", "iopub.status.busy": "2022-12-07T16:35:34.544095Z", "iopub.status.idle": "2022-12-07T16:35:34.641586Z", "shell.execute_reply": "2022-12-07T16:35:34.640824Z"}, "origin_pos": 68, "tab": ["pytorch"]}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["go . => va !,  bleu 1.000\n", "i lost . => j'ai perdu .,  bleu 1.000\n", "he's calm . => il est calme .,  bleu 1.000\n", "i'm home . => je suis chez moi .,  bleu 1.000\n"]}], "source": ["engs = ['go .', \"i lost .\", 'he\\'s calm .', 'i\\'m home .']\n", "fras = ['va !', 'j\\'ai perdu .', 'il est calme .', 'je suis chez moi .']\n", "for eng, fra in zip(engs, fras):\n", "    translation, dec_attention_weight_seq = d2l.predict_seq2seq(\n", "        net, eng, src_vocab, tgt_vocab, num_steps, device, True)\n", "    print(f'{eng} => {translation}, ',\n", "          f'bleu {d2l.bleu(translation, fra, k=2):.3f}')"]}, {"cell_type": "markdown", "id": "cd612868", "metadata": {"origin_pos": 70}, "source": ["当进行最后一个英语到法语的句子翻译工作时，让我们[**可视化Transformer的注意力权重**]。编码器自注意力权重的形状为（编码器层数，注意力头数，`num_steps`或查询的数目，`num_steps`或“键－值”对的数目）。\n"]}, {"cell_type": "code", "execution_count": 16, "id": "7170c136", "metadata": {"execution": {"iopub.execute_input": "2022-12-07T16:35:34.644992Z", "iopub.status.busy": "2022-12-07T16:35:34.644441Z", "iopub.status.idle": "2022-12-07T16:35:34.649979Z", "shell.execute_reply": "2022-12-07T16:35:34.649258Z"}, "origin_pos": 71, "tab": ["pytorch"]}, "outputs": [{"data": {"text/plain": ["torch.Size([2, 4, 10, 10])"]}, "execution_count": 16, "metadata": {}, "output_type": "execute_result"}], "source": ["enc_attention_weights = torch.cat(net.encoder.attention_weights, 0).reshape((num_layers, num_heads,\n", "    -1, num_steps))\n", "enc_attention_weights.shape"]}, {"cell_type": "markdown", "id": "8b8e11da", "metadata": {"origin_pos": 72}, "source": ["在编码器的自注意力中，查询和键都来自相同的输入序列。因为填充词元是不携带信息的，因此通过指定输入序列的有效长度可以避免查询与使用填充词元的位置计算注意力。接下来，将逐行呈现两层多头注意力的权重。每个注意力头都根据查询、键和值的不同的表示子空间来表示不同的注意力。\n"]}, {"cell_type": "code", "execution_count": 17, "id": "fc6fe023", "metadata": {"execution": {"iopub.execute_input": "2022-12-07T16:35:34.653090Z", "iopub.status.busy": "2022-12-07T16:35:34.652565Z", "iopub.status.idle": "2022-12-07T16:35:35.407001Z", "shell.execute_reply": "2022-12-07T16:35:35.406220Z"}, "origin_pos": 74, "tab": ["pytorch"]}, "outputs": [{"data": {"image/svg+xml": ["<?xml version=\"1.0\" encoding=\"utf-8\" standalone=\"no\"?>\n", "<!DOCTYPE svg PUBLIC \"-//W3C//DTD SVG 1.1//EN\"\n", "  \"http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd\">\n", "<svg xmlns:xlink=\"http://www.w3.org/1999/xlink\" width=\"402.17495pt\" height=\"233.64481pt\" viewBox=\"0 0 402.17495 233.64481\" xmlns=\"http://www.w3.org/2000/svg\" version=\"1.1\">\n", " <metadata>\n", "  <rdf:RDF xmlns:dc=\"http://purl.org/dc/elements/1.1/\" xmlns:cc=\"http://creativecommons.org/ns#\" xmlns:rdf=\"http://www.w3.org/1999/02/22-rdf-syntax-ns#\">\n", "   <cc:Work>\n", "    <dc:type rdf:resource=\"http://purl.org/dc/dcmitype/StillImage\"/>\n", "    <dc:date>2022-12-07T16:35:35.262027</dc:date>\n", "    <dc:format>image/svg+xml</dc:format>\n", "    <dc:creator>\n", "     <cc:Agent>\n", "      <dc:title>Matplotlib v3.5.1, https://matplotlib.org/</dc:title>\n", "     </cc:Agent>\n", "    </dc:creator>\n", "   </cc:Work>\n", "  </rdf:RDF>\n", " </metadata>\n", " <defs>\n", "  <style type=\"text/css\">*{stroke-linejoin: round; stroke-linecap: butt}</style>\n", " </defs>\n", " <g id=\"figure_1\">\n", "  <g id=\"patch_1\">\n", "   <path d=\"M 0 233.64481 \n", "L 402.17495 233.64481 \n", "L 402.17495 0 \n", "L 0 0 \n", "z\n", "\" style=\"fill: #ffffff\"/>\n", "  </g>\n", "  <g id=\"axes_1\">\n", "   <g id=\"patch_2\">\n", "    <path d=\"M 34.240625 90.24856 \n", "L 102.17106 90.24856 \n", "L 102.17106 22.318125 \n", "L 34.240625 22.318125 \n", "z\n", "\" style=\"fill: #ffffff\"/>\n", "   </g>\n", "   <g clip-path=\"url(#p57fc6aeaf7)\">\n", "    <image xlink:href=\"data:image/png;base64,\n", "iVBORw0KGgoAAAANSUhEUgAAAF8AAABfCAYAAACOTBv1AAABGUlEQVR4nO3doRECMRBA0RwWSqA/KIkCKQBz+hAIZGKYL3hPr8j8WRex27G/jjFxP19nI2OMMR77c2mOj1P9gH8mfkj8kPgh8UPih8QPiR8SPyR+SPyQ+CHxQ+KHxA+JHxI/JH5I/JD4IfFD4ofED4kfEj8kfkj8kPgh8UPih8QPiR8SPyR+SPyQ+CHxQ+KHxA+JHxI/JH5I/JD4IfFD4ofED4kfEj8kfkj8kPgh8UPih8QPiR8SPyR+SPyQ+CHxQ+KHxA+JHxI/JH5oWznbsWrlvIfTHl82PyR+SPyQ+CHxQ+KHxA+JHxI/JH5I/JD4IfFD4ofED4kfEj8kfmi7jcv0G9HX32/Y/JD4IfFD4ofED4kfEj8kfugNTzkSZfM37ekAAAAASUVORK5CYII=\" id=\"image54f983e223\" transform=\"scale(1 -1)translate(0 -68.4)\" x=\"34.240625\" y=\"-21.84856\" width=\"68.4\" height=\"68.4\"/>\n", "   </g>\n", "   <g id=\"matplotlib.axis_1\">\n", "    <g id=\"xtick_1\">\n", "     <g id=\"line2d_1\">\n", "      <defs>\n", "       <path id=\"m19029c781b\" d=\"M 0 0 \n", "L 0 3.5 \n", "\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </defs>\n", "      <g>\n", "       <use xlink:href=\"#m19029c781b\" x=\"37.637147\" y=\"90.24856\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_2\">\n", "     <g id=\"line2d_2\">\n", "      <g>\n", "       <use xlink:href=\"#m19029c781b\" x=\"71.602364\" y=\"90.24856\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "   </g>\n", "   <g id=\"matplotlib.axis_2\">\n", "    <g id=\"ytick_1\">\n", "     <g id=\"line2d_3\">\n", "      <defs>\n", "       <path id=\"ma5bd30be11\" d=\"M 0 0 \n", "L -3.5 0 \n", "\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </defs>\n", "      <g>\n", "       <use xlink:href=\"#ma5bd30be11\" x=\"34.240625\" y=\"25.714647\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_1\">\n", "      <!-- 0 -->\n", "      <g transform=\"translate(20.878125 29.513865)scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-30\" d=\"M 2034 4250 \n", "Q 1547 4250 1301 3770 \n", "Q 1056 3291 1056 2328 \n", "Q 1056 1369 1301 889 \n", "Q 1547 409 2034 409 \n", "Q 2525 409 2770 889 \n", "Q 3016 1369 3016 2328 \n", "Q 3016 3291 2770 3770 \n", "Q 2525 4250 2034 4250 \n", "z\n", "M 2034 4750 \n", "Q 2819 4750 3233 4129 \n", "Q 3647 3509 3647 2328 \n", "Q 3647 1150 3233 529 \n", "Q 2819 -91 2034 -91 \n", "Q 1250 -91 836 529 \n", "Q 422 1150 422 2328 \n", "Q 422 3509 836 4129 \n", "Q 1250 4750 2034 4750 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_2\">\n", "     <g id=\"line2d_4\">\n", "      <g>\n", "       <use xlink:href=\"#ma5bd30be11\" x=\"34.240625\" y=\"59.679864\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_2\">\n", "      <!-- 5 -->\n", "      <g transform=\"translate(20.878125 63.479083)scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-35\" d=\"M 691 4666 \n", "L 3169 4666 \n", "L 3169 4134 \n", "L 1269 4134 \n", "L 1269 2991 \n", "Q 1406 3038 1543 3061 \n", "Q 1681 3084 1819 3084 \n", "Q 2600 3084 3056 2656 \n", "Q 3513 2228 3513 1497 \n", "Q 3513 744 3044 326 \n", "Q 2575 -91 1722 -91 \n", "Q 1428 -91 1123 -41 \n", "Q 819 9 494 109 \n", "L 494 744 \n", "Q 775 591 1075 516 \n", "Q 1375 441 1709 441 \n", "Q 2250 441 2565 725 \n", "Q 2881 1009 2881 1497 \n", "Q 2881 1984 2565 2268 \n", "Q 2250 2553 1709 2553 \n", "Q 1456 2553 1204 2497 \n", "Q 953 2441 691 2322 \n", "L 691 4666 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-35\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"text_3\">\n", "     <!-- Query positions -->\n", "     <g transform=\"translate(14.798437 95.477874)rotate(-90)scale(0.1 -0.1)\">\n", "      <defs>\n", "       <path id=\"DejaVuSans-51\" d=\"M 2522 4238 \n", "Q 1834 4238 1429 3725 \n", "Q 1025 3213 1025 2328 \n", "Q 1025 1447 1429 934 \n", "Q 1834 422 2522 422 \n", "Q 3209 422 3611 934 \n", "Q 4013 1447 4013 2328 \n", "Q 4013 3213 3611 3725 \n", "Q 3209 4238 2522 4238 \n", "z\n", "M 3406 84 \n", "L 4238 -825 \n", "L 3475 -825 \n", "L 2784 -78 \n", "Q 2681 -84 2626 -87 \n", "Q 2572 -91 2522 -91 \n", "Q 1538 -91 948 567 \n", "Q 359 1225 359 2328 \n", "Q 359 3434 948 4092 \n", "Q 1538 4750 2522 4750 \n", "Q 3503 4750 4090 4092 \n", "Q 4678 3434 4678 2328 \n", "Q 4678 1516 4351 937 \n", "Q 4025 359 3406 84 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-75\" d=\"M 544 1381 \n", "L 544 3500 \n", "L 1119 3500 \n", "L 1119 1403 \n", "Q 1119 906 1312 657 \n", "Q 1506 409 1894 409 \n", "Q 2359 409 2629 706 \n", "Q 2900 1003 2900 1516 \n", "L 2900 3500 \n", "L 3475 3500 \n", "L 3475 0 \n", "L 2900 0 \n", "L 2900 538 \n", "Q 2691 219 2414 64 \n", "Q 2138 -91 1772 -91 \n", "Q 1169 -91 856 284 \n", "Q 544 659 544 1381 \n", "z\n", "M 1991 3584 \n", "L 1991 3584 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-65\" d=\"M 3597 1894 \n", "L 3597 1613 \n", "L 953 1613 \n", "Q 991 1019 1311 708 \n", "Q 1631 397 2203 397 \n", "Q 2534 397 2845 478 \n", "Q 3156 559 3463 722 \n", "L 3463 178 \n", "Q 3153 47 2828 -22 \n", "Q 2503 -91 2169 -91 \n", "Q 1331 -91 842 396 \n", "Q 353 884 353 1716 \n", "Q 353 2575 817 3079 \n", "Q 1281 3584 2069 3584 \n", "Q 2775 3584 3186 3129 \n", "Q 3597 2675 3597 1894 \n", "z\n", "M 3022 2063 \n", "Q 3016 2534 2758 2815 \n", "Q 2500 3097 2075 3097 \n", "Q 1594 3097 1305 2825 \n", "Q 1016 2553 972 2059 \n", "L 3022 2063 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-72\" d=\"M 2631 2963 \n", "Q 2534 3019 2420 3045 \n", "Q 2306 3072 2169 3072 \n", "Q 1681 3072 1420 2755 \n", "Q 1159 2438 1159 1844 \n", "L 1159 0 \n", "L 581 0 \n", "L 581 3500 \n", "L 1159 3500 \n", "L 1159 2956 \n", "Q 1341 3275 1631 3429 \n", "Q 1922 3584 2338 3584 \n", "Q 2397 3584 2469 3576 \n", "Q 2541 3569 2628 3553 \n", "L 2631 2963 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-79\" d=\"M 2059 -325 \n", "Q 1816 -950 1584 -1140 \n", "Q 1353 -1331 966 -1331 \n", "L 506 -1331 \n", "L 506 -850 \n", "L 844 -850 \n", "Q 1081 -850 1212 -737 \n", "Q 1344 -625 1503 -206 \n", "L 1606 56 \n", "L 191 3500 \n", "L 800 3500 \n", "L 1894 763 \n", "L 2988 3500 \n", "L 3597 3500 \n", "L 2059 -325 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-20\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-70\" d=\"M 1159 525 \n", "L 1159 -1331 \n", "L 581 -1331 \n", "L 581 3500 \n", "L 1159 3500 \n", "L 1159 2969 \n", "Q 1341 3281 1617 3432 \n", "Q 1894 3584 2278 3584 \n", "Q 2916 3584 3314 3078 \n", "Q 3713 2572 3713 1747 \n", "Q 3713 922 3314 415 \n", "Q 2916 -91 2278 -91 \n", "Q 1894 -91 1617 61 \n", "Q 1341 213 1159 525 \n", "z\n", "M 3116 1747 \n", "Q 3116 2381 2855 2742 \n", "Q 2594 3103 2138 3103 \n", "Q 1681 3103 1420 2742 \n", "Q 1159 2381 1159 1747 \n", "Q 1159 1113 1420 752 \n", "Q 1681 391 2138 391 \n", "Q 2594 391 2855 752 \n", "Q 3116 1113 3116 1747 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-6f\" d=\"M 1959 3097 \n", "Q 1497 3097 1228 2736 \n", "Q 959 2375 959 1747 \n", "Q 959 1119 1226 758 \n", "Q 1494 397 1959 397 \n", "Q 2419 397 2687 759 \n", "Q 2956 1122 2956 1747 \n", "Q 2956 2369 2687 2733 \n", "Q 2419 3097 1959 3097 \n", "z\n", "M 1959 3584 \n", "Q 2709 3584 3137 3096 \n", "Q 3566 2609 3566 1747 \n", "Q 3566 888 3137 398 \n", "Q 2709 -91 1959 -91 \n", "Q 1206 -91 779 398 \n", "Q 353 888 353 1747 \n", "Q 353 2609 779 3096 \n", "Q 1206 3584 1959 3584 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-73\" d=\"M 2834 3397 \n", "L 2834 2853 \n", "Q 2591 2978 2328 3040 \n", "Q 2066 3103 1784 3103 \n", "Q 1356 3103 1142 2972 \n", "Q 928 2841 928 2578 \n", "Q 928 2378 1081 2264 \n", "Q 1234 2150 1697 2047 \n", "L 1894 2003 \n", "Q 2506 1872 2764 1633 \n", "Q 3022 1394 3022 966 \n", "Q 3022 478 2636 193 \n", "Q 2250 -91 1575 -91 \n", "Q 1294 -91 989 -36 \n", "Q 684 19 347 128 \n", "L 347 722 \n", "Q 666 556 975 473 \n", "Q 1284 391 1588 391 \n", "Q 1994 391 2212 530 \n", "Q 2431 669 2431 922 \n", "Q 2431 1156 2273 1281 \n", "Q 2116 1406 1581 1522 \n", "L 1381 1569 \n", "Q 847 1681 609 1914 \n", "Q 372 2147 372 2553 \n", "Q 372 3047 722 3315 \n", "Q 1072 3584 1716 3584 \n", "Q 2034 3584 2315 3537 \n", "Q 2597 3491 2834 3397 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-69\" d=\"M 603 3500 \n", "L 1178 3500 \n", "L 1178 0 \n", "L 603 0 \n", "L 603 3500 \n", "z\n", "M 603 4863 \n", "L 1178 4863 \n", "L 1178 4134 \n", "L 603 4134 \n", "L 603 4863 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-74\" d=\"M 1172 4494 \n", "L 1172 3500 \n", "L 2356 3500 \n", "L 2356 3053 \n", "L 1172 3053 \n", "L 1172 1153 \n", "Q 1172 725 1289 603 \n", "Q 1406 481 1766 481 \n", "L 2356 481 \n", "L 2356 0 \n", "L 1766 0 \n", "Q 1100 0 847 248 \n", "Q 594 497 594 1153 \n", "L 594 3053 \n", "L 172 3053 \n", "L 172 3500 \n", "L 594 3500 \n", "L 594 4494 \n", "L 1172 4494 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-6e\" d=\"M 3513 2113 \n", "L 3513 0 \n", "L 2938 0 \n", "L 2938 2094 \n", "Q 2938 2591 2744 2837 \n", "Q 2550 3084 2163 3084 \n", "Q 1697 3084 1428 2787 \n", "Q 1159 2491 1159 1978 \n", "L 1159 0 \n", "L 581 0 \n", "L 581 3500 \n", "L 1159 3500 \n", "L 1159 2956 \n", "Q 1366 3272 1645 3428 \n", "Q 1925 3584 2291 3584 \n", "Q 2894 3584 3203 3211 \n", "Q 3513 2838 3513 2113 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "      </defs>\n", "      <use xlink:href=\"#DejaVuSans-51\"/>\n", "      <use xlink:href=\"#DejaVuSans-75\" x=\"78.710938\"/>\n", "      <use xlink:href=\"#DejaVuSans-65\" x=\"142.089844\"/>\n", "      <use xlink:href=\"#DejaVuSans-72\" x=\"203.613281\"/>\n", "      <use xlink:href=\"#DejaVuSans-79\" x=\"244.726562\"/>\n", "      <use xlink:href=\"#DejaVuSans-20\" x=\"303.90625\"/>\n", "      <use xlink:href=\"#DejaVuSans-70\" x=\"335.693359\"/>\n", "      <use xlink:href=\"#DejaVuSans-6f\" x=\"399.169922\"/>\n", "      <use xlink:href=\"#DejaVuSans-73\" x=\"460.351562\"/>\n", "      <use xlink:href=\"#DejaVuSans-69\" x=\"512.451172\"/>\n", "      <use xlink:href=\"#DejaVuSans-74\" x=\"540.234375\"/>\n", "      <use xlink:href=\"#DejaVuSans-69\" x=\"579.443359\"/>\n", "      <use xlink:href=\"#DejaVuSans-6f\" x=\"607.226562\"/>\n", "      <use xlink:href=\"#DejaVuSans-6e\" x=\"668.408203\"/>\n", "      <use xlink:href=\"#DejaVuSans-73\" x=\"731.787109\"/>\n", "     </g>\n", "    </g>\n", "   </g>\n", "   <g id=\"patch_3\">\n", "    <path d=\"M 34.240625 90.24856 \n", "L 34.240625 22.318125 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_4\">\n", "    <path d=\"M 102.17106 90.24856 \n", "L 102.17106 22.318125 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_5\">\n", "    <path d=\"M 34.240625 90.24856 \n", "L 102.17106 90.24856 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_6\">\n", "    <path d=\"M 34.240625 22.318125 \n", "L 102.17106 22.318125 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"text_4\">\n", "    <!-- Head 1 -->\n", "    <g transform=\"translate(46.791467 16.318125)scale(0.12 -0.12)\">\n", "     <defs>\n", "      <path id=\"DejaVuSans-48\" d=\"M 628 4666 \n", "L 1259 4666 \n", "L 1259 2753 \n", "L 3553 2753 \n", "L 3553 4666 \n", "L 4184 4666 \n", "L 4184 0 \n", "L 3553 0 \n", "L 3553 2222 \n", "L 1259 2222 \n", "L 1259 0 \n", "L 628 0 \n", "L 628 4666 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "      <path id=\"DejaVuSans-61\" d=\"M 2194 1759 \n", "Q 1497 1759 1228 1600 \n", "Q 959 1441 959 1056 \n", "Q 959 750 1161 570 \n", "Q 1363 391 1709 391 \n", "Q 2188 391 2477 730 \n", "Q 2766 1069 2766 1631 \n", "L 2766 1759 \n", "L 2194 1759 \n", "z\n", "M 3341 1997 \n", "L 3341 0 \n", "L 2766 0 \n", "L 2766 531 \n", "Q 2569 213 2275 61 \n", "Q 1981 -91 1556 -91 \n", "Q 1019 -91 701 211 \n", "Q 384 513 384 1019 \n", "Q 384 1609 779 1909 \n", "Q 1175 2209 1959 2209 \n", "L 2766 2209 \n", "L 2766 2266 \n", "Q 2766 2663 2505 2880 \n", "Q 2244 3097 1772 3097 \n", "Q 1472 3097 1187 3025 \n", "Q 903 2953 641 2809 \n", "L 641 3341 \n", "Q 956 3463 1253 3523 \n", "Q 1550 3584 1831 3584 \n", "Q 2591 3584 2966 3190 \n", "Q 3341 2797 3341 1997 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "      <path id=\"DejaVuSans-64\" d=\"M 2906 2969 \n", "L 2906 4863 \n", "L 3481 4863 \n", "L 3481 0 \n", "L 2906 0 \n", "L 2906 525 \n", "Q 2725 213 2448 61 \n", "Q 2172 -91 1784 -91 \n", "Q 1150 -91 751 415 \n", "Q 353 922 353 1747 \n", "Q 353 2572 751 3078 \n", "Q 1150 3584 1784 3584 \n", "Q 2172 3584 2448 3432 \n", "Q 2725 3281 2906 2969 \n", "z\n", "M 947 1747 \n", "Q 947 1113 1208 752 \n", "Q 1469 391 1925 391 \n", "Q 2381 391 2643 752 \n", "Q 2906 1113 2906 1747 \n", "Q 2906 2381 2643 2742 \n", "Q 2381 3103 1925 3103 \n", "Q 1469 3103 1208 2742 \n", "Q 947 2381 947 1747 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "      <path id=\"DejaVuSans-31\" d=\"M 794 531 \n", "L 1825 531 \n", "L 1825 4091 \n", "L 703 3866 \n", "L 703 4441 \n", "L 1819 4666 \n", "L 2450 4666 \n", "L 2450 531 \n", "L 3481 531 \n", "L 3481 0 \n", "L 794 0 \n", "L 794 531 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "     </defs>\n", "     <use xlink:href=\"#DejaVuSans-48\"/>\n", "     <use xlink:href=\"#DejaVuSans-65\" x=\"75.195312\"/>\n", "     <use xlink:href=\"#DejaVuSans-61\" x=\"136.71875\"/>\n", "     <use xlink:href=\"#DejaVuSans-64\" x=\"197.998047\"/>\n", "     <use xlink:href=\"#DejaVuSans-20\" x=\"261.474609\"/>\n", "     <use xlink:href=\"#DejaVuSans-31\" x=\"293.261719\"/>\n", "    </g>\n", "   </g>\n", "  </g>\n", "  <g id=\"axes_2\">\n", "   <g id=\"patch_7\">\n", "    <path d=\"M 115.757147 90.24856 \n", "L 183.687582 90.24856 \n", "L 183.687582 22.318125 \n", "L 115.757147 22.318125 \n", "z\n", "\" style=\"fill: #ffffff\"/>\n", "   </g>\n", "   <g clip-path=\"url(#pa7c66a5544)\">\n", "    <image xlink:href=\"data:image/png;base64,\n", "iVBORw0KGgoAAAANSUhEUgAAAF8AAABfCAYAAACOTBv1AAABHElEQVR4nO3dsQ3CMBRAwZgWRmA/GIkBGYAmdSgpcUG4Iu9qF9bTbyxL9tjW17b80f18nVr3WJ8778Q76Q0cWfGh4kPFh4oPFR8qPlR8qPhQ8aHiQ8WHig8VHyo+VHyo+FDxoeJDxYeKDxUfKj5UfKj4UPGh4kPFh4oPFR8qPlR8qPhQ8aHiQ8WHig8VHyo+VHyo+FDxoeJDxYeKDxUfKj5UfKj4UPGh4kPFh4oPFR8qPlR8aNyWy9dHTY/wwKjQ5EPFh4oPFR8qPlR8qPhQ8aHiQ2Pm246+2thHkw8VHyo+VHyo+FDxoeJDxYemDlm/1IHto8mHig8VHyo+VHyo+FDxoeJDxYe6RoSafKj4UPGh4kPFh4oPFR8qPvQGvDIgKa9jOsYAAAAASUVORK5CYII=\" id=\"image449b8fb3a5\" transform=\"scale(1 -1)translate(0 -68.4)\" x=\"115.757147\" y=\"-21.84856\" width=\"68.4\" height=\"68.4\"/>\n", "   </g>\n", "   <g id=\"matplotlib.axis_3\">\n", "    <g id=\"xtick_3\">\n", "     <g id=\"line2d_5\">\n", "      <g>\n", "       <use xlink:href=\"#m19029c781b\" x=\"119.153668\" y=\"90.24856\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_4\">\n", "     <g id=\"line2d_6\">\n", "      <g>\n", "       <use xlink:href=\"#m19029c781b\" x=\"153.118886\" y=\"90.24856\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "   </g>\n", "   <g id=\"matplotlib.axis_4\">\n", "    <g id=\"ytick_3\">\n", "     <g id=\"line2d_7\">\n", "      <g>\n", "       <use xlink:href=\"#ma5bd30be11\" x=\"115.757147\" y=\"25.714647\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_4\">\n", "     <g id=\"line2d_8\">\n", "      <g>\n", "       <use xlink:href=\"#ma5bd30be11\" x=\"115.757147\" y=\"59.679864\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "   </g>\n", "   <g id=\"patch_8\">\n", "    <path d=\"M 115.757147 90.24856 \n", "L 115.757147 22.318125 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_9\">\n", "    <path d=\"M 183.687582 90.24856 \n", "L 183.687582 22.318125 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_10\">\n", "    <path d=\"M 115.757147 90.24856 \n", "L 183.687582 90.24856 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_11\">\n", "    <path d=\"M 115.757147 22.318125 \n", "L 183.687582 22.318125 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"text_5\">\n", "    <!-- Head 2 -->\n", "    <g transform=\"translate(128.307989 16.318125)scale(0.12 -0.12)\">\n", "     <defs>\n", "      <path id=\"DejaVuSans-32\" d=\"M 1228 531 \n", "L 3431 531 \n", "L 3431 0 \n", "L 469 0 \n", "L 469 531 \n", "Q 828 903 1448 1529 \n", "Q 2069 2156 2228 2338 \n", "Q 2531 2678 2651 2914 \n", "Q 2772 3150 2772 3378 \n", "Q 2772 3750 2511 3984 \n", "Q 2250 4219 1831 4219 \n", "Q 1534 4219 1204 4116 \n", "Q 875 4013 500 3803 \n", "L 500 4441 \n", "Q 881 4594 1212 4672 \n", "Q 1544 4750 1819 4750 \n", "Q 2544 4750 2975 4387 \n", "Q 3406 4025 3406 3419 \n", "Q 3406 3131 3298 2873 \n", "Q 3191 2616 2906 2266 \n", "Q 2828 2175 2409 1742 \n", "Q 1991 1309 1228 531 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "     </defs>\n", "     <use xlink:href=\"#DejaVuSans-48\"/>\n", "     <use xlink:href=\"#DejaVuSans-65\" x=\"75.195312\"/>\n", "     <use xlink:href=\"#DejaVuSans-61\" x=\"136.71875\"/>\n", "     <use xlink:href=\"#DejaVuSans-64\" x=\"197.998047\"/>\n", "     <use xlink:href=\"#DejaVuSans-20\" x=\"261.474609\"/>\n", "     <use xlink:href=\"#DejaVuSans-32\" x=\"293.261719\"/>\n", "    </g>\n", "   </g>\n", "  </g>\n", "  <g id=\"axes_3\">\n", "   <g id=\"patch_12\">\n", "    <path d=\"M 197.273668 90.24856 \n", "L 265.204103 90.24856 \n", "L 265.204103 22.318125 \n", "L 197.273668 22.318125 \n", "z\n", "\" style=\"fill: #ffffff\"/>\n", "   </g>\n", "   <g clip-path=\"url(#p55ba7735ab)\">\n", "    <image xlink:href=\"data:image/png;base64,\n", "iVBORw0KGgoAAAANSUhEUgAAAF8AAABfCAYAAACOTBv1AAABLklEQVR4nO3csU1DMRRA0WdEB2zAfslEiPkQA1CQ+qdgAEdC0S1yTm19WVevsWz9dZ63YzY+fr92S2ZmZq110zr+PNUbeGTih8QPiR8SPyR+SPyQ+CHxQ8/HbA+4Tq53YvJD4ofED4kfEj8kfkj8kPgh8UPih8QPiR8SPyR+SPyQ+CHxQ+KHxA+JHxI/JH5I/JD4IfFD4ofED4kfEj8kfkj8kPgh8UPih8QPiR8SPyR+SPyQ+CHxQ+KHxA+JHxI/JH5oHZef7T9fzi/vN33s8/L97w09EpMfEj8kfkj8kPgh8UPih8QPiR8SPyR+SPyQ+CHxQ+KHxA+JHxI/tE7zur1GdD14HyY/JH5I/JD4IfFD4ofED4kfEj/koWzI5IfED4kfEj8kfkj8kPgh8UNXGtAYSua2P6UAAAAASUVORK5CYII=\" id=\"imagea51d795d79\" transform=\"scale(1 -1)translate(0 -68.4)\" x=\"197.273668\" y=\"-21.84856\" width=\"68.4\" height=\"68.4\"/>\n", "   </g>\n", "   <g id=\"matplotlib.axis_5\">\n", "    <g id=\"xtick_5\">\n", "     <g id=\"line2d_9\">\n", "      <g>\n", "       <use xlink:href=\"#m19029c781b\" x=\"200.67019\" y=\"90.24856\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_6\">\n", "     <g id=\"line2d_10\">\n", "      <g>\n", "       <use xlink:href=\"#m19029c781b\" x=\"234.635408\" y=\"90.24856\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "   </g>\n", "   <g id=\"matplotlib.axis_6\">\n", "    <g id=\"ytick_5\">\n", "     <g id=\"line2d_11\">\n", "      <g>\n", "       <use xlink:href=\"#ma5bd30be11\" x=\"197.273668\" y=\"25.714647\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_6\">\n", "     <g id=\"line2d_12\">\n", "      <g>\n", "       <use xlink:href=\"#ma5bd30be11\" x=\"197.273668\" y=\"59.679864\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "   </g>\n", "   <g id=\"patch_13\">\n", "    <path d=\"M 197.273668 90.24856 \n", "L 197.273668 22.318125 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_14\">\n", "    <path d=\"M 265.204103 90.24856 \n", "L 265.204103 22.318125 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_15\">\n", "    <path d=\"M 197.273668 90.24856 \n", "L 265.204103 90.24856 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_16\">\n", "    <path d=\"M 197.273668 22.318125 \n", "L 265.204103 22.318125 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"text_6\">\n", "    <!-- Head 3 -->\n", "    <g transform=\"translate(209.824511 16.318125)scale(0.12 -0.12)\">\n", "     <defs>\n", "      <path id=\"DejaVuSans-33\" d=\"M 2597 2516 \n", "Q 3050 2419 3304 2112 \n", "Q 3559 1806 3559 1356 \n", "Q 3559 666 3084 287 \n", "Q 2609 -91 1734 -91 \n", "Q 1441 -91 1130 -33 \n", "Q 819 25 488 141 \n", "L 488 750 \n", "Q 750 597 1062 519 \n", "Q 1375 441 1716 441 \n", "Q 2309 441 2620 675 \n", "Q 2931 909 2931 1356 \n", "Q 2931 1769 2642 2001 \n", "Q 2353 2234 1838 2234 \n", "L 1294 2234 \n", "L 1294 2753 \n", "L 1863 2753 \n", "Q 2328 2753 2575 2939 \n", "Q 2822 3125 2822 3475 \n", "Q 2822 3834 2567 4026 \n", "Q 2313 4219 1838 4219 \n", "Q 1578 4219 1281 4162 \n", "Q 984 4106 628 3988 \n", "L 628 4550 \n", "Q 988 4650 1302 4700 \n", "Q 1616 4750 1894 4750 \n", "Q 2613 4750 3031 4423 \n", "Q 3450 4097 3450 3541 \n", "Q 3450 3153 3228 2886 \n", "Q 3006 2619 2597 2516 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "     </defs>\n", "     <use xlink:href=\"#DejaVuSans-48\"/>\n", "     <use xlink:href=\"#DejaVuSans-65\" x=\"75.195312\"/>\n", "     <use xlink:href=\"#DejaVuSans-61\" x=\"136.71875\"/>\n", "     <use xlink:href=\"#DejaVuSans-64\" x=\"197.998047\"/>\n", "     <use xlink:href=\"#DejaVuSans-20\" x=\"261.474609\"/>\n", "     <use xlink:href=\"#DejaVuSans-33\" x=\"293.261719\"/>\n", "    </g>\n", "   </g>\n", "  </g>\n", "  <g id=\"axes_4\">\n", "   <g id=\"patch_17\">\n", "    <path d=\"M 278.79019 90.24856 \n", "L 346.720625 90.24856 \n", "L 346.720625 22.318125 \n", "L 278.79019 22.318125 \n", "z\n", "\" style=\"fill: #ffffff\"/>\n", "   </g>\n", "   <g clip-path=\"url(#pdf50211cf8)\">\n", "    <image xlink:href=\"data:image/png;base64,\n", "iVBORw0KGgoAAAANSUhEUgAAAF8AAABfCAYAAACOTBv1AAABmElEQVR4nO3dMUoDQRSA4XlmO4MgpBEU7O2sxcoLeIF0Eo9kay94BxVvYGchKNhYiBCwUMYjzCAv/pH9v3rYt/y8ZthAYlGmtTRcLF9aR/QLG/QLjJnxQcYHGR9kfJDxQcYHGR9kfNBwtrP9pwO/7667zk2OTlf8Jjw3H2R8kPFBxgcZH2R8kPFBxgfF1+1V8zPi5PAkbeDN/kHXueOnh7SZ68rNBxkfZHyQ8UHGBxkfZHyQ8UHGB8Xl1qx5w52/PqYNrG/PXeditpc2c125+SDjg4wPMj7I+CDjg4wPMj5ouP/4bB6aJw4cw+Wpl5sPMj7I+CDjg4wPMj7I+CDjg4wPMj7I+CDjg4wPMj7I+CDjg4wPMj5oiBJpD6u1+bPPEpE3779z80HGBxkfZHyQ8UHGBxkfZHyQ8UFRl+/ta2mi883drnNj+KsQNx9kfJDxQcYHGR9kfJDxQcYHDZkP67lAjeHy1MvNBxkfZHyQ8UHGBxkfZHyQ8UHGB8WiTJufEb2VroabDzI+yPgg44OMDzI+yPgg44N+ACN/KgK1gKULAAAAAElFTkSuQmCC\" id=\"image739875f856\" transform=\"scale(1 -1)translate(0 -68.4)\" x=\"278.79019\" y=\"-21.84856\" width=\"68.4\" height=\"68.4\"/>\n", "   </g>\n", "   <g id=\"matplotlib.axis_7\">\n", "    <g id=\"xtick_7\">\n", "     <g id=\"line2d_13\">\n", "      <g>\n", "       <use xlink:href=\"#m19029c781b\" x=\"282.186712\" y=\"90.24856\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_8\">\n", "     <g id=\"line2d_14\">\n", "      <g>\n", "       <use xlink:href=\"#m19029c781b\" x=\"316.151929\" y=\"90.24856\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "   </g>\n", "   <g id=\"matplotlib.axis_8\">\n", "    <g id=\"ytick_7\">\n", "     <g id=\"line2d_15\">\n", "      <g>\n", "       <use xlink:href=\"#ma5bd30be11\" x=\"278.79019\" y=\"25.714647\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_8\">\n", "     <g id=\"line2d_16\">\n", "      <g>\n", "       <use xlink:href=\"#ma5bd30be11\" x=\"278.79019\" y=\"59.679864\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "   </g>\n", "   <g id=\"patch_18\">\n", "    <path d=\"M 278.79019 90.24856 \n", "L 278.79019 22.318125 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_19\">\n", "    <path d=\"M 346.720625 90.24856 \n", "L 346.720625 22.318125 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_20\">\n", "    <path d=\"M 278.79019 90.24856 \n", "L 346.720625 90.24856 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_21\">\n", "    <path d=\"M 278.79019 22.318125 \n", "L 346.720625 22.318125 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"text_7\">\n", "    <!-- Head 4 -->\n", "    <g transform=\"translate(291.341033 16.318125)scale(0.12 -0.12)\">\n", "     <defs>\n", "      <path id=\"DejaVuSans-34\" d=\"M 2419 4116 \n", "L 825 1625 \n", "L 2419 1625 \n", "L 2419 4116 \n", "z\n", "M 2253 4666 \n", "L 3047 4666 \n", "L 3047 1625 \n", "L 3713 1625 \n", "L 3713 1100 \n", "L 3047 1100 \n", "L 3047 0 \n", "L 2419 0 \n", "L 2419 1100 \n", "L 313 1100 \n", "L 313 1709 \n", "L 2253 4666 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "     </defs>\n", "     <use xlink:href=\"#DejaVuSans-48\"/>\n", "     <use xlink:href=\"#DejaVuSans-65\" x=\"75.195312\"/>\n", "     <use xlink:href=\"#DejaVuSans-61\" x=\"136.71875\"/>\n", "     <use xlink:href=\"#DejaVuSans-64\" x=\"197.998047\"/>\n", "     <use xlink:href=\"#DejaVuSans-20\" x=\"261.474609\"/>\n", "     <use xlink:href=\"#DejaVuSans-34\" x=\"293.261719\"/>\n", "    </g>\n", "   </g>\n", "  </g>\n", "  <g id=\"axes_5\">\n", "   <g id=\"patch_22\">\n", "    <path d=\"M 34.240625 196.08856 \n", "L 102.17106 196.08856 \n", "L 102.17106 128.158125 \n", "L 34.240625 128.158125 \n", "z\n", "\" style=\"fill: #ffffff\"/>\n", "   </g>\n", "   <g clip-path=\"url(#p4c20b79364)\">\n", "    <image xlink:href=\"data:image/png;base64,\n", "iVBORw0KGgoAAAANSUhEUgAAAF8AAABfCAYAAACOTBv1AAABxUlEQVR4nO3cMUpDQRRA0Zn/vwrBuIAogk2w0MbSThDsbAU3YGPhSsTSJYguIatRUglqECSgMeMS5hXCJfx76sdLuLxmICT/3N2UVNFeXNdGUkopLScP9aG9/dCu9ug0NLfKGvoL9JnxQcYHGR9kfJDxQcYHGR9kfFCXxwf1qcV3bNvnLDDzHtvVA14+yPgg44OMDzI+yPgg44OMD+qaw+PqUJm9hpaV2aw6kz/eQrv6wMsHGR9kfJDxQcYHGR9kfJDxQcYHdcvJY3WoObsMLcujnfrQaDe0qw+8fJDxQcYHGR9kfJDxQcYHGR/UlelLfartYtuGW/WZQWCmJ7x8kPFBxgcZH2R8kPFBxgcZH2R8UJfm8//btrZen2na//u8Feflg4wPMj7I+CDjg4wPMj7I+KB8lTarf2p6/zUNLSuluirlnEO7+sDLBxkfZHyQ8UHGBxkfZHyQ8UHGB3Ubud6/LH9j2yK7Aq/glPrxEvbyQcYHGR9kfJDxQcYHGR9kfFBePN1WXz1l+hxa1pyc14cGw9iu7XFobpV5+SDjg4wPMj7I+CDjg4wPMj7I+CB/KAvy8kHGBxkfZHyQ8UHGBxkfZHzQH+1uONVTmHC3AAAAAElFTkSuQmCC\" id=\"image25a9649929\" transform=\"scale(1 -1)translate(0 -68.4)\" x=\"34.240625\" y=\"-127.68856\" width=\"68.4\" height=\"68.4\"/>\n", "   </g>\n", "   <g id=\"matplotlib.axis_9\">\n", "    <g id=\"xtick_9\">\n", "     <g id=\"line2d_17\">\n", "      <g>\n", "       <use xlink:href=\"#m19029c781b\" x=\"37.637147\" y=\"196.08856\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_8\">\n", "      <!-- 0 -->\n", "      <g transform=\"translate(34.455897 210.686997)scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_10\">\n", "     <g id=\"line2d_18\">\n", "      <g>\n", "       <use xlink:href=\"#m19029c781b\" x=\"71.602364\" y=\"196.08856\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_9\">\n", "      <!-- 5 -->\n", "      <g transform=\"translate(68.421114 210.686997)scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-35\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"text_10\">\n", "     <!-- Key positions -->\n", "     <g transform=\"translate(35.142561 224.365122)scale(0.1 -0.1)\">\n", "      <defs>\n", "       <path id=\"DejaVuSans-4b\" d=\"M 628 4666 \n", "L 1259 4666 \n", "L 1259 2694 \n", "L 3353 4666 \n", "L 4166 4666 \n", "L 1850 2491 \n", "L 4331 0 \n", "L 3500 0 \n", "L 1259 2247 \n", "L 1259 0 \n", "L 628 0 \n", "L 628 4666 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "      </defs>\n", "      <use xlink:href=\"#DejaVuSans-4b\"/>\n", "      <use xlink:href=\"#DejaVuSans-65\" x=\"60.576172\"/>\n", "      <use xlink:href=\"#DejaVuSans-79\" x=\"122.099609\"/>\n", "      <use xlink:href=\"#DejaVuSans-20\" x=\"181.279297\"/>\n", "      <use xlink:href=\"#DejaVuSans-70\" x=\"213.066406\"/>\n", "      <use xlink:href=\"#DejaVuSans-6f\" x=\"276.542969\"/>\n", "      <use xlink:href=\"#DejaVuSans-73\" x=\"337.724609\"/>\n", "      <use xlink:href=\"#DejaVuSans-69\" x=\"389.824219\"/>\n", "      <use xlink:href=\"#DejaVuSans-74\" x=\"417.607422\"/>\n", "      <use xlink:href=\"#DejaVuSans-69\" x=\"456.816406\"/>\n", "      <use xlink:href=\"#DejaVuSans-6f\" x=\"484.599609\"/>\n", "      <use xlink:href=\"#DejaVuSans-6e\" x=\"545.78125\"/>\n", "      <use xlink:href=\"#DejaVuSans-73\" x=\"609.160156\"/>\n", "     </g>\n", "    </g>\n", "   </g>\n", "   <g id=\"matplotlib.axis_10\">\n", "    <g id=\"ytick_9\">\n", "     <g id=\"line2d_19\">\n", "      <g>\n", "       <use xlink:href=\"#ma5bd30be11\" x=\"34.240625\" y=\"131.554647\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_11\">\n", "      <!-- 0 -->\n", "      <g transform=\"translate(20.878125 135.353865)scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_10\">\n", "     <g id=\"line2d_20\">\n", "      <g>\n", "       <use xlink:href=\"#ma5bd30be11\" x=\"34.240625\" y=\"165.519864\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_12\">\n", "      <!-- 5 -->\n", "      <g transform=\"translate(20.878125 169.319083)scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-35\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"text_13\">\n", "     <!-- Query positions -->\n", "     <g transform=\"translate(14.798437 201.317874)rotate(-90)scale(0.1 -0.1)\">\n", "      <use xlink:href=\"#DejaVuSans-51\"/>\n", "      <use xlink:href=\"#DejaVuSans-75\" x=\"78.710938\"/>\n", "      <use xlink:href=\"#DejaVuSans-65\" x=\"142.089844\"/>\n", "      <use xlink:href=\"#DejaVuSans-72\" x=\"203.613281\"/>\n", "      <use xlink:href=\"#DejaVuSans-79\" x=\"244.726562\"/>\n", "      <use xlink:href=\"#DejaVuSans-20\" x=\"303.90625\"/>\n", "      <use xlink:href=\"#DejaVuSans-70\" x=\"335.693359\"/>\n", "      <use xlink:href=\"#DejaVuSans-6f\" x=\"399.169922\"/>\n", "      <use xlink:href=\"#DejaVuSans-73\" x=\"460.351562\"/>\n", "      <use xlink:href=\"#DejaVuSans-69\" x=\"512.451172\"/>\n", "      <use xlink:href=\"#DejaVuSans-74\" x=\"540.234375\"/>\n", "      <use xlink:href=\"#DejaVuSans-69\" x=\"579.443359\"/>\n", "      <use xlink:href=\"#DejaVuSans-6f\" x=\"607.226562\"/>\n", "      <use xlink:href=\"#DejaVuSans-6e\" x=\"668.408203\"/>\n", "      <use xlink:href=\"#DejaVuSans-73\" x=\"731.787109\"/>\n", "     </g>\n", "    </g>\n", "   </g>\n", "   <g id=\"patch_23\">\n", "    <path d=\"M 34.240625 196.08856 \n", "L 34.240625 128.158125 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_24\">\n", "    <path d=\"M 102.17106 196.08856 \n", "L 102.17106 128.158125 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_25\">\n", "    <path d=\"M 34.240625 196.08856 \n", "L 102.17106 196.08856 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_26\">\n", "    <path d=\"M 34.240625 128.158125 \n", "L 102.17106 128.158125 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"text_14\">\n", "    <!-- Head 1 -->\n", "    <g transform=\"translate(46.791467 122.158125)scale(0.12 -0.12)\">\n", "     <use xlink:href=\"#DejaVuSans-48\"/>\n", "     <use xlink:href=\"#DejaVuSans-65\" x=\"75.195312\"/>\n", "     <use xlink:href=\"#DejaVuSans-61\" x=\"136.71875\"/>\n", "     <use xlink:href=\"#DejaVuSans-64\" x=\"197.998047\"/>\n", "     <use xlink:href=\"#DejaVuSans-20\" x=\"261.474609\"/>\n", "     <use xlink:href=\"#DejaVuSans-31\" x=\"293.261719\"/>\n", "    </g>\n", "   </g>\n", "  </g>\n", "  <g id=\"axes_6\">\n", "   <g id=\"patch_27\">\n", "    <path d=\"M 115.757147 196.08856 \n", "L 183.687582 196.08856 \n", "L 183.687582 128.158125 \n", "L 115.757147 128.158125 \n", "z\n", "\" style=\"fill: #ffffff\"/>\n", "   </g>\n", "   <g clip-path=\"url(#p0dfe651b5c)\">\n", "    <image xlink:href=\"data:image/png;base64,\n", "iVBORw0KGgoAAAANSUhEUgAAAF8AAABfCAYAAACOTBv1AAAB4klEQVR4nO3dTWpTcRSG8XNv/2loJ0GxgSK1xVGHrsRlCC7BgesonQluQSi4g0IHnSqOnFmoNPhFyMftqDg8RxAeQp7f+OWkPLmTkEC75YfzIRLzd++zSUREjN+8TTf99FnpVvf4sLTbZD39B2wz44OMDzI+yPgg44OMDzI+yPigFqtVOup3W+3asP4/my3hkw8yPsj4IOODjA8yPsj4IOODWvz5lY+eHtSu/f6ZToblonSqq73iRvPJBxkfZHyQ8UHGBxkfZHyQ8UHGB7V49CQdLb5+Kx3bHe+lm673/X5gCZDxQcYHGR9kfJDxQcYHGR/Uot9JR8My/z1nRER0vpf/wlog44OMDzI+yPgg44OMDzI+yPigFn3+k9RhUfyEW7jlp+C/LAEyPsj4IOODjA8yPsj4IOODWjc9Skc3X25Lx44LX0nGaFy6tQ188kHGBxkfZHyQ8UHGBxkfZHyQ8UFt+HSdju5m89Kxk6PTwiuOSre2gU8+yPgg44OMDzI+yPgg44OMD2rD9VU6enH5sXRsfXmRj37MSrd2Xr4q7TaZTz7I+CDjg4wPMj7I+CDjg4wPMj6oW89uhmz0evK8dOzs++d8tCr+2479SWm3yXzyQcYHGR9kfJDxQcYHGR9kfNA9Ptk5CPn6oAwAAAAASUVORK5CYII=\" id=\"image7a09f4cf69\" transform=\"scale(1 -1)translate(0 -68.4)\" x=\"115.757147\" y=\"-127.68856\" width=\"68.4\" height=\"68.4\"/>\n", "   </g>\n", "   <g id=\"matplotlib.axis_11\">\n", "    <g id=\"xtick_11\">\n", "     <g id=\"line2d_21\">\n", "      <g>\n", "       <use xlink:href=\"#m19029c781b\" x=\"119.153668\" y=\"196.08856\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_15\">\n", "      <!-- 0 -->\n", "      <g transform=\"translate(115.972418 210.686997)scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_12\">\n", "     <g id=\"line2d_22\">\n", "      <g>\n", "       <use xlink:href=\"#m19029c781b\" x=\"153.118886\" y=\"196.08856\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_16\">\n", "      <!-- 5 -->\n", "      <g transform=\"translate(149.937636 210.686997)scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-35\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"text_17\">\n", "     <!-- Key positions -->\n", "     <g transform=\"translate(116.659083 224.365122)scale(0.1 -0.1)\">\n", "      <use xlink:href=\"#DejaVuSans-4b\"/>\n", "      <use xlink:href=\"#DejaVuSans-65\" x=\"60.576172\"/>\n", "      <use xlink:href=\"#DejaVuSans-79\" x=\"122.099609\"/>\n", "      <use xlink:href=\"#DejaVuSans-20\" x=\"181.279297\"/>\n", "      <use xlink:href=\"#DejaVuSans-70\" x=\"213.066406\"/>\n", "      <use xlink:href=\"#DejaVuSans-6f\" x=\"276.542969\"/>\n", "      <use xlink:href=\"#DejaVuSans-73\" x=\"337.724609\"/>\n", "      <use xlink:href=\"#DejaVuSans-69\" x=\"389.824219\"/>\n", "      <use xlink:href=\"#DejaVuSans-74\" x=\"417.607422\"/>\n", "      <use xlink:href=\"#DejaVuSans-69\" x=\"456.816406\"/>\n", "      <use xlink:href=\"#DejaVuSans-6f\" x=\"484.599609\"/>\n", "      <use xlink:href=\"#DejaVuSans-6e\" x=\"545.78125\"/>\n", "      <use xlink:href=\"#DejaVuSans-73\" x=\"609.160156\"/>\n", "     </g>\n", "    </g>\n", "   </g>\n", "   <g id=\"matplotlib.axis_12\">\n", "    <g id=\"ytick_11\">\n", "     <g id=\"line2d_23\">\n", "      <g>\n", "       <use xlink:href=\"#ma5bd30be11\" x=\"115.757147\" y=\"131.554647\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_12\">\n", "     <g id=\"line2d_24\">\n", "      <g>\n", "       <use xlink:href=\"#ma5bd30be11\" x=\"115.757147\" y=\"165.519864\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "   </g>\n", "   <g id=\"patch_28\">\n", "    <path d=\"M 115.757147 196.08856 \n", "L 115.757147 128.158125 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_29\">\n", "    <path d=\"M 183.687582 196.08856 \n", "L 183.687582 128.158125 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_30\">\n", "    <path d=\"M 115.757147 196.08856 \n", "L 183.687582 196.08856 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_31\">\n", "    <path d=\"M 115.757147 128.158125 \n", "L 183.687582 128.158125 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"text_18\">\n", "    <!-- Head 2 -->\n", "    <g transform=\"translate(128.307989 122.158125)scale(0.12 -0.12)\">\n", "     <use xlink:href=\"#DejaVuSans-48\"/>\n", "     <use xlink:href=\"#DejaVuSans-65\" x=\"75.195312\"/>\n", "     <use xlink:href=\"#DejaVuSans-61\" x=\"136.71875\"/>\n", "     <use xlink:href=\"#DejaVuSans-64\" x=\"197.998047\"/>\n", "     <use xlink:href=\"#DejaVuSans-20\" x=\"261.474609\"/>\n", "     <use xlink:href=\"#DejaVuSans-32\" x=\"293.261719\"/>\n", "    </g>\n", "   </g>\n", "  </g>\n", "  <g id=\"axes_7\">\n", "   <g id=\"patch_32\">\n", "    <path d=\"M 197.273668 196.08856 \n", "L 265.204103 196.08856 \n", "L 265.204103 128.158125 \n", "L 197.273668 128.158125 \n", "z\n", "\" style=\"fill: #ffffff\"/>\n", "   </g>\n", "   <g clip-path=\"url(#p17197fe9e6)\">\n", "    <image xlink:href=\"data:image/png;base64,\n", "iVBORw0KGgoAAAANSUhEUgAAAF8AAABfCAYAAACOTBv1AAAB8UlEQVR4nO3dsUpbcRSA8f+9uZYMLZIhpVBxacgk0slB8AHsLtpJcHPQyaFDX6BDBaFP0E3HvkEfwRcQ2iXU0kHEpdGkgzifM4R+XO73mw/nhi9nCQmkuj//PC+Bens/GimllDK/ugxnvmzupHYd/75KzbVZTb+ALjM+yPgg44OMDzI+yPgg44OMD2rK8xeL21bF7+Wf6Wxxz2s5Lx9kfJDxQcYHGR9kfJDxQcYHNWUwjKeqKrdt9hCO/Jre53Z1gJcPMj7I+CDjg4wPMj7I+CDjg4wPasrkZzy1Fv6W9lHdC0fe9JdyuzrAywcZH2R8kPFBxgcZH2R8kPFBTbm7Xdy2Ov66cdD4fj+xBMj4IOODjA8yPsj4IOODjA8yPqgp07//9YH9xKfgrvDyQcYHGR9kfJDxQcYHGR9kfFD1Y30c/hBz5eJratn8+7d4aLyW2tXbeJeaazMvH2R8kPFBxgcZH2R8kPFBxgcZH9S8PtoNhyZ7ub/tePXxMJypR29Tu7rAywcZH2R8kPFBxgcZH2R8kPFB1ezmOvwa8WQwSi37dHoQzvT2P6R2Vc/6qbk28/JBxgcZH2R8kPFBxgcZH2R8kPFBzez8LBx6/3I5taza2o6Hbq5Tu8pwNTfXYl4+yPgg44OMDzI+yPgg44OMD/oHAbgwvn/vlSEAAAAASUVORK5CYII=\" id=\"image0358f2de0f\" transform=\"scale(1 -1)translate(0 -68.4)\" x=\"197.273668\" y=\"-127.68856\" width=\"68.4\" height=\"68.4\"/>\n", "   </g>\n", "   <g id=\"matplotlib.axis_13\">\n", "    <g id=\"xtick_13\">\n", "     <g id=\"line2d_25\">\n", "      <g>\n", "       <use xlink:href=\"#m19029c781b\" x=\"200.67019\" y=\"196.08856\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_19\">\n", "      <!-- 0 -->\n", "      <g transform=\"translate(197.48894 210.686997)scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_14\">\n", "     <g id=\"line2d_26\">\n", "      <g>\n", "       <use xlink:href=\"#m19029c781b\" x=\"234.635408\" y=\"196.08856\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_20\">\n", "      <!-- 5 -->\n", "      <g transform=\"translate(231.454158 210.686997)scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-35\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"text_21\">\n", "     <!-- Key positions -->\n", "     <g transform=\"translate(198.175605 224.365122)scale(0.1 -0.1)\">\n", "      <use xlink:href=\"#DejaVuSans-4b\"/>\n", "      <use xlink:href=\"#DejaVuSans-65\" x=\"60.576172\"/>\n", "      <use xlink:href=\"#DejaVuSans-79\" x=\"122.099609\"/>\n", "      <use xlink:href=\"#DejaVuSans-20\" x=\"181.279297\"/>\n", "      <use xlink:href=\"#DejaVuSans-70\" x=\"213.066406\"/>\n", "      <use xlink:href=\"#DejaVuSans-6f\" x=\"276.542969\"/>\n", "      <use xlink:href=\"#DejaVuSans-73\" x=\"337.724609\"/>\n", "      <use xlink:href=\"#DejaVuSans-69\" x=\"389.824219\"/>\n", "      <use xlink:href=\"#DejaVuSans-74\" x=\"417.607422\"/>\n", "      <use xlink:href=\"#DejaVuSans-69\" x=\"456.816406\"/>\n", "      <use xlink:href=\"#DejaVuSans-6f\" x=\"484.599609\"/>\n", "      <use xlink:href=\"#DejaVuSans-6e\" x=\"545.78125\"/>\n", "      <use xlink:href=\"#DejaVuSans-73\" x=\"609.160156\"/>\n", "     </g>\n", "    </g>\n", "   </g>\n", "   <g id=\"matplotlib.axis_14\">\n", "    <g id=\"ytick_13\">\n", "     <g id=\"line2d_27\">\n", "      <g>\n", "       <use xlink:href=\"#ma5bd30be11\" x=\"197.273668\" y=\"131.554647\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_14\">\n", "     <g id=\"line2d_28\">\n", "      <g>\n", "       <use xlink:href=\"#ma5bd30be11\" x=\"197.273668\" y=\"165.519864\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "   </g>\n", "   <g id=\"patch_33\">\n", "    <path d=\"M 197.273668 196.08856 \n", "L 197.273668 128.158125 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_34\">\n", "    <path d=\"M 265.204103 196.08856 \n", "L 265.204103 128.158125 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_35\">\n", "    <path d=\"M 197.273668 196.08856 \n", "L 265.204103 196.08856 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_36\">\n", "    <path d=\"M 197.273668 128.158125 \n", "L 265.204103 128.158125 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"text_22\">\n", "    <!-- Head 3 -->\n", "    <g transform=\"translate(209.824511 122.158125)scale(0.12 -0.12)\">\n", "     <use xlink:href=\"#DejaVuSans-48\"/>\n", "     <use xlink:href=\"#DejaVuSans-65\" x=\"75.195312\"/>\n", "     <use xlink:href=\"#DejaVuSans-61\" x=\"136.71875\"/>\n", "     <use xlink:href=\"#DejaVuSans-64\" x=\"197.998047\"/>\n", "     <use xlink:href=\"#DejaVuSans-20\" x=\"261.474609\"/>\n", "     <use xlink:href=\"#DejaVuSans-33\" x=\"293.261719\"/>\n", "    </g>\n", "   </g>\n", "  </g>\n", "  <g id=\"axes_8\">\n", "   <g id=\"patch_37\">\n", "    <path d=\"M 278.79019 196.08856 \n", "L 346.720625 196.08856 \n", "L 346.720625 128.158125 \n", "L 278.79019 128.158125 \n", "z\n", "\" style=\"fill: #ffffff\"/>\n", "   </g>\n", "   <g clip-path=\"url(#p5f463500e1)\">\n", "    <image xlink:href=\"data:image/png;base64,\n", "iVBORw0KGgoAAAANSUhEUgAAAF8AAABfCAYAAACOTBv1AAABN0lEQVR4nO3dsU1DMRRAUTuiywaIKmIBRgklE1AzDQMyQJBIRfEpGMC/4grlnPrJerpy48pzu162sfB6fFiNjDHGeL9+7Jrj16Fe4JaJHxI/JH5I/JD4IfFD4ofED4kfEj8kfkj8kPgh8UPih8QPiR8SPyR+SPyQ+CHxQ+KHxA+JHxI/JH5I/JD4IfFD4ofED4kfEj8kfkj8kPgh8UPih8QPiR8SPyR+SPyQ+CHxQ+KHxA+JHxI/JH5I/JD4IfFD4ofED4kfEj8kfkj8kPgh8UPih8QPiR8SPyR+SPyQ+CHxQ+KHxA+JHxI/dLdty68RxxzzD1a5PW5+SPyQ+CHxQ+KHxA+JHxI/JH5ofr89L5+48/yy77D703ro63PXWYfHp11z/5mbHxI/JH5I/JD4IfFD4ofED/0AlHoQhDH1C4MAAAAASUVORK5CYII=\" id=\"imaged979e1b54f\" transform=\"scale(1 -1)translate(0 -68.4)\" x=\"278.79019\" y=\"-127.68856\" width=\"68.4\" height=\"68.4\"/>\n", "   </g>\n", "   <g id=\"matplotlib.axis_15\">\n", "    <g id=\"xtick_15\">\n", "     <g id=\"line2d_29\">\n", "      <g>\n", "       <use xlink:href=\"#m19029c781b\" x=\"282.186712\" y=\"196.08856\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_23\">\n", "      <!-- 0 -->\n", "      <g transform=\"translate(279.005462 210.686997)scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_16\">\n", "     <g id=\"line2d_30\">\n", "      <g>\n", "       <use xlink:href=\"#m19029c781b\" x=\"316.151929\" y=\"196.08856\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_24\">\n", "      <!-- 5 -->\n", "      <g transform=\"translate(312.970679 210.686997)scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-35\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"text_25\">\n", "     <!-- Key positions -->\n", "     <g transform=\"translate(279.692126 224.365122)scale(0.1 -0.1)\">\n", "      <use xlink:href=\"#DejaVuSans-4b\"/>\n", "      <use xlink:href=\"#DejaVuSans-65\" x=\"60.576172\"/>\n", "      <use xlink:href=\"#DejaVuSans-79\" x=\"122.099609\"/>\n", "      <use xlink:href=\"#DejaVuSans-20\" x=\"181.279297\"/>\n", "      <use xlink:href=\"#DejaVuSans-70\" x=\"213.066406\"/>\n", "      <use xlink:href=\"#DejaVuSans-6f\" x=\"276.542969\"/>\n", "      <use xlink:href=\"#DejaVuSans-73\" x=\"337.724609\"/>\n", "      <use xlink:href=\"#DejaVuSans-69\" x=\"389.824219\"/>\n", "      <use xlink:href=\"#DejaVuSans-74\" x=\"417.607422\"/>\n", "      <use xlink:href=\"#DejaVuSans-69\" x=\"456.816406\"/>\n", "      <use xlink:href=\"#DejaVuSans-6f\" x=\"484.599609\"/>\n", "      <use xlink:href=\"#DejaVuSans-6e\" x=\"545.78125\"/>\n", "      <use xlink:href=\"#DejaVuSans-73\" x=\"609.160156\"/>\n", "     </g>\n", "    </g>\n", "   </g>\n", "   <g id=\"matplotlib.axis_16\">\n", "    <g id=\"ytick_15\">\n", "     <g id=\"line2d_31\">\n", "      <g>\n", "       <use xlink:href=\"#ma5bd30be11\" x=\"278.79019\" y=\"131.554647\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_16\">\n", "     <g id=\"line2d_32\">\n", "      <g>\n", "       <use xlink:href=\"#ma5bd30be11\" x=\"278.79019\" y=\"165.519864\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "   </g>\n", "   <g id=\"patch_38\">\n", "    <path d=\"M 278.79019 196.08856 \n", "L 278.79019 128.158125 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_39\">\n", "    <path d=\"M 346.720625 196.08856 \n", "L 346.720625 128.158125 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_40\">\n", "    <path d=\"M 278.79019 196.08856 \n", "L 346.720625 196.08856 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_41\">\n", "    <path d=\"M 278.79019 128.158125 \n", "L 346.720625 128.158125 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"text_26\">\n", "    <!-- Head 4 -->\n", "    <g transform=\"translate(291.341033 122.158125)scale(0.12 -0.12)\">\n", "     <use xlink:href=\"#DejaVuSans-48\"/>\n", "     <use xlink:href=\"#DejaVuSans-65\" x=\"75.195312\"/>\n", "     <use xlink:href=\"#DejaVuSans-61\" x=\"136.71875\"/>\n", "     <use xlink:href=\"#DejaVuSans-64\" x=\"197.998047\"/>\n", "     <use xlink:href=\"#DejaVuSans-20\" x=\"261.474609\"/>\n", "     <use xlink:href=\"#DejaVuSans-34\" x=\"293.261719\"/>\n", "    </g>\n", "   </g>\n", "  </g>\n", "  <g id=\"axes_9\">\n", "   <g id=\"patch_42\">\n", "    <path d=\"M 366.250625 167.415342 \n", "L 372.071825 167.415342 \n", "L 372.071825 50.991342 \n", "L 366.250625 50.991342 \n", "z\n", "\" style=\"fill: #ffffff\"/>\n", "   </g>\n", "   <g id=\"patch_43\">\n", "    <path clip-path=\"url(#p55a8fc3c42)\" style=\"fill: #ffffff; stroke: #ffffff; stroke-width: 0.01; stroke-linejoin: miter\"/>\n", "   </g>\n", "   <image xlink:href=\"data:image/png;base64,\n", "iVBORw0KGgoAAAANSUhEUgAAAAgAAACiCAYAAAB8iIwDAAAA7klEQVR4nN2XSw4DIQxDUyn3P2s33ZWkN8iLZGCgsx1kP8cwn1d+3mnF5ZZR3Te3LAU6CkELjCyCFrACxoQFqc9BV5gQExkOiBmDFsgKBJkbGBiSLIYakxnYgg6OHpMnKcc8YMvxqOUutkzy+S13xbmYADkh5g2QqCC/9fKEmFd0QZA58BGEXehtygx3xFw/h7/oAp7E5oEMFBMVBluU980D60YFYgg5Jk9S7mIHpNwFODRSgEBDARjNk34noatOTLRYr4CTHHoXBNnogixkSIxZfxx0Rv2VIVGhwSBb1AJzFGQLUsC6dUjck+u7+AE+P2IIsbjxbAAAAABJRU5ErkJggg==\" id=\"image1be0275f22\" transform=\"scale(1 -1)translate(0 -116.64)\" x=\"366.48\" y=\"-50.4\" width=\"5.76\" height=\"116.64\"/>\n", "   <g id=\"matplotlib.axis_17\">\n", "    <g id=\"ytick_17\">\n", "     <g id=\"line2d_33\">\n", "      <defs>\n", "       <path id=\"md871e5d9db\" d=\"M 0 0 \n", "L 3.5 0 \n", "\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </defs>\n", "      <g>\n", "       <use xlink:href=\"#md871e5d9db\" x=\"372.071825\" y=\"167.415342\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_27\">\n", "      <!-- 0.0 -->\n", "      <g transform=\"translate(379.071825 171.214561)scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-2e\" d=\"M 684 794 \n", "L 1344 794 \n", "L 1344 0 \n", "L 684 0 \n", "L 684 794 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"95.410156\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_18\">\n", "     <g id=\"line2d_34\">\n", "      <g>\n", "       <use xlink:href=\"#md871e5d9db\" x=\"372.071825\" y=\"144.130176\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_28\">\n", "      <!-- 0.2 -->\n", "      <g transform=\"translate(379.071825 147.929395)scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-32\" x=\"95.410156\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_19\">\n", "     <g id=\"line2d_35\">\n", "      <g>\n", "       <use xlink:href=\"#md871e5d9db\" x=\"372.071825\" y=\"120.84501\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_29\">\n", "      <!-- 0.4 -->\n", "      <g transform=\"translate(379.071825 124.644228)scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-34\" x=\"95.410156\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_20\">\n", "     <g id=\"line2d_36\">\n", "      <g>\n", "       <use xlink:href=\"#md871e5d9db\" x=\"372.071825\" y=\"97.559843\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_30\">\n", "      <!-- 0.6 -->\n", "      <g transform=\"translate(379.071825 101.359062)scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-36\" d=\"M 2113 2584 \n", "Q 1688 2584 1439 2293 \n", "Q 1191 2003 1191 1497 \n", "Q 1191 994 1439 701 \n", "Q 1688 409 2113 409 \n", "Q 2538 409 2786 701 \n", "Q 3034 994 3034 1497 \n", "Q 3034 2003 2786 2293 \n", "Q 2538 2584 2113 2584 \n", "z\n", "M 3366 4563 \n", "L 3366 3988 \n", "Q 3128 4100 2886 4159 \n", "Q 2644 4219 2406 4219 \n", "Q 1781 4219 1451 3797 \n", "Q 1122 3375 1075 2522 \n", "Q 1259 2794 1537 2939 \n", "Q 1816 3084 2150 3084 \n", "Q 2853 3084 3261 2657 \n", "Q 3669 2231 3669 1497 \n", "Q 3669 778 3244 343 \n", "Q 2819 -91 2113 -91 \n", "Q 1303 -91 875 529 \n", "Q 447 1150 447 2328 \n", "Q 447 3434 972 4092 \n", "Q 1497 4750 2381 4750 \n", "Q 2619 4750 2861 4703 \n", "Q 3103 4656 3366 4563 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-36\" x=\"95.410156\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_21\">\n", "     <g id=\"line2d_37\">\n", "      <g>\n", "       <use xlink:href=\"#md871e5d9db\" x=\"372.071825\" y=\"74.274677\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_31\">\n", "      <!-- 0.8 -->\n", "      <g transform=\"translate(379.071825 78.073896)scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-38\" d=\"M 2034 2216 \n", "Q 1584 2216 1326 1975 \n", "Q 1069 1734 1069 1313 \n", "Q 1069 891 1326 650 \n", "Q 1584 409 2034 409 \n", "Q 2484 409 2743 651 \n", "Q 3003 894 3003 1313 \n", "Q 3003 1734 2745 1975 \n", "Q 2488 2216 2034 2216 \n", "z\n", "M 1403 2484 \n", "Q 997 2584 770 2862 \n", "Q 544 3141 544 3541 \n", "Q 544 4100 942 4425 \n", "Q 1341 4750 2034 4750 \n", "Q 2731 4750 3128 4425 \n", "Q 3525 4100 3525 3541 \n", "Q 3525 3141 3298 2862 \n", "Q 3072 2584 2669 2484 \n", "Q 3125 2378 3379 2068 \n", "Q 3634 1759 3634 1313 \n", "Q 3634 634 3220 271 \n", "Q 2806 -91 2034 -91 \n", "Q 1263 -91 848 271 \n", "Q 434 634 434 1313 \n", "Q 434 1759 690 2068 \n", "Q 947 2378 1403 2484 \n", "z\n", "M 1172 3481 \n", "Q 1172 3119 1398 2916 \n", "Q 1625 2713 2034 2713 \n", "Q 2441 2713 2670 2916 \n", "Q 2900 3119 2900 3481 \n", "Q 2900 3844 2670 4047 \n", "Q 2441 4250 2034 4250 \n", "Q 1625 4250 1398 4047 \n", "Q 1172 3844 1172 3481 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-38\" x=\"95.410156\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "   </g>\n", "   <g id=\"LineCollection_1\"/>\n", "   <g id=\"patch_44\">\n", "    <path d=\"M 366.250625 167.415342 \n", "L 369.161225 167.415342 \n", "L 372.071825 167.415342 \n", "L 372.071825 50.991342 \n", "L 369.161225 50.991342 \n", "L 366.250625 50.991342 \n", "L 366.250625 167.415342 \n", "z\n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "  </g>\n", " </g>\n", " <defs>\n", "  <clipPath id=\"p57fc6aeaf7\">\n", "   <rect x=\"34.240625\" y=\"22.318125\" width=\"67.930435\" height=\"67.930435\"/>\n", "  </clipPath>\n", "  <clipPath id=\"pa7c66a5544\">\n", "   <rect x=\"115.757147\" y=\"22.318125\" width=\"67.930435\" height=\"67.930435\"/>\n", "  </clipPath>\n", "  <clipPath id=\"p55ba7735ab\">\n", "   <rect x=\"197.273668\" y=\"22.318125\" width=\"67.930435\" height=\"67.930435\"/>\n", "  </clipPath>\n", "  <clipPath id=\"pdf50211cf8\">\n", "   <rect x=\"278.79019\" y=\"22.318125\" width=\"67.930435\" height=\"67.930435\"/>\n", "  </clipPath>\n", "  <clipPath id=\"p4c20b79364\">\n", "   <rect x=\"34.240625\" y=\"128.158125\" width=\"67.930435\" height=\"67.930435\"/>\n", "  </clipPath>\n", "  <clipPath id=\"p0dfe651b5c\">\n", "   <rect x=\"115.757147\" y=\"128.158125\" width=\"67.930435\" height=\"67.930435\"/>\n", "  </clipPath>\n", "  <clipPath id=\"p17197fe9e6\">\n", "   <rect x=\"197.273668\" y=\"128.158125\" width=\"67.930435\" height=\"67.930435\"/>\n", "  </clipPath>\n", "  <clipPath id=\"p5f463500e1\">\n", "   <rect x=\"278.79019\" y=\"128.158125\" width=\"67.930435\" height=\"67.930435\"/>\n", "  </clipPath>\n", "  <clipPath id=\"p55a8fc3c42\">\n", "   <rect x=\"366.250625\" y=\"50.991342\" width=\"5.8212\" height=\"116.424\"/>\n", "  </clipPath>\n", " </defs>\n", "</svg>\n"], "text/plain": ["<Figure size 700x350 with 9 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["d2l.show_heatmaps(\n", "    enc_attention_weights.cpu(), xlabel='Key positions',\n", "    ylabel='Query positions', titles=['Head %d' % i for i in range(1, 5)],\n", "    figsize=(7, 3.5))"]}, {"cell_type": "markdown", "id": "d7e18bf2", "metadata": {"origin_pos": 75}, "source": ["[**为了可视化解码器的自注意力权重和“编码器－解码器”的注意力权重，我们需要完成更多的数据操作工作。**]例如用零填充被掩蔽住的注意力权重。值得注意的是，解码器的自注意力权重和“编码器－解码器”的注意力权重都有相同的查询：即以*序列开始词元*（beginning-of-sequence,BOS）打头，再与后续输出的词元共同组成序列。\n"]}, {"cell_type": "code", "execution_count": 18, "id": "91a26759", "metadata": {"execution": {"iopub.execute_input": "2022-12-07T16:35:35.410418Z", "iopub.status.busy": "2022-12-07T16:35:35.410150Z", "iopub.status.idle": "2022-12-07T16:35:35.424393Z", "shell.execute_reply": "2022-12-07T16:35:35.423247Z"}, "origin_pos": 77, "tab": ["pytorch"]}, "outputs": [{"data": {"text/plain": ["(torch.<PERSON><PERSON>([2, 4, 6, 10]), torch.<PERSON><PERSON>([2, 4, 6, 10]))"]}, "execution_count": 18, "metadata": {}, "output_type": "execute_result"}], "source": ["dec_attention_weights_2d = [head[0].tolist()\n", "                            for step in dec_attention_weight_seq\n", "                            for attn in step for blk in attn for head in blk]\n", "dec_attention_weights_filled = torch.tensor(\n", "    pd.DataFrame(dec_attention_weights_2d).fillna(0.0).values)\n", "dec_attention_weights = dec_attention_weights_filled.reshape((-1, 2, num_layers, num_heads, num_steps))\n", "dec_self_attention_weights, dec_inter_attention_weights = \\\n", "    dec_attention_weights.permute(1, 2, 3, 0, 4)\n", "dec_self_attention_weights.shape, dec_inter_attention_weights.shape"]}, {"cell_type": "markdown", "id": "bb673888", "metadata": {"origin_pos": 80}, "source": ["由于解码器自注意力的自回归属性，查询不会对当前位置之后的“键－值”对进行注意力计算。\n"]}, {"cell_type": "code", "execution_count": 19, "id": "0a3cb785", "metadata": {"execution": {"iopub.execute_input": "2022-12-07T16:35:35.428197Z", "iopub.status.busy": "2022-12-07T16:35:35.427400Z", "iopub.status.idle": "2022-12-07T16:35:36.140436Z", "shell.execute_reply": "2022-12-07T16:35:36.139674Z"}, "origin_pos": 81, "tab": ["pytorch"]}, "outputs": [{"data": {"image/svg+xml": ["<?xml version=\"1.0\" encoding=\"utf-8\" standalone=\"no\"?>\n", "<!DOCTYPE svg PUBLIC \"-//W3C//DTD SVG 1.1//EN\"\n", "  \"http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd\">\n", "<svg xmlns:xlink=\"http://www.w3.org/1999/xlink\" width=\"402.17495pt\" height=\"233.64481pt\" viewBox=\"0 0 402.17495 233.64481\" xmlns=\"http://www.w3.org/2000/svg\" version=\"1.1\">\n", " <metadata>\n", "  <rdf:RDF xmlns:dc=\"http://purl.org/dc/elements/1.1/\" xmlns:cc=\"http://creativecommons.org/ns#\" xmlns:rdf=\"http://www.w3.org/1999/02/22-rdf-syntax-ns#\">\n", "   <cc:Work>\n", "    <dc:type rdf:resource=\"http://purl.org/dc/dcmitype/StillImage\"/>\n", "    <dc:date>2022-12-07T16:35:35.990724</dc:date>\n", "    <dc:format>image/svg+xml</dc:format>\n", "    <dc:creator>\n", "     <cc:Agent>\n", "      <dc:title>Matplotlib v3.5.1, https://matplotlib.org/</dc:title>\n", "     </cc:Agent>\n", "    </dc:creator>\n", "   </cc:Work>\n", "  </rdf:RDF>\n", " </metadata>\n", " <defs>\n", "  <style type=\"text/css\">*{stroke-linejoin: round; stroke-linecap: butt}</style>\n", " </defs>\n", " <g id=\"figure_1\">\n", "  <g id=\"patch_1\">\n", "   <path d=\"M 0 233.64481 \n", "L 402.17495 233.64481 \n", "L 402.17495 0 \n", "L 0 0 \n", "z\n", "\" style=\"fill: #ffffff\"/>\n", "  </g>\n", "  <g id=\"axes_1\">\n", "   <g id=\"patch_2\">\n", "    <path d=\"M 34.240625 90.24856 \n", "L 102.17106 90.24856 \n", "L 102.17106 22.318125 \n", "L 34.240625 22.318125 \n", "z\n", "\" style=\"fill: #ffffff\"/>\n", "   </g>\n", "   <g clip-path=\"url(#pcf64e38511)\">\n", "    <image xlink:href=\"data:image/png;base64,\n", "iVBORw0KGgoAAAANSUhEUgAAAF8AAABfCAYAAACOTBv1AAABHUlEQVR4nO3csQ3CMBBA0RjRwQjsByMxIAPQpA47xBFfiPf6RNbXVSfLY1vf28Iuj8tt6vvTQedgB/FD4ofED4kfEj8kfkj8kPgh8UPih8QPiR8SPyR+aNT7/Jmd+HN9HXiS7zP5IfFD4ofED4kfEj8kfkj8kPgh8UPih8QPiR8SPyR+6Dz7g9k76r++k59h8kPih8QPiR8SPyR+SPyQ+CHxQ+KHxA+JHxI/JH5I/JD4IfFD4ofED4kfEj8kfkj8kPgh8UPih8QPiR8SPyR+SPyQ+CHxQ+KHxA+JHxI/JH5I/JD4IfFD4ofED4kfEj8kfmjcl+vUE+7//GTLLJMfEj8kfkj8kPgh8UPih8QPiR8SPyR+SPyQ+CHxQ+KHPsatEvZb2MfeAAAAAElFTkSuQmCC\" id=\"image02c667cd11\" transform=\"scale(1 -1)translate(0 -68.4)\" x=\"34.240625\" y=\"-21.84856\" width=\"68.4\" height=\"68.4\"/>\n", "   </g>\n", "   <g id=\"matplotlib.axis_1\">\n", "    <g id=\"xtick_1\">\n", "     <g id=\"line2d_1\">\n", "      <defs>\n", "       <path id=\"m2d610d42e1\" d=\"M 0 0 \n", "L 0 3.5 \n", "\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </defs>\n", "      <g>\n", "       <use xlink:href=\"#m2d610d42e1\" x=\"39.901495\" y=\"90.24856\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_2\">\n", "     <g id=\"line2d_2\">\n", "      <g>\n", "       <use xlink:href=\"#m2d610d42e1\" x=\"96.51019\" y=\"90.24856\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "   </g>\n", "   <g id=\"matplotlib.axis_2\">\n", "    <g id=\"ytick_1\">\n", "     <g id=\"line2d_3\">\n", "      <defs>\n", "       <path id=\"m1942115f57\" d=\"M 0 0 \n", "L -3.5 0 \n", "\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </defs>\n", "      <g>\n", "       <use xlink:href=\"#m1942115f57\" x=\"34.240625\" y=\"27.978995\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_1\">\n", "      <!-- 0 -->\n", "      <g transform=\"translate(20.878125 31.778213)scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-30\" d=\"M 2034 4250 \n", "Q 1547 4250 1301 3770 \n", "Q 1056 3291 1056 2328 \n", "Q 1056 1369 1301 889 \n", "Q 1547 409 2034 409 \n", "Q 2525 409 2770 889 \n", "Q 3016 1369 3016 2328 \n", "Q 3016 3291 2770 3770 \n", "Q 2525 4250 2034 4250 \n", "z\n", "M 2034 4750 \n", "Q 2819 4750 3233 4129 \n", "Q 3647 3509 3647 2328 \n", "Q 3647 1150 3233 529 \n", "Q 2819 -91 2034 -91 \n", "Q 1250 -91 836 529 \n", "Q 422 1150 422 2328 \n", "Q 422 3509 836 4129 \n", "Q 1250 4750 2034 4750 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_2\">\n", "     <g id=\"line2d_4\">\n", "      <g>\n", "       <use xlink:href=\"#m1942115f57\" x=\"34.240625\" y=\"50.622473\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_2\">\n", "      <!-- 2 -->\n", "      <g transform=\"translate(20.878125 54.421692)scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-32\" d=\"M 1228 531 \n", "L 3431 531 \n", "L 3431 0 \n", "L 469 0 \n", "L 469 531 \n", "Q 828 903 1448 1529 \n", "Q 2069 2156 2228 2338 \n", "Q 2531 2678 2651 2914 \n", "Q 2772 3150 2772 3378 \n", "Q 2772 3750 2511 3984 \n", "Q 2250 4219 1831 4219 \n", "Q 1534 4219 1204 4116 \n", "Q 875 4013 500 3803 \n", "L 500 4441 \n", "Q 881 4594 1212 4672 \n", "Q 1544 4750 1819 4750 \n", "Q 2544 4750 2975 4387 \n", "Q 3406 4025 3406 3419 \n", "Q 3406 3131 3298 2873 \n", "Q 3191 2616 2906 2266 \n", "Q 2828 2175 2409 1742 \n", "Q 1991 1309 1228 531 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-32\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_3\">\n", "     <g id=\"line2d_5\">\n", "      <g>\n", "       <use xlink:href=\"#m1942115f57\" x=\"34.240625\" y=\"73.265951\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_3\">\n", "      <!-- 4 -->\n", "      <g transform=\"translate(20.878125 77.06517)scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-34\" d=\"M 2419 4116 \n", "L 825 1625 \n", "L 2419 1625 \n", "L 2419 4116 \n", "z\n", "M 2253 4666 \n", "L 3047 4666 \n", "L 3047 1625 \n", "L 3713 1625 \n", "L 3713 1100 \n", "L 3047 1100 \n", "L 3047 0 \n", "L 2419 0 \n", "L 2419 1100 \n", "L 313 1100 \n", "L 313 1709 \n", "L 2253 4666 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-34\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"text_4\">\n", "     <!-- Query positions -->\n", "     <g transform=\"translate(14.798437 95.477874)rotate(-90)scale(0.1 -0.1)\">\n", "      <defs>\n", "       <path id=\"DejaVuSans-51\" d=\"M 2522 4238 \n", "Q 1834 4238 1429 3725 \n", "Q 1025 3213 1025 2328 \n", "Q 1025 1447 1429 934 \n", "Q 1834 422 2522 422 \n", "Q 3209 422 3611 934 \n", "Q 4013 1447 4013 2328 \n", "Q 4013 3213 3611 3725 \n", "Q 3209 4238 2522 4238 \n", "z\n", "M 3406 84 \n", "L 4238 -825 \n", "L 3475 -825 \n", "L 2784 -78 \n", "Q 2681 -84 2626 -87 \n", "Q 2572 -91 2522 -91 \n", "Q 1538 -91 948 567 \n", "Q 359 1225 359 2328 \n", "Q 359 3434 948 4092 \n", "Q 1538 4750 2522 4750 \n", "Q 3503 4750 4090 4092 \n", "Q 4678 3434 4678 2328 \n", "Q 4678 1516 4351 937 \n", "Q 4025 359 3406 84 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-75\" d=\"M 544 1381 \n", "L 544 3500 \n", "L 1119 3500 \n", "L 1119 1403 \n", "Q 1119 906 1312 657 \n", "Q 1506 409 1894 409 \n", "Q 2359 409 2629 706 \n", "Q 2900 1003 2900 1516 \n", "L 2900 3500 \n", "L 3475 3500 \n", "L 3475 0 \n", "L 2900 0 \n", "L 2900 538 \n", "Q 2691 219 2414 64 \n", "Q 2138 -91 1772 -91 \n", "Q 1169 -91 856 284 \n", "Q 544 659 544 1381 \n", "z\n", "M 1991 3584 \n", "L 1991 3584 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-65\" d=\"M 3597 1894 \n", "L 3597 1613 \n", "L 953 1613 \n", "Q 991 1019 1311 708 \n", "Q 1631 397 2203 397 \n", "Q 2534 397 2845 478 \n", "Q 3156 559 3463 722 \n", "L 3463 178 \n", "Q 3153 47 2828 -22 \n", "Q 2503 -91 2169 -91 \n", "Q 1331 -91 842 396 \n", "Q 353 884 353 1716 \n", "Q 353 2575 817 3079 \n", "Q 1281 3584 2069 3584 \n", "Q 2775 3584 3186 3129 \n", "Q 3597 2675 3597 1894 \n", "z\n", "M 3022 2063 \n", "Q 3016 2534 2758 2815 \n", "Q 2500 3097 2075 3097 \n", "Q 1594 3097 1305 2825 \n", "Q 1016 2553 972 2059 \n", "L 3022 2063 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-72\" d=\"M 2631 2963 \n", "Q 2534 3019 2420 3045 \n", "Q 2306 3072 2169 3072 \n", "Q 1681 3072 1420 2755 \n", "Q 1159 2438 1159 1844 \n", "L 1159 0 \n", "L 581 0 \n", "L 581 3500 \n", "L 1159 3500 \n", "L 1159 2956 \n", "Q 1341 3275 1631 3429 \n", "Q 1922 3584 2338 3584 \n", "Q 2397 3584 2469 3576 \n", "Q 2541 3569 2628 3553 \n", "L 2631 2963 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-79\" d=\"M 2059 -325 \n", "Q 1816 -950 1584 -1140 \n", "Q 1353 -1331 966 -1331 \n", "L 506 -1331 \n", "L 506 -850 \n", "L 844 -850 \n", "Q 1081 -850 1212 -737 \n", "Q 1344 -625 1503 -206 \n", "L 1606 56 \n", "L 191 3500 \n", "L 800 3500 \n", "L 1894 763 \n", "L 2988 3500 \n", "L 3597 3500 \n", "L 2059 -325 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-20\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-70\" d=\"M 1159 525 \n", "L 1159 -1331 \n", "L 581 -1331 \n", "L 581 3500 \n", "L 1159 3500 \n", "L 1159 2969 \n", "Q 1341 3281 1617 3432 \n", "Q 1894 3584 2278 3584 \n", "Q 2916 3584 3314 3078 \n", "Q 3713 2572 3713 1747 \n", "Q 3713 922 3314 415 \n", "Q 2916 -91 2278 -91 \n", "Q 1894 -91 1617 61 \n", "Q 1341 213 1159 525 \n", "z\n", "M 3116 1747 \n", "Q 3116 2381 2855 2742 \n", "Q 2594 3103 2138 3103 \n", "Q 1681 3103 1420 2742 \n", "Q 1159 2381 1159 1747 \n", "Q 1159 1113 1420 752 \n", "Q 1681 391 2138 391 \n", "Q 2594 391 2855 752 \n", "Q 3116 1113 3116 1747 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-6f\" d=\"M 1959 3097 \n", "Q 1497 3097 1228 2736 \n", "Q 959 2375 959 1747 \n", "Q 959 1119 1226 758 \n", "Q 1494 397 1959 397 \n", "Q 2419 397 2687 759 \n", "Q 2956 1122 2956 1747 \n", "Q 2956 2369 2687 2733 \n", "Q 2419 3097 1959 3097 \n", "z\n", "M 1959 3584 \n", "Q 2709 3584 3137 3096 \n", "Q 3566 2609 3566 1747 \n", "Q 3566 888 3137 398 \n", "Q 2709 -91 1959 -91 \n", "Q 1206 -91 779 398 \n", "Q 353 888 353 1747 \n", "Q 353 2609 779 3096 \n", "Q 1206 3584 1959 3584 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-73\" d=\"M 2834 3397 \n", "L 2834 2853 \n", "Q 2591 2978 2328 3040 \n", "Q 2066 3103 1784 3103 \n", "Q 1356 3103 1142 2972 \n", "Q 928 2841 928 2578 \n", "Q 928 2378 1081 2264 \n", "Q 1234 2150 1697 2047 \n", "L 1894 2003 \n", "Q 2506 1872 2764 1633 \n", "Q 3022 1394 3022 966 \n", "Q 3022 478 2636 193 \n", "Q 2250 -91 1575 -91 \n", "Q 1294 -91 989 -36 \n", "Q 684 19 347 128 \n", "L 347 722 \n", "Q 666 556 975 473 \n", "Q 1284 391 1588 391 \n", "Q 1994 391 2212 530 \n", "Q 2431 669 2431 922 \n", "Q 2431 1156 2273 1281 \n", "Q 2116 1406 1581 1522 \n", "L 1381 1569 \n", "Q 847 1681 609 1914 \n", "Q 372 2147 372 2553 \n", "Q 372 3047 722 3315 \n", "Q 1072 3584 1716 3584 \n", "Q 2034 3584 2315 3537 \n", "Q 2597 3491 2834 3397 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-69\" d=\"M 603 3500 \n", "L 1178 3500 \n", "L 1178 0 \n", "L 603 0 \n", "L 603 3500 \n", "z\n", "M 603 4863 \n", "L 1178 4863 \n", "L 1178 4134 \n", "L 603 4134 \n", "L 603 4863 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-74\" d=\"M 1172 4494 \n", "L 1172 3500 \n", "L 2356 3500 \n", "L 2356 3053 \n", "L 1172 3053 \n", "L 1172 1153 \n", "Q 1172 725 1289 603 \n", "Q 1406 481 1766 481 \n", "L 2356 481 \n", "L 2356 0 \n", "L 1766 0 \n", "Q 1100 0 847 248 \n", "Q 594 497 594 1153 \n", "L 594 3053 \n", "L 172 3053 \n", "L 172 3500 \n", "L 594 3500 \n", "L 594 4494 \n", "L 1172 4494 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-6e\" d=\"M 3513 2113 \n", "L 3513 0 \n", "L 2938 0 \n", "L 2938 2094 \n", "Q 2938 2591 2744 2837 \n", "Q 2550 3084 2163 3084 \n", "Q 1697 3084 1428 2787 \n", "Q 1159 2491 1159 1978 \n", "L 1159 0 \n", "L 581 0 \n", "L 581 3500 \n", "L 1159 3500 \n", "L 1159 2956 \n", "Q 1366 3272 1645 3428 \n", "Q 1925 3584 2291 3584 \n", "Q 2894 3584 3203 3211 \n", "Q 3513 2838 3513 2113 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "      </defs>\n", "      <use xlink:href=\"#DejaVuSans-51\"/>\n", "      <use xlink:href=\"#DejaVuSans-75\" x=\"78.710938\"/>\n", "      <use xlink:href=\"#DejaVuSans-65\" x=\"142.089844\"/>\n", "      <use xlink:href=\"#DejaVuSans-72\" x=\"203.613281\"/>\n", "      <use xlink:href=\"#DejaVuSans-79\" x=\"244.726562\"/>\n", "      <use xlink:href=\"#DejaVuSans-20\" x=\"303.90625\"/>\n", "      <use xlink:href=\"#DejaVuSans-70\" x=\"335.693359\"/>\n", "      <use xlink:href=\"#DejaVuSans-6f\" x=\"399.169922\"/>\n", "      <use xlink:href=\"#DejaVuSans-73\" x=\"460.351562\"/>\n", "      <use xlink:href=\"#DejaVuSans-69\" x=\"512.451172\"/>\n", "      <use xlink:href=\"#DejaVuSans-74\" x=\"540.234375\"/>\n", "      <use xlink:href=\"#DejaVuSans-69\" x=\"579.443359\"/>\n", "      <use xlink:href=\"#DejaVuSans-6f\" x=\"607.226562\"/>\n", "      <use xlink:href=\"#DejaVuSans-6e\" x=\"668.408203\"/>\n", "      <use xlink:href=\"#DejaVuSans-73\" x=\"731.787109\"/>\n", "     </g>\n", "    </g>\n", "   </g>\n", "   <g id=\"patch_3\">\n", "    <path d=\"M 34.240625 90.24856 \n", "L 34.240625 22.318125 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_4\">\n", "    <path d=\"M 102.17106 90.24856 \n", "L 102.17106 22.318125 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_5\">\n", "    <path d=\"M 34.240625 90.24856 \n", "L 102.17106 90.24856 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_6\">\n", "    <path d=\"M 34.240625 22.318125 \n", "L 102.17106 22.318125 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"text_5\">\n", "    <!-- Head 1 -->\n", "    <g transform=\"translate(46.791467 16.318125)scale(0.12 -0.12)\">\n", "     <defs>\n", "      <path id=\"DejaVuSans-48\" d=\"M 628 4666 \n", "L 1259 4666 \n", "L 1259 2753 \n", "L 3553 2753 \n", "L 3553 4666 \n", "L 4184 4666 \n", "L 4184 0 \n", "L 3553 0 \n", "L 3553 2222 \n", "L 1259 2222 \n", "L 1259 0 \n", "L 628 0 \n", "L 628 4666 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "      <path id=\"DejaVuSans-61\" d=\"M 2194 1759 \n", "Q 1497 1759 1228 1600 \n", "Q 959 1441 959 1056 \n", "Q 959 750 1161 570 \n", "Q 1363 391 1709 391 \n", "Q 2188 391 2477 730 \n", "Q 2766 1069 2766 1631 \n", "L 2766 1759 \n", "L 2194 1759 \n", "z\n", "M 3341 1997 \n", "L 3341 0 \n", "L 2766 0 \n", "L 2766 531 \n", "Q 2569 213 2275 61 \n", "Q 1981 -91 1556 -91 \n", "Q 1019 -91 701 211 \n", "Q 384 513 384 1019 \n", "Q 384 1609 779 1909 \n", "Q 1175 2209 1959 2209 \n", "L 2766 2209 \n", "L 2766 2266 \n", "Q 2766 2663 2505 2880 \n", "Q 2244 3097 1772 3097 \n", "Q 1472 3097 1187 3025 \n", "Q 903 2953 641 2809 \n", "L 641 3341 \n", "Q 956 3463 1253 3523 \n", "Q 1550 3584 1831 3584 \n", "Q 2591 3584 2966 3190 \n", "Q 3341 2797 3341 1997 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "      <path id=\"DejaVuSans-64\" d=\"M 2906 2969 \n", "L 2906 4863 \n", "L 3481 4863 \n", "L 3481 0 \n", "L 2906 0 \n", "L 2906 525 \n", "Q 2725 213 2448 61 \n", "Q 2172 -91 1784 -91 \n", "Q 1150 -91 751 415 \n", "Q 353 922 353 1747 \n", "Q 353 2572 751 3078 \n", "Q 1150 3584 1784 3584 \n", "Q 2172 3584 2448 3432 \n", "Q 2725 3281 2906 2969 \n", "z\n", "M 947 1747 \n", "Q 947 1113 1208 752 \n", "Q 1469 391 1925 391 \n", "Q 2381 391 2643 752 \n", "Q 2906 1113 2906 1747 \n", "Q 2906 2381 2643 2742 \n", "Q 2381 3103 1925 3103 \n", "Q 1469 3103 1208 2742 \n", "Q 947 2381 947 1747 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "      <path id=\"DejaVuSans-31\" d=\"M 794 531 \n", "L 1825 531 \n", "L 1825 4091 \n", "L 703 3866 \n", "L 703 4441 \n", "L 1819 4666 \n", "L 2450 4666 \n", "L 2450 531 \n", "L 3481 531 \n", "L 3481 0 \n", "L 794 0 \n", "L 794 531 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "     </defs>\n", "     <use xlink:href=\"#DejaVuSans-48\"/>\n", "     <use xlink:href=\"#DejaVuSans-65\" x=\"75.195312\"/>\n", "     <use xlink:href=\"#DejaVuSans-61\" x=\"136.71875\"/>\n", "     <use xlink:href=\"#DejaVuSans-64\" x=\"197.998047\"/>\n", "     <use xlink:href=\"#DejaVuSans-20\" x=\"261.474609\"/>\n", "     <use xlink:href=\"#DejaVuSans-31\" x=\"293.261719\"/>\n", "    </g>\n", "   </g>\n", "  </g>\n", "  <g id=\"axes_2\">\n", "   <g id=\"patch_7\">\n", "    <path d=\"M 115.757147 90.24856 \n", "L 183.687582 90.24856 \n", "L 183.687582 22.318125 \n", "L 115.757147 22.318125 \n", "z\n", "\" style=\"fill: #ffffff\"/>\n", "   </g>\n", "   <g clip-path=\"url(#p9de629ba07)\">\n", "    <image xlink:href=\"data:image/png;base64,\n", "iVBORw0KGgoAAAANSUhEUgAAAF8AAABfCAYAAACOTBv1AAABD0lEQVR4nO3csQ3CMBBA0Zg2GYH9YCQGzABpUocdcPGE8l9/kvV1lS15vJbtWiZ8zn1m/NYe+gB3Vnyo+FDxoeJDxYeKDxUfKj5UfKj4UPGh4kPFh4oPjes8pu7zZ73X58+z//6W0OZDxYeKDxUfKj5UfKj4UPGh4kPFh4oPFR8qPlR8qPhQ8aHiQ8WHig8VHyo+VHyo+FDxoeJDxYeKDxUfKj5UfKj4UPGh4kPFh4oPFR8qPlR8qPhQ8aHiQ8WHig8VHyo+VHxo9IW70+ZDxYeKDxUfKj5UfKj4UPGh4kPFh4oPFR8qPlR8qPhQ8aHiQ8WHig8VHyo+VHyo+FDxoeJDxYeKDxUfKj5UfKj40BelKw9lNGsZ8QAAAABJRU5ErkJggg==\" id=\"image08c9110377\" transform=\"scale(1 -1)translate(0 -68.4)\" x=\"115.757147\" y=\"-21.84856\" width=\"68.4\" height=\"68.4\"/>\n", "   </g>\n", "   <g id=\"matplotlib.axis_3\">\n", "    <g id=\"xtick_3\">\n", "     <g id=\"line2d_6\">\n", "      <g>\n", "       <use xlink:href=\"#m2d610d42e1\" x=\"121.418016\" y=\"90.24856\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_4\">\n", "     <g id=\"line2d_7\">\n", "      <g>\n", "       <use xlink:href=\"#m2d610d42e1\" x=\"178.026712\" y=\"90.24856\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "   </g>\n", "   <g id=\"matplotlib.axis_4\">\n", "    <g id=\"ytick_4\">\n", "     <g id=\"line2d_8\">\n", "      <g>\n", "       <use xlink:href=\"#m1942115f57\" x=\"115.757147\" y=\"27.978995\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_5\">\n", "     <g id=\"line2d_9\">\n", "      <g>\n", "       <use xlink:href=\"#m1942115f57\" x=\"115.757147\" y=\"50.622473\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_6\">\n", "     <g id=\"line2d_10\">\n", "      <g>\n", "       <use xlink:href=\"#m1942115f57\" x=\"115.757147\" y=\"73.265951\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "   </g>\n", "   <g id=\"patch_8\">\n", "    <path d=\"M 115.757147 90.24856 \n", "L 115.757147 22.318125 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_9\">\n", "    <path d=\"M 183.687582 90.24856 \n", "L 183.687582 22.318125 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_10\">\n", "    <path d=\"M 115.757147 90.24856 \n", "L 183.687582 90.24856 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_11\">\n", "    <path d=\"M 115.757147 22.318125 \n", "L 183.687582 22.318125 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"text_6\">\n", "    <!-- Head 2 -->\n", "    <g transform=\"translate(128.307989 16.318125)scale(0.12 -0.12)\">\n", "     <use xlink:href=\"#DejaVuSans-48\"/>\n", "     <use xlink:href=\"#DejaVuSans-65\" x=\"75.195312\"/>\n", "     <use xlink:href=\"#DejaVuSans-61\" x=\"136.71875\"/>\n", "     <use xlink:href=\"#DejaVuSans-64\" x=\"197.998047\"/>\n", "     <use xlink:href=\"#DejaVuSans-20\" x=\"261.474609\"/>\n", "     <use xlink:href=\"#DejaVuSans-32\" x=\"293.261719\"/>\n", "    </g>\n", "   </g>\n", "  </g>\n", "  <g id=\"axes_3\">\n", "   <g id=\"patch_12\">\n", "    <path d=\"M 197.273668 90.24856 \n", "L 265.204103 90.24856 \n", "L 265.204103 22.318125 \n", "L 197.273668 22.318125 \n", "z\n", "\" style=\"fill: #ffffff\"/>\n", "   </g>\n", "   <g clip-path=\"url(#pe354619074)\">\n", "    <image xlink:href=\"data:image/png;base64,\n", "iVBORw0KGgoAAAANSUhEUgAAAF8AAABfCAYAAACOTBv1AAABM0lEQVR4nO3csW0CQRBAUQ6HUAId4MBN0IVzUxIFuAs3QWB34AJICBHuwYP0A97LR7f6mui02uV+vdxXodvX579nXw7vo2//vL6N5vff59H8ejTNiPgh8UPih8QPiR8SPyR+SPyQ+CHxQ+KHxA+JHxI/tHystqP/+afr76PO8nRsfkj8kPgh8UPih8QPiR8SPyR+SPyQ+CHxQ+KHxA+JHxI/JH5I/JD4IfFD4ofED4kfEj8kfkj8kPgh8UPih8QPiR9apu/tHDe70QGe+X6/zQ+JHxI/JH5I/JD4IfFD4ofED4kfEj8kfkj8kPghT76EbH5I/JD4IfFD4ofED4kfEj8kfkj8kPgh8UPih8QPiR8SPyR+SPyQ+CHxQ+KHxA+JHxI/JH5I/JD4IfFD4ofED4kf+gN8CxlHJ0Pg1gAAAABJRU5ErkJggg==\" id=\"image016c74ce1f\" transform=\"scale(1 -1)translate(0 -68.4)\" x=\"197.273668\" y=\"-21.84856\" width=\"68.4\" height=\"68.4\"/>\n", "   </g>\n", "   <g id=\"matplotlib.axis_5\">\n", "    <g id=\"xtick_5\">\n", "     <g id=\"line2d_11\">\n", "      <g>\n", "       <use xlink:href=\"#m2d610d42e1\" x=\"202.934538\" y=\"90.24856\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_6\">\n", "     <g id=\"line2d_12\">\n", "      <g>\n", "       <use xlink:href=\"#m2d610d42e1\" x=\"259.543234\" y=\"90.24856\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "   </g>\n", "   <g id=\"matplotlib.axis_6\">\n", "    <g id=\"ytick_7\">\n", "     <g id=\"line2d_13\">\n", "      <g>\n", "       <use xlink:href=\"#m1942115f57\" x=\"197.273668\" y=\"27.978995\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_8\">\n", "     <g id=\"line2d_14\">\n", "      <g>\n", "       <use xlink:href=\"#m1942115f57\" x=\"197.273668\" y=\"50.622473\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_9\">\n", "     <g id=\"line2d_15\">\n", "      <g>\n", "       <use xlink:href=\"#m1942115f57\" x=\"197.273668\" y=\"73.265951\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "   </g>\n", "   <g id=\"patch_13\">\n", "    <path d=\"M 197.273668 90.24856 \n", "L 197.273668 22.318125 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_14\">\n", "    <path d=\"M 265.204103 90.24856 \n", "L 265.204103 22.318125 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_15\">\n", "    <path d=\"M 197.273668 90.24856 \n", "L 265.204103 90.24856 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_16\">\n", "    <path d=\"M 197.273668 22.318125 \n", "L 265.204103 22.318125 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"text_7\">\n", "    <!-- Head 3 -->\n", "    <g transform=\"translate(209.824511 16.318125)scale(0.12 -0.12)\">\n", "     <defs>\n", "      <path id=\"DejaVuSans-33\" d=\"M 2597 2516 \n", "Q 3050 2419 3304 2112 \n", "Q 3559 1806 3559 1356 \n", "Q 3559 666 3084 287 \n", "Q 2609 -91 1734 -91 \n", "Q 1441 -91 1130 -33 \n", "Q 819 25 488 141 \n", "L 488 750 \n", "Q 750 597 1062 519 \n", "Q 1375 441 1716 441 \n", "Q 2309 441 2620 675 \n", "Q 2931 909 2931 1356 \n", "Q 2931 1769 2642 2001 \n", "Q 2353 2234 1838 2234 \n", "L 1294 2234 \n", "L 1294 2753 \n", "L 1863 2753 \n", "Q 2328 2753 2575 2939 \n", "Q 2822 3125 2822 3475 \n", "Q 2822 3834 2567 4026 \n", "Q 2313 4219 1838 4219 \n", "Q 1578 4219 1281 4162 \n", "Q 984 4106 628 3988 \n", "L 628 4550 \n", "Q 988 4650 1302 4700 \n", "Q 1616 4750 1894 4750 \n", "Q 2613 4750 3031 4423 \n", "Q 3450 4097 3450 3541 \n", "Q 3450 3153 3228 2886 \n", "Q 3006 2619 2597 2516 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "     </defs>\n", "     <use xlink:href=\"#DejaVuSans-48\"/>\n", "     <use xlink:href=\"#DejaVuSans-65\" x=\"75.195312\"/>\n", "     <use xlink:href=\"#DejaVuSans-61\" x=\"136.71875\"/>\n", "     <use xlink:href=\"#DejaVuSans-64\" x=\"197.998047\"/>\n", "     <use xlink:href=\"#DejaVuSans-20\" x=\"261.474609\"/>\n", "     <use xlink:href=\"#DejaVuSans-33\" x=\"293.261719\"/>\n", "    </g>\n", "   </g>\n", "  </g>\n", "  <g id=\"axes_4\">\n", "   <g id=\"patch_17\">\n", "    <path d=\"M 278.79019 90.24856 \n", "L 346.720625 90.24856 \n", "L 346.720625 22.318125 \n", "L 278.79019 22.318125 \n", "z\n", "\" style=\"fill: #ffffff\"/>\n", "   </g>\n", "   <g clip-path=\"url(#p5cd93cd5d0)\">\n", "    <image xlink:href=\"data:image/png;base64,\n", "iVBORw0KGgoAAAANSUhEUgAAAF8AAABfCAYAAACOTBv1AAABB0lEQVR4nO3YsQ2DAAwAQUgLI7BfGIkBGYCGmuwASF/krrdkvVx5vM7jGrhlnZZH85+X9uAG8UPih8QPiR8SPyR+SPyQ+CHxQ+KHxA+JHxI/JH5o/A7zo3/+du5v7fJ3XH5I/JD4IfFD4ofED4kfEj8kfkj8kPgh8UPih8QPiR8SPyR+SPyQ+CHxQ+KHxA+JHxI/JH5I/JD4IfFD4ofED4kfEj8kfkj8kPgh8UPih8QPiR8SPyR+SPyQ+CHxQ+KHxA+JHxI/JH5I/JD4IfFD4ofED4kfEj8kfkj8kPgh8UPih8QPiR8SPyR+SPyQ+CHxQ+KHxA+JHxI/JH5I/JD4IfFD4ofED/0A7YwJEnBR4G8AAAAASUVORK5CYII=\" id=\"image670ea5b0ca\" transform=\"scale(1 -1)translate(0 -68.4)\" x=\"278.79019\" y=\"-21.84856\" width=\"68.4\" height=\"68.4\"/>\n", "   </g>\n", "   <g id=\"matplotlib.axis_7\">\n", "    <g id=\"xtick_7\">\n", "     <g id=\"line2d_16\">\n", "      <g>\n", "       <use xlink:href=\"#m2d610d42e1\" x=\"284.45106\" y=\"90.24856\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_8\">\n", "     <g id=\"line2d_17\">\n", "      <g>\n", "       <use xlink:href=\"#m2d610d42e1\" x=\"341.059755\" y=\"90.24856\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "   </g>\n", "   <g id=\"matplotlib.axis_8\">\n", "    <g id=\"ytick_10\">\n", "     <g id=\"line2d_18\">\n", "      <g>\n", "       <use xlink:href=\"#m1942115f57\" x=\"278.79019\" y=\"27.978995\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_11\">\n", "     <g id=\"line2d_19\">\n", "      <g>\n", "       <use xlink:href=\"#m1942115f57\" x=\"278.79019\" y=\"50.622473\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_12\">\n", "     <g id=\"line2d_20\">\n", "      <g>\n", "       <use xlink:href=\"#m1942115f57\" x=\"278.79019\" y=\"73.265951\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "   </g>\n", "   <g id=\"patch_18\">\n", "    <path d=\"M 278.79019 90.24856 \n", "L 278.79019 22.318125 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_19\">\n", "    <path d=\"M 346.720625 90.24856 \n", "L 346.720625 22.318125 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_20\">\n", "    <path d=\"M 278.79019 90.24856 \n", "L 346.720625 90.24856 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_21\">\n", "    <path d=\"M 278.79019 22.318125 \n", "L 346.720625 22.318125 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"text_8\">\n", "    <!-- Head 4 -->\n", "    <g transform=\"translate(291.341033 16.318125)scale(0.12 -0.12)\">\n", "     <use xlink:href=\"#DejaVuSans-48\"/>\n", "     <use xlink:href=\"#DejaVuSans-65\" x=\"75.195312\"/>\n", "     <use xlink:href=\"#DejaVuSans-61\" x=\"136.71875\"/>\n", "     <use xlink:href=\"#DejaVuSans-64\" x=\"197.998047\"/>\n", "     <use xlink:href=\"#DejaVuSans-20\" x=\"261.474609\"/>\n", "     <use xlink:href=\"#DejaVuSans-34\" x=\"293.261719\"/>\n", "    </g>\n", "   </g>\n", "  </g>\n", "  <g id=\"axes_5\">\n", "   <g id=\"patch_22\">\n", "    <path d=\"M 34.240625 196.08856 \n", "L 102.17106 196.08856 \n", "L 102.17106 128.158125 \n", "L 34.240625 128.158125 \n", "z\n", "\" style=\"fill: #ffffff\"/>\n", "   </g>\n", "   <g clip-path=\"url(#p7ebd79cc09)\">\n", "    <image xlink:href=\"data:image/png;base64,\n", "iVBORw0KGgoAAAANSUhEUgAAAF8AAABfCAYAAACOTBv1AAABgElEQVR4nO3cMUoDcRBG8Z0kVoqVZSBlWkG8gJcxhUJKaxuxsfY0nsDCIqUKgmCsEoR0ceMd9hMfi+/XD/vnMdUUW+3H864JbK8vk/GmJpPOs8OLm+zbVdF8aoB+/Z8zPsj4IOODjA8yPsj4IOODjA8yPsj4IOODjA8yPsj4oGrXn9E9/3F6Ej3g9HXRefb76SH69vD4LJpPufkg44OMDzI+yPgg44OMDzI+yPgg44OMDzI+yPgg44OMD6rdZh3d81Oz/XHn2fvN+y++5O+5+SDjg4wPMj7I+CDjg4wPMj7I+CDjg4wPMj7I+CDjg4wPqvngMLrn361esgeM9qL5PnPzQcYHGR9kfJDxQcYHGR9kfJDxQcYHGR9kfJDxQcYHVbtaRiflq6Np9IDbr7dovs/cfJDxQcYHGR9kfJDxQcYHGR9kfJDxQcYHGR9kfJDxQcYH1XlzEN3z+/7bFZKbDzI+yPgg44OMDzI+yPgg44OMDzI+yPgg44OMDzI+yPigH8/pJwtzXgXhAAAAAElFTkSuQmCC\" id=\"image26b1552c96\" transform=\"scale(1 -1)translate(0 -68.4)\" x=\"34.240625\" y=\"-127.68856\" width=\"68.4\" height=\"68.4\"/>\n", "   </g>\n", "   <g id=\"matplotlib.axis_9\">\n", "    <g id=\"xtick_9\">\n", "     <g id=\"line2d_21\">\n", "      <g>\n", "       <use xlink:href=\"#m2d610d42e1\" x=\"39.901495\" y=\"196.08856\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_9\">\n", "      <!-- 0 -->\n", "      <g transform=\"translate(36.720245 210.686997)scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_10\">\n", "     <g id=\"line2d_22\">\n", "      <g>\n", "       <use xlink:href=\"#m2d610d42e1\" x=\"96.51019\" y=\"196.08856\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_10\">\n", "      <!-- 5 -->\n", "      <g transform=\"translate(93.32894 210.686997)scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-35\" d=\"M 691 4666 \n", "L 3169 4666 \n", "L 3169 4134 \n", "L 1269 4134 \n", "L 1269 2991 \n", "Q 1406 3038 1543 3061 \n", "Q 1681 3084 1819 3084 \n", "Q 2600 3084 3056 2656 \n", "Q 3513 2228 3513 1497 \n", "Q 3513 744 3044 326 \n", "Q 2575 -91 1722 -91 \n", "Q 1428 -91 1123 -41 \n", "Q 819 9 494 109 \n", "L 494 744 \n", "Q 775 591 1075 516 \n", "Q 1375 441 1709 441 \n", "Q 2250 441 2565 725 \n", "Q 2881 1009 2881 1497 \n", "Q 2881 1984 2565 2268 \n", "Q 2250 2553 1709 2553 \n", "Q 1456 2553 1204 2497 \n", "Q 953 2441 691 2322 \n", "L 691 4666 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-35\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"text_11\">\n", "     <!-- Key positions -->\n", "     <g transform=\"translate(35.142561 224.365122)scale(0.1 -0.1)\">\n", "      <defs>\n", "       <path id=\"DejaVuSans-4b\" d=\"M 628 4666 \n", "L 1259 4666 \n", "L 1259 2694 \n", "L 3353 4666 \n", "L 4166 4666 \n", "L 1850 2491 \n", "L 4331 0 \n", "L 3500 0 \n", "L 1259 2247 \n", "L 1259 0 \n", "L 628 0 \n", "L 628 4666 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "      </defs>\n", "      <use xlink:href=\"#DejaVuSans-4b\"/>\n", "      <use xlink:href=\"#DejaVuSans-65\" x=\"60.576172\"/>\n", "      <use xlink:href=\"#DejaVuSans-79\" x=\"122.099609\"/>\n", "      <use xlink:href=\"#DejaVuSans-20\" x=\"181.279297\"/>\n", "      <use xlink:href=\"#DejaVuSans-70\" x=\"213.066406\"/>\n", "      <use xlink:href=\"#DejaVuSans-6f\" x=\"276.542969\"/>\n", "      <use xlink:href=\"#DejaVuSans-73\" x=\"337.724609\"/>\n", "      <use xlink:href=\"#DejaVuSans-69\" x=\"389.824219\"/>\n", "      <use xlink:href=\"#DejaVuSans-74\" x=\"417.607422\"/>\n", "      <use xlink:href=\"#DejaVuSans-69\" x=\"456.816406\"/>\n", "      <use xlink:href=\"#DejaVuSans-6f\" x=\"484.599609\"/>\n", "      <use xlink:href=\"#DejaVuSans-6e\" x=\"545.78125\"/>\n", "      <use xlink:href=\"#DejaVuSans-73\" x=\"609.160156\"/>\n", "     </g>\n", "    </g>\n", "   </g>\n", "   <g id=\"matplotlib.axis_10\">\n", "    <g id=\"ytick_13\">\n", "     <g id=\"line2d_23\">\n", "      <g>\n", "       <use xlink:href=\"#m1942115f57\" x=\"34.240625\" y=\"133.818995\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_12\">\n", "      <!-- 0 -->\n", "      <g transform=\"translate(20.878125 137.618213)scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_14\">\n", "     <g id=\"line2d_24\">\n", "      <g>\n", "       <use xlink:href=\"#m1942115f57\" x=\"34.240625\" y=\"156.462473\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_13\">\n", "      <!-- 2 -->\n", "      <g transform=\"translate(20.878125 160.261692)scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-32\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_15\">\n", "     <g id=\"line2d_25\">\n", "      <g>\n", "       <use xlink:href=\"#m1942115f57\" x=\"34.240625\" y=\"179.105951\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_14\">\n", "      <!-- 4 -->\n", "      <g transform=\"translate(20.878125 182.90517)scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-34\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"text_15\">\n", "     <!-- Query positions -->\n", "     <g transform=\"translate(14.798437 201.317874)rotate(-90)scale(0.1 -0.1)\">\n", "      <use xlink:href=\"#DejaVuSans-51\"/>\n", "      <use xlink:href=\"#DejaVuSans-75\" x=\"78.710938\"/>\n", "      <use xlink:href=\"#DejaVuSans-65\" x=\"142.089844\"/>\n", "      <use xlink:href=\"#DejaVuSans-72\" x=\"203.613281\"/>\n", "      <use xlink:href=\"#DejaVuSans-79\" x=\"244.726562\"/>\n", "      <use xlink:href=\"#DejaVuSans-20\" x=\"303.90625\"/>\n", "      <use xlink:href=\"#DejaVuSans-70\" x=\"335.693359\"/>\n", "      <use xlink:href=\"#DejaVuSans-6f\" x=\"399.169922\"/>\n", "      <use xlink:href=\"#DejaVuSans-73\" x=\"460.351562\"/>\n", "      <use xlink:href=\"#DejaVuSans-69\" x=\"512.451172\"/>\n", "      <use xlink:href=\"#DejaVuSans-74\" x=\"540.234375\"/>\n", "      <use xlink:href=\"#DejaVuSans-69\" x=\"579.443359\"/>\n", "      <use xlink:href=\"#DejaVuSans-6f\" x=\"607.226562\"/>\n", "      <use xlink:href=\"#DejaVuSans-6e\" x=\"668.408203\"/>\n", "      <use xlink:href=\"#DejaVuSans-73\" x=\"731.787109\"/>\n", "     </g>\n", "    </g>\n", "   </g>\n", "   <g id=\"patch_23\">\n", "    <path d=\"M 34.240625 196.08856 \n", "L 34.240625 128.158125 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_24\">\n", "    <path d=\"M 102.17106 196.08856 \n", "L 102.17106 128.158125 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_25\">\n", "    <path d=\"M 34.240625 196.08856 \n", "L 102.17106 196.08856 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_26\">\n", "    <path d=\"M 34.240625 128.158125 \n", "L 102.17106 128.158125 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"text_16\">\n", "    <!-- Head 1 -->\n", "    <g transform=\"translate(46.791467 122.158125)scale(0.12 -0.12)\">\n", "     <use xlink:href=\"#DejaVuSans-48\"/>\n", "     <use xlink:href=\"#DejaVuSans-65\" x=\"75.195312\"/>\n", "     <use xlink:href=\"#DejaVuSans-61\" x=\"136.71875\"/>\n", "     <use xlink:href=\"#DejaVuSans-64\" x=\"197.998047\"/>\n", "     <use xlink:href=\"#DejaVuSans-20\" x=\"261.474609\"/>\n", "     <use xlink:href=\"#DejaVuSans-31\" x=\"293.261719\"/>\n", "    </g>\n", "   </g>\n", "  </g>\n", "  <g id=\"axes_6\">\n", "   <g id=\"patch_27\">\n", "    <path d=\"M 115.757147 196.08856 \n", "L 183.687582 196.08856 \n", "L 183.687582 128.158125 \n", "L 115.757147 128.158125 \n", "z\n", "\" style=\"fill: #ffffff\"/>\n", "   </g>\n", "   <g clip-path=\"url(#pcc0618ac45)\">\n", "    <image xlink:href=\"data:image/png;base64,\n", "iVBORw0KGgoAAAANSUhEUgAAAF8AAABfCAYAAACOTBv1AAABeklEQVR4nO3aMUoDURhF4fnFdEFSJU3A7EGbbEhbG/chCCFbkKwilWBlb6OQRiJBGVJYOOMe5gqn8Hz9zXscXjWknmbzvglcvjwn86ZpD4OnNZlGR3fvr9G+pufR/iRaK2J8kPFBxgcZH2R8kPFBxgcZH2R8kPFBxgcZH2R8kPFB1R8/o+/5ffcTXaDb3A/efqweorNn28don/Llg4wPMj7I+CDjg4wPMj7I+CDjg4wPMj7I+CDjg4wPMj7otP/aRz/wfXsV7UfLi8Fb+nt8ypcPMj7I+CDjg4wPMj7I+CDjg4wPMj7I+CDjg4wPMj7I+KDq2kP0//ybs0V0gbv2bfC2qqKzab58kPFBxgcZH2R8kPFBxgcZH2R8kPFBxgcZH2R8kPFBdd2Mo0/K6+Pur+7y7/jyQcYHGR9kfJDxQcYHGR9kfJDxQcYHGR9kfJDxQcYHGR9kfJDxQcYHGR9kfJDxQcYHGR9kfJDxQcYHGR9kfJDxQcYHGR/0C1ZPKAXeNx3ZAAAAAElFTkSuQmCC\" id=\"imagec46584216d\" transform=\"scale(1 -1)translate(0 -68.4)\" x=\"115.757147\" y=\"-127.68856\" width=\"68.4\" height=\"68.4\"/>\n", "   </g>\n", "   <g id=\"matplotlib.axis_11\">\n", "    <g id=\"xtick_11\">\n", "     <g id=\"line2d_26\">\n", "      <g>\n", "       <use xlink:href=\"#m2d610d42e1\" x=\"121.418016\" y=\"196.08856\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_17\">\n", "      <!-- 0 -->\n", "      <g transform=\"translate(118.236766 210.686997)scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_12\">\n", "     <g id=\"line2d_27\">\n", "      <g>\n", "       <use xlink:href=\"#m2d610d42e1\" x=\"178.026712\" y=\"196.08856\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_18\">\n", "      <!-- 5 -->\n", "      <g transform=\"translate(174.845462 210.686997)scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-35\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"text_19\">\n", "     <!-- Key positions -->\n", "     <g transform=\"translate(116.659083 224.365122)scale(0.1 -0.1)\">\n", "      <use xlink:href=\"#DejaVuSans-4b\"/>\n", "      <use xlink:href=\"#DejaVuSans-65\" x=\"60.576172\"/>\n", "      <use xlink:href=\"#DejaVuSans-79\" x=\"122.099609\"/>\n", "      <use xlink:href=\"#DejaVuSans-20\" x=\"181.279297\"/>\n", "      <use xlink:href=\"#DejaVuSans-70\" x=\"213.066406\"/>\n", "      <use xlink:href=\"#DejaVuSans-6f\" x=\"276.542969\"/>\n", "      <use xlink:href=\"#DejaVuSans-73\" x=\"337.724609\"/>\n", "      <use xlink:href=\"#DejaVuSans-69\" x=\"389.824219\"/>\n", "      <use xlink:href=\"#DejaVuSans-74\" x=\"417.607422\"/>\n", "      <use xlink:href=\"#DejaVuSans-69\" x=\"456.816406\"/>\n", "      <use xlink:href=\"#DejaVuSans-6f\" x=\"484.599609\"/>\n", "      <use xlink:href=\"#DejaVuSans-6e\" x=\"545.78125\"/>\n", "      <use xlink:href=\"#DejaVuSans-73\" x=\"609.160156\"/>\n", "     </g>\n", "    </g>\n", "   </g>\n", "   <g id=\"matplotlib.axis_12\">\n", "    <g id=\"ytick_16\">\n", "     <g id=\"line2d_28\">\n", "      <g>\n", "       <use xlink:href=\"#m1942115f57\" x=\"115.757147\" y=\"133.818995\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_17\">\n", "     <g id=\"line2d_29\">\n", "      <g>\n", "       <use xlink:href=\"#m1942115f57\" x=\"115.757147\" y=\"156.462473\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_18\">\n", "     <g id=\"line2d_30\">\n", "      <g>\n", "       <use xlink:href=\"#m1942115f57\" x=\"115.757147\" y=\"179.105951\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "   </g>\n", "   <g id=\"patch_28\">\n", "    <path d=\"M 115.757147 196.08856 \n", "L 115.757147 128.158125 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_29\">\n", "    <path d=\"M 183.687582 196.08856 \n", "L 183.687582 128.158125 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_30\">\n", "    <path d=\"M 115.757147 196.08856 \n", "L 183.687582 196.08856 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_31\">\n", "    <path d=\"M 115.757147 128.158125 \n", "L 183.687582 128.158125 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"text_20\">\n", "    <!-- Head 2 -->\n", "    <g transform=\"translate(128.307989 122.158125)scale(0.12 -0.12)\">\n", "     <use xlink:href=\"#DejaVuSans-48\"/>\n", "     <use xlink:href=\"#DejaVuSans-65\" x=\"75.195312\"/>\n", "     <use xlink:href=\"#DejaVuSans-61\" x=\"136.71875\"/>\n", "     <use xlink:href=\"#DejaVuSans-64\" x=\"197.998047\"/>\n", "     <use xlink:href=\"#DejaVuSans-20\" x=\"261.474609\"/>\n", "     <use xlink:href=\"#DejaVuSans-32\" x=\"293.261719\"/>\n", "    </g>\n", "   </g>\n", "  </g>\n", "  <g id=\"axes_7\">\n", "   <g id=\"patch_32\">\n", "    <path d=\"M 197.273668 196.08856 \n", "L 265.204103 196.08856 \n", "L 265.204103 128.158125 \n", "L 197.273668 128.158125 \n", "z\n", "\" style=\"fill: #ffffff\"/>\n", "   </g>\n", "   <g clip-path=\"url(#p95032dac8d)\">\n", "    <image xlink:href=\"data:image/png;base64,\n", "iVBORw0KGgoAAAANSUhEUgAAAF8AAABfCAYAAACOTBv1AAABVUlEQVR4nO3csU3EUBBAQRtOJBAQIZAI6YoCoCQKoBNqoANyCO4kIkwPt0hP6Gbyta2njWzrr9vha1s4yvPl/Wj+7I+egyOIHxI/JH5I/JD4IfFD4ofED4kfEj8kfkj8kPgh8UPrz/5z9D5/XdfRA7zePRw9+/j+Nrr3cn0zGl/PzkfzNj8kfkj8kPgh8UPih8QPiR8SPyR+SPyQ+CHxQ+KHxA/tpu/jp/+ovxw+RvP/mc0PiR8SPyR+SPyQ+CHxQ+KHxA+JHxI/JH5I/JD4IfFDu22bHbezLrPvAafM5ofED4kfEj8kfkj8kPgh8UPih8QPiR8SPyR+SPzQbvnejy5wezE79uSU2fyQ+CHxQ+KHxA+JHxI/JH5I/JD4IfFD4ofED4kfEj+0Pi1Xo3/ET/nIlimbHxI/JH5I/JD4IfFD4ofED4kfEj8kfkj8kPgh8UPih34BDa4YCXD0+UwAAAAASUVORK5CYII=\" id=\"image27c1a27a53\" transform=\"scale(1 -1)translate(0 -68.4)\" x=\"197.273668\" y=\"-127.68856\" width=\"68.4\" height=\"68.4\"/>\n", "   </g>\n", "   <g id=\"matplotlib.axis_13\">\n", "    <g id=\"xtick_13\">\n", "     <g id=\"line2d_31\">\n", "      <g>\n", "       <use xlink:href=\"#m2d610d42e1\" x=\"202.934538\" y=\"196.08856\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_21\">\n", "      <!-- 0 -->\n", "      <g transform=\"translate(199.753288 210.686997)scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_14\">\n", "     <g id=\"line2d_32\">\n", "      <g>\n", "       <use xlink:href=\"#m2d610d42e1\" x=\"259.543234\" y=\"196.08856\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_22\">\n", "      <!-- 5 -->\n", "      <g transform=\"translate(256.361984 210.686997)scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-35\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"text_23\">\n", "     <!-- Key positions -->\n", "     <g transform=\"translate(198.175605 224.365122)scale(0.1 -0.1)\">\n", "      <use xlink:href=\"#DejaVuSans-4b\"/>\n", "      <use xlink:href=\"#DejaVuSans-65\" x=\"60.576172\"/>\n", "      <use xlink:href=\"#DejaVuSans-79\" x=\"122.099609\"/>\n", "      <use xlink:href=\"#DejaVuSans-20\" x=\"181.279297\"/>\n", "      <use xlink:href=\"#DejaVuSans-70\" x=\"213.066406\"/>\n", "      <use xlink:href=\"#DejaVuSans-6f\" x=\"276.542969\"/>\n", "      <use xlink:href=\"#DejaVuSans-73\" x=\"337.724609\"/>\n", "      <use xlink:href=\"#DejaVuSans-69\" x=\"389.824219\"/>\n", "      <use xlink:href=\"#DejaVuSans-74\" x=\"417.607422\"/>\n", "      <use xlink:href=\"#DejaVuSans-69\" x=\"456.816406\"/>\n", "      <use xlink:href=\"#DejaVuSans-6f\" x=\"484.599609\"/>\n", "      <use xlink:href=\"#DejaVuSans-6e\" x=\"545.78125\"/>\n", "      <use xlink:href=\"#DejaVuSans-73\" x=\"609.160156\"/>\n", "     </g>\n", "    </g>\n", "   </g>\n", "   <g id=\"matplotlib.axis_14\">\n", "    <g id=\"ytick_19\">\n", "     <g id=\"line2d_33\">\n", "      <g>\n", "       <use xlink:href=\"#m1942115f57\" x=\"197.273668\" y=\"133.818995\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_20\">\n", "     <g id=\"line2d_34\">\n", "      <g>\n", "       <use xlink:href=\"#m1942115f57\" x=\"197.273668\" y=\"156.462473\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_21\">\n", "     <g id=\"line2d_35\">\n", "      <g>\n", "       <use xlink:href=\"#m1942115f57\" x=\"197.273668\" y=\"179.105951\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "   </g>\n", "   <g id=\"patch_33\">\n", "    <path d=\"M 197.273668 196.08856 \n", "L 197.273668 128.158125 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_34\">\n", "    <path d=\"M 265.204103 196.08856 \n", "L 265.204103 128.158125 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_35\">\n", "    <path d=\"M 197.273668 196.08856 \n", "L 265.204103 196.08856 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_36\">\n", "    <path d=\"M 197.273668 128.158125 \n", "L 265.204103 128.158125 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"text_24\">\n", "    <!-- Head 3 -->\n", "    <g transform=\"translate(209.824511 122.158125)scale(0.12 -0.12)\">\n", "     <use xlink:href=\"#DejaVuSans-48\"/>\n", "     <use xlink:href=\"#DejaVuSans-65\" x=\"75.195312\"/>\n", "     <use xlink:href=\"#DejaVuSans-61\" x=\"136.71875\"/>\n", "     <use xlink:href=\"#DejaVuSans-64\" x=\"197.998047\"/>\n", "     <use xlink:href=\"#DejaVuSans-20\" x=\"261.474609\"/>\n", "     <use xlink:href=\"#DejaVuSans-33\" x=\"293.261719\"/>\n", "    </g>\n", "   </g>\n", "  </g>\n", "  <g id=\"axes_8\">\n", "   <g id=\"patch_37\">\n", "    <path d=\"M 278.79019 196.08856 \n", "L 346.720625 196.08856 \n", "L 346.720625 128.158125 \n", "L 278.79019 128.158125 \n", "z\n", "\" style=\"fill: #ffffff\"/>\n", "   </g>\n", "   <g clip-path=\"url(#pb6e07274af)\">\n", "    <image xlink:href=\"data:image/png;base64,\n", "iVBORw0KGgoAAAANSUhEUgAAAF8AAABfCAYAAACOTBv1AAABi0lEQVR4nO3cPUpDQRhG4ZlrmkDEWrCztXUBtmJnYWshiIJbyAbE2mzAJo29jSuxEwSTJgER/Jm4h/sGDpLz9N+d4fBVU9z6fXezKoGti3EyXtrjpPdsd3IenV3abzS+Wsyi+S6aVsT4IOODjA8yPsj4IOODjA8yPsj4IOODjA8yPsj4IOODalvOo/f88vMV3qD2Hr3fO4iOvn5/ieZTbj7I+CDjg4wPMj7I+CDjg4wPMj7I+CDjg4wPMj7I+CDjg+ry+DB6zx9Nn6ILtIfb3rPd6VV0dh1uR/MpNx9kfJDxQcYHGR9kfJDxQcYHGR9kfJDxQcYHGR9kfJDxQfWyjKL3/MnH67rusnHcfJDxQcYHGR9kfJDxQcYHGR9kfJDxQcYHGR9kfJDxQYOjnWH0gfaW/Tal292P5v8zNx9kfJDxQcYHGR9kfJDxQcYHGR9kfJDxQcYHGR9kfJDxQYPnxWf0gbMNfo9Pufkg44OMDzI+yPgg44OMDzI+yPgg44OMDzI+yPgg44OMD/oDMBEmnRYHJ4MAAAAASUVORK5CYII=\" id=\"imagede5e7ca7fa\" transform=\"scale(1 -1)translate(0 -68.4)\" x=\"278.79019\" y=\"-127.68856\" width=\"68.4\" height=\"68.4\"/>\n", "   </g>\n", "   <g id=\"matplotlib.axis_15\">\n", "    <g id=\"xtick_15\">\n", "     <g id=\"line2d_36\">\n", "      <g>\n", "       <use xlink:href=\"#m2d610d42e1\" x=\"284.45106\" y=\"196.08856\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_25\">\n", "      <!-- 0 -->\n", "      <g transform=\"translate(281.26981 210.686997)scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_16\">\n", "     <g id=\"line2d_37\">\n", "      <g>\n", "       <use xlink:href=\"#m2d610d42e1\" x=\"341.059755\" y=\"196.08856\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_26\">\n", "      <!-- 5 -->\n", "      <g transform=\"translate(337.878505 210.686997)scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-35\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"text_27\">\n", "     <!-- Key positions -->\n", "     <g transform=\"translate(279.692126 224.365122)scale(0.1 -0.1)\">\n", "      <use xlink:href=\"#DejaVuSans-4b\"/>\n", "      <use xlink:href=\"#DejaVuSans-65\" x=\"60.576172\"/>\n", "      <use xlink:href=\"#DejaVuSans-79\" x=\"122.099609\"/>\n", "      <use xlink:href=\"#DejaVuSans-20\" x=\"181.279297\"/>\n", "      <use xlink:href=\"#DejaVuSans-70\" x=\"213.066406\"/>\n", "      <use xlink:href=\"#DejaVuSans-6f\" x=\"276.542969\"/>\n", "      <use xlink:href=\"#DejaVuSans-73\" x=\"337.724609\"/>\n", "      <use xlink:href=\"#DejaVuSans-69\" x=\"389.824219\"/>\n", "      <use xlink:href=\"#DejaVuSans-74\" x=\"417.607422\"/>\n", "      <use xlink:href=\"#DejaVuSans-69\" x=\"456.816406\"/>\n", "      <use xlink:href=\"#DejaVuSans-6f\" x=\"484.599609\"/>\n", "      <use xlink:href=\"#DejaVuSans-6e\" x=\"545.78125\"/>\n", "      <use xlink:href=\"#DejaVuSans-73\" x=\"609.160156\"/>\n", "     </g>\n", "    </g>\n", "   </g>\n", "   <g id=\"matplotlib.axis_16\">\n", "    <g id=\"ytick_22\">\n", "     <g id=\"line2d_38\">\n", "      <g>\n", "       <use xlink:href=\"#m1942115f57\" x=\"278.79019\" y=\"133.818995\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_23\">\n", "     <g id=\"line2d_39\">\n", "      <g>\n", "       <use xlink:href=\"#m1942115f57\" x=\"278.79019\" y=\"156.462473\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_24\">\n", "     <g id=\"line2d_40\">\n", "      <g>\n", "       <use xlink:href=\"#m1942115f57\" x=\"278.79019\" y=\"179.105951\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "   </g>\n", "   <g id=\"patch_38\">\n", "    <path d=\"M 278.79019 196.08856 \n", "L 278.79019 128.158125 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_39\">\n", "    <path d=\"M 346.720625 196.08856 \n", "L 346.720625 128.158125 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_40\">\n", "    <path d=\"M 278.79019 196.08856 \n", "L 346.720625 196.08856 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_41\">\n", "    <path d=\"M 278.79019 128.158125 \n", "L 346.720625 128.158125 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"text_28\">\n", "    <!-- Head 4 -->\n", "    <g transform=\"translate(291.341033 122.158125)scale(0.12 -0.12)\">\n", "     <use xlink:href=\"#DejaVuSans-48\"/>\n", "     <use xlink:href=\"#DejaVuSans-65\" x=\"75.195312\"/>\n", "     <use xlink:href=\"#DejaVuSans-61\" x=\"136.71875\"/>\n", "     <use xlink:href=\"#DejaVuSans-64\" x=\"197.998047\"/>\n", "     <use xlink:href=\"#DejaVuSans-20\" x=\"261.474609\"/>\n", "     <use xlink:href=\"#DejaVuSans-34\" x=\"293.261719\"/>\n", "    </g>\n", "   </g>\n", "  </g>\n", "  <g id=\"axes_9\">\n", "   <g id=\"patch_42\">\n", "    <path d=\"M 366.250625 167.415342 \n", "L 372.071825 167.415342 \n", "L 372.071825 50.991342 \n", "L 366.250625 50.991342 \n", "z\n", "\" style=\"fill: #ffffff\"/>\n", "   </g>\n", "   <g id=\"patch_43\">\n", "    <path clip-path=\"url(#p3dd3149d8b)\" style=\"fill: #ffffff; stroke: #ffffff; stroke-width: 0.01; stroke-linejoin: miter\"/>\n", "   </g>\n", "   <image xlink:href=\"data:image/png;base64,\n", "iVBORw0KGgoAAAANSUhEUgAAAAgAAACiCAYAAAB8iIwDAAAA7klEQVR4nN2XSw4DIQxDUyn3P2s33ZWkN8iLZGCgsx1kP8cwn1d+3mnF5ZZR3Te3LAU6CkELjCyCFrACxoQFqc9BV5gQExkOiBmDFsgKBJkbGBiSLIYakxnYgg6OHpMnKcc8YMvxqOUutkzy+S13xbmYADkh5g2QqCC/9fKEmFd0QZA58BGEXehtygx3xFw/h7/oAp7E5oEMFBMVBluU980D60YFYgg5Jk9S7mIHpNwFODRSgEBDARjNk34noatOTLRYr4CTHHoXBNnogixkSIxZfxx0Rv2VIVGhwSBb1AJzFGQLUsC6dUjck+u7+AE+P2IIsbjxbAAAAABJRU5ErkJggg==\" id=\"image6ec4319596\" transform=\"scale(1 -1)translate(0 -116.64)\" x=\"366.48\" y=\"-50.4\" width=\"5.76\" height=\"116.64\"/>\n", "   <g id=\"matplotlib.axis_17\">\n", "    <g id=\"ytick_25\">\n", "     <g id=\"line2d_41\">\n", "      <defs>\n", "       <path id=\"m58b7310b9f\" d=\"M 0 0 \n", "L 3.5 0 \n", "\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </defs>\n", "      <g>\n", "       <use xlink:href=\"#m58b7310b9f\" x=\"372.071825\" y=\"167.415342\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_29\">\n", "      <!-- 0.0 -->\n", "      <g transform=\"translate(379.071825 171.214561)scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-2e\" d=\"M 684 794 \n", "L 1344 794 \n", "L 1344 0 \n", "L 684 0 \n", "L 684 794 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"95.410156\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_26\">\n", "     <g id=\"line2d_42\">\n", "      <g>\n", "       <use xlink:href=\"#m58b7310b9f\" x=\"372.071825\" y=\"144.130542\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_30\">\n", "      <!-- 0.2 -->\n", "      <g transform=\"translate(379.071825 147.929761)scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-32\" x=\"95.410156\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_27\">\n", "     <g id=\"line2d_43\">\n", "      <g>\n", "       <use xlink:href=\"#m58b7310b9f\" x=\"372.071825\" y=\"120.845742\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_31\">\n", "      <!-- 0.4 -->\n", "      <g transform=\"translate(379.071825 124.644961)scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-34\" x=\"95.410156\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_28\">\n", "     <g id=\"line2d_44\">\n", "      <g>\n", "       <use xlink:href=\"#m58b7310b9f\" x=\"372.071825\" y=\"97.560942\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_32\">\n", "      <!-- 0.6 -->\n", "      <g transform=\"translate(379.071825 101.360161)scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-36\" d=\"M 2113 2584 \n", "Q 1688 2584 1439 2293 \n", "Q 1191 2003 1191 1497 \n", "Q 1191 994 1439 701 \n", "Q 1688 409 2113 409 \n", "Q 2538 409 2786 701 \n", "Q 3034 994 3034 1497 \n", "Q 3034 2003 2786 2293 \n", "Q 2538 2584 2113 2584 \n", "z\n", "M 3366 4563 \n", "L 3366 3988 \n", "Q 3128 4100 2886 4159 \n", "Q 2644 4219 2406 4219 \n", "Q 1781 4219 1451 3797 \n", "Q 1122 3375 1075 2522 \n", "Q 1259 2794 1537 2939 \n", "Q 1816 3084 2150 3084 \n", "Q 2853 3084 3261 2657 \n", "Q 3669 2231 3669 1497 \n", "Q 3669 778 3244 343 \n", "Q 2819 -91 2113 -91 \n", "Q 1303 -91 875 529 \n", "Q 447 1150 447 2328 \n", "Q 447 3434 972 4092 \n", "Q 1497 4750 2381 4750 \n", "Q 2619 4750 2861 4703 \n", "Q 3103 4656 3366 4563 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-36\" x=\"95.410156\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_29\">\n", "     <g id=\"line2d_45\">\n", "      <g>\n", "       <use xlink:href=\"#m58b7310b9f\" x=\"372.071825\" y=\"74.276142\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_33\">\n", "      <!-- 0.8 -->\n", "      <g transform=\"translate(379.071825 78.075361)scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-38\" d=\"M 2034 2216 \n", "Q 1584 2216 1326 1975 \n", "Q 1069 1734 1069 1313 \n", "Q 1069 891 1326 650 \n", "Q 1584 409 2034 409 \n", "Q 2484 409 2743 651 \n", "Q 3003 894 3003 1313 \n", "Q 3003 1734 2745 1975 \n", "Q 2488 2216 2034 2216 \n", "z\n", "M 1403 2484 \n", "Q 997 2584 770 2862 \n", "Q 544 3141 544 3541 \n", "Q 544 4100 942 4425 \n", "Q 1341 4750 2034 4750 \n", "Q 2731 4750 3128 4425 \n", "Q 3525 4100 3525 3541 \n", "Q 3525 3141 3298 2862 \n", "Q 3072 2584 2669 2484 \n", "Q 3125 2378 3379 2068 \n", "Q 3634 1759 3634 1313 \n", "Q 3634 634 3220 271 \n", "Q 2806 -91 2034 -91 \n", "Q 1263 -91 848 271 \n", "Q 434 634 434 1313 \n", "Q 434 1759 690 2068 \n", "Q 947 2378 1403 2484 \n", "z\n", "M 1172 3481 \n", "Q 1172 3119 1398 2916 \n", "Q 1625 2713 2034 2713 \n", "Q 2441 2713 2670 2916 \n", "Q 2900 3119 2900 3481 \n", "Q 2900 3844 2670 4047 \n", "Q 2441 4250 2034 4250 \n", "Q 1625 4250 1398 4047 \n", "Q 1172 3844 1172 3481 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-38\" x=\"95.410156\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_30\">\n", "     <g id=\"line2d_46\">\n", "      <g>\n", "       <use xlink:href=\"#m58b7310b9f\" x=\"372.071825\" y=\"50.991342\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_34\">\n", "      <!-- 1.0 -->\n", "      <g transform=\"translate(379.071825 54.790561)scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-31\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"95.410156\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "   </g>\n", "   <g id=\"LineCollection_1\"/>\n", "   <g id=\"patch_44\">\n", "    <path d=\"M 366.250625 167.415342 \n", "L 369.161225 167.415342 \n", "L 372.071825 167.415342 \n", "L 372.071825 50.991342 \n", "L 369.161225 50.991342 \n", "L 366.250625 50.991342 \n", "L 366.250625 167.415342 \n", "z\n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "  </g>\n", " </g>\n", " <defs>\n", "  <clipPath id=\"pcf64e38511\">\n", "   <rect x=\"34.240625\" y=\"22.318125\" width=\"67.930435\" height=\"67.930435\"/>\n", "  </clipPath>\n", "  <clipPath id=\"p9de629ba07\">\n", "   <rect x=\"115.757147\" y=\"22.318125\" width=\"67.930435\" height=\"67.930435\"/>\n", "  </clipPath>\n", "  <clipPath id=\"pe354619074\">\n", "   <rect x=\"197.273668\" y=\"22.318125\" width=\"67.930435\" height=\"67.930435\"/>\n", "  </clipPath>\n", "  <clipPath id=\"p5cd93cd5d0\">\n", "   <rect x=\"278.79019\" y=\"22.318125\" width=\"67.930435\" height=\"67.930435\"/>\n", "  </clipPath>\n", "  <clipPath id=\"p7ebd79cc09\">\n", "   <rect x=\"34.240625\" y=\"128.158125\" width=\"67.930435\" height=\"67.930435\"/>\n", "  </clipPath>\n", "  <clipPath id=\"pcc0618ac45\">\n", "   <rect x=\"115.757147\" y=\"128.158125\" width=\"67.930435\" height=\"67.930435\"/>\n", "  </clipPath>\n", "  <clipPath id=\"p95032dac8d\">\n", "   <rect x=\"197.273668\" y=\"128.158125\" width=\"67.930435\" height=\"67.930435\"/>\n", "  </clipPath>\n", "  <clipPath id=\"pb6e07274af\">\n", "   <rect x=\"278.79019\" y=\"128.158125\" width=\"67.930435\" height=\"67.930435\"/>\n", "  </clipPath>\n", "  <clipPath id=\"p3dd3149d8b\">\n", "   <rect x=\"366.250625\" y=\"50.991342\" width=\"5.8212\" height=\"116.424\"/>\n", "  </clipPath>\n", " </defs>\n", "</svg>\n"], "text/plain": ["<Figure size 700x350 with 9 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["# Plusonetoincludethebeginning-of-sequencetoken\n", "d2l.show_heatmaps(\n", "    dec_self_attention_weights[:, :, :, :len(translation.split()) + 1],\n", "    xlabel='Key positions', ylabel='Query positions',\n", "    titles=['Head %d' % i for i in range(1, 5)], figsize=(7, 3.5))"]}, {"cell_type": "markdown", "id": "48ff7f16", "metadata": {"origin_pos": 82}, "source": ["与编码器的自注意力的情况类似，通过指定输入序列的有效长度，[**输出序列的查询不会与输入序列中填充位置的词元进行注意力计算**]。\n"]}, {"cell_type": "code", "execution_count": 20, "id": "d3a113e7", "metadata": {"execution": {"iopub.execute_input": "2022-12-07T16:35:36.144118Z", "iopub.status.busy": "2022-12-07T16:35:36.143549Z", "iopub.status.idle": "2022-12-07T16:35:36.789216Z", "shell.execute_reply": "2022-12-07T16:35:36.788437Z"}, "origin_pos": 83, "tab": ["pytorch"]}, "outputs": [{"data": {"image/svg+xml": ["<?xml version=\"1.0\" encoding=\"utf-8\" standalone=\"no\"?>\n", "<!DOCTYPE svg PUBLIC \"-//W3C//DTD SVG 1.1//EN\"\n", "  \"http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd\">\n", "<svg xmlns:xlink=\"http://www.w3.org/1999/xlink\" width=\"402.17495pt\" height=\"210.169912pt\" viewBox=\"0 0 402.17495 210.169912\" xmlns=\"http://www.w3.org/2000/svg\" version=\"1.1\">\n", " <metadata>\n", "  <rdf:RDF xmlns:dc=\"http://purl.org/dc/elements/1.1/\" xmlns:cc=\"http://creativecommons.org/ns#\" xmlns:rdf=\"http://www.w3.org/1999/02/22-rdf-syntax-ns#\">\n", "   <cc:Work>\n", "    <dc:type rdf:resource=\"http://purl.org/dc/dcmitype/StillImage\"/>\n", "    <dc:date>2022-12-07T16:35:36.647847</dc:date>\n", "    <dc:format>image/svg+xml</dc:format>\n", "    <dc:creator>\n", "     <cc:Agent>\n", "      <dc:title>Matplotlib v3.5.1, https://matplotlib.org/</dc:title>\n", "     </cc:Agent>\n", "    </dc:creator>\n", "   </cc:Work>\n", "  </rdf:RDF>\n", " </metadata>\n", " <defs>\n", "  <style type=\"text/css\">*{stroke-linejoin: round; stroke-linecap: butt}</style>\n", " </defs>\n", " <g id=\"figure_1\">\n", "  <g id=\"patch_1\">\n", "   <path d=\"M 0 210.169912 \n", "L 402.17495 210.169912 \n", "L 402.17495 0 \n", "L 0 0 \n", "z\n", "\" style=\"fill: #ffffff\"/>\n", "  </g>\n", "  <g id=\"axes_1\">\n", "   <g id=\"patch_2\">\n", "    <path d=\"M 34.240625 66.773662 \n", "L 102.17106 66.773662 \n", "L 102.17106 26.015401 \n", "L 34.240625 26.015401 \n", "z\n", "\" style=\"fill: #ffffff\"/>\n", "   </g>\n", "   <g clip-path=\"url(#pf1c967b119)\">\n", "    <image xlink:href=\"data:image/png;base64,\n", "iVBORw0KGgoAAAANSUhEUgAAAF8AAAA5CAYAAABQ4feyAAABJElEQVR4nO3ZMUoDQRSA4TdRJBADBkIqD+EZ0likSJMD2OmFhHRpvEbaFFbpbCwt7LTIVtFdj/AGZPkJ+b/68Xb5mWaYshlPu0g8fL5nIxER0f0c86HDV9WucjOrmjtlA/oHzpnxQcYHGR9kfJDxQcYHGR9kfFD53W/TG25cDauWtS/P+QcXq6pdF3fzqrlT5skHGR9kfJDxQcYHGR9kfJDxQZdlkj/XtW+vVcvK/TKd6Xbbql3hJUt9Mj7I+CDjg4wPMj7I+CDjg4wPKl3znT4jPo1uq5atm49//9A58eSDjA8yPsj4IOODjA8yPsj4oPIY1+kly8tTPzz5IOODjA8yPsj4IOODjA8yPsj4IOODjA8yPsj4IOODjA8yPsj4IOOD/gDazCFPF+U4NwAAAABJRU5ErkJggg==\" id=\"image555a862771\" transform=\"scale(1 -1)translate(0 -41.04)\" x=\"34.240625\" y=\"-25.733662\" width=\"68.4\" height=\"41.04\"/>\n", "   </g>\n", "   <g id=\"matplotlib.axis_1\">\n", "    <g id=\"xtick_1\">\n", "     <g id=\"line2d_1\">\n", "      <defs>\n", "       <path id=\"m72174680ec\" d=\"M 0 0 \n", "L 0 3.5 \n", "\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </defs>\n", "      <g>\n", "       <use xlink:href=\"#m72174680ec\" x=\"37.637147\" y=\"66.773662\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_2\">\n", "     <g id=\"line2d_2\">\n", "      <g>\n", "       <use xlink:href=\"#m72174680ec\" x=\"71.602364\" y=\"66.773662\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "   </g>\n", "   <g id=\"matplotlib.axis_2\">\n", "    <g id=\"ytick_1\">\n", "     <g id=\"line2d_3\">\n", "      <defs>\n", "       <path id=\"mee265d5878\" d=\"M 0 0 \n", "L -3.5 0 \n", "\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </defs>\n", "      <g>\n", "       <use xlink:href=\"#mee265d5878\" x=\"34.240625\" y=\"29.411923\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_1\">\n", "      <!-- 0 -->\n", "      <g transform=\"translate(20.878125 33.211141)scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-30\" d=\"M 2034 4250 \n", "Q 1547 4250 1301 3770 \n", "Q 1056 3291 1056 2328 \n", "Q 1056 1369 1301 889 \n", "Q 1547 409 2034 409 \n", "Q 2525 409 2770 889 \n", "Q 3016 1369 3016 2328 \n", "Q 3016 3291 2770 3770 \n", "Q 2525 4250 2034 4250 \n", "z\n", "M 2034 4750 \n", "Q 2819 4750 3233 4129 \n", "Q 3647 3509 3647 2328 \n", "Q 3647 1150 3233 529 \n", "Q 2819 -91 2034 -91 \n", "Q 1250 -91 836 529 \n", "Q 422 1150 422 2328 \n", "Q 422 3509 836 4129 \n", "Q 1250 4750 2034 4750 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_2\">\n", "     <g id=\"line2d_4\">\n", "      <g>\n", "       <use xlink:href=\"#mee265d5878\" x=\"34.240625\" y=\"63.37714\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_2\">\n", "      <!-- 5 -->\n", "      <g transform=\"translate(20.878125 67.176359)scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-35\" d=\"M 691 4666 \n", "L 3169 4666 \n", "L 3169 4134 \n", "L 1269 4134 \n", "L 1269 2991 \n", "Q 1406 3038 1543 3061 \n", "Q 1681 3084 1819 3084 \n", "Q 2600 3084 3056 2656 \n", "Q 3513 2228 3513 1497 \n", "Q 3513 744 3044 326 \n", "Q 2575 -91 1722 -91 \n", "Q 1428 -91 1123 -41 \n", "Q 819 9 494 109 \n", "L 494 744 \n", "Q 775 591 1075 516 \n", "Q 1375 441 1709 441 \n", "Q 2250 441 2565 725 \n", "Q 2881 1009 2881 1497 \n", "Q 2881 1984 2565 2268 \n", "Q 2250 2553 1709 2553 \n", "Q 1456 2553 1204 2497 \n", "Q 953 2441 691 2322 \n", "L 691 4666 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-35\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"text_3\">\n", "     <!-- Query positions -->\n", "     <g transform=\"translate(14.798437 85.589063)rotate(-90)scale(0.1 -0.1)\">\n", "      <defs>\n", "       <path id=\"DejaVuSans-51\" d=\"M 2522 4238 \n", "Q 1834 4238 1429 3725 \n", "Q 1025 3213 1025 2328 \n", "Q 1025 1447 1429 934 \n", "Q 1834 422 2522 422 \n", "Q 3209 422 3611 934 \n", "Q 4013 1447 4013 2328 \n", "Q 4013 3213 3611 3725 \n", "Q 3209 4238 2522 4238 \n", "z\n", "M 3406 84 \n", "L 4238 -825 \n", "L 3475 -825 \n", "L 2784 -78 \n", "Q 2681 -84 2626 -87 \n", "Q 2572 -91 2522 -91 \n", "Q 1538 -91 948 567 \n", "Q 359 1225 359 2328 \n", "Q 359 3434 948 4092 \n", "Q 1538 4750 2522 4750 \n", "Q 3503 4750 4090 4092 \n", "Q 4678 3434 4678 2328 \n", "Q 4678 1516 4351 937 \n", "Q 4025 359 3406 84 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-75\" d=\"M 544 1381 \n", "L 544 3500 \n", "L 1119 3500 \n", "L 1119 1403 \n", "Q 1119 906 1312 657 \n", "Q 1506 409 1894 409 \n", "Q 2359 409 2629 706 \n", "Q 2900 1003 2900 1516 \n", "L 2900 3500 \n", "L 3475 3500 \n", "L 3475 0 \n", "L 2900 0 \n", "L 2900 538 \n", "Q 2691 219 2414 64 \n", "Q 2138 -91 1772 -91 \n", "Q 1169 -91 856 284 \n", "Q 544 659 544 1381 \n", "z\n", "M 1991 3584 \n", "L 1991 3584 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-65\" d=\"M 3597 1894 \n", "L 3597 1613 \n", "L 953 1613 \n", "Q 991 1019 1311 708 \n", "Q 1631 397 2203 397 \n", "Q 2534 397 2845 478 \n", "Q 3156 559 3463 722 \n", "L 3463 178 \n", "Q 3153 47 2828 -22 \n", "Q 2503 -91 2169 -91 \n", "Q 1331 -91 842 396 \n", "Q 353 884 353 1716 \n", "Q 353 2575 817 3079 \n", "Q 1281 3584 2069 3584 \n", "Q 2775 3584 3186 3129 \n", "Q 3597 2675 3597 1894 \n", "z\n", "M 3022 2063 \n", "Q 3016 2534 2758 2815 \n", "Q 2500 3097 2075 3097 \n", "Q 1594 3097 1305 2825 \n", "Q 1016 2553 972 2059 \n", "L 3022 2063 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-72\" d=\"M 2631 2963 \n", "Q 2534 3019 2420 3045 \n", "Q 2306 3072 2169 3072 \n", "Q 1681 3072 1420 2755 \n", "Q 1159 2438 1159 1844 \n", "L 1159 0 \n", "L 581 0 \n", "L 581 3500 \n", "L 1159 3500 \n", "L 1159 2956 \n", "Q 1341 3275 1631 3429 \n", "Q 1922 3584 2338 3584 \n", "Q 2397 3584 2469 3576 \n", "Q 2541 3569 2628 3553 \n", "L 2631 2963 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-79\" d=\"M 2059 -325 \n", "Q 1816 -950 1584 -1140 \n", "Q 1353 -1331 966 -1331 \n", "L 506 -1331 \n", "L 506 -850 \n", "L 844 -850 \n", "Q 1081 -850 1212 -737 \n", "Q 1344 -625 1503 -206 \n", "L 1606 56 \n", "L 191 3500 \n", "L 800 3500 \n", "L 1894 763 \n", "L 2988 3500 \n", "L 3597 3500 \n", "L 2059 -325 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-20\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-70\" d=\"M 1159 525 \n", "L 1159 -1331 \n", "L 581 -1331 \n", "L 581 3500 \n", "L 1159 3500 \n", "L 1159 2969 \n", "Q 1341 3281 1617 3432 \n", "Q 1894 3584 2278 3584 \n", "Q 2916 3584 3314 3078 \n", "Q 3713 2572 3713 1747 \n", "Q 3713 922 3314 415 \n", "Q 2916 -91 2278 -91 \n", "Q 1894 -91 1617 61 \n", "Q 1341 213 1159 525 \n", "z\n", "M 3116 1747 \n", "Q 3116 2381 2855 2742 \n", "Q 2594 3103 2138 3103 \n", "Q 1681 3103 1420 2742 \n", "Q 1159 2381 1159 1747 \n", "Q 1159 1113 1420 752 \n", "Q 1681 391 2138 391 \n", "Q 2594 391 2855 752 \n", "Q 3116 1113 3116 1747 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-6f\" d=\"M 1959 3097 \n", "Q 1497 3097 1228 2736 \n", "Q 959 2375 959 1747 \n", "Q 959 1119 1226 758 \n", "Q 1494 397 1959 397 \n", "Q 2419 397 2687 759 \n", "Q 2956 1122 2956 1747 \n", "Q 2956 2369 2687 2733 \n", "Q 2419 3097 1959 3097 \n", "z\n", "M 1959 3584 \n", "Q 2709 3584 3137 3096 \n", "Q 3566 2609 3566 1747 \n", "Q 3566 888 3137 398 \n", "Q 2709 -91 1959 -91 \n", "Q 1206 -91 779 398 \n", "Q 353 888 353 1747 \n", "Q 353 2609 779 3096 \n", "Q 1206 3584 1959 3584 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-73\" d=\"M 2834 3397 \n", "L 2834 2853 \n", "Q 2591 2978 2328 3040 \n", "Q 2066 3103 1784 3103 \n", "Q 1356 3103 1142 2972 \n", "Q 928 2841 928 2578 \n", "Q 928 2378 1081 2264 \n", "Q 1234 2150 1697 2047 \n", "L 1894 2003 \n", "Q 2506 1872 2764 1633 \n", "Q 3022 1394 3022 966 \n", "Q 3022 478 2636 193 \n", "Q 2250 -91 1575 -91 \n", "Q 1294 -91 989 -36 \n", "Q 684 19 347 128 \n", "L 347 722 \n", "Q 666 556 975 473 \n", "Q 1284 391 1588 391 \n", "Q 1994 391 2212 530 \n", "Q 2431 669 2431 922 \n", "Q 2431 1156 2273 1281 \n", "Q 2116 1406 1581 1522 \n", "L 1381 1569 \n", "Q 847 1681 609 1914 \n", "Q 372 2147 372 2553 \n", "Q 372 3047 722 3315 \n", "Q 1072 3584 1716 3584 \n", "Q 2034 3584 2315 3537 \n", "Q 2597 3491 2834 3397 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-69\" d=\"M 603 3500 \n", "L 1178 3500 \n", "L 1178 0 \n", "L 603 0 \n", "L 603 3500 \n", "z\n", "M 603 4863 \n", "L 1178 4863 \n", "L 1178 4134 \n", "L 603 4134 \n", "L 603 4863 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-74\" d=\"M 1172 4494 \n", "L 1172 3500 \n", "L 2356 3500 \n", "L 2356 3053 \n", "L 1172 3053 \n", "L 1172 1153 \n", "Q 1172 725 1289 603 \n", "Q 1406 481 1766 481 \n", "L 2356 481 \n", "L 2356 0 \n", "L 1766 0 \n", "Q 1100 0 847 248 \n", "Q 594 497 594 1153 \n", "L 594 3053 \n", "L 172 3053 \n", "L 172 3500 \n", "L 594 3500 \n", "L 594 4494 \n", "L 1172 4494 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-6e\" d=\"M 3513 2113 \n", "L 3513 0 \n", "L 2938 0 \n", "L 2938 2094 \n", "Q 2938 2591 2744 2837 \n", "Q 2550 3084 2163 3084 \n", "Q 1697 3084 1428 2787 \n", "Q 1159 2491 1159 1978 \n", "L 1159 0 \n", "L 581 0 \n", "L 581 3500 \n", "L 1159 3500 \n", "L 1159 2956 \n", "Q 1366 3272 1645 3428 \n", "Q 1925 3584 2291 3584 \n", "Q 2894 3584 3203 3211 \n", "Q 3513 2838 3513 2113 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "      </defs>\n", "      <use xlink:href=\"#DejaVuSans-51\"/>\n", "      <use xlink:href=\"#DejaVuSans-75\" x=\"78.710938\"/>\n", "      <use xlink:href=\"#DejaVuSans-65\" x=\"142.089844\"/>\n", "      <use xlink:href=\"#DejaVuSans-72\" x=\"203.613281\"/>\n", "      <use xlink:href=\"#DejaVuSans-79\" x=\"244.726562\"/>\n", "      <use xlink:href=\"#DejaVuSans-20\" x=\"303.90625\"/>\n", "      <use xlink:href=\"#DejaVuSans-70\" x=\"335.693359\"/>\n", "      <use xlink:href=\"#DejaVuSans-6f\" x=\"399.169922\"/>\n", "      <use xlink:href=\"#DejaVuSans-73\" x=\"460.351562\"/>\n", "      <use xlink:href=\"#DejaVuSans-69\" x=\"512.451172\"/>\n", "      <use xlink:href=\"#DejaVuSans-74\" x=\"540.234375\"/>\n", "      <use xlink:href=\"#DejaVuSans-69\" x=\"579.443359\"/>\n", "      <use xlink:href=\"#DejaVuSans-6f\" x=\"607.226562\"/>\n", "      <use xlink:href=\"#DejaVuSans-6e\" x=\"668.408203\"/>\n", "      <use xlink:href=\"#DejaVuSans-73\" x=\"731.787109\"/>\n", "     </g>\n", "    </g>\n", "   </g>\n", "   <g id=\"patch_3\">\n", "    <path d=\"M 34.240625 66.773662 \n", "L 34.240625 26.015401 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_4\">\n", "    <path d=\"M 102.17106 66.773662 \n", "L 102.17106 26.015401 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_5\">\n", "    <path d=\"M 34.240625 66.773662 \n", "L 102.17106 66.773662 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_6\">\n", "    <path d=\"M 34.240625 26.015401 \n", "L 102.17106 26.015401 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"text_4\">\n", "    <!-- Head 1 -->\n", "    <g transform=\"translate(46.791467 20.015401)scale(0.12 -0.12)\">\n", "     <defs>\n", "      <path id=\"DejaVuSans-48\" d=\"M 628 4666 \n", "L 1259 4666 \n", "L 1259 2753 \n", "L 3553 2753 \n", "L 3553 4666 \n", "L 4184 4666 \n", "L 4184 0 \n", "L 3553 0 \n", "L 3553 2222 \n", "L 1259 2222 \n", "L 1259 0 \n", "L 628 0 \n", "L 628 4666 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "      <path id=\"DejaVuSans-61\" d=\"M 2194 1759 \n", "Q 1497 1759 1228 1600 \n", "Q 959 1441 959 1056 \n", "Q 959 750 1161 570 \n", "Q 1363 391 1709 391 \n", "Q 2188 391 2477 730 \n", "Q 2766 1069 2766 1631 \n", "L 2766 1759 \n", "L 2194 1759 \n", "z\n", "M 3341 1997 \n", "L 3341 0 \n", "L 2766 0 \n", "L 2766 531 \n", "Q 2569 213 2275 61 \n", "Q 1981 -91 1556 -91 \n", "Q 1019 -91 701 211 \n", "Q 384 513 384 1019 \n", "Q 384 1609 779 1909 \n", "Q 1175 2209 1959 2209 \n", "L 2766 2209 \n", "L 2766 2266 \n", "Q 2766 2663 2505 2880 \n", "Q 2244 3097 1772 3097 \n", "Q 1472 3097 1187 3025 \n", "Q 903 2953 641 2809 \n", "L 641 3341 \n", "Q 956 3463 1253 3523 \n", "Q 1550 3584 1831 3584 \n", "Q 2591 3584 2966 3190 \n", "Q 3341 2797 3341 1997 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "      <path id=\"DejaVuSans-64\" d=\"M 2906 2969 \n", "L 2906 4863 \n", "L 3481 4863 \n", "L 3481 0 \n", "L 2906 0 \n", "L 2906 525 \n", "Q 2725 213 2448 61 \n", "Q 2172 -91 1784 -91 \n", "Q 1150 -91 751 415 \n", "Q 353 922 353 1747 \n", "Q 353 2572 751 3078 \n", "Q 1150 3584 1784 3584 \n", "Q 2172 3584 2448 3432 \n", "Q 2725 3281 2906 2969 \n", "z\n", "M 947 1747 \n", "Q 947 1113 1208 752 \n", "Q 1469 391 1925 391 \n", "Q 2381 391 2643 752 \n", "Q 2906 1113 2906 1747 \n", "Q 2906 2381 2643 2742 \n", "Q 2381 3103 1925 3103 \n", "Q 1469 3103 1208 2742 \n", "Q 947 2381 947 1747 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "      <path id=\"DejaVuSans-31\" d=\"M 794 531 \n", "L 1825 531 \n", "L 1825 4091 \n", "L 703 3866 \n", "L 703 4441 \n", "L 1819 4666 \n", "L 2450 4666 \n", "L 2450 531 \n", "L 3481 531 \n", "L 3481 0 \n", "L 794 0 \n", "L 794 531 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "     </defs>\n", "     <use xlink:href=\"#DejaVuSans-48\"/>\n", "     <use xlink:href=\"#DejaVuSans-65\" x=\"75.195312\"/>\n", "     <use xlink:href=\"#DejaVuSans-61\" x=\"136.71875\"/>\n", "     <use xlink:href=\"#DejaVuSans-64\" x=\"197.998047\"/>\n", "     <use xlink:href=\"#DejaVuSans-20\" x=\"261.474609\"/>\n", "     <use xlink:href=\"#DejaVuSans-31\" x=\"293.261719\"/>\n", "    </g>\n", "   </g>\n", "  </g>\n", "  <g id=\"axes_2\">\n", "   <g id=\"patch_7\">\n", "    <path d=\"M 115.757147 66.773662 \n", "L 183.687582 66.773662 \n", "L 183.687582 26.015401 \n", "L 115.757147 26.015401 \n", "z\n", "\" style=\"fill: #ffffff\"/>\n", "   </g>\n", "   <g clip-path=\"url(#p58f44b6830)\">\n", "    <image xlink:href=\"data:image/png;base64,\n", "iVBORw0KGgoAAAANSUhEUgAAAF8AAAA5CAYAAABQ4feyAAABSklEQVR4nO3bPUoDURRA4ffiKAgGC23tXIArcBHuQldgYecOlOAm3IFg4QayBBU7EfxHiHHGyvpeMHIy8Xz15TI5vOYxk9o+33clcLW9E42UUkrZHV/GQ1+fqV11cys112cD+gH+M+ODjA8yPsj4IOODjA8yPsj4oHq0vB7ecI+fblPLusnHrx/oR11ZndmueeXJBxkfZHyQ8UHGBxkfZHyQ8UG1fXsML1n7a7lXemevmctYTe2qg8U/F4v/C+eY8UHGBxkfZHyQ8UHGBxkfZHxQkxna2xjmtr08xDPTSW6XH8rqLxkfZHyQ8UHGBxkfZHyQ8UH1oAzD14ij97vUsm6a+KdhTb5GXErd/3rNkw8yPsj4IOODjA8yPsj4IOODjA9qTi5GM1vWnp+GM93NdWpXcxjv6jtPPsj4IOODjA8yPsj4IOODjA/6BhxCKmLKLWNUAAAAAElFTkSuQmCC\" id=\"imagea7d07f8c5e\" transform=\"scale(1 -1)translate(0 -41.04)\" x=\"115.757147\" y=\"-25.733662\" width=\"68.4\" height=\"41.04\"/>\n", "   </g>\n", "   <g id=\"matplotlib.axis_3\">\n", "    <g id=\"xtick_3\">\n", "     <g id=\"line2d_5\">\n", "      <g>\n", "       <use xlink:href=\"#m72174680ec\" x=\"119.153668\" y=\"66.773662\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_4\">\n", "     <g id=\"line2d_6\">\n", "      <g>\n", "       <use xlink:href=\"#m72174680ec\" x=\"153.118886\" y=\"66.773662\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "   </g>\n", "   <g id=\"matplotlib.axis_4\">\n", "    <g id=\"ytick_3\">\n", "     <g id=\"line2d_7\">\n", "      <g>\n", "       <use xlink:href=\"#mee265d5878\" x=\"115.757147\" y=\"29.411923\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_4\">\n", "     <g id=\"line2d_8\">\n", "      <g>\n", "       <use xlink:href=\"#mee265d5878\" x=\"115.757147\" y=\"63.37714\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "   </g>\n", "   <g id=\"patch_8\">\n", "    <path d=\"M 115.757147 66.773662 \n", "L 115.757147 26.015401 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_9\">\n", "    <path d=\"M 183.687582 66.773662 \n", "L 183.687582 26.015401 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_10\">\n", "    <path d=\"M 115.757147 66.773662 \n", "L 183.687582 66.773662 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_11\">\n", "    <path d=\"M 115.757147 26.015401 \n", "L 183.687582 26.015401 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"text_5\">\n", "    <!-- Head 2 -->\n", "    <g transform=\"translate(128.307989 20.015401)scale(0.12 -0.12)\">\n", "     <defs>\n", "      <path id=\"DejaVuSans-32\" d=\"M 1228 531 \n", "L 3431 531 \n", "L 3431 0 \n", "L 469 0 \n", "L 469 531 \n", "Q 828 903 1448 1529 \n", "Q 2069 2156 2228 2338 \n", "Q 2531 2678 2651 2914 \n", "Q 2772 3150 2772 3378 \n", "Q 2772 3750 2511 3984 \n", "Q 2250 4219 1831 4219 \n", "Q 1534 4219 1204 4116 \n", "Q 875 4013 500 3803 \n", "L 500 4441 \n", "Q 881 4594 1212 4672 \n", "Q 1544 4750 1819 4750 \n", "Q 2544 4750 2975 4387 \n", "Q 3406 4025 3406 3419 \n", "Q 3406 3131 3298 2873 \n", "Q 3191 2616 2906 2266 \n", "Q 2828 2175 2409 1742 \n", "Q 1991 1309 1228 531 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "     </defs>\n", "     <use xlink:href=\"#DejaVuSans-48\"/>\n", "     <use xlink:href=\"#DejaVuSans-65\" x=\"75.195312\"/>\n", "     <use xlink:href=\"#DejaVuSans-61\" x=\"136.71875\"/>\n", "     <use xlink:href=\"#DejaVuSans-64\" x=\"197.998047\"/>\n", "     <use xlink:href=\"#DejaVuSans-20\" x=\"261.474609\"/>\n", "     <use xlink:href=\"#DejaVuSans-32\" x=\"293.261719\"/>\n", "    </g>\n", "   </g>\n", "  </g>\n", "  <g id=\"axes_3\">\n", "   <g id=\"patch_12\">\n", "    <path d=\"M 197.273668 66.773662 \n", "L 265.204103 66.773662 \n", "L 265.204103 26.015401 \n", "L 197.273668 26.015401 \n", "z\n", "\" style=\"fill: #ffffff\"/>\n", "   </g>\n", "   <g clip-path=\"url(#pc34a698109)\">\n", "    <image xlink:href=\"data:image/png;base64,\n", "iVBORw0KGgoAAAANSUhEUgAAAF8AAAA5CAYAAABQ4feyAAABW0lEQVR4nO3asS4EURSA4b1DZJMVCskqt6VWEA+gETqtRKWlWM+g0FNryRb0SGTjDUSlEVHZRCSEWKvwAOd0f4b/q0/OTP7c5mamfF2fjhqB0c1FNPJrMAhHyspaatXY4mrumTVW0S/wnxkfZHyQ8UHGBxkfZHyQ8UHGB5Wnhbnwhjt71c9t+3wPR143cjfcqfPL3DNrzJMPMj7I+CDjg4wPMj7I+CDjg8r382N4ydprz6eW7Z8dhDPV8npqV5lopubqzJMPMj7I+CDjg4wPMj7I+CDjg4wPGm+UEg5tdWZSy4a9k3CmWvr7P8BmefJBxgcZH2R8kPFBxgcZH2R8UNluTIafEQ9f7lPLRg938VBrOrWrandSc3XmyQcZH2R8kPFBxgcZH2R8kPFBxgeV4W0/vOG+dXdTy1rHvXDmY2cztat5FO+qO08+yPgg44OMDzI+yPgg44OMD/oBG9MtT6/1MKgAAAAASUVORK5CYII=\" id=\"image5ff11abcde\" transform=\"scale(1 -1)translate(0 -41.04)\" x=\"197.273668\" y=\"-25.733662\" width=\"68.4\" height=\"41.04\"/>\n", "   </g>\n", "   <g id=\"matplotlib.axis_5\">\n", "    <g id=\"xtick_5\">\n", "     <g id=\"line2d_9\">\n", "      <g>\n", "       <use xlink:href=\"#m72174680ec\" x=\"200.67019\" y=\"66.773662\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_6\">\n", "     <g id=\"line2d_10\">\n", "      <g>\n", "       <use xlink:href=\"#m72174680ec\" x=\"234.635408\" y=\"66.773662\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "   </g>\n", "   <g id=\"matplotlib.axis_6\">\n", "    <g id=\"ytick_5\">\n", "     <g id=\"line2d_11\">\n", "      <g>\n", "       <use xlink:href=\"#mee265d5878\" x=\"197.273668\" y=\"29.411923\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_6\">\n", "     <g id=\"line2d_12\">\n", "      <g>\n", "       <use xlink:href=\"#mee265d5878\" x=\"197.273668\" y=\"63.37714\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "   </g>\n", "   <g id=\"patch_13\">\n", "    <path d=\"M 197.273668 66.773662 \n", "L 197.273668 26.015401 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_14\">\n", "    <path d=\"M 265.204103 66.773662 \n", "L 265.204103 26.015401 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_15\">\n", "    <path d=\"M 197.273668 66.773662 \n", "L 265.204103 66.773662 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_16\">\n", "    <path d=\"M 197.273668 26.015401 \n", "L 265.204103 26.015401 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"text_6\">\n", "    <!-- Head 3 -->\n", "    <g transform=\"translate(209.824511 20.015401)scale(0.12 -0.12)\">\n", "     <defs>\n", "      <path id=\"DejaVuSans-33\" d=\"M 2597 2516 \n", "Q 3050 2419 3304 2112 \n", "Q 3559 1806 3559 1356 \n", "Q 3559 666 3084 287 \n", "Q 2609 -91 1734 -91 \n", "Q 1441 -91 1130 -33 \n", "Q 819 25 488 141 \n", "L 488 750 \n", "Q 750 597 1062 519 \n", "Q 1375 441 1716 441 \n", "Q 2309 441 2620 675 \n", "Q 2931 909 2931 1356 \n", "Q 2931 1769 2642 2001 \n", "Q 2353 2234 1838 2234 \n", "L 1294 2234 \n", "L 1294 2753 \n", "L 1863 2753 \n", "Q 2328 2753 2575 2939 \n", "Q 2822 3125 2822 3475 \n", "Q 2822 3834 2567 4026 \n", "Q 2313 4219 1838 4219 \n", "Q 1578 4219 1281 4162 \n", "Q 984 4106 628 3988 \n", "L 628 4550 \n", "Q 988 4650 1302 4700 \n", "Q 1616 4750 1894 4750 \n", "Q 2613 4750 3031 4423 \n", "Q 3450 4097 3450 3541 \n", "Q 3450 3153 3228 2886 \n", "Q 3006 2619 2597 2516 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "     </defs>\n", "     <use xlink:href=\"#DejaVuSans-48\"/>\n", "     <use xlink:href=\"#DejaVuSans-65\" x=\"75.195312\"/>\n", "     <use xlink:href=\"#DejaVuSans-61\" x=\"136.71875\"/>\n", "     <use xlink:href=\"#DejaVuSans-64\" x=\"197.998047\"/>\n", "     <use xlink:href=\"#DejaVuSans-20\" x=\"261.474609\"/>\n", "     <use xlink:href=\"#DejaVuSans-33\" x=\"293.261719\"/>\n", "    </g>\n", "   </g>\n", "  </g>\n", "  <g id=\"axes_4\">\n", "   <g id=\"patch_17\">\n", "    <path d=\"M 278.79019 66.773662 \n", "L 346.720625 66.773662 \n", "L 346.720625 26.015401 \n", "L 278.79019 26.015401 \n", "z\n", "\" style=\"fill: #ffffff\"/>\n", "   </g>\n", "   <g clip-path=\"url(#pf740cccb89)\">\n", "    <image xlink:href=\"data:image/png;base64,\n", "iVBORw0KGgoAAAANSUhEUgAAAF8AAAA5CAYAAABQ4feyAAABTklEQVR4nO3ZoUpDYRiH8e87DLEIBmEgyIaGFWXF6zB7CXoR3oaK0WIxKEarwWYUMRi8ADUMnIqfQczv2x4OPr/85914dsph9ev2qpVAN9mOJqWUUr4vTsJNe7hP3RocHKd2fdbRX+A/Mz7I+CDjg4wPMj7I+CDjg4wPqs/TSfiGu3qae9usw1E8ms9yt1bWUrs+88kHGR9kfJDxQcYHGR9kfJDxQbXNXsOXrPY5Tx07G22Gm92769StbjhO7frMJx9kfJDxQcYHGR9kfJDxQcYHGR80yIz2lzdSx47enuJR5+/9xxIg44OMDzI+yPgg44OMDzI+KPc34sd76tjl+jTc7Nycp251463Urs988kHGBxkfZHyQ8UHGBxkfZHyQ8UF1ryyFb7iHL4+5YwuL4aa18ON+b9Wa2vWZTz7I+CDjg4wPMj7I+CDjg4wP+gERTy3mrzzZNAAAAABJRU5ErkJggg==\" id=\"imagedd92049109\" transform=\"scale(1 -1)translate(0 -41.04)\" x=\"278.79019\" y=\"-25.733662\" width=\"68.4\" height=\"41.04\"/>\n", "   </g>\n", "   <g id=\"matplotlib.axis_7\">\n", "    <g id=\"xtick_7\">\n", "     <g id=\"line2d_13\">\n", "      <g>\n", "       <use xlink:href=\"#m72174680ec\" x=\"282.186712\" y=\"66.773662\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_8\">\n", "     <g id=\"line2d_14\">\n", "      <g>\n", "       <use xlink:href=\"#m72174680ec\" x=\"316.151929\" y=\"66.773662\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "   </g>\n", "   <g id=\"matplotlib.axis_8\">\n", "    <g id=\"ytick_7\">\n", "     <g id=\"line2d_15\">\n", "      <g>\n", "       <use xlink:href=\"#mee265d5878\" x=\"278.79019\" y=\"29.411923\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_8\">\n", "     <g id=\"line2d_16\">\n", "      <g>\n", "       <use xlink:href=\"#mee265d5878\" x=\"278.79019\" y=\"63.37714\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "   </g>\n", "   <g id=\"patch_18\">\n", "    <path d=\"M 278.79019 66.773662 \n", "L 278.79019 26.015401 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_19\">\n", "    <path d=\"M 346.720625 66.773662 \n", "L 346.720625 26.015401 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_20\">\n", "    <path d=\"M 278.79019 66.773662 \n", "L 346.720625 66.773662 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_21\">\n", "    <path d=\"M 278.79019 26.015401 \n", "L 346.720625 26.015401 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"text_7\">\n", "    <!-- Head 4 -->\n", "    <g transform=\"translate(291.341033 20.015401)scale(0.12 -0.12)\">\n", "     <defs>\n", "      <path id=\"DejaVuSans-34\" d=\"M 2419 4116 \n", "L 825 1625 \n", "L 2419 1625 \n", "L 2419 4116 \n", "z\n", "M 2253 4666 \n", "L 3047 4666 \n", "L 3047 1625 \n", "L 3713 1625 \n", "L 3713 1100 \n", "L 3047 1100 \n", "L 3047 0 \n", "L 2419 0 \n", "L 2419 1100 \n", "L 313 1100 \n", "L 313 1709 \n", "L 2253 4666 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "     </defs>\n", "     <use xlink:href=\"#DejaVuSans-48\"/>\n", "     <use xlink:href=\"#DejaVuSans-65\" x=\"75.195312\"/>\n", "     <use xlink:href=\"#DejaVuSans-61\" x=\"136.71875\"/>\n", "     <use xlink:href=\"#DejaVuSans-64\" x=\"197.998047\"/>\n", "     <use xlink:href=\"#DejaVuSans-20\" x=\"261.474609\"/>\n", "     <use xlink:href=\"#DejaVuSans-34\" x=\"293.261719\"/>\n", "    </g>\n", "   </g>\n", "  </g>\n", "  <g id=\"axes_5\">\n", "   <g id=\"patch_22\">\n", "    <path d=\"M 34.240625 172.613662 \n", "L 102.17106 172.613662 \n", "L 102.17106 131.855401 \n", "L 34.240625 131.855401 \n", "z\n", "\" style=\"fill: #ffffff\"/>\n", "   </g>\n", "   <g clip-path=\"url(#p89ec5908de)\">\n", "    <image xlink:href=\"data:image/png;base64,\n", "iVBORw0KGgoAAAANSUhEUgAAAF8AAAA5CAYAAABQ4feyAAABU0lEQVR4nO3ZsS6DYRSH8ffrVwvSRJAmGFhEQmLkBsTuFgwNd4DVZGLo4C4sYquNG2AzEDFYTGKoaF3COduTL31+8z+nzZN3aVr9XhyPS6A+PIsmpZRS/q5Ows3P41PqVudmkNo1WYv+ApPM+CDjg4wPMj7I+CDjg4wPMj6o3do7iFffX7lrw2E4eX/J3drMfWKj+fJBxgcZH2R8kPFBxgcZH2R8UNWfmQ//Rjx6vk8dGz3cxR+4u5+61equpnZN5ssHGR9kfJDxQcYHGR9kfJDxQcYHVaPP1/AX7unydurY+e1lPFpYSt2qN3ZSuybz5YOMDzI+yPgg44OMDzI+yPigqldmwx9Z/cF17thcN96srOduTXdSuybz5YOMDzI+yPgg44OMDzI+yPgg44Pai1N1vPp4y11b24o341Hu1gTw5YOMDzI+yPgg44OMDzI+yPigf7pYJ3O/kPmqAAAAAElFTkSuQmCC\" id=\"image151a2c5dc3\" transform=\"scale(1 -1)translate(0 -41.04)\" x=\"34.240625\" y=\"-131.573662\" width=\"68.4\" height=\"41.04\"/>\n", "   </g>\n", "   <g id=\"matplotlib.axis_9\">\n", "    <g id=\"xtick_9\">\n", "     <g id=\"line2d_17\">\n", "      <g>\n", "       <use xlink:href=\"#m72174680ec\" x=\"37.637147\" y=\"172.613662\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_8\">\n", "      <!-- 0 -->\n", "      <g transform=\"translate(34.455897 187.212099)scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_10\">\n", "     <g id=\"line2d_18\">\n", "      <g>\n", "       <use xlink:href=\"#m72174680ec\" x=\"71.602364\" y=\"172.613662\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_9\">\n", "      <!-- 5 -->\n", "      <g transform=\"translate(68.421114 187.212099)scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-35\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"text_10\">\n", "     <!-- Key positions -->\n", "     <g transform=\"translate(35.142561 200.890224)scale(0.1 -0.1)\">\n", "      <defs>\n", "       <path id=\"DejaVuSans-4b\" d=\"M 628 4666 \n", "L 1259 4666 \n", "L 1259 2694 \n", "L 3353 4666 \n", "L 4166 4666 \n", "L 1850 2491 \n", "L 4331 0 \n", "L 3500 0 \n", "L 1259 2247 \n", "L 1259 0 \n", "L 628 0 \n", "L 628 4666 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "      </defs>\n", "      <use xlink:href=\"#DejaVuSans-4b\"/>\n", "      <use xlink:href=\"#DejaVuSans-65\" x=\"60.576172\"/>\n", "      <use xlink:href=\"#DejaVuSans-79\" x=\"122.099609\"/>\n", "      <use xlink:href=\"#DejaVuSans-20\" x=\"181.279297\"/>\n", "      <use xlink:href=\"#DejaVuSans-70\" x=\"213.066406\"/>\n", "      <use xlink:href=\"#DejaVuSans-6f\" x=\"276.542969\"/>\n", "      <use xlink:href=\"#DejaVuSans-73\" x=\"337.724609\"/>\n", "      <use xlink:href=\"#DejaVuSans-69\" x=\"389.824219\"/>\n", "      <use xlink:href=\"#DejaVuSans-74\" x=\"417.607422\"/>\n", "      <use xlink:href=\"#DejaVuSans-69\" x=\"456.816406\"/>\n", "      <use xlink:href=\"#DejaVuSans-6f\" x=\"484.599609\"/>\n", "      <use xlink:href=\"#DejaVuSans-6e\" x=\"545.78125\"/>\n", "      <use xlink:href=\"#DejaVuSans-73\" x=\"609.160156\"/>\n", "     </g>\n", "    </g>\n", "   </g>\n", "   <g id=\"matplotlib.axis_10\">\n", "    <g id=\"ytick_9\">\n", "     <g id=\"line2d_19\">\n", "      <g>\n", "       <use xlink:href=\"#mee265d5878\" x=\"34.240625\" y=\"135.251923\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_11\">\n", "      <!-- 0 -->\n", "      <g transform=\"translate(20.878125 139.051141)scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_10\">\n", "     <g id=\"line2d_20\">\n", "      <g>\n", "       <use xlink:href=\"#mee265d5878\" x=\"34.240625\" y=\"169.21714\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_12\">\n", "      <!-- 5 -->\n", "      <g transform=\"translate(20.878125 173.016359)scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-35\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"text_13\">\n", "     <!-- Query positions -->\n", "     <g transform=\"translate(14.798437 191.429063)rotate(-90)scale(0.1 -0.1)\">\n", "      <use xlink:href=\"#DejaVuSans-51\"/>\n", "      <use xlink:href=\"#DejaVuSans-75\" x=\"78.710938\"/>\n", "      <use xlink:href=\"#DejaVuSans-65\" x=\"142.089844\"/>\n", "      <use xlink:href=\"#DejaVuSans-72\" x=\"203.613281\"/>\n", "      <use xlink:href=\"#DejaVuSans-79\" x=\"244.726562\"/>\n", "      <use xlink:href=\"#DejaVuSans-20\" x=\"303.90625\"/>\n", "      <use xlink:href=\"#DejaVuSans-70\" x=\"335.693359\"/>\n", "      <use xlink:href=\"#DejaVuSans-6f\" x=\"399.169922\"/>\n", "      <use xlink:href=\"#DejaVuSans-73\" x=\"460.351562\"/>\n", "      <use xlink:href=\"#DejaVuSans-69\" x=\"512.451172\"/>\n", "      <use xlink:href=\"#DejaVuSans-74\" x=\"540.234375\"/>\n", "      <use xlink:href=\"#DejaVuSans-69\" x=\"579.443359\"/>\n", "      <use xlink:href=\"#DejaVuSans-6f\" x=\"607.226562\"/>\n", "      <use xlink:href=\"#DejaVuSans-6e\" x=\"668.408203\"/>\n", "      <use xlink:href=\"#DejaVuSans-73\" x=\"731.787109\"/>\n", "     </g>\n", "    </g>\n", "   </g>\n", "   <g id=\"patch_23\">\n", "    <path d=\"M 34.240625 172.613662 \n", "L 34.240625 131.855401 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_24\">\n", "    <path d=\"M 102.17106 172.613662 \n", "L 102.17106 131.855401 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_25\">\n", "    <path d=\"M 34.240625 172.613662 \n", "L 102.17106 172.613662 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_26\">\n", "    <path d=\"M 34.240625 131.855401 \n", "L 102.17106 131.855401 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"text_14\">\n", "    <!-- Head 1 -->\n", "    <g transform=\"translate(46.791467 125.855401)scale(0.12 -0.12)\">\n", "     <use xlink:href=\"#DejaVuSans-48\"/>\n", "     <use xlink:href=\"#DejaVuSans-65\" x=\"75.195312\"/>\n", "     <use xlink:href=\"#DejaVuSans-61\" x=\"136.71875\"/>\n", "     <use xlink:href=\"#DejaVuSans-64\" x=\"197.998047\"/>\n", "     <use xlink:href=\"#DejaVuSans-20\" x=\"261.474609\"/>\n", "     <use xlink:href=\"#DejaVuSans-31\" x=\"293.261719\"/>\n", "    </g>\n", "   </g>\n", "  </g>\n", "  <g id=\"axes_6\">\n", "   <g id=\"patch_27\">\n", "    <path d=\"M 115.757147 172.613662 \n", "L 183.687582 172.613662 \n", "L 183.687582 131.855401 \n", "L 115.757147 131.855401 \n", "z\n", "\" style=\"fill: #ffffff\"/>\n", "   </g>\n", "   <g clip-path=\"url(#pdc1a692a85)\">\n", "    <image xlink:href=\"data:image/png;base64,\n", "iVBORw0KGgoAAAANSUhEUgAAAF8AAAA5CAYAAABQ4feyAAABT0lEQVR4nO3ZMS4FURSA4XvfmwR5JKIgEolOaQEanVZiE7QWMgWNDejECixBrRCFKFAIjwjVM2MJ53R/Ju//6pNzJ39uczN1dn3Wl8Bo/ygaKaWU8nfZhjN1fTO1a3x4nJobshH9AfPM+CDjg4wPMj7I+CDjg4wPMj6oKds74VB3f5vbNp3GM3sHuV1zwJsPMj7I+CDjg4wPMj7I+CDjg2r39hT+RuzfX1PL+ueHcOa3PU/tWrm6Sc0NmTcfZHyQ8UHGBxkfZHyQ8UHGBxkfVPufz/CFezLZSi27+HpMnFhTu+q4Sc0NmTcfZHyQ8UHGBxkfZHyQ8UHGBzV914VDu5OF5LrwvVbK90du1epG8szh8uaDjA8yPsj4IOODjA8yPsj4IOODaru0Fj5LT1/usuvikW6W27S4nDxzuLz5IOODjA8yPsj4IOODjA8yPugfbysshpHglSEAAAAASUVORK5CYII=\" id=\"image91dffb7198\" transform=\"scale(1 -1)translate(0 -41.04)\" x=\"115.757147\" y=\"-131.573662\" width=\"68.4\" height=\"41.04\"/>\n", "   </g>\n", "   <g id=\"matplotlib.axis_11\">\n", "    <g id=\"xtick_11\">\n", "     <g id=\"line2d_21\">\n", "      <g>\n", "       <use xlink:href=\"#m72174680ec\" x=\"119.153668\" y=\"172.613662\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_15\">\n", "      <!-- 0 -->\n", "      <g transform=\"translate(115.972418 187.212099)scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_12\">\n", "     <g id=\"line2d_22\">\n", "      <g>\n", "       <use xlink:href=\"#m72174680ec\" x=\"153.118886\" y=\"172.613662\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_16\">\n", "      <!-- 5 -->\n", "      <g transform=\"translate(149.937636 187.212099)scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-35\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"text_17\">\n", "     <!-- Key positions -->\n", "     <g transform=\"translate(116.659083 200.890224)scale(0.1 -0.1)\">\n", "      <use xlink:href=\"#DejaVuSans-4b\"/>\n", "      <use xlink:href=\"#DejaVuSans-65\" x=\"60.576172\"/>\n", "      <use xlink:href=\"#DejaVuSans-79\" x=\"122.099609\"/>\n", "      <use xlink:href=\"#DejaVuSans-20\" x=\"181.279297\"/>\n", "      <use xlink:href=\"#DejaVuSans-70\" x=\"213.066406\"/>\n", "      <use xlink:href=\"#DejaVuSans-6f\" x=\"276.542969\"/>\n", "      <use xlink:href=\"#DejaVuSans-73\" x=\"337.724609\"/>\n", "      <use xlink:href=\"#DejaVuSans-69\" x=\"389.824219\"/>\n", "      <use xlink:href=\"#DejaVuSans-74\" x=\"417.607422\"/>\n", "      <use xlink:href=\"#DejaVuSans-69\" x=\"456.816406\"/>\n", "      <use xlink:href=\"#DejaVuSans-6f\" x=\"484.599609\"/>\n", "      <use xlink:href=\"#DejaVuSans-6e\" x=\"545.78125\"/>\n", "      <use xlink:href=\"#DejaVuSans-73\" x=\"609.160156\"/>\n", "     </g>\n", "    </g>\n", "   </g>\n", "   <g id=\"matplotlib.axis_12\">\n", "    <g id=\"ytick_11\">\n", "     <g id=\"line2d_23\">\n", "      <g>\n", "       <use xlink:href=\"#mee265d5878\" x=\"115.757147\" y=\"135.251923\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_12\">\n", "     <g id=\"line2d_24\">\n", "      <g>\n", "       <use xlink:href=\"#mee265d5878\" x=\"115.757147\" y=\"169.21714\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "   </g>\n", "   <g id=\"patch_28\">\n", "    <path d=\"M 115.757147 172.613662 \n", "L 115.757147 131.855401 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_29\">\n", "    <path d=\"M 183.687582 172.613662 \n", "L 183.687582 131.855401 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_30\">\n", "    <path d=\"M 115.757147 172.613662 \n", "L 183.687582 172.613662 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_31\">\n", "    <path d=\"M 115.757147 131.855401 \n", "L 183.687582 131.855401 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"text_18\">\n", "    <!-- Head 2 -->\n", "    <g transform=\"translate(128.307989 125.855401)scale(0.12 -0.12)\">\n", "     <use xlink:href=\"#DejaVuSans-48\"/>\n", "     <use xlink:href=\"#DejaVuSans-65\" x=\"75.195312\"/>\n", "     <use xlink:href=\"#DejaVuSans-61\" x=\"136.71875\"/>\n", "     <use xlink:href=\"#DejaVuSans-64\" x=\"197.998047\"/>\n", "     <use xlink:href=\"#DejaVuSans-20\" x=\"261.474609\"/>\n", "     <use xlink:href=\"#DejaVuSans-32\" x=\"293.261719\"/>\n", "    </g>\n", "   </g>\n", "  </g>\n", "  <g id=\"axes_7\">\n", "   <g id=\"patch_32\">\n", "    <path d=\"M 197.273668 172.613662 \n", "L 265.204103 172.613662 \n", "L 265.204103 131.855401 \n", "L 197.273668 131.855401 \n", "z\n", "\" style=\"fill: #ffffff\"/>\n", "   </g>\n", "   <g clip-path=\"url(#pc1b293a7b9)\">\n", "    <image xlink:href=\"data:image/png;base64,\n", "iVBORw0KGgoAAAANSUhEUgAAAF8AAAA5CAYAAABQ4feyAAABN0lEQVR4nO3aMUoDURRG4TeTV6kIsXYNbsHCPYhuQGzSuAp3oL21WNtYWWcDamUqJRkRAoKJY2V9L1gcwpyv/rmEk9clzfpl2pdIOwonpZTSP9zGm/e31K06uUztNllLf4AhMz7I+CDjg4wPMj7I+CDjg4wPqplRszNOHetXq3jUdalbQ+DLBxkfZHyQ8UHGBxkfZHyQ8UG1fC7C0c/sKXUs8xNhe3qWujUEvnyQ8UHGBxkfZHyQ8UHGBxkfZHxQ0y8/wj/Knm/vp45dL2f//kBD4ssHGR9kfJDxQcYHGR9kfJDxQfX74iQcXXXPqWPrx7t4tLuXujU6OEztNpkvH2R8kPFBxgcZH2R8kPFBxgcZH1S/XufhaOv+JnWsPTqOR43f9x9LgIwPMj7I+CDjg4wPMj7I+KBfCUkqV02NF20AAAAASUVORK5CYII=\" id=\"image75665c9a43\" transform=\"scale(1 -1)translate(0 -41.04)\" x=\"197.273668\" y=\"-131.573662\" width=\"68.4\" height=\"41.04\"/>\n", "   </g>\n", "   <g id=\"matplotlib.axis_13\">\n", "    <g id=\"xtick_13\">\n", "     <g id=\"line2d_25\">\n", "      <g>\n", "       <use xlink:href=\"#m72174680ec\" x=\"200.67019\" y=\"172.613662\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_19\">\n", "      <!-- 0 -->\n", "      <g transform=\"translate(197.48894 187.212099)scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_14\">\n", "     <g id=\"line2d_26\">\n", "      <g>\n", "       <use xlink:href=\"#m72174680ec\" x=\"234.635408\" y=\"172.613662\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_20\">\n", "      <!-- 5 -->\n", "      <g transform=\"translate(231.454158 187.212099)scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-35\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"text_21\">\n", "     <!-- Key positions -->\n", "     <g transform=\"translate(198.175605 200.890224)scale(0.1 -0.1)\">\n", "      <use xlink:href=\"#DejaVuSans-4b\"/>\n", "      <use xlink:href=\"#DejaVuSans-65\" x=\"60.576172\"/>\n", "      <use xlink:href=\"#DejaVuSans-79\" x=\"122.099609\"/>\n", "      <use xlink:href=\"#DejaVuSans-20\" x=\"181.279297\"/>\n", "      <use xlink:href=\"#DejaVuSans-70\" x=\"213.066406\"/>\n", "      <use xlink:href=\"#DejaVuSans-6f\" x=\"276.542969\"/>\n", "      <use xlink:href=\"#DejaVuSans-73\" x=\"337.724609\"/>\n", "      <use xlink:href=\"#DejaVuSans-69\" x=\"389.824219\"/>\n", "      <use xlink:href=\"#DejaVuSans-74\" x=\"417.607422\"/>\n", "      <use xlink:href=\"#DejaVuSans-69\" x=\"456.816406\"/>\n", "      <use xlink:href=\"#DejaVuSans-6f\" x=\"484.599609\"/>\n", "      <use xlink:href=\"#DejaVuSans-6e\" x=\"545.78125\"/>\n", "      <use xlink:href=\"#DejaVuSans-73\" x=\"609.160156\"/>\n", "     </g>\n", "    </g>\n", "   </g>\n", "   <g id=\"matplotlib.axis_14\">\n", "    <g id=\"ytick_13\">\n", "     <g id=\"line2d_27\">\n", "      <g>\n", "       <use xlink:href=\"#mee265d5878\" x=\"197.273668\" y=\"135.251923\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_14\">\n", "     <g id=\"line2d_28\">\n", "      <g>\n", "       <use xlink:href=\"#mee265d5878\" x=\"197.273668\" y=\"169.21714\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "   </g>\n", "   <g id=\"patch_33\">\n", "    <path d=\"M 197.273668 172.613662 \n", "L 197.273668 131.855401 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_34\">\n", "    <path d=\"M 265.204103 172.613662 \n", "L 265.204103 131.855401 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_35\">\n", "    <path d=\"M 197.273668 172.613662 \n", "L 265.204103 172.613662 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_36\">\n", "    <path d=\"M 197.273668 131.855401 \n", "L 265.204103 131.855401 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"text_22\">\n", "    <!-- Head 3 -->\n", "    <g transform=\"translate(209.824511 125.855401)scale(0.12 -0.12)\">\n", "     <use xlink:href=\"#DejaVuSans-48\"/>\n", "     <use xlink:href=\"#DejaVuSans-65\" x=\"75.195312\"/>\n", "     <use xlink:href=\"#DejaVuSans-61\" x=\"136.71875\"/>\n", "     <use xlink:href=\"#DejaVuSans-64\" x=\"197.998047\"/>\n", "     <use xlink:href=\"#DejaVuSans-20\" x=\"261.474609\"/>\n", "     <use xlink:href=\"#DejaVuSans-33\" x=\"293.261719\"/>\n", "    </g>\n", "   </g>\n", "  </g>\n", "  <g id=\"axes_8\">\n", "   <g id=\"patch_37\">\n", "    <path d=\"M 278.79019 172.613662 \n", "L 346.720625 172.613662 \n", "L 346.720625 131.855401 \n", "L 278.79019 131.855401 \n", "z\n", "\" style=\"fill: #ffffff\"/>\n", "   </g>\n", "   <g clip-path=\"url(#p8b820e7c13)\">\n", "    <image xlink:href=\"data:image/png;base64,\n", "iVBORw0KGgoAAAANSUhEUgAAAF8AAAA5CAYAAABQ4feyAAABOklEQVR4nO3ZMUoDURCA4fd2N4KNhWCd0sJWUngAG2trT6AnEGy8gNoKnsBCEOytvIdtsDHEIrKxs50hEX7D/l89DI+faZat3483yxJojk6ikVJKKf3rUzz0NU/tak/PU3ObrKEfMGTGBxkfZHyQ8UHGBxkfZHyQ8UFdMzkOh/q3l9y2+Sye2d3L7RoALx9kfJDxQcYHGR9kfJDxQcYH1f7zI/yNeLEzTi27m72v/aAh8fJBxgcZH2R8kPFBxgcZH2R8kPFBXW3i/otl+BGsFXj5IOODjA8yPsj4IOODjA8yPqgurs7iL6jpNLWsvb5f9z2/6tb2n+36r7x8kPFBxgcZH2R8kPFBxgcZH2R8UFdGo3CovbxNLeufH8KZenCY2lX3J6m5Teblg4wPMj7I+CDjg4wPMj7I+KAfI1kiU7DCFp4AAAAASUVORK5CYII=\" id=\"image9d5fc25450\" transform=\"scale(1 -1)translate(0 -41.04)\" x=\"278.79019\" y=\"-131.573662\" width=\"68.4\" height=\"41.04\"/>\n", "   </g>\n", "   <g id=\"matplotlib.axis_15\">\n", "    <g id=\"xtick_15\">\n", "     <g id=\"line2d_29\">\n", "      <g>\n", "       <use xlink:href=\"#m72174680ec\" x=\"282.186712\" y=\"172.613662\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_23\">\n", "      <!-- 0 -->\n", "      <g transform=\"translate(279.005462 187.212099)scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_16\">\n", "     <g id=\"line2d_30\">\n", "      <g>\n", "       <use xlink:href=\"#m72174680ec\" x=\"316.151929\" y=\"172.613662\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_24\">\n", "      <!-- 5 -->\n", "      <g transform=\"translate(312.970679 187.212099)scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-35\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"text_25\">\n", "     <!-- Key positions -->\n", "     <g transform=\"translate(279.692126 200.890224)scale(0.1 -0.1)\">\n", "      <use xlink:href=\"#DejaVuSans-4b\"/>\n", "      <use xlink:href=\"#DejaVuSans-65\" x=\"60.576172\"/>\n", "      <use xlink:href=\"#DejaVuSans-79\" x=\"122.099609\"/>\n", "      <use xlink:href=\"#DejaVuSans-20\" x=\"181.279297\"/>\n", "      <use xlink:href=\"#DejaVuSans-70\" x=\"213.066406\"/>\n", "      <use xlink:href=\"#DejaVuSans-6f\" x=\"276.542969\"/>\n", "      <use xlink:href=\"#DejaVuSans-73\" x=\"337.724609\"/>\n", "      <use xlink:href=\"#DejaVuSans-69\" x=\"389.824219\"/>\n", "      <use xlink:href=\"#DejaVuSans-74\" x=\"417.607422\"/>\n", "      <use xlink:href=\"#DejaVuSans-69\" x=\"456.816406\"/>\n", "      <use xlink:href=\"#DejaVuSans-6f\" x=\"484.599609\"/>\n", "      <use xlink:href=\"#DejaVuSans-6e\" x=\"545.78125\"/>\n", "      <use xlink:href=\"#DejaVuSans-73\" x=\"609.160156\"/>\n", "     </g>\n", "    </g>\n", "   </g>\n", "   <g id=\"matplotlib.axis_16\">\n", "    <g id=\"ytick_15\">\n", "     <g id=\"line2d_31\">\n", "      <g>\n", "       <use xlink:href=\"#mee265d5878\" x=\"278.79019\" y=\"135.251923\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_16\">\n", "     <g id=\"line2d_32\">\n", "      <g>\n", "       <use xlink:href=\"#mee265d5878\" x=\"278.79019\" y=\"169.21714\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "   </g>\n", "   <g id=\"patch_38\">\n", "    <path d=\"M 278.79019 172.613662 \n", "L 278.79019 131.855401 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_39\">\n", "    <path d=\"M 346.720625 172.613662 \n", "L 346.720625 131.855401 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_40\">\n", "    <path d=\"M 278.79019 172.613662 \n", "L 346.720625 172.613662 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_41\">\n", "    <path d=\"M 278.79019 131.855401 \n", "L 346.720625 131.855401 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"text_26\">\n", "    <!-- Head 4 -->\n", "    <g transform=\"translate(291.341033 125.855401)scale(0.12 -0.12)\">\n", "     <use xlink:href=\"#DejaVuSans-48\"/>\n", "     <use xlink:href=\"#DejaVuSans-65\" x=\"75.195312\"/>\n", "     <use xlink:href=\"#DejaVuSans-61\" x=\"136.71875\"/>\n", "     <use xlink:href=\"#DejaVuSans-64\" x=\"197.998047\"/>\n", "     <use xlink:href=\"#DejaVuSans-20\" x=\"261.474609\"/>\n", "     <use xlink:href=\"#DejaVuSans-34\" x=\"293.261719\"/>\n", "    </g>\n", "   </g>\n", "  </g>\n", "  <g id=\"axes_9\">\n", "   <g id=\"patch_42\">\n", "    <path d=\"M 366.250625 157.526531 \n", "L 372.071825 157.526531 \n", "L 372.071825 41.102531 \n", "L 366.250625 41.102531 \n", "z\n", "\" style=\"fill: #ffffff\"/>\n", "   </g>\n", "   <g id=\"patch_43\">\n", "    <path clip-path=\"url(#pdd15af8364)\" style=\"fill: #ffffff; stroke: #ffffff; stroke-width: 0.01; stroke-linejoin: miter\"/>\n", "   </g>\n", "   <image xlink:href=\"data:image/png;base64,\n", "iVBORw0KGgoAAAANSUhEUgAAAAgAAACiCAYAAAB8iIwDAAAA60lEQVR4nOWXMQ7DMAwDVcD/f2uWbpHUH+gM0BZstGsE6kjaSPrJ75NW/IZlVM9tWJYCPQpBCkYDQStIIVGhIYf9NjnqjgNDA+4wEDRAkIkKGDUzyAqYA3ahQxIDJ6nngId2v039yE20uR9ST/KELhruhX79dcgFNtGFPHCDzbwB8ogumIFerI5dqB8YrICQd9hUv+X+pIugoAIZyOaEQvnchssucAVCxn6behcdkPqJwhV4cWqBmRw6FGgA/1bjvSAFZMAkXe9igU1aIUPqNl9y8S5YodaNkFiWbhMvDkPSClTAuk+AnGCoJX6MlWEIAYuDfgAAAABJRU5ErkJggg==\" id=\"imaged3eaa1a202\" transform=\"scale(1 -1)translate(0 -116.64)\" x=\"366.48\" y=\"-40.32\" width=\"5.76\" height=\"116.64\"/>\n", "   <g id=\"matplotlib.axis_17\">\n", "    <g id=\"ytick_17\">\n", "     <g id=\"line2d_33\">\n", "      <defs>\n", "       <path id=\"m2d944a38c2\" d=\"M 0 0 \n", "L 3.5 0 \n", "\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </defs>\n", "      <g>\n", "       <use xlink:href=\"#m2d944a38c2\" x=\"372.071825\" y=\"157.526531\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_27\">\n", "      <!-- 0.0 -->\n", "      <g transform=\"translate(379.071825 161.32575)scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-2e\" d=\"M 684 794 \n", "L 1344 794 \n", "L 1344 0 \n", "L 684 0 \n", "L 684 794 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"95.410156\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_18\">\n", "     <g id=\"line2d_34\">\n", "      <g>\n", "       <use xlink:href=\"#m2d944a38c2\" x=\"372.071825\" y=\"134.206556\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_28\">\n", "      <!-- 0.2 -->\n", "      <g transform=\"translate(379.071825 138.005775)scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-32\" x=\"95.410156\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_19\">\n", "     <g id=\"line2d_35\">\n", "      <g>\n", "       <use xlink:href=\"#m2d944a38c2\" x=\"372.071825\" y=\"110.886582\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_29\">\n", "      <!-- 0.4 -->\n", "      <g transform=\"translate(379.071825 114.6858)scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-34\" x=\"95.410156\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_20\">\n", "     <g id=\"line2d_36\">\n", "      <g>\n", "       <use xlink:href=\"#m2d944a38c2\" x=\"372.071825\" y=\"87.566607\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_30\">\n", "      <!-- 0.6 -->\n", "      <g transform=\"translate(379.071825 91.365826)scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-36\" d=\"M 2113 2584 \n", "Q 1688 2584 1439 2293 \n", "Q 1191 2003 1191 1497 \n", "Q 1191 994 1439 701 \n", "Q 1688 409 2113 409 \n", "Q 2538 409 2786 701 \n", "Q 3034 994 3034 1497 \n", "Q 3034 2003 2786 2293 \n", "Q 2538 2584 2113 2584 \n", "z\n", "M 3366 4563 \n", "L 3366 3988 \n", "Q 3128 4100 2886 4159 \n", "Q 2644 4219 2406 4219 \n", "Q 1781 4219 1451 3797 \n", "Q 1122 3375 1075 2522 \n", "Q 1259 2794 1537 2939 \n", "Q 1816 3084 2150 3084 \n", "Q 2853 3084 3261 2657 \n", "Q 3669 2231 3669 1497 \n", "Q 3669 778 3244 343 \n", "Q 2819 -91 2113 -91 \n", "Q 1303 -91 875 529 \n", "Q 447 1150 447 2328 \n", "Q 447 3434 972 4092 \n", "Q 1497 4750 2381 4750 \n", "Q 2619 4750 2861 4703 \n", "Q 3103 4656 3366 4563 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-36\" x=\"95.410156\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_21\">\n", "     <g id=\"line2d_37\">\n", "      <g>\n", "       <use xlink:href=\"#m2d944a38c2\" x=\"372.071825\" y=\"64.246632\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_31\">\n", "      <!-- 0.8 -->\n", "      <g transform=\"translate(379.071825 68.045851)scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-38\" d=\"M 2034 2216 \n", "Q 1584 2216 1326 1975 \n", "Q 1069 1734 1069 1313 \n", "Q 1069 891 1326 650 \n", "Q 1584 409 2034 409 \n", "Q 2484 409 2743 651 \n", "Q 3003 894 3003 1313 \n", "Q 3003 1734 2745 1975 \n", "Q 2488 2216 2034 2216 \n", "z\n", "M 1403 2484 \n", "Q 997 2584 770 2862 \n", "Q 544 3141 544 3541 \n", "Q 544 4100 942 4425 \n", "Q 1341 4750 2034 4750 \n", "Q 2731 4750 3128 4425 \n", "Q 3525 4100 3525 3541 \n", "Q 3525 3141 3298 2862 \n", "Q 3072 2584 2669 2484 \n", "Q 3125 2378 3379 2068 \n", "Q 3634 1759 3634 1313 \n", "Q 3634 634 3220 271 \n", "Q 2806 -91 2034 -91 \n", "Q 1263 -91 848 271 \n", "Q 434 634 434 1313 \n", "Q 434 1759 690 2068 \n", "Q 947 2378 1403 2484 \n", "z\n", "M 1172 3481 \n", "Q 1172 3119 1398 2916 \n", "Q 1625 2713 2034 2713 \n", "Q 2441 2713 2670 2916 \n", "Q 2900 3119 2900 3481 \n", "Q 2900 3844 2670 4047 \n", "Q 2441 4250 2034 4250 \n", "Q 1625 4250 1398 4047 \n", "Q 1172 3844 1172 3481 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-38\" x=\"95.410156\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "   </g>\n", "   <g id=\"LineCollection_1\"/>\n", "   <g id=\"patch_44\">\n", "    <path d=\"M 366.250625 157.526531 \n", "L 369.161225 157.526531 \n", "L 372.071825 157.526531 \n", "L 372.071825 41.102531 \n", "L 369.161225 41.102531 \n", "L 366.250625 41.102531 \n", "L 366.250625 157.526531 \n", "z\n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "  </g>\n", " </g>\n", " <defs>\n", "  <clipPath id=\"pf1c967b119\">\n", "   <rect x=\"34.240625\" y=\"26.015401\" width=\"67.930435\" height=\"40.758261\"/>\n", "  </clipPath>\n", "  <clipPath id=\"p58f44b6830\">\n", "   <rect x=\"115.757147\" y=\"26.015401\" width=\"67.930435\" height=\"40.758261\"/>\n", "  </clipPath>\n", "  <clipPath id=\"pc34a698109\">\n", "   <rect x=\"197.273668\" y=\"26.015401\" width=\"67.930435\" height=\"40.758261\"/>\n", "  </clipPath>\n", "  <clipPath id=\"pf740cccb89\">\n", "   <rect x=\"278.79019\" y=\"26.015401\" width=\"67.930435\" height=\"40.758261\"/>\n", "  </clipPath>\n", "  <clipPath id=\"p89ec5908de\">\n", "   <rect x=\"34.240625\" y=\"131.855401\" width=\"67.930435\" height=\"40.758261\"/>\n", "  </clipPath>\n", "  <clipPath id=\"pdc1a692a85\">\n", "   <rect x=\"115.757147\" y=\"131.855401\" width=\"67.930435\" height=\"40.758261\"/>\n", "  </clipPath>\n", "  <clipPath id=\"pc1b293a7b9\">\n", "   <rect x=\"197.273668\" y=\"131.855401\" width=\"67.930435\" height=\"40.758261\"/>\n", "  </clipPath>\n", "  <clipPath id=\"p8b820e7c13\">\n", "   <rect x=\"278.79019\" y=\"131.855401\" width=\"67.930435\" height=\"40.758261\"/>\n", "  </clipPath>\n", "  <clipPath id=\"pdd15af8364\">\n", "   <rect x=\"366.250625\" y=\"41.102531\" width=\"5.8212\" height=\"116.424\"/>\n", "  </clipPath>\n", " </defs>\n", "</svg>\n"], "text/plain": ["<Figure size 700x350 with 9 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["d2l.show_heatmaps(\n", "    dec_inter_attention_weights, xlabel='Key positions',\n", "    ylabel='Query positions', titles=['Head %d' % i for i in range(1, 5)],\n", "    figsize=(7, 3.5))"]}, {"cell_type": "markdown", "id": "12f431d4", "metadata": {"origin_pos": 84}, "source": ["尽管Transformer架构是为了*序列到序列*的学习而提出的，但正如本书后面将提及的那样，Transformer编码器或Transformer解码器通常被单独用于不同的深度学习任务中。\n", "\n", "## 小结\n", "\n", "* Transformer是编码器－解码器架构的一个实践，尽管在实际情况中编码器或解码器可以单独使用。\n", "* 在Transformer中，多头自注意力用于表示输入序列和输出序列，不过解码器必须通过掩蔽机制来保留自回归属性。\n", "* Transformer中的残差连接和层规范化是训练非常深度模型的重要工具。\n", "* Transformer模型中基于位置的前馈网络使用同一个多层感知机，作用是对所有序列位置的表示进行转换。\n", "\n", "## 练习\n", "\n", "1. 在实验中训练更深的Transformer将如何影响训练速度和翻译效果？\n", "1. 在Transformer中使用加性注意力取代缩放点积注意力是不是个好办法？为什么？\n", "1. 对于语言模型，应该使用Transformer的编码器还是解码器，或者两者都用？如何设计？\n", "1. 如果输入序列很长，Transformer会面临什么挑战？为什么？\n", "1. 如何提高Transformer的计算速度和内存使用效率？提示：可以参考论文 :cite:`Tay.Dehghani.Bahri.ea.2020`。\n", "1. 如果不使用卷积神经网络，如何设计基于Transformer模型的图像分类任务？提示：可以参考Vision Transformer :cite:`Dosovitskiy.Beyer.Kolesnikov.ea.2021`。\n"]}, {"cell_type": "markdown", "id": "7cb2b452", "metadata": {"origin_pos": 86, "tab": ["pytorch"]}, "source": ["[Discussions](https://discuss.d2l.ai/t/5756)\n"]}], "metadata": {"language_info": {"name": "python"}}, "nbformat": 4, "nbformat_minor": 5}