{"cells": [{"cell_type": "markdown", "id": "44a90018", "metadata": {"origin_pos": 0}, "source": ["# 多输入多输出通道\n", ":label:`sec_channels`\n", "\n", "虽然我们在 :numref:`subsec_why-conv-channels`中描述了构成每个图像的多个通道和多层卷积层。例如彩色图像具有标准的RGB通道来代表红、绿和蓝。\n", "但是到目前为止，我们仅展示了单个输入和单个输出通道的简化例子。\n", "这使得我们可以将输入、卷积核和输出看作二维张量。\n", "\n", "当我们添加通道时，我们的输入和隐藏的表示都变成了三维张量。例如，每个RGB输入图像具有$3\\times h\\times w$的形状。我们将这个大小为$3$的轴称为*通道*（channel）维度。本节将更深入地研究具有多输入和多输出通道的卷积核。\n", "\n", "## 多输入通道\n", "\n", "当输入包含多个通道时，需要构造一个与输入数据具有相同输入通道数的卷积核，以便与输入数据进行互相关运算。假设输入的通道数为$c_i$，那么卷积核的输入通道数也需要为$c_i$。如果卷积核的窗口形状是$k_h\\times k_w$，那么当$c_i=1$时，我们可以把卷积核看作形状为$k_h\\times k_w$的二维张量。\n", "\n", "然而，当$c_i>1$时，我们卷积核的每个输入通道将包含形状为$k_h\\times k_w$的张量。将这些张量$c_i$连结在一起可以得到形状为$c_i\\times k_h\\times k_w$的卷积核。由于输入和卷积核都有$c_i$个通道，我们可以对每个通道输入的二维张量和卷积核的二维张量进行互相关运算，再对通道求和（将$c_i$的结果相加）得到二维张量。这是多通道输入和多输入通道卷积核之间进行二维互相关运算的结果。\n", "\n", "在 :numref:`fig_conv_multi_in`中，我们演示了一个具有两个输入通道的二维互相关运算的示例。阴影部分是第一个输出元素以及用于计算这个输出的输入和核张量元素：$(1\\times1+2\\times2+4\\times3+5\\times4)+(0\\times0+1\\times1+3\\times2+4\\times3)=56$。\n", "\n", "![两个输入通道的互相关计算。](../img/conv-multi-in.svg)\n", ":label:`fig_conv_multi_in`\n", "\n", "为了加深理解，我们(**实现一下多输入通道互相关运算**)。\n", "简而言之，我们所做的就是对每个通道执行互相关操作，然后将结果相加。\n"]}, {"cell_type": "code", "execution_count": 1, "id": "f9ce9950", "metadata": {"execution": {"iopub.execute_input": "2022-12-07T16:46:39.272474Z", "iopub.status.busy": "2022-12-07T16:46:39.272152Z", "iopub.status.idle": "2022-12-07T16:46:41.512405Z", "shell.execute_reply": "2022-12-07T16:46:41.511598Z"}, "origin_pos": 2, "tab": ["pytorch"]}, "outputs": [], "source": ["import torch\n", "from d2l import torch as d2l"]}, {"cell_type": "code", "execution_count": 2, "id": "17b6ac9b", "metadata": {"execution": {"iopub.execute_input": "2022-12-07T16:46:41.516654Z", "iopub.status.busy": "2022-12-07T16:46:41.516082Z", "iopub.status.idle": "2022-12-07T16:46:41.520583Z", "shell.execute_reply": "2022-12-07T16:46:41.519870Z"}, "origin_pos": 4, "tab": ["pytorch"]}, "outputs": [], "source": ["def corr2d_multi_in(X, K):\n", "    # 先遍历“X”和“K”的第0个维度（通道维度），再把它们加在一起\n", "    return sum(d2l.corr2d(x, k) for x, k in zip(X, K))"]}, {"cell_type": "markdown", "id": "b964467f", "metadata": {"origin_pos": 6}, "source": ["我们可以构造与 :numref:`fig_conv_multi_in`中的值相对应的输入张量`X`和核张量`K`，以(**验证互相关运算的输出**)。\n"]}, {"cell_type": "code", "execution_count": 3, "id": "cd65d964", "metadata": {"execution": {"iopub.execute_input": "2022-12-07T16:46:41.523694Z", "iopub.status.busy": "2022-12-07T16:46:41.523242Z", "iopub.status.idle": "2022-12-07T16:46:41.555743Z", "shell.execute_reply": "2022-12-07T16:46:41.554661Z"}, "origin_pos": 7, "tab": ["pytorch"]}, "outputs": [{"data": {"text/plain": ["tensor([[ 56.,  72.],\n", "        [104., 120.]])"]}, "execution_count": 3, "metadata": {}, "output_type": "execute_result"}], "source": ["X = torch.tensor([[[0.0, 1.0, 2.0], [3.0, 4.0, 5.0], [6.0, 7.0, 8.0]],\n", "               [[1.0, 2.0, 3.0], [4.0, 5.0, 6.0], [7.0, 8.0, 9.0]]])\n", "K = torch.tensor([[[0.0, 1.0], [2.0, 3.0]], [[1.0, 2.0], [3.0, 4.0]]])\n", "\n", "corr2d_multi_in(X, K)"]}, {"cell_type": "markdown", "id": "a3f502b5", "metadata": {"origin_pos": 8}, "source": ["## 多输出通道\n", "\n", "到目前为止，不论有多少输入通道，我们还只有一个输出通道。然而，正如我们在 :numref:`subsec_why-conv-channels`中所讨论的，每一层有多个输出通道是至关重要的。在最流行的神经网络架构中，随着神经网络层数的加深，我们常会增加输出通道的维数，通过减少空间分辨率以获得更大的通道深度。直观地说，我们可以将每个通道看作对不同特征的响应。而现实可能更为复杂一些，因为每个通道不是独立学习的，而是为了共同使用而优化的。因此，多输出通道并不仅是学习多个单通道的检测器。\n", "\n", "用$c_i$和$c_o$分别表示输入和输出通道的数目，并让$k_h$和$k_w$为卷积核的高度和宽度。为了获得多个通道的输出，我们可以为每个输出通道创建一个形状为$c_i\\times k_h\\times k_w$的卷积核张量，这样卷积核的形状是$c_o\\times c_i\\times k_h\\times k_w$。在互相关运算中，每个输出通道先获取所有输入通道，再以对应该输出通道的卷积核计算出结果。\n", "\n", "如下所示，我们实现一个[**计算多个通道的输出的互相关函数**]。\n"]}, {"cell_type": "code", "execution_count": 4, "id": "0cd1966b", "metadata": {"execution": {"iopub.execute_input": "2022-12-07T16:46:41.559667Z", "iopub.status.busy": "2022-12-07T16:46:41.559172Z", "iopub.status.idle": "2022-12-07T16:46:41.564193Z", "shell.execute_reply": "2022-12-07T16:46:41.563142Z"}, "origin_pos": 9, "tab": ["pytorch"]}, "outputs": [], "source": ["def corr2d_multi_in_out(X, K):\n", "    # 迭代“K”的第0个维度，每次都对输入“X”执行互相关运算。\n", "    # 最后将所有结果都叠加在一起\n", "    return torch.stack([corr2d_multi_in(X, k) for k in K], 0)"]}, {"cell_type": "markdown", "id": "aa350ab7", "metadata": {"origin_pos": 10}, "source": ["通过将核张量`K`与`K+1`（`K`中每个元素加$1$）和`K+2`连接起来，构造了一个具有$3$个输出通道的卷积核。\n"]}, {"cell_type": "code", "execution_count": 5, "id": "94120658", "metadata": {"execution": {"iopub.execute_input": "2022-12-07T16:46:41.567411Z", "iopub.status.busy": "2022-12-07T16:46:41.566965Z", "iopub.status.idle": "2022-12-07T16:46:41.573775Z", "shell.execute_reply": "2022-12-07T16:46:41.572744Z"}, "origin_pos": 11, "tab": ["pytorch"]}, "outputs": [{"data": {"text/plain": ["torch.Si<PERSON>([3, 2, 2, 2])"]}, "execution_count": 5, "metadata": {}, "output_type": "execute_result"}], "source": ["K = torch.stack((K, K + 1, K + 2), 0)\n", "K.shape"]}, {"cell_type": "markdown", "id": "691ff824", "metadata": {"origin_pos": 12}, "source": ["下面，我们对输入张量`X`与卷积核张量`K`执行互相关运算。现在的输出包含$3$个通道，第一个通道的结果与先前输入张量`X`和多输入单输出通道的结果一致。\n"]}, {"cell_type": "code", "execution_count": 6, "id": "e91e4797", "metadata": {"execution": {"iopub.execute_input": "2022-12-07T16:46:41.577253Z", "iopub.status.busy": "2022-12-07T16:46:41.576737Z", "iopub.status.idle": "2022-12-07T16:46:41.585070Z", "shell.execute_reply": "2022-12-07T16:46:41.584049Z"}, "origin_pos": 13, "tab": ["pytorch"]}, "outputs": [{"data": {"text/plain": ["tensor([[[ 56.,  72.],\n", "         [104., 120.]],\n", "\n", "        [[ 76., 100.],\n", "         [148., 172.]],\n", "\n", "        [[ 96., 128.],\n", "         [192., 224.]]])"]}, "execution_count": 6, "metadata": {}, "output_type": "execute_result"}], "source": ["corr2d_multi_in_out(X, K)"]}, {"cell_type": "markdown", "id": "dec26f46", "metadata": {"origin_pos": 14}, "source": ["## $1\\times 1$ 卷积层\n", "\n", "[~~1x1卷积~~]\n", "\n", "$1 \\times 1$卷积，即$k_h = k_w = 1$，看起来似乎没有多大意义。\n", "毕竟，卷积的本质是有效提取相邻像素间的相关特征，而$1 \\times 1$卷积显然没有此作用。\n", "尽管如此，$1 \\times 1$仍然十分流行，经常包含在复杂深层网络的设计中。下面，让我们详细地解读一下它的实际作用。\n", "\n", "因为使用了最小窗口，$1\\times 1$卷积失去了卷积层的特有能力——在高度和宽度维度上，识别相邻元素间相互作用的能力。\n", "其实$1\\times 1$卷积的唯一计算发生在通道上。\n", "\n", " :numref:`fig_conv_1x1`展示了使用$1\\times 1$卷积核与$3$个输入通道和$2$个输出通道的互相关计算。\n", "这里输入和输出具有相同的高度和宽度，输出中的每个元素都是从输入图像中同一位置的元素的线性组合。\n", "我们可以将$1\\times 1$卷积层看作在每个像素位置应用的全连接层，以$c_i$个输入值转换为$c_o$个输出值。\n", "因为这仍然是一个卷积层，所以跨像素的权重是一致的。\n", "同时，$1\\times 1$卷积层需要的权重维度为$c_o\\times c_i$，再额外加上一个偏置。\n", "\n", "![互相关计算使用了具有3个输入通道和2个输出通道的 $1\\times 1$ 卷积核。其中，输入和输出具有相同的高度和宽度。](../img/conv-1x1.svg)\n", ":label:`fig_conv_1x1`\n", "\n", "下面，我们使用全连接层实现$1 \\times 1$卷积。\n", "请注意，我们需要对输入和输出的数据形状进行调整。\n"]}, {"cell_type": "code", "execution_count": 7, "id": "7ae1f936", "metadata": {"execution": {"iopub.execute_input": "2022-12-07T16:46:41.588625Z", "iopub.status.busy": "2022-12-07T16:46:41.587919Z", "iopub.status.idle": "2022-12-07T16:46:41.593508Z", "shell.execute_reply": "2022-12-07T16:46:41.592461Z"}, "origin_pos": 15, "tab": ["pytorch"]}, "outputs": [], "source": ["def corr2d_multi_in_out_1x1(X, K):\n", "    c_i, h, w = X.shape\n", "    c_o = K.shape[0]\n", "    X = X.reshape((c_i, h * w))\n", "    K = K.reshape((c_o, c_i))\n", "    # 全连接层中的矩阵乘法\n", "    Y = torch.matmul(K, X)\n", "    return Y.reshape((c_o, h, w))"]}, {"cell_type": "markdown", "id": "9656da71", "metadata": {"origin_pos": 16}, "source": ["当执行$1\\times 1$卷积运算时，上述函数相当于先前实现的互相关函数`corr2d_multi_in_out`。让我们用一些样本数据来验证这一点。\n"]}, {"cell_type": "code", "execution_count": 8, "id": "7ed959c6", "metadata": {"execution": {"iopub.execute_input": "2022-12-07T16:46:41.596992Z", "iopub.status.busy": "2022-12-07T16:46:41.596126Z", "iopub.status.idle": "2022-12-07T16:46:41.601323Z", "shell.execute_reply": "2022-12-07T16:46:41.600271Z"}, "origin_pos": 17, "tab": ["pytorch"]}, "outputs": [], "source": ["X = torch.normal(0, 1, (3, 3, 3))\n", "K = torch.normal(0, 1, (2, 3, 1, 1))"]}, {"cell_type": "code", "execution_count": 9, "id": "168724fb", "metadata": {"execution": {"iopub.execute_input": "2022-12-07T16:46:41.605027Z", "iopub.status.busy": "2022-12-07T16:46:41.604179Z", "iopub.status.idle": "2022-12-07T16:46:41.611021Z", "shell.execute_reply": "2022-12-07T16:46:41.609969Z"}, "origin_pos": 19, "tab": ["pytorch"]}, "outputs": [], "source": ["Y1 = corr2d_multi_in_out_1x1(X, K)\n", "Y2 = corr2d_multi_in_out(X, K)\n", "assert float(torch.abs(Y1 - Y2).sum()) < 1e-6"]}, {"cell_type": "markdown", "id": "3aa43632", "metadata": {"origin_pos": 20}, "source": ["## 小结\n", "\n", "* 多输入多输出通道可以用来扩展卷积层的模型。\n", "* 当以每像素为基础应用时，$1\\times 1$卷积层相当于全连接层。\n", "* $1\\times 1$卷积层通常用于调整网络层的通道数量和控制模型复杂性。\n", "\n", "## 练习\n", "\n", "1. 假设我们有两个卷积核，大小分别为$k_1$和$k_2$（中间没有非线性激活函数）。\n", "    1. 证明运算可以用单次卷积来表示。\n", "    1. 这个等效的单个卷积核的维数是多少呢？\n", "    1. 反之亦然吗？\n", "1. 假设输入为$c_i\\times h\\times w$，卷积核大小为$c_o\\times c_i\\times k_h\\times k_w$，填充为$(p_h, p_w)$，步幅为$(s_h, s_w)$。\n", "    1. 前向传播的计算成本（乘法和加法）是多少？\n", "    1. 内存占用是多少？\n", "    1. 反向传播的内存占用是多少？\n", "    1. 反向传播的计算成本是多少？\n", "1. 如果我们将输入通道$c_i$和输出通道$c_o$的数量加倍，计算数量会增加多少？如果我们把填充数量翻一番会怎么样？\n", "1. 如果卷积核的高度和宽度是$k_h=k_w=1$，前向传播的计算复杂度是多少？\n", "1. 本节最后一个示例中的变量`Y1`和`Y2`是否完全相同？为什么？\n", "1. 当卷积窗口不是$1\\times 1$时，如何使用矩阵乘法实现卷积？\n"]}, {"cell_type": "markdown", "id": "d45309b1", "metadata": {"origin_pos": 22, "tab": ["pytorch"]}, "source": ["[Discussions](https://discuss.d2l.ai/t/1854)\n"]}], "metadata": {"language_info": {"name": "python"}}, "nbformat": 4, "nbformat_minor": 5}