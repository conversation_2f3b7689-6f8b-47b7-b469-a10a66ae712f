<?xml version="1.0" encoding="UTF-8"?>
<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="251pt" height="87pt" viewBox="0 0 251 87" version="1.1">
<defs>
<g>
<symbol overflow="visible" id="glyph0-0">
<path style="stroke:none;" d=""/>
</symbol>
<symbol overflow="visible" id="glyph0-1">
<path style="stroke:none;" d="M 4.359375 0 L 4.359375 -0.21875 C 4.21875 -0.265625 4.15625 -0.296875 4.09375 -0.390625 L 2.75 -2.453125 L 3.65625 -3.578125 C 3.828125 -3.78125 4.015625 -3.890625 4.296875 -3.9375 L 4.296875 -4.15625 L 2.78125 -4.15625 L 2.78125 -3.9375 L 2.96875 -3.921875 C 3.171875 -3.890625 3.234375 -3.859375 3.234375 -3.71875 C 3.234375 -3.578125 3.140625 -3.453125 2.890625 -3.140625 L 2.578125 -2.734375 C 2.53125 -2.78125 2.5 -2.828125 2.453125 -2.875 C 2.15625 -3.265625 1.9375 -3.640625 1.9375 -3.765625 C 1.9375 -3.875 2.0625 -3.921875 2.359375 -3.9375 L 2.359375 -4.15625 L 0.109375 -4.15625 L 0.109375 -3.9375 C 0.34375 -3.890625 0.390625 -3.859375 0.578125 -3.578125 L 1.734375 -1.8125 C 1.59375 -1.640625 1.671875 -1.734375 1.53125 -1.5625 L 1.140625 -1.046875 C 0.640625 -0.375 0.453125 -0.234375 0.125 -0.21875 L 0.125 0 L 1.640625 0 L 1.640625 -0.21875 C 1.328125 -0.234375 1.203125 -0.296875 1.203125 -0.4375 C 1.203125 -0.578125 1.421875 -0.9375 1.765625 -1.359375 C 1.828125 -1.421875 1.875 -1.484375 1.921875 -1.53125 C 2.03125 -1.34375 2.15625 -1.15625 2.296875 -0.96875 C 2.515625 -0.65625 2.59375 -0.5 2.59375 -0.390625 C 2.59375 -0.28125 2.46875 -0.234375 2.1875 -0.21875 L 2.1875 0 Z M 4.359375 0 "/>
</symbol>
<symbol overflow="visible" id="glyph1-0">
<path style="stroke:none;" d=""/>
</symbol>
<symbol overflow="visible" id="glyph1-1">
<path style="stroke:none;" d="M 0.375 -3.859375 L 0.3125 -3.5625 L 1.125 -3.5625 L 0.328125 0.21875 C 0.125 1.1875 -0.15625 1.671875 -0.546875 1.671875 C -0.640625 1.671875 -0.71875 1.609375 -0.71875 1.53125 C -0.71875 1.4375 -0.640625 1.390625 -0.640625 1.265625 C -0.640625 1.078125 -0.78125 0.9375 -0.984375 0.9375 C -1.171875 0.9375 -1.328125 1.109375 -1.328125 1.3125 C -1.328125 1.625 -1.015625 1.859375 -0.609375 1.859375 C 0.203125 1.859375 0.8125 0.984375 1.1875 -0.6875 L 1.84375 -3.5625 L 2.8125 -3.5625 L 2.859375 -3.859375 L 1.90625 -3.859375 C 2.15625 -5.28125 2.5 -5.90625 3 -5.90625 C 3.125 -5.90625 3.1875 -5.859375 3.1875 -5.78125 C 3.1875 -5.703125 3.109375 -5.65625 3.109375 -5.515625 C 3.109375 -5.296875 3.28125 -5.15625 3.4375 -5.15625 C 3.640625 -5.15625 3.8125 -5.34375 3.8125 -5.546875 C 3.8125 -5.875 3.484375 -6.109375 3.046875 -6.109375 C 2.5 -6.109375 2.125 -5.796875 1.859375 -5.40625 C 1.5625 -4.984375 1.40625 -4.484375 1.203125 -3.859375 Z M 0.375 -3.859375 "/>
</symbol>
<symbol overflow="visible" id="glyph2-0">
<path style="stroke:none;" d=""/>
</symbol>
<symbol overflow="visible" id="glyph2-1">
<path style="stroke:none;" d="M 2.359375 0 L 2.359375 -0.09375 C 1.90625 -0.09375 1.796875 -0.203125 1.796875 -0.453125 L 1.796875 -4.03125 L 1.734375 -4.0625 L 0.671875 -3.515625 L 0.671875 -3.421875 L 0.828125 -3.484375 C 0.9375 -3.53125 1.03125 -3.5625 1.09375 -3.5625 C 1.21875 -3.5625 1.28125 -3.46875 1.28125 -3.265625 L 1.28125 -0.5625 C 1.28125 -0.234375 1.15625 -0.109375 0.703125 -0.09375 L 0.703125 0 Z M 2.359375 0 "/>
</symbol>
<symbol overflow="visible" id="glyph2-2">
<path style="stroke:none;" d="M 2.84375 -0.828125 L 2.765625 -0.859375 C 2.5625 -0.515625 2.4375 -0.453125 2.1875 -0.453125 L 0.78125 -0.453125 L 1.765625 -1.515625 C 2.296875 -2.078125 2.53125 -2.53125 2.53125 -3 C 2.53125 -3.59375 2.109375 -4.0625 1.421875 -4.0625 C 0.6875 -4.0625 0.3125 -3.5625 0.1875 -2.859375 L 0.3125 -2.828125 C 0.546875 -3.421875 0.75 -3.609375 1.1875 -3.609375 C 1.703125 -3.609375 2.015625 -3.3125 2.015625 -2.765625 C 2.015625 -2.25 1.8125 -1.796875 1.234375 -1.203125 L 0.171875 -0.078125 L 0.171875 0 L 2.515625 0 Z M 2.84375 -0.828125 "/>
</symbol>
<symbol overflow="visible" id="glyph2-3">
<path style="stroke:none;" d="M 0.359375 -3.0625 C 0.609375 -3.5 0.890625 -3.703125 1.265625 -3.703125 C 1.65625 -3.703125 1.90625 -3.484375 1.90625 -3.078125 C 1.90625 -2.71875 1.71875 -2.453125 1.421875 -2.28125 C 1.296875 -2.203125 1.140625 -2.140625 0.921875 -2.0625 L 0.921875 -1.984375 C 1.265625 -1.984375 1.390625 -1.96875 1.53125 -1.921875 C 1.9375 -1.796875 2.15625 -1.5 2.15625 -1.046875 C 2.15625 -0.546875 1.8125 -0.125 1.375 -0.125 C 1.203125 -0.125 1.078125 -0.15625 0.859375 -0.3125 C 0.6875 -0.4375 0.59375 -0.46875 0.484375 -0.46875 C 0.359375 -0.46875 0.25 -0.390625 0.25 -0.265625 C 0.25 -0.046875 0.484375 0.078125 0.921875 0.078125 C 1.453125 0.078125 2.015625 -0.09375 2.3125 -0.46875 C 2.484375 -0.703125 2.59375 -1 2.59375 -1.3125 C 2.59375 -1.625 2.484375 -1.90625 2.328125 -2.09375 C 2.203125 -2.21875 2.09375 -2.296875 1.828125 -2.40625 C 2.21875 -2.640625 2.375 -2.953125 2.375 -3.234375 C 2.375 -3.71875 2 -4.0625 1.453125 -4.0625 C 0.828125 -4.0625 0.4375 -3.65625 0.265625 -3.078125 Z M 0.359375 -3.0625 "/>
</symbol>
<symbol overflow="visible" id="glyph2-4">
<path style="stroke:none;" d="M 2.84375 -1 L 2.84375 -1.390625 L 2.21875 -1.390625 L 2.21875 -4.0625 L 1.953125 -4.0625 L 0.078125 -1.390625 L 0.078125 -1 L 1.75 -1 L 1.75 0 L 2.21875 0 L 2.21875 -1 Z M 1.75 -1.390625 L 0.3125 -1.390625 L 1.75 -3.4375 Z M 1.75 -1.390625 "/>
</symbol>
<symbol overflow="visible" id="glyph3-0">
<path style="stroke:none;" d=""/>
</symbol>
<symbol overflow="visible" id="glyph3-1">
<path style="stroke:none;" d="M 2.734375 1.453125 C 1.46875 0.390625 1.203125 -0.625 1.203125 -2.296875 C 1.203125 -4.03125 1.484375 -4.875 2.734375 -5.9375 L 2.65625 -6.078125 C 1.21875 -5.234375 0.4375 -3.9375 0.4375 -2.265625 C 0.4375 -0.734375 1.203125 0.78125 2.625 1.59375 Z M 2.734375 1.453125 "/>
</symbol>
<symbol overflow="visible" id="glyph3-2">
<path style="stroke:none;" d="M 0.265625 -5.9375 C 1.5625 -4.90625 1.796875 -3.875 1.796875 -2.203125 C 1.796875 -0.453125 1.53125 0.390625 0.265625 1.453125 L 0.34375 1.59375 C 1.765625 0.71875 2.5625 -0.5625 2.5625 -2.21875 C 2.5625 -3.75 1.75 -5.25 0.375 -6.078125 Z M 0.265625 -5.9375 "/>
</symbol>
<symbol overflow="visible" id="glyph4-0">
<path style="stroke:none;" d="M 1.125 0 L 1.125 -5.625 L 5.625 -5.625 L 5.625 0 Z M 1.265625 -0.140625 L 5.484375 -0.140625 L 5.484375 -5.484375 L 1.265625 -5.484375 Z M 1.265625 -0.140625 "/>
</symbol>
<symbol overflow="visible" id="glyph4-1">
<path style="stroke:none;" d="M 1.046875 0 L 1.046875 -0.90625 L 1.953125 -0.90625 L 1.953125 0 Z M 4.046875 0 L 4.046875 -0.90625 L 4.953125 -0.90625 L 4.953125 0 Z M 7.046875 0 L 7.046875 -0.90625 L 7.953125 -0.90625 L 7.953125 0 Z M 7.046875 0 "/>
</symbol>
</g>
</defs>
<g id="surface1">
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 30 30 L 60 30 L 60 60 L 30 60 Z M 30 30 " transform="matrix(1,0,0,1,-29,26)"/>
<g style="fill:rgb(0%,0%,0%);fill-opacity:1;">
  <use xlink:href="#glyph0-1" x="13.75" y="73.5"/>
</g>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 80 30 L 110 30 L 110 60 L 80 60 Z M 80 30 " transform="matrix(1,0,0,1,-29,26)"/>
<g style="fill:rgb(0%,0%,0%);fill-opacity:1;">
  <use xlink:href="#glyph1-1" x="58.002" y="72.5"/>
</g>
<g style="fill:rgb(0%,0%,0%);fill-opacity:1;">
  <use xlink:href="#glyph2-1" x="60.504" y="75.5"/>
</g>
<g style="fill:rgb(0%,0%,0%);fill-opacity:1;">
  <use xlink:href="#glyph3-1" x="63.504" y="72.5"/>
</g>
<g style="fill:rgb(0%,0%,0%);fill-opacity:1;">
  <use xlink:href="#glyph0-1" x="66.501" y="72.5"/>
</g>
<g style="fill:rgb(0%,0%,0%);fill-opacity:1;">
  <use xlink:href="#glyph3-2" x="71.001" y="72.5"/>
</g>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 130 30 L 160 30 L 160 60 L 130 60 Z M 130 30 " transform="matrix(1,0,0,1,-29,26)"/>
<g style="fill:rgb(0%,0%,0%);fill-opacity:1;">
  <use xlink:href="#glyph1-1" x="108.002" y="72.5"/>
</g>
<g style="fill:rgb(0%,0%,0%);fill-opacity:1;">
  <use xlink:href="#glyph2-2" x="110.504" y="75.5"/>
</g>
<g style="fill:rgb(0%,0%,0%);fill-opacity:1;">
  <use xlink:href="#glyph3-1" x="113.504" y="72.5"/>
</g>
<g style="fill:rgb(0%,0%,0%);fill-opacity:1;">
  <use xlink:href="#glyph0-1" x="116.501" y="72.5"/>
</g>
<g style="fill:rgb(0%,0%,0%);fill-opacity:1;">
  <use xlink:href="#glyph3-2" x="121.001" y="72.5"/>
</g>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 179 30 L 209 30 L 209 60 L 179 60 Z M 179 30 " transform="matrix(1,0,0,1,-29,26)"/>
<g style="fill:rgb(0%,0%,0%);fill-opacity:1;">
  <use xlink:href="#glyph1-1" x="157.002" y="72.5"/>
</g>
<g style="fill:rgb(0%,0%,0%);fill-opacity:1;">
  <use xlink:href="#glyph2-3" x="159.504" y="75.5"/>
</g>
<g style="fill:rgb(0%,0%,0%);fill-opacity:1;">
  <use xlink:href="#glyph3-1" x="162.504" y="72.5"/>
</g>
<g style="fill:rgb(0%,0%,0%);fill-opacity:1;">
  <use xlink:href="#glyph0-1" x="165.501" y="72.5"/>
</g>
<g style="fill:rgb(0%,0%,0%);fill-opacity:1;">
  <use xlink:href="#glyph3-2" x="170.001" y="72.5"/>
</g>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 228 30 L 258 30 L 258 60 L 228 60 Z M 228 30 " transform="matrix(1,0,0,1,-29,26)"/>
<g style="fill:rgb(0%,0%,0%);fill-opacity:1;">
  <use xlink:href="#glyph1-1" x="206.002" y="72.5"/>
</g>
<g style="fill:rgb(0%,0%,0%);fill-opacity:1;">
  <use xlink:href="#glyph2-4" x="208.504" y="75.5"/>
</g>
<g style="fill:rgb(0%,0%,0%);fill-opacity:1;">
  <use xlink:href="#glyph3-1" x="211.504" y="72.5"/>
</g>
<g style="fill:rgb(0%,0%,0%);fill-opacity:1;">
  <use xlink:href="#glyph0-1" x="214.501" y="72.5"/>
</g>
<g style="fill:rgb(0%,0%,0%);fill-opacity:1;">
  <use xlink:href="#glyph3-2" x="219.001" y="72.5"/>
</g>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:miter;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 60 45 L 74.101562 45 " transform="matrix(1,0,0,1,-29,26)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:butt;stroke-linejoin:miter;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 78.101562 45 L 74.101562 45 M 74.101562 43.5 L 78.101562 45 L 74.101562 46.5 " transform="matrix(1,0,0,1,-29,26)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:miter;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 110 45 L 124.101562 45 " transform="matrix(1,0,0,1,-29,26)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:butt;stroke-linejoin:miter;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 128.101562 45 L 124.101562 45 M 124.101562 43.5 L 128.101562 45 L 124.101562 46.5 " transform="matrix(1,0,0,1,-29,26)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:miter;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 160 45 L 173.101562 45 " transform="matrix(1,0,0,1,-29,26)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:butt;stroke-linejoin:miter;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 177.101562 45 L 173.101562 45 M 173.101562 43.5 L 177.101562 45 L 173.101562 46.5 " transform="matrix(1,0,0,1,-29,26)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:miter;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 209 45 L 222.101562 45 " transform="matrix(1,0,0,1,-29,26)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:butt;stroke-linejoin:miter;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 226.101562 45 L 222.101562 45 M 222.101562 43.5 L 226.101562 45 L 222.101562 46.5 " transform="matrix(1,0,0,1,-29,26)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:miter;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 60 36.898438 L 94.96875 18.011719 C 96.847656 17 99.121094 17.058594 100.941406 18.167969 L 124.960938 32.796875 " transform="matrix(1,0,0,1,-29,26)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:butt;stroke-linejoin:miter;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 128.378906 34.875 L 124.960938 32.796875 M 125.742188 31.515625 L 128.378906 34.875 L 124.179688 34.078125 " transform="matrix(1,0,0,1,-29,26)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:miter;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 110 36.914062 L 135.996094 22.898438 M 146.335938 20.816406 L 173.738281 34.71875 " transform="matrix(1,0,0,1,-29,26)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:butt;stroke-linejoin:miter;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 177.304688 36.53125 L 173.738281 34.71875 M 174.417969 33.382812 L 177.304688 36.53125 L 173.058594 36.058594 " transform="matrix(1,0,0,1,-29,26)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:miter;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 160 37.113281 L 168.78125 32.496094 M 178.515625 27.375 L 190.5 21.070312 C 192.222656 20.164062 194.28125 20.152344 196.019531 21.035156 L 222.746094 34.667969 " transform="matrix(1,0,0,1,-29,26)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:butt;stroke-linejoin:miter;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 226.308594 36.484375 L 222.746094 34.667969 M 223.425781 33.332031 L 226.308594 36.484375 L 222.0625 36.003906 " transform="matrix(1,0,0,1,-29,26)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:miter;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 60 36.640625 L 126.621094 -0.492188 C 128.675781 -1.636719 131.214844 -1.472656 133.109375 -0.0742188 L 147.90625 10.878906 M 156.746094 17.425781 L 174.257812 30.386719 " transform="matrix(1,0,0,1,-29,26)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:butt;stroke-linejoin:miter;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 177.472656 32.765625 L 174.257812 30.386719 M 175.148438 29.179688 L 177.472656 32.765625 L 173.367188 31.59375 " transform="matrix(1,0,0,1,-29,26)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:miter;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 110 36.929688 L 170.714844 4.257812 C 172.59375 3.246094 174.871094 3.308594 176.691406 4.425781 L 222.96875 32.742188 " transform="matrix(1,0,0,1,-29,26)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:butt;stroke-linejoin:miter;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 226.378906 34.828125 L 222.96875 32.742188 M 223.75 31.460938 L 226.378906 34.828125 L 222.183594 34.019531 " transform="matrix(1,0,0,1,-29,26)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:miter;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 60 36.605469 L 152.679688 -15.265625 C 154.707031 -16.402344 157.207031 -16.261719 159.097656 -14.914062 L 223.199219 30.859375 " transform="matrix(1,0,0,1,-29,26)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:butt;stroke-linejoin:miter;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 226.453125 33.183594 L 223.199219 30.859375 M 224.070312 29.640625 L 226.453125 33.183594 L 222.328125 32.082031 " transform="matrix(1,0,0,1,-29,26)"/>
<g style="fill:rgb(0%,0%,0%);fill-opacity:1;">
  <use xlink:href="#glyph4-1" x="236.8" y="71.4"/>
</g>
</g>
</svg>
