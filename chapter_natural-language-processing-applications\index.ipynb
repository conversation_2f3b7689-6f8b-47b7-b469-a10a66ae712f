{"cells": [{"cell_type": "markdown", "id": "1ae31836", "metadata": {"origin_pos": 0}, "source": ["# 自然语言处理：应用\n", ":label:`chap_nlp_app`\n", "\n", "前面我们学习了如何在文本序列中表示词元，\n", "并在 :numref:`chap_nlp_pretrain`中训练了词元的表示。\n", "这样的预训练文本表示可以通过不同模型架构，放入不同的下游自然语言处理任务。\n", "\n", "前一章我们提及到一些自然语言处理应用，这些应用没有预训练，只是为了解释深度学习架构。\n", "例如，在 :numref:`chap_rnn`中，\n", "我们依赖循环神经网络设计语言模型来生成类似中篇小说的文本。\n", "在 :numref:`chap_modern_rnn`和 :numref:`chap_attention`中，\n", "我们还设计了基于循环神经网络和注意力机制的机器翻译模型。\n", "\n", "然而，本书并不打算全面涵盖所有此类应用。\n", "相反，我们的重点是*如何应用深度语言表征学习来解决自然语言处理问题*。\n", "在给定预训练的文本表示的情况下，\n", "本章将探讨两种流行且具有代表性的下游自然语言处理任务：\n", "情感分析和自然语言推断，它们分别分析单个文本和文本对之间的关系。\n", "\n", "![预训练文本表示可以通过不同模型架构，放入不同的下游自然语言处理应用（本章重点介绍如何为不同的下游应用设计模型）](../img/nlp-map-app.svg)\n", ":label:`fig_nlp-map-app`\n", "\n", "如 :numref:`fig_nlp-map-app`所述，\n", "本章将重点描述然后使用不同类型的深度学习架构\n", "（如多层感知机、卷积神经网络、循环神经网络和注意力）\n", "设计自然语言处理模型。\n", "尽管在 :numref:`fig_nlp-map-app`中，\n", "可以将任何预训练的文本表示与任何应用的架构相结合，\n", "但我们选择了一些具有代表性的组合。\n", "具体来说，我们将探索基于循环神经网络和卷积神经网络的流行架构进行情感分析。\n", "对于自然语言推断，我们选择注意力和多层感知机来演示如何分析文本对。\n", "最后，我们介绍了如何为广泛的自然语言处理应用，\n", "如在序列级（单文本分类和文本对分类）和词元级（文本标注和问答）上\n", "对预训练BERT模型进行微调。\n", "作为一个具体的经验案例，我们将针对自然语言推断对BERT进行微调。\n", "\n", "正如我们在 :numref:`sec_bert`中介绍的那样，\n", "对于广泛的自然语言处理应用，BERT只需要最少的架构更改。\n", "然而，这一好处是以微调下游应用的大量BERT参数为代价的。\n", "当空间或时间有限时，基于多层感知机、卷积神经网络、循环神经网络\n", "和注意力的精心构建的模型更具可行性。\n", "下面，我们从情感分析应用开始，分别解读基于循环神经网络和卷积神经网络的模型设计。\n", "\n", ":begin_tab:toc\n", " - [sentiment-analysis-and-dataset](sentiment-analysis-and-dataset.ipynb)\n", " - [sentiment-analysis-rnn](sentiment-analysis-rnn.ipynb)\n", " - [sentiment-analysis-cnn](sentiment-analysis-cnn.ipynb)\n", " - [natural-language-inference-and-dataset](natural-language-inference-and-dataset.ipynb)\n", " - [natural-language-inference-attention](natural-language-inference-attention.ipynb)\n", " - [finetuning-bert](finetuning-bert.ipynb)\n", " - [natural-language-inference-bert](natural-language-inference-bert.ipynb)\n", ":end_tab:\n"]}], "metadata": {"language_info": {"name": "python"}}, "nbformat": 4, "nbformat_minor": 5}