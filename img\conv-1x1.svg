<?xml version="1.0" encoding="UTF-8"?>
<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="270pt" height="101pt" viewBox="0 0 270 101" version="1.1">
<defs>
<g>
<symbol overflow="visible" id="glyph0-0">
<path style="stroke:none;" d=""/>
</symbol>
<symbol overflow="visible" id="glyph0-1">
<path style="stroke:none;" d="M 2.0625 -4.84375 L 2.0625 -3.515625 L 1.171875 -3.515625 C 1.390625 -4.09375 1.609375 -4.75 1.796875 -5.484375 L 3.53125 -5.484375 L 3.53125 -6.09375 L 1.953125 -6.09375 C 2.03125 -6.453125 2.109375 -6.84375 2.203125 -7.25 L 1.609375 -7.375 C 1.515625 -6.9375 1.4375 -6.5 1.34375 -6.09375 L 0.453125 -6.09375 L 0.453125 -5.484375 L 1.203125 -5.484375 C 1 -4.6875 0.78125 -4 0.515625 -3.46875 L 0.65625 -2.921875 L 2.0625 -2.921875 L 2.0625 -1.578125 C 1.53125 -1.484375 0.984375 -1.390625 0.390625 -1.328125 L 0.46875 -0.6875 C 1.015625 -0.78125 1.546875 -0.859375 2.0625 -0.96875 L 2.0625 0.90625 L 2.65625 0.90625 L 2.65625 -1.09375 L 3.515625 -1.3125 L 3.515625 -1.921875 C 3.25 -1.859375 2.96875 -1.78125 2.65625 -1.703125 L 2.65625 -2.921875 L 3.484375 -2.921875 L 3.484375 -3.515625 L 2.65625 -3.515625 L 2.65625 -4.84375 Z M 4.71875 -5.3125 L 4.71875 -4.78125 L 7.296875 -4.78125 L 7.296875 -5.3125 L 4.71875 -5.3125 C 5.171875 -5.75 5.609375 -6.234375 6.03125 -6.75 C 6.71875 -5.875 7.484375 -5.125 8.328125 -4.515625 L 8.671875 -5.015625 C 7.859375 -5.59375 7.0625 -6.359375 6.265625 -7.328125 L 5.8125 -7.328125 C 5.078125 -6.4375 4.25 -5.640625 3.328125 -4.96875 L 3.6875 -4.421875 C 4.03125 -4.703125 4.375 -5 4.71875 -5.3125 Z M 5.34375 0.84375 C 5.78125 0.84375 6.015625 0.609375 6.015625 0.140625 L 6.015625 -4.0625 L 3.890625 -4.0625 L 3.890625 0.90625 L 4.453125 0.90625 L 4.453125 -0.78125 L 5.46875 -0.78125 L 5.46875 0.03125 C 5.46875 0.21875 5.359375 0.328125 5.171875 0.328125 L 4.765625 0.3125 L 4.90625 0.84375 Z M 4.453125 -1.28125 L 4.453125 -2.140625 L 5.46875 -2.140625 L 5.46875 -1.28125 Z M 4.453125 -2.640625 L 4.453125 -3.53125 L 5.46875 -3.53125 L 5.46875 -2.640625 Z M 6.546875 -3.8125 L 6.546875 -0.578125 L 7.0625 -0.578125 L 7.0625 -3.8125 Z M 7.53125 0.859375 C 7.96875 0.859375 8.1875 0.59375 8.1875 0.078125 L 8.1875 -4.21875 L 7.625 -4.21875 L 7.625 -0.0625 C 7.625 0.1875 7.53125 0.328125 7.328125 0.328125 C 7.109375 0.328125 6.875 0.3125 6.640625 0.28125 L 6.765625 0.859375 Z M 7.53125 0.859375 "/>
</symbol>
<symbol overflow="visible" id="glyph0-2">
<path style="stroke:none;" d="M 6.9375 -4.21875 L 4.828125 -4.21875 L 4.828125 -7.359375 L 4.171875 -7.359375 L 4.171875 -4.21875 L 2.0625 -4.21875 L 2.0625 -6.671875 L 1.40625 -6.671875 L 1.40625 -3.59375 L 4.171875 -3.59375 L 4.171875 -0.28125 L 1.625 -0.28125 L 1.625 -2.75 L 0.96875 -2.75 L 0.96875 0.84375 L 1.625 0.84375 L 1.625 0.328125 L 7.375 0.328125 L 7.375 0.84375 L 8.03125 0.84375 L 8.03125 -2.75 L 7.375 -2.75 L 7.375 -0.28125 L 4.828125 -0.28125 L 4.828125 -3.59375 L 7.59375 -3.59375 L 7.59375 -6.671875 L 6.9375 -6.671875 Z M 6.9375 -4.21875 "/>
</symbol>
<symbol overflow="visible" id="glyph0-3">
<path style="stroke:none;" d="M 2.34375 -3.6875 C 2.625 -3.296875 2.9375 -2.78125 3.296875 -2.15625 L 3.65625 -2.703125 C 3.21875 -3.328125 2.78125 -3.890625 2.34375 -4.390625 L 2.34375 -4.96875 L 3.40625 -4.96875 L 3.40625 -5.59375 L 2.34375 -5.59375 L 2.34375 -7.34375 L 1.734375 -7.34375 L 1.734375 -5.59375 L 0.484375 -5.59375 L 0.484375 -4.96875 L 1.703125 -4.96875 C 1.4375 -3.78125 0.96875 -2.71875 0.328125 -1.78125 L 0.609375 -1.125 C 1.0625 -1.859375 1.4375 -2.6875 1.734375 -3.609375 L 1.734375 0.90625 L 2.34375 0.90625 Z M 3.640625 -6.359375 L 3.640625 -5.78125 L 5.375 -5.78125 C 4.8125 -4.796875 4.34375 -4.1875 3.984375 -3.953125 C 3.953125 -3.9375 3.921875 -3.921875 3.859375 -3.90625 L 4 -3.375 C 4.703125 -3.40625 5.375 -3.4375 6.015625 -3.46875 C 5.28125 -2.640625 4.359375 -2.046875 3.28125 -1.65625 L 3.625 -1.109375 C 5.375 -1.765625 6.6875 -2.984375 7.59375 -4.765625 L 7.0625 -5.046875 C 6.890625 -4.6875 6.671875 -4.359375 6.4375 -4.03125 C 5.890625 -3.984375 5.3125 -3.953125 4.75 -3.921875 C 5.125 -4.28125 5.546875 -4.90625 6.046875 -5.78125 L 8.515625 -5.78125 L 8.515625 -6.359375 L 6.515625 -6.359375 C 6.390625 -6.734375 6.25 -7.078125 6.125 -7.359375 L 5.453125 -7.25 C 5.609375 -6.984375 5.765625 -6.6875 5.890625 -6.359375 Z M 6.515625 -0.921875 C 7.1875 -0.296875 7.71875 0.296875 8.109375 0.859375 L 8.578125 0.390625 C 8.125 -0.1875 7.5625 -0.75 6.921875 -1.328125 C 7.5 -1.921875 8.015625 -2.625 8.453125 -3.421875 L 7.90625 -3.71875 C 6.84375 -1.75 5.265625 -0.40625 3.15625 0.28125 L 3.5 0.84375 C 4.65625 0.4375 5.65625 -0.15625 6.515625 -0.921875 Z M 6.515625 -0.921875 "/>
</symbol>
<symbol overflow="visible" id="glyph0-4">
<path style="stroke:none;" d="M 1.90625 -1.09375 C 2.765625 -1.640625 3.578125 -2.28125 4.34375 -3.015625 L 4.34375 -1.625 C 4.34375 -1.328125 4.21875 -1.1875 3.96875 -1.1875 C 3.703125 -1.1875 3.453125 -1.203125 3.21875 -1.203125 L 3.390625 -0.609375 L 4.203125 -0.609375 C 4.6875 -0.609375 4.9375 -0.875 4.9375 -1.390625 L 4.9375 -2.90625 C 5.484375 -2.484375 6.15625 -1.90625 6.9375 -1.140625 L 7.3125 -1.703125 C 6.5 -2.40625 5.71875 -3.015625 4.9375 -3.5625 L 4.9375 -4.9375 C 5.828125 -5.421875 6.625 -5.90625 7.34375 -6.40625 L 7.34375 -7.015625 L 1.765625 -7.015625 L 1.765625 -6.40625 L 6.375 -6.40625 C 5.921875 -6.109375 5.25 -5.734375 4.34375 -5.296875 L 4.34375 -3.75 C 3.484375 -2.9375 2.609375 -2.265625 1.671875 -1.734375 Z M 7.5 -5.375 L 7.5 -0.1875 L 1.5 -0.1875 L 1.5 -5.375 L 0.875 -5.375 L 0.875 0.90625 L 1.5 0.90625 L 1.5 0.40625 L 7.5 0.40625 L 7.5 0.921875 L 8.125 0.921875 L 8.125 -5.375 Z M 2.46875 -5.09375 L 2 -4.796875 C 2.515625 -4.296875 2.9375 -3.859375 3.25 -3.4375 L 3.75 -3.78125 C 3.453125 -4.15625 3.03125 -4.59375 2.46875 -5.09375 Z M 6.5625 -5.15625 C 6.234375 -4.609375 5.8125 -4.125 5.34375 -3.734375 L 5.8125 -3.40625 C 6.296875 -3.828125 6.734375 -4.328125 7.09375 -4.90625 Z M 6.5625 -5.15625 "/>
</symbol>
<symbol overflow="visible" id="glyph0-5">
<path style="stroke:none;" d="M 5.921875 -5.359375 L 7.25 -5.359375 C 7.21875 -4.140625 7.03125 -3.078125 6.671875 -2.171875 C 6.3125 -2.921875 6.03125 -3.828125 5.8125 -4.890625 Z M 6.375 -1.515625 C 5.96875 -0.75 5.421875 -0.140625 4.71875 0.34375 L 5.046875 0.890625 C 5.71875 0.40625 6.28125 -0.203125 6.71875 -0.921875 C 7.15625 -0.15625 7.703125 0.453125 8.328125 0.90625 L 8.6875 0.375 C 8.03125 -0.078125 7.484375 -0.6875 7.015625 -1.5 C 7.53125 -2.5625 7.796875 -3.859375 7.84375 -5.359375 L 8.4375 -5.359375 L 8.4375 -5.96875 L 6.0625 -5.96875 C 6.125 -6.375 6.203125 -6.796875 6.25 -7.265625 L 5.625 -7.375 C 5.4375 -5.65625 5.078125 -4.296875 4.546875 -3.296875 L 4.953125 -2.796875 C 5.140625 -3.125 5.328125 -3.484375 5.484375 -3.90625 C 5.734375 -3 6.03125 -2.21875 6.375 -1.515625 Z M 2.109375 -3.171875 C 2.046875 -2.984375 1.984375 -2.796875 1.90625 -2.59375 L 0.578125 -2.59375 L 0.578125 -2.03125 L 1.640625 -2.03125 C 1.46875 -1.671875 1.265625 -1.3125 1.03125 -0.90625 C 1.46875 -0.75 1.875 -0.578125 2.28125 -0.40625 C 1.75 -0.109375 1.109375 0.140625 0.375 0.328125 L 0.6875 0.875 C 1.578125 0.625 2.328125 0.28125 2.921875 -0.125 C 3.453125 0.109375 3.921875 0.34375 4.3125 0.578125 L 4.65625 0.078125 C 4.265625 -0.140625 3.859375 -0.328125 3.421875 -0.53125 C 3.890625 -0.96875 4.21875 -1.46875 4.421875 -2.0625 L 4.421875 -2.59375 L 2.53125 -2.59375 C 2.59375 -2.765625 2.65625 -2.921875 2.71875 -3.09375 Z M 2.828125 -0.78125 C 2.5 -0.921875 2.15625 -1.046875 1.796875 -1.171875 C 1.984375 -1.4375 2.140625 -1.734375 2.28125 -2.03125 L 3.828125 -2.03125 C 3.625 -1.5625 3.28125 -1.140625 2.828125 -0.78125 Z M 1.296875 -7.1875 L 0.78125 -7.015625 C 1 -6.671875 1.203125 -6.28125 1.390625 -5.84375 L 1.859375 -6.0625 C 1.6875 -6.453125 1.515625 -6.828125 1.296875 -7.1875 Z M 4.03125 -7.203125 C 3.875 -6.828125 3.640625 -6.453125 3.34375 -6.0625 L 3.8125 -5.828125 C 4.09375 -6.203125 4.3125 -6.59375 4.5 -7 Z M 0.609375 -5.625 L 0.609375 -5.0625 L 1.875 -5.0625 C 1.609375 -4.5625 1.125 -4.109375 0.4375 -3.703125 L 0.75 -3.1875 C 1.515625 -3.703125 2.0625 -4.3125 2.359375 -5.03125 L 2.359375 -3.328125 L 2.96875 -3.328125 L 2.96875 -4.484375 C 3.28125 -4.28125 3.671875 -3.984375 4.125 -3.625 L 4.453125 -4.125 C 3.953125 -4.4375 3.453125 -4.71875 2.96875 -4.953125 L 2.96875 -5.0625 L 4.59375 -5.0625 L 4.59375 -5.625 L 2.96875 -5.625 L 2.96875 -7.34375 L 2.359375 -7.34375 L 2.359375 -5.625 Z M 0.609375 -5.625 "/>
</symbol>
<symbol overflow="visible" id="glyph0-6">
<path style="stroke:none;" d="M 4.109375 -5.265625 C 3.53125 -2.875 2.28125 -1.03125 0.34375 0.21875 L 0.734375 0.78125 C 2.625 -0.453125 3.875 -2.21875 4.46875 -4.515625 C 4.515625 -4.40625 4.546875 -4.28125 4.609375 -4.125 C 5.53125 -1.75 6.71875 -0.109375 8.1875 0.765625 L 8.59375 0.25 C 7.15625 -0.640625 6.03125 -2.171875 5.1875 -4.3125 C 4.640625 -5.78125 3.953125 -6.796875 3.15625 -7.359375 L 2.625 -7.015625 C 3.1875 -6.59375 3.6875 -6.015625 4.109375 -5.265625 Z M 4.109375 -5.265625 "/>
</symbol>
<symbol overflow="visible" id="glyph1-0">
<path style="stroke:none;" d="M 2.25 0 L 2.25 -11.25 L 11.25 -11.25 L 11.25 0 Z M 2.53125 -0.28125 L 10.96875 -0.28125 L 10.96875 -10.96875 L 2.53125 -10.96875 Z M 2.53125 -0.28125 "/>
</symbol>
<symbol overflow="visible" id="glyph1-1">
<path style="stroke:none;" d="M 0.5625 -10.515625 L 0.96875 -11.765625 C 1.894531 -11.441406 2.570312 -11.160156 3 -10.921875 C 2.882812 -11.972656 2.828125 -12.703125 2.828125 -13.109375 L 4.09375 -13.109375 C 4.070312 -12.523438 4.003906 -11.796875 3.890625 -10.921875 C 4.492188 -11.222656 5.1875 -11.503906 5.96875 -11.765625 L 6.375 -10.515625 C 5.625 -10.273438 4.894531 -10.113281 4.1875 -10.03125 C 4.539062 -9.71875 5.046875 -9.164062 5.703125 -8.375 L 4.640625 -7.625 C 4.296875 -8.082031 3.894531 -8.710938 3.4375 -9.515625 C 3 -8.679688 2.617188 -8.050781 2.296875 -7.625 L 1.25 -8.375 C 1.9375 -9.207031 2.425781 -9.757812 2.71875 -10.03125 C 1.957031 -10.175781 1.238281 -10.335938 0.5625 -10.515625 Z M 0.5625 -10.515625 "/>
</symbol>
<symbol overflow="visible" id="glyph1-2">
<path style="stroke:none;" d="M 9.515625 -7.578125 L 1 -7.578125 L 1 -9.046875 L 9.515625 -9.046875 Z M 9.515625 -3.671875 L 1 -3.671875 L 1 -5.140625 L 9.515625 -5.140625 Z M 9.515625 -3.671875 "/>
</symbol>
</g>
</defs>
<g id="surface1">
<path style=" stroke:none;fill-rule:nonzero;fill:rgb(39.99939%,74.902344%,100%);fill-opacity:1;" d="M 55 82 L 73 82 L 73 64 L 55 64 Z M 55 82 "/>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 63 108 L 81 108 L 81 126 L 63 126 Z M 63 108 " transform="matrix(1,0,0,1,-8,-44)"/>
<path style=" stroke:none;fill-rule:nonzero;fill:rgb(100%,100%,100%);fill-opacity:1;" d="M 55 46 L 73 46 L 73 28 L 55 28 Z M 55 46 "/>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 63 72 L 81 72 L 81 90 L 63 90 Z M 63 72 " transform="matrix(1,0,0,1,-8,-44)"/>
<path style=" stroke:none;fill-rule:nonzero;fill:rgb(100%,100%,100%);fill-opacity:1;" d="M 55 64 L 73 64 L 73 46 L 55 46 Z M 55 64 "/>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 63 90 L 81 90 L 81 108 L 63 108 Z M 63 90 " transform="matrix(1,0,0,1,-8,-44)"/>
<path style=" stroke:none;fill-rule:nonzero;fill:rgb(100%,100%,100%);fill-opacity:1;" d="M 19 82 L 37 82 L 37 64 L 19 64 Z M 19 82 "/>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 27 108 L 45 108 L 45 126 L 27 126 Z M 27 108 " transform="matrix(1,0,0,1,-8,-44)"/>
<path style=" stroke:none;fill-rule:nonzero;fill:rgb(100%,100%,100%);fill-opacity:1;" d="M 37 82 L 55 82 L 55 64 L 37 64 Z M 37 82 "/>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 45 108 L 63 108 L 63 126 L 45 126 Z M 45 108 " transform="matrix(1,0,0,1,-8,-44)"/>
<path style=" stroke:none;fill-rule:nonzero;fill:rgb(69.804382%,85.098267%,100%);fill-opacity:1;" d="M 19 46 L 37 46 L 37 28 L 19 28 Z M 19 46 "/>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 27 72 L 45 72 L 45 90 L 27 90 Z M 27 72 " transform="matrix(1,0,0,1,-8,-44)"/>
<path style=" stroke:none;fill-rule:nonzero;fill:rgb(100%,100%,100%);fill-opacity:1;" d="M 19 64 L 37 64 L 37 46 L 19 46 Z M 19 64 "/>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 27 90 L 45 90 L 45 108 L 27 108 Z M 27 90 " transform="matrix(1,0,0,1,-8,-44)"/>
<path style=" stroke:none;fill-rule:nonzero;fill:rgb(100%,100%,100%);fill-opacity:1;" d="M 37 46 L 55 46 L 55 28 L 37 28 Z M 37 46 "/>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 45 72 L 63 72 L 63 90 L 45 90 Z M 45 72 " transform="matrix(1,0,0,1,-8,-44)"/>
<path style=" stroke:none;fill-rule:nonzero;fill:rgb(100%,100%,100%);fill-opacity:1;" d="M 37 64 L 55 64 L 55 46 L 37 46 Z M 37 64 "/>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 45 90 L 63 90 L 63 108 L 45 108 Z M 45 90 " transform="matrix(1,0,0,1,-8,-44)"/>
<path style=" stroke:none;fill-rule:nonzero;fill:rgb(39.99939%,74.902344%,100%);fill-opacity:1;" d="M 46 91 L 64 91 L 64 73 L 46 73 Z M 46 91 "/>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 54 117 L 72 117 L 72 135 L 54 135 Z M 54 117 " transform="matrix(1,0,0,1,-8,-44)"/>
<path style=" stroke:none;fill-rule:nonzero;fill:rgb(100%,100%,100%);fill-opacity:1;" d="M 46 55 L 64 55 L 64 37 L 46 37 Z M 46 55 "/>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 54 81 L 72 81 L 72 99 L 54 99 Z M 54 81 " transform="matrix(1,0,0,1,-8,-44)"/>
<path style=" stroke:none;fill-rule:nonzero;fill:rgb(100%,100%,100%);fill-opacity:1;" d="M 46 73 L 64 73 L 64 55 L 46 55 Z M 46 73 "/>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 54 99 L 72 99 L 72 117 L 54 117 Z M 54 99 " transform="matrix(1,0,0,1,-8,-44)"/>
<path style=" stroke:none;fill-rule:nonzero;fill:rgb(100%,100%,100%);fill-opacity:1;" d="M 10 91 L 28 91 L 28 73 L 10 73 Z M 10 91 "/>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 18 117 L 36 117 L 36 135 L 18 135 Z M 18 117 " transform="matrix(1,0,0,1,-8,-44)"/>
<path style=" stroke:none;fill-rule:nonzero;fill:rgb(100%,100%,100%);fill-opacity:1;" d="M 28 91 L 46 91 L 46 73 L 28 73 Z M 28 91 "/>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 36 117 L 54 117 L 54 135 L 36 135 Z M 36 117 " transform="matrix(1,0,0,1,-8,-44)"/>
<path style=" stroke:none;fill-rule:nonzero;fill:rgb(69.804382%,85.098267%,100%);fill-opacity:1;" d="M 10 55 L 28 55 L 28 37 L 10 37 Z M 10 55 "/>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 18 81 L 36 81 L 36 99 L 18 99 Z M 18 81 " transform="matrix(1,0,0,1,-8,-44)"/>
<path style=" stroke:none;fill-rule:nonzero;fill:rgb(100%,100%,100%);fill-opacity:1;" d="M 10 73 L 28 73 L 28 55 L 10 55 Z M 10 73 "/>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 18 99 L 36 99 L 36 117 L 18 117 Z M 18 99 " transform="matrix(1,0,0,1,-8,-44)"/>
<path style=" stroke:none;fill-rule:nonzero;fill:rgb(100%,100%,100%);fill-opacity:1;" d="M 28 55 L 46 55 L 46 37 L 28 37 Z M 28 55 "/>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 36 81 L 54 81 L 54 99 L 36 99 Z M 36 81 " transform="matrix(1,0,0,1,-8,-44)"/>
<path style=" stroke:none;fill-rule:nonzero;fill:rgb(100%,100%,100%);fill-opacity:1;" d="M 28 73 L 46 73 L 46 55 L 28 55 Z M 28 73 "/>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 36 99 L 54 99 L 54 117 L 36 117 Z M 36 99 " transform="matrix(1,0,0,1,-8,-44)"/>
<g style="fill:rgb(0%,0%,0%);fill-opacity:1;">
  <use xlink:href="#glyph0-1" x="228.5758" y="15"/>
  <use xlink:href="#glyph0-2" x="237.5758" y="15"/>
</g>
<g style="fill:rgb(0%,0%,0%);fill-opacity:1;">
  <use xlink:href="#glyph0-3" x="131.4419" y="15"/>
  <use xlink:href="#glyph0-4" x="140.4419" y="15"/>
  <use xlink:href="#glyph0-5" x="149.4419" y="15"/>
</g>
<g style="fill:rgb(0%,0%,0%);fill-opacity:1;">
  <use xlink:href="#glyph0-1" x="32" y="15"/>
  <use xlink:href="#glyph0-6" x="41" y="15"/>
</g>
<path style=" stroke:none;fill-rule:nonzero;fill:rgb(39.99939%,74.902344%,100%);fill-opacity:1;" d="M 37 100 L 55 100 L 55 82 L 37 82 Z M 37 100 "/>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 45 126 L 63 126 L 63 144 L 45 144 Z M 45 126 " transform="matrix(1,0,0,1,-8,-44)"/>
<path style=" stroke:none;fill-rule:nonzero;fill:rgb(100%,100%,100%);fill-opacity:1;" d="M 37 64 L 55 64 L 55 46 L 37 46 Z M 37 64 "/>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 45 90 L 63 90 L 63 108 L 45 108 Z M 45 90 " transform="matrix(1,0,0,1,-8,-44)"/>
<path style=" stroke:none;fill-rule:nonzero;fill:rgb(100%,100%,100%);fill-opacity:1;" d="M 37 82 L 55 82 L 55 64 L 37 64 Z M 37 82 "/>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 45 108 L 63 108 L 63 126 L 45 126 Z M 45 108 " transform="matrix(1,0,0,1,-8,-44)"/>
<path style=" stroke:none;fill-rule:nonzero;fill:rgb(100%,100%,100%);fill-opacity:1;" d="M 1 100 L 19 100 L 19 82 L 1 82 Z M 1 100 "/>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 9 126 L 27 126 L 27 144 L 9 144 Z M 9 126 " transform="matrix(1,0,0,1,-8,-44)"/>
<path style=" stroke:none;fill-rule:nonzero;fill:rgb(100%,100%,100%);fill-opacity:1;" d="M 19 100 L 37 100 L 37 82 L 19 82 Z M 19 100 "/>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 27 126 L 45 126 L 45 144 L 27 144 Z M 27 126 " transform="matrix(1,0,0,1,-8,-44)"/>
<path style=" stroke:none;fill-rule:nonzero;fill:rgb(69.804382%,85.098267%,100%);fill-opacity:1;" d="M 1 64 L 19 64 L 19 46 L 1 46 Z M 1 64 "/>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 9 90 L 27 90 L 27 108 L 9 108 Z M 9 90 " transform="matrix(1,0,0,1,-8,-44)"/>
<path style=" stroke:none;fill-rule:nonzero;fill:rgb(100%,100%,100%);fill-opacity:1;" d="M 1 82 L 19 82 L 19 64 L 1 64 Z M 1 82 "/>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 9 108 L 27 108 L 27 126 L 9 126 Z M 9 108 " transform="matrix(1,0,0,1,-8,-44)"/>
<path style=" stroke:none;fill-rule:nonzero;fill:rgb(100%,100%,100%);fill-opacity:1;" d="M 19 64 L 37 64 L 37 46 L 19 46 Z M 19 64 "/>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 27 90 L 45 90 L 45 108 L 27 108 Z M 27 90 " transform="matrix(1,0,0,1,-8,-44)"/>
<path style=" stroke:none;fill-rule:nonzero;fill:rgb(100%,100%,100%);fill-opacity:1;" d="M 19 82 L 37 82 L 37 64 L 19 64 Z M 19 82 "/>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 27 108 L 45 108 L 45 126 L 27 126 Z M 27 108 " transform="matrix(1,0,0,1,-8,-44)"/>
<g style="fill:rgb(0%,0%,0%);fill-opacity:1;">
  <use xlink:href="#glyph1-1" x="90.87781" y="73.087"/>
</g>
<g style="fill:rgb(0%,0%,0%);fill-opacity:1;">
  <use xlink:href="#glyph1-2" x="173.304" y="69.7048"/>
</g>
<path style=" stroke:none;fill-rule:nonzero;fill:rgb(69.804382%,85.098267%,100%);fill-opacity:1;" d="M 132.96875 46 L 150.96875 46 L 150.96875 28 L 132.96875 28 Z M 132.96875 46 "/>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 140.96875 72 L 158.96875 72 L 158.96875 90 L 140.96875 90 Z M 140.96875 72 " transform="matrix(1,0,0,1,-8,-44)"/>
<path style=" stroke:none;fill-rule:nonzero;fill:rgb(69.804382%,85.098267%,100%);fill-opacity:1;" d="M 123.96875 55 L 141.96875 55 L 141.96875 37 L 123.96875 37 Z M 123.96875 55 "/>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 131.96875 81 L 149.96875 81 L 149.96875 99 L 131.96875 99 Z M 131.96875 81 " transform="matrix(1,0,0,1,-8,-44)"/>
<path style=" stroke:none;fill-rule:nonzero;fill:rgb(69.804382%,85.098267%,100%);fill-opacity:1;" d="M 114.96875 64 L 132.96875 64 L 132.96875 46 L 114.96875 46 Z M 114.96875 64 "/>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 122.96875 90 L 140.96875 90 L 140.96875 108 L 122.96875 108 Z M 122.96875 90 " transform="matrix(1,0,0,1,-8,-44)"/>
<path style=" stroke:none;fill-rule:nonzero;fill:rgb(39.99939%,74.902344%,100%);fill-opacity:1;" d="M 132.96875 82 L 150.96875 82 L 150.96875 64 L 132.96875 64 Z M 132.96875 82 "/>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 140.96875 108 L 158.96875 108 L 158.96875 126 L 140.96875 126 Z M 140.96875 108 " transform="matrix(1,0,0,1,-8,-44)"/>
<path style=" stroke:none;fill-rule:nonzero;fill:rgb(39.99939%,74.902344%,100%);fill-opacity:1;" d="M 123.96875 91 L 141.96875 91 L 141.96875 73 L 123.96875 73 Z M 123.96875 91 "/>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 131.96875 117 L 149.96875 117 L 149.96875 135 L 131.96875 135 Z M 131.96875 117 " transform="matrix(1,0,0,1,-8,-44)"/>
<path style=" stroke:none;fill-rule:nonzero;fill:rgb(39.99939%,74.902344%,100%);fill-opacity:1;" d="M 114.96875 100 L 132.96875 100 L 132.96875 82 L 114.96875 82 Z M 114.96875 100 "/>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 122.96875 126 L 140.96875 126 L 140.96875 144 L 122.96875 144 Z M 122.96875 126 " transform="matrix(1,0,0,1,-8,-44)"/>
<path style=" stroke:none;fill-rule:nonzero;fill:rgb(39.99939%,74.902344%,100%);fill-opacity:1;" d="M 250.574219 87.363281 L 268.574219 87.363281 L 268.574219 69.363281 L 250.574219 69.363281 Z M 250.574219 87.363281 "/>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 258.574219 113.363281 L 276.574219 113.363281 L 276.574219 131.363281 L 258.574219 131.363281 Z M 258.574219 113.363281 " transform="matrix(1,0,0,1,-8,-44)"/>
<path style=" stroke:none;fill-rule:nonzero;fill:rgb(100%,100%,100%);fill-opacity:1;" d="M 250.574219 51.363281 L 268.574219 51.363281 L 268.574219 33.363281 L 250.574219 33.363281 Z M 250.574219 51.363281 "/>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 258.574219 77.363281 L 276.574219 77.363281 L 276.574219 95.363281 L 258.574219 95.363281 Z M 258.574219 77.363281 " transform="matrix(1,0,0,1,-8,-44)"/>
<path style=" stroke:none;fill-rule:nonzero;fill:rgb(100%,100%,100%);fill-opacity:1;" d="M 250.574219 69.363281 L 268.574219 69.363281 L 268.574219 51.363281 L 250.574219 51.363281 Z M 250.574219 69.363281 "/>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 258.574219 95.363281 L 276.574219 95.363281 L 276.574219 113.363281 L 258.574219 113.363281 Z M 258.574219 95.363281 " transform="matrix(1,0,0,1,-8,-44)"/>
<path style=" stroke:none;fill-rule:nonzero;fill:rgb(100%,100%,100%);fill-opacity:1;" d="M 214.574219 87.363281 L 232.574219 87.363281 L 232.574219 69.363281 L 214.574219 69.363281 Z M 214.574219 87.363281 "/>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 222.574219 113.363281 L 240.574219 113.363281 L 240.574219 131.363281 L 222.574219 131.363281 Z M 222.574219 113.363281 " transform="matrix(1,0,0,1,-8,-44)"/>
<path style=" stroke:none;fill-rule:nonzero;fill:rgb(100%,100%,100%);fill-opacity:1;" d="M 232.574219 87.363281 L 250.574219 87.363281 L 250.574219 69.363281 L 232.574219 69.363281 Z M 232.574219 87.363281 "/>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 240.574219 113.363281 L 258.574219 113.363281 L 258.574219 131.363281 L 240.574219 131.363281 Z M 240.574219 113.363281 " transform="matrix(1,0,0,1,-8,-44)"/>
<path style=" stroke:none;fill-rule:nonzero;fill:rgb(100%,100%,100%);fill-opacity:1;" d="M 214.574219 51.363281 L 232.574219 51.363281 L 232.574219 33.363281 L 214.574219 33.363281 Z M 214.574219 51.363281 "/>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 222.574219 77.363281 L 240.574219 77.363281 L 240.574219 95.363281 L 222.574219 95.363281 Z M 222.574219 77.363281 " transform="matrix(1,0,0,1,-8,-44)"/>
<path style=" stroke:none;fill-rule:nonzero;fill:rgb(100%,100%,100%);fill-opacity:1;" d="M 214.574219 69.363281 L 232.574219 69.363281 L 232.574219 51.363281 L 214.574219 51.363281 Z M 214.574219 69.363281 "/>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 222.574219 95.363281 L 240.574219 95.363281 L 240.574219 113.363281 L 222.574219 113.363281 Z M 222.574219 95.363281 " transform="matrix(1,0,0,1,-8,-44)"/>
<path style=" stroke:none;fill-rule:nonzero;fill:rgb(100%,100%,100%);fill-opacity:1;" d="M 232.574219 51.363281 L 250.574219 51.363281 L 250.574219 33.363281 L 232.574219 33.363281 Z M 232.574219 51.363281 "/>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 240.574219 77.363281 L 258.574219 77.363281 L 258.574219 95.363281 L 240.574219 95.363281 Z M 240.574219 77.363281 " transform="matrix(1,0,0,1,-8,-44)"/>
<path style=" stroke:none;fill-rule:nonzero;fill:rgb(100%,100%,100%);fill-opacity:1;" d="M 232.574219 69.363281 L 250.574219 69.363281 L 250.574219 51.363281 L 232.574219 51.363281 Z M 232.574219 69.363281 "/>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 240.574219 95.363281 L 258.574219 95.363281 L 258.574219 113.363281 L 240.574219 113.363281 Z M 240.574219 95.363281 " transform="matrix(1,0,0,1,-8,-44)"/>
<path style=" stroke:none;fill-rule:nonzero;fill:rgb(100%,100%,100%);fill-opacity:1;" d="M 241.574219 96.363281 L 259.574219 96.363281 L 259.574219 78.363281 L 241.574219 78.363281 Z M 241.574219 96.363281 "/>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 249.574219 122.363281 L 267.574219 122.363281 L 267.574219 140.363281 L 249.574219 140.363281 Z M 249.574219 122.363281 " transform="matrix(1,0,0,1,-8,-44)"/>
<path style=" stroke:none;fill-rule:nonzero;fill:rgb(100%,100%,100%);fill-opacity:1;" d="M 241.574219 60.363281 L 259.574219 60.363281 L 259.574219 42.363281 L 241.574219 42.363281 Z M 241.574219 60.363281 "/>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 249.574219 86.363281 L 267.574219 86.363281 L 267.574219 104.363281 L 249.574219 104.363281 Z M 249.574219 86.363281 " transform="matrix(1,0,0,1,-8,-44)"/>
<path style=" stroke:none;fill-rule:nonzero;fill:rgb(100%,100%,100%);fill-opacity:1;" d="M 241.574219 78.363281 L 259.574219 78.363281 L 259.574219 60.363281 L 241.574219 60.363281 Z M 241.574219 78.363281 "/>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 249.574219 104.363281 L 267.574219 104.363281 L 267.574219 122.363281 L 249.574219 122.363281 Z M 249.574219 104.363281 " transform="matrix(1,0,0,1,-8,-44)"/>
<path style=" stroke:none;fill-rule:nonzero;fill:rgb(100%,100%,100%);fill-opacity:1;" d="M 205.574219 96.363281 L 223.574219 96.363281 L 223.574219 78.363281 L 205.574219 78.363281 Z M 205.574219 96.363281 "/>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 213.574219 122.363281 L 231.574219 122.363281 L 231.574219 140.363281 L 213.574219 140.363281 Z M 213.574219 122.363281 " transform="matrix(1,0,0,1,-8,-44)"/>
<path style=" stroke:none;fill-rule:nonzero;fill:rgb(100%,100%,100%);fill-opacity:1;" d="M 223.574219 96.363281 L 241.574219 96.363281 L 241.574219 78.363281 L 223.574219 78.363281 Z M 223.574219 96.363281 "/>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 231.574219 122.363281 L 249.574219 122.363281 L 249.574219 140.363281 L 231.574219 140.363281 Z M 231.574219 122.363281 " transform="matrix(1,0,0,1,-8,-44)"/>
<path style=" stroke:none;fill-rule:nonzero;fill:rgb(69.804382%,85.098267%,100%);fill-opacity:1;" d="M 205.574219 60.363281 L 223.574219 60.363281 L 223.574219 42.363281 L 205.574219 42.363281 Z M 205.574219 60.363281 "/>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 213.574219 86.363281 L 231.574219 86.363281 L 231.574219 104.363281 L 213.574219 104.363281 Z M 213.574219 86.363281 " transform="matrix(1,0,0,1,-8,-44)"/>
<path style=" stroke:none;fill-rule:nonzero;fill:rgb(100%,100%,100%);fill-opacity:1;" d="M 205.574219 78.363281 L 223.574219 78.363281 L 223.574219 60.363281 L 205.574219 60.363281 Z M 205.574219 78.363281 "/>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 213.574219 104.363281 L 231.574219 104.363281 L 231.574219 122.363281 L 213.574219 122.363281 Z M 213.574219 104.363281 " transform="matrix(1,0,0,1,-8,-44)"/>
<path style=" stroke:none;fill-rule:nonzero;fill:rgb(100%,100%,100%);fill-opacity:1;" d="M 223.574219 60.363281 L 241.574219 60.363281 L 241.574219 42.363281 L 223.574219 42.363281 Z M 223.574219 60.363281 "/>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 231.574219 86.363281 L 249.574219 86.363281 L 249.574219 104.363281 L 231.574219 104.363281 Z M 231.574219 86.363281 " transform="matrix(1,0,0,1,-8,-44)"/>
<path style=" stroke:none;fill-rule:nonzero;fill:rgb(100%,100%,100%);fill-opacity:1;" d="M 223.574219 78.363281 L 241.574219 78.363281 L 241.574219 60.363281 L 223.574219 60.363281 Z M 223.574219 78.363281 "/>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 231.574219 104.363281 L 249.574219 104.363281 L 249.574219 122.363281 L 231.574219 122.363281 Z M 231.574219 104.363281 " transform="matrix(1,0,0,1,-8,-44)"/>
</g>
</svg>
