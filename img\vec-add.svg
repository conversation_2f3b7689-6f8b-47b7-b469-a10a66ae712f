<?xml version="1.0" encoding="UTF-8"?>
<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="202pt" height="154pt" viewBox="0 0 202 154" version="1.1">
<defs>
<g>
<symbol overflow="visible" id="glyph0-0">
<path style="stroke:none;" d=""/>
</symbol>
<symbol overflow="visible" id="glyph0-1">
<path style="stroke:none;" d="M 2.734375 1.453125 C 1.46875 0.390625 1.203125 -0.625 1.203125 -2.296875 C 1.203125 -4.03125 1.484375 -4.875 2.734375 -5.9375 L 2.65625 -6.078125 C 1.21875 -5.234375 0.4375 -3.9375 0.4375 -2.265625 C 0.4375 -0.734375 1.203125 0.78125 2.625 1.59375 Z M 2.734375 1.453125 "/>
</symbol>
<symbol overflow="visible" id="glyph0-2">
<path style="stroke:none;" d="M 4.265625 -1.234375 L 4.140625 -1.28125 C 3.84375 -0.78125 3.65625 -0.6875 3.28125 -0.6875 L 1.171875 -0.6875 L 2.65625 -2.265625 C 3.453125 -3.109375 3.8125 -3.78125 3.8125 -4.5 C 3.8125 -5.390625 3.15625 -6.078125 2.140625 -6.078125 C 1.03125 -6.078125 0.453125 -5.34375 0.265625 -4.296875 L 0.453125 -4.25 C 0.8125 -5.125 1.140625 -5.421875 1.78125 -5.421875 C 2.546875 -5.421875 3.03125 -4.96875 3.03125 -4.15625 C 3.03125 -3.390625 2.703125 -2.703125 1.859375 -1.8125 L 0.265625 -0.109375 L 0.265625 0 L 3.78125 0 Z M 4.265625 -1.234375 "/>
</symbol>
<symbol overflow="visible" id="glyph0-3">
<path style="stroke:none;" d="M 0.75 1.265625 C 1.375 0.96875 1.75 0.40625 1.75 -0.140625 C 1.75 -0.609375 1.4375 -0.921875 1.03125 -0.921875 C 0.71875 -0.921875 0.5 -0.71875 0.5 -0.40625 C 0.5 -0.09375 0.6875 0.046875 1.015625 0.046875 C 1.109375 0.046875 1.203125 0.015625 1.28125 0.015625 C 1.34375 0.015625 1.40625 0.078125 1.40625 0.140625 C 1.40625 0.4375 1.15625 0.765625 0.65625 1.09375 Z M 0.75 1.265625 "/>
</symbol>
<symbol overflow="visible" id="glyph0-4">
<path style="stroke:none;" d=""/>
</symbol>
<symbol overflow="visible" id="glyph0-5">
<path style="stroke:none;" d="M 2.5625 -1.75 L 2.5625 -2.3125 L 0.34375 -2.3125 L 0.34375 -1.75 Z M 2.5625 -1.75 "/>
</symbol>
<symbol overflow="visible" id="glyph0-6">
<path style="stroke:none;" d="M 3.546875 0 L 3.546875 -0.140625 C 2.875 -0.140625 2.6875 -0.296875 2.6875 -0.6875 L 2.6875 -6.0625 L 2.609375 -6.078125 L 1 -5.265625 L 1 -5.140625 L 1.234375 -5.234375 C 1.40625 -5.296875 1.5625 -5.34375 1.640625 -5.34375 C 1.84375 -5.34375 1.921875 -5.203125 1.921875 -4.890625 L 1.921875 -0.859375 C 1.921875 -0.359375 1.734375 -0.171875 1.0625 -0.140625 L 1.0625 0 Z M 3.546875 0 "/>
</symbol>
<symbol overflow="visible" id="glyph0-7">
<path style="stroke:none;" d="M 0.265625 -5.9375 C 1.5625 -4.90625 1.796875 -3.875 1.796875 -2.203125 C 1.796875 -0.453125 1.53125 0.390625 0.265625 1.453125 L 0.34375 1.59375 C 1.765625 0.71875 2.5625 -0.5625 2.5625 -2.21875 C 2.5625 -3.75 1.75 -5.25 0.375 -6.078125 Z M 0.265625 -5.9375 "/>
</symbol>
<symbol overflow="visible" id="glyph0-8">
<path style="stroke:none;" d="M 0.546875 -4.59375 C 0.921875 -5.25 1.328125 -5.546875 1.890625 -5.546875 C 2.484375 -5.546875 2.859375 -5.234375 2.859375 -4.625 C 2.859375 -4.078125 2.578125 -3.671875 2.140625 -3.421875 C 1.953125 -3.3125 1.71875 -3.21875 1.375 -3.09375 L 1.375 -2.96875 C 1.890625 -2.96875 2.09375 -2.9375 2.296875 -2.875 C 2.921875 -2.703125 3.234375 -2.265625 3.234375 -1.578125 C 3.234375 -0.8125 2.734375 -0.203125 2.0625 -0.203125 C 1.8125 -0.203125 1.625 -0.25 1.28125 -0.484375 C 1.03125 -0.65625 0.890625 -0.71875 0.734375 -0.71875 C 0.53125 -0.71875 0.375 -0.578125 0.375 -0.390625 C 0.375 -0.0625 0.71875 0.125 1.375 0.125 C 2.171875 0.125 3.03125 -0.140625 3.46875 -0.71875 C 3.71875 -1.046875 3.875 -1.5 3.875 -1.96875 C 3.875 -2.4375 3.734375 -2.859375 3.484375 -3.125 C 3.296875 -3.328125 3.125 -3.4375 2.734375 -3.609375 C 3.34375 -3.96875 3.578125 -4.421875 3.578125 -4.84375 C 3.578125 -5.59375 3 -6.078125 2.171875 -6.078125 C 1.234375 -6.078125 0.671875 -5.484375 0.40625 -4.625 Z M 0.546875 -4.59375 "/>
</symbol>
</g>
</defs>
<g id="surface1">
<path style="fill:none;stroke-width:1;stroke-linecap:butt;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 142.71875 97.371094 L 101.34375 76.773438 " transform="matrix(1,0,0,1,1,1)"/>
<path style="fill-rule:nonzero;fill:rgb(0%,0%,0%);fill-opacity:1;stroke-width:1;stroke-linecap:butt;stroke-linejoin:miter;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 101.421875 75.628906 C 101.683594 76.414062 101.261719 77.261719 100.476562 77.527344 C 99.691406 77.789062 98.839844 77.367188 98.578125 76.582031 C 98.316406 75.796875 98.738281 74.945312 99.523438 74.683594 C 100.308594 74.417969 101.160156 74.84375 101.421875 75.628906 " transform="matrix(1,0,0,1,1,1)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:butt;stroke-linejoin:miter;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 146.300781 99.152344 L 142.71875 97.371094 M 143.386719 96.027344 L 146.300781 99.152344 L 142.050781 98.714844 " transform="matrix(1,0,0,1,1,1)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:butt;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 5.898438 76 L 194.101562 76 " transform="matrix(1,0,0,1,1,1)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:butt;stroke-linejoin:miter;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 198.101562 76 L 194.101562 76 M 194.101562 74.5 L 198.101562 76 L 194.101562 77.5 " transform="matrix(1,0,0,1,1,1)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:butt;stroke-linejoin:miter;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 1.898438 76 L 5.898438 76 M 5.898438 77.5 L 1.898438 76 L 5.898438 74.5 " transform="matrix(1,0,0,1,1,1)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:butt;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 100 5.898438 L 100 146.101562 " transform="matrix(1,0,0,1,1,1)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:butt;stroke-linejoin:miter;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 100 150.101562 L 100 146.101562 M 101.5 146.101562 L 100 150.101562 L 98.5 146.101562 " transform="matrix(1,0,0,1,1,1)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:butt;stroke-linejoin:miter;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 100 1.898438 L 100 5.898438 M 98.5 5.898438 L 100 1.898438 L 101.5 5.898438 " transform="matrix(1,0,0,1,1,1)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:butt;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-dasharray:1,4;stroke-miterlimit:10;" d="M 124 8 L 124 144 " transform="matrix(1,0,0,1,1,1)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:butt;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-dasharray:1,4;stroke-miterlimit:10;" d="M 148 8 L 148 144 " transform="matrix(1,0,0,1,1,1)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:butt;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-dasharray:1,4;stroke-miterlimit:10;" d="M 172 8 L 172 144 " transform="matrix(1,0,0,1,1,1)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:butt;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-dasharray:1,4;stroke-miterlimit:10;" d="M 76 8 L 76 144 " transform="matrix(1,0,0,1,1,1)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:butt;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-dasharray:1,4;stroke-miterlimit:10;" d="M 28 8 L 28 144 " transform="matrix(1,0,0,1,1,1)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:butt;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-dasharray:1,4;stroke-miterlimit:10;" d="M 52 8 L 52 144 " transform="matrix(1,0,0,1,1,1)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:butt;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-dasharray:1,4;stroke-miterlimit:10;" d="M 8 52 L 192 52 " transform="matrix(1,0,0,1,1,1)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:butt;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-dasharray:1,4;stroke-miterlimit:10;" d="M 8 28 L 192 28 " transform="matrix(1,0,0,1,1,1)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:butt;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-dasharray:1,4;stroke-miterlimit:10;" d="M 8 100 L 192 100 " transform="matrix(1,0,0,1,1,1)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:butt;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-dasharray:1,4;stroke-miterlimit:10;" d="M 8 124 L 192 124 " transform="matrix(1,0,0,1,1,1)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:butt;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 166.402344 53.867188 L 100.710938 75.761719 " transform="matrix(1,0,0,1,1,1)"/>
<path style="fill-rule:nonzero;fill:rgb(0%,0%,0%);fill-opacity:1;stroke-width:1;stroke-linecap:butt;stroke-linejoin:miter;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 100.335938 75.328125 C 100.707031 75.515625 100.855469 75.964844 100.671875 76.335938 C 100.484375 76.707031 100.035156 76.855469 99.664062 76.671875 C 99.292969 76.484375 99.144531 76.035156 99.328125 75.664062 C 99.515625 75.292969 99.964844 75.144531 100.335938 75.328125 " transform="matrix(1,0,0,1,1,1)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:butt;stroke-linejoin:miter;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 170.199219 52.601562 L 166.402344 53.867188 M 165.929688 52.441406 L 170.199219 52.601562 L 166.878906 55.289062 " transform="matrix(1,0,0,1,1,1)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:butt;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 121.363281 33.277344 L 100.671875 74.660156 " transform="matrix(1,0,0,1,1,1)"/>
<path style="fill-rule:nonzero;fill:rgb(0%,0%,0%);fill-opacity:1;stroke-width:1;stroke-linecap:butt;stroke-linejoin:miter;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 99.527344 74.578125 C 100.3125 74.316406 101.160156 74.738281 101.421875 75.527344 C 101.683594 76.3125 101.261719 77.160156 100.472656 77.421875 C 99.6875 77.683594 98.839844 77.261719 98.578125 76.472656 C 98.316406 75.6875 98.738281 74.839844 99.527344 74.578125 " transform="matrix(1,0,0,1,1,1)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:butt;stroke-linejoin:miter;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 123.148438 29.699219 L 121.363281 33.277344 M 120.019531 32.605469 L 123.148438 29.699219 L 122.703125 33.949219 " transform="matrix(1,0,0,1,1,1)"/>
<g style="fill:rgb(0%,0%,0%);fill-opacity:1;">
  <use xlink:href="#glyph0-1" x="149.875" y="110"/>
  <use xlink:href="#glyph0-2" x="152.872" y="110"/>
  <use xlink:href="#glyph0-3" x="157.372" y="110"/>
  <use xlink:href="#glyph0-4" x="159.622" y="110"/>
  <use xlink:href="#glyph0-5" x="161.872" y="110"/>
  <use xlink:href="#glyph0-6" x="164.869" y="110"/>
  <use xlink:href="#glyph0-7" x="169.369" y="110"/>
</g>
<path style="fill:none;stroke-width:1;stroke-linecap:butt;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-dasharray:4,4;stroke-miterlimit:10;" d="M 169.582031 57.265625 L 148.675781 98.660156 " transform="matrix(1,0,0,1,1,1)"/>
<path style="fill-rule:nonzero;fill:rgb(0%,0%,0%);fill-opacity:1;stroke-width:1;stroke-linecap:butt;stroke-linejoin:miter;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 147.53125 98.574219 C 148.316406 98.316406 149.167969 98.746094 149.425781 99.53125 C 149.683594 100.316406 149.253906 101.167969 148.46875 101.425781 C 147.683594 101.683594 146.832031 101.253906 146.574219 100.46875 C 146.316406 99.683594 146.746094 98.832031 147.53125 98.574219 " transform="matrix(1,0,0,1,1,1)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:butt;stroke-linejoin:miter;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 171.382812 53.695312 L 169.582031 57.265625 M 168.242188 56.589844 L 171.382812 53.695312 L 170.921875 57.941406 " transform="matrix(1,0,0,1,1,1)"/>
<g style="fill:rgb(0%,0%,0%);fill-opacity:1;">
  <use xlink:href="#glyph0-1" x="103.631" y="26"/>
  <use xlink:href="#glyph0-6" x="106.628" y="26"/>
  <use xlink:href="#glyph0-3" x="111.128" y="26"/>
  <use xlink:href="#glyph0-4" x="113.378" y="26"/>
  <use xlink:href="#glyph0-2" x="115.628" y="26"/>
  <use xlink:href="#glyph0-7" x="120.128" y="26"/>
</g>
<g style="fill:rgb(0%,0%,0%);fill-opacity:1;">
  <use xlink:href="#glyph0-1" x="175.875" y="50"/>
  <use xlink:href="#glyph0-8" x="178.872" y="50"/>
  <use xlink:href="#glyph0-3" x="183.372" y="50"/>
  <use xlink:href="#glyph0-4" x="185.622" y="50"/>
  <use xlink:href="#glyph0-6" x="187.872" y="50"/>
  <use xlink:href="#glyph0-7" x="192.372" y="50"/>
</g>
<path style="fill:none;stroke-width:1;stroke-linecap:butt;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-dasharray:4,4;stroke-miterlimit:10;" d="M 166.957031 49.371094 L 125.34375 28.667969 " transform="matrix(1,0,0,1,1,1)"/>
<path style="fill-rule:nonzero;fill:rgb(0%,0%,0%);fill-opacity:1;stroke-width:1;stroke-linecap:butt;stroke-linejoin:miter;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 125.421875 27.523438 C 125.6875 28.308594 125.261719 29.160156 124.476562 29.421875 C 123.691406 29.6875 122.839844 29.261719 122.578125 28.476562 C 122.3125 27.691406 122.738281 26.839844 123.523438 26.578125 C 124.308594 26.3125 125.160156 26.738281 125.421875 27.523438 " transform="matrix(1,0,0,1,1,1)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:butt;stroke-linejoin:miter;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 170.539062 51.152344 L 166.957031 49.371094 M 167.625 48.027344 L 170.539062 51.152344 L 166.289062 50.714844 " transform="matrix(1,0,0,1,1,1)"/>
</g>
</svg>
