{"cells": [{"cell_type": "markdown", "id": "b2bee90b", "metadata": {"origin_pos": 0}, "source": ["# 深度学习计算\n", ":label:`chap_computation`\n", "\n", "除了庞大的数据集和强大的硬件，\n", "优秀的软件工具在深度学习的快速发展中发挥了不可或缺的作用。\n", "从2007年发布的开创性的Theano库开始，\n", "灵活的开源工具使研究人员能够快速开发模型原型，\n", "避免了我们使用标准组件时的重复工作，\n", "同时仍然保持了我们进行底层修改的能力。\n", "随着时间的推移，深度学习库已经演变成提供越来越粗糙的抽象。\n", "就像半导体设计师从指定晶体管到逻辑电路再到编写代码一样，\n", "神经网络研究人员已经从考虑单个人工神经元的行为转变为从层的角度构思网络，\n", "通常在设计架构时考虑的是更粗糙的块（block）。\n", "\n", "之前我们已经介绍了一些基本的机器学习概念，\n", "并慢慢介绍了功能齐全的深度学习模型。\n", "在上一章中，我们从零开始实现了多层感知机的每个组件，\n", "然后展示了如何利用高级API轻松地实现相同的模型。\n", "为了易于学习，我们调用了深度学习库，但是跳过了它们工作的细节。\n", "在本章中，我们将深入探索深度学习计算的关键组件，\n", "即模型构建、参数访问与初始化、设计自定义层和块、将模型读写到磁盘，\n", "以及利用GPU实现显著的加速。\n", "这些知识将使读者从深度学习“基础用户”变为“高级用户”。\n", "虽然本章不介绍任何新的模型或数据集，\n", "但后面的高级模型章节在很大程度上依赖于本章的知识。\n", "\n", ":begin_tab:toc\n", " - [model-construction](model-construction.ipynb)\n", " - [parameters](parameters.ipynb)\n", " - [deferred-init](deferred-init.ipynb)\n", " - [custom-layer](custom-layer.ipynb)\n", " - [read-write](read-write.ipynb)\n", " - [use-gpu](use-gpu.ipynb)\n", ":end_tab:\n"]}], "metadata": {"language_info": {"name": "python"}}, "nbformat": 4, "nbformat_minor": 5}