<?xml version="1.0" encoding="UTF-8"?>
<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="572pt" height="220pt" viewBox="0 0 572 220" version="1.1">
<g id="surface1">
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 350.269531 182.550781 C 353.636719 185.921875 353.636719 191.382812 350.269531 194.753906 C 346.902344 198.121094 341.4375 198.121094 338.070312 194.753906 C 334.699219 191.382812 334.699219 185.921875 338.070312 182.550781 C 341.4375 179.183594 346.902344 179.183594 350.269531 182.550781 " transform="matrix(1,0,0,1,-326,-37)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 386.046875 182.550781 C 389.414062 185.921875 389.414062 191.382812 386.046875 194.753906 C 382.675781 198.121094 377.214844 198.121094 373.84375 194.753906 C 370.476562 191.382812 370.476562 185.921875 373.84375 182.550781 C 377.214844 179.183594 382.675781 179.183594 386.046875 182.550781 " transform="matrix(1,0,0,1,-326,-37)"/>
<path style="fill-rule:nonzero;fill:rgb(39.99939%,74.902344%,100%);fill-opacity:1;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 372.597656 141.277344 C 375.96875 144.648438 375.96875 150.109375 372.597656 153.480469 C 369.230469 156.847656 363.765625 156.847656 360.398438 153.480469 C 357.027344 150.109375 357.027344 144.648438 360.398438 141.277344 C 363.765625 137.910156 369.230469 137.910156 372.597656 141.277344 " transform="matrix(1,0,0,1,-326,-37)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 421.824219 182.550781 C 425.191406 185.921875 425.191406 191.382812 421.824219 194.753906 C 418.453125 198.121094 412.992188 198.121094 409.621094 194.753906 C 406.253906 191.382812 406.253906 185.921875 409.621094 182.550781 C 412.992188 179.183594 418.453125 179.183594 421.824219 182.550781 " transform="matrix(1,0,0,1,-326,-37)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 457.597656 182.046875 C 460.96875 185.414062 460.96875 190.875 457.597656 194.246094 C 454.230469 197.613281 448.765625 197.613281 445.398438 194.246094 C 442.03125 190.875 442.03125 185.414062 445.398438 182.046875 C 448.765625 178.675781 454.230469 178.675781 457.597656 182.046875 " transform="matrix(1,0,0,1,-326,-37)"/>
<path style="fill-rule:nonzero;fill:rgb(39.99939%,74.902344%,100%);fill-opacity:1;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 434.339844 141.277344 C 437.707031 144.648438 437.707031 150.109375 434.339844 153.480469 C 430.972656 156.847656 425.507812 156.847656 422.140625 153.480469 C 418.769531 150.109375 418.769531 144.648438 422.140625 141.277344 C 425.507812 137.910156 430.972656 137.910156 434.339844 141.277344 " transform="matrix(1,0,0,1,-326,-37)"/>
<path style="fill-rule:nonzero;fill:rgb(39.99939%,74.902344%,100%);fill-opacity:1;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 403.46875 141.277344 C 406.839844 144.648438 406.839844 150.109375 403.46875 153.480469 C 400.101562 156.847656 394.636719 156.847656 391.269531 153.480469 C 387.898438 150.109375 387.898438 144.648438 391.269531 141.277344 C 394.636719 137.910156 400.101562 137.910156 403.46875 141.277344 " transform="matrix(1,0,0,1,-326,-37)"/>
<path style="fill-rule:nonzero;fill:rgb(69.804382%,85.098267%,100%);fill-opacity:1;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 367.691406 100.003906 C 371.0625 103.375 371.0625 108.835938 367.691406 112.203125 C 364.324219 115.574219 358.859375 115.574219 355.492188 112.203125 C 352.125 108.835938 352.125 103.375 355.492188 100.003906 C 358.859375 96.636719 364.324219 96.636719 367.691406 100.003906 " transform="matrix(1,0,0,1,-326,-37)"/>
<path style="fill-rule:nonzero;fill:rgb(69.804382%,85.098267%,100%);fill-opacity:1;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 403.46875 100.003906 C 406.839844 103.375 406.839844 108.835938 403.46875 112.203125 C 400.101562 115.574219 394.636719 115.574219 391.269531 112.203125 C 387.898438 108.835938 387.898438 103.375 391.269531 100.003906 C 394.636719 96.636719 400.101562 96.636719 403.46875 100.003906 " transform="matrix(1,0,0,1,-326,-37)"/>
<path style="fill-rule:nonzero;fill:rgb(69.804382%,85.098267%,100%);fill-opacity:1;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 439.246094 99.496094 C 442.613281 102.867188 442.613281 108.328125 439.246094 111.699219 C 435.875 115.066406 430.414062 115.066406 427.046875 111.699219 C 423.675781 108.328125 423.675781 102.867188 427.046875 99.496094 C 430.414062 96.128906 435.875 96.128906 439.246094 99.496094 " transform="matrix(1,0,0,1,-326,-37)"/>
<path style="fill-rule:nonzero;fill:rgb(39.99939%,74.902344%,100%);fill-opacity:1;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 341.726562 141.277344 C 345.097656 144.648438 345.097656 150.109375 341.726562 153.480469 C 338.359375 156.847656 332.894531 156.847656 329.527344 153.480469 C 326.15625 150.109375 326.15625 144.648438 329.527344 141.277344 C 332.894531 137.910156 338.359375 137.910156 341.726562 141.277344 " transform="matrix(1,0,0,1,-326,-37)"/>
<path style="fill-rule:nonzero;fill:rgb(39.99939%,74.902344%,100%);fill-opacity:1;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 465.210938 141.277344 C 468.578125 144.648438 468.578125 150.109375 465.210938 153.480469 C 461.839844 156.847656 456.378906 156.847656 453.011719 153.480469 C 449.640625 150.109375 449.640625 144.648438 453.011719 141.277344 C 456.378906 137.910156 461.839844 137.910156 465.210938 141.277344 " transform="matrix(1,0,0,1,-326,-37)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 340.222656 140.074219 L 353.855469 118.402344 " transform="matrix(1,0,0,1,-326,-37)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:butt;stroke-linejoin:miter;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 355.984375 115.015625 L 353.855469 118.402344 M 352.585938 117.605469 L 355.984375 115.015625 L 355.125 119.203125 " transform="matrix(1,0,0,1,-326,-37)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 342.800781 142.582031 L 385.289062 114.179688 " transform="matrix(1,0,0,1,-326,-37)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:butt;stroke-linejoin:miter;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 388.617188 111.957031 L 385.289062 114.179688 M 384.457031 112.933594 L 388.617188 111.957031 L 386.125 115.425781 " transform="matrix(1,0,0,1,-326,-37)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 343.558594 143.980469 L 419.789062 111.320312 " transform="matrix(1,0,0,1,-326,-37)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:butt;stroke-linejoin:miter;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 423.46875 109.742188 L 419.789062 111.320312 M 419.199219 109.941406 L 423.46875 109.742188 L 420.378906 112.699219 " transform="matrix(1,0,0,1,-326,-37)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 365.480469 138.8125 L 363.308594 120.53125 " transform="matrix(1,0,0,1,-326,-37)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:butt;stroke-linejoin:miter;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 362.835938 116.558594 L 363.308594 120.53125 M 361.816406 120.707031 L 362.835938 116.558594 L 364.796875 120.351562 " transform="matrix(1,0,0,1,-326,-37)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 371.664062 140.46875 L 388.667969 117.738281 " transform="matrix(1,0,0,1,-326,-37)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:butt;stroke-linejoin:miter;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 391.0625 114.535156 L 388.667969 117.738281 M 387.464844 116.839844 L 391.0625 114.535156 L 389.867188 118.636719 " transform="matrix(1,0,0,1,-326,-37)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 373.808594 142.796875 L 420.835938 113.3125 " transform="matrix(1,0,0,1,-326,-37)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:butt;stroke-linejoin:miter;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 424.222656 111.191406 L 420.835938 113.3125 M 420.039062 112.042969 L 424.222656 111.191406 L 421.632812 114.585938 " transform="matrix(1,0,0,1,-326,-37)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 391.71875 140.859375 L 371.109375 117.082031 " transform="matrix(1,0,0,1,-326,-37)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:butt;stroke-linejoin:miter;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 368.488281 114.058594 L 371.109375 117.082031 M 369.972656 118.066406 L 368.488281 114.058594 L 372.242188 116.101562 " transform="matrix(1,0,0,1,-326,-37)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 397.367188 138.75 L 397.367188 120.632812 " transform="matrix(1,0,0,1,-326,-37)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:butt;stroke-linejoin:miter;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 397.367188 116.632812 L 397.367188 120.632812 M 395.867188 120.632812 L 397.367188 116.632812 L 398.867188 120.632812 " transform="matrix(1,0,0,1,-326,-37)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 402.980469 140.824219 L 423.695312 116.632812 " transform="matrix(1,0,0,1,-326,-37)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:butt;stroke-linejoin:miter;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 426.296875 113.59375 L 423.695312 116.632812 M 422.558594 115.65625 L 426.296875 113.59375 L 424.835938 117.609375 " transform="matrix(1,0,0,1,-326,-37)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 420.902344 142.835938 L 373.945312 113.753906 " transform="matrix(1,0,0,1,-326,-37)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:butt;stroke-linejoin:miter;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 370.542969 111.648438 L 373.945312 113.753906 M 373.15625 115.03125 L 370.542969 111.648438 L 374.734375 112.480469 " transform="matrix(1,0,0,1,-326,-37)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 423.070312 140.46875 L 406.070312 117.738281 " transform="matrix(1,0,0,1,-326,-37)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:butt;stroke-linejoin:miter;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 403.675781 114.535156 L 406.070312 117.738281 M 404.867188 118.636719 L 403.675781 114.535156 L 407.273438 116.839844 " transform="matrix(1,0,0,1,-326,-37)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 429.246094 138.808594 L 431.449219 120.027344 " transform="matrix(1,0,0,1,-326,-37)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:butt;stroke-linejoin:miter;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 431.917969 116.054688 L 431.449219 120.027344 M 429.960938 119.851562 L 431.917969 116.054688 L 432.941406 120.199219 " transform="matrix(1,0,0,1,-326,-37)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 451.164062 144.015625 L 374.972656 111.769531 " transform="matrix(1,0,0,1,-326,-37)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:butt;stroke-linejoin:miter;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 371.289062 110.207031 L 374.972656 111.769531 M 374.386719 113.148438 L 371.289062 110.207031 L 375.558594 110.386719 " transform="matrix(1,0,0,1,-326,-37)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 451.9375 142.582031 L 409.445312 114.179688 " transform="matrix(1,0,0,1,-326,-37)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:butt;stroke-linejoin:miter;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 406.121094 111.957031 L 409.445312 114.179688 M 408.613281 115.425781 L 406.121094 111.957031 L 410.28125 112.933594 " transform="matrix(1,0,0,1,-326,-37)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 454.554688 140.050781 L 440.8125 117.9375 " transform="matrix(1,0,0,1,-326,-37)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:butt;stroke-linejoin:miter;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 438.703125 114.539062 L 440.8125 117.9375 M 439.539062 118.730469 L 438.703125 114.539062 L 442.089844 117.144531 " transform="matrix(1,0,0,1,-326,-37)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 342.421875 180.203125 L 338.570312 161.605469 " transform="matrix(1,0,0,1,-326,-37)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:butt;stroke-linejoin:miter;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 337.761719 157.6875 L 338.570312 161.605469 M 337.101562 161.910156 L 337.761719 157.6875 L 340.039062 161.300781 " transform="matrix(1,0,0,1,-326,-37)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 348.273438 181.0625 L 359.585938 160.15625 " transform="matrix(1,0,0,1,-326,-37)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:butt;stroke-linejoin:miter;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 361.488281 156.640625 L 359.585938 160.15625 M 358.265625 159.445312 L 361.488281 156.640625 L 360.902344 160.871094 " transform="matrix(1,0,0,1,-326,-37)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 350.984375 183.363281 L 385.890625 156.285156 " transform="matrix(1,0,0,1,-326,-37)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:butt;stroke-linejoin:miter;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 389.050781 153.832031 L 385.890625 156.285156 M 384.972656 155.097656 L 389.050781 153.832031 L 386.808594 157.46875 " transform="matrix(1,0,0,1,-326,-37)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 351.914062 184.851562 L 415.199219 153.78125 " transform="matrix(1,0,0,1,-326,-37)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:butt;stroke-linejoin:miter;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 418.789062 152.019531 L 415.199219 153.78125 M 414.535156 152.433594 L 418.789062 152.019531 L 415.859375 155.128906 " transform="matrix(1,0,0,1,-326,-37)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 352.289062 185.738281 L 445.4375 152.289062 " transform="matrix(1,0,0,1,-326,-37)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:butt;stroke-linejoin:miter;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 449.199219 150.9375 L 445.4375 152.289062 M 444.929688 150.878906 L 449.199219 150.9375 L 445.945312 153.699219 " transform="matrix(1,0,0,1,-326,-37)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 373.632812 182.773438 L 346.257812 157.277344 " transform="matrix(1,0,0,1,-326,-37)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:butt;stroke-linejoin:miter;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 343.332031 154.554688 L 346.257812 157.277344 M 345.234375 158.375 L 343.332031 154.554688 L 347.28125 156.179688 " transform="matrix(1,0,0,1,-326,-37)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 377.273438 180.449219 L 371 161.191406 " transform="matrix(1,0,0,1,-326,-37)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:butt;stroke-linejoin:miter;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 369.757812 157.390625 L 371 161.191406 M 369.574219 161.65625 L 369.757812 157.390625 L 372.425781 160.726562 " transform="matrix(1,0,0,1,-326,-37)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 383.300781 180.703125 L 391.71875 160.765625 " transform="matrix(1,0,0,1,-326,-37)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:butt;stroke-linejoin:miter;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 393.273438 157.078125 L 391.71875 160.765625 M 390.335938 160.179688 L 393.273438 157.078125 L 393.101562 161.347656 " transform="matrix(1,0,0,1,-326,-37)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 386.503906 183.046875 L 417.195312 156.816406 " transform="matrix(1,0,0,1,-326,-37)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:butt;stroke-linejoin:miter;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 420.238281 154.21875 L 417.195312 156.816406 M 416.222656 155.675781 L 420.238281 154.21875 L 418.171875 157.957031 " transform="matrix(1,0,0,1,-326,-37)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 387.597656 184.664062 L 446.226562 154.09375 " transform="matrix(1,0,0,1,-326,-37)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:butt;stroke-linejoin:miter;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 449.773438 152.246094 L 446.226562 154.09375 M 445.535156 152.765625 L 449.773438 152.246094 L 446.921875 155.425781 " transform="matrix(1,0,0,1,-326,-37)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 408.050781 184.699219 L 348.542969 154.035156 " transform="matrix(1,0,0,1,-326,-37)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:butt;stroke-linejoin:miter;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 344.988281 152.203125 L 348.542969 154.035156 M 347.855469 155.367188 L 344.988281 152.203125 L 349.230469 152.699219 " transform="matrix(1,0,0,1,-326,-37)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 409.109375 183.109375 L 377.628906 156.710938 " transform="matrix(1,0,0,1,-326,-37)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:butt;stroke-linejoin:miter;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 374.566406 154.144531 L 377.628906 156.710938 M 376.667969 157.863281 L 374.566406 154.144531 L 378.59375 155.5625 " transform="matrix(1,0,0,1,-326,-37)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 412.214844 180.769531 L 403.273438 160.65625 " transform="matrix(1,0,0,1,-326,-37)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:butt;stroke-linejoin:miter;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 401.648438 157 L 403.273438 160.65625 M 401.902344 161.265625 L 401.648438 157 L 404.644531 160.046875 " transform="matrix(1,0,0,1,-326,-37)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 418.226562 180.394531 L 424.023438 161.28125 " transform="matrix(1,0,0,1,-326,-37)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:butt;stroke-linejoin:miter;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 425.183594 157.453125 L 424.023438 161.28125 M 422.585938 160.847656 L 425.183594 157.453125 L 425.457031 161.71875 " transform="matrix(1,0,0,1,-326,-37)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 421.972656 182.707031 L 448.585938 157.390625 " transform="matrix(1,0,0,1,-326,-37)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:butt;stroke-linejoin:miter;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 451.484375 154.632812 L 448.585938 157.390625 M 447.550781 156.304688 L 451.484375 154.632812 L 449.617188 158.476562 " transform="matrix(1,0,0,1,-326,-37)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 443.359375 185.28125 L 349.332031 152.199219 " transform="matrix(1,0,0,1,-326,-37)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:butt;stroke-linejoin:miter;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 345.558594 150.875 L 349.332031 152.199219 M 348.835938 153.617188 L 345.558594 150.875 L 349.832031 150.785156 " transform="matrix(1,0,0,1,-326,-37)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 443.71875 184.414062 L 379.597656 153.660156 " transform="matrix(1,0,0,1,-326,-37)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:butt;stroke-linejoin:miter;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 375.992188 151.933594 L 379.597656 153.660156 M 378.949219 155.015625 L 375.992188 151.933594 L 380.246094 152.308594 " transform="matrix(1,0,0,1,-326,-37)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 444.605469 182.953125 L 408.972656 156.117188 " transform="matrix(1,0,0,1,-326,-37)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:butt;stroke-linejoin:miter;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 405.777344 153.710938 L 408.972656 156.117188 M 408.070312 157.316406 L 405.777344 153.710938 L 409.875 154.921875 " transform="matrix(1,0,0,1,-326,-37)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 447.222656 180.648438 L 435.441406 160 " transform="matrix(1,0,0,1,-326,-37)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:butt;stroke-linejoin:miter;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 433.457031 156.523438 L 435.441406 160 M 434.136719 160.742188 L 433.457031 156.523438 L 436.742188 159.253906 " transform="matrix(1,0,0,1,-326,-37)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 453.082031 179.664062 L 456.445312 161.660156 " transform="matrix(1,0,0,1,-326,-37)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:butt;stroke-linejoin:miter;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 457.179688 157.726562 L 456.445312 161.660156 M 454.96875 161.382812 L 457.179688 157.726562 L 457.917969 161.933594 " transform="matrix(1,0,0,1,-326,-37)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 540.894531 182.550781 C 544.261719 185.921875 544.261719 191.382812 540.894531 194.753906 C 537.527344 198.121094 532.0625 198.121094 528.695312 194.753906 C 525.324219 191.382812 525.324219 185.921875 528.695312 182.550781 C 532.0625 179.183594 537.527344 179.183594 540.894531 182.550781 " transform="matrix(1,0,0,1,-326,-37)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 576.671875 182.550781 C 580.039062 185.921875 580.039062 191.382812 576.671875 194.753906 C 573.300781 198.121094 567.839844 198.121094 564.46875 194.753906 C 561.101562 191.382812 561.101562 185.921875 564.46875 182.550781 C 567.839844 179.183594 573.300781 179.183594 576.671875 182.550781 " transform="matrix(1,0,0,1,-326,-37)"/>
<path style="fill-rule:nonzero;fill:rgb(39.99939%,74.902344%,100%);fill-opacity:1;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 563.222656 141.277344 C 566.59375 144.648438 566.59375 150.109375 563.222656 153.480469 C 559.855469 156.847656 554.390625 156.847656 551.023438 153.480469 C 547.652344 150.109375 547.652344 144.648438 551.023438 141.277344 C 554.390625 137.910156 559.855469 137.910156 563.222656 141.277344 " transform="matrix(1,0,0,1,-326,-37)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 612.449219 182.550781 C 615.816406 185.921875 615.816406 191.382812 612.449219 194.753906 C 609.078125 198.121094 603.617188 198.121094 600.246094 194.753906 C 596.878906 191.382812 596.878906 185.921875 600.246094 182.550781 C 603.617188 179.183594 609.078125 179.183594 612.449219 182.550781 " transform="matrix(1,0,0,1,-326,-37)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 648.222656 182.046875 C 651.59375 185.414062 651.59375 190.875 648.222656 194.246094 C 644.855469 197.613281 639.390625 197.613281 636.023438 194.246094 C 632.65625 190.875 632.65625 185.414062 636.023438 182.046875 C 639.390625 178.675781 644.855469 178.675781 648.222656 182.046875 " transform="matrix(1,0,0,1,-326,-37)"/>
<path style="fill-rule:nonzero;fill:rgb(39.99939%,74.902344%,100%);fill-opacity:1;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 624.964844 141.277344 C 628.332031 144.648438 628.332031 150.109375 624.964844 153.480469 C 621.597656 156.847656 616.132812 156.847656 612.765625 153.480469 C 609.394531 150.109375 609.394531 144.648438 612.765625 141.277344 C 616.132812 137.910156 621.597656 137.910156 624.964844 141.277344 " transform="matrix(1,0,0,1,-326,-37)"/>
<path style="fill-rule:nonzero;fill:rgb(39.99939%,74.902344%,100%);fill-opacity:1;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 594.09375 141.277344 C 597.464844 144.648438 597.464844 150.109375 594.09375 153.480469 C 590.726562 156.847656 585.261719 156.847656 581.894531 153.480469 C 578.523438 150.109375 578.523438 144.648438 581.894531 141.277344 C 585.261719 137.910156 590.726562 137.910156 594.09375 141.277344 " transform="matrix(1,0,0,1,-326,-37)"/>
<path style="fill-rule:nonzero;fill:rgb(69.804382%,85.098267%,100%);fill-opacity:1;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 558.316406 100.003906 C 561.6875 103.375 561.6875 108.835938 558.316406 112.203125 C 554.949219 115.574219 549.484375 115.574219 546.117188 112.203125 C 542.75 108.835938 542.75 103.375 546.117188 100.003906 C 549.484375 96.636719 554.949219 96.636719 558.316406 100.003906 " transform="matrix(1,0,0,1,-326,-37)"/>
<path style="fill-rule:nonzero;fill:rgb(69.804382%,85.098267%,100%);fill-opacity:1;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 594.09375 100.003906 C 597.464844 103.375 597.464844 108.835938 594.09375 112.203125 C 590.726562 115.574219 585.261719 115.574219 581.894531 112.203125 C 578.523438 108.835938 578.523438 103.375 581.894531 100.003906 C 585.261719 96.636719 590.726562 96.636719 594.09375 100.003906 " transform="matrix(1,0,0,1,-326,-37)"/>
<path style="fill-rule:nonzero;fill:rgb(69.804382%,85.098267%,100%);fill-opacity:1;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 629.871094 99.496094 C 633.238281 102.867188 633.238281 108.328125 629.871094 111.699219 C 626.5 115.066406 621.039062 115.066406 617.671875 111.699219 C 614.300781 108.328125 614.300781 102.867188 617.671875 99.496094 C 621.039062 96.128906 626.5 96.128906 629.871094 99.496094 " transform="matrix(1,0,0,1,-326,-37)"/>
<path style="fill-rule:nonzero;fill:rgb(39.99939%,74.902344%,100%);fill-opacity:1;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 532.351562 141.277344 C 535.722656 144.648438 535.722656 150.109375 532.351562 153.480469 C 528.984375 156.847656 523.519531 156.847656 520.152344 153.480469 C 516.78125 150.109375 516.78125 144.648438 520.152344 141.277344 C 523.519531 137.910156 528.984375 137.910156 532.351562 141.277344 " transform="matrix(1,0,0,1,-326,-37)"/>
<path style="fill-rule:nonzero;fill:rgb(39.99939%,74.902344%,100%);fill-opacity:1;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 655.835938 141.277344 C 659.203125 144.648438 659.203125 150.109375 655.835938 153.480469 C 652.464844 156.847656 647.003906 156.847656 643.636719 153.480469 C 640.265625 150.109375 640.265625 144.648438 643.636719 141.277344 C 647.003906 137.910156 652.464844 137.910156 655.835938 141.277344 " transform="matrix(1,0,0,1,-326,-37)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 530.847656 140.074219 L 544.480469 118.402344 " transform="matrix(1,0,0,1,-326,-37)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:butt;stroke-linejoin:miter;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 546.609375 115.015625 L 544.480469 118.402344 M 543.210938 117.605469 L 546.609375 115.015625 L 545.75 119.203125 " transform="matrix(1,0,0,1,-326,-37)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 533.425781 142.582031 L 575.914062 114.179688 " transform="matrix(1,0,0,1,-326,-37)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:butt;stroke-linejoin:miter;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 579.242188 111.957031 L 575.914062 114.179688 M 575.082031 112.933594 L 579.242188 111.957031 L 576.75 115.425781 " transform="matrix(1,0,0,1,-326,-37)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 534.183594 143.980469 L 610.414062 111.320312 " transform="matrix(1,0,0,1,-326,-37)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:butt;stroke-linejoin:miter;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 614.09375 109.742188 L 610.414062 111.320312 M 609.824219 109.941406 L 614.09375 109.742188 L 611.003906 112.699219 " transform="matrix(1,0,0,1,-326,-37)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 556.105469 138.8125 L 553.933594 120.53125 " transform="matrix(1,0,0,1,-326,-37)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:butt;stroke-linejoin:miter;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 553.460938 116.558594 L 553.933594 120.53125 M 552.441406 120.707031 L 553.460938 116.558594 L 555.421875 120.351562 " transform="matrix(1,0,0,1,-326,-37)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 562.289062 140.46875 L 579.292969 117.738281 " transform="matrix(1,0,0,1,-326,-37)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:butt;stroke-linejoin:miter;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 581.6875 114.535156 L 579.292969 117.738281 M 578.089844 116.839844 L 581.6875 114.535156 L 580.492188 118.636719 " transform="matrix(1,0,0,1,-326,-37)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 564.433594 142.796875 L 611.460938 113.3125 " transform="matrix(1,0,0,1,-326,-37)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:butt;stroke-linejoin:miter;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 614.847656 111.191406 L 611.460938 113.3125 M 610.664062 112.042969 L 614.847656 111.191406 L 612.257812 114.585938 " transform="matrix(1,0,0,1,-326,-37)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 582.34375 140.859375 L 561.734375 117.082031 " transform="matrix(1,0,0,1,-326,-37)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:butt;stroke-linejoin:miter;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 559.113281 114.058594 L 561.734375 117.082031 M 560.597656 118.066406 L 559.113281 114.058594 L 562.867188 116.101562 " transform="matrix(1,0,0,1,-326,-37)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 587.992188 138.75 L 587.992188 120.632812 " transform="matrix(1,0,0,1,-326,-37)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:butt;stroke-linejoin:miter;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 587.992188 116.632812 L 587.992188 120.632812 M 586.492188 120.632812 L 587.992188 116.632812 L 589.492188 120.632812 " transform="matrix(1,0,0,1,-326,-37)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 593.605469 140.824219 L 614.320312 116.632812 " transform="matrix(1,0,0,1,-326,-37)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:butt;stroke-linejoin:miter;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 616.921875 113.59375 L 614.320312 116.632812 M 613.183594 115.65625 L 616.921875 113.59375 L 615.460938 117.609375 " transform="matrix(1,0,0,1,-326,-37)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 611.527344 142.835938 L 564.570312 113.753906 " transform="matrix(1,0,0,1,-326,-37)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:butt;stroke-linejoin:miter;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 561.167969 111.648438 L 564.570312 113.753906 M 563.78125 115.03125 L 561.167969 111.648438 L 565.359375 112.480469 " transform="matrix(1,0,0,1,-326,-37)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 613.695312 140.46875 L 596.695312 117.738281 " transform="matrix(1,0,0,1,-326,-37)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:butt;stroke-linejoin:miter;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 594.300781 114.535156 L 596.695312 117.738281 M 595.492188 118.636719 L 594.300781 114.535156 L 597.898438 116.839844 " transform="matrix(1,0,0,1,-326,-37)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 619.871094 138.808594 L 622.074219 120.027344 " transform="matrix(1,0,0,1,-326,-37)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:butt;stroke-linejoin:miter;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 622.542969 116.054688 L 622.074219 120.027344 M 620.585938 119.851562 L 622.542969 116.054688 L 623.566406 120.199219 " transform="matrix(1,0,0,1,-326,-37)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 641.789062 144.015625 L 565.597656 111.769531 " transform="matrix(1,0,0,1,-326,-37)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:butt;stroke-linejoin:miter;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 561.914062 110.207031 L 565.597656 111.769531 M 565.011719 113.148438 L 561.914062 110.207031 L 566.183594 110.386719 " transform="matrix(1,0,0,1,-326,-37)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 642.5625 142.582031 L 600.070312 114.179688 " transform="matrix(1,0,0,1,-326,-37)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:butt;stroke-linejoin:miter;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 596.746094 111.957031 L 600.070312 114.179688 M 599.238281 115.425781 L 596.746094 111.957031 L 600.90625 112.933594 " transform="matrix(1,0,0,1,-326,-37)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 645.179688 140.050781 L 631.4375 117.9375 " transform="matrix(1,0,0,1,-326,-37)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:butt;stroke-linejoin:miter;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 629.328125 114.539062 L 631.4375 117.9375 M 630.164062 118.730469 L 629.328125 114.539062 L 632.714844 117.144531 " transform="matrix(1,0,0,1,-326,-37)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 533.046875 180.203125 L 529.195312 161.605469 " transform="matrix(1,0,0,1,-326,-37)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:butt;stroke-linejoin:miter;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 528.386719 157.6875 L 529.195312 161.605469 M 527.726562 161.910156 L 528.386719 157.6875 L 530.664062 161.300781 " transform="matrix(1,0,0,1,-326,-37)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 538.898438 181.0625 L 550.210938 160.15625 " transform="matrix(1,0,0,1,-326,-37)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:butt;stroke-linejoin:miter;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 552.113281 156.640625 L 550.210938 160.15625 M 548.890625 159.445312 L 552.113281 156.640625 L 551.527344 160.871094 " transform="matrix(1,0,0,1,-326,-37)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 541.609375 183.363281 L 576.515625 156.285156 " transform="matrix(1,0,0,1,-326,-37)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:butt;stroke-linejoin:miter;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 579.675781 153.832031 L 576.515625 156.285156 M 575.597656 155.097656 L 579.675781 153.832031 L 577.433594 157.46875 " transform="matrix(1,0,0,1,-326,-37)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 542.539062 184.851562 L 605.824219 153.78125 " transform="matrix(1,0,0,1,-326,-37)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:butt;stroke-linejoin:miter;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 609.414062 152.019531 L 605.824219 153.78125 M 605.160156 152.433594 L 609.414062 152.019531 L 606.484375 155.128906 " transform="matrix(1,0,0,1,-326,-37)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 542.914062 185.738281 L 636.0625 152.289062 " transform="matrix(1,0,0,1,-326,-37)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:butt;stroke-linejoin:miter;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 639.824219 150.9375 L 636.0625 152.289062 M 635.554688 150.878906 L 639.824219 150.9375 L 636.570312 153.699219 " transform="matrix(1,0,0,1,-326,-37)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 564.257812 182.773438 L 536.882812 157.277344 " transform="matrix(1,0,0,1,-326,-37)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:butt;stroke-linejoin:miter;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 533.957031 154.554688 L 536.882812 157.277344 M 535.859375 158.375 L 533.957031 154.554688 L 537.90625 156.179688 " transform="matrix(1,0,0,1,-326,-37)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 567.898438 180.449219 L 561.625 161.191406 " transform="matrix(1,0,0,1,-326,-37)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:butt;stroke-linejoin:miter;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 560.382812 157.390625 L 561.625 161.191406 M 560.199219 161.65625 L 560.382812 157.390625 L 563.050781 160.726562 " transform="matrix(1,0,0,1,-326,-37)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 573.925781 180.703125 L 582.34375 160.765625 " transform="matrix(1,0,0,1,-326,-37)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:butt;stroke-linejoin:miter;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 583.898438 157.078125 L 582.34375 160.765625 M 580.960938 160.179688 L 583.898438 157.078125 L 583.726562 161.347656 " transform="matrix(1,0,0,1,-326,-37)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 577.128906 183.046875 L 607.820312 156.816406 " transform="matrix(1,0,0,1,-326,-37)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:butt;stroke-linejoin:miter;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 610.863281 154.21875 L 607.820312 156.816406 M 606.847656 155.675781 L 610.863281 154.21875 L 608.796875 157.957031 " transform="matrix(1,0,0,1,-326,-37)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 578.222656 184.664062 L 636.851562 154.09375 " transform="matrix(1,0,0,1,-326,-37)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:butt;stroke-linejoin:miter;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 640.398438 152.246094 L 636.851562 154.09375 M 636.160156 152.765625 L 640.398438 152.246094 L 637.546875 155.425781 " transform="matrix(1,0,0,1,-326,-37)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 598.675781 184.699219 L 539.167969 154.035156 " transform="matrix(1,0,0,1,-326,-37)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:butt;stroke-linejoin:miter;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 535.613281 152.203125 L 539.167969 154.035156 M 538.480469 155.367188 L 535.613281 152.203125 L 539.855469 152.699219 " transform="matrix(1,0,0,1,-326,-37)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 599.734375 183.109375 L 568.253906 156.710938 " transform="matrix(1,0,0,1,-326,-37)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:butt;stroke-linejoin:miter;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 565.191406 154.144531 L 568.253906 156.710938 M 567.292969 157.863281 L 565.191406 154.144531 L 569.21875 155.5625 " transform="matrix(1,0,0,1,-326,-37)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 602.839844 180.769531 L 593.898438 160.65625 " transform="matrix(1,0,0,1,-326,-37)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:butt;stroke-linejoin:miter;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 592.273438 157 L 593.898438 160.65625 M 592.527344 161.265625 L 592.273438 157 L 595.269531 160.046875 " transform="matrix(1,0,0,1,-326,-37)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 608.851562 180.394531 L 614.648438 161.28125 " transform="matrix(1,0,0,1,-326,-37)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:butt;stroke-linejoin:miter;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 615.808594 157.453125 L 614.648438 161.28125 M 613.210938 160.847656 L 615.808594 157.453125 L 616.082031 161.71875 " transform="matrix(1,0,0,1,-326,-37)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 612.597656 182.707031 L 639.210938 157.390625 " transform="matrix(1,0,0,1,-326,-37)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:butt;stroke-linejoin:miter;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 642.109375 154.632812 L 639.210938 157.390625 M 638.175781 156.304688 L 642.109375 154.632812 L 640.242188 158.476562 " transform="matrix(1,0,0,1,-326,-37)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 633.984375 185.28125 L 539.957031 152.199219 " transform="matrix(1,0,0,1,-326,-37)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:butt;stroke-linejoin:miter;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 536.183594 150.875 L 539.957031 152.199219 M 539.460938 153.617188 L 536.183594 150.875 L 540.457031 150.785156 " transform="matrix(1,0,0,1,-326,-37)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 634.34375 184.414062 L 570.222656 153.660156 " transform="matrix(1,0,0,1,-326,-37)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:butt;stroke-linejoin:miter;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 566.617188 151.933594 L 570.222656 153.660156 M 569.574219 155.015625 L 566.617188 151.933594 L 570.871094 152.308594 " transform="matrix(1,0,0,1,-326,-37)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 635.230469 182.953125 L 599.597656 156.117188 " transform="matrix(1,0,0,1,-326,-37)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:butt;stroke-linejoin:miter;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 596.402344 153.710938 L 599.597656 156.117188 M 598.695312 157.316406 L 596.402344 153.710938 L 600.5 154.921875 " transform="matrix(1,0,0,1,-326,-37)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 637.847656 180.648438 L 626.066406 160 " transform="matrix(1,0,0,1,-326,-37)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:butt;stroke-linejoin:miter;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 624.082031 156.523438 L 626.066406 160 M 624.761719 160.742188 L 624.082031 156.523438 L 627.367188 159.253906 " transform="matrix(1,0,0,1,-326,-37)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 643.707031 179.664062 L 647.070312 161.660156 " transform="matrix(1,0,0,1,-326,-37)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:butt;stroke-linejoin:miter;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 647.804688 157.726562 L 647.070312 161.660156 M 645.59375 161.382812 L 647.804688 157.726562 L 648.542969 161.933594 " transform="matrix(1,0,0,1,-326,-37)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 509.886719 89.78125 L 666.101562 89.78125 L 666.101562 204.46875 L 509.886719 204.46875 Z M 509.886719 89.78125 " transform="matrix(1,0,0,1,-326,-37)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 777.9375 244.621094 C 779.699219 246.378906 779.699219 249.230469 777.9375 250.988281 C 776.179688 252.75 773.328125 252.75 771.570312 250.988281 C 769.808594 249.230469 769.808594 246.378906 771.570312 244.621094 C 773.328125 242.863281 776.179688 242.863281 777.9375 244.621094 " transform="matrix(1,0,0,1,-326,-37)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 796.617188 244.621094 C 798.375 246.378906 798.375 249.230469 796.617188 250.988281 C 794.859375 252.75 792.003906 252.75 790.246094 250.988281 C 788.488281 249.230469 788.488281 246.378906 790.246094 244.621094 C 792.003906 242.863281 794.859375 242.863281 796.617188 244.621094 " transform="matrix(1,0,0,1,-326,-37)"/>
<path style="fill-rule:nonzero;fill:rgb(39.99939%,74.902344%,100%);fill-opacity:1;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 789.59375 223.074219 C 791.355469 224.832031 791.355469 227.683594 789.59375 229.441406 C 787.835938 231.199219 784.984375 231.199219 783.226562 229.441406 C 781.46875 227.683594 781.46875 224.832031 783.226562 223.074219 C 784.984375 221.3125 787.835938 221.3125 789.59375 223.074219 " transform="matrix(1,0,0,1,-326,-37)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 815.292969 244.621094 C 817.054688 246.378906 817.054688 249.230469 815.292969 250.988281 C 813.535156 252.75 810.683594 252.75 808.925781 250.988281 C 807.164062 249.230469 807.164062 246.378906 808.925781 244.621094 C 810.683594 242.863281 813.535156 242.863281 815.292969 244.621094 " transform="matrix(1,0,0,1,-326,-37)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 833.972656 244.355469 C 835.730469 246.113281 835.730469 248.964844 833.972656 250.726562 C 832.214844 252.484375 829.363281 252.484375 827.601562 250.726562 C 825.84375 248.964844 825.84375 246.113281 827.601562 244.355469 C 829.363281 242.597656 832.214844 242.597656 833.972656 244.355469 " transform="matrix(1,0,0,1,-326,-37)"/>
<path style="fill-rule:nonzero;fill:rgb(39.99939%,74.902344%,100%);fill-opacity:1;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 821.828125 223.074219 C 823.589844 224.832031 823.589844 227.683594 821.828125 229.441406 C 820.070312 231.199219 817.21875 231.199219 815.460938 229.441406 C 813.699219 227.683594 813.699219 224.832031 815.460938 223.074219 C 817.21875 221.3125 820.070312 221.3125 821.828125 223.074219 " transform="matrix(1,0,0,1,-326,-37)"/>
<path style="fill-rule:nonzero;fill:rgb(39.99939%,74.902344%,100%);fill-opacity:1;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 805.710938 223.074219 C 807.472656 224.832031 807.472656 227.683594 805.710938 229.441406 C 803.953125 231.199219 801.101562 231.199219 799.34375 229.441406 C 797.585938 227.683594 797.585938 224.832031 799.34375 223.074219 C 801.101562 221.3125 803.953125 221.3125 805.710938 223.074219 " transform="matrix(1,0,0,1,-326,-37)"/>
<path style="fill-rule:nonzero;fill:rgb(69.804382%,85.098267%,100%);fill-opacity:1;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 787.035156 201.523438 C 788.792969 203.285156 788.792969 206.136719 787.035156 207.894531 C 785.277344 209.652344 782.425781 209.652344 780.664062 207.894531 C 778.90625 206.136719 778.90625 203.285156 780.664062 201.523438 C 782.425781 199.765625 785.277344 199.765625 787.035156 201.523438 " transform="matrix(1,0,0,1,-326,-37)"/>
<path style="fill-rule:nonzero;fill:rgb(69.804382%,85.098267%,100%);fill-opacity:1;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 805.710938 201.523438 C 807.472656 203.285156 807.472656 206.136719 805.710938 207.894531 C 803.953125 209.652344 801.101562 209.652344 799.34375 207.894531 C 797.585938 206.136719 797.585938 203.285156 799.34375 201.523438 C 801.101562 199.765625 803.953125 199.765625 805.710938 201.523438 " transform="matrix(1,0,0,1,-326,-37)"/>
<path style="fill-rule:nonzero;fill:rgb(69.804382%,85.098267%,100%);fill-opacity:1;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 824.390625 201.261719 C 826.148438 203.019531 826.148438 205.871094 824.390625 207.628906 C 822.632812 209.386719 819.78125 209.386719 818.019531 207.628906 C 816.261719 205.871094 816.261719 203.019531 818.019531 201.261719 C 819.78125 199.5 822.632812 199.5 824.390625 201.261719 " transform="matrix(1,0,0,1,-326,-37)"/>
<path style="fill-rule:nonzero;fill:rgb(39.99939%,74.902344%,100%);fill-opacity:1;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 773.480469 223.074219 C 775.238281 224.832031 775.238281 227.683594 773.480469 229.441406 C 771.71875 231.199219 768.867188 231.199219 767.109375 229.441406 C 765.351562 227.683594 765.351562 224.832031 767.109375 223.074219 C 768.867188 221.3125 771.71875 221.3125 773.480469 223.074219 " transform="matrix(1,0,0,1,-326,-37)"/>
<path style="fill-rule:nonzero;fill:rgb(39.99939%,74.902344%,100%);fill-opacity:1;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 837.945312 223.074219 C 839.703125 224.832031 839.703125 227.683594 837.945312 229.441406 C 836.1875 231.199219 833.335938 231.199219 831.578125 229.441406 C 829.816406 227.683594 829.816406 224.832031 831.578125 223.074219 C 833.335938 221.3125 836.1875 221.3125 837.945312 223.074219 " transform="matrix(1,0,0,1,-326,-37)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 772.691406 222.445312 L 778.308594 213.515625 " transform="matrix(1,0,0,1,-326,-37)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:butt;stroke-linejoin:miter;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 780.4375 210.128906 L 778.308594 213.515625 M 777.039062 212.71875 L 780.4375 210.128906 L 779.578125 214.316406 " transform="matrix(1,0,0,1,-326,-37)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 774.039062 223.753906 L 793.878906 210.492188 " transform="matrix(1,0,0,1,-326,-37)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:butt;stroke-linejoin:miter;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 797.203125 208.269531 L 793.878906 210.492188 M 793.042969 209.246094 L 797.203125 208.269531 L 794.710938 211.738281 " transform="matrix(1,0,0,1,-326,-37)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 774.433594 224.484375 L 811.640625 208.542969 " transform="matrix(1,0,0,1,-326,-37)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:butt;stroke-linejoin:miter;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 815.316406 206.96875 L 811.640625 208.542969 M 811.050781 207.164062 L 815.316406 206.96875 L 812.230469 209.921875 " transform="matrix(1,0,0,1,-326,-37)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 785.878906 221.785156 L 785.078125 215.039062 " transform="matrix(1,0,0,1,-326,-37)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:butt;stroke-linejoin:miter;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 784.605469 211.070312 L 785.078125 215.039062 M 783.589844 215.21875 L 784.605469 211.070312 L 786.566406 214.863281 " transform="matrix(1,0,0,1,-326,-37)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 789.109375 222.652344 L 796.296875 213.042969 " transform="matrix(1,0,0,1,-326,-37)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:butt;stroke-linejoin:miter;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 798.691406 209.839844 L 796.296875 213.042969 M 795.09375 212.144531 L 798.691406 209.839844 L 797.496094 213.941406 " transform="matrix(1,0,0,1,-326,-37)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 790.226562 223.863281 L 812.390625 209.972656 " transform="matrix(1,0,0,1,-326,-37)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:butt;stroke-linejoin:miter;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 815.777344 207.847656 L 812.390625 209.972656 M 811.59375 208.699219 L 815.777344 207.847656 L 813.1875 211.242188 " transform="matrix(1,0,0,1,-326,-37)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 799.578125 222.855469 L 790.664062 212.570312 " transform="matrix(1,0,0,1,-326,-37)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:butt;stroke-linejoin:miter;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 788.042969 209.546875 L 790.664062 212.570312 M 789.53125 213.554688 L 788.042969 209.546875 L 791.796875 211.589844 " transform="matrix(1,0,0,1,-326,-37)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 802.527344 221.753906 L 802.527344 215.113281 " transform="matrix(1,0,0,1,-326,-37)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:butt;stroke-linejoin:miter;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 802.527344 211.113281 L 802.527344 215.113281 M 801.027344 215.113281 L 802.527344 211.113281 L 804.027344 215.113281 " transform="matrix(1,0,0,1,-326,-37)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 805.457031 222.835938 L 814.4375 212.347656 " transform="matrix(1,0,0,1,-326,-37)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:butt;stroke-linejoin:miter;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 817.039062 209.308594 L 814.4375 212.347656 M 813.300781 211.371094 L 817.039062 209.308594 L 815.578125 213.324219 " transform="matrix(1,0,0,1,-326,-37)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 814.816406 223.886719 L 792.695312 210.1875 " transform="matrix(1,0,0,1,-326,-37)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:butt;stroke-linejoin:miter;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 789.296875 208.082031 L 792.695312 210.1875 M 791.90625 211.464844 L 789.296875 208.082031 L 793.484375 208.914062 " transform="matrix(1,0,0,1,-326,-37)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 815.945312 222.652344 L 808.757812 213.042969 " transform="matrix(1,0,0,1,-326,-37)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:butt;stroke-linejoin:miter;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 806.363281 209.839844 L 808.757812 213.042969 M 807.558594 213.941406 L 806.363281 209.839844 L 809.960938 212.144531 " transform="matrix(1,0,0,1,-326,-37)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 819.167969 221.785156 L 819.992188 214.777344 " transform="matrix(1,0,0,1,-326,-37)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:butt;stroke-linejoin:miter;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 820.457031 210.804688 L 819.992188 214.777344 M 818.503906 214.601562 L 820.457031 210.804688 L 821.480469 214.953125 " transform="matrix(1,0,0,1,-326,-37)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 830.613281 224.5 L 793.433594 208.765625 " transform="matrix(1,0,0,1,-326,-37)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:butt;stroke-linejoin:miter;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 789.75 207.207031 L 793.433594 208.765625 M 792.847656 210.148438 L 789.75 207.207031 L 794.015625 207.382812 " transform="matrix(1,0,0,1,-326,-37)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 831.015625 223.753906 L 811.175781 210.492188 " transform="matrix(1,0,0,1,-326,-37)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:butt;stroke-linejoin:miter;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 807.851562 208.269531 L 811.175781 210.492188 M 810.34375 211.738281 L 807.851562 208.269531 L 812.011719 209.246094 " transform="matrix(1,0,0,1,-326,-37)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 832.382812 222.429688 L 826.699219 213.28125 " transform="matrix(1,0,0,1,-326,-37)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:butt;stroke-linejoin:miter;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 824.585938 209.882812 L 826.699219 213.28125 M 825.421875 214.074219 L 824.585938 209.882812 L 827.972656 212.488281 " transform="matrix(1,0,0,1,-326,-37)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 773.839844 243.394531 L 772.402344 236.445312 " transform="matrix(1,0,0,1,-326,-37)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:butt;stroke-linejoin:miter;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 771.59375 232.527344 L 772.402344 236.445312 M 770.933594 236.75 L 771.59375 232.527344 L 773.871094 236.140625 " transform="matrix(1,0,0,1,-326,-37)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 776.898438 243.84375 L 781.460938 235.410156 " transform="matrix(1,0,0,1,-326,-37)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:butt;stroke-linejoin:miter;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 783.363281 231.890625 L 781.460938 235.410156 M 780.140625 234.695312 L 783.363281 231.890625 L 782.78125 236.121094 " transform="matrix(1,0,0,1,-326,-37)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 778.3125 245.042969 L 794.308594 232.636719 " transform="matrix(1,0,0,1,-326,-37)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:butt;stroke-linejoin:miter;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 797.46875 230.183594 L 794.308594 232.636719 M 793.386719 231.449219 L 797.46875 230.183594 L 795.226562 233.820312 " transform="matrix(1,0,0,1,-326,-37)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 778.796875 245.820312 L 809.304688 230.84375 " transform="matrix(1,0,0,1,-326,-37)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:butt;stroke-linejoin:miter;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 812.894531 229.082031 L 809.304688 230.84375 M 808.644531 229.496094 L 812.894531 229.082031 L 809.964844 232.191406 " transform="matrix(1,0,0,1,-326,-37)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 778.992188 246.28125 L 824.96875 229.773438 " transform="matrix(1,0,0,1,-326,-37)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:butt;stroke-linejoin:miter;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 828.734375 228.421875 L 824.96875 229.773438 M 824.460938 228.363281 L 828.734375 228.421875 L 825.476562 231.1875 " transform="matrix(1,0,0,1,-326,-37)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 790.136719 244.734375 L 777.90625 233.347656 " transform="matrix(1,0,0,1,-326,-37)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:butt;stroke-linejoin:miter;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 774.980469 230.621094 L 777.90625 233.347656 M 776.886719 234.445312 L 774.980469 230.621094 L 778.929688 232.25 " transform="matrix(1,0,0,1,-326,-37)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 792.035156 243.523438 L 789.632812 236.152344 " transform="matrix(1,0,0,1,-326,-37)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:butt;stroke-linejoin:miter;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 788.394531 232.347656 L 789.632812 236.152344 M 788.207031 236.613281 L 788.394531 232.347656 L 791.058594 235.6875 " transform="matrix(1,0,0,1,-326,-37)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 795.183594 243.65625 L 798.480469 235.84375 " transform="matrix(1,0,0,1,-326,-37)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:butt;stroke-linejoin:miter;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 800.035156 232.160156 L 798.480469 235.84375 M 797.097656 235.261719 L 800.035156 232.160156 L 799.863281 236.425781 " transform="matrix(1,0,0,1,-326,-37)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 796.855469 244.878906 L 810.734375 233.015625 " transform="matrix(1,0,0,1,-326,-37)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:butt;stroke-linejoin:miter;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 813.777344 230.417969 L 810.734375 233.015625 M 809.761719 231.875 L 813.777344 230.417969 L 811.710938 234.15625 " transform="matrix(1,0,0,1,-326,-37)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 797.425781 245.722656 L 825.535156 231.066406 " transform="matrix(1,0,0,1,-326,-37)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:butt;stroke-linejoin:miter;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 829.082031 229.21875 L 825.535156 231.066406 M 824.839844 229.738281 L 829.082031 229.21875 L 826.226562 232.398438 " transform="matrix(1,0,0,1,-326,-37)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 808.105469 245.742188 L 779.542969 231.023438 " transform="matrix(1,0,0,1,-326,-37)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:butt;stroke-linejoin:miter;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 775.988281 229.191406 L 779.542969 231.023438 M 778.855469 232.355469 L 775.988281 229.191406 L 780.230469 229.691406 " transform="matrix(1,0,0,1,-326,-37)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 808.65625 244.910156 L 794.382812 232.941406 " transform="matrix(1,0,0,1,-326,-37)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:butt;stroke-linejoin:miter;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 791.316406 230.371094 L 794.382812 232.941406 M 793.417969 234.089844 L 791.316406 230.371094 L 795.347656 231.792969 " transform="matrix(1,0,0,1,-326,-37)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 810.277344 243.6875 L 806.753906 235.765625 " transform="matrix(1,0,0,1,-326,-37)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:butt;stroke-linejoin:miter;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 805.128906 232.109375 L 806.753906 235.765625 M 805.386719 236.375 L 805.128906 232.109375 L 808.125 235.15625 " transform="matrix(1,0,0,1,-326,-37)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 813.417969 243.496094 L 815.625 236.214844 " transform="matrix(1,0,0,1,-326,-37)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:butt;stroke-linejoin:miter;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 816.785156 232.386719 L 815.625 236.214844 M 814.1875 235.777344 L 816.785156 232.386719 L 817.058594 236.648438 " transform="matrix(1,0,0,1,-326,-37)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 815.371094 244.703125 L 827.222656 233.429688 " transform="matrix(1,0,0,1,-326,-37)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:butt;stroke-linejoin:miter;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 830.121094 230.671875 L 827.222656 233.429688 M 826.1875 232.339844 L 830.121094 230.671875 L 828.257812 234.515625 " transform="matrix(1,0,0,1,-326,-37)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 826.539062 246.046875 L 780.109375 229.710938 " transform="matrix(1,0,0,1,-326,-37)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:butt;stroke-linejoin:miter;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 776.335938 228.382812 L 780.109375 229.710938 M 779.613281 231.125 L 776.335938 228.382812 L 780.605469 228.296875 " transform="matrix(1,0,0,1,-326,-37)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 826.726562 245.59375 L 795.792969 230.757812 " transform="matrix(1,0,0,1,-326,-37)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:butt;stroke-linejoin:miter;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 792.1875 229.027344 L 795.792969 230.757812 M 795.144531 232.109375 L 792.1875 229.027344 L 796.441406 229.40625 " transform="matrix(1,0,0,1,-326,-37)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 827.1875 244.832031 L 810.839844 232.515625 " transform="matrix(1,0,0,1,-326,-37)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:butt;stroke-linejoin:miter;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 807.644531 230.109375 L 810.839844 232.515625 M 809.9375 233.714844 L 807.644531 230.109375 L 811.742188 231.320312 " transform="matrix(1,0,0,1,-326,-37)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 828.554688 243.628906 L 823.800781 235.296875 " transform="matrix(1,0,0,1,-326,-37)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:butt;stroke-linejoin:miter;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 821.820312 231.820312 L 823.800781 235.296875 M 822.496094 236.039062 L 821.820312 231.820312 L 825.105469 234.550781 " transform="matrix(1,0,0,1,-326,-37)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 831.613281 243.113281 L 832.851562 236.484375 " transform="matrix(1,0,0,1,-326,-37)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:butt;stroke-linejoin:miter;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 833.585938 232.554688 L 832.851562 236.484375 M 831.378906 236.210938 L 833.585938 232.554688 L 834.324219 236.761719 " transform="matrix(1,0,0,1,-326,-37)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 761.75 196.1875 L 843.304688 196.1875 L 843.304688 256.0625 L 761.75 256.0625 Z M 761.75 196.1875 " transform="matrix(1,0,0,1,-326,-37)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 777.9375 86.621094 C 779.699219 88.378906 779.699219 91.230469 777.9375 92.988281 C 776.179688 94.75 773.328125 94.75 771.570312 92.988281 C 769.808594 91.230469 769.808594 88.378906 771.570312 86.621094 C 773.328125 84.863281 776.179688 84.863281 777.9375 86.621094 " transform="matrix(1,0,0,1,-326,-37)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 796.617188 86.621094 C 798.375 88.378906 798.375 91.230469 796.617188 92.988281 C 794.859375 94.75 792.003906 94.75 790.246094 92.988281 C 788.488281 91.230469 788.488281 88.378906 790.246094 86.621094 C 792.003906 84.863281 794.859375 84.863281 796.617188 86.621094 " transform="matrix(1,0,0,1,-326,-37)"/>
<path style="fill-rule:nonzero;fill:rgb(39.99939%,74.902344%,100%);fill-opacity:1;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 789.59375 65.074219 C 791.355469 66.832031 791.355469 69.683594 789.59375 71.441406 C 787.835938 73.199219 784.984375 73.199219 783.226562 71.441406 C 781.46875 69.683594 781.46875 66.832031 783.226562 65.074219 C 784.984375 63.3125 787.835938 63.3125 789.59375 65.074219 " transform="matrix(1,0,0,1,-326,-37)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 815.292969 86.621094 C 817.054688 88.378906 817.054688 91.230469 815.292969 92.988281 C 813.535156 94.75 810.683594 94.75 808.925781 92.988281 C 807.164062 91.230469 807.164062 88.378906 808.925781 86.621094 C 810.683594 84.863281 813.535156 84.863281 815.292969 86.621094 " transform="matrix(1,0,0,1,-326,-37)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 833.972656 86.355469 C 835.730469 88.113281 835.730469 90.964844 833.972656 92.726562 C 832.214844 94.484375 829.363281 94.484375 827.601562 92.726562 C 825.84375 90.964844 825.84375 88.113281 827.601562 86.355469 C 829.363281 84.597656 832.214844 84.597656 833.972656 86.355469 " transform="matrix(1,0,0,1,-326,-37)"/>
<path style="fill-rule:nonzero;fill:rgb(39.99939%,74.902344%,100%);fill-opacity:1;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 821.828125 65.074219 C 823.589844 66.832031 823.589844 69.683594 821.828125 71.441406 C 820.070312 73.199219 817.21875 73.199219 815.460938 71.441406 C 813.699219 69.683594 813.699219 66.832031 815.460938 65.074219 C 817.21875 63.3125 820.070312 63.3125 821.828125 65.074219 " transform="matrix(1,0,0,1,-326,-37)"/>
<path style="fill-rule:nonzero;fill:rgb(39.99939%,74.902344%,100%);fill-opacity:1;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 805.710938 65.074219 C 807.472656 66.832031 807.472656 69.683594 805.710938 71.441406 C 803.953125 73.199219 801.101562 73.199219 799.34375 71.441406 C 797.585938 69.683594 797.585938 66.832031 799.34375 65.074219 C 801.101562 63.3125 803.953125 63.3125 805.710938 65.074219 " transform="matrix(1,0,0,1,-326,-37)"/>
<path style="fill-rule:nonzero;fill:rgb(69.804382%,85.098267%,100%);fill-opacity:1;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 787.035156 43.523438 C 788.792969 45.285156 788.792969 48.136719 787.035156 49.894531 C 785.277344 51.652344 782.425781 51.652344 780.664062 49.894531 C 778.90625 48.136719 778.90625 45.285156 780.664062 43.523438 C 782.425781 41.765625 785.277344 41.765625 787.035156 43.523438 " transform="matrix(1,0,0,1,-326,-37)"/>
<path style="fill-rule:nonzero;fill:rgb(69.804382%,85.098267%,100%);fill-opacity:1;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 805.710938 43.523438 C 807.472656 45.285156 807.472656 48.136719 805.710938 49.894531 C 803.953125 51.652344 801.101562 51.652344 799.34375 49.894531 C 797.585938 48.136719 797.585938 45.285156 799.34375 43.523438 C 801.101562 41.765625 803.953125 41.765625 805.710938 43.523438 " transform="matrix(1,0,0,1,-326,-37)"/>
<path style="fill-rule:nonzero;fill:rgb(69.804382%,85.098267%,100%);fill-opacity:1;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 824.390625 43.261719 C 826.148438 45.019531 826.148438 47.871094 824.390625 49.628906 C 822.632812 51.386719 819.78125 51.386719 818.019531 49.628906 C 816.261719 47.871094 816.261719 45.019531 818.019531 43.261719 C 819.78125 41.5 822.632812 41.5 824.390625 43.261719 " transform="matrix(1,0,0,1,-326,-37)"/>
<path style="fill-rule:nonzero;fill:rgb(39.99939%,74.902344%,100%);fill-opacity:1;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 773.480469 65.074219 C 775.238281 66.832031 775.238281 69.683594 773.480469 71.441406 C 771.71875 73.199219 768.867188 73.199219 767.109375 71.441406 C 765.351562 69.683594 765.351562 66.832031 767.109375 65.074219 C 768.867188 63.3125 771.71875 63.3125 773.480469 65.074219 " transform="matrix(1,0,0,1,-326,-37)"/>
<path style="fill-rule:nonzero;fill:rgb(39.99939%,74.902344%,100%);fill-opacity:1;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 837.945312 65.074219 C 839.703125 66.832031 839.703125 69.683594 837.945312 71.441406 C 836.1875 73.199219 833.335938 73.199219 831.578125 71.441406 C 829.816406 69.683594 829.816406 66.832031 831.578125 65.074219 C 833.335938 63.3125 836.1875 63.3125 837.945312 65.074219 " transform="matrix(1,0,0,1,-326,-37)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 772.691406 64.445312 L 778.308594 55.515625 " transform="matrix(1,0,0,1,-326,-37)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:butt;stroke-linejoin:miter;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 780.4375 52.128906 L 778.308594 55.515625 M 777.039062 54.71875 L 780.4375 52.128906 L 779.578125 56.316406 " transform="matrix(1,0,0,1,-326,-37)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 774.039062 65.753906 L 793.878906 52.492188 " transform="matrix(1,0,0,1,-326,-37)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:butt;stroke-linejoin:miter;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 797.203125 50.269531 L 793.878906 52.492188 M 793.042969 51.246094 L 797.203125 50.269531 L 794.710938 53.738281 " transform="matrix(1,0,0,1,-326,-37)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 774.433594 66.484375 L 811.640625 50.542969 " transform="matrix(1,0,0,1,-326,-37)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:butt;stroke-linejoin:miter;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 815.316406 48.96875 L 811.640625 50.542969 M 811.050781 49.164062 L 815.316406 48.96875 L 812.230469 51.921875 " transform="matrix(1,0,0,1,-326,-37)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 785.878906 63.785156 L 785.078125 57.039062 " transform="matrix(1,0,0,1,-326,-37)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:butt;stroke-linejoin:miter;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 784.605469 53.070312 L 785.078125 57.039062 M 783.589844 57.21875 L 784.605469 53.070312 L 786.566406 56.863281 " transform="matrix(1,0,0,1,-326,-37)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 789.109375 64.648438 L 796.296875 55.042969 " transform="matrix(1,0,0,1,-326,-37)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:butt;stroke-linejoin:miter;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 798.691406 51.839844 L 796.296875 55.042969 M 795.09375 54.144531 L 798.691406 51.839844 L 797.496094 55.941406 " transform="matrix(1,0,0,1,-326,-37)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 790.226562 65.863281 L 812.390625 51.972656 " transform="matrix(1,0,0,1,-326,-37)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:butt;stroke-linejoin:miter;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 815.777344 49.847656 L 812.390625 51.972656 M 811.59375 50.699219 L 815.777344 49.847656 L 813.1875 53.242188 " transform="matrix(1,0,0,1,-326,-37)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 799.578125 64.855469 L 790.664062 54.570312 " transform="matrix(1,0,0,1,-326,-37)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:butt;stroke-linejoin:miter;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 788.042969 51.546875 L 790.664062 54.570312 M 789.53125 55.554688 L 788.042969 51.546875 L 791.796875 53.589844 " transform="matrix(1,0,0,1,-326,-37)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 802.527344 63.753906 L 802.527344 57.113281 " transform="matrix(1,0,0,1,-326,-37)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:butt;stroke-linejoin:miter;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 802.527344 53.113281 L 802.527344 57.113281 M 801.027344 57.113281 L 802.527344 53.113281 L 804.027344 57.113281 " transform="matrix(1,0,0,1,-326,-37)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 805.457031 64.835938 L 814.4375 54.347656 " transform="matrix(1,0,0,1,-326,-37)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:butt;stroke-linejoin:miter;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 817.039062 51.308594 L 814.4375 54.347656 M 813.300781 53.371094 L 817.039062 51.308594 L 815.578125 55.324219 " transform="matrix(1,0,0,1,-326,-37)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 814.816406 65.886719 L 792.695312 52.1875 " transform="matrix(1,0,0,1,-326,-37)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:butt;stroke-linejoin:miter;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 789.296875 50.082031 L 792.695312 52.1875 M 791.90625 53.464844 L 789.296875 50.082031 L 793.484375 50.914062 " transform="matrix(1,0,0,1,-326,-37)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 815.945312 64.648438 L 808.757812 55.042969 " transform="matrix(1,0,0,1,-326,-37)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:butt;stroke-linejoin:miter;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 806.363281 51.839844 L 808.757812 55.042969 M 807.558594 55.941406 L 806.363281 51.839844 L 809.960938 54.144531 " transform="matrix(1,0,0,1,-326,-37)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 819.167969 63.785156 L 819.992188 56.777344 " transform="matrix(1,0,0,1,-326,-37)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:butt;stroke-linejoin:miter;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 820.457031 52.804688 L 819.992188 56.777344 M 818.503906 56.601562 L 820.457031 52.804688 L 821.480469 56.953125 " transform="matrix(1,0,0,1,-326,-37)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 830.613281 66.5 L 793.433594 50.765625 " transform="matrix(1,0,0,1,-326,-37)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:butt;stroke-linejoin:miter;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 789.75 49.207031 L 793.433594 50.765625 M 792.847656 52.144531 L 789.75 49.207031 L 794.015625 49.382812 " transform="matrix(1,0,0,1,-326,-37)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 831.015625 65.753906 L 811.175781 52.492188 " transform="matrix(1,0,0,1,-326,-37)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:butt;stroke-linejoin:miter;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 807.851562 50.269531 L 811.175781 52.492188 M 810.34375 53.738281 L 807.851562 50.269531 L 812.011719 51.246094 " transform="matrix(1,0,0,1,-326,-37)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 832.382812 64.429688 L 826.699219 55.28125 " transform="matrix(1,0,0,1,-326,-37)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:butt;stroke-linejoin:miter;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 824.585938 51.882812 L 826.699219 55.28125 M 825.421875 56.074219 L 824.585938 51.882812 L 827.972656 54.488281 " transform="matrix(1,0,0,1,-326,-37)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 773.839844 85.394531 L 772.402344 78.445312 " transform="matrix(1,0,0,1,-326,-37)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:butt;stroke-linejoin:miter;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 771.59375 74.527344 L 772.402344 78.445312 M 770.933594 78.75 L 771.59375 74.527344 L 773.871094 78.140625 " transform="matrix(1,0,0,1,-326,-37)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 776.898438 85.84375 L 781.460938 77.410156 " transform="matrix(1,0,0,1,-326,-37)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:butt;stroke-linejoin:miter;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 783.363281 73.890625 L 781.460938 77.410156 M 780.140625 76.695312 L 783.363281 73.890625 L 782.78125 78.121094 " transform="matrix(1,0,0,1,-326,-37)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 778.3125 87.042969 L 794.308594 74.636719 " transform="matrix(1,0,0,1,-326,-37)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:butt;stroke-linejoin:miter;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 797.46875 72.183594 L 794.308594 74.636719 M 793.386719 73.449219 L 797.46875 72.183594 L 795.226562 75.820312 " transform="matrix(1,0,0,1,-326,-37)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 778.796875 87.820312 L 809.304688 72.84375 " transform="matrix(1,0,0,1,-326,-37)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:butt;stroke-linejoin:miter;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 812.894531 71.082031 L 809.304688 72.84375 M 808.644531 71.496094 L 812.894531 71.082031 L 809.964844 74.191406 " transform="matrix(1,0,0,1,-326,-37)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 778.992188 88.28125 L 824.96875 71.773438 " transform="matrix(1,0,0,1,-326,-37)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:butt;stroke-linejoin:miter;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 828.734375 70.421875 L 824.96875 71.773438 M 824.460938 70.363281 L 828.734375 70.421875 L 825.476562 73.1875 " transform="matrix(1,0,0,1,-326,-37)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 790.136719 86.734375 L 777.90625 75.347656 " transform="matrix(1,0,0,1,-326,-37)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:butt;stroke-linejoin:miter;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 774.980469 72.621094 L 777.90625 75.347656 M 776.886719 76.445312 L 774.980469 72.621094 L 778.929688 74.25 " transform="matrix(1,0,0,1,-326,-37)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 792.035156 85.523438 L 789.632812 78.152344 " transform="matrix(1,0,0,1,-326,-37)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:butt;stroke-linejoin:miter;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 788.394531 74.347656 L 789.632812 78.152344 M 788.207031 78.613281 L 788.394531 74.347656 L 791.058594 77.6875 " transform="matrix(1,0,0,1,-326,-37)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 795.183594 85.65625 L 798.480469 77.84375 " transform="matrix(1,0,0,1,-326,-37)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:butt;stroke-linejoin:miter;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 800.035156 74.160156 L 798.480469 77.84375 M 797.097656 77.261719 L 800.035156 74.160156 L 799.863281 78.425781 " transform="matrix(1,0,0,1,-326,-37)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 796.855469 86.878906 L 810.734375 75.015625 " transform="matrix(1,0,0,1,-326,-37)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:butt;stroke-linejoin:miter;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 813.777344 72.417969 L 810.734375 75.015625 M 809.761719 73.875 L 813.777344 72.417969 L 811.710938 76.15625 " transform="matrix(1,0,0,1,-326,-37)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 797.425781 87.722656 L 825.535156 73.066406 " transform="matrix(1,0,0,1,-326,-37)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:butt;stroke-linejoin:miter;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 829.082031 71.21875 L 825.535156 73.066406 M 824.839844 71.738281 L 829.082031 71.21875 L 826.226562 74.398438 " transform="matrix(1,0,0,1,-326,-37)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 808.105469 87.742188 L 779.542969 73.023438 " transform="matrix(1,0,0,1,-326,-37)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:butt;stroke-linejoin:miter;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 775.988281 71.191406 L 779.542969 73.023438 M 778.855469 74.355469 L 775.988281 71.191406 L 780.230469 71.691406 " transform="matrix(1,0,0,1,-326,-37)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 808.65625 86.910156 L 794.382812 74.941406 " transform="matrix(1,0,0,1,-326,-37)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:butt;stroke-linejoin:miter;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 791.316406 72.371094 L 794.382812 74.941406 M 793.417969 76.089844 L 791.316406 72.371094 L 795.347656 73.792969 " transform="matrix(1,0,0,1,-326,-37)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 810.277344 85.6875 L 806.753906 77.765625 " transform="matrix(1,0,0,1,-326,-37)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:butt;stroke-linejoin:miter;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 805.128906 74.109375 L 806.753906 77.765625 M 805.386719 78.375 L 805.128906 74.109375 L 808.125 77.15625 " transform="matrix(1,0,0,1,-326,-37)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 813.417969 85.496094 L 815.625 78.214844 " transform="matrix(1,0,0,1,-326,-37)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:butt;stroke-linejoin:miter;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 816.785156 74.386719 L 815.625 78.214844 M 814.1875 77.777344 L 816.785156 74.386719 L 817.058594 78.648438 " transform="matrix(1,0,0,1,-326,-37)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 815.371094 86.699219 L 827.222656 75.429688 " transform="matrix(1,0,0,1,-326,-37)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:butt;stroke-linejoin:miter;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 830.121094 72.671875 L 827.222656 75.429688 M 826.1875 74.339844 L 830.121094 72.671875 L 828.257812 76.515625 " transform="matrix(1,0,0,1,-326,-37)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 826.539062 88.046875 L 780.109375 71.710938 " transform="matrix(1,0,0,1,-326,-37)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:butt;stroke-linejoin:miter;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 776.335938 70.382812 L 780.109375 71.710938 M 779.613281 73.125 L 776.335938 70.382812 L 780.605469 70.296875 " transform="matrix(1,0,0,1,-326,-37)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 826.726562 87.59375 L 795.792969 72.757812 " transform="matrix(1,0,0,1,-326,-37)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:butt;stroke-linejoin:miter;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 792.1875 71.027344 L 795.792969 72.757812 M 795.144531 74.109375 L 792.1875 71.027344 L 796.441406 71.40625 " transform="matrix(1,0,0,1,-326,-37)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 827.1875 86.832031 L 810.839844 74.515625 " transform="matrix(1,0,0,1,-326,-37)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:butt;stroke-linejoin:miter;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 807.644531 72.109375 L 810.839844 74.515625 M 809.9375 75.714844 L 807.644531 72.109375 L 811.742188 73.320312 " transform="matrix(1,0,0,1,-326,-37)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 828.554688 85.628906 L 823.800781 77.296875 " transform="matrix(1,0,0,1,-326,-37)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:butt;stroke-linejoin:miter;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 821.820312 73.820312 L 823.800781 77.296875 M 822.496094 78.039062 L 821.820312 73.820312 L 825.105469 76.550781 " transform="matrix(1,0,0,1,-326,-37)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 831.613281 85.113281 L 832.851562 78.484375 " transform="matrix(1,0,0,1,-326,-37)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:butt;stroke-linejoin:miter;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 833.585938 74.554688 L 832.851562 78.484375 M 831.378906 78.210938 L 833.585938 74.554688 L 834.324219 78.761719 " transform="matrix(1,0,0,1,-326,-37)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 761.75 38.1875 L 843.304688 38.1875 L 843.304688 98.0625 L 761.75 98.0625 Z M 761.75 38.1875 " transform="matrix(1,0,0,1,-326,-37)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 724.4375 165.621094 C 726.199219 167.378906 726.199219 170.230469 724.4375 171.988281 C 722.679688 173.75 719.828125 173.75 718.070312 171.988281 C 716.308594 170.230469 716.308594 167.378906 718.070312 165.621094 C 719.828125 163.863281 722.679688 163.863281 724.4375 165.621094 " transform="matrix(1,0,0,1,-326,-37)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 743.117188 165.621094 C 744.875 167.378906 744.875 170.230469 743.117188 171.988281 C 741.359375 173.75 738.503906 173.75 736.746094 171.988281 C 734.988281 170.230469 734.988281 167.378906 736.746094 165.621094 C 738.503906 163.863281 741.359375 163.863281 743.117188 165.621094 " transform="matrix(1,0,0,1,-326,-37)"/>
<path style="fill-rule:nonzero;fill:rgb(39.99939%,74.902344%,100%);fill-opacity:1;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 736.09375 144.074219 C 737.855469 145.832031 737.855469 148.683594 736.09375 150.441406 C 734.335938 152.199219 731.484375 152.199219 729.726562 150.441406 C 727.96875 148.683594 727.96875 145.832031 729.726562 144.074219 C 731.484375 142.3125 734.335938 142.3125 736.09375 144.074219 " transform="matrix(1,0,0,1,-326,-37)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 761.792969 165.621094 C 763.554688 167.378906 763.554688 170.230469 761.792969 171.988281 C 760.035156 173.75 757.183594 173.75 755.425781 171.988281 C 753.664062 170.230469 753.664062 167.378906 755.425781 165.621094 C 757.183594 163.863281 760.035156 163.863281 761.792969 165.621094 " transform="matrix(1,0,0,1,-326,-37)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 780.472656 165.355469 C 782.230469 167.113281 782.230469 169.964844 780.472656 171.726562 C 778.714844 173.484375 775.863281 173.484375 774.101562 171.726562 C 772.34375 169.964844 772.34375 167.113281 774.101562 165.355469 C 775.863281 163.597656 778.714844 163.597656 780.472656 165.355469 " transform="matrix(1,0,0,1,-326,-37)"/>
<path style="fill-rule:nonzero;fill:rgb(39.99939%,74.902344%,100%);fill-opacity:1;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 768.328125 144.074219 C 770.089844 145.832031 770.089844 148.683594 768.328125 150.441406 C 766.570312 152.199219 763.71875 152.199219 761.960938 150.441406 C 760.199219 148.683594 760.199219 145.832031 761.960938 144.074219 C 763.71875 142.3125 766.570312 142.3125 768.328125 144.074219 " transform="matrix(1,0,0,1,-326,-37)"/>
<path style="fill-rule:nonzero;fill:rgb(39.99939%,74.902344%,100%);fill-opacity:1;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 752.210938 144.074219 C 753.972656 145.832031 753.972656 148.683594 752.210938 150.441406 C 750.453125 152.199219 747.601562 152.199219 745.84375 150.441406 C 744.085938 148.683594 744.085938 145.832031 745.84375 144.074219 C 747.601562 142.3125 750.453125 142.3125 752.210938 144.074219 " transform="matrix(1,0,0,1,-326,-37)"/>
<path style="fill-rule:nonzero;fill:rgb(69.804382%,85.098267%,100%);fill-opacity:1;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 733.535156 122.523438 C 735.292969 124.285156 735.292969 127.136719 733.535156 128.894531 C 731.777344 130.652344 728.925781 130.652344 727.164062 128.894531 C 725.40625 127.136719 725.40625 124.285156 727.164062 122.523438 C 728.925781 120.765625 731.777344 120.765625 733.535156 122.523438 " transform="matrix(1,0,0,1,-326,-37)"/>
<path style="fill-rule:nonzero;fill:rgb(69.804382%,85.098267%,100%);fill-opacity:1;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 752.210938 122.523438 C 753.972656 124.285156 753.972656 127.136719 752.210938 128.894531 C 750.453125 130.652344 747.601562 130.652344 745.84375 128.894531 C 744.085938 127.136719 744.085938 124.285156 745.84375 122.523438 C 747.601562 120.765625 750.453125 120.765625 752.210938 122.523438 " transform="matrix(1,0,0,1,-326,-37)"/>
<path style="fill-rule:nonzero;fill:rgb(69.804382%,85.098267%,100%);fill-opacity:1;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 770.890625 122.261719 C 772.648438 124.019531 772.648438 126.871094 770.890625 128.628906 C 769.132812 130.386719 766.28125 130.386719 764.519531 128.628906 C 762.761719 126.871094 762.761719 124.019531 764.519531 122.261719 C 766.28125 120.5 769.132812 120.5 770.890625 122.261719 " transform="matrix(1,0,0,1,-326,-37)"/>
<path style="fill-rule:nonzero;fill:rgb(39.99939%,74.902344%,100%);fill-opacity:1;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 719.980469 144.074219 C 721.738281 145.832031 721.738281 148.683594 719.980469 150.441406 C 718.21875 152.199219 715.367188 152.199219 713.609375 150.441406 C 711.851562 148.683594 711.851562 145.832031 713.609375 144.074219 C 715.367188 142.3125 718.21875 142.3125 719.980469 144.074219 " transform="matrix(1,0,0,1,-326,-37)"/>
<path style="fill-rule:nonzero;fill:rgb(39.99939%,74.902344%,100%);fill-opacity:1;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 784.445312 144.074219 C 786.203125 145.832031 786.203125 148.683594 784.445312 150.441406 C 782.6875 152.199219 779.835938 152.199219 778.078125 150.441406 C 776.316406 148.683594 776.316406 145.832031 778.078125 144.074219 C 779.835938 142.3125 782.6875 142.3125 784.445312 144.074219 " transform="matrix(1,0,0,1,-326,-37)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 719.191406 143.445312 L 724.808594 134.515625 " transform="matrix(1,0,0,1,-326,-37)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:butt;stroke-linejoin:miter;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 726.9375 131.128906 L 724.808594 134.515625 M 723.539062 133.71875 L 726.9375 131.128906 L 726.078125 135.316406 " transform="matrix(1,0,0,1,-326,-37)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 720.539062 144.753906 L 740.378906 131.492188 " transform="matrix(1,0,0,1,-326,-37)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:butt;stroke-linejoin:miter;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 743.703125 129.269531 L 740.378906 131.492188 M 739.542969 130.246094 L 743.703125 129.269531 L 741.210938 132.738281 " transform="matrix(1,0,0,1,-326,-37)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 720.933594 145.484375 L 758.140625 129.542969 " transform="matrix(1,0,0,1,-326,-37)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:butt;stroke-linejoin:miter;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 761.816406 127.96875 L 758.140625 129.542969 M 757.550781 128.164062 L 761.816406 127.96875 L 758.730469 130.921875 " transform="matrix(1,0,0,1,-326,-37)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 732.378906 142.785156 L 731.578125 136.039062 " transform="matrix(1,0,0,1,-326,-37)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:butt;stroke-linejoin:miter;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 731.105469 132.070312 L 731.578125 136.039062 M 730.089844 136.21875 L 731.105469 132.070312 L 733.066406 135.863281 " transform="matrix(1,0,0,1,-326,-37)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 735.609375 143.652344 L 742.796875 134.042969 " transform="matrix(1,0,0,1,-326,-37)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:butt;stroke-linejoin:miter;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 745.191406 130.839844 L 742.796875 134.042969 M 741.59375 133.144531 L 745.191406 130.839844 L 743.996094 134.941406 " transform="matrix(1,0,0,1,-326,-37)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 736.726562 144.863281 L 758.890625 130.972656 " transform="matrix(1,0,0,1,-326,-37)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:butt;stroke-linejoin:miter;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 762.277344 128.847656 L 758.890625 130.972656 M 758.09375 129.699219 L 762.277344 128.847656 L 759.6875 132.242188 " transform="matrix(1,0,0,1,-326,-37)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 746.078125 143.855469 L 737.164062 133.570312 " transform="matrix(1,0,0,1,-326,-37)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:butt;stroke-linejoin:miter;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 734.542969 130.546875 L 737.164062 133.570312 M 736.03125 134.554688 L 734.542969 130.546875 L 738.296875 132.589844 " transform="matrix(1,0,0,1,-326,-37)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 749.027344 142.753906 L 749.027344 136.113281 " transform="matrix(1,0,0,1,-326,-37)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:butt;stroke-linejoin:miter;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 749.027344 132.113281 L 749.027344 136.113281 M 747.527344 136.113281 L 749.027344 132.113281 L 750.527344 136.113281 " transform="matrix(1,0,0,1,-326,-37)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 751.957031 143.835938 L 760.9375 133.347656 " transform="matrix(1,0,0,1,-326,-37)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:butt;stroke-linejoin:miter;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 763.539062 130.308594 L 760.9375 133.347656 M 759.800781 132.371094 L 763.539062 130.308594 L 762.078125 134.324219 " transform="matrix(1,0,0,1,-326,-37)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 761.316406 144.886719 L 739.195312 131.1875 " transform="matrix(1,0,0,1,-326,-37)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:butt;stroke-linejoin:miter;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 735.796875 129.082031 L 739.195312 131.1875 M 738.40625 132.464844 L 735.796875 129.082031 L 739.984375 129.914062 " transform="matrix(1,0,0,1,-326,-37)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 762.445312 143.652344 L 755.257812 134.042969 " transform="matrix(1,0,0,1,-326,-37)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:butt;stroke-linejoin:miter;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 752.863281 130.839844 L 755.257812 134.042969 M 754.058594 134.941406 L 752.863281 130.839844 L 756.460938 133.144531 " transform="matrix(1,0,0,1,-326,-37)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 765.667969 142.785156 L 766.492188 135.777344 " transform="matrix(1,0,0,1,-326,-37)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:butt;stroke-linejoin:miter;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 766.957031 131.804688 L 766.492188 135.777344 M 765.003906 135.601562 L 766.957031 131.804688 L 767.980469 135.953125 " transform="matrix(1,0,0,1,-326,-37)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 777.113281 145.5 L 739.933594 129.765625 " transform="matrix(1,0,0,1,-326,-37)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:butt;stroke-linejoin:miter;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 736.25 128.207031 L 739.933594 129.765625 M 739.347656 131.148438 L 736.25 128.207031 L 740.515625 128.382812 " transform="matrix(1,0,0,1,-326,-37)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 777.515625 144.753906 L 757.675781 131.492188 " transform="matrix(1,0,0,1,-326,-37)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:butt;stroke-linejoin:miter;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 754.351562 129.269531 L 757.675781 131.492188 M 756.84375 132.738281 L 754.351562 129.269531 L 758.511719 130.246094 " transform="matrix(1,0,0,1,-326,-37)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 778.882812 143.429688 L 773.199219 134.28125 " transform="matrix(1,0,0,1,-326,-37)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:butt;stroke-linejoin:miter;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 771.085938 130.882812 L 773.199219 134.28125 M 771.921875 135.074219 L 771.085938 130.882812 L 774.472656 133.488281 " transform="matrix(1,0,0,1,-326,-37)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 720.339844 164.394531 L 718.902344 157.445312 " transform="matrix(1,0,0,1,-326,-37)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:butt;stroke-linejoin:miter;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 718.09375 153.527344 L 718.902344 157.445312 M 717.433594 157.75 L 718.09375 153.527344 L 720.371094 157.140625 " transform="matrix(1,0,0,1,-326,-37)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 723.398438 164.84375 L 727.960938 156.410156 " transform="matrix(1,0,0,1,-326,-37)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:butt;stroke-linejoin:miter;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 729.863281 152.890625 L 727.960938 156.410156 M 726.640625 155.695312 L 729.863281 152.890625 L 729.28125 157.121094 " transform="matrix(1,0,0,1,-326,-37)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 724.8125 166.042969 L 740.808594 153.636719 " transform="matrix(1,0,0,1,-326,-37)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:butt;stroke-linejoin:miter;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 743.96875 151.183594 L 740.808594 153.636719 M 739.886719 152.449219 L 743.96875 151.183594 L 741.726562 154.820312 " transform="matrix(1,0,0,1,-326,-37)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 725.296875 166.820312 L 755.804688 151.84375 " transform="matrix(1,0,0,1,-326,-37)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:butt;stroke-linejoin:miter;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 759.394531 150.082031 L 755.804688 151.84375 M 755.144531 150.496094 L 759.394531 150.082031 L 756.464844 153.191406 " transform="matrix(1,0,0,1,-326,-37)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 725.492188 167.28125 L 771.46875 150.773438 " transform="matrix(1,0,0,1,-326,-37)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:butt;stroke-linejoin:miter;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 775.234375 149.421875 L 771.46875 150.773438 M 770.960938 149.363281 L 775.234375 149.421875 L 771.976562 152.1875 " transform="matrix(1,0,0,1,-326,-37)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 736.636719 165.734375 L 724.40625 154.347656 " transform="matrix(1,0,0,1,-326,-37)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:butt;stroke-linejoin:miter;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 721.480469 151.621094 L 724.40625 154.347656 M 723.386719 155.445312 L 721.480469 151.621094 L 725.429688 153.25 " transform="matrix(1,0,0,1,-326,-37)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 738.535156 164.523438 L 736.132812 157.152344 " transform="matrix(1,0,0,1,-326,-37)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:butt;stroke-linejoin:miter;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 734.894531 153.347656 L 736.132812 157.152344 M 734.707031 157.613281 L 734.894531 153.347656 L 737.558594 156.6875 " transform="matrix(1,0,0,1,-326,-37)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 741.683594 164.65625 L 744.980469 156.84375 " transform="matrix(1,0,0,1,-326,-37)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:butt;stroke-linejoin:miter;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 746.535156 153.160156 L 744.980469 156.84375 M 743.597656 156.261719 L 746.535156 153.160156 L 746.363281 157.425781 " transform="matrix(1,0,0,1,-326,-37)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 743.355469 165.878906 L 757.234375 154.015625 " transform="matrix(1,0,0,1,-326,-37)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:butt;stroke-linejoin:miter;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 760.277344 151.417969 L 757.234375 154.015625 M 756.261719 152.875 L 760.277344 151.417969 L 758.210938 155.15625 " transform="matrix(1,0,0,1,-326,-37)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 743.925781 166.722656 L 772.035156 152.066406 " transform="matrix(1,0,0,1,-326,-37)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:butt;stroke-linejoin:miter;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 775.582031 150.21875 L 772.035156 152.066406 M 771.339844 150.738281 L 775.582031 150.21875 L 772.726562 153.398438 " transform="matrix(1,0,0,1,-326,-37)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 754.605469 166.742188 L 726.042969 152.023438 " transform="matrix(1,0,0,1,-326,-37)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:butt;stroke-linejoin:miter;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 722.488281 150.191406 L 726.042969 152.023438 M 725.355469 153.355469 L 722.488281 150.191406 L 726.730469 150.691406 " transform="matrix(1,0,0,1,-326,-37)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 755.15625 165.910156 L 740.882812 153.941406 " transform="matrix(1,0,0,1,-326,-37)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:butt;stroke-linejoin:miter;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 737.816406 151.371094 L 740.882812 153.941406 M 739.917969 155.089844 L 737.816406 151.371094 L 741.847656 152.792969 " transform="matrix(1,0,0,1,-326,-37)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 756.777344 164.6875 L 753.253906 156.765625 " transform="matrix(1,0,0,1,-326,-37)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:butt;stroke-linejoin:miter;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 751.628906 153.109375 L 753.253906 156.765625 M 751.886719 157.375 L 751.628906 153.109375 L 754.625 156.15625 " transform="matrix(1,0,0,1,-326,-37)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 759.917969 164.496094 L 762.125 157.214844 " transform="matrix(1,0,0,1,-326,-37)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:butt;stroke-linejoin:miter;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 763.285156 153.386719 L 762.125 157.214844 M 760.6875 156.777344 L 763.285156 153.386719 L 763.558594 157.648438 " transform="matrix(1,0,0,1,-326,-37)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 761.871094 165.703125 L 773.722656 154.429688 " transform="matrix(1,0,0,1,-326,-37)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:butt;stroke-linejoin:miter;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 776.621094 151.671875 L 773.722656 154.429688 M 772.6875 153.339844 L 776.621094 151.671875 L 774.757812 155.515625 " transform="matrix(1,0,0,1,-326,-37)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 773.039062 167.046875 L 726.609375 150.710938 " transform="matrix(1,0,0,1,-326,-37)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:butt;stroke-linejoin:miter;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 722.835938 149.382812 L 726.609375 150.710938 M 726.113281 152.125 L 722.835938 149.382812 L 727.105469 149.296875 " transform="matrix(1,0,0,1,-326,-37)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 773.226562 166.59375 L 742.292969 151.757812 " transform="matrix(1,0,0,1,-326,-37)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:butt;stroke-linejoin:miter;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 738.6875 150.027344 L 742.292969 151.757812 M 741.644531 153.109375 L 738.6875 150.027344 L 742.941406 150.40625 " transform="matrix(1,0,0,1,-326,-37)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 773.6875 165.832031 L 757.339844 153.515625 " transform="matrix(1,0,0,1,-326,-37)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:butt;stroke-linejoin:miter;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 754.144531 151.109375 L 757.339844 153.515625 M 756.4375 154.714844 L 754.144531 151.109375 L 758.242188 152.320312 " transform="matrix(1,0,0,1,-326,-37)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 775.054688 164.628906 L 770.300781 156.296875 " transform="matrix(1,0,0,1,-326,-37)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:butt;stroke-linejoin:miter;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 768.320312 152.820312 L 770.300781 156.296875 M 768.996094 157.039062 L 768.320312 152.820312 L 771.605469 155.550781 " transform="matrix(1,0,0,1,-326,-37)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 778.113281 164.113281 L 779.351562 157.484375 " transform="matrix(1,0,0,1,-326,-37)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:butt;stroke-linejoin:miter;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 780.085938 153.554688 L 779.351562 157.484375 M 777.878906 157.210938 L 780.085938 153.554688 L 780.824219 157.761719 " transform="matrix(1,0,0,1,-326,-37)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 708.25 117.1875 L 789.804688 117.1875 L 789.804688 177.0625 L 708.25 177.0625 Z M 708.25 117.1875 " transform="matrix(1,0,0,1,-326,-37)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 831.4375 165.621094 C 833.199219 167.378906 833.199219 170.230469 831.4375 171.988281 C 829.679688 173.75 826.828125 173.75 825.070312 171.988281 C 823.308594 170.230469 823.308594 167.378906 825.070312 165.621094 C 826.828125 163.863281 829.679688 163.863281 831.4375 165.621094 " transform="matrix(1,0,0,1,-326,-37)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 850.117188 165.621094 C 851.875 167.378906 851.875 170.230469 850.117188 171.988281 C 848.359375 173.75 845.503906 173.75 843.746094 171.988281 C 841.988281 170.230469 841.988281 167.378906 843.746094 165.621094 C 845.503906 163.863281 848.359375 163.863281 850.117188 165.621094 " transform="matrix(1,0,0,1,-326,-37)"/>
<path style="fill-rule:nonzero;fill:rgb(39.99939%,74.902344%,100%);fill-opacity:1;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 843.09375 144.074219 C 844.855469 145.832031 844.855469 148.683594 843.09375 150.441406 C 841.335938 152.199219 838.484375 152.199219 836.726562 150.441406 C 834.96875 148.683594 834.96875 145.832031 836.726562 144.074219 C 838.484375 142.3125 841.335938 142.3125 843.09375 144.074219 " transform="matrix(1,0,0,1,-326,-37)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 868.792969 165.621094 C 870.554688 167.378906 870.554688 170.230469 868.792969 171.988281 C 867.035156 173.75 864.183594 173.75 862.425781 171.988281 C 860.664062 170.230469 860.664062 167.378906 862.425781 165.621094 C 864.183594 163.863281 867.035156 163.863281 868.792969 165.621094 " transform="matrix(1,0,0,1,-326,-37)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 887.472656 165.355469 C 889.230469 167.113281 889.230469 169.964844 887.472656 171.726562 C 885.714844 173.484375 882.863281 173.484375 881.101562 171.726562 C 879.34375 169.964844 879.34375 167.113281 881.101562 165.355469 C 882.863281 163.597656 885.714844 163.597656 887.472656 165.355469 " transform="matrix(1,0,0,1,-326,-37)"/>
<path style="fill-rule:nonzero;fill:rgb(39.99939%,74.902344%,100%);fill-opacity:1;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 875.328125 144.074219 C 877.089844 145.832031 877.089844 148.683594 875.328125 150.441406 C 873.570312 152.199219 870.71875 152.199219 868.960938 150.441406 C 867.199219 148.683594 867.199219 145.832031 868.960938 144.074219 C 870.71875 142.3125 873.570312 142.3125 875.328125 144.074219 " transform="matrix(1,0,0,1,-326,-37)"/>
<path style="fill-rule:nonzero;fill:rgb(39.99939%,74.902344%,100%);fill-opacity:1;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 859.210938 144.074219 C 860.972656 145.832031 860.972656 148.683594 859.210938 150.441406 C 857.453125 152.199219 854.601562 152.199219 852.84375 150.441406 C 851.085938 148.683594 851.085938 145.832031 852.84375 144.074219 C 854.601562 142.3125 857.453125 142.3125 859.210938 144.074219 " transform="matrix(1,0,0,1,-326,-37)"/>
<path style="fill-rule:nonzero;fill:rgb(69.804382%,85.098267%,100%);fill-opacity:1;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 840.535156 122.523438 C 842.292969 124.285156 842.292969 127.136719 840.535156 128.894531 C 838.777344 130.652344 835.925781 130.652344 834.164062 128.894531 C 832.40625 127.136719 832.40625 124.285156 834.164062 122.523438 C 835.925781 120.765625 838.777344 120.765625 840.535156 122.523438 " transform="matrix(1,0,0,1,-326,-37)"/>
<path style="fill-rule:nonzero;fill:rgb(69.804382%,85.098267%,100%);fill-opacity:1;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 859.210938 122.523438 C 860.972656 124.285156 860.972656 127.136719 859.210938 128.894531 C 857.453125 130.652344 854.601562 130.652344 852.84375 128.894531 C 851.085938 127.136719 851.085938 124.285156 852.84375 122.523438 C 854.601562 120.765625 857.453125 120.765625 859.210938 122.523438 " transform="matrix(1,0,0,1,-326,-37)"/>
<path style="fill-rule:nonzero;fill:rgb(69.804382%,85.098267%,100%);fill-opacity:1;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 877.890625 122.261719 C 879.648438 124.019531 879.648438 126.871094 877.890625 128.628906 C 876.132812 130.386719 873.28125 130.386719 871.519531 128.628906 C 869.761719 126.871094 869.761719 124.019531 871.519531 122.261719 C 873.28125 120.5 876.132812 120.5 877.890625 122.261719 " transform="matrix(1,0,0,1,-326,-37)"/>
<path style="fill-rule:nonzero;fill:rgb(39.99939%,74.902344%,100%);fill-opacity:1;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 826.980469 144.074219 C 828.738281 145.832031 828.738281 148.683594 826.980469 150.441406 C 825.21875 152.199219 822.367188 152.199219 820.609375 150.441406 C 818.851562 148.683594 818.851562 145.832031 820.609375 144.074219 C 822.367188 142.3125 825.21875 142.3125 826.980469 144.074219 " transform="matrix(1,0,0,1,-326,-37)"/>
<path style="fill-rule:nonzero;fill:rgb(39.99939%,74.902344%,100%);fill-opacity:1;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 891.445312 144.074219 C 893.203125 145.832031 893.203125 148.683594 891.445312 150.441406 C 889.6875 152.199219 886.835938 152.199219 885.078125 150.441406 C 883.316406 148.683594 883.316406 145.832031 885.078125 144.074219 C 886.835938 142.3125 889.6875 142.3125 891.445312 144.074219 " transform="matrix(1,0,0,1,-326,-37)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 826.191406 143.445312 L 831.808594 134.515625 " transform="matrix(1,0,0,1,-326,-37)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:butt;stroke-linejoin:miter;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 833.9375 131.128906 L 831.808594 134.515625 M 830.539062 133.71875 L 833.9375 131.128906 L 833.078125 135.316406 " transform="matrix(1,0,0,1,-326,-37)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 827.539062 144.753906 L 847.378906 131.492188 " transform="matrix(1,0,0,1,-326,-37)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:butt;stroke-linejoin:miter;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 850.703125 129.269531 L 847.378906 131.492188 M 846.542969 130.246094 L 850.703125 129.269531 L 848.210938 132.738281 " transform="matrix(1,0,0,1,-326,-37)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 827.933594 145.484375 L 865.140625 129.542969 " transform="matrix(1,0,0,1,-326,-37)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:butt;stroke-linejoin:miter;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 868.816406 127.96875 L 865.140625 129.542969 M 864.550781 128.164062 L 868.816406 127.96875 L 865.730469 130.921875 " transform="matrix(1,0,0,1,-326,-37)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 839.378906 142.785156 L 838.578125 136.039062 " transform="matrix(1,0,0,1,-326,-37)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:butt;stroke-linejoin:miter;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 838.105469 132.070312 L 838.578125 136.039062 M 837.089844 136.21875 L 838.105469 132.070312 L 840.066406 135.863281 " transform="matrix(1,0,0,1,-326,-37)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 842.609375 143.652344 L 849.796875 134.042969 " transform="matrix(1,0,0,1,-326,-37)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:butt;stroke-linejoin:miter;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 852.191406 130.839844 L 849.796875 134.042969 M 848.59375 133.144531 L 852.191406 130.839844 L 850.996094 134.941406 " transform="matrix(1,0,0,1,-326,-37)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 843.726562 144.863281 L 865.890625 130.972656 " transform="matrix(1,0,0,1,-326,-37)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:butt;stroke-linejoin:miter;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 869.277344 128.847656 L 865.890625 130.972656 M 865.09375 129.699219 L 869.277344 128.847656 L 866.6875 132.242188 " transform="matrix(1,0,0,1,-326,-37)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 853.078125 143.855469 L 844.164062 133.570312 " transform="matrix(1,0,0,1,-326,-37)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:butt;stroke-linejoin:miter;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 841.542969 130.546875 L 844.164062 133.570312 M 843.03125 134.554688 L 841.542969 130.546875 L 845.296875 132.589844 " transform="matrix(1,0,0,1,-326,-37)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 856.027344 142.753906 L 856.027344 136.113281 " transform="matrix(1,0,0,1,-326,-37)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:butt;stroke-linejoin:miter;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 856.027344 132.113281 L 856.027344 136.113281 M 854.527344 136.113281 L 856.027344 132.113281 L 857.527344 136.113281 " transform="matrix(1,0,0,1,-326,-37)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 858.957031 143.835938 L 867.9375 133.347656 " transform="matrix(1,0,0,1,-326,-37)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:butt;stroke-linejoin:miter;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 870.539062 130.308594 L 867.9375 133.347656 M 866.800781 132.371094 L 870.539062 130.308594 L 869.078125 134.324219 " transform="matrix(1,0,0,1,-326,-37)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 868.316406 144.886719 L 846.195312 131.1875 " transform="matrix(1,0,0,1,-326,-37)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:butt;stroke-linejoin:miter;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 842.796875 129.082031 L 846.195312 131.1875 M 845.40625 132.464844 L 842.796875 129.082031 L 846.984375 129.914062 " transform="matrix(1,0,0,1,-326,-37)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 869.445312 143.652344 L 862.257812 134.042969 " transform="matrix(1,0,0,1,-326,-37)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:butt;stroke-linejoin:miter;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 859.863281 130.839844 L 862.257812 134.042969 M 861.058594 134.941406 L 859.863281 130.839844 L 863.460938 133.144531 " transform="matrix(1,0,0,1,-326,-37)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 872.667969 142.785156 L 873.492188 135.777344 " transform="matrix(1,0,0,1,-326,-37)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:butt;stroke-linejoin:miter;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 873.957031 131.804688 L 873.492188 135.777344 M 872.003906 135.601562 L 873.957031 131.804688 L 874.980469 135.953125 " transform="matrix(1,0,0,1,-326,-37)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 884.113281 145.5 L 846.933594 129.765625 " transform="matrix(1,0,0,1,-326,-37)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:butt;stroke-linejoin:miter;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 843.25 128.207031 L 846.933594 129.765625 M 846.347656 131.148438 L 843.25 128.207031 L 847.515625 128.382812 " transform="matrix(1,0,0,1,-326,-37)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 884.515625 144.753906 L 864.675781 131.492188 " transform="matrix(1,0,0,1,-326,-37)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:butt;stroke-linejoin:miter;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 861.351562 129.269531 L 864.675781 131.492188 M 863.84375 132.738281 L 861.351562 129.269531 L 865.511719 130.246094 " transform="matrix(1,0,0,1,-326,-37)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 885.882812 143.429688 L 880.199219 134.28125 " transform="matrix(1,0,0,1,-326,-37)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:butt;stroke-linejoin:miter;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 878.085938 130.882812 L 880.199219 134.28125 M 878.921875 135.074219 L 878.085938 130.882812 L 881.472656 133.488281 " transform="matrix(1,0,0,1,-326,-37)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 827.339844 164.394531 L 825.902344 157.445312 " transform="matrix(1,0,0,1,-326,-37)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:butt;stroke-linejoin:miter;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 825.09375 153.527344 L 825.902344 157.445312 M 824.433594 157.75 L 825.09375 153.527344 L 827.371094 157.140625 " transform="matrix(1,0,0,1,-326,-37)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 830.398438 164.84375 L 834.960938 156.410156 " transform="matrix(1,0,0,1,-326,-37)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:butt;stroke-linejoin:miter;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 836.863281 152.890625 L 834.960938 156.410156 M 833.640625 155.695312 L 836.863281 152.890625 L 836.28125 157.121094 " transform="matrix(1,0,0,1,-326,-37)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 831.8125 166.042969 L 847.808594 153.636719 " transform="matrix(1,0,0,1,-326,-37)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:butt;stroke-linejoin:miter;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 850.96875 151.183594 L 847.808594 153.636719 M 846.886719 152.449219 L 850.96875 151.183594 L 848.726562 154.820312 " transform="matrix(1,0,0,1,-326,-37)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 832.296875 166.820312 L 862.804688 151.84375 " transform="matrix(1,0,0,1,-326,-37)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:butt;stroke-linejoin:miter;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 866.394531 150.082031 L 862.804688 151.84375 M 862.144531 150.496094 L 866.394531 150.082031 L 863.464844 153.191406 " transform="matrix(1,0,0,1,-326,-37)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 832.492188 167.28125 L 878.46875 150.773438 " transform="matrix(1,0,0,1,-326,-37)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:butt;stroke-linejoin:miter;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 882.234375 149.421875 L 878.46875 150.773438 M 877.960938 149.363281 L 882.234375 149.421875 L 878.976562 152.1875 " transform="matrix(1,0,0,1,-326,-37)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 843.636719 165.734375 L 831.40625 154.347656 " transform="matrix(1,0,0,1,-326,-37)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:butt;stroke-linejoin:miter;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 828.480469 151.621094 L 831.40625 154.347656 M 830.386719 155.445312 L 828.480469 151.621094 L 832.429688 153.25 " transform="matrix(1,0,0,1,-326,-37)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 845.535156 164.523438 L 843.132812 157.152344 " transform="matrix(1,0,0,1,-326,-37)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:butt;stroke-linejoin:miter;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 841.894531 153.347656 L 843.132812 157.152344 M 841.707031 157.613281 L 841.894531 153.347656 L 844.558594 156.6875 " transform="matrix(1,0,0,1,-326,-37)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 848.683594 164.65625 L 851.980469 156.84375 " transform="matrix(1,0,0,1,-326,-37)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:butt;stroke-linejoin:miter;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 853.535156 153.160156 L 851.980469 156.84375 M 850.597656 156.261719 L 853.535156 153.160156 L 853.363281 157.425781 " transform="matrix(1,0,0,1,-326,-37)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 850.355469 165.878906 L 864.234375 154.015625 " transform="matrix(1,0,0,1,-326,-37)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:butt;stroke-linejoin:miter;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 867.277344 151.417969 L 864.234375 154.015625 M 863.261719 152.875 L 867.277344 151.417969 L 865.210938 155.15625 " transform="matrix(1,0,0,1,-326,-37)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 850.925781 166.722656 L 879.035156 152.066406 " transform="matrix(1,0,0,1,-326,-37)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:butt;stroke-linejoin:miter;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 882.582031 150.21875 L 879.035156 152.066406 M 878.339844 150.738281 L 882.582031 150.21875 L 879.726562 153.398438 " transform="matrix(1,0,0,1,-326,-37)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 861.605469 166.742188 L 833.042969 152.023438 " transform="matrix(1,0,0,1,-326,-37)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:butt;stroke-linejoin:miter;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 829.488281 150.191406 L 833.042969 152.023438 M 832.355469 153.355469 L 829.488281 150.191406 L 833.730469 150.691406 " transform="matrix(1,0,0,1,-326,-37)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 862.15625 165.910156 L 847.882812 153.941406 " transform="matrix(1,0,0,1,-326,-37)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:butt;stroke-linejoin:miter;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 844.816406 151.371094 L 847.882812 153.941406 M 846.917969 155.089844 L 844.816406 151.371094 L 848.847656 152.792969 " transform="matrix(1,0,0,1,-326,-37)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 863.777344 164.6875 L 860.253906 156.765625 " transform="matrix(1,0,0,1,-326,-37)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:butt;stroke-linejoin:miter;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 858.628906 153.109375 L 860.253906 156.765625 M 858.886719 157.375 L 858.628906 153.109375 L 861.625 156.15625 " transform="matrix(1,0,0,1,-326,-37)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 866.917969 164.496094 L 869.125 157.214844 " transform="matrix(1,0,0,1,-326,-37)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:butt;stroke-linejoin:miter;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 870.285156 153.386719 L 869.125 157.214844 M 867.6875 156.777344 L 870.285156 153.386719 L 870.558594 157.648438 " transform="matrix(1,0,0,1,-326,-37)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 868.871094 165.703125 L 880.722656 154.429688 " transform="matrix(1,0,0,1,-326,-37)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:butt;stroke-linejoin:miter;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 883.621094 151.671875 L 880.722656 154.429688 M 879.6875 153.339844 L 883.621094 151.671875 L 881.757812 155.515625 " transform="matrix(1,0,0,1,-326,-37)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 880.039062 167.046875 L 833.609375 150.710938 " transform="matrix(1,0,0,1,-326,-37)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:butt;stroke-linejoin:miter;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 829.835938 149.382812 L 833.609375 150.710938 M 833.113281 152.125 L 829.835938 149.382812 L 834.105469 149.296875 " transform="matrix(1,0,0,1,-326,-37)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 880.226562 166.59375 L 849.292969 151.757812 " transform="matrix(1,0,0,1,-326,-37)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:butt;stroke-linejoin:miter;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 845.6875 150.027344 L 849.292969 151.757812 M 848.644531 153.109375 L 845.6875 150.027344 L 849.941406 150.40625 " transform="matrix(1,0,0,1,-326,-37)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 880.6875 165.832031 L 864.339844 153.515625 " transform="matrix(1,0,0,1,-326,-37)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:butt;stroke-linejoin:miter;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 861.144531 151.109375 L 864.339844 153.515625 M 863.4375 154.714844 L 861.144531 151.109375 L 865.242188 152.320312 " transform="matrix(1,0,0,1,-326,-37)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 882.054688 164.628906 L 877.300781 156.296875 " transform="matrix(1,0,0,1,-326,-37)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:butt;stroke-linejoin:miter;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 875.320312 152.820312 L 877.300781 156.296875 M 875.996094 157.039062 L 875.320312 152.820312 L 878.605469 155.550781 " transform="matrix(1,0,0,1,-326,-37)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 885.113281 164.113281 L 886.351562 157.484375 " transform="matrix(1,0,0,1,-326,-37)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:butt;stroke-linejoin:miter;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 887.085938 153.554688 L 886.351562 157.484375 M 884.878906 157.210938 L 887.085938 153.554688 L 887.824219 157.761719 " transform="matrix(1,0,0,1,-326,-37)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 815.25 117.1875 L 896.804688 117.1875 L 896.804688 177.0625 L 815.25 177.0625 Z M 815.25 117.1875 " transform="matrix(1,0,0,1,-326,-37)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:miter;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 802.527344 196.1875 L 802.527344 186.625 L 829.277344 187.074219 L 856.027344 186.625 L 856.027344 182.960938 " transform="matrix(1,0,0,1,-326,-37)"/>
<path style="fill-rule:nonzero;fill:rgb(0%,0%,0%);fill-opacity:1;stroke-width:1;stroke-linecap:butt;stroke-linejoin:miter;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 856.027344 178.960938 L 854.527344 182.960938 L 857.527344 182.960938 Z M 856.027344 178.960938 " transform="matrix(1,0,0,1,-326,-37)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:miter;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 802.527344 196.1875 L 802.527344 186.625 L 775.777344 187.074219 L 749.027344 186.625 L 749.027344 182.960938 " transform="matrix(1,0,0,1,-326,-37)"/>
<path style="fill-rule:nonzero;fill:rgb(0%,0%,0%);fill-opacity:1;stroke-width:1;stroke-linecap:butt;stroke-linejoin:miter;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 749.027344 178.960938 L 747.527344 182.960938 L 750.527344 182.960938 Z M 749.027344 178.960938 " transform="matrix(1,0,0,1,-326,-37)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:miter;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 749.027344 117.1875 L 749.027344 107.625 L 775.777344 108.074219 L 802.527344 107.625 L 802.527344 103.960938 " transform="matrix(1,0,0,1,-326,-37)"/>
<path style="fill-rule:nonzero;fill:rgb(0%,0%,0%);fill-opacity:1;stroke-width:1;stroke-linecap:butt;stroke-linejoin:miter;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 802.527344 99.960938 L 801.027344 103.960938 L 804.027344 103.960938 Z M 802.527344 99.960938 " transform="matrix(1,0,0,1,-326,-37)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:miter;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 856.027344 117.1875 L 856.027344 107.625 L 829.277344 108.074219 L 802.527344 107.625 L 802.527344 103.960938 " transform="matrix(1,0,0,1,-326,-37)"/>
<path style="fill-rule:nonzero;fill:rgb(0%,0%,0%);fill-opacity:1;stroke-width:1;stroke-linecap:butt;stroke-linejoin:miter;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 802.527344 99.960938 L 801.027344 103.960938 L 804.027344 103.960938 Z M 802.527344 99.960938 " transform="matrix(1,0,0,1,-326,-37)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:miter;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-dasharray:4,4;stroke-miterlimit:10;" d="M 470.847656 146.625 L 500.875 146.695312 " transform="matrix(1,0,0,1,-326,-37)"/>
<path style="fill-rule:nonzero;fill:rgb(0%,0%,0%);fill-opacity:1;stroke-width:1;stroke-linecap:butt;stroke-linejoin:miter;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 504.875 146.703125 L 500.878906 145.195312 L 500.871094 148.195312 Z M 504.875 146.703125 " transform="matrix(1,0,0,1,-326,-37)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:miter;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-dasharray:4,4;stroke-miterlimit:10;" d="M 669.210938 146.625 L 699.238281 146.695312 " transform="matrix(1,0,0,1,-326,-37)"/>
<path style="fill-rule:nonzero;fill:rgb(0%,0%,0%);fill-opacity:1;stroke-width:1;stroke-linecap:butt;stroke-linejoin:miter;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 703.238281 146.703125 L 699.242188 145.195312 L 699.234375 148.195312 Z M 703.238281 146.703125 " transform="matrix(1,0,0,1,-326,-37)"/>
</g>
</svg>
