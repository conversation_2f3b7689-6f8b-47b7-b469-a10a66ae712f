{"cells": [{"cell_type": "markdown", "id": "6ac233d0", "metadata": {"origin_pos": 0}, "source": ["# 多层感知机的从零开始实现\n", ":label:`sec_mlp_scratch`\n", "\n", "我们已经在 :numref:`sec_mlp`中描述了多层感知机（MLP），\n", "现在让我们尝试自己实现一个多层感知机。\n", "为了与之前softmax回归（ :numref:`sec_softmax_scratch` ）\n", "获得的结果进行比较，\n", "我们将继续使用Fashion-MNIST图像分类数据集\n", "（ :numref:`sec_fashion_mnist`）。\n"]}, {"cell_type": "code", "execution_count": 2, "id": "6022f13f", "metadata": {"execution": {"iopub.execute_input": "2022-12-07T16:53:16.171822Z", "iopub.status.busy": "2022-12-07T16:53:16.171259Z", "iopub.status.idle": "2022-12-07T16:53:18.446980Z", "shell.execute_reply": "2022-12-07T16:53:18.446091Z"}, "origin_pos": 2, "tab": ["pytorch"]}, "outputs": [], "source": ["import torch\n", "from torch import nn\n", "from d2l import torch as d2l"]}, {"cell_type": "code", "execution_count": 3, "id": "800b1fdb", "metadata": {"execution": {"iopub.execute_input": "2022-12-07T16:53:18.450902Z", "iopub.status.busy": "2022-12-07T16:53:18.450400Z", "iopub.status.idle": "2022-12-07T16:53:18.558831Z", "shell.execute_reply": "2022-12-07T16:53:18.557933Z"}, "origin_pos": 5, "tab": ["pytorch"]}, "outputs": [], "source": ["batch_size = 256\n", "train_iter, test_iter = d2l.load_data_fashion_mnist(batch_size)"]}, {"cell_type": "markdown", "id": "5d8c8733", "metadata": {"origin_pos": 6}, "source": ["## 初始化模型参数\n", "\n", "回想一下，Fashion-MNIST中的每个图像由\n", "$28 \\times 28 = 784$个灰度像素值组成。\n", "所有图像共分为10个类别。\n", "忽略像素之间的空间结构，\n", "我们可以将每个图像视为具有784个输入特征\n", "和10个类的简单分类数据集。\n", "首先，我们将[**实现一个具有单隐藏层的多层感知机，\n", "它包含256个隐藏单元**]。\n", "注意，我们可以将这两个变量都视为超参数。\n", "通常，我们选择2的若干次幂作为层的宽度。\n", "因为内存在硬件中的分配和寻址方式，这么做往往可以在计算上更高效。\n", "\n", "我们用几个张量来表示我们的参数。\n", "注意，对于每一层我们都要记录一个权重矩阵和一个偏置向量。\n", "跟以前一样，我们要为损失关于这些参数的梯度分配内存。\n"]}, {"cell_type": "code", "execution_count": 11, "id": "49c7fe7a", "metadata": {"execution": {"iopub.execute_input": "2022-12-07T16:53:18.562702Z", "iopub.status.busy": "2022-12-07T16:53:18.562201Z", "iopub.status.idle": "2022-12-07T16:53:18.570702Z", "shell.execute_reply": "2022-12-07T16:53:18.569857Z"}, "origin_pos": 8, "tab": ["pytorch"]}, "outputs": [], "source": ["num_inputs, num_outputs, num_hiddens = 784, 10, 128\n", "\n", "W1 = nn.Parameter(torch.randn(\n", "    num_inputs, num_hiddens, requires_grad=True) * 0.01) # 特征数 * 神经元数 - 矩阵\n", "b1 = nn.Parameter(torch.zeros(num_hiddens, requires_grad=True)) # 神经元数 - 向量\n", "W2 = nn.Parameter(torch.randn(\n", "    num_hiddens, num_outputs, requires_grad=True) * 0.01) # 神经元数 * 分类数 - 矩阵\n", "b2 = nn.Parameter(torch.zeros(num_outputs, requires_grad=True)) # 分类数 - 向量\n", "\n", "params = [W1, b1, W2, b2]"]}, {"cell_type": "markdown", "id": "018c2cd1", "metadata": {"origin_pos": 11}, "source": ["## 激活函数\n", "\n", "为了确保我们对模型的细节了如指掌，\n", "我们将[**实现ReLU激活函数**]，\n", "而不是直接调用内置的`relu`函数。\n"]}, {"cell_type": "code", "execution_count": 12, "id": "c35aafce", "metadata": {"execution": {"iopub.execute_input": "2022-12-07T16:53:18.573966Z", "iopub.status.busy": "2022-12-07T16:53:18.573440Z", "iopub.status.idle": "2022-12-07T16:53:18.577442Z", "shell.execute_reply": "2022-12-07T16:53:18.576677Z"}, "origin_pos": 13, "tab": ["pytorch"]}, "outputs": [], "source": ["def relu(X):\n", "    a = torch.zeros_like(X)\n", "    return torch.max(X, a)"]}, {"cell_type": "markdown", "id": "39edbb28", "metadata": {"origin_pos": 16}, "source": ["## 模型\n", "\n", "因为我们忽略了空间结构，\n", "所以我们使用`reshape`将每个二维图像转换为一个长度为`num_inputs`的向量。\n", "只需几行代码就可以(**实现我们的模型**)。\n"]}, {"cell_type": "code", "execution_count": 14, "id": "b1e92687", "metadata": {"execution": {"iopub.execute_input": "2022-12-07T16:53:18.580954Z", "iopub.status.busy": "2022-12-07T16:53:18.580278Z", "iopub.status.idle": "2022-12-07T16:53:18.584613Z", "shell.execute_reply": "2022-12-07T16:53:18.583852Z"}, "origin_pos": 18, "tab": ["pytorch"]}, "outputs": [], "source": ["def net(X):\n", "    X = X.reshape((-1, num_inputs))\n", "    H = relu(X@W1 + b1)  # 这里“@”代表矩阵乘法\n", "    return (H@W2 + b2)"]}, {"cell_type": "markdown", "id": "e643487d", "metadata": {"origin_pos": 21}, "source": ["## 损失函数\n", "\n", "由于我们已经从零实现过softmax函数（ :numref:`sec_softmax_scratch`），\n", "因此在这里我们直接使用高级API中的内置函数来计算softmax和交叉熵损失。\n", "回想一下我们之前在 :numref:`subsec_softmax-implementation-revisited`中\n", "对这些复杂问题的讨论。\n", "我们鼓励感兴趣的读者查看损失函数的源代码，以加深对实现细节的了解。\n"]}, {"cell_type": "code", "execution_count": 6, "id": "04ab07e8", "metadata": {"execution": {"iopub.execute_input": "2022-12-07T16:53:18.587941Z", "iopub.status.busy": "2022-12-07T16:53:18.587334Z", "iopub.status.idle": "2022-12-07T16:53:18.591407Z", "shell.execute_reply": "2022-12-07T16:53:18.590560Z"}, "origin_pos": 23, "tab": ["pytorch"]}, "outputs": [], "source": ["loss = nn.CrossEntropyLoss(reduction='none')"]}, {"cell_type": "markdown", "id": "c7ef81b7", "metadata": {"origin_pos": 25}, "source": ["## 训练\n", "\n", "幸运的是，[**多层感知机的训练过程与softmax回归的训练过程完全相同**]。\n", "可以直接调用`d2l`包的`train_ch3`函数（参见 :numref:`sec_softmax_scratch` ），\n", "将迭代周期数设置为10，并将学习率设置为0.1.\n"]}, {"cell_type": "code", "execution_count": 16, "id": "b33bb4e4", "metadata": {"execution": {"iopub.execute_input": "2022-12-07T16:53:18.594433Z", "iopub.status.busy": "2022-12-07T16:53:18.594034Z", "iopub.status.idle": "2022-12-07T16:53:52.243707Z", "shell.execute_reply": "2022-12-07T16:53:52.242778Z"}, "origin_pos": 27, "tab": ["pytorch"]}, "outputs": [{"data": {"image/svg+xml": ["<?xml version=\"1.0\" encoding=\"utf-8\" standalone=\"no\"?>\n", "<!DOCTYPE svg PUBLIC \"-//W3C//DTD SVG 1.1//EN\"\n", "  \"http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd\">\n", "<svg xmlns:xlink=\"http://www.w3.org/1999/xlink\" width=\"238.965625pt\" height=\"183.35625pt\" viewBox=\"0 0 238.**********.35625\" xmlns=\"http://www.w3.org/2000/svg\" version=\"1.1\">\n", " <metadata>\n", "  <rdf:RDF xmlns:dc=\"http://purl.org/dc/elements/1.1/\" xmlns:cc=\"http://creativecommons.org/ns#\" xmlns:rdf=\"http://www.w3.org/1999/02/22-rdf-syntax-ns#\">\n", "   <cc:Work>\n", "    <dc:type rdf:resource=\"http://purl.org/dc/dcmitype/StillImage\"/>\n", "    <dc:date>2025-07-20T16:05:31.165324</dc:date>\n", "    <dc:format>image/svg+xml</dc:format>\n", "    <dc:creator>\n", "     <cc:Agent>\n", "      <dc:title>Matplotlib v3.5.1, https://matplotlib.org/</dc:title>\n", "     </cc:Agent>\n", "    </dc:creator>\n", "   </cc:Work>\n", "  </rdf:RDF>\n", " </metadata>\n", " <defs>\n", "  <style type=\"text/css\">*{stroke-linejoin: round; stroke-linecap: butt}</style>\n", " </defs>\n", " <g id=\"figure_1\">\n", "  <g id=\"patch_1\">\n", "   <path d=\"M 0 183.35625 \n", "L 238.**********.35625 \n", "L 238.965625 0 \n", "L 0 0 \n", "z\n", "\" style=\"fill: #ffffff\"/>\n", "  </g>\n", "  <g id=\"axes_1\">\n", "   <g id=\"patch_2\">\n", "    <path d=\"M 30.**********.8 \n", "L 225.**********.8 \n", "L 225.403125 7.2 \n", "L 30.103125 7.2 \n", "z\n", "\" style=\"fill: #ffffff\"/>\n", "   </g>\n", "   <g id=\"matplotlib.axis_1\">\n", "    <g id=\"xtick_1\">\n", "     <g id=\"line2d_1\">\n", "      <path d=\"M 51.803125 145.8 \n", "L 51.803125 7.2 \n", "\" clip-path=\"url(#pee30dfacd2)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_2\">\n", "      <defs>\n", "       <path id=\"m3516a8f740\" d=\"M 0 0 \n", "L 0 3.5 \n", "\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </defs>\n", "      <g>\n", "       <use xlink:href=\"#m3516a8f740\" x=\"51.803125\" y=\"145.8\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_1\">\n", "      <!-- 2 -->\n", "      <g transform=\"translate(48.621875 160.398438)scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-32\" d=\"M 1228 531 \n", "L 3431 531 \n", "L 3431 0 \n", "L 469 0 \n", "L 469 531 \n", "Q 828 903 1448 1529 \n", "Q 2069 2156 2228 2338 \n", "Q 2531 2678 2651 2914 \n", "Q 2772 3150 2772 3378 \n", "Q 2772 3750 2511 3984 \n", "Q 2250 4219 1831 4219 \n", "Q 1534 4219 1204 4116 \n", "Q 875 4013 500 3803 \n", "L 500 4441 \n", "Q 881 4594 1212 4672 \n", "Q 1544 4750 1819 4750 \n", "Q 2544 4750 2975 4387 \n", "Q 3406 4025 3406 3419 \n", "Q 3406 3131 3298 2873 \n", "Q 3191 2616 2906 2266 \n", "Q 2828 2175 2409 1742 \n", "Q 1991 1309 1228 531 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-32\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_2\">\n", "     <g id=\"line2d_3\">\n", "      <path d=\"M 95.203125 145.8 \n", "L 95.203125 7.2 \n", "\" clip-path=\"url(#pee30dfacd2)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_4\">\n", "      <g>\n", "       <use xlink:href=\"#m3516a8f740\" x=\"95.203125\" y=\"145.8\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_2\">\n", "      <!-- 4 -->\n", "      <g transform=\"translate(92.021875 160.398438)scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-34\" d=\"M 2419 4116 \n", "L 825 1625 \n", "L 2419 1625 \n", "L 2419 4116 \n", "z\n", "M 2253 4666 \n", "L 3047 4666 \n", "L 3047 1625 \n", "L 3713 1625 \n", "L 3713 1100 \n", "L 3047 1100 \n", "L 3047 0 \n", "L 2419 0 \n", "L 2419 1100 \n", "L 313 1100 \n", "L 313 1709 \n", "L 2253 4666 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-34\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_3\">\n", "     <g id=\"line2d_5\">\n", "      <path d=\"M 138.603125 145.8 \n", "L 138.603125 7.2 \n", "\" clip-path=\"url(#pee30dfacd2)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_6\">\n", "      <g>\n", "       <use xlink:href=\"#m3516a8f740\" x=\"138.603125\" y=\"145.8\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_3\">\n", "      <!-- 6 -->\n", "      <g transform=\"translate(135.421875 160.398438)scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-36\" d=\"M 2113 2584 \n", "Q 1688 2584 1439 2293 \n", "Q 1191 2003 1191 1497 \n", "Q 1191 994 1439 701 \n", "Q 1688 409 2113 409 \n", "Q 2538 409 2786 701 \n", "Q 3034 994 3034 1497 \n", "Q 3034 2003 2786 2293 \n", "Q 2538 2584 2113 2584 \n", "z\n", "M 3366 4563 \n", "L 3366 3988 \n", "Q 3128 4100 2886 4159 \n", "Q 2644 4219 2406 4219 \n", "Q 1781 4219 1451 3797 \n", "Q 1122 3375 1075 2522 \n", "Q 1259 2794 1537 2939 \n", "Q 1816 3084 2150 3084 \n", "Q 2853 3084 3261 2657 \n", "Q 3669 2231 3669 1497 \n", "Q 3669 778 3244 343 \n", "Q 2819 -91 2113 -91 \n", "Q 1303 -91 875 529 \n", "Q 447 1150 447 2328 \n", "Q 447 3434 972 4092 \n", "Q 1497 4750 2381 4750 \n", "Q 2619 4750 2861 4703 \n", "Q 3103 4656 3366 4563 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-36\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_4\">\n", "     <g id=\"line2d_7\">\n", "      <path d=\"M 182.003125 145.8 \n", "L 182.003125 7.2 \n", "\" clip-path=\"url(#pee30dfacd2)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_8\">\n", "      <g>\n", "       <use xlink:href=\"#m3516a8f740\" x=\"182.003125\" y=\"145.8\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_4\">\n", "      <!-- 8 -->\n", "      <g transform=\"translate(178.821875 160.398438)scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-38\" d=\"M 2034 2216 \n", "Q 1584 2216 1326 1975 \n", "Q 1069 1734 1069 1313 \n", "Q 1069 891 1326 650 \n", "Q 1584 409 2034 409 \n", "Q 2484 409 2743 651 \n", "Q 3003 894 3003 1313 \n", "Q 3003 1734 2745 1975 \n", "Q 2488 2216 2034 2216 \n", "z\n", "M 1403 2484 \n", "Q 997 2584 770 2862 \n", "Q 544 3141 544 3541 \n", "Q 544 4100 942 4425 \n", "Q 1341 4750 2034 4750 \n", "Q 2731 4750 3128 4425 \n", "Q 3525 4100 3525 3541 \n", "Q 3525 3141 3298 2862 \n", "Q 3072 2584 2669 2484 \n", "Q 3125 2378 3379 2068 \n", "Q 3634 1759 3634 1313 \n", "Q 3634 634 3220 271 \n", "Q 2806 -91 2034 -91 \n", "Q 1263 -91 848 271 \n", "Q 434 634 434 1313 \n", "Q 434 1759 690 2068 \n", "Q 947 2378 1403 2484 \n", "z\n", "M 1172 3481 \n", "Q 1172 3119 1398 2916 \n", "Q 1625 2713 2034 2713 \n", "Q 2441 2713 2670 2916 \n", "Q 2900 3119 2900 3481 \n", "Q 2900 3844 2670 4047 \n", "Q 2441 4250 2034 4250 \n", "Q 1625 4250 1398 4047 \n", "Q 1172 3844 1172 3481 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-38\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_5\">\n", "     <g id=\"line2d_9\">\n", "      <path d=\"M 225.**********.8 \n", "L 225.403125 7.2 \n", "\" clip-path=\"url(#pee30dfacd2)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_10\">\n", "      <g>\n", "       <use xlink:href=\"#m3516a8f740\" x=\"225.403125\" y=\"145.8\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_5\">\n", "      <!-- 10 -->\n", "      <g transform=\"translate(219.040625 160.398438)scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-31\" d=\"M 794 531 \n", "L 1825 531 \n", "L 1825 4091 \n", "L 703 3866 \n", "L 703 4441 \n", "L 1819 4666 \n", "L 2450 4666 \n", "L 2450 531 \n", "L 3481 531 \n", "L 3481 0 \n", "L 794 0 \n", "L 794 531 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "        <path id=\"DejaVuSans-30\" d=\"M 2034 4250 \n", "Q 1547 4250 1301 3770 \n", "Q 1056 3291 1056 2328 \n", "Q 1056 1369 1301 889 \n", "Q 1547 409 2034 409 \n", "Q 2525 409 2770 889 \n", "Q 3016 1369 3016 2328 \n", "Q 3016 3291 2770 3770 \n", "Q 2525 4250 2034 4250 \n", "z\n", "M 2034 4750 \n", "Q 2819 4750 3233 4129 \n", "Q 3647 3509 3647 2328 \n", "Q 3647 1150 3233 529 \n", "Q 2819 -91 2034 -91 \n", "Q 1250 -91 836 529 \n", "Q 422 1150 422 2328 \n", "Q 422 3509 836 4129 \n", "Q 1250 4750 2034 4750 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-31\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"63.623047\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"text_6\">\n", "     <!-- epoch -->\n", "     <g transform=\"translate(112.525 174.076563)scale(0.1 -0.1)\">\n", "      <defs>\n", "       <path id=\"DejaVuSans-65\" d=\"M 3597 1894 \n", "L 3597 1613 \n", "L 953 1613 \n", "Q 991 1019 1311 708 \n", "Q 1631 397 2203 397 \n", "Q 2534 397 2845 478 \n", "Q 3156 559 3463 722 \n", "L 3463 178 \n", "Q 3153 47 2828 -22 \n", "Q 2503 -91 2169 -91 \n", "Q 1331 -91 842 396 \n", "Q 353 884 353 1716 \n", "Q 353 2575 817 3079 \n", "Q 1281 3584 2069 3584 \n", "Q 2775 3584 3186 3129 \n", "Q 3597 2675 3597 1894 \n", "z\n", "M 3022 2063 \n", "Q 3016 2534 2758 2815 \n", "Q 2500 3097 2075 3097 \n", "Q 1594 3097 1305 2825 \n", "Q 1016 2553 972 2059 \n", "L 3022 2063 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-70\" d=\"M 1159 525 \n", "L 1159 -1331 \n", "L 581 -1331 \n", "L 581 3500 \n", "L 1159 3500 \n", "L 1159 2969 \n", "Q 1341 3281 1617 3432 \n", "Q 1894 3584 2278 3584 \n", "Q 2916 3584 3314 3078 \n", "Q 3713 2572 3713 1747 \n", "Q 3713 922 3314 415 \n", "Q 2916 -91 2278 -91 \n", "Q 1894 -91 1617 61 \n", "Q 1341 213 1159 525 \n", "z\n", "M 3116 1747 \n", "Q 3116 2381 2855 2742 \n", "Q 2594 3103 2138 3103 \n", "Q 1681 3103 1420 2742 \n", "Q 1159 2381 1159 1747 \n", "Q 1159 1113 1420 752 \n", "Q 1681 391 2138 391 \n", "Q 2594 391 2855 752 \n", "Q 3116 1113 3116 1747 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-6f\" d=\"M 1959 3097 \n", "Q 1497 3097 1228 2736 \n", "Q 959 2375 959 1747 \n", "Q 959 1119 1226 758 \n", "Q 1494 397 1959 397 \n", "Q 2419 397 2687 759 \n", "Q 2956 1122 2956 1747 \n", "Q 2956 2369 2687 2733 \n", "Q 2419 3097 1959 3097 \n", "z\n", "M 1959 3584 \n", "Q 2709 3584 3137 3096 \n", "Q 3566 2609 3566 1747 \n", "Q 3566 888 3137 398 \n", "Q 2709 -91 1959 -91 \n", "Q 1206 -91 779 398 \n", "Q 353 888 353 1747 \n", "Q 353 2609 779 3096 \n", "Q 1206 3584 1959 3584 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-63\" d=\"M 3122 3366 \n", "L 3122 2828 \n", "Q 2878 2963 2633 3030 \n", "Q 2388 3097 2138 3097 \n", "Q 1578 3097 1268 2742 \n", "Q 959 2388 959 1747 \n", "Q 959 1106 1268 751 \n", "Q 1578 397 2138 397 \n", "Q 2388 397 2633 464 \n", "Q 2878 531 3122 666 \n", "L 3122 134 \n", "Q 2881 22 2623 -34 \n", "Q 2366 -91 2075 -91 \n", "Q 1284 -91 818 406 \n", "Q 353 903 353 1747 \n", "Q 353 2603 823 3093 \n", "Q 1294 3584 2113 3584 \n", "Q 2378 3584 2631 3529 \n", "Q 2884 3475 3122 3366 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-68\" d=\"M 3513 2113 \n", "L 3513 0 \n", "L 2938 0 \n", "L 2938 2094 \n", "Q 2938 2591 2744 2837 \n", "Q 2550 3084 2163 3084 \n", "Q 1697 3084 1428 2787 \n", "Q 1159 2491 1159 1978 \n", "L 1159 0 \n", "L 581 0 \n", "L 581 4863 \n", "L 1159 4863 \n", "L 1159 2956 \n", "Q 1366 3272 1645 3428 \n", "Q 1925 3584 2291 3584 \n", "Q 2894 3584 3203 3211 \n", "Q 3513 2838 3513 2113 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "      </defs>\n", "      <use xlink:href=\"#DejaVuSans-65\"/>\n", "      <use xlink:href=\"#DejaVuSans-70\" x=\"61.523438\"/>\n", "      <use xlink:href=\"#DejaVuSans-6f\" x=\"125\"/>\n", "      <use xlink:href=\"#DejaVuSans-63\" x=\"186.181641\"/>\n", "      <use xlink:href=\"#DejaVuSans-68\" x=\"241.162109\"/>\n", "     </g>\n", "    </g>\n", "   </g>\n", "   <g id=\"matplotlib.axis_2\">\n", "    <g id=\"ytick_1\">\n", "     <g id=\"line2d_11\">\n", "      <path d=\"M 30.103125 122.7 \n", "L 225.403125 122.7 \n", "\" clip-path=\"url(#pee30dfacd2)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_12\">\n", "      <defs>\n", "       <path id=\"m3890691683\" d=\"M 0 0 \n", "L -3.5 0 \n", "\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </defs>\n", "      <g>\n", "       <use xlink:href=\"#m3890691683\" x=\"30.103125\" y=\"122.7\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_7\">\n", "      <!-- 0.4 -->\n", "      <g transform=\"translate(7.2 126.499219)scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-2e\" d=\"M 684 794 \n", "L 1344 794 \n", "L 1344 0 \n", "L 684 0 \n", "L 684 794 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-34\" x=\"95.410156\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_2\">\n", "     <g id=\"line2d_13\">\n", "      <path d=\"M 30.103125 76.5 \n", "L 225.403125 76.5 \n", "\" clip-path=\"url(#pee30dfacd2)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_14\">\n", "      <g>\n", "       <use xlink:href=\"#m3890691683\" x=\"30.103125\" y=\"76.5\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_8\">\n", "      <!-- 0.6 -->\n", "      <g transform=\"translate(7.2 80.299219)scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-36\" x=\"95.410156\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_3\">\n", "     <g id=\"line2d_15\">\n", "      <path d=\"M 30.103125 30.3 \n", "L 225.403125 30.3 \n", "\" clip-path=\"url(#pee30dfacd2)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_16\">\n", "      <g>\n", "       <use xlink:href=\"#m3890691683\" x=\"30.103125\" y=\"30.3\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_9\">\n", "      <!-- 0.8 -->\n", "      <g transform=\"translate(7.2 34.099219)scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-38\" x=\"95.410156\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "   </g>\n", "   <g id=\"line2d_17\">\n", "    <path d=\"M 30.103125 127.596238 \n", "L 51.803125 129.942457 \n", "L 73.503125 130.140772 \n", "L 95.203125 130.255064 \n", "L 116.903125 130.338652 \n", "L 138.603125 130.395005 \n", "L 160.303125 130.451591 \n", "L 182.003125 130.491907 \n", "L 203.703125 130.530421 \n", "L 225.403125 130.567522 \n", "\" clip-path=\"url(#pee30dfacd2)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_18\">\n", "    <path d=\"M 30.103125 14.9462 \n", "L 51.803125 14.01065 \n", "L 73.503125 13.94135 \n", "L 95.203125 13.87975 \n", "L 116.903125 13.9529 \n", "L 138.603125 13.9221 \n", "L 160.303125 13.9067 \n", "L 182.003125 13.8759 \n", "L 203.703125 13.87205 \n", "L 225.403125 13.86435 \n", "\" clip-path=\"url(#pee30dfacd2)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #bf00bf; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_19\">\n", "    <path d=\"M 30.103125 17.826 \n", "L 51.803125 17.6643 \n", "L 73.503125 17.6412 \n", "L 95.203125 17.6181 \n", "L 116.903125 17.7105 \n", "L 138.603125 17.595 \n", "L 160.303125 17.6181 \n", "L 182.003125 17.7336 \n", "L 203.703125 17.5026 \n", "L 225.403125 17.5257 \n", "\" clip-path=\"url(#pee30dfacd2)\" style=\"fill: none; stroke-dasharray: 9.6,2.4,1.5,2.4; stroke-dashoffset: 0; stroke: #008000; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"patch_3\">\n", "    <path d=\"M 30.**********.8 \n", "L 30.103125 7.2 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_4\">\n", "    <path d=\"M 225.**********.8 \n", "L 225.403125 7.2 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_5\">\n", "    <path d=\"M 30.**********.8 \n", "L 225.**********.8 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_6\">\n", "    <path d=\"M 30.103125 7.2 \n", "L 225.403125 7.2 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"legend_1\">\n", "    <g id=\"patch_7\">\n", "     <path d=\"M 140.634375 100.017188 \n", "L 218.403125 100.017188 \n", "Q 220.403125 100.017188 220.403125 98.017188 \n", "L 220.403125 54.982812 \n", "Q 220.403125 52.982812 218.403125 52.982812 \n", "L 140.634375 52.982812 \n", "Q 138.634375 52.982812 138.634375 54.982812 \n", "L 138.634375 98.017188 \n", "Q 138.634375 100.017188 140.634375 100.017188 \n", "z\n", "\" style=\"fill: #ffffff; opacity: 0.8; stroke: #cccccc; stroke-linejoin: miter\"/>\n", "    </g>\n", "    <g id=\"line2d_20\">\n", "     <path d=\"M 142.634375 61.08125 \n", "L 152.634375 61.08125 \n", "L 162.634375 61.08125 \n", "\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "    </g>\n", "    <g id=\"text_10\">\n", "     <!-- train loss -->\n", "     <g transform=\"translate(170.634375 64.58125)scale(0.1 -0.1)\">\n", "      <defs>\n", "       <path id=\"DejaVuSans-74\" d=\"M 1172 4494 \n", "L 1172 3500 \n", "L 2356 3500 \n", "L 2356 3053 \n", "L 1172 3053 \n", "L 1172 1153 \n", "Q 1172 725 1289 603 \n", "Q 1406 481 1766 481 \n", "L 2356 481 \n", "L 2356 0 \n", "L 1766 0 \n", "Q 1100 0 847 248 \n", "Q 594 497 594 1153 \n", "L 594 3053 \n", "L 172 3053 \n", "L 172 3500 \n", "L 594 3500 \n", "L 594 4494 \n", "L 1172 4494 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-72\" d=\"M 2631 2963 \n", "Q 2534 3019 2420 3045 \n", "Q 2306 3072 2169 3072 \n", "Q 1681 3072 1420 2755 \n", "Q 1159 2438 1159 1844 \n", "L 1159 0 \n", "L 581 0 \n", "L 581 3500 \n", "L 1159 3500 \n", "L 1159 2956 \n", "Q 1341 3275 1631 3429 \n", "Q 1922 3584 2338 3584 \n", "Q 2397 3584 2469 3576 \n", "Q 2541 3569 2628 3553 \n", "L 2631 2963 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-61\" d=\"M 2194 1759 \n", "Q 1497 1759 1228 1600 \n", "Q 959 1441 959 1056 \n", "Q 959 750 1161 570 \n", "Q 1363 391 1709 391 \n", "Q 2188 391 2477 730 \n", "Q 2766 1069 2766 1631 \n", "L 2766 1759 \n", "L 2194 1759 \n", "z\n", "M 3341 1997 \n", "L 3341 0 \n", "L 2766 0 \n", "L 2766 531 \n", "Q 2569 213 2275 61 \n", "Q 1981 -91 1556 -91 \n", "Q 1019 -91 701 211 \n", "Q 384 513 384 1019 \n", "Q 384 1609 779 1909 \n", "Q 1175 2209 1959 2209 \n", "L 2766 2209 \n", "L 2766 2266 \n", "Q 2766 2663 2505 2880 \n", "Q 2244 3097 1772 3097 \n", "Q 1472 3097 1187 3025 \n", "Q 903 2953 641 2809 \n", "L 641 3341 \n", "Q 956 3463 1253 3523 \n", "Q 1550 3584 1831 3584 \n", "Q 2591 3584 2966 3190 \n", "Q 3341 2797 3341 1997 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-69\" d=\"M 603 3500 \n", "L 1178 3500 \n", "L 1178 0 \n", "L 603 0 \n", "L 603 3500 \n", "z\n", "M 603 4863 \n", "L 1178 4863 \n", "L 1178 4134 \n", "L 603 4134 \n", "L 603 4863 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-6e\" d=\"M 3513 2113 \n", "L 3513 0 \n", "L 2938 0 \n", "L 2938 2094 \n", "Q 2938 2591 2744 2837 \n", "Q 2550 3084 2163 3084 \n", "Q 1697 3084 1428 2787 \n", "Q 1159 2491 1159 1978 \n", "L 1159 0 \n", "L 581 0 \n", "L 581 3500 \n", "L 1159 3500 \n", "L 1159 2956 \n", "Q 1366 3272 1645 3428 \n", "Q 1925 3584 2291 3584 \n", "Q 2894 3584 3203 3211 \n", "Q 3513 2838 3513 2113 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-20\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-6c\" d=\"M 603 4863 \n", "L 1178 4863 \n", "L 1178 0 \n", "L 603 0 \n", "L 603 4863 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-73\" d=\"M 2834 3397 \n", "L 2834 2853 \n", "Q 2591 2978 2328 3040 \n", "Q 2066 3103 1784 3103 \n", "Q 1356 3103 1142 2972 \n", "Q 928 2841 928 2578 \n", "Q 928 2378 1081 2264 \n", "Q 1234 2150 1697 2047 \n", "L 1894 2003 \n", "Q 2506 1872 2764 1633 \n", "Q 3022 1394 3022 966 \n", "Q 3022 478 2636 193 \n", "Q 2250 -91 1575 -91 \n", "Q 1294 -91 989 -36 \n", "Q 684 19 347 128 \n", "L 347 722 \n", "Q 666 556 975 473 \n", "Q 1284 391 1588 391 \n", "Q 1994 391 2212 530 \n", "Q 2431 669 2431 922 \n", "Q 2431 1156 2273 1281 \n", "Q 2116 1406 1581 1522 \n", "L 1381 1569 \n", "Q 847 1681 609 1914 \n", "Q 372 2147 372 2553 \n", "Q 372 3047 722 3315 \n", "Q 1072 3584 1716 3584 \n", "Q 2034 3584 2315 3537 \n", "Q 2597 3491 2834 3397 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "      </defs>\n", "      <use xlink:href=\"#DejaVuSans-74\"/>\n", "      <use xlink:href=\"#DejaVuSans-72\" x=\"39.208984\"/>\n", "      <use xlink:href=\"#DejaVuSans-61\" x=\"80.322266\"/>\n", "      <use xlink:href=\"#DejaVuSans-69\" x=\"141.601562\"/>\n", "      <use xlink:href=\"#DejaVuSans-6e\" x=\"169.384766\"/>\n", "      <use xlink:href=\"#DejaVuSans-20\" x=\"232.763672\"/>\n", "      <use xlink:href=\"#DejaVuSans-6c\" x=\"264.550781\"/>\n", "      <use xlink:href=\"#DejaVuSans-6f\" x=\"292.333984\"/>\n", "      <use xlink:href=\"#DejaVuSans-73\" x=\"353.515625\"/>\n", "      <use xlink:href=\"#DejaVuSans-73\" x=\"405.615234\"/>\n", "     </g>\n", "    </g>\n", "    <g id=\"line2d_21\">\n", "     <path d=\"M 142.634375 75.759375 \n", "L 152.634375 75.759375 \n", "L 162.634375 75.759375 \n", "\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #bf00bf; stroke-width: 1.5\"/>\n", "    </g>\n", "    <g id=\"text_11\">\n", "     <!-- train acc -->\n", "     <g transform=\"translate(170.634375 79.259375)scale(0.1 -0.1)\">\n", "      <use xlink:href=\"#DejaVuSans-74\"/>\n", "      <use xlink:href=\"#DejaVuSans-72\" x=\"39.208984\"/>\n", "      <use xlink:href=\"#DejaVuSans-61\" x=\"80.322266\"/>\n", "      <use xlink:href=\"#DejaVuSans-69\" x=\"141.601562\"/>\n", "      <use xlink:href=\"#DejaVuSans-6e\" x=\"169.384766\"/>\n", "      <use xlink:href=\"#DejaVuSans-20\" x=\"232.763672\"/>\n", "      <use xlink:href=\"#DejaVuSans-61\" x=\"264.550781\"/>\n", "      <use xlink:href=\"#DejaVuSans-63\" x=\"325.830078\"/>\n", "      <use xlink:href=\"#DejaVuSans-63\" x=\"380.810547\"/>\n", "     </g>\n", "    </g>\n", "    <g id=\"line2d_22\">\n", "     <path d=\"M 142.634375 90.4375 \n", "L 152.634375 90.4375 \n", "L 162.634375 90.4375 \n", "\" style=\"fill: none; stroke-dasharray: 9.6,2.4,1.5,2.4; stroke-dashoffset: 0; stroke: #008000; stroke-width: 1.5\"/>\n", "    </g>\n", "    <g id=\"text_12\">\n", "     <!-- test acc -->\n", "     <g transform=\"translate(170.634375 93.9375)scale(0.1 -0.1)\">\n", "      <use xlink:href=\"#DejaVuSans-74\"/>\n", "      <use xlink:href=\"#DejaVuSans-65\" x=\"39.208984\"/>\n", "      <use xlink:href=\"#DejaVuSans-73\" x=\"100.732422\"/>\n", "      <use xlink:href=\"#DejaVuSans-74\" x=\"152.832031\"/>\n", "      <use xlink:href=\"#DejaVuSans-20\" x=\"192.041016\"/>\n", "      <use xlink:href=\"#DejaVuSans-61\" x=\"223.828125\"/>\n", "      <use xlink:href=\"#DejaVuSans-63\" x=\"285.107422\"/>\n", "      <use xlink:href=\"#DejaVuSans-63\" x=\"340.087891\"/>\n", "     </g>\n", "    </g>\n", "   </g>\n", "  </g>\n", " </g>\n", " <defs>\n", "  <clipPath id=\"pee30dfacd2\">\n", "   <rect x=\"30.103125\" y=\"7.2\" width=\"195.3\" height=\"138.6\"/>\n", "  </clipPath>\n", " </defs>\n", "</svg>\n"], "text/plain": ["<Figure size 350x250 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["num_epochs, lr = 10, 0.001\n", "updater = torch.optim.SGD(params, lr=lr)\n", "d2l.train_ch3(net, train_iter, test_iter, loss, num_epochs, updater)"]}, {"cell_type": "markdown", "id": "e758d73b", "metadata": {"origin_pos": 30}, "source": ["为了对学习到的模型进行评估，我们将[**在一些测试数据上应用这个模型**]。\n"]}, {"cell_type": "code", "execution_count": 10, "id": "635b1066", "metadata": {"execution": {"iopub.execute_input": "2022-12-07T16:53:52.247758Z", "iopub.status.busy": "2022-12-07T16:53:52.246974Z", "iopub.status.idle": "2022-12-07T16:53:52.810293Z", "shell.execute_reply": "2022-12-07T16:53:52.808744Z"}, "origin_pos": 31, "tab": ["pytorch"]}, "outputs": [{"data": {"image/svg+xml": ["<?xml version=\"1.0\" encoding=\"utf-8\" standalone=\"no\"?>\n", "<!DOCTYPE svg PUBLIC \"-//W3C//DTD SVG 1.1//EN\"\n", "  \"http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd\">\n", "<svg xmlns:xlink=\"http://www.w3.org/1999/xlink\" width=\"520.1pt\" height=\"118.198357pt\" viewBox=\"0 0 520.1 118.198357\" xmlns=\"http://www.w3.org/2000/svg\" version=\"1.1\">\n", " <metadata>\n", "  <rdf:RDF xmlns:dc=\"http://purl.org/dc/elements/1.1/\" xmlns:cc=\"http://creativecommons.org/ns#\" xmlns:rdf=\"http://www.w3.org/1999/02/22-rdf-syntax-ns#\">\n", "   <cc:Work>\n", "    <dc:type rdf:resource=\"http://purl.org/dc/dcmitype/StillImage\"/>\n", "    <dc:date>2025-07-20T15:58:18.131372</dc:date>\n", "    <dc:format>image/svg+xml</dc:format>\n", "    <dc:creator>\n", "     <cc:Agent>\n", "      <dc:title>Matplotlib v3.5.1, https://matplotlib.org/</dc:title>\n", "     </cc:Agent>\n", "    </dc:creator>\n", "   </cc:Work>\n", "  </rdf:RDF>\n", " </metadata>\n", " <defs>\n", "  <style type=\"text/css\">*{stroke-linejoin: round; stroke-linecap: butt}</style>\n", " </defs>\n", " <g id=\"figure_1\">\n", "  <g id=\"patch_1\">\n", "   <path d=\"M 0 118.198357 \n", "L 520.1 118.198357 \n", "L 520.1 0 \n", "L 0 0 \n", "z\n", "\" style=\"fill: #ffffff\"/>\n", "  </g>\n", "  <g id=\"axes_1\">\n", "   <g id=\"patch_2\">\n", "    <path d=\"M 10.7 107.498357 \n", "L 82.**********.498357 \n", "L 82.442857 35.7555 \n", "L 10.7 35.7555 \n", "z\n", "\" style=\"fill: #ffffff\"/>\n", "   </g>\n", "   <g clip-path=\"url(#pb75e02419d)\">\n", "    <image xlink:href=\"data:image/png;base64,\n", "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\" id=\"image8d4ad0f9ec\" transform=\"scale(1 -1)translate(0 -72)\" x=\"10.7\" y=\"-35.498357\" width=\"72\" height=\"72\"/>\n", "   </g>\n", "   <g id=\"patch_3\">\n", "    <path d=\"M 10.7 107.498357 \n", "L 10.7 35.7555 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_4\">\n", "    <path d=\"M 82.**********.498357 \n", "L 82.442857 35.7555 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_5\">\n", "    <path d=\"M 10.7 107.498357 \n", "L 82.**********.498357 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_6\">\n", "    <path d=\"M 10.7 35.7555 \n", "L 82.442857 35.7555 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"text_1\">\n", "    <!-- ankle boot -->\n", "    <g transform=\"translate(14.848304 16.318125)scale(0.12 -0.12)\">\n", "     <defs>\n", "      <path id=\"DejaVuSans-61\" d=\"M 2194 1759 \n", "Q 1497 1759 1228 1600 \n", "Q 959 1441 959 1056 \n", "Q 959 750 1161 570 \n", "Q 1363 391 1709 391 \n", "Q 2188 391 2477 730 \n", "Q 2766 1069 2766 1631 \n", "L 2766 1759 \n", "L 2194 1759 \n", "z\n", "M 3341 1997 \n", "L 3341 0 \n", "L 2766 0 \n", "L 2766 531 \n", "Q 2569 213 2275 61 \n", "Q 1981 -91 1556 -91 \n", "Q 1019 -91 701 211 \n", "Q 384 513 384 1019 \n", "Q 384 1609 779 1909 \n", "Q 1175 2209 1959 2209 \n", "L 2766 2209 \n", "L 2766 2266 \n", "Q 2766 2663 2505 2880 \n", "Q 2244 3097 1772 3097 \n", "Q 1472 3097 1187 3025 \n", "Q 903 2953 641 2809 \n", "L 641 3341 \n", "Q 956 3463 1253 3523 \n", "Q 1550 3584 1831 3584 \n", "Q 2591 3584 2966 3190 \n", "Q 3341 2797 3341 1997 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "      <path id=\"DejaVuSans-6e\" d=\"M 3513 2113 \n", "L 3513 0 \n", "L 2938 0 \n", "L 2938 2094 \n", "Q 2938 2591 2744 2837 \n", "Q 2550 3084 2163 3084 \n", "Q 1697 3084 1428 2787 \n", "Q 1159 2491 1159 1978 \n", "L 1159 0 \n", "L 581 0 \n", "L 581 3500 \n", "L 1159 3500 \n", "L 1159 2956 \n", "Q 1366 3272 1645 3428 \n", "Q 1925 3584 2291 3584 \n", "Q 2894 3584 3203 3211 \n", "Q 3513 2838 3513 2113 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "      <path id=\"DejaVuSans-6b\" d=\"M 581 4863 \n", "L 1159 4863 \n", "L 1159 1991 \n", "L 2875 3500 \n", "L 3609 3500 \n", "L 1753 1863 \n", "L 3688 0 \n", "L 2938 0 \n", "L 1159 1709 \n", "L 1159 0 \n", "L 581 0 \n", "L 581 4863 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "      <path id=\"DejaVuSans-6c\" d=\"M 603 4863 \n", "L 1178 4863 \n", "L 1178 0 \n", "L 603 0 \n", "L 603 4863 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "      <path id=\"DejaVuSans-65\" d=\"M 3597 1894 \n", "L 3597 1613 \n", "L 953 1613 \n", "Q 991 1019 1311 708 \n", "Q 1631 397 2203 397 \n", "Q 2534 397 2845 478 \n", "Q 3156 559 3463 722 \n", "L 3463 178 \n", "Q 3153 47 2828 -22 \n", "Q 2503 -91 2169 -91 \n", "Q 1331 -91 842 396 \n", "Q 353 884 353 1716 \n", "Q 353 2575 817 3079 \n", "Q 1281 3584 2069 3584 \n", "Q 2775 3584 3186 3129 \n", "Q 3597 2675 3597 1894 \n", "z\n", "M 3022 2063 \n", "Q 3016 2534 2758 2815 \n", "Q 2500 3097 2075 3097 \n", "Q 1594 3097 1305 2825 \n", "Q 1016 2553 972 2059 \n", "L 3022 2063 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "      <path id=\"DejaVuSans-20\" transform=\"scale(0.015625)\"/>\n", "      <path id=\"DejaVuSans-62\" d=\"M 3116 1747 \n", "Q 3116 2381 2855 2742 \n", "Q 2594 3103 2138 3103 \n", "Q 1681 3103 1420 2742 \n", "Q 1159 2381 1159 1747 \n", "Q 1159 1113 1420 752 \n", "Q 1681 391 2138 391 \n", "Q 2594 391 2855 752 \n", "Q 3116 1113 3116 1747 \n", "z\n", "M 1159 2969 \n", "Q 1341 3281 1617 3432 \n", "Q 1894 3584 2278 3584 \n", "Q 2916 3584 3314 3078 \n", "Q 3713 2572 3713 1747 \n", "Q 3713 922 3314 415 \n", "Q 2916 -91 2278 -91 \n", "Q 1894 -91 1617 61 \n", "Q 1341 213 1159 525 \n", "L 1159 0 \n", "L 581 0 \n", "L 581 4863 \n", "L 1159 4863 \n", "L 1159 2969 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "      <path id=\"DejaVuSans-6f\" d=\"M 1959 3097 \n", "Q 1497 3097 1228 2736 \n", "Q 959 2375 959 1747 \n", "Q 959 1119 1226 758 \n", "Q 1494 397 1959 397 \n", "Q 2419 397 2687 759 \n", "Q 2956 1122 2956 1747 \n", "Q 2956 2369 2687 2733 \n", "Q 2419 3097 1959 3097 \n", "z\n", "M 1959 3584 \n", "Q 2709 3584 3137 3096 \n", "Q 3566 2609 3566 1747 \n", "Q 3566 888 3137 398 \n", "Q 2709 -91 1959 -91 \n", "Q 1206 -91 779 398 \n", "Q 353 888 353 1747 \n", "Q 353 2609 779 3096 \n", "Q 1206 3584 1959 3584 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "      <path id=\"DejaVuSans-74\" d=\"M 1172 4494 \n", "L 1172 3500 \n", "L 2356 3500 \n", "L 2356 3053 \n", "L 1172 3053 \n", "L 1172 1153 \n", "Q 1172 725 1289 603 \n", "Q 1406 481 1766 481 \n", "L 2356 481 \n", "L 2356 0 \n", "L 1766 0 \n", "Q 1100 0 847 248 \n", "Q 594 497 594 1153 \n", "L 594 3053 \n", "L 172 3053 \n", "L 172 3500 \n", "L 594 3500 \n", "L 594 4494 \n", "L 1172 4494 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "     </defs>\n", "     <use xlink:href=\"#DejaVuSans-61\"/>\n", "     <use xlink:href=\"#DejaVuSans-6e\" x=\"61.279297\"/>\n", "     <use xlink:href=\"#DejaVuSans-6b\" x=\"124.658203\"/>\n", "     <use xlink:href=\"#DejaVuSans-6c\" x=\"182.568359\"/>\n", "     <use xlink:href=\"#DejaVuSans-65\" x=\"210.351562\"/>\n", "     <use xlink:href=\"#DejaVuSans-20\" x=\"271.875\"/>\n", "     <use xlink:href=\"#DejaVuSans-62\" x=\"303.662109\"/>\n", "     <use xlink:href=\"#DejaVuSans-6f\" x=\"367.138672\"/>\n", "     <use xlink:href=\"#DejaVuSans-6f\" x=\"428.320312\"/>\n", "     <use xlink:href=\"#DejaVuSans-74\" x=\"489.501953\"/>\n", "    </g>\n", "    <!-- ankle boot -->\n", "    <g transform=\"translate(14.848304 29.7555)scale(0.12 -0.12)\">\n", "     <use xlink:href=\"#DejaVuSans-61\"/>\n", "     <use xlink:href=\"#DejaVuSans-6e\" x=\"61.279297\"/>\n", "     <use xlink:href=\"#DejaVuSans-6b\" x=\"124.658203\"/>\n", "     <use xlink:href=\"#DejaVuSans-6c\" x=\"182.568359\"/>\n", "     <use xlink:href=\"#DejaVuSans-65\" x=\"210.351562\"/>\n", "     <use xlink:href=\"#DejaVuSans-20\" x=\"271.875\"/>\n", "     <use xlink:href=\"#DejaVuSans-62\" x=\"303.662109\"/>\n", "     <use xlink:href=\"#DejaVuSans-6f\" x=\"367.138672\"/>\n", "     <use xlink:href=\"#DejaVuSans-6f\" x=\"428.320312\"/>\n", "     <use xlink:href=\"#DejaVuSans-74\" x=\"489.501953\"/>\n", "    </g>\n", "   </g>\n", "  </g>\n", "  <g id=\"axes_2\">\n", "   <g id=\"patch_7\">\n", "    <path d=\"M 96.**********.498357 \n", "L 168.534286 107.498357 \n", "L 168.534286 35.7555 \n", "L 96.791429 35.7555 \n", "z\n", "\" style=\"fill: #ffffff\"/>\n", "   </g>\n", "   <g clip-path=\"url(#p2057e4ef54)\">\n", "    <image xlink:href=\"data:image/png;base64,\n", "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\" id=\"imageea706a2e9b\" transform=\"scale(1 -1)translate(0 -72)\" x=\"96.791429\" y=\"-35.498357\" width=\"72\" height=\"72\"/>\n", "   </g>\n", "   <g id=\"patch_8\">\n", "    <path d=\"M 96.**********.498357 \n", "L 96.791429 35.7555 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_9\">\n", "    <path d=\"M 168.534286 107.498357 \n", "L 168.534286 35.7555 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_10\">\n", "    <path d=\"M 96.**********.498357 \n", "L 168.534286 107.498357 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_11\">\n", "    <path d=\"M 96.791429 35.7555 \n", "L 168.534286 35.7555 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"text_2\">\n", "    <!-- pullover -->\n", "    <g transform=\"translate(108.336607 16.318125)scale(0.12 -0.12)\">\n", "     <defs>\n", "      <path id=\"DejaVuSans-70\" d=\"M 1159 525 \n", "L 1159 -1331 \n", "L 581 -1331 \n", "L 581 3500 \n", "L 1159 3500 \n", "L 1159 2969 \n", "Q 1341 3281 1617 3432 \n", "Q 1894 3584 2278 3584 \n", "Q 2916 3584 3314 3078 \n", "Q 3713 2572 3713 1747 \n", "Q 3713 922 3314 415 \n", "Q 2916 -91 2278 -91 \n", "Q 1894 -91 1617 61 \n", "Q 1341 213 1159 525 \n", "z\n", "M 3116 1747 \n", "Q 3116 2381 2855 2742 \n", "Q 2594 3103 2138 3103 \n", "Q 1681 3103 1420 2742 \n", "Q 1159 2381 1159 1747 \n", "Q 1159 1113 1420 752 \n", "Q 1681 391 2138 391 \n", "Q 2594 391 2855 752 \n", "Q 3116 1113 3116 1747 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "      <path id=\"DejaVuSans-75\" d=\"M 544 1381 \n", "L 544 3500 \n", "L 1119 3500 \n", "L 1119 1403 \n", "Q 1119 906 1312 657 \n", "Q 1506 409 1894 409 \n", "Q 2359 409 2629 706 \n", "Q 2900 1003 2900 1516 \n", "L 2900 3500 \n", "L 3475 3500 \n", "L 3475 0 \n", "L 2900 0 \n", "L 2900 538 \n", "Q 2691 219 2414 64 \n", "Q 2138 -91 1772 -91 \n", "Q 1169 -91 856 284 \n", "Q 544 659 544 1381 \n", "z\n", "M 1991 3584 \n", "L 1991 3584 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "      <path id=\"DejaVuSans-76\" d=\"M 191 3500 \n", "L 800 3500 \n", "L 1894 563 \n", "L 2988 3500 \n", "L 3597 3500 \n", "L 2284 0 \n", "L 1503 0 \n", "L 191 3500 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "      <path id=\"DejaVuSans-72\" d=\"M 2631 2963 \n", "Q 2534 3019 2420 3045 \n", "Q 2306 3072 2169 3072 \n", "Q 1681 3072 1420 2755 \n", "Q 1159 2438 1159 1844 \n", "L 1159 0 \n", "L 581 0 \n", "L 581 3500 \n", "L 1159 3500 \n", "L 1159 2956 \n", "Q 1341 3275 1631 3429 \n", "Q 1922 3584 2338 3584 \n", "Q 2397 3584 2469 3576 \n", "Q 2541 3569 2628 3553 \n", "L 2631 2963 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "     </defs>\n", "     <use xlink:href=\"#DejaVuSans-70\"/>\n", "     <use xlink:href=\"#DejaVuSans-75\" x=\"63.476562\"/>\n", "     <use xlink:href=\"#DejaVuSans-6c\" x=\"126.855469\"/>\n", "     <use xlink:href=\"#DejaVuSans-6c\" x=\"154.638672\"/>\n", "     <use xlink:href=\"#DejaVuSans-6f\" x=\"182.421875\"/>\n", "     <use xlink:href=\"#DejaVuSans-76\" x=\"243.603516\"/>\n", "     <use xlink:href=\"#DejaVuSans-65\" x=\"302.783203\"/>\n", "     <use xlink:href=\"#DejaVuSans-72\" x=\"364.306641\"/>\n", "    </g>\n", "    <!-- pullover -->\n", "    <g transform=\"translate(108.336607 29.7555)scale(0.12 -0.12)\">\n", "     <use xlink:href=\"#DejaVuSans-70\"/>\n", "     <use xlink:href=\"#DejaVuSans-75\" x=\"63.476562\"/>\n", "     <use xlink:href=\"#DejaVuSans-6c\" x=\"126.855469\"/>\n", "     <use xlink:href=\"#DejaVuSans-6c\" x=\"154.638672\"/>\n", "     <use xlink:href=\"#DejaVuSans-6f\" x=\"182.421875\"/>\n", "     <use xlink:href=\"#DejaVuSans-76\" x=\"243.603516\"/>\n", "     <use xlink:href=\"#DejaVuSans-65\" x=\"302.783203\"/>\n", "     <use xlink:href=\"#DejaVuSans-72\" x=\"364.306641\"/>\n", "    </g>\n", "   </g>\n", "  </g>\n", "  <g id=\"axes_3\">\n", "   <g id=\"patch_12\">\n", "    <path d=\"M 182.882857 107.498357 \n", "L 254.625714 107.498357 \n", "L 254.625714 35.7555 \n", "L 182.882857 35.7555 \n", "z\n", "\" style=\"fill: #ffffff\"/>\n", "   </g>\n", "   <g clip-path=\"url(#p2088396578)\">\n", "    <image xlink:href=\"data:image/png;base64,\n", "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\" id=\"imagee5173ba1d9\" transform=\"scale(1 -1)translate(0 -72)\" x=\"182.882857\" y=\"-35.498357\" width=\"72\" height=\"72\"/>\n", "   </g>\n", "   <g id=\"patch_13\">\n", "    <path d=\"M 182.882857 107.498357 \n", "L 182.882857 35.7555 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_14\">\n", "    <path d=\"M 254.625714 107.498357 \n", "L 254.625714 35.7555 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_15\">\n", "    <path d=\"M 182.882857 107.498357 \n", "L 254.625714 107.498357 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_16\">\n", "    <path d=\"M 182.882857 35.7555 \n", "L 254.625714 35.7555 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"text_3\">\n", "    <!-- trouser -->\n", "    <g transform=\"translate(197.312723 16.318125)scale(0.12 -0.12)\">\n", "     <defs>\n", "      <path id=\"DejaVuSans-73\" d=\"M 2834 3397 \n", "L 2834 2853 \n", "Q 2591 2978 2328 3040 \n", "Q 2066 3103 1784 3103 \n", "Q 1356 3103 1142 2972 \n", "Q 928 2841 928 2578 \n", "Q 928 2378 1081 2264 \n", "Q 1234 2150 1697 2047 \n", "L 1894 2003 \n", "Q 2506 1872 2764 1633 \n", "Q 3022 1394 3022 966 \n", "Q 3022 478 2636 193 \n", "Q 2250 -91 1575 -91 \n", "Q 1294 -91 989 -36 \n", "Q 684 19 347 128 \n", "L 347 722 \n", "Q 666 556 975 473 \n", "Q 1284 391 1588 391 \n", "Q 1994 391 2212 530 \n", "Q 2431 669 2431 922 \n", "Q 2431 1156 2273 1281 \n", "Q 2116 1406 1581 1522 \n", "L 1381 1569 \n", "Q 847 1681 609 1914 \n", "Q 372 2147 372 2553 \n", "Q 372 3047 722 3315 \n", "Q 1072 3584 1716 3584 \n", "Q 2034 3584 2315 3537 \n", "Q 2597 3491 2834 3397 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "     </defs>\n", "     <use xlink:href=\"#DejaVuSans-74\"/>\n", "     <use xlink:href=\"#DejaVuSans-72\" x=\"39.208984\"/>\n", "     <use xlink:href=\"#DejaVuSans-6f\" x=\"78.072266\"/>\n", "     <use xlink:href=\"#DejaVuSans-75\" x=\"139.253906\"/>\n", "     <use xlink:href=\"#DejaVuSans-73\" x=\"202.632812\"/>\n", "     <use xlink:href=\"#DejaVuSans-65\" x=\"254.732422\"/>\n", "     <use xlink:href=\"#DejaVuSans-72\" x=\"316.255859\"/>\n", "    </g>\n", "    <!-- trouser -->\n", "    <g transform=\"translate(197.312723 29.7555)scale(0.12 -0.12)\">\n", "     <use xlink:href=\"#DejaVuSans-74\"/>\n", "     <use xlink:href=\"#DejaVuSans-72\" x=\"39.208984\"/>\n", "     <use xlink:href=\"#DejaVuSans-6f\" x=\"78.072266\"/>\n", "     <use xlink:href=\"#DejaVuSans-75\" x=\"139.253906\"/>\n", "     <use xlink:href=\"#DejaVuSans-73\" x=\"202.632812\"/>\n", "     <use xlink:href=\"#DejaVuSans-65\" x=\"254.732422\"/>\n", "     <use xlink:href=\"#DejaVuSans-72\" x=\"316.255859\"/>\n", "    </g>\n", "   </g>\n", "  </g>\n", "  <g id=\"axes_4\">\n", "   <g id=\"patch_17\">\n", "    <path d=\"M 268.974286 107.498357 \n", "L 340.717143 107.498357 \n", "L 340.717143 35.7555 \n", "L 268.974286 35.7555 \n", "z\n", "\" style=\"fill: #ffffff\"/>\n", "   </g>\n", "   <g clip-path=\"url(#pa483edf8dc)\">\n", "    <image xlink:href=\"data:image/png;base64,\n", "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\" id=\"imagedcf2c20795\" transform=\"scale(1 -1)translate(0 -72)\" x=\"268.974286\" y=\"-35.498357\" width=\"72\" height=\"72\"/>\n", "   </g>\n", "   <g id=\"patch_18\">\n", "    <path d=\"M 268.974286 107.498357 \n", "L 268.974286 35.7555 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_19\">\n", "    <path d=\"M 340.717143 107.498357 \n", "L 340.717143 35.7555 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_20\">\n", "    <path d=\"M 268.974286 107.498357 \n", "L 340.717143 107.498357 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_21\">\n", "    <path d=\"M 268.974286 35.7555 \n", "L 340.717143 35.7555 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"text_4\">\n", "    <!-- trouser -->\n", "    <g transform=\"translate(283.404152 16.318125)scale(0.12 -0.12)\">\n", "     <use xlink:href=\"#DejaVuSans-74\"/>\n", "     <use xlink:href=\"#DejaVuSans-72\" x=\"39.208984\"/>\n", "     <use xlink:href=\"#DejaVuSans-6f\" x=\"78.072266\"/>\n", "     <use xlink:href=\"#DejaVuSans-75\" x=\"139.253906\"/>\n", "     <use xlink:href=\"#DejaVuSans-73\" x=\"202.632812\"/>\n", "     <use xlink:href=\"#DejaVuSans-65\" x=\"254.732422\"/>\n", "     <use xlink:href=\"#DejaVuSans-72\" x=\"316.255859\"/>\n", "    </g>\n", "    <!-- trouser -->\n", "    <g transform=\"translate(283.404152 29.7555)scale(0.12 -0.12)\">\n", "     <use xlink:href=\"#DejaVuSans-74\"/>\n", "     <use xlink:href=\"#DejaVuSans-72\" x=\"39.208984\"/>\n", "     <use xlink:href=\"#DejaVuSans-6f\" x=\"78.072266\"/>\n", "     <use xlink:href=\"#DejaVuSans-75\" x=\"139.253906\"/>\n", "     <use xlink:href=\"#DejaVuSans-73\" x=\"202.632812\"/>\n", "     <use xlink:href=\"#DejaVuSans-65\" x=\"254.732422\"/>\n", "     <use xlink:href=\"#DejaVuSans-72\" x=\"316.255859\"/>\n", "    </g>\n", "   </g>\n", "  </g>\n", "  <g id=\"axes_5\">\n", "   <g id=\"patch_22\">\n", "    <path d=\"M 355.065714 107.498357 \n", "L 426.808571 107.498357 \n", "L 426.808571 35.7555 \n", "L 355.065714 35.7555 \n", "z\n", "\" style=\"fill: #ffffff\"/>\n", "   </g>\n", "   <g clip-path=\"url(#pd990645b26)\">\n", "    <image xlink:href=\"data:image/png;base64,\n", "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\" id=\"image09f46996b2\" transform=\"scale(1 -1)translate(0 -72)\" x=\"355.065714\" y=\"-35.498357\" width=\"72\" height=\"72\"/>\n", "   </g>\n", "   <g id=\"patch_23\">\n", "    <path d=\"M 355.065714 107.498357 \n", "L 355.065714 35.7555 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_24\">\n", "    <path d=\"M 426.808571 107.498357 \n", "L 426.808571 35.7555 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_25\">\n", "    <path d=\"M 355.065714 107.498357 \n", "L 426.808571 107.498357 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_26\">\n", "    <path d=\"M 355.065714 35.7555 \n", "L 426.808571 35.7555 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"text_5\">\n", "    <!-- shirt -->\n", "    <g transform=\"translate(377.523393 16.318125)scale(0.12 -0.12)\">\n", "     <defs>\n", "      <path id=\"DejaVuSans-68\" d=\"M 3513 2113 \n", "L 3513 0 \n", "L 2938 0 \n", "L 2938 2094 \n", "Q 2938 2591 2744 2837 \n", "Q 2550 3084 2163 3084 \n", "Q 1697 3084 1428 2787 \n", "Q 1159 2491 1159 1978 \n", "L 1159 0 \n", "L 581 0 \n", "L 581 4863 \n", "L 1159 4863 \n", "L 1159 2956 \n", "Q 1366 3272 1645 3428 \n", "Q 1925 3584 2291 3584 \n", "Q 2894 3584 3203 3211 \n", "Q 3513 2838 3513 2113 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "      <path id=\"DejaVuSans-69\" d=\"M 603 3500 \n", "L 1178 3500 \n", "L 1178 0 \n", "L 603 0 \n", "L 603 3500 \n", "z\n", "M 603 4863 \n", "L 1178 4863 \n", "L 1178 4134 \n", "L 603 4134 \n", "L 603 4863 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "     </defs>\n", "     <use xlink:href=\"#DejaVuSans-73\"/>\n", "     <use xlink:href=\"#DejaVuSans-68\" x=\"52.099609\"/>\n", "     <use xlink:href=\"#DejaVuSans-69\" x=\"115.478516\"/>\n", "     <use xlink:href=\"#DejaVuSans-72\" x=\"143.261719\"/>\n", "     <use xlink:href=\"#DejaVuSans-74\" x=\"184.375\"/>\n", "    </g>\n", "    <!-- shirt -->\n", "    <g transform=\"translate(377.523393 29.7555)scale(0.12 -0.12)\">\n", "     <use xlink:href=\"#DejaVuSans-73\"/>\n", "     <use xlink:href=\"#DejaVuSans-68\" x=\"52.099609\"/>\n", "     <use xlink:href=\"#DejaVuSans-69\" x=\"115.478516\"/>\n", "     <use xlink:href=\"#DejaVuSans-72\" x=\"143.261719\"/>\n", "     <use xlink:href=\"#DejaVuSans-74\" x=\"184.375\"/>\n", "    </g>\n", "   </g>\n", "  </g>\n", "  <g id=\"axes_6\">\n", "   <g id=\"patch_27\">\n", "    <path d=\"M 441.157143 107.498357 \n", "L 512.9 107.498357 \n", "L 512.9 35.7555 \n", "L 441.157143 35.7555 \n", "z\n", "\" style=\"fill: #ffffff\"/>\n", "   </g>\n", "   <g clip-path=\"url(#p4f8f67fab7)\">\n", "    <image xlink:href=\"data:image/png;base64,\n", "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\" id=\"image83389eb0d7\" transform=\"scale(1 -1)translate(0 -72)\" x=\"441.157143\" y=\"-35.498357\" width=\"72\" height=\"72\"/>\n", "   </g>\n", "   <g id=\"patch_28\">\n", "    <path d=\"M 441.157143 107.498357 \n", "L 441.157143 35.7555 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_29\">\n", "    <path d=\"M 512.9 107.498357 \n", "L 512.9 35.7555 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_30\">\n", "    <path d=\"M 441.157143 107.498357 \n", "L 512.9 107.498357 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_31\">\n", "    <path d=\"M 441.157143 35.7555 \n", "L 512.9 35.7555 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"text_6\">\n", "    <!-- trouser -->\n", "    <g transform=\"translate(455.587009 16.318125)scale(0.12 -0.12)\">\n", "     <use xlink:href=\"#DejaVuSans-74\"/>\n", "     <use xlink:href=\"#DejaVuSans-72\" x=\"39.208984\"/>\n", "     <use xlink:href=\"#DejaVuSans-6f\" x=\"78.072266\"/>\n", "     <use xlink:href=\"#DejaVuSans-75\" x=\"139.253906\"/>\n", "     <use xlink:href=\"#DejaVuSans-73\" x=\"202.632812\"/>\n", "     <use xlink:href=\"#DejaVuSans-65\" x=\"254.732422\"/>\n", "     <use xlink:href=\"#DejaVuSans-72\" x=\"316.255859\"/>\n", "    </g>\n", "    <!-- trouser -->\n", "    <g transform=\"translate(455.587009 29.7555)scale(0.12 -0.12)\">\n", "     <use xlink:href=\"#DejaVuSans-74\"/>\n", "     <use xlink:href=\"#DejaVuSans-72\" x=\"39.208984\"/>\n", "     <use xlink:href=\"#DejaVuSans-6f\" x=\"78.072266\"/>\n", "     <use xlink:href=\"#DejaVuSans-75\" x=\"139.253906\"/>\n", "     <use xlink:href=\"#DejaVuSans-73\" x=\"202.632812\"/>\n", "     <use xlink:href=\"#DejaVuSans-65\" x=\"254.732422\"/>\n", "     <use xlink:href=\"#DejaVuSans-72\" x=\"316.255859\"/>\n", "    </g>\n", "   </g>\n", "  </g>\n", " </g>\n", " <defs>\n", "  <clipPath id=\"pb75e02419d\">\n", "   <rect x=\"10.7\" y=\"35.7555\" width=\"71.742857\" height=\"71.742857\"/>\n", "  </clipPath>\n", "  <clipPath id=\"p2057e4ef54\">\n", "   <rect x=\"96.791429\" y=\"35.7555\" width=\"71.742857\" height=\"71.742857\"/>\n", "  </clipPath>\n", "  <clipPath id=\"p2088396578\">\n", "   <rect x=\"182.882857\" y=\"35.7555\" width=\"71.742857\" height=\"71.742857\"/>\n", "  </clipPath>\n", "  <clipPath id=\"pa483edf8dc\">\n", "   <rect x=\"268.974286\" y=\"35.7555\" width=\"71.742857\" height=\"71.742857\"/>\n", "  </clipPath>\n", "  <clipPath id=\"pd990645b26\">\n", "   <rect x=\"355.065714\" y=\"35.7555\" width=\"71.742857\" height=\"71.742857\"/>\n", "  </clipPath>\n", "  <clipPath id=\"p4f8f67fab7\">\n", "   <rect x=\"441.157143\" y=\"35.7555\" width=\"71.742857\" height=\"71.742857\"/>\n", "  </clipPath>\n", " </defs>\n", "</svg>\n"], "text/plain": ["<Figure size 900x150 with 6 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["d2l.predict_ch3(net, test_iter)"]}, {"cell_type": "markdown", "id": "055fdd0b", "metadata": {"origin_pos": 32}, "source": ["## 小结\n", "\n", "* 手动实现一个简单的多层感知机是很容易的。然而如果有大量的层，从零开始实现多层感知机会变得很麻烦（例如，要命名和记录模型的参数）。\n", "\n", "## 练习\n", "\n", "1. 在所有其他参数保持不变的情况下，更改超参数`num_hiddens`的值，并查看此超参数的变化对结果有何影响。确定此超参数的最佳值。\n", "1. 尝试添加更多的隐藏层，并查看它对结果有何影响。\n", "1. 改变学习速率会如何影响结果？保持模型架构和其他超参数（包括轮数）不变，学习率设置为多少会带来最好的结果？\n", "1. 通过对所有超参数（学习率、轮数、隐藏层数、每层的隐藏单元数）进行联合优化，可以得到的最佳结果是什么？\n", "1. 描述为什么涉及多个超参数更具挑战性。\n", "1. 如果想要构建多个超参数的搜索方法，请想出一个聪明的策略。\n"]}, {"cell_type": "markdown", "id": "0bb64861", "metadata": {"origin_pos": 34, "tab": ["pytorch"]}, "source": ["[Discussions](https://discuss.d2l.ai/t/1804)\n"]}], "metadata": {"kernelspec": {"display_name": "d2l", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.23"}}, "nbformat": 4, "nbformat_minor": 5}