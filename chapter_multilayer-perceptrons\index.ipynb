{"cells": [{"cell_type": "markdown", "id": "1ec21230", "metadata": {"origin_pos": 0}, "source": ["# 多层感知机\n", ":label:`chap_perceptrons`\n", "\n", "在本章中，我们将第一次介绍真正的*深度*网络。\n", "最简单的深度网络称为*多层感知机*。多层感知机由多层神经元组成，\n", "每一层与它的上一层相连，从中接收输入；\n", "同时每一层也与它的下一层相连，影响当前层的神经元。\n", "当我们训练容量较大的模型时，我们面临着*过拟合*的风险。\n", "因此，本章将从基本的概念介绍开始讲起，包括*过拟合*、*欠拟合*和模型选择。\n", "为了解决这些问题，本章将介绍*权重衰减*和*暂退法*等正则化技术。\n", "我们还将讨论数值稳定性和参数初始化相关的问题，\n", "这些问题是成功训练深度网络的关键。\n", "在本章的最后，我们将把所介绍的内容应用到一个真实的案例：房价预测。\n", "关于模型计算性能、可伸缩性和效率相关的问题，我们将放在后面的章节中讨论。\n", "\n", ":begin_tab:toc\n", " - [mlp](mlp.ipynb)\n", " - [mlp-scratch](mlp-scratch.ipynb)\n", " - [mlp-concise](mlp-concise.ipynb)\n", " - [underfit-overfit](underfit-overfit.ipynb)\n", " - [weight-decay](weight-decay.ipynb)\n", " - [dropout](dropout.ipynb)\n", " - [backprop](backprop.ipynb)\n", " - [numerical-stability-and-init](numerical-stability-and-init.ipynb)\n", " - [environment](environment.ipynb)\n", " - [kaggle-house-price](kaggle-house-price.ipynb)\n", ":end_tab:\n"]}], "metadata": {"language_info": {"name": "python"}}, "nbformat": 4, "nbformat_minor": 5}