<?xml version="1.0" encoding="UTF-8"?>
<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="367pt" height="153pt" viewBox="0 0 367 153" version="1.1">
<defs>
<g>
<symbol overflow="visible" id="glyph0-0">
<path style="stroke:none;" d=""/>
</symbol>
<symbol overflow="visible" id="glyph0-1">
<path style="stroke:none;" d="M 6.203125 0 L 6.203125 -0.21875 C 5.828125 -0.21875 5.671875 -0.421875 5.21875 -1.515625 L 3.21875 -6.203125 L 2.96875 -6.203125 L 0.96875 -1.3125 C 0.625 -0.453125 0.5 -0.296875 0.078125 -0.21875 L 0.078125 0 L 1.90625 0 L 1.90625 -0.21875 C 1.375 -0.265625 1.171875 -0.359375 1.171875 -0.59375 C 1.171875 -0.8125 1.34375 -1.1875 1.453125 -1.46875 L 1.5625 -1.765625 L 3.59375 -1.765625 C 3.890625 -1.0625 4 -0.75 4 -0.546875 C 4 -0.34375 3.890625 -0.28125 3.59375 -0.25 L 3.296875 -0.21875 L 3.296875 0 Z M 3.453125 -2.125 L 1.703125 -2.125 L 2.546875 -4.3125 Z M 3.453125 -2.125 "/>
</symbol>
<symbol overflow="visible" id="glyph1-0">
<path style="stroke:none;" d=""/>
</symbol>
<symbol overflow="visible" id="glyph1-1">
<path style="stroke:none;" d="M 2.734375 1.453125 C 1.46875 0.390625 1.203125 -0.625 1.203125 -2.296875 C 1.203125 -4.03125 1.484375 -4.875 2.734375 -5.9375 L 2.65625 -6.078125 C 1.21875 -5.234375 0.4375 -3.9375 0.4375 -2.265625 C 0.4375 -0.734375 1.203125 0.78125 2.625 1.59375 Z M 2.734375 1.453125 "/>
</symbol>
<symbol overflow="visible" id="glyph1-2">
<path style="stroke:none;" d="M 4.28125 -2.96875 C 4.28125 -4.8125 3.46875 -6.078125 2.28125 -6.078125 C 0.84375 -6.078125 0.21875 -4.609375 0.21875 -3.03125 C 0.21875 -1.546875 0.71875 0.125 2.25 0.125 C 3.71875 0.125 4.28125 -1.421875 4.28125 -2.96875 Z M 3.421875 -2.921875 C 3.421875 -1.140625 3.015625 -0.109375 2.25 -0.109375 C 1.46875 -0.109375 1.078125 -1.140625 1.078125 -2.96875 C 1.078125 -4.78125 1.484375 -5.84375 2.234375 -5.84375 C 3.03125 -5.84375 3.421875 -4.796875 3.421875 -2.921875 Z M 3.421875 -2.921875 "/>
</symbol>
<symbol overflow="visible" id="glyph1-3">
<path style="stroke:none;" d="M 0.75 1.265625 C 1.375 0.96875 1.75 0.40625 1.75 -0.140625 C 1.75 -0.609375 1.4375 -0.921875 1.03125 -0.921875 C 0.71875 -0.921875 0.5 -0.71875 0.5 -0.40625 C 0.5 -0.09375 0.6875 0.046875 1.015625 0.046875 C 1.109375 0.046875 1.203125 0.015625 1.28125 0.015625 C 1.34375 0.015625 1.40625 0.078125 1.40625 0.140625 C 1.40625 0.4375 1.15625 0.765625 0.65625 1.09375 Z M 0.75 1.265625 "/>
</symbol>
<symbol overflow="visible" id="glyph1-4">
<path style="stroke:none;" d=""/>
</symbol>
<symbol overflow="visible" id="glyph1-5">
<path style="stroke:none;" d="M 3.546875 0 L 3.546875 -0.140625 C 2.875 -0.140625 2.6875 -0.296875 2.6875 -0.6875 L 2.6875 -6.0625 L 2.609375 -6.078125 L 1 -5.265625 L 1 -5.140625 L 1.234375 -5.234375 C 1.40625 -5.296875 1.5625 -5.34375 1.640625 -5.34375 C 1.84375 -5.34375 1.921875 -5.203125 1.921875 -4.890625 L 1.921875 -0.859375 C 1.921875 -0.359375 1.734375 -0.171875 1.0625 -0.140625 L 1.0625 0 Z M 3.546875 0 "/>
</symbol>
<symbol overflow="visible" id="glyph1-6">
<path style="stroke:none;" d="M 0.265625 -5.9375 C 1.5625 -4.90625 1.796875 -3.875 1.796875 -2.203125 C 1.796875 -0.453125 1.53125 0.390625 0.265625 1.453125 L 0.34375 1.59375 C 1.765625 0.71875 2.5625 -0.5625 2.5625 -2.21875 C 2.5625 -3.75 1.75 -5.25 0.375 -6.078125 Z M 0.265625 -5.9375 "/>
</symbol>
<symbol overflow="visible" id="glyph1-7">
<path style="stroke:none;" d="M 2.5625 -1.75 L 2.5625 -2.3125 L 0.34375 -2.3125 L 0.34375 -1.75 Z M 2.5625 -1.75 "/>
</symbol>
<symbol overflow="visible" id="glyph1-8">
<path style="stroke:none;" d="M 4.265625 -1.234375 L 4.140625 -1.28125 C 3.84375 -0.78125 3.65625 -0.6875 3.28125 -0.6875 L 1.171875 -0.6875 L 2.65625 -2.265625 C 3.453125 -3.109375 3.8125 -3.78125 3.8125 -4.5 C 3.8125 -5.390625 3.15625 -6.078125 2.140625 -6.078125 C 1.03125 -6.078125 0.453125 -5.34375 0.265625 -4.296875 L 0.453125 -4.25 C 0.8125 -5.125 1.140625 -5.421875 1.78125 -5.421875 C 2.546875 -5.421875 3.03125 -4.96875 3.03125 -4.15625 C 3.03125 -3.390625 2.703125 -2.703125 1.859375 -1.8125 L 0.265625 -0.109375 L 0.265625 0 L 3.78125 0 Z M 4.265625 -1.234375 "/>
</symbol>
<symbol overflow="visible" id="glyph1-9">
<path style="stroke:none;" d="M 0.546875 -4.59375 C 0.921875 -5.25 1.328125 -5.546875 1.890625 -5.546875 C 2.484375 -5.546875 2.859375 -5.234375 2.859375 -4.625 C 2.859375 -4.078125 2.578125 -3.671875 2.140625 -3.421875 C 1.953125 -3.3125 1.71875 -3.21875 1.375 -3.09375 L 1.375 -2.96875 C 1.890625 -2.96875 2.09375 -2.9375 2.296875 -2.875 C 2.921875 -2.703125 3.234375 -2.265625 3.234375 -1.578125 C 3.234375 -0.8125 2.734375 -0.203125 2.0625 -0.203125 C 1.8125 -0.203125 1.625 -0.25 1.28125 -0.484375 C 1.03125 -0.65625 0.890625 -0.71875 0.734375 -0.71875 C 0.53125 -0.71875 0.375 -0.578125 0.375 -0.390625 C 0.375 -0.0625 0.71875 0.125 1.375 0.125 C 2.171875 0.125 3.03125 -0.140625 3.46875 -0.71875 C 3.71875 -1.046875 3.875 -1.5 3.875 -1.96875 C 3.875 -2.4375 3.734375 -2.859375 3.484375 -3.125 C 3.296875 -3.328125 3.125 -3.4375 2.734375 -3.609375 C 3.34375 -3.96875 3.578125 -4.421875 3.578125 -4.84375 C 3.578125 -5.59375 3 -6.078125 2.171875 -6.078125 C 1.234375 -6.078125 0.671875 -5.484375 0.40625 -4.625 Z M 0.546875 -4.59375 "/>
</symbol>
</g>
</defs>
<g id="surface1">
<path style="fill:none;stroke-width:1;stroke-linecap:butt;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-dasharray:1,4;stroke-miterlimit:10;" d="M 0 4 L 144 4 " transform="matrix(1,0,0,1,1,75)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:butt;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-dasharray:1,4;stroke-miterlimit:10;" d="M 71 -67 L 71 77 " transform="matrix(1,0,0,1,1,75)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:butt;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-dasharray:1,4;stroke-miterlimit:10;" d="M 95 -67 L 95 77 " transform="matrix(1,0,0,1,1,75)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:butt;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-dasharray:1,4;stroke-miterlimit:10;" d="M 119 -67 L 119 77 " transform="matrix(1,0,0,1,1,75)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:butt;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-dasharray:1,4;stroke-miterlimit:10;" d="M 47 -67 L 47 77 " transform="matrix(1,0,0,1,1,75)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:butt;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-dasharray:1,4;stroke-miterlimit:10;" d="M 23 -67 L 23 77 " transform="matrix(1,0,0,1,1,75)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:butt;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-dasharray:1,4;stroke-miterlimit:10;" d="M 0 -20 L 144 -20 " transform="matrix(1,0,0,1,1,75)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:butt;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-dasharray:1,4;stroke-miterlimit:10;" d="M 0 -44 L 144 -44 " transform="matrix(1,0,0,1,1,75)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:butt;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-dasharray:1,4;stroke-miterlimit:10;" d="M 0 28 L 144 28 " transform="matrix(1,0,0,1,1,75)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:butt;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-dasharray:1,4;stroke-miterlimit:10;" d="M 0 52 L 144 52 " transform="matrix(1,0,0,1,1,75)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:butt;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 71 -14.101562 L 71 2.5 " transform="matrix(1,0,0,1,1,75)"/>
<path style="fill-rule:nonzero;fill:rgb(0%,0%,0%);fill-opacity:1;stroke-width:1;stroke-linecap:butt;stroke-linejoin:miter;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 69.9375 2.9375 C 70.523438 2.355469 71.476562 2.355469 72.0625 2.9375 C 72.644531 3.523438 72.644531 4.476562 72.0625 5.0625 C 71.476562 5.644531 70.523438 5.644531 69.9375 5.0625 C 69.355469 4.476562 69.355469 3.523438 69.9375 2.9375 " transform="matrix(1,0,0,1,1,75)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:butt;stroke-linejoin:miter;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 71 -18.101562 L 71 -14.101562 M 69.5 -14.101562 L 71 -18.101562 L 72.5 -14.101562 " transform="matrix(1,0,0,1,1,75)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:butt;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 89.101562 4 L 72.5 4 " transform="matrix(1,0,0,1,1,75)"/>
<path style="fill-rule:nonzero;fill:rgb(0%,0%,0%);fill-opacity:1;stroke-width:1;stroke-linecap:butt;stroke-linejoin:miter;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 72.0625 2.9375 C 72.644531 3.523438 72.644531 4.476562 72.0625 5.0625 C 71.476562 5.644531 70.523438 5.644531 69.9375 5.0625 C 69.355469 4.476562 69.355469 3.523438 69.9375 2.9375 C 70.523438 2.355469 71.476562 2.355469 72.0625 2.9375 " transform="matrix(1,0,0,1,1,75)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:butt;stroke-linejoin:miter;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 93.101562 4 L 89.101562 4 M 89.101562 2.5 L 93.101562 4 L 89.101562 5.5 " transform="matrix(1,0,0,1,1,75)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:butt;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 321.726562 -63.089844 L 277.832031 2.75 " transform="matrix(1,0,0,1,1,75)"/>
<path style="fill-rule:nonzero;fill:rgb(0%,0%,0%);fill-opacity:1;stroke-width:1;stroke-linecap:butt;stroke-linejoin:miter;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 276.707031 2.527344 C 277.519531 2.367188 278.308594 2.894531 278.472656 3.707031 C 278.632812 4.519531 278.105469 5.308594 277.292969 5.472656 C 276.480469 5.632812 275.691406 5.105469 275.527344 4.292969 C 275.367188 3.480469 275.894531 2.691406 276.707031 2.527344 " transform="matrix(1,0,0,1,1,75)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:butt;stroke-linejoin:miter;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 323.945312 -66.417969 L 321.726562 -63.089844 M 320.480469 -63.921875 L 323.945312 -66.417969 L 322.976562 -62.257812 " transform="matrix(1,0,0,1,1,75)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:butt;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 296.828125 23.828125 L 278.0625 5.0625 " transform="matrix(1,0,0,1,1,75)"/>
<path style="fill-rule:nonzero;fill:rgb(0%,0%,0%);fill-opacity:1;stroke-width:1;stroke-linecap:butt;stroke-linejoin:miter;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 278.5 4 C 278.5 4.828125 277.828125 5.5 277 5.5 C 276.171875 5.5 275.5 4.828125 275.5 4 C 275.5 3.171875 276.171875 2.5 277 2.5 C 277.828125 2.5 278.5 3.171875 278.5 4 " transform="matrix(1,0,0,1,1,75)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:butt;stroke-linejoin:miter;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 299.65625 26.65625 L 296.828125 23.828125 M 297.890625 22.765625 L 299.65625 26.65625 L 295.765625 24.890625 " transform="matrix(1,0,0,1,1,75)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:butt;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-dasharray:1,4;stroke-miterlimit:10;" d="M 325 -68 L 229 76 " transform="matrix(1,0,0,1,1,75)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:butt;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-dasharray:1,4;stroke-miterlimit:10;" d="M 349 76 L 205 -68 " transform="matrix(1,0,0,1,1,75)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:butt;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-dasharray:1,4;stroke-miterlimit:10;" d="M 229.5 76 L 200.300781 46.800781 " transform="matrix(1,0,0,1,1,75)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:butt;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-dasharray:1,4;stroke-miterlimit:10;" d="M 245 -68 L 200.121094 -0.679688 " transform="matrix(1,0,0,1,1,75)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:butt;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-dasharray:1,4;stroke-miterlimit:10;" d="M 285 -68 L 200.042969 59.4375 " transform="matrix(1,0,0,1,1,75)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:butt;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-dasharray:1,4;stroke-miterlimit:10;" d="M 352.089844 -48.632812 L 269 76 " transform="matrix(1,0,0,1,1,75)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:butt;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-dasharray:1,4;stroke-miterlimit:10;" d="M 354.242188 -39 L 325 -68 " transform="matrix(1,0,0,1,1,75)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:butt;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-dasharray:1,4;stroke-miterlimit:10;" d="M 355 7 L 309 76 " transform="matrix(1,0,0,1,1,75)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:butt;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 181.101562 4 L 163 4 " transform="matrix(1,0,0,1,1,75)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:butt;stroke-linejoin:miter;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 185.101562 4 L 181.101562 4 M 181.101562 2.5 L 185.101562 4 L 181.101562 5.5 " transform="matrix(1,0,0,1,1,75)"/>
<g style="fill:rgb(0%,0%,0%);fill-opacity:1;">
  <use xlink:href="#glyph0-1" x="172.251" y="74.126"/>
</g>
<g style="fill:rgb(0%,0%,0%);fill-opacity:1;">
  <use xlink:href="#glyph1-1" x="73.631" y="49.5"/>
  <use xlink:href="#glyph1-2" x="76.628" y="49.5"/>
  <use xlink:href="#glyph1-3" x="81.128" y="49.5"/>
  <use xlink:href="#glyph1-4" x="83.378" y="49.5"/>
  <use xlink:href="#glyph1-5" x="85.628" y="49.5"/>
  <use xlink:href="#glyph1-6" x="90.128" y="49.5"/>
</g>
<g style="fill:rgb(0%,0%,0%);fill-opacity:1;">
  <use xlink:href="#glyph1-1" x="97.753" y="76"/>
  <use xlink:href="#glyph1-5" x="100.75" y="76"/>
  <use xlink:href="#glyph1-3" x="105.25" y="76"/>
  <use xlink:href="#glyph1-4" x="107.5" y="76"/>
  <use xlink:href="#glyph1-2" x="109.75" y="76"/>
  <use xlink:href="#glyph1-6" x="114.25" y="76"/>
</g>
<g style="fill:rgb(0%,0%,0%);fill-opacity:1;">
  <use xlink:href="#glyph1-1" x="304.3765" y="106"/>
  <use xlink:href="#glyph1-5" x="307.3735" y="106"/>
  <use xlink:href="#glyph1-3" x="311.8735" y="106"/>
  <use xlink:href="#glyph1-4" x="314.1235" y="106"/>
  <use xlink:href="#glyph1-7" x="316.3735" y="106"/>
  <use xlink:href="#glyph1-5" x="319.3705" y="106"/>
  <use xlink:href="#glyph1-6" x="323.8705" y="106"/>
</g>
<g style="fill:rgb(0%,0%,0%);fill-opacity:1;">
  <use xlink:href="#glyph1-1" x="299.875" y="14"/>
  <use xlink:href="#glyph1-8" x="302.872" y="14"/>
  <use xlink:href="#glyph1-3" x="307.372" y="14"/>
  <use xlink:href="#glyph1-4" x="309.622" y="14"/>
  <use xlink:href="#glyph1-9" x="311.872" y="14"/>
  <use xlink:href="#glyph1-6" x="316.372" y="14"/>
</g>
</g>
</svg>
