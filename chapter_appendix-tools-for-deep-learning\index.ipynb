{"cells": [{"cell_type": "markdown", "id": "898fa7c6", "metadata": {"origin_pos": 0}, "source": ["# 附录：深度学习工具\n", ":label:`chap_appendix_tools`\n", "\n", "为了充分利用《动手学深度学习》，本书将在本附录中介绍不同工具，\n", "例如如何运行这本交互式开源书籍和为本书做贡献。\n", "\n", ":begin_tab:toc\n", " - [jupyter](jupyter.ipynb)\n", " - [sagemaker](sagemaker.ipynb)\n", " - [aws](aws.ipynb)\n", " - [selecting-servers-gpus](selecting-servers-gpus.ipynb)\n", " - [contributing](contributing.ipynb)\n", " - [d2l](d2l.ipynb)\n", ":end_tab:\n"]}], "metadata": {"language_info": {"name": "python"}}, "nbformat": 4, "nbformat_minor": 5}