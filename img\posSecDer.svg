<?xml version="1.0" encoding="UTF-8"?>
<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="403pt" height="123pt" viewBox="0 0 403 123" version="1.1">
<defs>
<g>
<symbol overflow="visible" id="glyph0-0">
<path style="stroke:none;" d=""/>
</symbol>
<symbol overflow="visible" id="glyph0-1">
<path style="stroke:none;" d="M 0.375 -3.859375 L 0.3125 -3.5625 L 1.125 -3.5625 L 0.328125 0.21875 C 0.125 1.1875 -0.15625 1.671875 -0.546875 1.671875 C -0.640625 1.671875 -0.71875 1.609375 -0.71875 1.53125 C -0.71875 1.4375 -0.640625 1.390625 -0.640625 1.265625 C -0.640625 1.078125 -0.78125 0.9375 -0.984375 0.9375 C -1.171875 0.9375 -1.328125 1.109375 -1.328125 1.3125 C -1.328125 1.625 -1.015625 1.859375 -0.609375 1.859375 C 0.203125 1.859375 0.8125 0.984375 1.1875 -0.6875 L 1.84375 -3.5625 L 2.8125 -3.5625 L 2.859375 -3.859375 L 1.90625 -3.859375 C 2.15625 -5.28125 2.5 -5.90625 3 -5.90625 C 3.125 -5.90625 3.1875 -5.859375 3.1875 -5.78125 C 3.1875 -5.703125 3.109375 -5.65625 3.109375 -5.515625 C 3.109375 -5.296875 3.28125 -5.15625 3.4375 -5.15625 C 3.640625 -5.15625 3.8125 -5.34375 3.8125 -5.546875 C 3.8125 -5.875 3.484375 -6.109375 3.046875 -6.109375 C 2.5 -6.109375 2.125 -5.796875 1.859375 -5.40625 C 1.5625 -4.984375 1.40625 -4.484375 1.203125 -3.859375 Z M 0.375 -3.859375 "/>
</symbol>
<symbol overflow="visible" id="glyph0-2">
<path style="stroke:none;" d=""/>
</symbol>
<symbol overflow="visible" id="glyph0-3">
<path style="stroke:none;" d="M 2.1875 -3.1875 C 2.0625 -3.8125 1.953125 -3.96875 1.734375 -3.96875 C 1.515625 -3.96875 1.21875 -3.90625 0.671875 -3.703125 L 0.578125 -3.671875 L 0.609375 -3.53125 L 0.765625 -3.578125 C 0.9375 -3.625 1.046875 -3.640625 1.109375 -3.640625 C 1.34375 -3.640625 1.40625 -3.5625 1.53125 -3.03125 L 1.78125 -1.90625 L 1.046875 -0.859375 C 0.859375 -0.59375 0.6875 -0.421875 0.578125 -0.421875 C 0.53125 -0.421875 0.4375 -0.453125 0.34375 -0.5 C 0.234375 -0.5625 0.140625 -0.609375 0.0625 -0.609375 C -0.109375 -0.609375 -0.25 -0.453125 -0.25 -0.28125 C -0.25 -0.046875 -0.078125 0.09375 0.203125 0.09375 C 0.484375 0.09375 0.671875 0.015625 1.0625 -0.515625 L 1.859375 -1.578125 L 2.109375 -0.515625 C 2.21875 -0.0625 2.359375 0.09375 2.640625 0.09375 C 2.984375 0.09375 3.21875 -0.109375 3.75 -0.921875 L 3.609375 -1.015625 C 3.53125 -0.921875 3.5 -0.859375 3.421875 -0.75 C 3.21875 -0.484375 3.109375 -0.390625 3 -0.390625 C 2.875 -0.390625 2.796875 -0.515625 2.734375 -0.765625 L 2.4375 -1.96875 C 2.390625 -2.1875 2.359375 -2.3125 2.359375 -2.375 C 2.765625 -3.0625 3.09375 -3.46875 3.25 -3.46875 C 3.46875 -3.46875 3.546875 -3.3125 3.71875 -3.3125 C 3.890625 -3.3125 4.015625 -3.453125 4.015625 -3.640625 C 4.015625 -3.828125 3.875 -3.96875 3.65625 -3.96875 C 3.265625 -3.96875 2.921875 -3.640625 2.296875 -2.6875 Z M 2.1875 -3.1875 "/>
</symbol>
<symbol overflow="visible" id="glyph1-0">
<path style="stroke:none;" d=""/>
</symbol>
<symbol overflow="visible" id="glyph1-1">
<path style="stroke:none;" d="M 0.8125 -3.890625 L 0.96875 -3.78125 L 2.140625 -5.5 C 2.234375 -5.625 2.3125 -5.734375 2.3125 -5.875 C 2.3125 -6.046875 2.21875 -6.15625 2.046875 -6.15625 C 1.859375 -6.15625 1.765625 -6.0625 1.640625 -5.796875 Z M 0.8125 -3.890625 "/>
</symbol>
<symbol overflow="visible" id="glyph2-0">
<path style="stroke:none;" d=""/>
</symbol>
<symbol overflow="visible" id="glyph2-1">
<path style="stroke:none;" d="M 2.734375 1.453125 C 1.46875 0.390625 1.203125 -0.625 1.203125 -2.296875 C 1.203125 -4.03125 1.484375 -4.875 2.734375 -5.9375 L 2.65625 -6.078125 C 1.21875 -5.234375 0.4375 -3.9375 0.4375 -2.265625 C 0.4375 -0.734375 1.203125 0.78125 2.625 1.59375 Z M 2.734375 1.453125 "/>
</symbol>
<symbol overflow="visible" id="glyph2-2">
<path style="stroke:none;" d="M 0.265625 -5.9375 C 1.5625 -4.90625 1.796875 -3.875 1.796875 -2.203125 C 1.796875 -0.453125 1.53125 0.390625 0.265625 1.453125 L 0.34375 1.59375 C 1.765625 0.71875 2.5625 -0.5625 2.5625 -2.21875 C 2.5625 -3.75 1.75 -5.25 0.375 -6.078125 Z M 0.265625 -5.9375 "/>
</symbol>
</g>
</defs>
<g id="surface1">
<path style=" stroke:none;fill-rule:nonzero;fill:rgb(69.804382%,85.098267%,100%);fill-opacity:1;" d="M 151 1 L 252 1 L 252 102 L 151 102 Z M 151 1 "/>
<path style=" stroke:none;fill-rule:nonzero;fill:rgb(69.804382%,85.098267%,100%);fill-opacity:1;" d="M 301 1 L 402 1 L 402 102 L 301 102 Z M 301 1 "/>
<path style=" stroke:none;fill-rule:nonzero;fill:rgb(69.804382%,85.098267%,100%);fill-opacity:1;" d="M 1 1 L 102 1 L 102 102 L 1 102 Z M 1 1 "/>
<path style="fill:none;stroke-width:1;stroke-linecap:butt;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 5.898438 50 L 95.101562 50 " transform="matrix(1,0,0,1,1,1)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:butt;stroke-linejoin:miter;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 99.101562 50 L 95.101562 50 M 95.101562 48.5 L 99.101562 50 L 95.101562 51.5 " transform="matrix(1,0,0,1,1,1)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:butt;stroke-linejoin:miter;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 1.898438 50 L 5.898438 50 M 5.898438 51.5 L 1.898438 50 L 5.898438 48.5 " transform="matrix(1,0,0,1,1,1)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:butt;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 50 5.898438 L 50 95.101562 " transform="matrix(1,0,0,1,1,1)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:butt;stroke-linejoin:miter;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 50 99.101562 L 50 95.101562 M 51.5 95.101562 L 50 99.101562 L 48.5 95.101562 " transform="matrix(1,0,0,1,1,1)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:butt;stroke-linejoin:miter;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 50 1.898438 L 50 5.898438 M 48.5 5.898438 L 50 1.898438 L 51.5 5.898438 " transform="matrix(1,0,0,1,1,1)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:butt;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 155.898438 50 L 245.101562 50 " transform="matrix(1,0,0,1,1,1)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:butt;stroke-linejoin:miter;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 249.101562 50 L 245.101562 50 M 245.101562 48.5 L 249.101562 50 L 245.101562 51.5 " transform="matrix(1,0,0,1,1,1)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:butt;stroke-linejoin:miter;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 151.898438 50 L 155.898438 50 M 155.898438 51.5 L 151.898438 50 L 155.898438 48.5 " transform="matrix(1,0,0,1,1,1)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:butt;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 200 5.898438 L 200 95.101562 " transform="matrix(1,0,0,1,1,1)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:butt;stroke-linejoin:miter;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 200 99.101562 L 200 95.101562 M 201.5 95.101562 L 200 99.101562 L 198.5 95.101562 " transform="matrix(1,0,0,1,1,1)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:butt;stroke-linejoin:miter;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 200 1.898438 L 200 5.898438 M 198.5 5.898438 L 200 1.898438 L 201.5 5.898438 " transform="matrix(1,0,0,1,1,1)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:butt;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 305.898438 50 L 395.101562 50 " transform="matrix(1,0,0,1,1,1)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:butt;stroke-linejoin:miter;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 399.101562 50 L 395.101562 50 M 395.101562 48.5 L 399.101562 50 L 395.101562 51.5 " transform="matrix(1,0,0,1,1,1)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:butt;stroke-linejoin:miter;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 301.898438 50 L 305.898438 50 M 305.898438 51.5 L 301.898438 50 L 305.898438 48.5 " transform="matrix(1,0,0,1,1,1)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:butt;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 350 5.898438 L 350 95.101562 " transform="matrix(1,0,0,1,1,1)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:butt;stroke-linejoin:miter;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 350 99.101562 L 350 95.101562 M 351.5 95.101562 L 350 99.101562 L 348.5 95.101562 " transform="matrix(1,0,0,1,1,1)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:butt;stroke-linejoin:miter;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 350 1.898438 L 350 5.898438 M 348.5 5.898438 L 350 1.898438 L 351.5 5.898438 " transform="matrix(1,0,0,1,1,1)"/>
<g style="fill:rgb(0%,0%,0%);fill-opacity:1;">
  <use xlink:href="#glyph0-1" x="42.2995" y="114"/>
  <use xlink:href="#glyph0-2" x="44.8015" y="114"/>
</g>
<g style="fill:rgb(0%,0%,0%);fill-opacity:1;">
  <use xlink:href="#glyph1-1" x="47.0515" y="114"/>
  <use xlink:href="#glyph1-1" x="48.9235" y="114"/>
</g>
<g style="fill:rgb(0%,0%,0%);fill-opacity:1;">
  <use xlink:href="#glyph2-1" x="50.7955" y="114"/>
</g>
<g style="fill:rgb(0%,0%,0%);fill-opacity:1;">
  <use xlink:href="#glyph0-3" x="53.7925" y="114"/>
</g>
<g style="fill:rgb(0%,0%,0%);fill-opacity:1;">
  <use xlink:href="#glyph2-2" x="57.7885" y="114"/>
</g>
<g style="fill:rgb(0%,0%,0%);fill-opacity:1;">
  <use xlink:href="#glyph0-1" x="194.2355" y="114"/>
  <use xlink:href="#glyph0-2" x="196.7375" y="114"/>
</g>
<g style="fill:rgb(0%,0%,0%);fill-opacity:1;">
  <use xlink:href="#glyph1-1" x="198.9875" y="114"/>
</g>
<g style="fill:rgb(0%,0%,0%);fill-opacity:1;">
  <use xlink:href="#glyph2-1" x="200.8595" y="114"/>
</g>
<g style="fill:rgb(0%,0%,0%);fill-opacity:1;">
  <use xlink:href="#glyph0-3" x="203.8565" y="114"/>
</g>
<g style="fill:rgb(0%,0%,0%);fill-opacity:1;">
  <use xlink:href="#glyph2-2" x="207.8525" y="114"/>
</g>
<g style="fill:rgb(0%,0%,0%);fill-opacity:1;">
  <use xlink:href="#glyph0-1" x="345.1715" y="114"/>
  <use xlink:href="#glyph0-2" x="347.6735" y="114"/>
</g>
<g style="fill:rgb(0%,0%,0%);fill-opacity:1;">
  <use xlink:href="#glyph2-1" x="349.9235" y="114"/>
</g>
<g style="fill:rgb(0%,0%,0%);fill-opacity:1;">
  <use xlink:href="#glyph0-3" x="352.9205" y="114"/>
</g>
<g style="fill:rgb(0%,0%,0%);fill-opacity:1;">
  <use xlink:href="#glyph2-2" x="356.9165" y="114"/>
</g>
<path style="fill:none;stroke-width:1;stroke-linecap:butt;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 112 50 L 132.714844 50 " transform="matrix(1,0,0,1,1,1)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:butt;stroke-linejoin:miter;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 136.714844 50 L 132.714844 50 M 132.714844 48.5 L 136.714844 50 L 132.714844 51.5 " transform="matrix(1,0,0,1,1,1)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:butt;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 262.191406 50 L 282.746094 50 " transform="matrix(1,0,0,1,1,1)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:butt;stroke-linejoin:miter;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 286.90625 50 L 282.746094 50 M 282.746094 48.441406 L 286.90625 50 L 282.746094 51.558594 " transform="matrix(1,0,0,1,1,1)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:butt;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 0 33 L 101 33 " transform="matrix(1,0,0,1,1,1)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:butt;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 150 92 L 251 25 " transform="matrix(1,0,0,1,1,1)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 326 0 C 326 0 342.664062 59 363.914062 59 C 385.160156 59 401 0 401 0 " transform="matrix(1,0,0,1,1,1)"/>
</g>
</svg>
