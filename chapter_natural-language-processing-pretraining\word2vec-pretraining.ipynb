{"cells": [{"cell_type": "markdown", "id": "fe9ebb9a", "metadata": {"origin_pos": 0}, "source": ["# 预训练word2vec\n", ":label:`sec_word2vec_pretraining`\n", "\n", "我们继续实现 :numref:`sec_word2vec`中定义的跳元语法模型。然后，我们将在PTB数据集上使用负采样预训练word2vec。首先，让我们通过调用`d2l.load_data_ptb`函数来获得该数据集的数据迭代器和词表，该函数在 :numref:`sec_word2vec_data`中进行了描述。\n"]}, {"cell_type": "code", "execution_count": 1, "id": "f3a5b938", "metadata": {"execution": {"iopub.execute_input": "2022-12-07T17:40:19.953226Z", "iopub.status.busy": "2022-12-07T17:40:19.952448Z", "iopub.status.idle": "2022-12-07T17:40:35.784827Z", "shell.execute_reply": "2022-12-07T17:40:35.783935Z"}, "origin_pos": 2, "tab": ["pytorch"]}, "outputs": [], "source": ["import math\n", "import torch\n", "from torch import nn\n", "from d2l import torch as d2l\n", "\n", "batch_size, max_window_size, num_noise_words = 512, 5, 5\n", "data_iter, vocab = d2l.load_data_ptb(batch_size, max_window_size,\n", "                                     num_noise_words)"]}, {"cell_type": "markdown", "id": "78f2a41d", "metadata": {"origin_pos": 4}, "source": ["## 跳元模型\n", "\n", "我们通过嵌入层和批量矩阵乘法实现了跳元模型。首先，让我们回顾一下嵌入层是如何工作的。\n", "\n", "### 嵌入层\n", "\n", "如 :numref:`sec_seq2seq`中所述，嵌入层将词元的索引映射到其特征向量。该层的权重是一个矩阵，其行数等于字典大小（`input_dim`），列数等于每个标记的向量维数（`output_dim`）。在词嵌入模型训练之后，这个权重就是我们所需要的。\n"]}, {"cell_type": "code", "execution_count": 2, "id": "6df40714", "metadata": {"execution": {"iopub.execute_input": "2022-12-07T17:40:35.789134Z", "iopub.status.busy": "2022-12-07T17:40:35.788454Z", "iopub.status.idle": "2022-12-07T17:40:35.818392Z", "shell.execute_reply": "2022-12-07T17:40:35.817643Z"}, "origin_pos": 6, "tab": ["pytorch"]}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Parameter embedding_weight (torch.Size([20, 4]), dtype=torch.float32)\n"]}], "source": ["embed = nn.Embedding(num_embeddings=20, embedding_dim=4)\n", "print(f'Parameter embedding_weight ({embed.weight.shape}, '\n", "      f'dtype={embed.weight.dtype})')"]}, {"cell_type": "markdown", "id": "42970cc1", "metadata": {"origin_pos": 8}, "source": ["嵌入层的输入是词元（词）的索引。对于任何词元索引$i$，其向量表示可以从嵌入层中的权重矩阵的第$i$行获得。由于向量维度（`output_dim`）被设置为4，因此当小批量词元索引的形状为（2，3）时，嵌入层返回具有形状（2，3，4）的向量。\n"]}, {"cell_type": "code", "execution_count": 3, "id": "2613bef6", "metadata": {"execution": {"iopub.execute_input": "2022-12-07T17:40:35.821598Z", "iopub.status.busy": "2022-12-07T17:40:35.821157Z", "iopub.status.idle": "2022-12-07T17:40:35.829815Z", "shell.execute_reply": "2022-12-07T17:40:35.829074Z"}, "origin_pos": 9, "tab": ["pytorch"]}, "outputs": [{"data": {"text/plain": ["tensor([[[ 0.5375, -0.9901, -0.4834,  0.2410],\n", "         [-2.3540, -0.0612, -0.5962,  1.3918],\n", "         [ 0.2869, -0.3350,  0.2566, -0.3582]],\n", "\n", "        [[ 1.7132, -2.4833,  0.3041, -1.0750],\n", "         [ 1.5866,  0.4862,  1.1711, -0.1775],\n", "         [-1.0431, -1.6185, -1.4956, -0.8961]]], grad_fn=<EmbeddingBackward0>)"]}, "execution_count": 3, "metadata": {}, "output_type": "execute_result"}], "source": ["x = torch.tensor([[1, 2, 3], [4, 5, 6]])\n", "embed(x)"]}, {"cell_type": "markdown", "id": "b1d41ce3", "metadata": {"origin_pos": 10}, "source": ["### 定义前向传播\n", "\n", "在前向传播中，跳元语法模型的输入包括形状为（批量大小，1）的中心词索引`center`和形状为（批量大小，`max_len`）的上下文与噪声词索引`contexts_and_negatives`，其中`max_len`在 :numref:`subsec_word2vec-minibatch-loading`中定义。这两个变量首先通过嵌入层从词元索引转换成向量，然后它们的批量矩阵相乘（在 :numref:`subsec_batch_dot`中描述）返回形状为（批量大小，1，`max_len`）的输出。输出中的每个元素是中心词向量和上下文或噪声词向量的点积。\n"]}, {"cell_type": "code", "execution_count": 4, "id": "a5788022", "metadata": {"execution": {"iopub.execute_input": "2022-12-07T17:40:35.832998Z", "iopub.status.busy": "2022-12-07T17:40:35.832565Z", "iopub.status.idle": "2022-12-07T17:40:35.836875Z", "shell.execute_reply": "2022-12-07T17:40:35.836091Z"}, "origin_pos": 12, "tab": ["pytorch"]}, "outputs": [], "source": ["def skip_gram(center, contexts_and_negatives, embed_v, embed_u):\n", "    v = embed_v(center)\n", "    u = embed_u(contexts_and_negatives)\n", "    pred = torch.bmm(v, u.permute(0, 2, 1))\n", "    return pred"]}, {"cell_type": "markdown", "id": "af56bf5d", "metadata": {"origin_pos": 14}, "source": ["让我们为一些样例输入打印此`skip_gram`函数的输出形状。\n"]}, {"cell_type": "code", "execution_count": 5, "id": "ac86c08e", "metadata": {"execution": {"iopub.execute_input": "2022-12-07T17:40:35.840038Z", "iopub.status.busy": "2022-12-07T17:40:35.839619Z", "iopub.status.idle": "2022-12-07T17:40:35.847112Z", "shell.execute_reply": "2022-12-07T17:40:35.846261Z"}, "origin_pos": 16, "tab": ["pytorch"]}, "outputs": [{"data": {"text/plain": ["torch.Size([2, 1, 4])"]}, "execution_count": 5, "metadata": {}, "output_type": "execute_result"}], "source": ["skip_gram(torch.ones((2, 1), dtype=torch.long),\n", "          torch.ones((2, 4), dtype=torch.long), embed, embed).shape"]}, {"cell_type": "markdown", "id": "91723681", "metadata": {"origin_pos": 18}, "source": ["## 训练\n", "\n", "在训练带负采样的跳元模型之前，我们先定义它的损失函数。\n", "\n", "### 二元交叉熵损失\n", "\n", "根据 :numref:`subsec_negative-sampling`中负采样损失函数的定义，我们将使用二元交叉熵损失。\n"]}, {"cell_type": "code", "execution_count": 6, "id": "879b0bee", "metadata": {"execution": {"iopub.execute_input": "2022-12-07T17:40:35.850479Z", "iopub.status.busy": "2022-12-07T17:40:35.849845Z", "iopub.status.idle": "2022-12-07T17:40:35.854887Z", "shell.execute_reply": "2022-12-07T17:40:35.854107Z"}, "origin_pos": 20, "tab": ["pytorch"]}, "outputs": [], "source": ["class SigmoidBCELoss(nn.Module):\n", "    # 带掩码的二元交叉熵损失\n", "    def __init__(self):\n", "        super().__init__()\n", "\n", "    def forward(self, inputs, target, mask=None):\n", "        out = nn.functional.binary_cross_entropy_with_logits(\n", "            inputs, target, weight=mask, reduction=\"none\")\n", "        return out.mean(dim=1)\n", "\n", "loss = SigmoidBCELoss()"]}, {"cell_type": "markdown", "id": "f586bf47", "metadata": {"origin_pos": 22}, "source": ["回想一下我们在 :numref:`subsec_word2vec-minibatch-loading`中对掩码变量和标签变量的描述。下面计算给定变量的二进制交叉熵损失。\n"]}, {"cell_type": "code", "execution_count": 7, "id": "2bb8e16d", "metadata": {"execution": {"iopub.execute_input": "2022-12-07T17:40:35.858635Z", "iopub.status.busy": "2022-12-07T17:40:35.857630Z", "iopub.status.idle": "2022-12-07T17:40:35.867067Z", "shell.execute_reply": "2022-12-07T17:40:35.865964Z"}, "origin_pos": 23, "tab": ["pytorch"]}, "outputs": [{"data": {"text/plain": ["tensor([0.9352, 1.8462])"]}, "execution_count": 7, "metadata": {}, "output_type": "execute_result"}], "source": ["pred = torch.tensor([[1.1, -2.2, 3.3, -4.4]] * 2)\n", "label = torch.tensor([[1.0, 0.0, 0.0, 0.0], [0.0, 1.0, 0.0, 0.0]])\n", "mask = torch.tensor([[1, 1, 1, 1], [1, 1, 0, 0]])\n", "loss(pred, label, mask) * mask.shape[1] / mask.sum(axis=1)"]}, {"cell_type": "markdown", "id": "32b9d7c2", "metadata": {"origin_pos": 25}, "source": ["下面显示了如何使用二元交叉熵损失中的Sigmoid激活函数（以较低效率的方式）计算上述结果。我们可以将这两个输出视为两个规范化的损失，在非掩码预测上进行平均。\n"]}, {"cell_type": "code", "execution_count": 8, "id": "f5b6bdec", "metadata": {"execution": {"iopub.execute_input": "2022-12-07T17:40:35.870326Z", "iopub.status.busy": "2022-12-07T17:40:35.869886Z", "iopub.status.idle": "2022-12-07T17:40:35.876076Z", "shell.execute_reply": "2022-12-07T17:40:35.874967Z"}, "origin_pos": 26, "tab": ["pytorch"]}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["0.9352\n", "1.8462\n"]}], "source": ["def sigmd(x):\n", "    return -math.log(1 / (1 + math.exp(-x)))\n", "\n", "print(f'{(sigmd(1.1) + sigmd(2.2) + sigmd(-3.3) + sigmd(4.4)) / 4:.4f}')\n", "print(f'{(sigmd(-1.1) + sigmd(-2.2)) / 2:.4f}')"]}, {"cell_type": "markdown", "id": "d6ebc686", "metadata": {"origin_pos": 27}, "source": ["### 初始化模型参数\n", "\n", "我们定义了两个嵌入层，将词表中的所有单词分别作为中心词和上下文词使用。字向量维度`embed_size`被设置为100。\n"]}, {"cell_type": "code", "execution_count": 9, "id": "869c6bed", "metadata": {"execution": {"iopub.execute_input": "2022-12-07T17:40:35.879376Z", "iopub.status.busy": "2022-12-07T17:40:35.878712Z", "iopub.status.idle": "2022-12-07T17:40:35.894832Z", "shell.execute_reply": "2022-12-07T17:40:35.893693Z"}, "origin_pos": 29, "tab": ["pytorch"]}, "outputs": [], "source": ["embed_size = 100\n", "net = nn.Sequential(nn.Embedding(num_embeddings=len(vocab),\n", "                                 embedding_dim=embed_size),\n", "                    nn.Embedding(num_embeddings=len(vocab),\n", "                                 embedding_dim=embed_size))"]}, {"cell_type": "markdown", "id": "e6bb9935", "metadata": {"origin_pos": 30}, "source": ["### 定义训练阶段代码\n", "\n", "训练阶段代码实现定义如下。由于填充的存在，损失函数的计算与以前的训练函数略有不同。\n"]}, {"cell_type": "code", "execution_count": 10, "id": "0f6b885d", "metadata": {"execution": {"iopub.execute_input": "2022-12-07T17:40:35.898003Z", "iopub.status.busy": "2022-12-07T17:40:35.897504Z", "iopub.status.idle": "2022-12-07T17:40:35.907430Z", "shell.execute_reply": "2022-12-07T17:40:35.906294Z"}, "origin_pos": 32, "tab": ["pytorch"]}, "outputs": [], "source": ["def train(net, data_iter, lr, num_epochs, device=d2l.try_gpu()):\n", "    def init_weights(m):\n", "        if type(m) == nn.Embedding:\n", "            nn.init.xavier_uniform_(m.weight)\n", "    net.apply(init_weights)\n", "    net = net.to(device)\n", "    optimizer = torch.optim.Adam(net.parameters(), lr=lr)\n", "    animator = d2l.Animator(xlabel='epoch', ylabel='loss',\n", "                            xlim=[1, num_epochs])\n", "    # 规范化的损失之和，规范化的损失数\n", "    metric = d2l.Accumulator(2)\n", "    for epoch in range(num_epochs):\n", "        timer, num_batches = d2l.Timer(), len(data_iter)\n", "        for i, batch in enumerate(data_iter):\n", "            optimizer.zero_grad()\n", "            center, context_negative, mask, label = [\n", "                data.to(device) for data in batch]\n", "\n", "            pred = skip_gram(center, context_negative, net[0], net[1])\n", "            l = (loss(pred.reshape(label.shape).float(), label.float(), mask)\n", "                     / mask.sum(axis=1) * mask.shape[1])\n", "            l.sum().backward()\n", "            optimizer.step()\n", "            metric.add(l.sum(), l.numel())\n", "            if (i + 1) % (num_batches // 5) == 0 or i == num_batches - 1:\n", "                animator.add(epoch + (i + 1) / num_batches,\n", "                             (metric[0] / metric[1],))\n", "    print(f'loss {metric[0] / metric[1]:.3f}, '\n", "          f'{metric[1] / timer.stop():.1f} tokens/sec on {str(device)}')"]}, {"cell_type": "markdown", "id": "e6545a7b", "metadata": {"origin_pos": 34}, "source": ["现在，我们可以使用负采样来训练跳元模型。\n"]}, {"cell_type": "code", "execution_count": 11, "id": "1dbc2455", "metadata": {"execution": {"iopub.execute_input": "2022-12-07T17:40:35.910762Z", "iopub.status.busy": "2022-12-07T17:40:35.910236Z", "iopub.status.idle": "2022-12-07T17:41:01.809090Z", "shell.execute_reply": "2022-12-07T17:41:01.808134Z"}, "origin_pos": 35, "tab": ["pytorch"]}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["loss 0.410, 371861.2 tokens/sec on cuda:0\n"]}, {"data": {"image/svg+xml": ["<?xml version=\"1.0\" encoding=\"utf-8\" standalone=\"no\"?>\n", "<!DOCTYPE svg PUBLIC \"-//W3C//DTD SVG 1.1//EN\"\n", "  \"http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd\">\n", "<svg xmlns:xlink=\"http://www.w3.org/1999/xlink\" width=\"255.825pt\" height=\"183.35625pt\" viewBox=\"0 0 255.825 183.35625\" xmlns=\"http://www.w3.org/2000/svg\" version=\"1.1\">\n", " <metadata>\n", "  <rdf:RDF xmlns:dc=\"http://purl.org/dc/elements/1.1/\" xmlns:cc=\"http://creativecommons.org/ns#\" xmlns:rdf=\"http://www.w3.org/1999/02/22-rdf-syntax-ns#\">\n", "   <cc:Work>\n", "    <dc:type rdf:resource=\"http://purl.org/dc/dcmitype/StillImage\"/>\n", "    <dc:date>2022-12-07T17:41:01.776168</dc:date>\n", "    <dc:format>image/svg+xml</dc:format>\n", "    <dc:creator>\n", "     <cc:Agent>\n", "      <dc:title>Matplotlib v3.5.1, https://matplotlib.org/</dc:title>\n", "     </cc:Agent>\n", "    </dc:creator>\n", "   </cc:Work>\n", "  </rdf:RDF>\n", " </metadata>\n", " <defs>\n", "  <style type=\"text/css\">*{stroke-linejoin: round; stroke-linecap: butt}</style>\n", " </defs>\n", " <g id=\"figure_1\">\n", "  <g id=\"patch_1\">\n", "   <path d=\"M 0 183.35625 \n", "L 255.825 183.35625 \n", "L 255.825 0 \n", "L 0 0 \n", "z\n", "\" style=\"fill: #ffffff\"/>\n", "  </g>\n", "  <g id=\"axes_1\">\n", "   <g id=\"patch_2\">\n", "    <path d=\"M 50.14375 145.8 \n", "L 245.44375 145.8 \n", "L 245.44375 7.2 \n", "L 50.14375 7.2 \n", "z\n", "\" style=\"fill: #ffffff\"/>\n", "   </g>\n", "   <g id=\"matplotlib.axis_1\">\n", "    <g id=\"xtick_1\">\n", "     <g id=\"line2d_1\">\n", "      <path d=\"M 50.14375 145.8 \n", "L 50.14375 7.2 \n", "\" clip-path=\"url(#pe5b6aa4ca4)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_2\">\n", "      <defs>\n", "       <path id=\"m5bfe0a623f\" d=\"M 0 0 \n", "L 0 3.5 \n", "\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </defs>\n", "      <g>\n", "       <use xlink:href=\"#m5bfe0a623f\" x=\"50.14375\" y=\"145.8\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_1\">\n", "      <!-- 1 -->\n", "      <g transform=\"translate(46.9625 160.398438)scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-31\" d=\"M 794 531 \n", "L 1825 531 \n", "L 1825 4091 \n", "L 703 3866 \n", "L 703 4441 \n", "L 1819 4666 \n", "L 2450 4666 \n", "L 2450 531 \n", "L 3481 531 \n", "L 3481 0 \n", "L 794 0 \n", "L 794 531 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-31\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_2\">\n", "     <g id=\"line2d_3\">\n", "      <path d=\"M 98.96875 145.8 \n", "L 98.96875 7.2 \n", "\" clip-path=\"url(#pe5b6aa4ca4)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_4\">\n", "      <g>\n", "       <use xlink:href=\"#m5bfe0a623f\" x=\"98.96875\" y=\"145.8\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_2\">\n", "      <!-- 2 -->\n", "      <g transform=\"translate(95.7875 160.398438)scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-32\" d=\"M 1228 531 \n", "L 3431 531 \n", "L 3431 0 \n", "L 469 0 \n", "L 469 531 \n", "Q 828 903 1448 1529 \n", "Q 2069 2156 2228 2338 \n", "Q 2531 2678 2651 2914 \n", "Q 2772 3150 2772 3378 \n", "Q 2772 3750 2511 3984 \n", "Q 2250 4219 1831 4219 \n", "Q 1534 4219 1204 4116 \n", "Q 875 4013 500 3803 \n", "L 500 4441 \n", "Q 881 4594 1212 4672 \n", "Q 1544 4750 1819 4750 \n", "Q 2544 4750 2975 4387 \n", "Q 3406 4025 3406 3419 \n", "Q 3406 3131 3298 2873 \n", "Q 3191 2616 2906 2266 \n", "Q 2828 2175 2409 1742 \n", "Q 1991 1309 1228 531 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-32\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_3\">\n", "     <g id=\"line2d_5\">\n", "      <path d=\"M 147.79375 145.8 \n", "L 147.79375 7.2 \n", "\" clip-path=\"url(#pe5b6aa4ca4)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_6\">\n", "      <g>\n", "       <use xlink:href=\"#m5bfe0a623f\" x=\"147.79375\" y=\"145.8\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_3\">\n", "      <!-- 3 -->\n", "      <g transform=\"translate(144.6125 160.398438)scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-33\" d=\"M 2597 2516 \n", "Q 3050 2419 3304 2112 \n", "Q 3559 1806 3559 1356 \n", "Q 3559 666 3084 287 \n", "Q 2609 -91 1734 -91 \n", "Q 1441 -91 1130 -33 \n", "Q 819 25 488 141 \n", "L 488 750 \n", "Q 750 597 1062 519 \n", "Q 1375 441 1716 441 \n", "Q 2309 441 2620 675 \n", "Q 2931 909 2931 1356 \n", "Q 2931 1769 2642 2001 \n", "Q 2353 2234 1838 2234 \n", "L 1294 2234 \n", "L 1294 2753 \n", "L 1863 2753 \n", "Q 2328 2753 2575 2939 \n", "Q 2822 3125 2822 3475 \n", "Q 2822 3834 2567 4026 \n", "Q 2313 4219 1838 4219 \n", "Q 1578 4219 1281 4162 \n", "Q 984 4106 628 3988 \n", "L 628 4550 \n", "Q 988 4650 1302 4700 \n", "Q 1616 4750 1894 4750 \n", "Q 2613 4750 3031 4423 \n", "Q 3450 4097 3450 3541 \n", "Q 3450 3153 3228 2886 \n", "Q 3006 2619 2597 2516 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-33\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_4\">\n", "     <g id=\"line2d_7\">\n", "      <path d=\"M 196.61875 145.8 \n", "L 196.61875 7.2 \n", "\" clip-path=\"url(#pe5b6aa4ca4)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_8\">\n", "      <g>\n", "       <use xlink:href=\"#m5bfe0a623f\" x=\"196.61875\" y=\"145.8\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_4\">\n", "      <!-- 4 -->\n", "      <g transform=\"translate(193.4375 160.398438)scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-34\" d=\"M 2419 4116 \n", "L 825 1625 \n", "L 2419 1625 \n", "L 2419 4116 \n", "z\n", "M 2253 4666 \n", "L 3047 4666 \n", "L 3047 1625 \n", "L 3713 1625 \n", "L 3713 1100 \n", "L 3047 1100 \n", "L 3047 0 \n", "L 2419 0 \n", "L 2419 1100 \n", "L 313 1100 \n", "L 313 1709 \n", "L 2253 4666 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-34\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_5\">\n", "     <g id=\"line2d_9\">\n", "      <path d=\"M 245.44375 145.8 \n", "L 245.44375 7.2 \n", "\" clip-path=\"url(#pe5b6aa4ca4)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_10\">\n", "      <g>\n", "       <use xlink:href=\"#m5bfe0a623f\" x=\"245.44375\" y=\"145.8\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_5\">\n", "      <!-- 5 -->\n", "      <g transform=\"translate(242.2625 160.398438)scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-35\" d=\"M 691 4666 \n", "L 3169 4666 \n", "L 3169 4134 \n", "L 1269 4134 \n", "L 1269 2991 \n", "Q 1406 3038 1543 3061 \n", "Q 1681 3084 1819 3084 \n", "Q 2600 3084 3056 2656 \n", "Q 3513 2228 3513 1497 \n", "Q 3513 744 3044 326 \n", "Q 2575 -91 1722 -91 \n", "Q 1428 -91 1123 -41 \n", "Q 819 9 494 109 \n", "L 494 744 \n", "Q 775 591 1075 516 \n", "Q 1375 441 1709 441 \n", "Q 2250 441 2565 725 \n", "Q 2881 1009 2881 1497 \n", "Q 2881 1984 2565 2268 \n", "Q 2250 2553 1709 2553 \n", "Q 1456 2553 1204 2497 \n", "Q 953 2441 691 2322 \n", "L 691 4666 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-35\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"text_6\">\n", "     <!-- epoch -->\n", "     <g transform=\"translate(132.565625 174.076563)scale(0.1 -0.1)\">\n", "      <defs>\n", "       <path id=\"DejaVuSans-65\" d=\"M 3597 1894 \n", "L 3597 1613 \n", "L 953 1613 \n", "Q 991 1019 1311 708 \n", "Q 1631 397 2203 397 \n", "Q 2534 397 2845 478 \n", "Q 3156 559 3463 722 \n", "L 3463 178 \n", "Q 3153 47 2828 -22 \n", "Q 2503 -91 2169 -91 \n", "Q 1331 -91 842 396 \n", "Q 353 884 353 1716 \n", "Q 353 2575 817 3079 \n", "Q 1281 3584 2069 3584 \n", "Q 2775 3584 3186 3129 \n", "Q 3597 2675 3597 1894 \n", "z\n", "M 3022 2063 \n", "Q 3016 2534 2758 2815 \n", "Q 2500 3097 2075 3097 \n", "Q 1594 3097 1305 2825 \n", "Q 1016 2553 972 2059 \n", "L 3022 2063 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-70\" d=\"M 1159 525 \n", "L 1159 -1331 \n", "L 581 -1331 \n", "L 581 3500 \n", "L 1159 3500 \n", "L 1159 2969 \n", "Q 1341 3281 1617 3432 \n", "Q 1894 3584 2278 3584 \n", "Q 2916 3584 3314 3078 \n", "Q 3713 2572 3713 1747 \n", "Q 3713 922 3314 415 \n", "Q 2916 -91 2278 -91 \n", "Q 1894 -91 1617 61 \n", "Q 1341 213 1159 525 \n", "z\n", "M 3116 1747 \n", "Q 3116 2381 2855 2742 \n", "Q 2594 3103 2138 3103 \n", "Q 1681 3103 1420 2742 \n", "Q 1159 2381 1159 1747 \n", "Q 1159 1113 1420 752 \n", "Q 1681 391 2138 391 \n", "Q 2594 391 2855 752 \n", "Q 3116 1113 3116 1747 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-6f\" d=\"M 1959 3097 \n", "Q 1497 3097 1228 2736 \n", "Q 959 2375 959 1747 \n", "Q 959 1119 1226 758 \n", "Q 1494 397 1959 397 \n", "Q 2419 397 2687 759 \n", "Q 2956 1122 2956 1747 \n", "Q 2956 2369 2687 2733 \n", "Q 2419 3097 1959 3097 \n", "z\n", "M 1959 3584 \n", "Q 2709 3584 3137 3096 \n", "Q 3566 2609 3566 1747 \n", "Q 3566 888 3137 398 \n", "Q 2709 -91 1959 -91 \n", "Q 1206 -91 779 398 \n", "Q 353 888 353 1747 \n", "Q 353 2609 779 3096 \n", "Q 1206 3584 1959 3584 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-63\" d=\"M 3122 3366 \n", "L 3122 2828 \n", "Q 2878 2963 2633 3030 \n", "Q 2388 3097 2138 3097 \n", "Q 1578 3097 1268 2742 \n", "Q 959 2388 959 1747 \n", "Q 959 1106 1268 751 \n", "Q 1578 397 2138 397 \n", "Q 2388 397 2633 464 \n", "Q 2878 531 3122 666 \n", "L 3122 134 \n", "Q 2881 22 2623 -34 \n", "Q 2366 -91 2075 -91 \n", "Q 1284 -91 818 406 \n", "Q 353 903 353 1747 \n", "Q 353 2603 823 3093 \n", "Q 1294 3584 2113 3584 \n", "Q 2378 3584 2631 3529 \n", "Q 2884 3475 3122 3366 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-68\" d=\"M 3513 2113 \n", "L 3513 0 \n", "L 2938 0 \n", "L 2938 2094 \n", "Q 2938 2591 2744 2837 \n", "Q 2550 3084 2163 3084 \n", "Q 1697 3084 1428 2787 \n", "Q 1159 2491 1159 1978 \n", "L 1159 0 \n", "L 581 0 \n", "L 581 4863 \n", "L 1159 4863 \n", "L 1159 2956 \n", "Q 1366 3272 1645 3428 \n", "Q 1925 3584 2291 3584 \n", "Q 2894 3584 3203 3211 \n", "Q 3513 2838 3513 2113 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "      </defs>\n", "      <use xlink:href=\"#DejaVuSans-65\"/>\n", "      <use xlink:href=\"#DejaVuSans-70\" x=\"61.523438\"/>\n", "      <use xlink:href=\"#DejaVuSans-6f\" x=\"125\"/>\n", "      <use xlink:href=\"#DejaVuSans-63\" x=\"186.181641\"/>\n", "      <use xlink:href=\"#DejaVuSans-68\" x=\"241.162109\"/>\n", "     </g>\n", "    </g>\n", "   </g>\n", "   <g id=\"matplotlib.axis_2\">\n", "    <g id=\"ytick_1\">\n", "     <g id=\"line2d_11\">\n", "      <path d=\"M 50.14375 114.775996 \n", "L 245.44375 114.775996 \n", "\" clip-path=\"url(#pe5b6aa4ca4)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_12\">\n", "      <defs>\n", "       <path id=\"m0044f852f7\" d=\"M 0 0 \n", "L -3.5 0 \n", "\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </defs>\n", "      <g>\n", "       <use xlink:href=\"#m0044f852f7\" x=\"50.14375\" y=\"114.775996\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_7\">\n", "      <!-- 0.45 -->\n", "      <g transform=\"translate(20.878125 118.575215)scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-30\" d=\"M 2034 4250 \n", "Q 1547 4250 1301 3770 \n", "Q 1056 3291 1056 2328 \n", "Q 1056 1369 1301 889 \n", "Q 1547 409 2034 409 \n", "Q 2525 409 2770 889 \n", "Q 3016 1369 3016 2328 \n", "Q 3016 3291 2770 3770 \n", "Q 2525 4250 2034 4250 \n", "z\n", "M 2034 4750 \n", "Q 2819 4750 3233 4129 \n", "Q 3647 3509 3647 2328 \n", "Q 3647 1150 3233 529 \n", "Q 2819 -91 2034 -91 \n", "Q 1250 -91 836 529 \n", "Q 422 1150 422 2328 \n", "Q 422 3509 836 4129 \n", "Q 1250 4750 2034 4750 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "        <path id=\"DejaVuSans-2e\" d=\"M 684 794 \n", "L 1344 794 \n", "L 1344 0 \n", "L 684 0 \n", "L 684 794 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-34\" x=\"95.410156\"/>\n", "       <use xlink:href=\"#DejaVuSans-35\" x=\"159.033203\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_2\">\n", "     <g id=\"line2d_13\">\n", "      <path d=\"M 50.14375 83.730383 \n", "L 245.44375 83.730383 \n", "\" clip-path=\"url(#pe5b6aa4ca4)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_14\">\n", "      <g>\n", "       <use xlink:href=\"#m0044f852f7\" x=\"50.14375\" y=\"83.730383\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_8\">\n", "      <!-- 0.50 -->\n", "      <g transform=\"translate(20.878125 87.529602)scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-35\" x=\"95.410156\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"159.033203\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_3\">\n", "     <g id=\"line2d_15\">\n", "      <path d=\"M 50.14375 52.684771 \n", "L 245.44375 52.684771 \n", "\" clip-path=\"url(#pe5b6aa4ca4)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_16\">\n", "      <g>\n", "       <use xlink:href=\"#m0044f852f7\" x=\"50.14375\" y=\"52.684771\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_9\">\n", "      <!-- 0.55 -->\n", "      <g transform=\"translate(20.878125 56.48399)scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-35\" x=\"95.410156\"/>\n", "       <use xlink:href=\"#DejaVuSans-35\" x=\"159.033203\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_4\">\n", "     <g id=\"line2d_17\">\n", "      <path d=\"M 50.14375 21.639158 \n", "L 245.44375 21.639158 \n", "\" clip-path=\"url(#pe5b6aa4ca4)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_18\">\n", "      <g>\n", "       <use xlink:href=\"#m0044f852f7\" x=\"50.14375\" y=\"21.639158\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_10\">\n", "      <!-- 0.60 -->\n", "      <g transform=\"translate(20.878125 25.438377)scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-36\" d=\"M 2113 2584 \n", "Q 1688 2584 1439 2293 \n", "Q 1191 2003 1191 1497 \n", "Q 1191 994 1439 701 \n", "Q 1688 409 2113 409 \n", "Q 2538 409 2786 701 \n", "Q 3034 994 3034 1497 \n", "Q 3034 2003 2786 2293 \n", "Q 2538 2584 2113 2584 \n", "z\n", "M 3366 4563 \n", "L 3366 3988 \n", "Q 3128 4100 2886 4159 \n", "Q 2644 4219 2406 4219 \n", "Q 1781 4219 1451 3797 \n", "Q 1122 3375 1075 2522 \n", "Q 1259 2794 1537 2939 \n", "Q 1816 3084 2150 3084 \n", "Q 2853 3084 3261 2657 \n", "Q 3669 2231 3669 1497 \n", "Q 3669 778 3244 343 \n", "Q 2819 -91 2113 -91 \n", "Q 1303 -91 875 529 \n", "Q 447 1150 447 2328 \n", "Q 447 3434 972 4092 \n", "Q 1497 4750 2381 4750 \n", "Q 2619 4750 2861 4703 \n", "Q 3103 4656 3366 4563 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-36\" x=\"95.410156\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"159.033203\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"text_11\">\n", "     <!-- loss -->\n", "     <g transform=\"translate(14.798437 86.157813)rotate(-90)scale(0.1 -0.1)\">\n", "      <defs>\n", "       <path id=\"DejaVuSans-6c\" d=\"M 603 4863 \n", "L 1178 4863 \n", "L 1178 0 \n", "L 603 0 \n", "L 603 4863 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-73\" d=\"M 2834 3397 \n", "L 2834 2853 \n", "Q 2591 2978 2328 3040 \n", "Q 2066 3103 1784 3103 \n", "Q 1356 3103 1142 2972 \n", "Q 928 2841 928 2578 \n", "Q 928 2378 1081 2264 \n", "Q 1234 2150 1697 2047 \n", "L 1894 2003 \n", "Q 2506 1872 2764 1633 \n", "Q 3022 1394 3022 966 \n", "Q 3022 478 2636 193 \n", "Q 2250 -91 1575 -91 \n", "Q 1294 -91 989 -36 \n", "Q 684 19 347 128 \n", "L 347 722 \n", "Q 666 556 975 473 \n", "Q 1284 391 1588 391 \n", "Q 1994 391 2212 530 \n", "Q 2431 669 2431 922 \n", "Q 2431 1156 2273 1281 \n", "Q 2116 1406 1581 1522 \n", "L 1381 1569 \n", "Q 847 1681 609 1914 \n", "Q 372 2147 372 2553 \n", "Q 372 3047 722 3315 \n", "Q 1072 3584 1716 3584 \n", "Q 2034 3584 2315 3537 \n", "Q 2597 3491 2834 3397 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "      </defs>\n", "      <use xlink:href=\"#DejaVuSans-6c\"/>\n", "      <use xlink:href=\"#DejaVuSans-6f\" x=\"27.783203\"/>\n", "      <use xlink:href=\"#DejaVuSans-73\" x=\"88.964844\"/>\n", "      <use xlink:href=\"#DejaVuSans-73\" x=\"141.064453\"/>\n", "     </g>\n", "    </g>\n", "   </g>\n", "   <g id=\"line2d_19\">\n", "    <path d=\"M 11.069197 13.5 \n", "L 20.819644 60.506856 \n", "L 30.570091 79.383622 \n", "L 40.320538 89.443771 \n", "L 50.070985 95.858035 \n", "L 50.14375 95.861294 \n", "L 59.894197 100.721818 \n", "L 69.644644 104.485806 \n", "L 79.395091 107.58866 \n", "L 89.145538 110.227737 \n", "L 98.895985 112.534348 \n", "L 98.96875 112.53624 \n", "L 108.719197 115.021772 \n", "L 118.469644 117.22833 \n", "L 128.220091 119.260327 \n", "L 137.970538 121.098635 \n", "L 147.720985 122.810268 \n", "L 147.79375 122.811583 \n", "L 157.544197 124.929769 \n", "L 167.294644 126.847121 \n", "L 177.045091 128.595662 \n", "L 186.795538 130.182827 \n", "L 196.545985 131.648003 \n", "L 196.61875 131.649424 \n", "L 206.369197 133.558644 \n", "L 216.119644 135.274322 \n", "L 225.870091 136.816615 \n", "L 235.620538 138.224928 \n", "L 245.370985 139.498856 \n", "L 245.44375 139.5 \n", "\" clip-path=\"url(#pe5b6aa4ca4)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_3\">\n", "    <path d=\"M 50.14375 145.8 \n", "L 50.14375 7.2 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_4\">\n", "    <path d=\"M 245.44375 145.8 \n", "L 245.44375 7.2 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_5\">\n", "    <path d=\"M 50.14375 145.8 \n", "L 245.44375 145.8 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_6\">\n", "    <path d=\"M 50.14375 7.2 \n", "L 245.44375 7.2 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "  </g>\n", " </g>\n", " <defs>\n", "  <clipPath id=\"pe5b6aa4ca4\">\n", "   <rect x=\"50.14375\" y=\"7.2\" width=\"195.3\" height=\"138.6\"/>\n", "  </clipPath>\n", " </defs>\n", "</svg>\n"], "text/plain": ["<Figure size 350x250 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["lr, num_epochs = 0.002, 5\n", "train(net, data_iter, lr, num_epochs)"]}, {"cell_type": "markdown", "id": "dc16aa09", "metadata": {"origin_pos": 36}, "source": ["## 应用词嵌入\n", ":label:`subsec_apply-word-embed`\n", "\n", "在训练word2vec模型之后，我们可以使用训练好模型中词向量的余弦相似度来从词表中找到与输入单词语义最相似的单词。\n"]}, {"cell_type": "code", "execution_count": 12, "id": "cf524983", "metadata": {"execution": {"iopub.execute_input": "2022-12-07T17:41:01.812657Z", "iopub.status.busy": "2022-12-07T17:41:01.812341Z", "iopub.status.idle": "2022-12-07T17:41:01.823756Z", "shell.execute_reply": "2022-12-07T17:41:01.822970Z"}, "origin_pos": 38, "tab": ["pytorch"]}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["cosine sim=0.717: intel\n", "cosine sim=0.699: microprocessor\n", "cosine sim=0.680: chips\n"]}], "source": ["def get_similar_tokens(query_token, k, embed):\n", "    W = embed.weight.data\n", "    x = W[vocab[query_token]]\n", "    # 计算余弦相似性。增加1e-9以获得数值稳定性\n", "    cos = torch.mv(W, x) / torch.sqrt(torch.sum(W * W, dim=1) *\n", "                                      torch.sum(x * x) + 1e-9)\n", "    topk = torch.topk(cos, k=k+1)[1].cpu().numpy().astype('int32')\n", "    for i in topk[1:]:  # 删除输入词\n", "        print(f'cosine sim={float(cos[i]):.3f}: {vocab.to_tokens(i)}')\n", "\n", "get_similar_tokens('chip', 3, net[0])"]}, {"cell_type": "markdown", "id": "6af25cfe", "metadata": {"origin_pos": 40}, "source": ["## 小结\n", "\n", "* 我们可以使用嵌入层和二元交叉熵损失来训练带负采样的跳元模型。\n", "* 词嵌入的应用包括基于词向量的余弦相似度为给定词找到语义相似的词。\n", "\n", "## 练习\n", "\n", "1. 使用训练好的模型，找出其他输入词在语义上相似的词。您能通过调优超参数来改进结果吗？\n", "1. 当训练语料库很大时，在更新模型参数时，我们经常对当前小批量的*中心词*进行上下文词和噪声词的采样。换言之，同一中心词在不同的训练迭代轮数可以有不同的上下文词或噪声词。这种方法的好处是什么？尝试实现这种训练方法。\n"]}, {"cell_type": "markdown", "id": "3e1c28b0", "metadata": {"origin_pos": 42, "tab": ["pytorch"]}, "source": ["[Discussions](https://discuss.d2l.ai/t/5740)\n"]}], "metadata": {"language_info": {"name": "python"}}, "nbformat": 4, "nbformat_minor": 5}