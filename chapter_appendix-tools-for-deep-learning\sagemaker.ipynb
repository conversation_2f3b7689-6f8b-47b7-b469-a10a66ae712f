{"cells": [{"cell_type": "markdown", "id": "c0bf5b37", "metadata": {"origin_pos": 0}, "source": ["# 使用Amazon SageMaker\n", ":label:`sec_sagemaker`\n", "\n", "深度学习程序可能需要很多计算资源，这很容易超出你的本地计算机所能提供的范围。云计算服务允许你使用功能更强大的计算机更轻松地运行本书的GPU密集型代码。本节将介绍如何使用Amazon SageMaker运行本书的代码。\n", "\n", "## 注册\n", "\n", "首先，我们需要在注册一个帐户https://aws.amazon.com/。 为了增加安全性，鼓励使用双因素身份验证。设置详细的计费和支出警报也是一个好主意，以避免任何意外，例如，当忘记停止运行实例时。登录AWS帐户后，转到[console](http://console.aws.amazon.com/)并搜索“Amazon SageMaker”（参见 :numref:`fig_sagemaker`），然后单击它打开SageMaker面板。\n", "\n", "![搜索并打开SageMaker面板](../img/sagemaker.png)\n", ":width:`300px`\n", ":label:`fig_sagemaker`\n", "\n", "## 创建SageMaker实例\n", "\n", "接下来，让我们创建一个notebook实例，如 :numref:`fig_sagemaker-create`所示。\n", "\n", "![创建一个SageMaker实例](../img/sagemaker-create.png)\n", ":width:`400px`\n", ":label:`fig_sagemaker-create`\n", "\n", "SageMaker提供多个具有不同计算能力和价格的[实例类型](https://aws.amazon.com/sagemaker/pricing/instance-types/)。创建notebook实例时，可以指定其名称和类型。在 :numref:`fig_sagemaker-create-2`中，我们选择`ml.p3.2xlarge`：使用一个Tesla V100 GPU和一个8核CPU，这个实例的性能足够本书的大部分内容使用。\n", "\n", "![选择实例类型](../img/sagemaker-create-2.png)\n", ":width:`400px`\n", ":label:`fig_sagemaker-create-2`\n"]}, {"cell_type": "markdown", "id": "6953be82", "metadata": {"origin_pos": 2, "tab": ["pytorch"]}, "source": ["用于与SageMaker一起运行的ipynb格式的整本书可从https://github.com/d2l-ai/d2l-pytorch-sagemaker获得。\n", "我们可以指定此GitHub存储库URL（ :numref:`fig_sagemaker-create-3`），以允许SageMaker在创建实例时克隆它。\n"]}, {"cell_type": "markdown", "id": "f2044c37", "metadata": {"origin_pos": 4}, "source": ["![指定GitHub存储库](../img/sagemaker-create-3.png)\n", ":width:`400px`\n", ":label:`fig_sagemaker-create-3`\n", "\n", "## 运行和停止实例\n", "\n", "创建实例可能需要几分钟的时间。当实例准备就绪时，单击它旁边的“Open Jupyter”链接（ :numref:`fig_sagemaker-open`），以便你可以在此实例上编辑并运行本书的所有Jupyter Notebook（类似于 :numref:`sec_jupyter`中的步骤）。\n", "\n", "![在创建的SageMaker实例上打开Jupyter](../img/sagemaker-open.png)\n", ":width:`400px`\n", ":label:`fig_sagemaker-open`\n", "\n", "完成工作后，不要忘记停止实例以避免进一步收费（ :numref:`fig_sagemaker-stop`）。\n", "\n", "![停止SageMaker实例](../img/sagemaker-stop.png)\n", ":width:`300px`\n", ":label:`fig_sagemaker-stop`\n", "\n", "## 更新Notebook\n"]}, {"cell_type": "markdown", "id": "77bd062c", "metadata": {"origin_pos": 6, "tab": ["pytorch"]}, "source": ["这本开源书的notebook将定期在GitHub上的[d2l-ai/d2l-pytorch-sagemaker](https://github.com/d2l-ai/d2l-pytorch-sagemaker)存储库中更新。要更新至最新版本，你可以在SageMaker实例（ :numref:`fig_sagemaker-terminal`）上打开终端。\n"]}, {"cell_type": "markdown", "id": "6eb77bbc", "metadata": {"origin_pos": 8}, "source": ["![在SageMaker实例上打开终端](../img/sagemaker-terminal.png)\n", ":width:`300px`\n", ":label:`fig_sagemaker-terminal`\n", "\n", "你可能希望在从远程存储库提取更新之前提交本地更改。否则，只需在终端中使用以下命令放弃所有本地更改：\n"]}, {"cell_type": "markdown", "id": "a2cbd0f0", "metadata": {"origin_pos": 10, "tab": ["pytorch"]}, "source": ["```bash\n", "cd SageMaker/d2l-pytorch-sagemaker/\n", "git reset --hard\n", "git pull\n", "```\n"]}, {"cell_type": "markdown", "id": "9aa0b5df", "metadata": {"origin_pos": 12}, "source": ["## 小结\n", "\n", "* 我们可以使用Amazon SageMaker创建一个GPU的notebook实例来运行本书的密集型代码。\n", "* 我们可以通过Amazon SageMaker实例上的终端更新notebooks。\n", "\n", "## 练习\n", "\n", "1. 使用Amazon SageMaker编辑并运行任何需要GPU的部分。\n", "1. 打开终端以访问保存本书所有notebooks的本地目录。\n", "\n", "[Discussions](https://discuss.d2l.ai/t/5732)\n"]}], "metadata": {"language_info": {"name": "python"}}, "nbformat": 4, "nbformat_minor": 5}