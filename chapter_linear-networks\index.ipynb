{"cells": [{"cell_type": "markdown", "id": "a4c79318", "metadata": {"origin_pos": 0}, "source": ["# 线性神经网络\n", ":label:`chap_linear`\n", "\n", "在介绍深度神经网络之前，我们需要了解神经网络训练的基础知识。\n", "本章我们将介绍神经网络的整个训练过程，\n", "包括：定义简单的神经网络架构、数据处理、指定损失函数和如何训练模型。\n", "为了更容易学习，我们将从经典算法————*线性*神经网络开始，介绍神经网络的基础知识。\n", "经典统计学习技术中的线性回归和softmax回归可以视为线性神经网络，\n", "这些知识将为本书其他部分中更复杂的技术奠定基础。\n", "\n", ":begin_tab:toc\n", " - [linear-regression](linear-regression.ipynb)\n", " - [linear-regression-scratch](linear-regression-scratch.ipynb)\n", " - [linear-regression-concise](linear-regression-concise.ipynb)\n", " - [softmax-regression](softmax-regression.ipynb)\n", " - [image-classification-dataset](image-classification-dataset.ipynb)\n", " - [softmax-regression-scratch](softmax-regression-scratch.ipynb)\n", " - [softmax-regression-concise](softmax-regression-concise.ipynb)\n", ":end_tab:\n"]}], "metadata": {"language_info": {"name": "python"}}, "nbformat": 4, "nbformat_minor": 5}