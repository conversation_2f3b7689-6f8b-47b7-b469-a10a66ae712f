{"cells": [{"cell_type": "markdown", "id": "7f3985f6", "metadata": {"origin_pos": 0}, "source": ["# 长短期记忆网络（LSTM）\n", ":label:`sec_lstm`\n", "\n", "长期以来，隐变量模型存在着长期信息保存和短期输入缺失的问题。\n", "解决这一问题的最早方法之一是长短期存储器（long short-term memory，LSTM）\n", " :cite:`Hochreiter.Schmidhuber.1997`。\n", "它有许多与门控循环单元（ :numref:`sec_gru`）一样的属性。\n", "有趣的是，长短期记忆网络的设计比门控循环单元稍微复杂一些，\n", "却比门控循环单元早诞生了近20年。\n", "\n", "## 门控记忆元\n", "\n", "可以说，长短期记忆网络的设计灵感来自于计算机的逻辑门。\n", "长短期记忆网络引入了*记忆元*（memory cell），或简称为*单元*（cell）。\n", "有些文献认为记忆元是隐状态的一种特殊类型，\n", "它们与隐状态具有相同的形状，其设计目的是用于记录附加的信息。\n", "为了控制记忆元，我们需要许多门。\n", "其中一个门用来从单元中输出条目，我们将其称为*输出门*（output gate）。\n", "另外一个门用来决定何时将数据读入单元，我们将其称为*输入门*（input gate）。\n", "我们还需要一种机制来重置单元的内容，由*遗忘门*（forget gate）来管理，\n", "这种设计的动机与门控循环单元相同，\n", "能够通过专用机制决定什么时候记忆或忽略隐状态中的输入。\n", "让我们看看这在实践中是如何运作的。\n", "\n", "### 输入门、忘记门和输出门\n", "\n", "就如在门控循环单元中一样，\n", "当前时间步的输入和前一个时间步的隐状态\n", "作为数据送入长短期记忆网络的门中，\n", "如 :numref:`lstm_0`所示。\n", "它们由三个具有sigmoid激活函数的全连接层处理，\n", "以计算输入门、遗忘门和输出门的值。\n", "因此，这三个门的值都在$(0, 1)$的范围内。\n", "\n", "![长短期记忆模型中的输入门、遗忘门和输出门](../img/lstm-0.svg)\n", ":label:`lstm_0`\n", "\n", "我们来细化一下长短期记忆网络的数学表达。\n", "假设有$h$个隐藏单元，批量大小为$n$，输入数为$d$。\n", "因此，输入为$\\mathbf{X}_t \\in \\mathbb{R}^{n \\times d}$，\n", "前一时间步的隐状态为$\\mathbf{H}_{t-1} \\in \\mathbb{R}^{n \\times h}$。\n", "相应地，时间步$t$的门被定义如下：\n", "输入门是$\\mathbf{I}_t \\in \\mathbb{R}^{n \\times h}$，\n", "遗忘门是$\\mathbf{F}_t \\in \\mathbb{R}^{n \\times h}$，\n", "输出门是$\\mathbf{O}_t \\in \\mathbb{R}^{n \\times h}$。\n", "它们的计算方法如下：\n", "\n", "$$\n", "\\begin{aligned}\n", "\\mathbf{I}_t &= \\sigma(\\mathbf{X}_t \\mathbf{W}_{xi} + \\mathbf{H}_{t-1} \\mathbf{W}_{hi} + \\mathbf{b}_i),\\\\\n", "\\mathbf{F}_t &= \\sigma(\\mathbf{X}_t \\mathbf{W}_{xf} + \\mathbf{H}_{t-1} \\mathbf{W}_{hf} + \\mathbf{b}_f),\\\\\n", "\\mathbf{O}_t &= \\sigma(\\mathbf{X}_t \\mathbf{W}_{xo} + \\mathbf{H}_{t-1} \\mathbf{W}_{ho} + \\mathbf{b}_o),\n", "\\end{aligned}\n", "$$\n", "\n", "其中$\\mathbf{W}_{xi}, \\mathbf{W}_{xf}, \\mathbf{W}_{xo} \\in \\mathbb{R}^{d \\times h}$\n", "和$\\mathbf{W}_{hi}, \\mathbf{W}_{hf}, \\mathbf{W}_{ho} \\in \\mathbb{R}^{h \\times h}$是权重参数，\n", "$\\mathbf{b}_i, \\mathbf{b}_f, \\mathbf{b}_o \\in \\mathbb{R}^{1 \\times h}$是偏置参数。\n", "\n", "### 候选记忆元\n", "\n", "由于还没有指定各种门的操作，所以先介绍*候选记忆元*（candidate memory cell）\n", "$\\tilde{\\mathbf{C}}_t \\in \\mathbb{R}^{n \\times h}$。\n", "它的计算与上面描述的三个门的计算类似，\n", "但是使用$\\tanh$函数作为激活函数，函数的值范围为$(-1, 1)$。\n", "下面导出在时间步$t$处的方程：\n", "\n", "$$\\tilde{\\mathbf{C}}_t = \\text{tanh}(\\mathbf{X}_t \\mathbf{W}_{xc} + \\mathbf{H}_{t-1} \\mathbf{W}_{hc} + \\mathbf{b}_c),$$\n", "\n", "其中$\\mathbf{W}_{xc} \\in \\mathbb{R}^{d \\times h}$和\n", "$\\mathbf{W}_{hc} \\in \\mathbb{R}^{h \\times h}$是权重参数，\n", "$\\mathbf{b}_c \\in \\mathbb{R}^{1 \\times h}$是偏置参数。\n", "\n", "候选记忆元的如 :numref:`lstm_1`所示。\n", "\n", "![长短期记忆模型中的候选记忆元](../img/lstm-1.svg)\n", ":label:`lstm_1`\n", "\n", "### 记忆元\n", "\n", "在门控循环单元中，有一种机制来控制输入和遗忘（或跳过）。\n", "类似地，在长短期记忆网络中，也有两个门用于这样的目的：\n", "输入门$\\mathbf{I}_t$控制采用多少来自$\\tilde{\\mathbf{C}}_t$的新数据，\n", "而遗忘门$\\mathbf{F}_t$控制保留多少过去的\n", "记忆元$\\mathbf{C}_{t-1} \\in \\mathbb{R}^{n \\times h}$的内容。\n", "使用按元素乘法，得出：\n", "\n", "$$\\mathbf{C}_t = \\mathbf{F}_t \\odot \\mathbf{C}_{t-1} + \\mathbf{I}_t \\odot \\tilde{\\mathbf{C}}_t.$$\n", "\n", "如果遗忘门始终为$1$且输入门始终为$0$，\n", "则过去的记忆元$\\mathbf{C}_{t-1}$\n", "将随时间被保存并传递到当前时间步。\n", "引入这种设计是为了缓解梯度消失问题，\n", "并更好地捕获序列中的长距离依赖关系。\n", "\n", "这样我们就得到了计算记忆元的流程图，如 :numref:`lstm_2`。\n", "\n", "![在长短期记忆网络模型中计算记忆元](../img/lstm-2.svg)\n", "\n", ":label:`lstm_2`\n", "\n", "### 隐状态\n", "\n", "最后，我们需要定义如何计算隐状态\n", "$\\mathbf{H}_t \\in \\mathbb{R}^{n \\times h}$，\n", "这就是输出门发挥作用的地方。\n", "在长短期记忆网络中，它仅仅是记忆元的$\\tanh$的门控版本。\n", "这就确保了$\\mathbf{H}_t$的值始终在区间$(-1, 1)$内：\n", "\n", "$$\\mathbf{H}_t = \\mathbf{O}_t \\odot \\tanh(\\mathbf{C}_t).$$\n", "\n", "只要输出门接近$1$，我们就能够有效地将所有记忆信息传递给预测部分，\n", "而对于输出门接近$0$，我们只保留记忆元内的所有信息，而不需要更新隐状态。\n", "\n", " :numref:`lstm_3`提供了数据流的图形化演示。\n", "\n", "![在长短期记忆模型中计算隐状态](../img/lstm-3.svg)\n", ":label:`lstm_3`\n", "\n", "## 从零开始实现\n", "\n", "现在，我们从零开始实现长短期记忆网络。\n", "与 :numref:`sec_rnn_scratch`中的实验相同，\n", "我们首先加载时光机器数据集。\n"]}, {"cell_type": "code", "execution_count": 1, "id": "ee9fba32", "metadata": {"execution": {"iopub.execute_input": "2022-12-07T17:16:40.361506Z", "iopub.status.busy": "2022-12-07T17:16:40.360967Z", "iopub.status.idle": "2022-12-07T17:16:42.753573Z", "shell.execute_reply": "2022-12-07T17:16:42.752594Z"}, "origin_pos": 2, "tab": ["pytorch"]}, "outputs": [], "source": ["import torch\n", "from torch import nn\n", "from d2l import torch as d2l\n", "\n", "batch_size, num_steps = 32, 35\n", "train_iter, vocab = d2l.load_data_time_machine(batch_size, num_steps)"]}, {"cell_type": "markdown", "id": "4d35cc79", "metadata": {"origin_pos": 5}, "source": ["### [**初始化模型参数**]\n", "\n", "接下来，我们需要定义和初始化模型参数。\n", "如前所述，超参数`num_hiddens`定义隐藏单元的数量。\n", "我们按照标准差$0.01$的高斯分布初始化权重，并将偏置项设为$0$。\n"]}, {"cell_type": "code", "execution_count": 2, "id": "82e521ee", "metadata": {"execution": {"iopub.execute_input": "2022-12-07T17:16:42.757509Z", "iopub.status.busy": "2022-12-07T17:16:42.757148Z", "iopub.status.idle": "2022-12-07T17:16:42.764560Z", "shell.execute_reply": "2022-12-07T17:16:42.763708Z"}, "origin_pos": 7, "tab": ["pytorch"]}, "outputs": [], "source": ["def get_lstm_params(vocab_size, num_hiddens, device):\n", "    num_inputs = num_outputs = vocab_size\n", "\n", "    def normal(shape):\n", "        return torch.randn(size=shape, device=device)*0.01\n", "\n", "    def three():\n", "        return (normal((num_inputs, num_hiddens)),\n", "                normal((num_hiddens, num_hiddens)),\n", "                torch.zeros(num_hiddens, device=device))\n", "\n", "    W_xi, W_hi, b_i = three()  # 输入门参数\n", "    W_xf, W_hf, b_f = three()  # 遗忘门参数\n", "    W_xo, W_ho, b_o = three()  # 输出门参数\n", "    W_xc, W_hc, b_c = three()  # 候选记忆元参数\n", "    # 输出层参数\n", "    W_hq = normal((num_hiddens, num_outputs))\n", "    b_q = torch.zeros(num_outputs, device=device)\n", "    # 附加梯度\n", "    params = [W_xi, W_hi, b_i, W_xf, W_hf, b_f, W_xo, W_ho, b_o, W_xc, W_hc,\n", "              b_c, W_hq, b_q]\n", "    for param in params:\n", "        param.requires_grad_(True)\n", "    return params"]}, {"cell_type": "markdown", "id": "8608bacc", "metadata": {"origin_pos": 10}, "source": ["### 定义模型\n", "\n", "在[**初始化函数**]中，\n", "长短期记忆网络的隐状态需要返回一个*额外*的记忆元，\n", "单元的值为0，形状为（批量大小，隐藏单元数）。\n", "因此，我们得到以下的状态初始化。\n"]}, {"cell_type": "code", "execution_count": 3, "id": "435c535b", "metadata": {"execution": {"iopub.execute_input": "2022-12-07T17:16:42.767980Z", "iopub.status.busy": "2022-12-07T17:16:42.767349Z", "iopub.status.idle": "2022-12-07T17:16:42.771815Z", "shell.execute_reply": "2022-12-07T17:16:42.770931Z"}, "origin_pos": 12, "tab": ["pytorch"]}, "outputs": [], "source": ["def init_lstm_state(batch_size, num_hiddens, device):\n", "    return (torch.zeros((batch_size, num_hiddens), device=device),\n", "            torch.zeros((batch_size, num_hiddens), device=device))"]}, {"cell_type": "markdown", "id": "b7d47d12", "metadata": {"origin_pos": 15}, "source": ["[**实际模型**]的定义与我们前面讨论的一样：\n", "提供三个门和一个额外的记忆元。\n", "请注意，只有隐状态才会传递到输出层，\n", "而记忆元$\\mathbf{C}_t$不直接参与输出计算。\n"]}, {"cell_type": "code", "execution_count": 4, "id": "1f73e53f", "metadata": {"execution": {"iopub.execute_input": "2022-12-07T17:16:42.775125Z", "iopub.status.busy": "2022-12-07T17:16:42.774489Z", "iopub.status.idle": "2022-12-07T17:16:42.780886Z", "shell.execute_reply": "2022-12-07T17:16:42.780150Z"}, "origin_pos": 17, "tab": ["pytorch"]}, "outputs": [], "source": ["def lstm(inputs, state, params):\n", "    [W_xi, W_hi, b_i, W_xf, W_hf, b_f, W_xo, W_ho, b_o, W_xc, W_hc, b_c,\n", "     W_hq, b_q] = params\n", "    (H, C) = state\n", "    outputs = []\n", "    for X in inputs:\n", "        I = torch.sigmoid((X @ W_xi) + (H @ W_hi) + b_i)\n", "        F = torch.sigmoid((X @ W_xf) + (H @ W_hf) + b_f)\n", "        O = torch.sigmoid((X @ W_xo) + (H @ W_ho) + b_o)\n", "        C_tilda = torch.tanh((X @ W_xc) + (H @ W_hc) + b_c)\n", "        C = F * C + I * C_tilda\n", "        H = O * torch.tanh(C)\n", "        Y = (H @ W_hq) + b_q\n", "        outputs.append(Y)\n", "    return torch.cat(outputs, dim=0), (H, C)"]}, {"cell_type": "markdown", "id": "47d061ad", "metadata": {"origin_pos": 20}, "source": ["### [**训练**]和预测\n", "\n", "让我们通过实例化 :numref:`sec_rnn_scratch`中\n", "引入的`RNNModelScratch`类来训练一个长短期记忆网络，\n", "就如我们在 :numref:`sec_gru`中所做的一样。\n"]}, {"cell_type": "code", "execution_count": 5, "id": "612fd567", "metadata": {"execution": {"iopub.execute_input": "2022-12-07T17:16:42.784719Z", "iopub.status.busy": "2022-12-07T17:16:42.784200Z", "iopub.status.idle": "2022-12-07T17:20:12.678091Z", "shell.execute_reply": "2022-12-07T17:20:12.677296Z"}, "origin_pos": 21, "tab": ["pytorch"]}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["perplexity 1.4, 22462.5 tokens/sec on cuda:0\n", "time traveller for the brow henint it aneles a overrecured aback\n"]}, {"name": "stdout", "output_type": "stream", "text": ["travellerifilby freenotin s dof nous be and the filing and \n"]}, {"data": {"image/svg+xml": ["<?xml version=\"1.0\" encoding=\"utf-8\" standalone=\"no\"?>\n", "<!DOCTYPE svg PUBLIC \"-//W3C//DTD SVG 1.1//EN\"\n", "  \"http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd\">\n", "<svg xmlns:xlink=\"http://www.w3.org/1999/xlink\" width=\"252.646875pt\" height=\"183.35625pt\" viewBox=\"0 0 252.**********.35625\" xmlns=\"http://www.w3.org/2000/svg\" version=\"1.1\">\n", " <metadata>\n", "  <rdf:RDF xmlns:dc=\"http://purl.org/dc/elements/1.1/\" xmlns:cc=\"http://creativecommons.org/ns#\" xmlns:rdf=\"http://www.w3.org/1999/02/22-rdf-syntax-ns#\">\n", "   <cc:Work>\n", "    <dc:type rdf:resource=\"http://purl.org/dc/dcmitype/StillImage\"/>\n", "    <dc:date>2022-12-07T17:20:12.642806</dc:date>\n", "    <dc:format>image/svg+xml</dc:format>\n", "    <dc:creator>\n", "     <cc:Agent>\n", "      <dc:title>Matplotlib v3.5.1, https://matplotlib.org/</dc:title>\n", "     </cc:Agent>\n", "    </dc:creator>\n", "   </cc:Work>\n", "  </rdf:RDF>\n", " </metadata>\n", " <defs>\n", "  <style type=\"text/css\">*{stroke-linejoin: round; stroke-linecap: butt}</style>\n", " </defs>\n", " <g id=\"figure_1\">\n", "  <g id=\"patch_1\">\n", "   <path d=\"M 0 183.35625 \n", "L 252.**********.35625 \n", "L 252.646875 0 \n", "L 0 0 \n", "z\n", "\" style=\"fill: #ffffff\"/>\n", "  </g>\n", "  <g id=\"axes_1\">\n", "   <g id=\"patch_2\">\n", "    <path d=\"M 40.**********.8 \n", "L 235.**********.8 \n", "L 235.903125 7.2 \n", "L 40.603125 7.2 \n", "z\n", "\" style=\"fill: #ffffff\"/>\n", "   </g>\n", "   <g id=\"matplotlib.axis_1\">\n", "    <g id=\"xtick_1\">\n", "     <g id=\"line2d_1\">\n", "      <path d=\"M 76.474554 145.8 \n", "L 76.474554 7.2 \n", "\" clip-path=\"url(#pc9d68983d9)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_2\">\n", "      <defs>\n", "       <path id=\"m693a922c00\" d=\"M 0 0 \n", "L 0 3.5 \n", "\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </defs>\n", "      <g>\n", "       <use xlink:href=\"#m693a922c00\" x=\"76.474554\" y=\"145.8\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_1\">\n", "      <!-- 100 -->\n", "      <g transform=\"translate(66.930804 160.398438)scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-31\" d=\"M 794 531 \n", "L 1825 531 \n", "L 1825 4091 \n", "L 703 3866 \n", "L 703 4441 \n", "L 1819 4666 \n", "L 2450 4666 \n", "L 2450 531 \n", "L 3481 531 \n", "L 3481 0 \n", "L 794 0 \n", "L 794 531 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "        <path id=\"DejaVuSans-30\" d=\"M 2034 4250 \n", "Q 1547 4250 1301 3770 \n", "Q 1056 3291 1056 2328 \n", "Q 1056 1369 1301 889 \n", "Q 1547 409 2034 409 \n", "Q 2525 409 2770 889 \n", "Q 3016 1369 3016 2328 \n", "Q 3016 3291 2770 3770 \n", "Q 2525 4250 2034 4250 \n", "z\n", "M 2034 4750 \n", "Q 2819 4750 3233 4129 \n", "Q 3647 3509 3647 2328 \n", "Q 3647 1150 3233 529 \n", "Q 2819 -91 2034 -91 \n", "Q 1250 -91 836 529 \n", "Q 422 1150 422 2328 \n", "Q 422 3509 836 4129 \n", "Q 1250 4750 2034 4750 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-31\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"127.246094\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_2\">\n", "     <g id=\"line2d_3\">\n", "      <path d=\"M 116.331696 145.8 \n", "L 116.331696 7.2 \n", "\" clip-path=\"url(#pc9d68983d9)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_4\">\n", "      <g>\n", "       <use xlink:href=\"#m693a922c00\" x=\"116.331696\" y=\"145.8\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_2\">\n", "      <!-- 200 -->\n", "      <g transform=\"translate(106.787946 160.398438)scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-32\" d=\"M 1228 531 \n", "L 3431 531 \n", "L 3431 0 \n", "L 469 0 \n", "L 469 531 \n", "Q 828 903 1448 1529 \n", "Q 2069 2156 2228 2338 \n", "Q 2531 2678 2651 2914 \n", "Q 2772 3150 2772 3378 \n", "Q 2772 3750 2511 3984 \n", "Q 2250 4219 1831 4219 \n", "Q 1534 4219 1204 4116 \n", "Q 875 4013 500 3803 \n", "L 500 4441 \n", "Q 881 4594 1212 4672 \n", "Q 1544 4750 1819 4750 \n", "Q 2544 4750 2975 4387 \n", "Q 3406 4025 3406 3419 \n", "Q 3406 3131 3298 2873 \n", "Q 3191 2616 2906 2266 \n", "Q 2828 2175 2409 1742 \n", "Q 1991 1309 1228 531 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-32\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"127.246094\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_3\">\n", "     <g id=\"line2d_5\">\n", "      <path d=\"M 156.188839 145.8 \n", "L 156.188839 7.2 \n", "\" clip-path=\"url(#pc9d68983d9)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_6\">\n", "      <g>\n", "       <use xlink:href=\"#m693a922c00\" x=\"156.188839\" y=\"145.8\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_3\">\n", "      <!-- 300 -->\n", "      <g transform=\"translate(146.645089 160.398438)scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-33\" d=\"M 2597 2516 \n", "Q 3050 2419 3304 2112 \n", "Q 3559 1806 3559 1356 \n", "Q 3559 666 3084 287 \n", "Q 2609 -91 1734 -91 \n", "Q 1441 -91 1130 -33 \n", "Q 819 25 488 141 \n", "L 488 750 \n", "Q 750 597 1062 519 \n", "Q 1375 441 1716 441 \n", "Q 2309 441 2620 675 \n", "Q 2931 909 2931 1356 \n", "Q 2931 1769 2642 2001 \n", "Q 2353 2234 1838 2234 \n", "L 1294 2234 \n", "L 1294 2753 \n", "L 1863 2753 \n", "Q 2328 2753 2575 2939 \n", "Q 2822 3125 2822 3475 \n", "Q 2822 3834 2567 4026 \n", "Q 2313 4219 1838 4219 \n", "Q 1578 4219 1281 4162 \n", "Q 984 4106 628 3988 \n", "L 628 4550 \n", "Q 988 4650 1302 4700 \n", "Q 1616 4750 1894 4750 \n", "Q 2613 4750 3031 4423 \n", "Q 3450 4097 3450 3541 \n", "Q 3450 3153 3228 2886 \n", "Q 3006 2619 2597 2516 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-33\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"127.246094\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_4\">\n", "     <g id=\"line2d_7\">\n", "      <path d=\"M 196.045982 145.8 \n", "L 196.045982 7.2 \n", "\" clip-path=\"url(#pc9d68983d9)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_8\">\n", "      <g>\n", "       <use xlink:href=\"#m693a922c00\" x=\"196.045982\" y=\"145.8\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_4\">\n", "      <!-- 400 -->\n", "      <g transform=\"translate(186.502232 160.398438)scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-34\" d=\"M 2419 4116 \n", "L 825 1625 \n", "L 2419 1625 \n", "L 2419 4116 \n", "z\n", "M 2253 4666 \n", "L 3047 4666 \n", "L 3047 1625 \n", "L 3713 1625 \n", "L 3713 1100 \n", "L 3047 1100 \n", "L 3047 0 \n", "L 2419 0 \n", "L 2419 1100 \n", "L 313 1100 \n", "L 313 1709 \n", "L 2253 4666 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-34\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"127.246094\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_5\">\n", "     <g id=\"line2d_9\">\n", "      <path d=\"M 235.**********.8 \n", "L 235.903125 7.2 \n", "\" clip-path=\"url(#pc9d68983d9)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_10\">\n", "      <g>\n", "       <use xlink:href=\"#m693a922c00\" x=\"235.903125\" y=\"145.8\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_5\">\n", "      <!-- 500 -->\n", "      <g transform=\"translate(226.359375 160.398438)scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-35\" d=\"M 691 4666 \n", "L 3169 4666 \n", "L 3169 4134 \n", "L 1269 4134 \n", "L 1269 2991 \n", "Q 1406 3038 1543 3061 \n", "Q 1681 3084 1819 3084 \n", "Q 2600 3084 3056 2656 \n", "Q 3513 2228 3513 1497 \n", "Q 3513 744 3044 326 \n", "Q 2575 -91 1722 -91 \n", "Q 1428 -91 1123 -41 \n", "Q 819 9 494 109 \n", "L 494 744 \n", "Q 775 591 1075 516 \n", "Q 1375 441 1709 441 \n", "Q 2250 441 2565 725 \n", "Q 2881 1009 2881 1497 \n", "Q 2881 1984 2565 2268 \n", "Q 2250 2553 1709 2553 \n", "Q 1456 2553 1204 2497 \n", "Q 953 2441 691 2322 \n", "L 691 4666 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-35\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"127.246094\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"text_6\">\n", "     <!-- epoch -->\n", "     <g transform=\"translate(123.025 174.076563)scale(0.1 -0.1)\">\n", "      <defs>\n", "       <path id=\"DejaVuSans-65\" d=\"M 3597 1894 \n", "L 3597 1613 \n", "L 953 1613 \n", "Q 991 1019 1311 708 \n", "Q 1631 397 2203 397 \n", "Q 2534 397 2845 478 \n", "Q 3156 559 3463 722 \n", "L 3463 178 \n", "Q 3153 47 2828 -22 \n", "Q 2503 -91 2169 -91 \n", "Q 1331 -91 842 396 \n", "Q 353 884 353 1716 \n", "Q 353 2575 817 3079 \n", "Q 1281 3584 2069 3584 \n", "Q 2775 3584 3186 3129 \n", "Q 3597 2675 3597 1894 \n", "z\n", "M 3022 2063 \n", "Q 3016 2534 2758 2815 \n", "Q 2500 3097 2075 3097 \n", "Q 1594 3097 1305 2825 \n", "Q 1016 2553 972 2059 \n", "L 3022 2063 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-70\" d=\"M 1159 525 \n", "L 1159 -1331 \n", "L 581 -1331 \n", "L 581 3500 \n", "L 1159 3500 \n", "L 1159 2969 \n", "Q 1341 3281 1617 3432 \n", "Q 1894 3584 2278 3584 \n", "Q 2916 3584 3314 3078 \n", "Q 3713 2572 3713 1747 \n", "Q 3713 922 3314 415 \n", "Q 2916 -91 2278 -91 \n", "Q 1894 -91 1617 61 \n", "Q 1341 213 1159 525 \n", "z\n", "M 3116 1747 \n", "Q 3116 2381 2855 2742 \n", "Q 2594 3103 2138 3103 \n", "Q 1681 3103 1420 2742 \n", "Q 1159 2381 1159 1747 \n", "Q 1159 1113 1420 752 \n", "Q 1681 391 2138 391 \n", "Q 2594 391 2855 752 \n", "Q 3116 1113 3116 1747 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-6f\" d=\"M 1959 3097 \n", "Q 1497 3097 1228 2736 \n", "Q 959 2375 959 1747 \n", "Q 959 1119 1226 758 \n", "Q 1494 397 1959 397 \n", "Q 2419 397 2687 759 \n", "Q 2956 1122 2956 1747 \n", "Q 2956 2369 2687 2733 \n", "Q 2419 3097 1959 3097 \n", "z\n", "M 1959 3584 \n", "Q 2709 3584 3137 3096 \n", "Q 3566 2609 3566 1747 \n", "Q 3566 888 3137 398 \n", "Q 2709 -91 1959 -91 \n", "Q 1206 -91 779 398 \n", "Q 353 888 353 1747 \n", "Q 353 2609 779 3096 \n", "Q 1206 3584 1959 3584 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-63\" d=\"M 3122 3366 \n", "L 3122 2828 \n", "Q 2878 2963 2633 3030 \n", "Q 2388 3097 2138 3097 \n", "Q 1578 3097 1268 2742 \n", "Q 959 2388 959 1747 \n", "Q 959 1106 1268 751 \n", "Q 1578 397 2138 397 \n", "Q 2388 397 2633 464 \n", "Q 2878 531 3122 666 \n", "L 3122 134 \n", "Q 2881 22 2623 -34 \n", "Q 2366 -91 2075 -91 \n", "Q 1284 -91 818 406 \n", "Q 353 903 353 1747 \n", "Q 353 2603 823 3093 \n", "Q 1294 3584 2113 3584 \n", "Q 2378 3584 2631 3529 \n", "Q 2884 3475 3122 3366 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-68\" d=\"M 3513 2113 \n", "L 3513 0 \n", "L 2938 0 \n", "L 2938 2094 \n", "Q 2938 2591 2744 2837 \n", "Q 2550 3084 2163 3084 \n", "Q 1697 3084 1428 2787 \n", "Q 1159 2491 1159 1978 \n", "L 1159 0 \n", "L 581 0 \n", "L 581 4863 \n", "L 1159 4863 \n", "L 1159 2956 \n", "Q 1366 3272 1645 3428 \n", "Q 1925 3584 2291 3584 \n", "Q 2894 3584 3203 3211 \n", "Q 3513 2838 3513 2113 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "      </defs>\n", "      <use xlink:href=\"#DejaVuSans-65\"/>\n", "      <use xlink:href=\"#DejaVuSans-70\" x=\"61.523438\"/>\n", "      <use xlink:href=\"#DejaVuSans-6f\" x=\"125\"/>\n", "      <use xlink:href=\"#DejaVuSans-63\" x=\"186.181641\"/>\n", "      <use xlink:href=\"#DejaVuSans-68\" x=\"241.162109\"/>\n", "     </g>\n", "    </g>\n", "   </g>\n", "   <g id=\"matplotlib.axis_2\">\n", "    <g id=\"ytick_1\">\n", "     <g id=\"line2d_11\">\n", "      <path d=\"M 40.603125 110.675329 \n", "L 235.903125 110.675329 \n", "\" clip-path=\"url(#pc9d68983d9)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_12\">\n", "      <defs>\n", "       <path id=\"mc5327d611a\" d=\"M 0 0 \n", "L -3.5 0 \n", "\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </defs>\n", "      <g>\n", "       <use xlink:href=\"#mc5327d611a\" x=\"40.603125\" y=\"110.675329\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_7\">\n", "      <!-- 5 -->\n", "      <g transform=\"translate(27.240625 114.474547)scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-35\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_2\">\n", "     <g id=\"line2d_13\">\n", "      <path d=\"M 40.603125 73.050364 \n", "L 235.903125 73.050364 \n", "\" clip-path=\"url(#pc9d68983d9)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_14\">\n", "      <g>\n", "       <use xlink:href=\"#mc5327d611a\" x=\"40.603125\" y=\"73.050364\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_8\">\n", "      <!-- 10 -->\n", "      <g transform=\"translate(20.878125 76.849583)scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-31\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"63.623047\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_3\">\n", "     <g id=\"line2d_15\">\n", "      <path d=\"M 40.603125 35.425399 \n", "L 235.903125 35.425399 \n", "\" clip-path=\"url(#pc9d68983d9)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_16\">\n", "      <g>\n", "       <use xlink:href=\"#mc5327d611a\" x=\"40.603125\" y=\"35.425399\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_9\">\n", "      <!-- 15 -->\n", "      <g transform=\"translate(20.878125 39.224618)scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-31\"/>\n", "       <use xlink:href=\"#DejaVuSans-35\" x=\"63.623047\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"text_10\">\n", "     <!-- perplexity -->\n", "     <g transform=\"translate(14.798437 101.626563)rotate(-90)scale(0.1 -0.1)\">\n", "      <defs>\n", "       <path id=\"DejaVuSans-72\" d=\"M 2631 2963 \n", "Q 2534 3019 2420 3045 \n", "Q 2306 3072 2169 3072 \n", "Q 1681 3072 1420 2755 \n", "Q 1159 2438 1159 1844 \n", "L 1159 0 \n", "L 581 0 \n", "L 581 3500 \n", "L 1159 3500 \n", "L 1159 2956 \n", "Q 1341 3275 1631 3429 \n", "Q 1922 3584 2338 3584 \n", "Q 2397 3584 2469 3576 \n", "Q 2541 3569 2628 3553 \n", "L 2631 2963 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-6c\" d=\"M 603 4863 \n", "L 1178 4863 \n", "L 1178 0 \n", "L 603 0 \n", "L 603 4863 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-78\" d=\"M 3513 3500 \n", "L 2247 1797 \n", "L 3578 0 \n", "L 2900 0 \n", "L 1881 1375 \n", "L 863 0 \n", "L 184 0 \n", "L 1544 1831 \n", "L 300 3500 \n", "L 978 3500 \n", "L 1906 2253 \n", "L 2834 3500 \n", "L 3513 3500 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-69\" d=\"M 603 3500 \n", "L 1178 3500 \n", "L 1178 0 \n", "L 603 0 \n", "L 603 3500 \n", "z\n", "M 603 4863 \n", "L 1178 4863 \n", "L 1178 4134 \n", "L 603 4134 \n", "L 603 4863 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-74\" d=\"M 1172 4494 \n", "L 1172 3500 \n", "L 2356 3500 \n", "L 2356 3053 \n", "L 1172 3053 \n", "L 1172 1153 \n", "Q 1172 725 1289 603 \n", "Q 1406 481 1766 481 \n", "L 2356 481 \n", "L 2356 0 \n", "L 1766 0 \n", "Q 1100 0 847 248 \n", "Q 594 497 594 1153 \n", "L 594 3053 \n", "L 172 3053 \n", "L 172 3500 \n", "L 594 3500 \n", "L 594 4494 \n", "L 1172 4494 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-79\" d=\"M 2059 -325 \n", "Q 1816 -950 1584 -1140 \n", "Q 1353 -1331 966 -1331 \n", "L 506 -1331 \n", "L 506 -850 \n", "L 844 -850 \n", "Q 1081 -850 1212 -737 \n", "Q 1344 -625 1503 -206 \n", "L 1606 56 \n", "L 191 3500 \n", "L 800 3500 \n", "L 1894 763 \n", "L 2988 3500 \n", "L 3597 3500 \n", "L 2059 -325 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "      </defs>\n", "      <use xlink:href=\"#DejaVuSans-70\"/>\n", "      <use xlink:href=\"#DejaVuSans-65\" x=\"63.476562\"/>\n", "      <use xlink:href=\"#DejaVuSans-72\" x=\"125\"/>\n", "      <use xlink:href=\"#DejaVuSans-70\" x=\"166.113281\"/>\n", "      <use xlink:href=\"#DejaVuSans-6c\" x=\"229.589844\"/>\n", "      <use xlink:href=\"#DejaVuSans-65\" x=\"257.373047\"/>\n", "      <use xlink:href=\"#DejaVuSans-78\" x=\"317.146484\"/>\n", "      <use xlink:href=\"#DejaVuSans-69\" x=\"376.326172\"/>\n", "      <use xlink:href=\"#DejaVuSans-74\" x=\"404.109375\"/>\n", "      <use xlink:href=\"#DejaVuSans-79\" x=\"443.318359\"/>\n", "     </g>\n", "    </g>\n", "   </g>\n", "   <g id=\"line2d_17\">\n", "    <path d=\"M 40.603125 13.5 \n", "L 44.588839 17.22558 \n", "L 48.574554 22.446223 \n", "L 52.560268 30.186492 \n", "L 56.545982 39.297144 \n", "L 60.531696 52.00113 \n", "L 64.517411 59.374912 \n", "L 68.503125 62.35067 \n", "L 72.488839 67.229289 \n", "L 76.474554 69.534547 \n", "L 80.460268 71.68089 \n", "L 84.445982 74.31173 \n", "L 88.431696 76.056872 \n", "L 92.417411 78.287057 \n", "L 96.403125 81.957065 \n", "L 100.388839 84.09248 \n", "L 104.374554 85.839346 \n", "L 108.360268 89.351841 \n", "L 112.345982 91.722564 \n", "L 116.331696 93.36851 \n", "L 120.317411 95.819916 \n", "L 124.303125 97.623527 \n", "L 128.288839 99.623085 \n", "L 132.274554 101.479925 \n", "L 136.260268 103.088393 \n", "L 140.245982 105.472264 \n", "L 144.231696 108.229064 \n", "L 148.217411 110.041542 \n", "L 152.203125 111.387465 \n", "L 156.188839 112.768781 \n", "L 160.174554 115.447639 \n", "L 164.160268 118.000587 \n", "L 168.145982 120.17282 \n", "L 172.131696 121.507541 \n", "L 176.117411 124.394931 \n", "L 180.103125 125.454757 \n", "L 184.088839 128.108906 \n", "L 188.074554 129.724969 \n", "L 192.060268 130.669265 \n", "L 196.045982 132.538503 \n", "L 200.031696 134.160931 \n", "L 204.017411 134.572828 \n", "L 208.003125 135.68532 \n", "L 211.988839 137.001546 \n", "L 215.974554 137.943494 \n", "L 219.960268 137.912622 \n", "L 223.945982 138.910147 \n", "L 227.931696 138.277653 \n", "L 231.917411 139.5 \n", "L 235.903125 138.121902 \n", "\" clip-path=\"url(#pc9d68983d9)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_3\">\n", "    <path d=\"M 40.**********.8 \n", "L 40.603125 7.2 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_4\">\n", "    <path d=\"M 235.**********.8 \n", "L 235.903125 7.2 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_5\">\n", "    <path d=\"M 40.**********.8 \n", "L 235.**********.8 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_6\">\n", "    <path d=\"M 40.603125 7.2 \n", "L 235.903125 7.2 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"legend_1\">\n", "    <g id=\"patch_7\">\n", "     <path d=\"M 173.628125 29.878125 \n", "L 228.903125 29.878125 \n", "Q 230.903125 29.878125 230.903125 27.878125 \n", "L 230.903125 14.2 \n", "Q 230.903125 12.2 228.903125 12.2 \n", "L 173.628125 12.2 \n", "Q 171.628125 12.2 171.628125 14.2 \n", "L 171.628125 27.878125 \n", "Q 171.628125 29.878125 173.628125 29.878125 \n", "z\n", "\" style=\"fill: #ffffff; opacity: 0.8; stroke: #cccccc; stroke-linejoin: miter\"/>\n", "    </g>\n", "    <g id=\"line2d_18\">\n", "     <path d=\"M 175.628125 20.298438 \n", "L 185.628125 20.298438 \n", "L 195.628125 20.298438 \n", "\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "    </g>\n", "    <g id=\"text_11\">\n", "     <!-- train -->\n", "     <g transform=\"translate(203.628125 23.798438)scale(0.1 -0.1)\">\n", "      <defs>\n", "       <path id=\"DejaVuSans-61\" d=\"M 2194 1759 \n", "Q 1497 1759 1228 1600 \n", "Q 959 1441 959 1056 \n", "Q 959 750 1161 570 \n", "Q 1363 391 1709 391 \n", "Q 2188 391 2477 730 \n", "Q 2766 1069 2766 1631 \n", "L 2766 1759 \n", "L 2194 1759 \n", "z\n", "M 3341 1997 \n", "L 3341 0 \n", "L 2766 0 \n", "L 2766 531 \n", "Q 2569 213 2275 61 \n", "Q 1981 -91 1556 -91 \n", "Q 1019 -91 701 211 \n", "Q 384 513 384 1019 \n", "Q 384 1609 779 1909 \n", "Q 1175 2209 1959 2209 \n", "L 2766 2209 \n", "L 2766 2266 \n", "Q 2766 2663 2505 2880 \n", "Q 2244 3097 1772 3097 \n", "Q 1472 3097 1187 3025 \n", "Q 903 2953 641 2809 \n", "L 641 3341 \n", "Q 956 3463 1253 3523 \n", "Q 1550 3584 1831 3584 \n", "Q 2591 3584 2966 3190 \n", "Q 3341 2797 3341 1997 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-6e\" d=\"M 3513 2113 \n", "L 3513 0 \n", "L 2938 0 \n", "L 2938 2094 \n", "Q 2938 2591 2744 2837 \n", "Q 2550 3084 2163 3084 \n", "Q 1697 3084 1428 2787 \n", "Q 1159 2491 1159 1978 \n", "L 1159 0 \n", "L 581 0 \n", "L 581 3500 \n", "L 1159 3500 \n", "L 1159 2956 \n", "Q 1366 3272 1645 3428 \n", "Q 1925 3584 2291 3584 \n", "Q 2894 3584 3203 3211 \n", "Q 3513 2838 3513 2113 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "      </defs>\n", "      <use xlink:href=\"#DejaVuSans-74\"/>\n", "      <use xlink:href=\"#DejaVuSans-72\" x=\"39.208984\"/>\n", "      <use xlink:href=\"#DejaVuSans-61\" x=\"80.322266\"/>\n", "      <use xlink:href=\"#DejaVuSans-69\" x=\"141.601562\"/>\n", "      <use xlink:href=\"#DejaVuSans-6e\" x=\"169.384766\"/>\n", "     </g>\n", "    </g>\n", "   </g>\n", "  </g>\n", " </g>\n", " <defs>\n", "  <clipPath id=\"pc9d68983d9\">\n", "   <rect x=\"40.603125\" y=\"7.2\" width=\"195.3\" height=\"138.6\"/>\n", "  </clipPath>\n", " </defs>\n", "</svg>\n"], "text/plain": ["<Figure size 350x250 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["vocab_size, num_hiddens, device = len(vocab), 256, d2l.try_gpu()\n", "num_epochs, lr = 500, 1\n", "model = d2l.RNNModelScratch(len(vocab), num_hiddens, device, get_lstm_params,\n", "                            init_lstm_state, lstm)\n", "d2l.train_ch8(model, train_iter, vocab, lr, num_epochs, device)"]}, {"cell_type": "markdown", "id": "2216f38e", "metadata": {"origin_pos": 24}, "source": ["## [**简洁实现**]\n", "\n", "使用高级API，我们可以直接实例化`LSTM`模型。\n", "高级API封装了前文介绍的所有配置细节。\n", "这段代码的运行速度要快得多，\n", "因为它使用的是编译好的运算符而不是Python来处理之前阐述的许多细节。\n"]}, {"cell_type": "code", "execution_count": 6, "id": "33b87e33", "metadata": {"execution": {"iopub.execute_input": "2022-12-07T17:20:12.683654Z", "iopub.status.busy": "2022-12-07T17:20:12.682838Z", "iopub.status.idle": "2022-12-07T17:20:35.140971Z", "shell.execute_reply": "2022-12-07T17:20:35.140062Z"}, "origin_pos": 26, "tab": ["pytorch"]}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["perplexity 1.1, 330289.2 tokens/sec on cuda:0\n", "time travelleryou can show black is white by argument said filby\n", "travelleryou can show black is white by argument said filby\n"]}, {"data": {"image/svg+xml": ["<?xml version=\"1.0\" encoding=\"utf-8\" standalone=\"no\"?>\n", "<!DOCTYPE svg PUBLIC \"-//W3C//DTD SVG 1.1//EN\"\n", "  \"http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd\">\n", "<svg xmlns:xlink=\"http://www.w3.org/1999/xlink\" width=\"252.646875pt\" height=\"183.35625pt\" viewBox=\"0 0 252.**********.35625\" xmlns=\"http://www.w3.org/2000/svg\" version=\"1.1\">\n", " <metadata>\n", "  <rdf:RDF xmlns:dc=\"http://purl.org/dc/elements/1.1/\" xmlns:cc=\"http://creativecommons.org/ns#\" xmlns:rdf=\"http://www.w3.org/1999/02/22-rdf-syntax-ns#\">\n", "   <cc:Work>\n", "    <dc:type rdf:resource=\"http://purl.org/dc/dcmitype/StillImage\"/>\n", "    <dc:date>2022-12-07T17:20:35.107823</dc:date>\n", "    <dc:format>image/svg+xml</dc:format>\n", "    <dc:creator>\n", "     <cc:Agent>\n", "      <dc:title>Matplotlib v3.5.1, https://matplotlib.org/</dc:title>\n", "     </cc:Agent>\n", "    </dc:creator>\n", "   </cc:Work>\n", "  </rdf:RDF>\n", " </metadata>\n", " <defs>\n", "  <style type=\"text/css\">*{stroke-linejoin: round; stroke-linecap: butt}</style>\n", " </defs>\n", " <g id=\"figure_1\">\n", "  <g id=\"patch_1\">\n", "   <path d=\"M 0 183.35625 \n", "L 252.**********.35625 \n", "L 252.646875 0 \n", "L 0 0 \n", "z\n", "\" style=\"fill: #ffffff\"/>\n", "  </g>\n", "  <g id=\"axes_1\">\n", "   <g id=\"patch_2\">\n", "    <path d=\"M 40.**********.8 \n", "L 235.**********.8 \n", "L 235.903125 7.2 \n", "L 40.603125 7.2 \n", "z\n", "\" style=\"fill: #ffffff\"/>\n", "   </g>\n", "   <g id=\"matplotlib.axis_1\">\n", "    <g id=\"xtick_1\">\n", "     <g id=\"line2d_1\">\n", "      <path d=\"M 76.474554 145.8 \n", "L 76.474554 7.2 \n", "\" clip-path=\"url(#pb0f0cf0716)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_2\">\n", "      <defs>\n", "       <path id=\"m75e5913cb7\" d=\"M 0 0 \n", "L 0 3.5 \n", "\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </defs>\n", "      <g>\n", "       <use xlink:href=\"#m75e5913cb7\" x=\"76.474554\" y=\"145.8\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_1\">\n", "      <!-- 100 -->\n", "      <g transform=\"translate(66.930804 160.398438)scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-31\" d=\"M 794 531 \n", "L 1825 531 \n", "L 1825 4091 \n", "L 703 3866 \n", "L 703 4441 \n", "L 1819 4666 \n", "L 2450 4666 \n", "L 2450 531 \n", "L 3481 531 \n", "L 3481 0 \n", "L 794 0 \n", "L 794 531 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "        <path id=\"DejaVuSans-30\" d=\"M 2034 4250 \n", "Q 1547 4250 1301 3770 \n", "Q 1056 3291 1056 2328 \n", "Q 1056 1369 1301 889 \n", "Q 1547 409 2034 409 \n", "Q 2525 409 2770 889 \n", "Q 3016 1369 3016 2328 \n", "Q 3016 3291 2770 3770 \n", "Q 2525 4250 2034 4250 \n", "z\n", "M 2034 4750 \n", "Q 2819 4750 3233 4129 \n", "Q 3647 3509 3647 2328 \n", "Q 3647 1150 3233 529 \n", "Q 2819 -91 2034 -91 \n", "Q 1250 -91 836 529 \n", "Q 422 1150 422 2328 \n", "Q 422 3509 836 4129 \n", "Q 1250 4750 2034 4750 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-31\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"127.246094\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_2\">\n", "     <g id=\"line2d_3\">\n", "      <path d=\"M 116.331696 145.8 \n", "L 116.331696 7.2 \n", "\" clip-path=\"url(#pb0f0cf0716)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_4\">\n", "      <g>\n", "       <use xlink:href=\"#m75e5913cb7\" x=\"116.331696\" y=\"145.8\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_2\">\n", "      <!-- 200 -->\n", "      <g transform=\"translate(106.787946 160.398438)scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-32\" d=\"M 1228 531 \n", "L 3431 531 \n", "L 3431 0 \n", "L 469 0 \n", "L 469 531 \n", "Q 828 903 1448 1529 \n", "Q 2069 2156 2228 2338 \n", "Q 2531 2678 2651 2914 \n", "Q 2772 3150 2772 3378 \n", "Q 2772 3750 2511 3984 \n", "Q 2250 4219 1831 4219 \n", "Q 1534 4219 1204 4116 \n", "Q 875 4013 500 3803 \n", "L 500 4441 \n", "Q 881 4594 1212 4672 \n", "Q 1544 4750 1819 4750 \n", "Q 2544 4750 2975 4387 \n", "Q 3406 4025 3406 3419 \n", "Q 3406 3131 3298 2873 \n", "Q 3191 2616 2906 2266 \n", "Q 2828 2175 2409 1742 \n", "Q 1991 1309 1228 531 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-32\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"127.246094\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_3\">\n", "     <g id=\"line2d_5\">\n", "      <path d=\"M 156.188839 145.8 \n", "L 156.188839 7.2 \n", "\" clip-path=\"url(#pb0f0cf0716)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_6\">\n", "      <g>\n", "       <use xlink:href=\"#m75e5913cb7\" x=\"156.188839\" y=\"145.8\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_3\">\n", "      <!-- 300 -->\n", "      <g transform=\"translate(146.645089 160.398438)scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-33\" d=\"M 2597 2516 \n", "Q 3050 2419 3304 2112 \n", "Q 3559 1806 3559 1356 \n", "Q 3559 666 3084 287 \n", "Q 2609 -91 1734 -91 \n", "Q 1441 -91 1130 -33 \n", "Q 819 25 488 141 \n", "L 488 750 \n", "Q 750 597 1062 519 \n", "Q 1375 441 1716 441 \n", "Q 2309 441 2620 675 \n", "Q 2931 909 2931 1356 \n", "Q 2931 1769 2642 2001 \n", "Q 2353 2234 1838 2234 \n", "L 1294 2234 \n", "L 1294 2753 \n", "L 1863 2753 \n", "Q 2328 2753 2575 2939 \n", "Q 2822 3125 2822 3475 \n", "Q 2822 3834 2567 4026 \n", "Q 2313 4219 1838 4219 \n", "Q 1578 4219 1281 4162 \n", "Q 984 4106 628 3988 \n", "L 628 4550 \n", "Q 988 4650 1302 4700 \n", "Q 1616 4750 1894 4750 \n", "Q 2613 4750 3031 4423 \n", "Q 3450 4097 3450 3541 \n", "Q 3450 3153 3228 2886 \n", "Q 3006 2619 2597 2516 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-33\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"127.246094\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_4\">\n", "     <g id=\"line2d_7\">\n", "      <path d=\"M 196.045982 145.8 \n", "L 196.045982 7.2 \n", "\" clip-path=\"url(#pb0f0cf0716)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_8\">\n", "      <g>\n", "       <use xlink:href=\"#m75e5913cb7\" x=\"196.045982\" y=\"145.8\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_4\">\n", "      <!-- 400 -->\n", "      <g transform=\"translate(186.502232 160.398438)scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-34\" d=\"M 2419 4116 \n", "L 825 1625 \n", "L 2419 1625 \n", "L 2419 4116 \n", "z\n", "M 2253 4666 \n", "L 3047 4666 \n", "L 3047 1625 \n", "L 3713 1625 \n", "L 3713 1100 \n", "L 3047 1100 \n", "L 3047 0 \n", "L 2419 0 \n", "L 2419 1100 \n", "L 313 1100 \n", "L 313 1709 \n", "L 2253 4666 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-34\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"127.246094\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_5\">\n", "     <g id=\"line2d_9\">\n", "      <path d=\"M 235.**********.8 \n", "L 235.903125 7.2 \n", "\" clip-path=\"url(#pb0f0cf0716)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_10\">\n", "      <g>\n", "       <use xlink:href=\"#m75e5913cb7\" x=\"235.903125\" y=\"145.8\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_5\">\n", "      <!-- 500 -->\n", "      <g transform=\"translate(226.359375 160.398438)scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-35\" d=\"M 691 4666 \n", "L 3169 4666 \n", "L 3169 4134 \n", "L 1269 4134 \n", "L 1269 2991 \n", "Q 1406 3038 1543 3061 \n", "Q 1681 3084 1819 3084 \n", "Q 2600 3084 3056 2656 \n", "Q 3513 2228 3513 1497 \n", "Q 3513 744 3044 326 \n", "Q 2575 -91 1722 -91 \n", "Q 1428 -91 1123 -41 \n", "Q 819 9 494 109 \n", "L 494 744 \n", "Q 775 591 1075 516 \n", "Q 1375 441 1709 441 \n", "Q 2250 441 2565 725 \n", "Q 2881 1009 2881 1497 \n", "Q 2881 1984 2565 2268 \n", "Q 2250 2553 1709 2553 \n", "Q 1456 2553 1204 2497 \n", "Q 953 2441 691 2322 \n", "L 691 4666 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-35\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"127.246094\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"text_6\">\n", "     <!-- epoch -->\n", "     <g transform=\"translate(123.025 174.076563)scale(0.1 -0.1)\">\n", "      <defs>\n", "       <path id=\"DejaVuSans-65\" d=\"M 3597 1894 \n", "L 3597 1613 \n", "L 953 1613 \n", "Q 991 1019 1311 708 \n", "Q 1631 397 2203 397 \n", "Q 2534 397 2845 478 \n", "Q 3156 559 3463 722 \n", "L 3463 178 \n", "Q 3153 47 2828 -22 \n", "Q 2503 -91 2169 -91 \n", "Q 1331 -91 842 396 \n", "Q 353 884 353 1716 \n", "Q 353 2575 817 3079 \n", "Q 1281 3584 2069 3584 \n", "Q 2775 3584 3186 3129 \n", "Q 3597 2675 3597 1894 \n", "z\n", "M 3022 2063 \n", "Q 3016 2534 2758 2815 \n", "Q 2500 3097 2075 3097 \n", "Q 1594 3097 1305 2825 \n", "Q 1016 2553 972 2059 \n", "L 3022 2063 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-70\" d=\"M 1159 525 \n", "L 1159 -1331 \n", "L 581 -1331 \n", "L 581 3500 \n", "L 1159 3500 \n", "L 1159 2969 \n", "Q 1341 3281 1617 3432 \n", "Q 1894 3584 2278 3584 \n", "Q 2916 3584 3314 3078 \n", "Q 3713 2572 3713 1747 \n", "Q 3713 922 3314 415 \n", "Q 2916 -91 2278 -91 \n", "Q 1894 -91 1617 61 \n", "Q 1341 213 1159 525 \n", "z\n", "M 3116 1747 \n", "Q 3116 2381 2855 2742 \n", "Q 2594 3103 2138 3103 \n", "Q 1681 3103 1420 2742 \n", "Q 1159 2381 1159 1747 \n", "Q 1159 1113 1420 752 \n", "Q 1681 391 2138 391 \n", "Q 2594 391 2855 752 \n", "Q 3116 1113 3116 1747 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-6f\" d=\"M 1959 3097 \n", "Q 1497 3097 1228 2736 \n", "Q 959 2375 959 1747 \n", "Q 959 1119 1226 758 \n", "Q 1494 397 1959 397 \n", "Q 2419 397 2687 759 \n", "Q 2956 1122 2956 1747 \n", "Q 2956 2369 2687 2733 \n", "Q 2419 3097 1959 3097 \n", "z\n", "M 1959 3584 \n", "Q 2709 3584 3137 3096 \n", "Q 3566 2609 3566 1747 \n", "Q 3566 888 3137 398 \n", "Q 2709 -91 1959 -91 \n", "Q 1206 -91 779 398 \n", "Q 353 888 353 1747 \n", "Q 353 2609 779 3096 \n", "Q 1206 3584 1959 3584 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-63\" d=\"M 3122 3366 \n", "L 3122 2828 \n", "Q 2878 2963 2633 3030 \n", "Q 2388 3097 2138 3097 \n", "Q 1578 3097 1268 2742 \n", "Q 959 2388 959 1747 \n", "Q 959 1106 1268 751 \n", "Q 1578 397 2138 397 \n", "Q 2388 397 2633 464 \n", "Q 2878 531 3122 666 \n", "L 3122 134 \n", "Q 2881 22 2623 -34 \n", "Q 2366 -91 2075 -91 \n", "Q 1284 -91 818 406 \n", "Q 353 903 353 1747 \n", "Q 353 2603 823 3093 \n", "Q 1294 3584 2113 3584 \n", "Q 2378 3584 2631 3529 \n", "Q 2884 3475 3122 3366 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-68\" d=\"M 3513 2113 \n", "L 3513 0 \n", "L 2938 0 \n", "L 2938 2094 \n", "Q 2938 2591 2744 2837 \n", "Q 2550 3084 2163 3084 \n", "Q 1697 3084 1428 2787 \n", "Q 1159 2491 1159 1978 \n", "L 1159 0 \n", "L 581 0 \n", "L 581 4863 \n", "L 1159 4863 \n", "L 1159 2956 \n", "Q 1366 3272 1645 3428 \n", "Q 1925 3584 2291 3584 \n", "Q 2894 3584 3203 3211 \n", "Q 3513 2838 3513 2113 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "      </defs>\n", "      <use xlink:href=\"#DejaVuSans-65\"/>\n", "      <use xlink:href=\"#DejaVuSans-70\" x=\"61.523438\"/>\n", "      <use xlink:href=\"#DejaVuSans-6f\" x=\"125\"/>\n", "      <use xlink:href=\"#DejaVuSans-63\" x=\"186.181641\"/>\n", "      <use xlink:href=\"#DejaVuSans-68\" x=\"241.162109\"/>\n", "     </g>\n", "    </g>\n", "   </g>\n", "   <g id=\"matplotlib.axis_2\">\n", "    <g id=\"ytick_1\">\n", "     <g id=\"line2d_11\">\n", "      <path d=\"M 40.603125 108.529687 \n", "L 235.903125 108.529687 \n", "\" clip-path=\"url(#pb0f0cf0716)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_12\">\n", "      <defs>\n", "       <path id=\"m617535db7b\" d=\"M 0 0 \n", "L -3.5 0 \n", "\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </defs>\n", "      <g>\n", "       <use xlink:href=\"#m617535db7b\" x=\"40.603125\" y=\"108.529687\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_7\">\n", "      <!-- 5 -->\n", "      <g transform=\"translate(27.240625 112.328905)scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-35\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_2\">\n", "     <g id=\"line2d_13\">\n", "      <path d=\"M 40.603125 69.390286 \n", "L 235.903125 69.390286 \n", "\" clip-path=\"url(#pb0f0cf0716)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_14\">\n", "      <g>\n", "       <use xlink:href=\"#m617535db7b\" x=\"40.603125\" y=\"69.390286\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_8\">\n", "      <!-- 10 -->\n", "      <g transform=\"translate(20.878125 73.189505)scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-31\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"63.623047\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_3\">\n", "     <g id=\"line2d_15\">\n", "      <path d=\"M 40.603125 30.250885 \n", "L 235.903125 30.250885 \n", "\" clip-path=\"url(#pb0f0cf0716)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_16\">\n", "      <g>\n", "       <use xlink:href=\"#m617535db7b\" x=\"40.603125\" y=\"30.250885\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_9\">\n", "      <!-- 15 -->\n", "      <g transform=\"translate(20.878125 34.050104)scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-31\"/>\n", "       <use xlink:href=\"#DejaVuSans-35\" x=\"63.623047\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"text_10\">\n", "     <!-- perplexity -->\n", "     <g transform=\"translate(14.798437 101.626563)rotate(-90)scale(0.1 -0.1)\">\n", "      <defs>\n", "       <path id=\"DejaVuSans-72\" d=\"M 2631 2963 \n", "Q 2534 3019 2420 3045 \n", "Q 2306 3072 2169 3072 \n", "Q 1681 3072 1420 2755 \n", "Q 1159 2438 1159 1844 \n", "L 1159 0 \n", "L 581 0 \n", "L 581 3500 \n", "L 1159 3500 \n", "L 1159 2956 \n", "Q 1341 3275 1631 3429 \n", "Q 1922 3584 2338 3584 \n", "Q 2397 3584 2469 3576 \n", "Q 2541 3569 2628 3553 \n", "L 2631 2963 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-6c\" d=\"M 603 4863 \n", "L 1178 4863 \n", "L 1178 0 \n", "L 603 0 \n", "L 603 4863 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-78\" d=\"M 3513 3500 \n", "L 2247 1797 \n", "L 3578 0 \n", "L 2900 0 \n", "L 1881 1375 \n", "L 863 0 \n", "L 184 0 \n", "L 1544 1831 \n", "L 300 3500 \n", "L 978 3500 \n", "L 1906 2253 \n", "L 2834 3500 \n", "L 3513 3500 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-69\" d=\"M 603 3500 \n", "L 1178 3500 \n", "L 1178 0 \n", "L 603 0 \n", "L 603 3500 \n", "z\n", "M 603 4863 \n", "L 1178 4863 \n", "L 1178 4134 \n", "L 603 4134 \n", "L 603 4863 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-74\" d=\"M 1172 4494 \n", "L 1172 3500 \n", "L 2356 3500 \n", "L 2356 3053 \n", "L 1172 3053 \n", "L 1172 1153 \n", "Q 1172 725 1289 603 \n", "Q 1406 481 1766 481 \n", "L 2356 481 \n", "L 2356 0 \n", "L 1766 0 \n", "Q 1100 0 847 248 \n", "Q 594 497 594 1153 \n", "L 594 3053 \n", "L 172 3053 \n", "L 172 3500 \n", "L 594 3500 \n", "L 594 4494 \n", "L 1172 4494 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-79\" d=\"M 2059 -325 \n", "Q 1816 -950 1584 -1140 \n", "Q 1353 -1331 966 -1331 \n", "L 506 -1331 \n", "L 506 -850 \n", "L 844 -850 \n", "Q 1081 -850 1212 -737 \n", "Q 1344 -625 1503 -206 \n", "L 1606 56 \n", "L 191 3500 \n", "L 800 3500 \n", "L 1894 763 \n", "L 2988 3500 \n", "L 3597 3500 \n", "L 2059 -325 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "      </defs>\n", "      <use xlink:href=\"#DejaVuSans-70\"/>\n", "      <use xlink:href=\"#DejaVuSans-65\" x=\"63.476562\"/>\n", "      <use xlink:href=\"#DejaVuSans-72\" x=\"125\"/>\n", "      <use xlink:href=\"#DejaVuSans-70\" x=\"166.113281\"/>\n", "      <use xlink:href=\"#DejaVuSans-6c\" x=\"229.589844\"/>\n", "      <use xlink:href=\"#DejaVuSans-65\" x=\"257.373047\"/>\n", "      <use xlink:href=\"#DejaVuSans-78\" x=\"317.146484\"/>\n", "      <use xlink:href=\"#DejaVuSans-69\" x=\"376.326172\"/>\n", "      <use xlink:href=\"#DejaVuSans-74\" x=\"404.109375\"/>\n", "      <use xlink:href=\"#DejaVuSans-79\" x=\"443.318359\"/>\n", "     </g>\n", "    </g>\n", "   </g>\n", "   <g id=\"line2d_17\">\n", "    <path d=\"M 40.603125 13.5 \n", "L 44.588839 22.620841 \n", "L 48.574554 37.296363 \n", "L 52.560268 52.909986 \n", "L 56.545982 60.631694 \n", "L 60.531696 64.28035 \n", "L 64.517411 69.214148 \n", "L 68.503125 72.230185 \n", "L 72.488839 76.576343 \n", "L 76.474554 80.034249 \n", "L 80.460268 83.234904 \n", "L 84.445982 85.811867 \n", "L 88.431696 88.925369 \n", "L 92.417411 91.384442 \n", "L 96.403125 94.93059 \n", "L 100.388839 99.190241 \n", "L 104.374554 102.553624 \n", "L 108.360268 106.804375 \n", "L 112.345982 109.761786 \n", "L 116.331696 113.737025 \n", "L 120.317411 117.371808 \n", "L 124.303125 120.190404 \n", "L 128.288839 123.802181 \n", "L 132.274554 125.769534 \n", "L 136.260268 129.312881 \n", "L 140.245982 131.509398 \n", "L 144.231696 133.440095 \n", "L 148.217411 135.542611 \n", "L 152.203125 136.744215 \n", "L 156.188839 136.960185 \n", "L 160.174554 137.32854 \n", "L 164.160268 138.244016 \n", "L 168.145982 137.973974 \n", "L 172.131696 138.668931 \n", "L 176.117411 139.127059 \n", "L 180.103125 139.019311 \n", "L 184.088839 138.712128 \n", "L 188.074554 139.246102 \n", "L 192.060268 139.263468 \n", "L 196.045982 139.451539 \n", "L 200.031696 138.900805 \n", "L 204.017411 139.34575 \n", "L 208.003125 139.426839 \n", "L 211.988839 139.321563 \n", "L 215.974554 139.310289 \n", "L 219.960268 139.414454 \n", "L 223.945982 139.5 \n", "L 227.931696 139.381705 \n", "L 231.917411 138.911374 \n", "L 235.903125 139.403675 \n", "\" clip-path=\"url(#pb0f0cf0716)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_3\">\n", "    <path d=\"M 40.**********.8 \n", "L 40.603125 7.2 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_4\">\n", "    <path d=\"M 235.**********.8 \n", "L 235.903125 7.2 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_5\">\n", "    <path d=\"M 40.**********.8 \n", "L 235.**********.8 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_6\">\n", "    <path d=\"M 40.603125 7.2 \n", "L 235.903125 7.2 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"legend_1\">\n", "    <g id=\"patch_7\">\n", "     <path d=\"M 173.628125 29.878125 \n", "L 228.903125 29.878125 \n", "Q 230.903125 29.878125 230.903125 27.878125 \n", "L 230.903125 14.2 \n", "Q 230.903125 12.2 228.903125 12.2 \n", "L 173.628125 12.2 \n", "Q 171.628125 12.2 171.628125 14.2 \n", "L 171.628125 27.878125 \n", "Q 171.628125 29.878125 173.628125 29.878125 \n", "z\n", "\" style=\"fill: #ffffff; opacity: 0.8; stroke: #cccccc; stroke-linejoin: miter\"/>\n", "    </g>\n", "    <g id=\"line2d_18\">\n", "     <path d=\"M 175.628125 20.298438 \n", "L 185.628125 20.298438 \n", "L 195.628125 20.298438 \n", "\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "    </g>\n", "    <g id=\"text_11\">\n", "     <!-- train -->\n", "     <g transform=\"translate(203.628125 23.798438)scale(0.1 -0.1)\">\n", "      <defs>\n", "       <path id=\"DejaVuSans-61\" d=\"M 2194 1759 \n", "Q 1497 1759 1228 1600 \n", "Q 959 1441 959 1056 \n", "Q 959 750 1161 570 \n", "Q 1363 391 1709 391 \n", "Q 2188 391 2477 730 \n", "Q 2766 1069 2766 1631 \n", "L 2766 1759 \n", "L 2194 1759 \n", "z\n", "M 3341 1997 \n", "L 3341 0 \n", "L 2766 0 \n", "L 2766 531 \n", "Q 2569 213 2275 61 \n", "Q 1981 -91 1556 -91 \n", "Q 1019 -91 701 211 \n", "Q 384 513 384 1019 \n", "Q 384 1609 779 1909 \n", "Q 1175 2209 1959 2209 \n", "L 2766 2209 \n", "L 2766 2266 \n", "Q 2766 2663 2505 2880 \n", "Q 2244 3097 1772 3097 \n", "Q 1472 3097 1187 3025 \n", "Q 903 2953 641 2809 \n", "L 641 3341 \n", "Q 956 3463 1253 3523 \n", "Q 1550 3584 1831 3584 \n", "Q 2591 3584 2966 3190 \n", "Q 3341 2797 3341 1997 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-6e\" d=\"M 3513 2113 \n", "L 3513 0 \n", "L 2938 0 \n", "L 2938 2094 \n", "Q 2938 2591 2744 2837 \n", "Q 2550 3084 2163 3084 \n", "Q 1697 3084 1428 2787 \n", "Q 1159 2491 1159 1978 \n", "L 1159 0 \n", "L 581 0 \n", "L 581 3500 \n", "L 1159 3500 \n", "L 1159 2956 \n", "Q 1366 3272 1645 3428 \n", "Q 1925 3584 2291 3584 \n", "Q 2894 3584 3203 3211 \n", "Q 3513 2838 3513 2113 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "      </defs>\n", "      <use xlink:href=\"#DejaVuSans-74\"/>\n", "      <use xlink:href=\"#DejaVuSans-72\" x=\"39.208984\"/>\n", "      <use xlink:href=\"#DejaVuSans-61\" x=\"80.322266\"/>\n", "      <use xlink:href=\"#DejaVuSans-69\" x=\"141.601562\"/>\n", "      <use xlink:href=\"#DejaVuSans-6e\" x=\"169.384766\"/>\n", "     </g>\n", "    </g>\n", "   </g>\n", "  </g>\n", " </g>\n", " <defs>\n", "  <clipPath id=\"pb0f0cf0716\">\n", "   <rect x=\"40.603125\" y=\"7.2\" width=\"195.3\" height=\"138.6\"/>\n", "  </clipPath>\n", " </defs>\n", "</svg>\n"], "text/plain": ["<Figure size 350x250 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["num_inputs = vocab_size\n", "lstm_layer = nn.LSTM(num_inputs, num_hiddens)\n", "model = d2l.RNNModel(lstm_layer, len(vocab))\n", "model = model.to(device)\n", "d2l.train_ch8(model, train_iter, vocab, lr, num_epochs, device)"]}, {"cell_type": "markdown", "id": "0efe89db", "metadata": {"origin_pos": 29}, "source": ["长短期记忆网络是典型的具有重要状态控制的隐变量自回归模型。\n", "多年来已经提出了其许多变体，例如，多层、残差连接、不同类型的正则化。\n", "然而，由于序列的长距离依赖性，训练长短期记忆网络\n", "和其他序列模型（例如门控循环单元）的成本是相当高的。\n", "在后面的内容中，我们将讲述更高级的替代模型，如Transformer。\n", "\n", "## 小结\n", "\n", "* 长短期记忆网络有三种类型的门：输入门、遗忘门和输出门。\n", "* 长短期记忆网络的隐藏层输出包括“隐状态”和“记忆元”。只有隐状态会传递到输出层，而记忆元完全属于内部信息。\n", "* 长短期记忆网络可以缓解梯度消失和梯度爆炸。\n", "\n", "\n", "## 练习\n", "\n", "1. 调整和分析超参数对运行时间、困惑度和输出顺序的影响。\n", "1. 如何更改模型以生成适当的单词，而不是字符序列？\n", "1. 在给定隐藏层维度的情况下，比较门控循环单元、长短期记忆网络和常规循环神经网络的计算成本。要特别注意训练和推断成本。\n", "1. 既然候选记忆元通过使用$\\tanh$函数来确保值范围在$(-1,1)$之间，那么为什么隐状态需要再次使用$\\tanh$函数来确保输出值范围在$(-1,1)$之间呢？\n", "1. 实现一个能够基于时间序列进行预测而不是基于字符序列进行预测的长短期记忆网络模型。\n"]}, {"cell_type": "markdown", "id": "d1754570", "metadata": {"origin_pos": 31, "tab": ["pytorch"]}, "source": ["[Discussions](https://discuss.d2l.ai/t/2768)\n"]}], "metadata": {"language_info": {"name": "python"}}, "nbformat": 4, "nbformat_minor": 5}