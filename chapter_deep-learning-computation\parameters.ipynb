{"cells": [{"cell_type": "markdown", "id": "c196a1a1", "metadata": {"origin_pos": 0}, "source": ["# 参数管理\n", "\n", "在选择了架构并设置了超参数后，我们就进入了训练阶段。\n", "此时，我们的目标是找到使损失函数最小化的模型参数值。\n", "经过训练后，我们将需要使用这些参数来做出未来的预测。\n", "此外，有时我们希望提取参数，以便在其他环境中复用它们，\n", "将模型保存下来，以便它可以在其他软件中执行，\n", "或者为了获得科学的理解而进行检查。\n", "\n", "之前的介绍中，我们只依靠深度学习框架来完成训练的工作，\n", "而忽略了操作参数的具体细节。\n", "本节，我们将介绍以下内容：\n", "\n", "* 访问参数，用于调试、诊断和可视化；\n", "* 参数初始化；\n", "* 在不同模型组件间共享参数。\n", "\n", "(**我们首先看一下具有单隐藏层的多层感知机。**)\n"]}, {"cell_type": "code", "execution_count": 1, "id": "31e7c733", "metadata": {"execution": {"iopub.execute_input": "2022-12-07T16:59:39.200121Z", "iopub.status.busy": "2022-12-07T16:59:39.199802Z", "iopub.status.idle": "2022-12-07T16:59:40.341995Z", "shell.execute_reply": "2022-12-07T16:59:40.341206Z"}, "origin_pos": 2, "tab": ["pytorch"]}, "outputs": [{"data": {"text/plain": ["tensor([[0.0415],\n", "        [0.0004]], grad_fn=<AddmmBackward0>)"]}, "execution_count": 1, "metadata": {}, "output_type": "execute_result"}], "source": ["import torch\n", "from torch import nn\n", "\n", "net = nn.Sequential(nn.<PERSON>ar(4, 8), nn.<PERSON><PERSON><PERSON>(), nn.<PERSON>ar(8, 1))\n", "X = torch.rand(size=(2, 4))\n", "net(X)"]}, {"cell_type": "markdown", "id": "e403f7f0", "metadata": {"origin_pos": 5}, "source": ["## [**参数访问**]\n", "\n", "我们从已有模型中访问参数。\n", "当通过`Sequential`类定义模型时，\n", "我们可以通过索引来访问模型的任意层。\n", "这就像模型是一个列表一样，每层的参数都在其属性中。\n", "如下所示，我们可以检查第二个全连接层的参数。\n"]}, {"cell_type": "code", "execution_count": 2, "id": "9e0c2768", "metadata": {"execution": {"iopub.execute_input": "2022-12-07T16:59:40.345895Z", "iopub.status.busy": "2022-12-07T16:59:40.345358Z", "iopub.status.idle": "2022-12-07T16:59:40.351780Z", "shell.execute_reply": "2022-12-07T16:59:40.350856Z"}, "origin_pos": 7, "tab": ["pytorch"]}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["OrderedDict([('weight', tensor([[ 0.3016, -0.1901, -0.1991, -0.1220,  0.1121, -0.1424, -0.3060,  0.3400]])), ('bias', tensor([-0.0291]))])\n"]}], "source": ["print(net[2].state_dict())"]}, {"cell_type": "markdown", "id": "cfa84f24", "metadata": {"origin_pos": 9}, "source": ["输出的结果告诉我们一些重要的事情：\n", "首先，这个全连接层包含两个参数，分别是该层的权重和偏置。\n", "两者都存储为单精度浮点数（float32）。\n", "注意，参数名称允许唯一标识每个参数，即使在包含数百个层的网络中也是如此。\n", "\n", "### [**目标参数**]\n", "\n", "注意，每个参数都表示为参数类的一个实例。\n", "要对参数执行任何操作，首先我们需要访问底层的数值。\n", "有几种方法可以做到这一点。有些比较简单，而另一些则比较通用。\n", "下面的代码从第二个全连接层（即第三个神经网络层）提取偏置，\n", "提取后返回的是一个参数类实例，并进一步访问该参数的值。\n"]}, {"cell_type": "code", "execution_count": 3, "id": "20960d14", "metadata": {"execution": {"iopub.execute_input": "2022-12-07T16:59:40.355017Z", "iopub.status.busy": "2022-12-07T16:59:40.354533Z", "iopub.status.idle": "2022-12-07T16:59:40.361115Z", "shell.execute_reply": "2022-12-07T16:59:40.360109Z"}, "origin_pos": 11, "tab": ["pytorch"]}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["<class 'torch.nn.parameter.Parameter'>\n", "Parameter containing:\n", "tensor([-0.0291], requires_grad=True)\n", "tensor([-0.0291])\n"]}], "source": ["print(type(net[2].bias))\n", "print(net[2].bias)\n", "print(net[2].bias.data)"]}, {"cell_type": "markdown", "id": "074e6a61", "metadata": {"origin_pos": 14, "tab": ["pytorch"]}, "source": ["参数是复合的对象，包含值、梯度和额外信息。\n", "这就是我们需要显式参数值的原因。\n", "除了值之外，我们还可以访问每个参数的梯度。\n", "在上面这个网络中，由于我们还没有调用反向传播，所以参数的梯度处于初始状态。\n"]}, {"cell_type": "code", "execution_count": 2, "id": "3aaf945f", "metadata": {"execution": {"iopub.execute_input": "2022-12-07T16:59:40.364515Z", "iopub.status.busy": "2022-12-07T16:59:40.364074Z", "iopub.status.idle": "2022-12-07T16:59:40.370363Z", "shell.execute_reply": "2022-12-07T16:59:40.369347Z"}, "origin_pos": 16, "tab": ["pytorch"]}, "outputs": [{"data": {"text/plain": ["True"]}, "execution_count": 2, "metadata": {}, "output_type": "execute_result"}], "source": ["net[2].weight.grad == None"]}, {"cell_type": "markdown", "id": "213da81f", "metadata": {"origin_pos": 17}, "source": ["### [**一次性访问所有参数**]\n", "\n", "当我们需要对所有参数执行操作时，逐个访问它们可能会很麻烦。\n", "当我们处理更复杂的块（例如，嵌套块）时，情况可能会变得特别复杂，\n", "因为我们需要递归整个树来提取每个子块的参数。\n", "下面，我们将通过演示来比较访问第一个全连接层的参数和访问所有层。\n"]}, {"cell_type": "code", "execution_count": 3, "id": "307d4757", "metadata": {"execution": {"iopub.execute_input": "2022-12-07T16:59:40.373816Z", "iopub.status.busy": "2022-12-07T16:59:40.373095Z", "iopub.status.idle": "2022-12-07T16:59:40.379087Z", "shell.execute_reply": "2022-12-07T16:59:40.378031Z"}, "origin_pos": 19, "tab": ["pytorch"]}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["('weight', torch.<PERSON><PERSON>([8, 4])) ('bias', torch.<PERSON><PERSON>([8]))\n", "('0.weight', torch.<PERSON><PERSON>([8, 4])) ('0.bias', torch.<PERSON><PERSON>([8])) ('2.weight', torch.<PERSON><PERSON>([1, 8])) ('2.bias', torch.<PERSON><PERSON>([1]))\n"]}], "source": ["print(*[(name, param.shape) for name, param in net[0].named_parameters()])\n", "print(*[(name, param.shape) for name, param in net.named_parameters()])"]}, {"cell_type": "markdown", "id": "f958a276", "metadata": {"origin_pos": 21}, "source": ["这为我们提供了另一种访问网络参数的方式，如下所示。\n"]}, {"cell_type": "code", "execution_count": 4, "id": "deb873b1", "metadata": {"execution": {"iopub.execute_input": "2022-12-07T16:59:40.382676Z", "iopub.status.busy": "2022-12-07T16:59:40.381950Z", "iopub.status.idle": "2022-12-07T16:59:40.387675Z", "shell.execute_reply": "2022-12-07T16:59:40.386925Z"}, "origin_pos": 23, "tab": ["pytorch"]}, "outputs": [{"data": {"text/plain": ["tensor([-0.2538])"]}, "execution_count": 4, "metadata": {}, "output_type": "execute_result"}], "source": ["net.state_dict()['2.bias'].data"]}, {"cell_type": "markdown", "id": "2a1d2a1f", "metadata": {"origin_pos": 26}, "source": ["### [**从嵌套块收集参数**]\n", "\n", "让我们看看，如果我们将多个块相互嵌套，参数命名约定是如何工作的。\n", "我们首先定义一个生成块的函数（可以说是“块工厂”），然后将这些块组合到更大的块中。\n"]}, {"cell_type": "code", "execution_count": 6, "id": "74cd9d52", "metadata": {"execution": {"iopub.execute_input": "2022-12-07T16:59:40.391302Z", "iopub.status.busy": "2022-12-07T16:59:40.390503Z", "iopub.status.idle": "2022-12-07T16:59:40.400904Z", "shell.execute_reply": "2022-12-07T16:59:40.400156Z"}, "origin_pos": 28, "tab": ["pytorch"]}, "outputs": [{"data": {"text/plain": ["tensor([[0.0657],\n", "        [0.0657]], grad_fn=<AddmmBackward0>)"]}, "execution_count": 6, "metadata": {}, "output_type": "execute_result"}], "source": ["def block1():\n", "    return nn.Sequential(nn.<PERSON><PERSON>(4, 8), nn.<PERSON><PERSON><PERSON>(),\n", "                         nn.<PERSON><PERSON>(8, 4), nn.<PERSON><PERSON><PERSON>())\n", "\n", "def block2():\n", "    net = nn.Sequential()\n", "    for i in range(4):\n", "        # 在这里嵌套\n", "        net.add_module(f'block {i}', block1())\n", "    return net\n", "\n", "rgnet = nn.Sequential(block2(), nn.Linear(4, 1))\n", "rgnet(X)"]}, {"cell_type": "markdown", "id": "4e4e7daa", "metadata": {"origin_pos": 31}, "source": ["[**设计了网络后，我们看看它是如何工作的。**]\n"]}, {"cell_type": "code", "execution_count": 7, "id": "dc4d4978", "metadata": {"execution": {"iopub.execute_input": "2022-12-07T16:59:40.404269Z", "iopub.status.busy": "2022-12-07T16:59:40.403645Z", "iopub.status.idle": "2022-12-07T16:59:40.407896Z", "shell.execute_reply": "2022-12-07T16:59:40.407140Z"}, "origin_pos": 33, "tab": ["pytorch"]}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Sequential(\n", "  (0): Sequential(\n", "    (block 0): Sequential(\n", "      (0): Linear(in_features=4, out_features=8, bias=True)\n", "      (1): ReLU()\n", "      (2): Linear(in_features=8, out_features=4, bias=True)\n", "      (3): ReLU()\n", "    )\n", "    (block 1): Sequential(\n", "      (0): Linear(in_features=4, out_features=8, bias=True)\n", "      (1): ReLU()\n", "      (2): Linear(in_features=8, out_features=4, bias=True)\n", "      (3): ReLU()\n", "    )\n", "    (block 2): Sequential(\n", "      (0): Linear(in_features=4, out_features=8, bias=True)\n", "      (1): ReLU()\n", "      (2): Linear(in_features=8, out_features=4, bias=True)\n", "      (3): ReLU()\n", "    )\n", "    (block 3): Sequential(\n", "      (0): Linear(in_features=4, out_features=8, bias=True)\n", "      (1): ReLU()\n", "      (2): Linear(in_features=8, out_features=4, bias=True)\n", "      (3): ReLU()\n", "    )\n", "  )\n", "  (1): Linear(in_features=4, out_features=1, bias=True)\n", ")\n"]}], "source": ["print(rgnet)"]}, {"cell_type": "markdown", "id": "9fb85a62", "metadata": {"origin_pos": 35}, "source": ["因为层是分层嵌套的，所以我们也可以像通过嵌套列表索引一样访问它们。\n", "下面，我们访问第一个主要的块中、第二个子块的第一层的偏置项。\n"]}, {"cell_type": "code", "execution_count": 9, "id": "d8e60b35", "metadata": {"execution": {"iopub.execute_input": "2022-12-07T16:59:40.410923Z", "iopub.status.busy": "2022-12-07T16:59:40.410613Z", "iopub.status.idle": "2022-12-07T16:59:40.416456Z", "shell.execute_reply": "2022-12-07T16:59:40.415718Z"}, "origin_pos": 37, "tab": ["pytorch"]}, "outputs": [{"data": {"text/plain": ["tensor([ 0.1697,  0.1289,  0.4275,  0.2784, -0.4169, -0.1422, -0.4286, -0.3350])"]}, "execution_count": 9, "metadata": {}, "output_type": "execute_result"}], "source": ["rgnet[0][1][0].bias.data"]}, {"cell_type": "markdown", "id": "b83eb7ff", "metadata": {"origin_pos": 40}, "source": ["## 参数初始化\n", "\n", "知道了如何访问参数后，现在我们看看如何正确地初始化参数。\n", "我们在 :numref:`sec_numerical_stability`中讨论了良好初始化的必要性。\n", "深度学习框架提供默认随机初始化，\n", "也允许我们创建自定义初始化方法，\n", "满足我们通过其他规则实现初始化权重。\n"]}, {"cell_type": "markdown", "id": "6bd728dd", "metadata": {"origin_pos": 42, "tab": ["pytorch"]}, "source": ["默认情况下，PyTorch会根据一个范围均匀地初始化权重和偏置矩阵，\n", "这个范围是根据输入和输出维度计算出的。\n", "PyTorch的`nn.init`模块提供了多种预置初始化方法。\n"]}, {"cell_type": "markdown", "id": "3ad9b894", "metadata": {"origin_pos": 45}, "source": ["### [**内置初始化**]\n", "\n", "让我们首先调用内置的初始化器。\n", "下面的代码将所有权重参数初始化为标准差为0.01的高斯随机变量，\n", "且将偏置参数设置为0。\n"]}, {"cell_type": "code", "execution_count": 8, "id": "719e7eb8", "metadata": {"execution": {"iopub.execute_input": "2022-12-07T16:59:40.419626Z", "iopub.status.busy": "2022-12-07T16:59:40.419352Z", "iopub.status.idle": "2022-12-07T16:59:40.426766Z", "shell.execute_reply": "2022-12-07T16:59:40.426002Z"}, "origin_pos": 47, "tab": ["pytorch"]}, "outputs": [{"data": {"text/plain": ["(tensor([ 0.0038,  0.0048, -0.0052, -0.0092]), tensor(0.))"]}, "execution_count": 8, "metadata": {}, "output_type": "execute_result"}], "source": ["def init_normal(m):\n", "    if type(m) == nn.Linear:\n", "        nn.init.normal_(m.weight, mean=0, std=0.01)\n", "        nn.init.zeros_(m.bias)\n", "net.apply(init_normal)\n", "net[0].weight.data[0], net[0].bias.data[0]"]}, {"cell_type": "markdown", "id": "aaeb6088", "metadata": {"origin_pos": 50}, "source": ["我们还可以将所有参数初始化为给定的常数，比如初始化为1。\n"]}, {"cell_type": "code", "execution_count": 10, "id": "acd121fc", "metadata": {"execution": {"iopub.execute_input": "2022-12-07T16:59:40.430188Z", "iopub.status.busy": "2022-12-07T16:59:40.429543Z", "iopub.status.idle": "2022-12-07T16:59:40.436502Z", "shell.execute_reply": "2022-12-07T16:59:40.435761Z"}, "origin_pos": 52, "tab": ["pytorch"]}, "outputs": [{"data": {"text/plain": ["(tensor([1., 1., 1., 1.]), tensor(0.))"]}, "execution_count": 10, "metadata": {}, "output_type": "execute_result"}], "source": ["def init_constant(m):\n", "    if type(m) == nn.Linear:\n", "        nn.init.constant_(m.weight, 1)\n", "        nn.init.zeros_(m.bias)\n", "net.apply(init_constant)\n", "net[0].weight.data[0], net[0].bias.data[0]"]}, {"cell_type": "markdown", "id": "25dec809", "metadata": {"origin_pos": 55}, "source": ["我们还可以[**对某些块应用不同的初始化方法**]。\n", "例如，下面我们使用Xavier初始化方法初始化第一个神经网络层，\n", "然后将第三个神经网络层初始化为常量值42。\n"]}, {"cell_type": "code", "execution_count": 11, "id": "44989c06", "metadata": {"execution": {"iopub.execute_input": "2022-12-07T16:59:40.439840Z", "iopub.status.busy": "2022-12-07T16:59:40.439218Z", "iopub.status.idle": "2022-12-07T16:59:40.445909Z", "shell.execute_reply": "2022-12-07T16:59:40.445156Z"}, "origin_pos": 57, "tab": ["pytorch"]}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["tensor([ 0.1042, -0.4185,  0.0256, -0.2615])\n", "tensor([[42., 42., 42., 42., 42., 42., 42., 42.]])\n"]}], "source": ["def init_xavier(m):\n", "    if type(m) == nn.Linear:\n", "        nn.init.xavier_uniform_(m.weight)\n", "def init_42(m):\n", "    if type(m) == nn.Linear:\n", "        nn.init.constant_(m.weight, 42)\n", "\n", "net[0].apply(init_xavier)\n", "net[2].apply(init_42)\n", "print(net[0].weight.data[0])\n", "print(net[2].weight.data)"]}, {"cell_type": "markdown", "id": "98030067", "metadata": {"origin_pos": 60}, "source": ["### [**自定义初始化**]\n", "\n", "有时，深度学习框架没有提供我们需要的初始化方法。\n", "在下面的例子中，我们使用以下的分布为任意权重参数$w$定义初始化方法：\n", "\n", "$$\n", "\\begin{aligned}\n", "    w \\sim \\begin{cases}\n", "        U(5, 10) & \\text{ 可能性 } \\frac{1}{4} \\\\\n", "            0    & \\text{ 可能性 } \\frac{1}{2} \\\\\n", "        U(-10, -5) & \\text{ 可能性 } \\frac{1}{4}\n", "    \\end{cases}\n", "\\end{aligned}\n", "$$\n"]}, {"cell_type": "markdown", "id": "4e04286d", "metadata": {"origin_pos": 62, "tab": ["pytorch"]}, "source": ["同样，我们实现了一个`my_init`函数来应用到`net`。\n"]}, {"cell_type": "code", "execution_count": 12, "id": "62859272", "metadata": {"execution": {"iopub.execute_input": "2022-12-07T16:59:40.449458Z", "iopub.status.busy": "2022-12-07T16:59:40.448848Z", "iopub.status.idle": "2022-12-07T16:59:40.457344Z", "shell.execute_reply": "2022-12-07T16:59:40.456599Z"}, "origin_pos": 66, "tab": ["pytorch"]}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Init weight torch.Si<PERSON>([8, 4])\n", "Init weight torch.Size([1, 8])\n"]}, {"data": {"text/plain": ["tensor([[-7.9362, -0.0000,  9.1421,  6.3697],\n", "        [ 0.0000,  0.0000, -8.9413, -0.0000]], grad_fn=<SliceBackward0>)"]}, "execution_count": 12, "metadata": {}, "output_type": "execute_result"}], "source": ["def my_init(m):\n", "    if type(m) == nn.Linear:\n", "        print(\"Init\", *[(name, param.shape)\n", "                        for name, param in m.named_parameters()][0])\n", "        nn.init.uniform_(m.weight, -10, 10)\n", "        m.weight.data *= m.weight.data.abs() >= 5\n", "\n", "net.apply(my_init)\n", "net[0].weight[:2]"]}, {"cell_type": "markdown", "id": "d2a17df1", "metadata": {"origin_pos": 69}, "source": ["注意，我们始终可以直接设置参数。\n"]}, {"cell_type": "code", "execution_count": 13, "id": "c384147e", "metadata": {"execution": {"iopub.execute_input": "2022-12-07T16:59:40.460372Z", "iopub.status.busy": "2022-12-07T16:59:40.460090Z", "iopub.status.idle": "2022-12-07T16:59:40.466428Z", "shell.execute_reply": "2022-12-07T16:59:40.465690Z"}, "origin_pos": 71, "tab": ["pytorch"]}, "outputs": [{"data": {"text/plain": ["tensor([42.0000,  1.0000, 10.1421,  7.3697])"]}, "execution_count": 13, "metadata": {}, "output_type": "execute_result"}], "source": ["net[0].weight.data[:] += 1\n", "net[0].weight.data[0, 0] = 42\n", "net[0].weight.data[0]"]}, {"cell_type": "markdown", "id": "3eb1069a", "metadata": {"origin_pos": 75}, "source": ["## [**参数绑定**]\n", "\n", "有时我们希望在多个层间共享参数：\n", "我们可以定义一个稠密层，然后使用它的参数来设置另一个层的参数。\n"]}, {"cell_type": "code", "execution_count": 14, "id": "ef91cda9", "metadata": {"execution": {"iopub.execute_input": "2022-12-07T16:59:40.469511Z", "iopub.status.busy": "2022-12-07T16:59:40.469232Z", "iopub.status.idle": "2022-12-07T16:59:40.477075Z", "shell.execute_reply": "2022-12-07T16:59:40.476334Z"}, "origin_pos": 77, "tab": ["pytorch"]}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["tensor([True, True, True, True, True, True, True, True])\n", "tensor([True, True, True, True, True, True, True, True])\n"]}], "source": ["# 我们需要给共享层一个名称，以便可以引用它的参数\n", "shared = nn.<PERSON><PERSON>(8, 8)\n", "net = nn.Sequential(nn.<PERSON>(4, 8), nn.ReLU(),\n", "                    shared, nn.ReLU(),\n", "                    shared, nn.ReLU(),\n", "                    nn.<PERSON><PERSON>(8, 1))\n", "net(X)\n", "# 检查参数是否相同\n", "print(net[2].weight.data[0] == net[4].weight.data[0])\n", "net[2].weight.data[0, 0] = 100\n", "# 确保它们实际上是同一个对象，而不只是有相同的值\n", "print(net[2].weight.data[0] == net[4].weight.data[0])"]}, {"cell_type": "markdown", "id": "d9b3ff77", "metadata": {"origin_pos": 81, "tab": ["pytorch"]}, "source": ["这个例子表明第三个和第五个神经网络层的参数是绑定的。\n", "它们不仅值相等，而且由相同的张量表示。\n", "因此，如果我们改变其中一个参数，另一个参数也会改变。\n", "这里有一个问题：当参数绑定时，梯度会发生什么情况？\n", "答案是由于模型参数包含梯度，因此在反向传播期间第二个隐藏层\n", "（即第三个神经网络层）和第三个隐藏层（即第五个神经网络层）的梯度会加在一起。\n"]}, {"cell_type": "markdown", "id": "a32e2e93", "metadata": {"origin_pos": 82}, "source": ["## 小结\n", "\n", "* 我们有几种方法可以访问、初始化和绑定模型参数。\n", "* 我们可以使用自定义初始化方法。\n", "\n", "## 练习\n", "\n", "1. 使用 :numref:`sec_model_construction` 中定义的`FancyMLP`模型，访问各个层的参数。\n", "1. 查看初始化模块文档以了解不同的初始化方法。\n", "1. 构建包含共享参数层的多层感知机并对其进行训练。在训练过程中，观察模型各层的参数和梯度。\n", "1. 为什么共享参数是个好主意？\n"]}, {"cell_type": "markdown", "id": "2b1255a6", "metadata": {"origin_pos": 84, "tab": ["pytorch"]}, "source": ["[Discussions](https://discuss.d2l.ai/t/1829)\n"]}], "metadata": {"kernelspec": {"display_name": "d2l", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.23"}}, "nbformat": 4, "nbformat_minor": 5}