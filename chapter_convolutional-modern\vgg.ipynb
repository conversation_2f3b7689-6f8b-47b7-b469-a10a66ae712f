{"cells": [{"cell_type": "markdown", "id": "3ba8a434", "metadata": {"origin_pos": 0}, "source": ["# 使用块的网络（VGG）\n", ":label:`sec_vgg`\n", "\n", "虽然AlexNet证明深层神经网络卓有成效，但它没有提供一个通用的模板来指导后续的研究人员设计新的网络。\n", "在下面的几个章节中，我们将介绍一些常用于设计深层神经网络的启发式概念。\n", "\n", "与芯片设计中工程师从放置晶体管到逻辑元件再到逻辑块的过程类似，神经网络架构的设计也逐渐变得更加抽象。研究人员开始从单个神经元的角度思考问题，发展到整个层，现在又转向块，重复层的模式。\n", "\n", "使用块的想法首先出现在牛津大学的[视觉几何组（visual geometry group）](http://www.robots.ox.ac.uk/~vgg/)的*VGG网络*中。通过使用循环和子程序，可以很容易地在任何现代深度学习框架的代码中实现这些重复的架构。\n", "\n", "## (**VGG块**)\n", "\n", "经典卷积神经网络的基本组成部分是下面的这个序列：\n", "\n", "1. 带填充以保持分辨率的卷积层；\n", "1. 非线性激活函数，如ReLU；\n", "1. 汇聚层，如最大汇聚层。\n", "\n", "而一个VGG块与之类似，由一系列卷积层组成，后面再加上用于空间下采样的最大汇聚层。在最初的VGG论文中 :cite:`Simonyan.Zisserman.2014`，作者使用了带有$3\\times3$卷积核、填充为1（保持高度和宽度）的卷积层，和带有$2 \\times 2$汇聚窗口、步幅为2（每个块后的分辨率减半）的最大汇聚层。在下面的代码中，我们定义了一个名为`vgg_block`的函数来实现一个VGG块。\n"]}, {"cell_type": "markdown", "id": "1b1b24c1", "metadata": {"origin_pos": 2, "tab": ["pytorch"]}, "source": ["该函数有三个参数，分别对应于卷积层的数量`num_convs`、输入通道的数量`in_channels`\n", "和输出通道的数量`out_channels`.\n"]}, {"cell_type": "code", "execution_count": 1, "id": "6eb985dc", "metadata": {"execution": {"iopub.execute_input": "2022-12-07T16:58:36.260832Z", "iopub.status.busy": "2022-12-07T16:58:36.260146Z", "iopub.status.idle": "2022-12-07T16:58:38.576974Z", "shell.execute_reply": "2022-12-07T16:58:38.576147Z"}, "origin_pos": 4, "tab": ["pytorch"]}, "outputs": [], "source": ["import torch\n", "from torch import nn\n", "from d2l import torch as d2l\n", "\n", "\n", "def vgg_block(num_convs, in_channels, out_channels):\n", "    layers = []\n", "    for _ in range(num_convs):\n", "        layers.append(nn.Conv2d(in_channels, out_channels,\n", "                                kernel_size=3, padding=1))\n", "        layers.append(nn.ReLU())\n", "        in_channels = out_channels\n", "    layers.append(nn.MaxPool2d(kernel_size=2,stride=2))\n", "    return nn.Sequential(*layers)"]}, {"cell_type": "markdown", "id": "24831f1f", "metadata": {"origin_pos": 7}, "source": ["## [**VGG网络**]\n", "\n", "与AlexNet、LeNet一样，VGG网络可以分为两部分：第一部分主要由卷积层和汇聚层组成，第二部分由全连接层组成。如 :numref:`fig_vgg`中所示。\n", "\n", "![从AlexNet到VGG，它们本质上都是块设计。](../img/vgg.svg)\n", ":width:`400px`\n", ":label:`fig_vgg`\n", "\n", "VGG神经网络连接 :numref:`fig_vgg`的几个VGG块（在`vgg_block`函数中定义）。其中有超参数变量`conv_arch`。该变量指定了每个VGG块里卷积层个数和输出通道数。全连接模块则与AlexNet中的相同。\n", "\n", "原始VGG网络有5个卷积块，其中前两个块各有一个卷积层，后三个块各包含两个卷积层。\n", "第一个模块有64个输出通道，每个后续模块将输出通道数量翻倍，直到该数字达到512。由于该网络使用8个卷积层和3个全连接层，因此它通常被称为VGG-11。\n"]}, {"cell_type": "code", "execution_count": 2, "id": "5af1d8c6", "metadata": {"execution": {"iopub.execute_input": "2022-12-07T16:58:38.581013Z", "iopub.status.busy": "2022-12-07T16:58:38.580458Z", "iopub.status.idle": "2022-12-07T16:58:38.584735Z", "shell.execute_reply": "2022-12-07T16:58:38.584016Z"}, "origin_pos": 8, "tab": ["pytorch"]}, "outputs": [], "source": ["conv_arch = ((1, 64), (1, 128), (2, 256), (2, 512), (2, 512))"]}, {"cell_type": "markdown", "id": "fbdf85f7", "metadata": {"origin_pos": 9}, "source": ["下面的代码实现了VGG-11。可以通过在`conv_arch`上执行for循环来简单实现。\n"]}, {"cell_type": "code", "execution_count": 3, "id": "41da2292", "metadata": {"execution": {"iopub.execute_input": "2022-12-07T16:58:38.587894Z", "iopub.status.busy": "2022-12-07T16:58:38.587481Z", "iopub.status.idle": "2022-12-07T16:58:39.800780Z", "shell.execute_reply": "2022-12-07T16:58:39.799939Z"}, "origin_pos": 11, "tab": ["pytorch"]}, "outputs": [], "source": ["def vgg(conv_arch):\n", "    conv_blks = []\n", "    in_channels = 1\n", "    # 卷积层部分\n", "    for (num_convs, out_channels) in conv_arch:\n", "        conv_blks.append(vgg_block(num_convs, in_channels, out_channels))\n", "        in_channels = out_channels\n", "\n", "    return nn.Sequential(\n", "        *conv_blks, nn.<PERSON>(),\n", "        # 全连接层部分\n", "        nn.<PERSON><PERSON>(out_channels * 7 * 7, 4096), nn.<PERSON><PERSON><PERSON>(), nn.Dropout(0.5),\n", "        nn.<PERSON><PERSON>(4096, 4096), nn.<PERSON><PERSON><PERSON>(), nn.Dropout(0.5),\n", "        nn.<PERSON><PERSON>(4096, 10))\n", "\n", "net = vgg(conv_arch)"]}, {"cell_type": "markdown", "id": "faabece7", "metadata": {"origin_pos": 14}, "source": ["接下来，我们将构建一个高度和宽度为224的单通道数据样本，以[**观察每个层输出的形状**]。\n"]}, {"cell_type": "code", "execution_count": 4, "id": "fb5107af", "metadata": {"execution": {"iopub.execute_input": "2022-12-07T16:58:39.804628Z", "iopub.status.busy": "2022-12-07T16:58:39.804064Z", "iopub.status.idle": "2022-12-07T16:58:39.868738Z", "shell.execute_reply": "2022-12-07T16:58:39.867895Z"}, "origin_pos": 16, "tab": ["pytorch"]}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Sequential output shape:\t torch.Size([1, 64, 112, 112])\n", "Sequential output shape:\t torch.Size([1, 128, 56, 56])\n", "Sequential output shape:\t torch.Size([1, 256, 28, 28])\n", "Sequential output shape:\t torch.Size([1, 512, 14, 14])\n", "Sequential output shape:\t torch.Size([1, 512, 7, 7])\n", "Flatten output shape:\t torch.Size([1, 25088])\n", "Linear output shape:\t torch.Size([1, 4096])\n", "ReLU output shape:\t torch.Size([1, 4096])\n", "Dropout output shape:\t torch.Size([1, 4096])\n", "Linear output shape:\t torch.Size([1, 4096])\n", "ReLU output shape:\t torch.Size([1, 4096])\n", "Dropout output shape:\t torch.Size([1, 4096])\n", "Linear output shape:\t torch.Size([1, 10])\n"]}], "source": ["X = torch.randn(size=(1, 1, 224, 224))\n", "for blk in net:\n", "    X = blk(X)\n", "    print(blk.__class__.__name__,'output shape:\\t',X.shape)"]}, {"cell_type": "markdown", "id": "55938e77", "metadata": {"origin_pos": 19}, "source": ["正如从代码中所看到的，我们在每个块的高度和宽度减半，最终高度和宽度都为7。最后再展平表示，送入全连接层处理。\n", "\n", "## 训练模型\n", "\n", "[**由于VGG-11比AlexNet计算量更大，因此我们构建了一个通道数较少的网络**]，足够用于训练Fashion-MNIST数据集。\n"]}, {"cell_type": "code", "execution_count": 5, "id": "5414436d", "metadata": {"execution": {"iopub.execute_input": "2022-12-07T16:58:39.872219Z", "iopub.status.busy": "2022-12-07T16:58:39.871657Z", "iopub.status.idle": "2022-12-07T16:58:40.269983Z", "shell.execute_reply": "2022-12-07T16:58:40.269105Z"}, "origin_pos": 20, "tab": ["pytorch"]}, "outputs": [], "source": ["ratio = 4\n", "small_conv_arch = [(pair[0], pair[1] // ratio) for pair in conv_arch]\n", "net = vgg(small_conv_arch)"]}, {"cell_type": "markdown", "id": "85b12f51", "metadata": {"origin_pos": 22}, "source": ["除了使用略高的学习率外，[**模型训练**]过程与 :numref:`sec_alexnet`中的AlexNet类似。\n"]}, {"cell_type": "code", "execution_count": 6, "id": "85690193", "metadata": {"execution": {"iopub.execute_input": "2022-12-07T16:58:40.273853Z", "iopub.status.busy": "2022-12-07T16:58:40.273288Z", "iopub.status.idle": "2022-12-07T17:03:41.730877Z", "shell.execute_reply": "2022-12-07T17:03:41.729913Z"}, "origin_pos": 23, "tab": ["pytorch"]}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["loss 0.220, train acc 0.918, test acc 0.900\n", "2578.4 examples/sec on cuda:0\n"]}, {"data": {"image/svg+xml": ["<?xml version=\"1.0\" encoding=\"utf-8\" standalone=\"no\"?>\n", "<!DOCTYPE svg PUBLIC \"-//W3C//DTD SVG 1.1//EN\"\n", "  \"http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd\">\n", "<svg xmlns:xlink=\"http://www.w3.org/1999/xlink\" width=\"238.965625pt\" height=\"183.35625pt\" viewBox=\"0 0 238.**********.35625\" xmlns=\"http://www.w3.org/2000/svg\" version=\"1.1\">\n", " <metadata>\n", "  <rdf:RDF xmlns:dc=\"http://purl.org/dc/elements/1.1/\" xmlns:cc=\"http://creativecommons.org/ns#\" xmlns:rdf=\"http://www.w3.org/1999/02/22-rdf-syntax-ns#\">\n", "   <cc:Work>\n", "    <dc:type rdf:resource=\"http://purl.org/dc/dcmitype/StillImage\"/>\n", "    <dc:date>2022-12-07T17:03:41.691220</dc:date>\n", "    <dc:format>image/svg+xml</dc:format>\n", "    <dc:creator>\n", "     <cc:Agent>\n", "      <dc:title>Matplotlib v3.5.1, https://matplotlib.org/</dc:title>\n", "     </cc:Agent>\n", "    </dc:creator>\n", "   </cc:Work>\n", "  </rdf:RDF>\n", " </metadata>\n", " <defs>\n", "  <style type=\"text/css\">*{stroke-linejoin: round; stroke-linecap: butt}</style>\n", " </defs>\n", " <g id=\"figure_1\">\n", "  <g id=\"patch_1\">\n", "   <path d=\"M 0 183.35625 \n", "L 238.**********.35625 \n", "L 238.965625 0 \n", "L 0 0 \n", "z\n", "\" style=\"fill: #ffffff\"/>\n", "  </g>\n", "  <g id=\"axes_1\">\n", "   <g id=\"patch_2\">\n", "    <path d=\"M 30.**********.8 \n", "L 225.**********.8 \n", "L 225.403125 7.2 \n", "L 30.103125 7.2 \n", "z\n", "\" style=\"fill: #ffffff\"/>\n", "   </g>\n", "   <g id=\"matplotlib.axis_1\">\n", "    <g id=\"xtick_1\">\n", "     <g id=\"line2d_1\">\n", "      <path d=\"M 51.803125 145.8 \n", "L 51.803125 7.2 \n", "\" clip-path=\"url(#p1c20af435f)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_2\">\n", "      <defs>\n", "       <path id=\"ma68b5a67f2\" d=\"M 0 0 \n", "L 0 3.5 \n", "\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </defs>\n", "      <g>\n", "       <use xlink:href=\"#ma68b5a67f2\" x=\"51.803125\" y=\"145.8\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_1\">\n", "      <!-- 2 -->\n", "      <g transform=\"translate(48.621875 160.398438)scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-32\" d=\"M 1228 531 \n", "L 3431 531 \n", "L 3431 0 \n", "L 469 0 \n", "L 469 531 \n", "Q 828 903 1448 1529 \n", "Q 2069 2156 2228 2338 \n", "Q 2531 2678 2651 2914 \n", "Q 2772 3150 2772 3378 \n", "Q 2772 3750 2511 3984 \n", "Q 2250 4219 1831 4219 \n", "Q 1534 4219 1204 4116 \n", "Q 875 4013 500 3803 \n", "L 500 4441 \n", "Q 881 4594 1212 4672 \n", "Q 1544 4750 1819 4750 \n", "Q 2544 4750 2975 4387 \n", "Q 3406 4025 3406 3419 \n", "Q 3406 3131 3298 2873 \n", "Q 3191 2616 2906 2266 \n", "Q 2828 2175 2409 1742 \n", "Q 1991 1309 1228 531 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-32\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_2\">\n", "     <g id=\"line2d_3\">\n", "      <path d=\"M 95.203125 145.8 \n", "L 95.203125 7.2 \n", "\" clip-path=\"url(#p1c20af435f)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_4\">\n", "      <g>\n", "       <use xlink:href=\"#ma68b5a67f2\" x=\"95.203125\" y=\"145.8\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_2\">\n", "      <!-- 4 -->\n", "      <g transform=\"translate(92.021875 160.398438)scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-34\" d=\"M 2419 4116 \n", "L 825 1625 \n", "L 2419 1625 \n", "L 2419 4116 \n", "z\n", "M 2253 4666 \n", "L 3047 4666 \n", "L 3047 1625 \n", "L 3713 1625 \n", "L 3713 1100 \n", "L 3047 1100 \n", "L 3047 0 \n", "L 2419 0 \n", "L 2419 1100 \n", "L 313 1100 \n", "L 313 1709 \n", "L 2253 4666 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-34\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_3\">\n", "     <g id=\"line2d_5\">\n", "      <path d=\"M 138.603125 145.8 \n", "L 138.603125 7.2 \n", "\" clip-path=\"url(#p1c20af435f)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_6\">\n", "      <g>\n", "       <use xlink:href=\"#ma68b5a67f2\" x=\"138.603125\" y=\"145.8\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_3\">\n", "      <!-- 6 -->\n", "      <g transform=\"translate(135.421875 160.398438)scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-36\" d=\"M 2113 2584 \n", "Q 1688 2584 1439 2293 \n", "Q 1191 2003 1191 1497 \n", "Q 1191 994 1439 701 \n", "Q 1688 409 2113 409 \n", "Q 2538 409 2786 701 \n", "Q 3034 994 3034 1497 \n", "Q 3034 2003 2786 2293 \n", "Q 2538 2584 2113 2584 \n", "z\n", "M 3366 4563 \n", "L 3366 3988 \n", "Q 3128 4100 2886 4159 \n", "Q 2644 4219 2406 4219 \n", "Q 1781 4219 1451 3797 \n", "Q 1122 3375 1075 2522 \n", "Q 1259 2794 1537 2939 \n", "Q 1816 3084 2150 3084 \n", "Q 2853 3084 3261 2657 \n", "Q 3669 2231 3669 1497 \n", "Q 3669 778 3244 343 \n", "Q 2819 -91 2113 -91 \n", "Q 1303 -91 875 529 \n", "Q 447 1150 447 2328 \n", "Q 447 3434 972 4092 \n", "Q 1497 4750 2381 4750 \n", "Q 2619 4750 2861 4703 \n", "Q 3103 4656 3366 4563 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-36\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_4\">\n", "     <g id=\"line2d_7\">\n", "      <path d=\"M 182.003125 145.8 \n", "L 182.003125 7.2 \n", "\" clip-path=\"url(#p1c20af435f)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_8\">\n", "      <g>\n", "       <use xlink:href=\"#ma68b5a67f2\" x=\"182.003125\" y=\"145.8\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_4\">\n", "      <!-- 8 -->\n", "      <g transform=\"translate(178.821875 160.398438)scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-38\" d=\"M 2034 2216 \n", "Q 1584 2216 1326 1975 \n", "Q 1069 1734 1069 1313 \n", "Q 1069 891 1326 650 \n", "Q 1584 409 2034 409 \n", "Q 2484 409 2743 651 \n", "Q 3003 894 3003 1313 \n", "Q 3003 1734 2745 1975 \n", "Q 2488 2216 2034 2216 \n", "z\n", "M 1403 2484 \n", "Q 997 2584 770 2862 \n", "Q 544 3141 544 3541 \n", "Q 544 4100 942 4425 \n", "Q 1341 4750 2034 4750 \n", "Q 2731 4750 3128 4425 \n", "Q 3525 4100 3525 3541 \n", "Q 3525 3141 3298 2862 \n", "Q 3072 2584 2669 2484 \n", "Q 3125 2378 3379 2068 \n", "Q 3634 1759 3634 1313 \n", "Q 3634 634 3220 271 \n", "Q 2806 -91 2034 -91 \n", "Q 1263 -91 848 271 \n", "Q 434 634 434 1313 \n", "Q 434 1759 690 2068 \n", "Q 947 2378 1403 2484 \n", "z\n", "M 1172 3481 \n", "Q 1172 3119 1398 2916 \n", "Q 1625 2713 2034 2713 \n", "Q 2441 2713 2670 2916 \n", "Q 2900 3119 2900 3481 \n", "Q 2900 3844 2670 4047 \n", "Q 2441 4250 2034 4250 \n", "Q 1625 4250 1398 4047 \n", "Q 1172 3844 1172 3481 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-38\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_5\">\n", "     <g id=\"line2d_9\">\n", "      <path d=\"M 225.**********.8 \n", "L 225.403125 7.2 \n", "\" clip-path=\"url(#p1c20af435f)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_10\">\n", "      <g>\n", "       <use xlink:href=\"#ma68b5a67f2\" x=\"225.403125\" y=\"145.8\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_5\">\n", "      <!-- 10 -->\n", "      <g transform=\"translate(219.040625 160.398438)scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-31\" d=\"M 794 531 \n", "L 1825 531 \n", "L 1825 4091 \n", "L 703 3866 \n", "L 703 4441 \n", "L 1819 4666 \n", "L 2450 4666 \n", "L 2450 531 \n", "L 3481 531 \n", "L 3481 0 \n", "L 794 0 \n", "L 794 531 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "        <path id=\"DejaVuSans-30\" d=\"M 2034 4250 \n", "Q 1547 4250 1301 3770 \n", "Q 1056 3291 1056 2328 \n", "Q 1056 1369 1301 889 \n", "Q 1547 409 2034 409 \n", "Q 2525 409 2770 889 \n", "Q 3016 1369 3016 2328 \n", "Q 3016 3291 2770 3770 \n", "Q 2525 4250 2034 4250 \n", "z\n", "M 2034 4750 \n", "Q 2819 4750 3233 4129 \n", "Q 3647 3509 3647 2328 \n", "Q 3647 1150 3233 529 \n", "Q 2819 -91 2034 -91 \n", "Q 1250 -91 836 529 \n", "Q 422 1150 422 2328 \n", "Q 422 3509 836 4129 \n", "Q 1250 4750 2034 4750 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-31\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"63.623047\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"text_6\">\n", "     <!-- epoch -->\n", "     <g transform=\"translate(112.525 174.076563)scale(0.1 -0.1)\">\n", "      <defs>\n", "       <path id=\"DejaVuSans-65\" d=\"M 3597 1894 \n", "L 3597 1613 \n", "L 953 1613 \n", "Q 991 1019 1311 708 \n", "Q 1631 397 2203 397 \n", "Q 2534 397 2845 478 \n", "Q 3156 559 3463 722 \n", "L 3463 178 \n", "Q 3153 47 2828 -22 \n", "Q 2503 -91 2169 -91 \n", "Q 1331 -91 842 396 \n", "Q 353 884 353 1716 \n", "Q 353 2575 817 3079 \n", "Q 1281 3584 2069 3584 \n", "Q 2775 3584 3186 3129 \n", "Q 3597 2675 3597 1894 \n", "z\n", "M 3022 2063 \n", "Q 3016 2534 2758 2815 \n", "Q 2500 3097 2075 3097 \n", "Q 1594 3097 1305 2825 \n", "Q 1016 2553 972 2059 \n", "L 3022 2063 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-70\" d=\"M 1159 525 \n", "L 1159 -1331 \n", "L 581 -1331 \n", "L 581 3500 \n", "L 1159 3500 \n", "L 1159 2969 \n", "Q 1341 3281 1617 3432 \n", "Q 1894 3584 2278 3584 \n", "Q 2916 3584 3314 3078 \n", "Q 3713 2572 3713 1747 \n", "Q 3713 922 3314 415 \n", "Q 2916 -91 2278 -91 \n", "Q 1894 -91 1617 61 \n", "Q 1341 213 1159 525 \n", "z\n", "M 3116 1747 \n", "Q 3116 2381 2855 2742 \n", "Q 2594 3103 2138 3103 \n", "Q 1681 3103 1420 2742 \n", "Q 1159 2381 1159 1747 \n", "Q 1159 1113 1420 752 \n", "Q 1681 391 2138 391 \n", "Q 2594 391 2855 752 \n", "Q 3116 1113 3116 1747 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-6f\" d=\"M 1959 3097 \n", "Q 1497 3097 1228 2736 \n", "Q 959 2375 959 1747 \n", "Q 959 1119 1226 758 \n", "Q 1494 397 1959 397 \n", "Q 2419 397 2687 759 \n", "Q 2956 1122 2956 1747 \n", "Q 2956 2369 2687 2733 \n", "Q 2419 3097 1959 3097 \n", "z\n", "M 1959 3584 \n", "Q 2709 3584 3137 3096 \n", "Q 3566 2609 3566 1747 \n", "Q 3566 888 3137 398 \n", "Q 2709 -91 1959 -91 \n", "Q 1206 -91 779 398 \n", "Q 353 888 353 1747 \n", "Q 353 2609 779 3096 \n", "Q 1206 3584 1959 3584 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-63\" d=\"M 3122 3366 \n", "L 3122 2828 \n", "Q 2878 2963 2633 3030 \n", "Q 2388 3097 2138 3097 \n", "Q 1578 3097 1268 2742 \n", "Q 959 2388 959 1747 \n", "Q 959 1106 1268 751 \n", "Q 1578 397 2138 397 \n", "Q 2388 397 2633 464 \n", "Q 2878 531 3122 666 \n", "L 3122 134 \n", "Q 2881 22 2623 -34 \n", "Q 2366 -91 2075 -91 \n", "Q 1284 -91 818 406 \n", "Q 353 903 353 1747 \n", "Q 353 2603 823 3093 \n", "Q 1294 3584 2113 3584 \n", "Q 2378 3584 2631 3529 \n", "Q 2884 3475 3122 3366 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-68\" d=\"M 3513 2113 \n", "L 3513 0 \n", "L 2938 0 \n", "L 2938 2094 \n", "Q 2938 2591 2744 2837 \n", "Q 2550 3084 2163 3084 \n", "Q 1697 3084 1428 2787 \n", "Q 1159 2491 1159 1978 \n", "L 1159 0 \n", "L 581 0 \n", "L 581 4863 \n", "L 1159 4863 \n", "L 1159 2956 \n", "Q 1366 3272 1645 3428 \n", "Q 1925 3584 2291 3584 \n", "Q 2894 3584 3203 3211 \n", "Q 3513 2838 3513 2113 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "      </defs>\n", "      <use xlink:href=\"#DejaVuSans-65\"/>\n", "      <use xlink:href=\"#DejaVuSans-70\" x=\"61.523438\"/>\n", "      <use xlink:href=\"#DejaVuSans-6f\" x=\"125\"/>\n", "      <use xlink:href=\"#DejaVuSans-63\" x=\"186.181641\"/>\n", "      <use xlink:href=\"#DejaVuSans-68\" x=\"241.162109\"/>\n", "     </g>\n", "    </g>\n", "   </g>\n", "   <g id=\"matplotlib.axis_2\">\n", "    <g id=\"ytick_1\">\n", "     <g id=\"line2d_11\">\n", "      <path d=\"M 30.103125 118.508278 \n", "L 225.403125 118.508278 \n", "\" clip-path=\"url(#p1c20af435f)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_12\">\n", "      <defs>\n", "       <path id=\"m7af3c0dc25\" d=\"M 0 0 \n", "L -3.5 0 \n", "\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </defs>\n", "      <g>\n", "       <use xlink:href=\"#m7af3c0dc25\" x=\"30.103125\" y=\"118.508278\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_7\">\n", "      <!-- 0.5 -->\n", "      <g transform=\"translate(7.2 122.307497)scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-2e\" d=\"M 684 794 \n", "L 1344 794 \n", "L 1344 0 \n", "L 684 0 \n", "L 684 794 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "        <path id=\"DejaVuSans-35\" d=\"M 691 4666 \n", "L 3169 4666 \n", "L 3169 4134 \n", "L 1269 4134 \n", "L 1269 2991 \n", "Q 1406 3038 1543 3061 \n", "Q 1681 3084 1819 3084 \n", "Q 2600 3084 3056 2656 \n", "Q 3513 2228 3513 1497 \n", "Q 3513 744 3044 326 \n", "Q 2575 -91 1722 -91 \n", "Q 1428 -91 1123 -41 \n", "Q 819 9 494 109 \n", "L 494 744 \n", "Q 775 591 1075 516 \n", "Q 1375 441 1709 441 \n", "Q 2250 441 2565 725 \n", "Q 2881 1009 2881 1497 \n", "Q 2881 1984 2565 2268 \n", "Q 2250 2553 1709 2553 \n", "Q 1456 2553 1204 2497 \n", "Q 953 2441 691 2322 \n", "L 691 4666 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-35\" x=\"95.410156\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_2\">\n", "     <g id=\"line2d_13\">\n", "      <path d=\"M 30.103125 89.254536 \n", "L 225.403125 89.254536 \n", "\" clip-path=\"url(#p1c20af435f)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_14\">\n", "      <g>\n", "       <use xlink:href=\"#m7af3c0dc25\" x=\"30.103125\" y=\"89.254536\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_8\">\n", "      <!-- 1.0 -->\n", "      <g transform=\"translate(7.2 93.053755)scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-31\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"95.410156\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_3\">\n", "     <g id=\"line2d_15\">\n", "      <path d=\"M 30.103125 60.000795 \n", "L 225.403125 60.000795 \n", "\" clip-path=\"url(#p1c20af435f)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_16\">\n", "      <g>\n", "       <use xlink:href=\"#m7af3c0dc25\" x=\"30.103125\" y=\"60.000795\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_9\">\n", "      <!-- 1.5 -->\n", "      <g transform=\"translate(7.2 63.800013)scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-31\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-35\" x=\"95.410156\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_4\">\n", "     <g id=\"line2d_17\">\n", "      <path d=\"M 30.103125 30.747053 \n", "L 225.403125 30.747053 \n", "\" clip-path=\"url(#p1c20af435f)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_18\">\n", "      <g>\n", "       <use xlink:href=\"#m7af3c0dc25\" x=\"30.103125\" y=\"30.747053\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_10\">\n", "      <!-- 2.0 -->\n", "      <g transform=\"translate(7.2 34.546272)scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-32\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"95.410156\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "   </g>\n", "   <g id=\"line2d_19\">\n", "    <path d=\"M 12.70611 13.5 \n", "L 17.009095 13.754345 \n", "L 21.31208 14.836426 \n", "L 25.615065 24.361527 \n", "L 29.91805 39.974737 \n", "L 30.103125 40.547121 \n", "L 34.40611 111.113074 \n", "L 38.709095 113.040515 \n", "L 43.01208 114.772191 \n", "L 47.315065 116.047027 \n", "L 51.61805 117.246866 \n", "L 51.803125 117.296134 \n", "L 56.10611 123.607737 \n", "L 60.409095 123.926207 \n", "L 64.71208 124.473663 \n", "L 69.015065 124.726244 \n", "L 73.31805 125.188198 \n", "L 73.503125 125.246466 \n", "L 77.80611 127.846078 \n", "L 82.109095 127.785399 \n", "L 86.41208 128.103695 \n", "L 90.715065 128.305754 \n", "L 95.01805 128.490935 \n", "L 95.203125 128.496322 \n", "L 99.50611 130.254251 \n", "L 103.809095 130.180556 \n", "L 108.11208 129.952373 \n", "L 112.415065 130.085415 \n", "L 116.71805 130.173035 \n", "L 116.903125 130.169741 \n", "L 121.20611 131.710169 \n", "L 125.509095 131.117181 \n", "L 129.81208 131.275633 \n", "L 134.115065 131.338926 \n", "L 138.41805 131.437989 \n", "L 138.603125 131.442208 \n", "L 142.90611 132.543735 \n", "L 147.209095 132.314008 \n", "L 151.51208 132.292346 \n", "L 155.815065 132.421935 \n", "L 160.11805 132.382245 \n", "L 160.303125 132.369695 \n", "L 164.60611 133.794293 \n", "L 168.909095 133.344324 \n", "L 173.21208 133.315444 \n", "L 177.515065 133.297713 \n", "L 181.81805 133.410161 \n", "L 182.003125 133.415306 \n", "L 186.30611 134.740323 \n", "L 190.609095 134.418909 \n", "L 194.91208 134.289466 \n", "L 199.215065 134.153561 \n", "L 203.51805 134.122485 \n", "L 203.703125 134.116555 \n", "L 208.00611 134.699385 \n", "L 212.309095 135.023144 \n", "L 216.61208 134.966199 \n", "L 220.915065 134.835233 \n", "L 225.21805 134.89145 \n", "L 225.403125 134.891763 \n", "\" clip-path=\"url(#p1c20af435f)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_20\">\n", "    <path d=\"M 12.70611 139.5 \n", "L 17.009095 138.865972 \n", "L 21.31208 138.379393 \n", "L 25.615065 134.373714 \n", "L 29.91805 128.760849 \n", "L 30.103125 128.547187 \n", "L 34.40611 103.478382 \n", "L 38.709095 102.549457 \n", "L 43.01208 101.679513 \n", "L 47.315065 101.145012 \n", "L 51.61805 100.676864 \n", "L 51.803125 100.657645 \n", "L 56.10611 98.057199 \n", "L 60.409095 98.000678 \n", "L 64.71208 97.826197 \n", "L 69.015065 97.713153 \n", "L 73.31805 97.630582 \n", "L 73.503125 97.607455 \n", "L 77.80611 96.548312 \n", "L 82.109095 96.626951 \n", "L 86.41208 96.512269 \n", "L 90.715065 96.45247 \n", "L 95.01805 96.408728 \n", "L 95.203125 96.410977 \n", "L 99.50611 95.894624 \n", "L 103.809095 95.904454 \n", "L 108.11208 95.907731 \n", "L 112.415065 95.857762 \n", "L 116.71805 95.808121 \n", "L 116.903125 95.8103 \n", "L 121.20611 95.304831 \n", "L 125.509095 95.535834 \n", "L 129.81208 95.499791 \n", "L 134.115065 95.465796 \n", "L 138.41805 95.365777 \n", "L 138.603125 95.362718 \n", "L 142.90611 95.034509 \n", "L 147.209095 95.118063 \n", "L 151.51208 95.073829 \n", "L 155.815065 95.011163 \n", "L 160.11805 95.019765 \n", "L 160.303125 95.027275 \n", "L 164.60611 94.444716 \n", "L 168.909095 94.594622 \n", "L 173.21208 94.628207 \n", "L 177.515065 94.663431 \n", "L 181.81805 94.614773 \n", "L 182.003125 94.608946 \n", "L 186.30611 94.208799 \n", "L 190.609095 94.243204 \n", "L 194.91208 94.308736 \n", "L 199.215065 94.366077 \n", "L 203.51805 94.361162 \n", "L 203.703125 94.36419 \n", "L 208.00611 94.13016 \n", "L 212.309095 93.977797 \n", "L 216.61208 93.981073 \n", "L 220.915065 94.057664 \n", "L 225.21805 94.062334 \n", "L 225.403125 94.065802 \n", "\" clip-path=\"url(#p1c20af435f)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #bf00bf; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_21\">\n", "    <path d=\"M 30.103125 103.255377 \n", "L 51.803125 97.942898 \n", "L 73.503125 96.995076 \n", "L 95.203125 96.462658 \n", "L 116.903125 95.473882 \n", "L 138.603125 95.555792 \n", "L 160.303125 95.07018 \n", "L 182.003125 94.953165 \n", "L 203.703125 94.765941 \n", "L 225.403125 95.111135 \n", "\" clip-path=\"url(#p1c20af435f)\" style=\"fill: none; stroke-dasharray: 9.6,2.4,1.5,2.4; stroke-dashoffset: 0; stroke: #008000; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"patch_3\">\n", "    <path d=\"M 30.**********.8 \n", "L 30.103125 7.2 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_4\">\n", "    <path d=\"M 225.**********.8 \n", "L 225.403125 7.2 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_5\">\n", "    <path d=\"M 30.**********.8 \n", "L 225.**********.8 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_6\">\n", "    <path d=\"M 30.103125 7.2 \n", "L 225.403125 7.2 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"legend_1\">\n", "    <g id=\"patch_7\">\n", "     <path d=\"M 140.634375 59.234375 \n", "L 218.403125 59.234375 \n", "Q 220.403125 59.234375 220.403125 57.234375 \n", "L 220.403125 14.2 \n", "Q 220.403125 12.2 218.403125 12.2 \n", "L 140.634375 12.2 \n", "Q 138.634375 12.2 138.634375 14.2 \n", "L 138.634375 57.234375 \n", "Q 138.634375 59.234375 140.634375 59.234375 \n", "z\n", "\" style=\"fill: #ffffff; opacity: 0.8; stroke: #cccccc; stroke-linejoin: miter\"/>\n", "    </g>\n", "    <g id=\"line2d_22\">\n", "     <path d=\"M 142.634375 20.298438 \n", "L 152.634375 20.298438 \n", "L 162.634375 20.298438 \n", "\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "    </g>\n", "    <g id=\"text_11\">\n", "     <!-- train loss -->\n", "     <g transform=\"translate(170.634375 23.798438)scale(0.1 -0.1)\">\n", "      <defs>\n", "       <path id=\"DejaVuSans-74\" d=\"M 1172 4494 \n", "L 1172 3500 \n", "L 2356 3500 \n", "L 2356 3053 \n", "L 1172 3053 \n", "L 1172 1153 \n", "Q 1172 725 1289 603 \n", "Q 1406 481 1766 481 \n", "L 2356 481 \n", "L 2356 0 \n", "L 1766 0 \n", "Q 1100 0 847 248 \n", "Q 594 497 594 1153 \n", "L 594 3053 \n", "L 172 3053 \n", "L 172 3500 \n", "L 594 3500 \n", "L 594 4494 \n", "L 1172 4494 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-72\" d=\"M 2631 2963 \n", "Q 2534 3019 2420 3045 \n", "Q 2306 3072 2169 3072 \n", "Q 1681 3072 1420 2755 \n", "Q 1159 2438 1159 1844 \n", "L 1159 0 \n", "L 581 0 \n", "L 581 3500 \n", "L 1159 3500 \n", "L 1159 2956 \n", "Q 1341 3275 1631 3429 \n", "Q 1922 3584 2338 3584 \n", "Q 2397 3584 2469 3576 \n", "Q 2541 3569 2628 3553 \n", "L 2631 2963 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-61\" d=\"M 2194 1759 \n", "Q 1497 1759 1228 1600 \n", "Q 959 1441 959 1056 \n", "Q 959 750 1161 570 \n", "Q 1363 391 1709 391 \n", "Q 2188 391 2477 730 \n", "Q 2766 1069 2766 1631 \n", "L 2766 1759 \n", "L 2194 1759 \n", "z\n", "M 3341 1997 \n", "L 3341 0 \n", "L 2766 0 \n", "L 2766 531 \n", "Q 2569 213 2275 61 \n", "Q 1981 -91 1556 -91 \n", "Q 1019 -91 701 211 \n", "Q 384 513 384 1019 \n", "Q 384 1609 779 1909 \n", "Q 1175 2209 1959 2209 \n", "L 2766 2209 \n", "L 2766 2266 \n", "Q 2766 2663 2505 2880 \n", "Q 2244 3097 1772 3097 \n", "Q 1472 3097 1187 3025 \n", "Q 903 2953 641 2809 \n", "L 641 3341 \n", "Q 956 3463 1253 3523 \n", "Q 1550 3584 1831 3584 \n", "Q 2591 3584 2966 3190 \n", "Q 3341 2797 3341 1997 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-69\" d=\"M 603 3500 \n", "L 1178 3500 \n", "L 1178 0 \n", "L 603 0 \n", "L 603 3500 \n", "z\n", "M 603 4863 \n", "L 1178 4863 \n", "L 1178 4134 \n", "L 603 4134 \n", "L 603 4863 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-6e\" d=\"M 3513 2113 \n", "L 3513 0 \n", "L 2938 0 \n", "L 2938 2094 \n", "Q 2938 2591 2744 2837 \n", "Q 2550 3084 2163 3084 \n", "Q 1697 3084 1428 2787 \n", "Q 1159 2491 1159 1978 \n", "L 1159 0 \n", "L 581 0 \n", "L 581 3500 \n", "L 1159 3500 \n", "L 1159 2956 \n", "Q 1366 3272 1645 3428 \n", "Q 1925 3584 2291 3584 \n", "Q 2894 3584 3203 3211 \n", "Q 3513 2838 3513 2113 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-20\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-6c\" d=\"M 603 4863 \n", "L 1178 4863 \n", "L 1178 0 \n", "L 603 0 \n", "L 603 4863 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-73\" d=\"M 2834 3397 \n", "L 2834 2853 \n", "Q 2591 2978 2328 3040 \n", "Q 2066 3103 1784 3103 \n", "Q 1356 3103 1142 2972 \n", "Q 928 2841 928 2578 \n", "Q 928 2378 1081 2264 \n", "Q 1234 2150 1697 2047 \n", "L 1894 2003 \n", "Q 2506 1872 2764 1633 \n", "Q 3022 1394 3022 966 \n", "Q 3022 478 2636 193 \n", "Q 2250 -91 1575 -91 \n", "Q 1294 -91 989 -36 \n", "Q 684 19 347 128 \n", "L 347 722 \n", "Q 666 556 975 473 \n", "Q 1284 391 1588 391 \n", "Q 1994 391 2212 530 \n", "Q 2431 669 2431 922 \n", "Q 2431 1156 2273 1281 \n", "Q 2116 1406 1581 1522 \n", "L 1381 1569 \n", "Q 847 1681 609 1914 \n", "Q 372 2147 372 2553 \n", "Q 372 3047 722 3315 \n", "Q 1072 3584 1716 3584 \n", "Q 2034 3584 2315 3537 \n", "Q 2597 3491 2834 3397 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "      </defs>\n", "      <use xlink:href=\"#DejaVuSans-74\"/>\n", "      <use xlink:href=\"#DejaVuSans-72\" x=\"39.208984\"/>\n", "      <use xlink:href=\"#DejaVuSans-61\" x=\"80.322266\"/>\n", "      <use xlink:href=\"#DejaVuSans-69\" x=\"141.601562\"/>\n", "      <use xlink:href=\"#DejaVuSans-6e\" x=\"169.384766\"/>\n", "      <use xlink:href=\"#DejaVuSans-20\" x=\"232.763672\"/>\n", "      <use xlink:href=\"#DejaVuSans-6c\" x=\"264.550781\"/>\n", "      <use xlink:href=\"#DejaVuSans-6f\" x=\"292.333984\"/>\n", "      <use xlink:href=\"#DejaVuSans-73\" x=\"353.515625\"/>\n", "      <use xlink:href=\"#DejaVuSans-73\" x=\"405.615234\"/>\n", "     </g>\n", "    </g>\n", "    <g id=\"line2d_23\">\n", "     <path d=\"M 142.634375 34.976563 \n", "L 152.634375 34.976563 \n", "L 162.634375 34.976563 \n", "\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #bf00bf; stroke-width: 1.5\"/>\n", "    </g>\n", "    <g id=\"text_12\">\n", "     <!-- train acc -->\n", "     <g transform=\"translate(170.634375 38.476563)scale(0.1 -0.1)\">\n", "      <use xlink:href=\"#DejaVuSans-74\"/>\n", "      <use xlink:href=\"#DejaVuSans-72\" x=\"39.208984\"/>\n", "      <use xlink:href=\"#DejaVuSans-61\" x=\"80.322266\"/>\n", "      <use xlink:href=\"#DejaVuSans-69\" x=\"141.601562\"/>\n", "      <use xlink:href=\"#DejaVuSans-6e\" x=\"169.384766\"/>\n", "      <use xlink:href=\"#DejaVuSans-20\" x=\"232.763672\"/>\n", "      <use xlink:href=\"#DejaVuSans-61\" x=\"264.550781\"/>\n", "      <use xlink:href=\"#DejaVuSans-63\" x=\"325.830078\"/>\n", "      <use xlink:href=\"#DejaVuSans-63\" x=\"380.810547\"/>\n", "     </g>\n", "    </g>\n", "    <g id=\"line2d_24\">\n", "     <path d=\"M 142.634375 49.654688 \n", "L 152.634375 49.654688 \n", "L 162.634375 49.654688 \n", "\" style=\"fill: none; stroke-dasharray: 9.6,2.4,1.5,2.4; stroke-dashoffset: 0; stroke: #008000; stroke-width: 1.5\"/>\n", "    </g>\n", "    <g id=\"text_13\">\n", "     <!-- test acc -->\n", "     <g transform=\"translate(170.634375 53.154688)scale(0.1 -0.1)\">\n", "      <use xlink:href=\"#DejaVuSans-74\"/>\n", "      <use xlink:href=\"#DejaVuSans-65\" x=\"39.208984\"/>\n", "      <use xlink:href=\"#DejaVuSans-73\" x=\"100.732422\"/>\n", "      <use xlink:href=\"#DejaVuSans-74\" x=\"152.832031\"/>\n", "      <use xlink:href=\"#DejaVuSans-20\" x=\"192.041016\"/>\n", "      <use xlink:href=\"#DejaVuSans-61\" x=\"223.828125\"/>\n", "      <use xlink:href=\"#DejaVuSans-63\" x=\"285.107422\"/>\n", "      <use xlink:href=\"#DejaVuSans-63\" x=\"340.087891\"/>\n", "     </g>\n", "    </g>\n", "   </g>\n", "  </g>\n", " </g>\n", " <defs>\n", "  <clipPath id=\"p1c20af435f\">\n", "   <rect x=\"30.103125\" y=\"7.2\" width=\"195.3\" height=\"138.6\"/>\n", "  </clipPath>\n", " </defs>\n", "</svg>\n"], "text/plain": ["<Figure size 350x250 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["lr, num_epochs, batch_size = 0.05, 10, 128\n", "train_iter, test_iter = d2l.load_data_fashion_mnist(batch_size, resize=224)\n", "d2l.train_ch6(net, train_iter, test_iter, num_epochs, lr, d2l.try_gpu())"]}, {"cell_type": "markdown", "id": "eef943e6", "metadata": {"origin_pos": 24}, "source": ["## 小结\n", "\n", "* VGG-11使用可复用的卷积块构造网络。不同的VGG模型可通过每个块中卷积层数量和输出通道数量的差异来定义。\n", "* 块的使用导致网络定义的非常简洁。使用块可以有效地设计复杂的网络。\n", "* 在VGG论文中，Simon<PERSON>和Ziserman尝试了各种架构。特别是他们发现深层且窄的卷积（即$3 \\times 3$）比较浅层且宽的卷积更有效。\n", "\n", "## 练习\n", "\n", "1. 打印层的尺寸时，我们只看到8个结果，而不是11个结果。剩余的3层信息去哪了？\n", "1. 与AlexNet相比，VGG的计算要慢得多，而且它还需要更多的显存。分析出现这种情况的原因。\n", "1. 尝试将Fashion-MNIST数据集图像的高度和宽度从224改为96。这对实验有什么影响？\n", "1. 请参考VGG论文 :cite:<PERSON><PERSON><PERSON>.Zisserman.2014`中的表1构建其他常见模型，如VGG-16或VGG-19。\n"]}, {"cell_type": "markdown", "id": "6f07628d", "metadata": {"origin_pos": 26, "tab": ["pytorch"]}, "source": ["[Discussions](https://discuss.d2l.ai/t/1866)\n"]}], "metadata": {"language_info": {"name": "python"}}, "nbformat": 4, "nbformat_minor": 5}