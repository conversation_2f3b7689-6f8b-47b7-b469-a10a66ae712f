{"cells": [{"cell_type": "markdown", "id": "30c7f234", "metadata": {"origin_pos": 0}, "source": ["# 双向循环神经网络\n", ":label:`sec_bi_rnn`\n", "\n", "在序列学习中，我们以往假设的目标是：\n", "在给定观测的情况下\n", "（例如，在时间序列的上下文中或在语言模型的上下文中），\n", "对下一个输出进行建模。\n", "虽然这是一个典型情景，但不是唯一的。\n", "还可能发生什么其它的情况呢？\n", "我们考虑以下三个在文本序列中填空的任务。\n", "\n", "* 我`___`。\n", "* 我`___`饿了。\n", "* 我`___`饿了，我可以吃半头猪。\n", "\n", "根据可获得的信息量，我们可以用不同的词填空，\n", "如“很高兴”（\"happy\"）、“不”（\"not\"）和“非常”（\"very\"）。\n", "很明显，每个短语的“下文”传达了重要信息（如果有的话），\n", "而这些信息关乎到选择哪个词来填空，\n", "所以无法利用这一点的序列模型将在相关任务上表现不佳。\n", "例如，如果要做好命名实体识别\n", "（例如，识别“Green”指的是“格林先生”还是绿色），\n", "不同长度的上下文范围重要性是相同的。\n", "为了获得一些解决问题的灵感，让我们先迂回到概率图模型。\n", "\n", "## 隐马尔可夫模型中的动态规划\n", "\n", "这一小节是用来说明动态规划问题的，\n", "具体的技术细节对于理解深度学习模型并不重要，\n", "但它有助于我们思考为什么要使用深度学习，\n", "以及为什么要选择特定的架构。\n", "\n", "如果我们想用概率图模型来解决这个问题，\n", "可以设计一个隐变量模型：\n", "在任意时间步$t$，假设存在某个隐变量$h_t$，\n", "通过概率$P(x_t \\mid h_t)$控制我们观测到的$x_t$。\n", "此外，任何$h_t \\to h_{t+1}$转移\n", "都是由一些状态转移概率$P(h_{t+1} \\mid h_{t})$给出。\n", "这个概率图模型就是一个*隐马尔可夫模型*（hidden Markov model，HMM），\n", "如 :numref:`fig_hmm`所示。\n", "\n", "![隐马尔可夫模型](../img/hmm.svg)\n", ":label:`fig_hmm`\n", "\n", "因此，对于有$T$个观测值的序列，\n", "我们在观测状态和隐状态上具有以下联合概率分布：\n", "\n", "$$P(x_1, \\ldots, x_T, h_1, \\ldots, h_T) = \\prod_{t=1}^T P(h_t \\mid h_{t-1}) P(x_t \\mid h_t), \\text{ where } P(h_1 \\mid h_0) = P(h_1).$$\n", ":eqlabel:`eq_hmm_jointP`\n", "\n", "现在，假设我们观测到所有的$x_i$，除了$x_j$，\n", "并且我们的目标是计算$P(x_j \\mid x_{-j})$，\n", "其中$x_{-j} = (x_1, \\ldots, x_{j-1}, x_{j+1}, \\ldots, x_{T})$。\n", "由于$P(x_j \\mid x_{-j})$中没有隐变量，\n", "因此我们考虑对$h_1, \\ldots, h_T$选择构成的\n", "所有可能的组合进行求和。\n", "如果任何$h_i$可以接受$k$个不同的值（有限的状态数），\n", "这意味着我们需要对$k^T$个项求和，\n", "这个任务显然难于登天。\n", "幸运的是，有个巧妙的解决方案：*动态规划*（dynamic programming）。\n", "\n", "要了解动态规划的工作方式，\n", "我们考虑对隐变量$h_1, \\ldots, h_T$的依次求和。\n", "根据 :eqref:`eq_hmm_jointP`，将得出：\n", "\n", "$$\\begin{aligned}\n", "    &P(x_1, \\ldots, x_T) \\\\\n", "    =& \\sum_{h_1, \\ldots, h_T} P(x_1, \\ldots, x_T, h_1, \\ldots, h_T) \\\\\n", "    =& \\sum_{h_1, \\ldots, h_T} \\prod_{t=1}^T P(h_t \\mid h_{t-1}) P(x_t \\mid h_t) \\\\\n", "    =& \\sum_{h_2, \\ldots, h_T} \\underbrace{\\left[\\sum_{h_1} P(h_1) P(x_1 \\mid h_1) P(h_2 \\mid h_1)\\right]}_{\\pi_2(h_2) \\stackrel{\\mathrm{def}}{=}}\n", "    P(x_2 \\mid h_2) \\prod_{t=3}^T P(h_t \\mid h_{t-1}) P(x_t \\mid h_t) \\\\\n", "    =& \\sum_{h_3, \\ldots, h_T} \\underbrace{\\left[\\sum_{h_2} \\pi_2(h_2) P(x_2 \\mid h_2) P(h_3 \\mid h_2)\\right]}_{\\pi_3(h_3)\\stackrel{\\mathrm{def}}{=}}\n", "    P(x_3 \\mid h_3) \\prod_{t=4}^T P(h_t \\mid h_{t-1}) P(x_t \\mid h_t)\\\\\n", "    =& \\dots \\\\\n", "    =& \\sum_{h_T} \\pi_T(h_T) P(x_T \\mid h_T).\n", "\\end{aligned}$$\n", "\n", "通常，我们将*前向递归*（forward recursion）写为：\n", "\n", "$$\\pi_{t+1}(h_{t+1}) = \\sum_{h_t} \\pi_t(h_t) P(x_t \\mid h_t) P(h_{t+1} \\mid h_t).$$\n", "\n", "递归被初始化为$\\pi_1(h_1) = P(h_1)$。\n", "符号简化，也可以写成$\\pi_{t+1} = f(\\pi_t, x_t)$，\n", "其中$f$是一些可学习的函数。\n", "这看起来就像我们在循环神经网络中讨论的隐变量模型中的更新方程。\n", "\n", "与前向递归一样，我们也可以使用后向递归对同一组隐变量求和。这将得到：\n", "\n", "$$\\begin{aligned}\n", "    & P(x_1, \\ldots, x_T) \\\\\n", "     =& \\sum_{h_1, \\ldots, h_T} P(x_1, \\ldots, x_T, h_1, \\ldots, h_T) \\\\\n", "    =& \\sum_{h_1, \\ldots, h_T} \\prod_{t=1}^{T-1} P(h_t \\mid h_{t-1}) P(x_t \\mid h_t) \\cdot P(h_T \\mid h_{T-1}) P(x_T \\mid h_T) \\\\\n", "    =& \\sum_{h_1, \\ldots, h_{T-1}} \\prod_{t=1}^{T-1} P(h_t \\mid h_{t-1}) P(x_t \\mid h_t) \\cdot\n", "    \\underbrace{\\left[\\sum_{h_T} P(h_T \\mid h_{T-1}) P(x_T \\mid h_T)\\right]}_{\\rho_{T-1}(h_{T-1})\\stackrel{\\mathrm{def}}{=}} \\\\\n", "    =& \\sum_{h_1, \\ldots, h_{T-2}} \\prod_{t=1}^{T-2} P(h_t \\mid h_{t-1}) P(x_t \\mid h_t) \\cdot\n", "    \\underbrace{\\left[\\sum_{h_{T-1}} P(h_{T-1} \\mid h_{T-2}) P(x_{T-1} \\mid h_{T-1}) \\rho_{T-1}(h_{T-1}) \\right]}_{\\rho_{T-2}(h_{T-2})\\stackrel{\\mathrm{def}}{=}} \\\\\n", "    =& \\ldots \\\\\n", "    =& \\sum_{h_1} P(h_1) P(x_1 \\mid h_1)\\rho_{1}(h_{1}).\n", "\\end{aligned}$$\n", "\n", "因此，我们可以将*后向递归*（backward recursion）写为：\n", "\n", "$$\\rho_{t-1}(h_{t-1})= \\sum_{h_{t}} P(h_{t} \\mid h_{t-1}) P(x_{t} \\mid h_{t}) \\rho_{t}(h_{t}),$$\n", "\n", "初始化$\\rho_T(h_T) = 1$。\n", "前向和后向递归都允许我们对$T$个隐变量在$\\mathcal{O}(kT)$\n", "（线性而不是指数）时间内对$(h_1, \\ldots, h_T)$的所有值求和。\n", "这是使用图模型进行概率推理的巨大好处之一。\n", "它也是通用消息传递算法 :cite:`Aji.McEliece.2000`的一个非常特殊的例子。\n", "结合前向和后向递归，我们能够计算\n", "\n", "$$P(x_j \\mid x_{-j}) \\propto \\sum_{h_j} \\pi_j(h_j) \\rho_j(h_j) P(x_j \\mid h_j).$$\n", "\n", "因为符号简化的需要，后向递归也可以写为$\\rho_{t-1} = g(\\rho_t, x_t)$，\n", "其中$g$是一个可以学习的函数。\n", "同样，这看起来非常像一个更新方程，\n", "只是不像我们在循环神经网络中看到的那样前向运算，而是后向计算。\n", "事实上，知道未来数据何时可用对隐马尔可夫模型是有益的。\n", "信号处理学家将是否知道未来观测这两种情况区分为内插和外推，\n", "有关更多详细信息，请参阅 :cite:`Doucet.De-Freitas.Gordon.2001`。\n", "\n", "## 双向模型\n", "\n", "如果我们希望在循环神经网络中拥有一种机制，\n", "使之能够提供与隐马尔可夫模型类似的前瞻能力，\n", "我们就需要修改循环神经网络的设计。\n", "幸运的是，这在概念上很容易，\n", "只需要增加一个“从最后一个词元开始从后向前运行”的循环神经网络，\n", "而不是只有一个在前向模式下“从第一个词元开始运行”的循环神经网络。\n", "*双向循环神经网络*（bidirectional RNNs）\n", "添加了反向传递信息的隐藏层，以便更灵活地处理此类信息。\n", " :numref:`fig_birnn`描述了具有单个隐藏层的双向循环神经网络的架构。\n", "\n", "![双向循环神经网络架构](../img/birnn.svg)\n", ":label:`fig_birnn`\n", "\n", "事实上，这与隐马尔可夫模型中的动态规划的前向和后向递归没有太大区别。\n", "其主要区别是，在隐马尔可夫模型中的方程具有特定的统计意义。\n", "双向循环神经网络没有这样容易理解的解释，\n", "我们只能把它们当作通用的、可学习的函数。\n", "这种转变集中体现了现代深度网络的设计原则：\n", "首先使用经典统计模型的函数依赖类型，然后将其参数化为通用形式。\n", "\n", "### 定义\n", "\n", "双向循环神经网络是由 :cite:<PERSON>Schuster.Paliwal.1997`提出的，\n", "关于各种架构的详细讨论请参阅 :cite:`Graves.Schmidhuber.2005`。\n", "让我们看看这样一个网络的细节。\n", "\n", "对于任意时间步$t$，给定一个小批量的输入数据\n", "$\\mathbf{X}_t \\in \\mathbb{R}^{n \\times d}$\n", "（样本数$n$，每个示例中的输入数$d$），\n", "并且令隐藏层激活函数为$\\phi$。\n", "在双向架构中，我们设该时间步的前向和反向隐状态分别为\n", "$\\overrightarrow{\\mathbf{H}}_t  \\in \\mathbb{R}^{n \\times h}$和\n", "$\\overleftarrow{\\mathbf{H}}_t  \\in \\mathbb{R}^{n \\times h}$，\n", "其中$h$是隐藏单元的数目。\n", "前向和反向隐状态的更新如下：\n", "\n", "$$\n", "\\begin{aligned}\n", "\\overrightarrow{\\mathbf{H}}_t &= \\phi(\\mathbf{X}_t \\mathbf{W}_{xh}^{(f)} + \\overrightarrow{\\mathbf{H}}_{t-1} \\mathbf{W}_{hh}^{(f)}  + \\mathbf{b}_h^{(f)}),\\\\\n", "\\overleftarrow{\\mathbf{H}}_t &= \\phi(\\mathbf{X}_t \\mathbf{W}_{xh}^{(b)} + \\overleftarrow{\\mathbf{H}}_{t+1} \\mathbf{W}_{hh}^{(b)}  + \\mathbf{b}_h^{(b)}),\n", "\\end{aligned}\n", "$$\n", "\n", "其中，权重$\\mathbf{W}_{xh}^{(f)} \\in \\mathbb{R}^{d \\times h}, \\mathbf{W}_{hh}^{(f)} \\in \\mathbb{R}^{h \\times h}, \\mathbf{W}_{xh}^{(b)} \\in \\mathbb{R}^{d \\times h}, \\mathbf{W}_{hh}^{(b)} \\in \\mathbb{R}^{h \\times h}$\n", "和偏置$\\mathbf{b}_h^{(f)} \\in \\mathbb{R}^{1 \\times h}, \\mathbf{b}_h^{(b)} \\in \\mathbb{R}^{1 \\times h}$都是模型参数。\n", "\n", "接下来，将前向隐状态$\\overrightarrow{\\mathbf{H}}_t$\n", "和反向隐状态$\\overleftarrow{\\mathbf{H}}_t$连接起来，\n", "获得需要送入输出层的隐状态$\\mathbf{H}_t \\in \\mathbb{R}^{n \\times 2h}$。\n", "在具有多个隐藏层的深度双向循环神经网络中，\n", "该信息作为输入传递到下一个双向层。\n", "最后，输出层计算得到的输出为\n", "$\\mathbf{O}_t \\in \\mathbb{R}^{n \\times q}$（$q$是输出单元的数目）：\n", "\n", "$$\\mathbf{O}_t = \\mathbf{H}_t \\mathbf{W}_{hq} + \\mathbf{b}_q.$$\n", "\n", "这里，权重矩阵$\\mathbf{W}_{hq} \\in \\mathbb{R}^{2h \\times q}$\n", "和偏置$\\mathbf{b}_q \\in \\mathbb{R}^{1 \\times q}$\n", "是输出层的模型参数。\n", "事实上，这两个方向可以拥有不同数量的隐藏单元。\n", "\n", "### 模型的计算代价及其应用\n", "\n", "双向循环神经网络的一个关键特性是：使用来自序列两端的信息来估计输出。\n", "也就是说，我们使用来自过去和未来的观测信息来预测当前的观测。\n", "但是在对下一个词元进行预测的情况中，这样的模型并不是我们所需的。\n", "因为在预测下一个词元时，我们终究无法知道下一个词元的下文是什么，\n", "所以将不会得到很好的精度。\n", "具体地说，在训练期间，我们能够利用过去和未来的数据来估计现在空缺的词；\n", "而在测试期间，我们只有过去的数据，因此精度将会很差。\n", "下面的实验将说明这一点。\n", "\n", "另一个严重问题是，双向循环神经网络的计算速度非常慢。\n", "其主要原因是网络的前向传播需要在双向层中进行前向和后向递归，\n", "并且网络的反向传播还依赖于前向传播的结果。\n", "因此，梯度求解将有一个非常长的链。\n", "\n", "双向层的使用在实践中非常少，并且仅仅应用于部分场合。\n", "例如，填充缺失的单词、词元注释（例如，用于命名实体识别）\n", "以及作为序列处理流水线中的一个步骤对序列进行编码（例如，用于机器翻译）。\n", "在 :numref:`sec_bert`和 :numref:`sec_sentiment_rnn`中，\n", "我们将介绍如何使用双向循环神经网络编码文本序列。\n", "\n", "## (**双向循环神经网络的错误应用**)\n", "\n", "由于双向循环神经网络使用了过去的和未来的数据，\n", "所以我们不能盲目地将这一语言模型应用于任何预测任务。\n", "尽管模型产出的困惑度是合理的，\n", "该模型预测未来词元的能力却可能存在严重缺陷。\n", "我们用下面的示例代码引以为戒，以防在错误的环境中使用它们。\n"]}, {"cell_type": "code", "execution_count": 1, "id": "bf63fc44", "metadata": {"execution": {"iopub.execute_input": "2022-12-07T17:41:58.368063Z", "iopub.status.busy": "2022-12-07T17:41:58.367759Z", "iopub.status.idle": "2022-12-07T17:42:52.658763Z", "shell.execute_reply": "2022-12-07T17:42:52.657858Z"}, "origin_pos": 2, "tab": ["pytorch"]}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["perplexity 1.1, 109857.9 tokens/sec on cuda:0\n", "time traveller<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>\n"]}, {"name": "stdout", "output_type": "stream", "text": ["travellererer<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>\n"]}, {"data": {"image/svg+xml": ["<?xml version=\"1.0\" encoding=\"utf-8\" standalone=\"no\"?>\n", "<!DOCTYPE svg PUBLIC \"-//W3C//DTD SVG 1.1//EN\"\n", "  \"http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd\">\n", "<svg xmlns:xlink=\"http://www.w3.org/1999/xlink\" width=\"252.646875pt\" height=\"183.35625pt\" viewBox=\"0 0 252.**********.35625\" xmlns=\"http://www.w3.org/2000/svg\" version=\"1.1\">\n", " <metadata>\n", "  <rdf:RDF xmlns:dc=\"http://purl.org/dc/elements/1.1/\" xmlns:cc=\"http://creativecommons.org/ns#\" xmlns:rdf=\"http://www.w3.org/1999/02/22-rdf-syntax-ns#\">\n", "   <cc:Work>\n", "    <dc:type rdf:resource=\"http://purl.org/dc/dcmitype/StillImage\"/>\n", "    <dc:date>2022-12-07T17:42:52.623697</dc:date>\n", "    <dc:format>image/svg+xml</dc:format>\n", "    <dc:creator>\n", "     <cc:Agent>\n", "      <dc:title>Matplotlib v3.5.1, https://matplotlib.org/</dc:title>\n", "     </cc:Agent>\n", "    </dc:creator>\n", "   </cc:Work>\n", "  </rdf:RDF>\n", " </metadata>\n", " <defs>\n", "  <style type=\"text/css\">*{stroke-linejoin: round; stroke-linecap: butt}</style>\n", " </defs>\n", " <g id=\"figure_1\">\n", "  <g id=\"patch_1\">\n", "   <path d=\"M 0 183.35625 \n", "L 252.**********.35625 \n", "L 252.646875 0 \n", "L 0 0 \n", "z\n", "\" style=\"fill: #ffffff\"/>\n", "  </g>\n", "  <g id=\"axes_1\">\n", "   <g id=\"patch_2\">\n", "    <path d=\"M 40.**********.8 \n", "L 235.**********.8 \n", "L 235.903125 7.2 \n", "L 40.603125 7.2 \n", "z\n", "\" style=\"fill: #ffffff\"/>\n", "   </g>\n", "   <g id=\"matplotlib.axis_1\">\n", "    <g id=\"xtick_1\">\n", "     <g id=\"line2d_1\">\n", "      <path d=\"M 76.474554 145.8 \n", "L 76.474554 7.2 \n", "\" clip-path=\"url(#pe78bfcb2b3)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_2\">\n", "      <defs>\n", "       <path id=\"mc3e733bec2\" d=\"M 0 0 \n", "L 0 3.5 \n", "\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </defs>\n", "      <g>\n", "       <use xlink:href=\"#mc3e733bec2\" x=\"76.474554\" y=\"145.8\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_1\">\n", "      <!-- 100 -->\n", "      <g transform=\"translate(66.930804 160.398438)scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-31\" d=\"M 794 531 \n", "L 1825 531 \n", "L 1825 4091 \n", "L 703 3866 \n", "L 703 4441 \n", "L 1819 4666 \n", "L 2450 4666 \n", "L 2450 531 \n", "L 3481 531 \n", "L 3481 0 \n", "L 794 0 \n", "L 794 531 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "        <path id=\"DejaVuSans-30\" d=\"M 2034 4250 \n", "Q 1547 4250 1301 3770 \n", "Q 1056 3291 1056 2328 \n", "Q 1056 1369 1301 889 \n", "Q 1547 409 2034 409 \n", "Q 2525 409 2770 889 \n", "Q 3016 1369 3016 2328 \n", "Q 3016 3291 2770 3770 \n", "Q 2525 4250 2034 4250 \n", "z\n", "M 2034 4750 \n", "Q 2819 4750 3233 4129 \n", "Q 3647 3509 3647 2328 \n", "Q 3647 1150 3233 529 \n", "Q 2819 -91 2034 -91 \n", "Q 1250 -91 836 529 \n", "Q 422 1150 422 2328 \n", "Q 422 3509 836 4129 \n", "Q 1250 4750 2034 4750 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-31\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"127.246094\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_2\">\n", "     <g id=\"line2d_3\">\n", "      <path d=\"M 116.331696 145.8 \n", "L 116.331696 7.2 \n", "\" clip-path=\"url(#pe78bfcb2b3)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_4\">\n", "      <g>\n", "       <use xlink:href=\"#mc3e733bec2\" x=\"116.331696\" y=\"145.8\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_2\">\n", "      <!-- 200 -->\n", "      <g transform=\"translate(106.787946 160.398438)scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-32\" d=\"M 1228 531 \n", "L 3431 531 \n", "L 3431 0 \n", "L 469 0 \n", "L 469 531 \n", "Q 828 903 1448 1529 \n", "Q 2069 2156 2228 2338 \n", "Q 2531 2678 2651 2914 \n", "Q 2772 3150 2772 3378 \n", "Q 2772 3750 2511 3984 \n", "Q 2250 4219 1831 4219 \n", "Q 1534 4219 1204 4116 \n", "Q 875 4013 500 3803 \n", "L 500 4441 \n", "Q 881 4594 1212 4672 \n", "Q 1544 4750 1819 4750 \n", "Q 2544 4750 2975 4387 \n", "Q 3406 4025 3406 3419 \n", "Q 3406 3131 3298 2873 \n", "Q 3191 2616 2906 2266 \n", "Q 2828 2175 2409 1742 \n", "Q 1991 1309 1228 531 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-32\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"127.246094\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_3\">\n", "     <g id=\"line2d_5\">\n", "      <path d=\"M 156.188839 145.8 \n", "L 156.188839 7.2 \n", "\" clip-path=\"url(#pe78bfcb2b3)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_6\">\n", "      <g>\n", "       <use xlink:href=\"#mc3e733bec2\" x=\"156.188839\" y=\"145.8\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_3\">\n", "      <!-- 300 -->\n", "      <g transform=\"translate(146.645089 160.398438)scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-33\" d=\"M 2597 2516 \n", "Q 3050 2419 3304 2112 \n", "Q 3559 1806 3559 1356 \n", "Q 3559 666 3084 287 \n", "Q 2609 -91 1734 -91 \n", "Q 1441 -91 1130 -33 \n", "Q 819 25 488 141 \n", "L 488 750 \n", "Q 750 597 1062 519 \n", "Q 1375 441 1716 441 \n", "Q 2309 441 2620 675 \n", "Q 2931 909 2931 1356 \n", "Q 2931 1769 2642 2001 \n", "Q 2353 2234 1838 2234 \n", "L 1294 2234 \n", "L 1294 2753 \n", "L 1863 2753 \n", "Q 2328 2753 2575 2939 \n", "Q 2822 3125 2822 3475 \n", "Q 2822 3834 2567 4026 \n", "Q 2313 4219 1838 4219 \n", "Q 1578 4219 1281 4162 \n", "Q 984 4106 628 3988 \n", "L 628 4550 \n", "Q 988 4650 1302 4700 \n", "Q 1616 4750 1894 4750 \n", "Q 2613 4750 3031 4423 \n", "Q 3450 4097 3450 3541 \n", "Q 3450 3153 3228 2886 \n", "Q 3006 2619 2597 2516 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-33\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"127.246094\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_4\">\n", "     <g id=\"line2d_7\">\n", "      <path d=\"M 196.045982 145.8 \n", "L 196.045982 7.2 \n", "\" clip-path=\"url(#pe78bfcb2b3)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_8\">\n", "      <g>\n", "       <use xlink:href=\"#mc3e733bec2\" x=\"196.045982\" y=\"145.8\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_4\">\n", "      <!-- 400 -->\n", "      <g transform=\"translate(186.502232 160.398438)scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-34\" d=\"M 2419 4116 \n", "L 825 1625 \n", "L 2419 1625 \n", "L 2419 4116 \n", "z\n", "M 2253 4666 \n", "L 3047 4666 \n", "L 3047 1625 \n", "L 3713 1625 \n", "L 3713 1100 \n", "L 3047 1100 \n", "L 3047 0 \n", "L 2419 0 \n", "L 2419 1100 \n", "L 313 1100 \n", "L 313 1709 \n", "L 2253 4666 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-34\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"127.246094\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_5\">\n", "     <g id=\"line2d_9\">\n", "      <path d=\"M 235.**********.8 \n", "L 235.903125 7.2 \n", "\" clip-path=\"url(#pe78bfcb2b3)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_10\">\n", "      <g>\n", "       <use xlink:href=\"#mc3e733bec2\" x=\"235.903125\" y=\"145.8\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_5\">\n", "      <!-- 500 -->\n", "      <g transform=\"translate(226.359375 160.398438)scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-35\" d=\"M 691 4666 \n", "L 3169 4666 \n", "L 3169 4134 \n", "L 1269 4134 \n", "L 1269 2991 \n", "Q 1406 3038 1543 3061 \n", "Q 1681 3084 1819 3084 \n", "Q 2600 3084 3056 2656 \n", "Q 3513 2228 3513 1497 \n", "Q 3513 744 3044 326 \n", "Q 2575 -91 1722 -91 \n", "Q 1428 -91 1123 -41 \n", "Q 819 9 494 109 \n", "L 494 744 \n", "Q 775 591 1075 516 \n", "Q 1375 441 1709 441 \n", "Q 2250 441 2565 725 \n", "Q 2881 1009 2881 1497 \n", "Q 2881 1984 2565 2268 \n", "Q 2250 2553 1709 2553 \n", "Q 1456 2553 1204 2497 \n", "Q 953 2441 691 2322 \n", "L 691 4666 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-35\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"127.246094\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"text_6\">\n", "     <!-- epoch -->\n", "     <g transform=\"translate(123.025 174.076563)scale(0.1 -0.1)\">\n", "      <defs>\n", "       <path id=\"DejaVuSans-65\" d=\"M 3597 1894 \n", "L 3597 1613 \n", "L 953 1613 \n", "Q 991 1019 1311 708 \n", "Q 1631 397 2203 397 \n", "Q 2534 397 2845 478 \n", "Q 3156 559 3463 722 \n", "L 3463 178 \n", "Q 3153 47 2828 -22 \n", "Q 2503 -91 2169 -91 \n", "Q 1331 -91 842 396 \n", "Q 353 884 353 1716 \n", "Q 353 2575 817 3079 \n", "Q 1281 3584 2069 3584 \n", "Q 2775 3584 3186 3129 \n", "Q 3597 2675 3597 1894 \n", "z\n", "M 3022 2063 \n", "Q 3016 2534 2758 2815 \n", "Q 2500 3097 2075 3097 \n", "Q 1594 3097 1305 2825 \n", "Q 1016 2553 972 2059 \n", "L 3022 2063 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-70\" d=\"M 1159 525 \n", "L 1159 -1331 \n", "L 581 -1331 \n", "L 581 3500 \n", "L 1159 3500 \n", "L 1159 2969 \n", "Q 1341 3281 1617 3432 \n", "Q 1894 3584 2278 3584 \n", "Q 2916 3584 3314 3078 \n", "Q 3713 2572 3713 1747 \n", "Q 3713 922 3314 415 \n", "Q 2916 -91 2278 -91 \n", "Q 1894 -91 1617 61 \n", "Q 1341 213 1159 525 \n", "z\n", "M 3116 1747 \n", "Q 3116 2381 2855 2742 \n", "Q 2594 3103 2138 3103 \n", "Q 1681 3103 1420 2742 \n", "Q 1159 2381 1159 1747 \n", "Q 1159 1113 1420 752 \n", "Q 1681 391 2138 391 \n", "Q 2594 391 2855 752 \n", "Q 3116 1113 3116 1747 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-6f\" d=\"M 1959 3097 \n", "Q 1497 3097 1228 2736 \n", "Q 959 2375 959 1747 \n", "Q 959 1119 1226 758 \n", "Q 1494 397 1959 397 \n", "Q 2419 397 2687 759 \n", "Q 2956 1122 2956 1747 \n", "Q 2956 2369 2687 2733 \n", "Q 2419 3097 1959 3097 \n", "z\n", "M 1959 3584 \n", "Q 2709 3584 3137 3096 \n", "Q 3566 2609 3566 1747 \n", "Q 3566 888 3137 398 \n", "Q 2709 -91 1959 -91 \n", "Q 1206 -91 779 398 \n", "Q 353 888 353 1747 \n", "Q 353 2609 779 3096 \n", "Q 1206 3584 1959 3584 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-63\" d=\"M 3122 3366 \n", "L 3122 2828 \n", "Q 2878 2963 2633 3030 \n", "Q 2388 3097 2138 3097 \n", "Q 1578 3097 1268 2742 \n", "Q 959 2388 959 1747 \n", "Q 959 1106 1268 751 \n", "Q 1578 397 2138 397 \n", "Q 2388 397 2633 464 \n", "Q 2878 531 3122 666 \n", "L 3122 134 \n", "Q 2881 22 2623 -34 \n", "Q 2366 -91 2075 -91 \n", "Q 1284 -91 818 406 \n", "Q 353 903 353 1747 \n", "Q 353 2603 823 3093 \n", "Q 1294 3584 2113 3584 \n", "Q 2378 3584 2631 3529 \n", "Q 2884 3475 3122 3366 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-68\" d=\"M 3513 2113 \n", "L 3513 0 \n", "L 2938 0 \n", "L 2938 2094 \n", "Q 2938 2591 2744 2837 \n", "Q 2550 3084 2163 3084 \n", "Q 1697 3084 1428 2787 \n", "Q 1159 2491 1159 1978 \n", "L 1159 0 \n", "L 581 0 \n", "L 581 4863 \n", "L 1159 4863 \n", "L 1159 2956 \n", "Q 1366 3272 1645 3428 \n", "Q 1925 3584 2291 3584 \n", "Q 2894 3584 3203 3211 \n", "Q 3513 2838 3513 2113 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "      </defs>\n", "      <use xlink:href=\"#DejaVuSans-65\"/>\n", "      <use xlink:href=\"#DejaVuSans-70\" x=\"61.523438\"/>\n", "      <use xlink:href=\"#DejaVuSans-6f\" x=\"125\"/>\n", "      <use xlink:href=\"#DejaVuSans-63\" x=\"186.181641\"/>\n", "      <use xlink:href=\"#DejaVuSans-68\" x=\"241.162109\"/>\n", "     </g>\n", "    </g>\n", "   </g>\n", "   <g id=\"matplotlib.axis_2\">\n", "    <g id=\"ytick_1\">\n", "     <g id=\"line2d_11\">\n", "      <path d=\"M 40.603125 109.381543 \n", "L 235.903125 109.381543 \n", "\" clip-path=\"url(#pe78bfcb2b3)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_12\">\n", "      <defs>\n", "       <path id=\"mf35c98c9a4\" d=\"M 0 0 \n", "L -3.5 0 \n", "\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </defs>\n", "      <g>\n", "       <use xlink:href=\"#mf35c98c9a4\" x=\"40.603125\" y=\"109.381543\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_7\">\n", "      <!-- 5 -->\n", "      <g transform=\"translate(27.240625 113.180762)scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-35\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_2\">\n", "     <g id=\"line2d_13\">\n", "      <path d=\"M 40.603125 70.760812 \n", "L 235.903125 70.760812 \n", "\" clip-path=\"url(#pe78bfcb2b3)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_14\">\n", "      <g>\n", "       <use xlink:href=\"#mf35c98c9a4\" x=\"40.603125\" y=\"70.760812\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_8\">\n", "      <!-- 10 -->\n", "      <g transform=\"translate(20.878125 74.56003)scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-31\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"63.623047\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_3\">\n", "     <g id=\"line2d_15\">\n", "      <path d=\"M 40.603125 32.14008 \n", "L 235.903125 32.14008 \n", "\" clip-path=\"url(#pe78bfcb2b3)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_16\">\n", "      <g>\n", "       <use xlink:href=\"#mf35c98c9a4\" x=\"40.603125\" y=\"32.14008\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_9\">\n", "      <!-- 15 -->\n", "      <g transform=\"translate(20.878125 35.939299)scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-31\"/>\n", "       <use xlink:href=\"#DejaVuSans-35\" x=\"63.623047\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"text_10\">\n", "     <!-- perplexity -->\n", "     <g transform=\"translate(14.798437 101.626563)rotate(-90)scale(0.1 -0.1)\">\n", "      <defs>\n", "       <path id=\"DejaVuSans-72\" d=\"M 2631 2963 \n", "Q 2534 3019 2420 3045 \n", "Q 2306 3072 2169 3072 \n", "Q 1681 3072 1420 2755 \n", "Q 1159 2438 1159 1844 \n", "L 1159 0 \n", "L 581 0 \n", "L 581 3500 \n", "L 1159 3500 \n", "L 1159 2956 \n", "Q 1341 3275 1631 3429 \n", "Q 1922 3584 2338 3584 \n", "Q 2397 3584 2469 3576 \n", "Q 2541 3569 2628 3553 \n", "L 2631 2963 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-6c\" d=\"M 603 4863 \n", "L 1178 4863 \n", "L 1178 0 \n", "L 603 0 \n", "L 603 4863 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-78\" d=\"M 3513 3500 \n", "L 2247 1797 \n", "L 3578 0 \n", "L 2900 0 \n", "L 1881 1375 \n", "L 863 0 \n", "L 184 0 \n", "L 1544 1831 \n", "L 300 3500 \n", "L 978 3500 \n", "L 1906 2253 \n", "L 2834 3500 \n", "L 3513 3500 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-69\" d=\"M 603 3500 \n", "L 1178 3500 \n", "L 1178 0 \n", "L 603 0 \n", "L 603 3500 \n", "z\n", "M 603 4863 \n", "L 1178 4863 \n", "L 1178 4134 \n", "L 603 4134 \n", "L 603 4863 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-74\" d=\"M 1172 4494 \n", "L 1172 3500 \n", "L 2356 3500 \n", "L 2356 3053 \n", "L 1172 3053 \n", "L 1172 1153 \n", "Q 1172 725 1289 603 \n", "Q 1406 481 1766 481 \n", "L 2356 481 \n", "L 2356 0 \n", "L 1766 0 \n", "Q 1100 0 847 248 \n", "Q 594 497 594 1153 \n", "L 594 3053 \n", "L 172 3053 \n", "L 172 3500 \n", "L 594 3500 \n", "L 594 4494 \n", "L 1172 4494 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-79\" d=\"M 2059 -325 \n", "Q 1816 -950 1584 -1140 \n", "Q 1353 -1331 966 -1331 \n", "L 506 -1331 \n", "L 506 -850 \n", "L 844 -850 \n", "Q 1081 -850 1212 -737 \n", "Q 1344 -625 1503 -206 \n", "L 1606 56 \n", "L 191 3500 \n", "L 800 3500 \n", "L 1894 763 \n", "L 2988 3500 \n", "L 3597 3500 \n", "L 2059 -325 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "      </defs>\n", "      <use xlink:href=\"#DejaVuSans-70\"/>\n", "      <use xlink:href=\"#DejaVuSans-65\" x=\"63.476562\"/>\n", "      <use xlink:href=\"#DejaVuSans-72\" x=\"125\"/>\n", "      <use xlink:href=\"#DejaVuSans-70\" x=\"166.113281\"/>\n", "      <use xlink:href=\"#DejaVuSans-6c\" x=\"229.589844\"/>\n", "      <use xlink:href=\"#DejaVuSans-65\" x=\"257.373047\"/>\n", "      <use xlink:href=\"#DejaVuSans-78\" x=\"317.146484\"/>\n", "      <use xlink:href=\"#DejaVuSans-69\" x=\"376.326172\"/>\n", "      <use xlink:href=\"#DejaVuSans-74\" x=\"404.109375\"/>\n", "      <use xlink:href=\"#DejaVuSans-79\" x=\"443.318359\"/>\n", "     </g>\n", "    </g>\n", "   </g>\n", "   <g id=\"line2d_17\">\n", "    <path d=\"M 40.603125 13.5 \n", "L 44.588839 15.84531 \n", "L 48.574554 26.700631 \n", "L 52.560268 80.828784 \n", "L 56.545982 117.115557 \n", "L 60.531696 130.374292 \n", "L 64.517411 135.756064 \n", "L 68.503125 136.838245 \n", "L 72.488839 137.748887 \n", "L 76.474554 137.986829 \n", "L 80.460268 138.329083 \n", "L 84.445982 138.462958 \n", "L 88.431696 138.572314 \n", "L 92.417411 137.685909 \n", "L 96.403125 138.591561 \n", "L 100.388839 138.759833 \n", "L 104.374554 138.282826 \n", "L 108.360268 138.910655 \n", "L 112.345982 138.942603 \n", "L 116.331696 138.871342 \n", "L 120.317411 139.032117 \n", "L 124.303125 139.020383 \n", "L 128.288839 139.061985 \n", "L 132.274554 139.07135 \n", "L 136.260268 139.06746 \n", "L 140.245982 138.921192 \n", "L 144.231696 139.073391 \n", "L 148.217411 139.152406 \n", "L 152.203125 139.189894 \n", "L 156.188839 139.198209 \n", "L 160.174554 139.131047 \n", "L 164.160268 139.165501 \n", "L 168.145982 139.175168 \n", "L 172.131696 139.192133 \n", "L 176.117411 139.170881 \n", "L 180.103125 139.182167 \n", "L 184.088839 139.206781 \n", "L 188.074554 139.293178 \n", "L 192.060268 139.251536 \n", "L 196.045982 139.361735 \n", "L 200.031696 139.375659 \n", "L 204.017411 139.377308 \n", "L 208.003125 139.371441 \n", "L 211.988839 139.357897 \n", "L 215.974554 139.333535 \n", "L 219.960268 139.410165 \n", "L 223.945982 139.394944 \n", "L 227.931696 139.494052 \n", "L 231.917411 139.480756 \n", "L 235.903125 139.5 \n", "\" clip-path=\"url(#pe78bfcb2b3)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_3\">\n", "    <path d=\"M 40.**********.8 \n", "L 40.603125 7.2 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_4\">\n", "    <path d=\"M 235.**********.8 \n", "L 235.903125 7.2 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_5\">\n", "    <path d=\"M 40.**********.8 \n", "L 235.**********.8 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_6\">\n", "    <path d=\"M 40.603125 7.2 \n", "L 235.903125 7.2 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"legend_1\">\n", "    <g id=\"patch_7\">\n", "     <path d=\"M 173.628125 29.878125 \n", "L 228.903125 29.878125 \n", "Q 230.903125 29.878125 230.903125 27.878125 \n", "L 230.903125 14.2 \n", "Q 230.903125 12.2 228.903125 12.2 \n", "L 173.628125 12.2 \n", "Q 171.628125 12.2 171.628125 14.2 \n", "L 171.628125 27.878125 \n", "Q 171.628125 29.878125 173.628125 29.878125 \n", "z\n", "\" style=\"fill: #ffffff; opacity: 0.8; stroke: #cccccc; stroke-linejoin: miter\"/>\n", "    </g>\n", "    <g id=\"line2d_18\">\n", "     <path d=\"M 175.628125 20.298438 \n", "L 185.628125 20.298438 \n", "L 195.628125 20.298438 \n", "\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "    </g>\n", "    <g id=\"text_11\">\n", "     <!-- train -->\n", "     <g transform=\"translate(203.628125 23.798438)scale(0.1 -0.1)\">\n", "      <defs>\n", "       <path id=\"DejaVuSans-61\" d=\"M 2194 1759 \n", "Q 1497 1759 1228 1600 \n", "Q 959 1441 959 1056 \n", "Q 959 750 1161 570 \n", "Q 1363 391 1709 391 \n", "Q 2188 391 2477 730 \n", "Q 2766 1069 2766 1631 \n", "L 2766 1759 \n", "L 2194 1759 \n", "z\n", "M 3341 1997 \n", "L 3341 0 \n", "L 2766 0 \n", "L 2766 531 \n", "Q 2569 213 2275 61 \n", "Q 1981 -91 1556 -91 \n", "Q 1019 -91 701 211 \n", "Q 384 513 384 1019 \n", "Q 384 1609 779 1909 \n", "Q 1175 2209 1959 2209 \n", "L 2766 2209 \n", "L 2766 2266 \n", "Q 2766 2663 2505 2880 \n", "Q 2244 3097 1772 3097 \n", "Q 1472 3097 1187 3025 \n", "Q 903 2953 641 2809 \n", "L 641 3341 \n", "Q 956 3463 1253 3523 \n", "Q 1550 3584 1831 3584 \n", "Q 2591 3584 2966 3190 \n", "Q 3341 2797 3341 1997 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-6e\" d=\"M 3513 2113 \n", "L 3513 0 \n", "L 2938 0 \n", "L 2938 2094 \n", "Q 2938 2591 2744 2837 \n", "Q 2550 3084 2163 3084 \n", "Q 1697 3084 1428 2787 \n", "Q 1159 2491 1159 1978 \n", "L 1159 0 \n", "L 581 0 \n", "L 581 3500 \n", "L 1159 3500 \n", "L 1159 2956 \n", "Q 1366 3272 1645 3428 \n", "Q 1925 3584 2291 3584 \n", "Q 2894 3584 3203 3211 \n", "Q 3513 2838 3513 2113 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "      </defs>\n", "      <use xlink:href=\"#DejaVuSans-74\"/>\n", "      <use xlink:href=\"#DejaVuSans-72\" x=\"39.208984\"/>\n", "      <use xlink:href=\"#DejaVuSans-61\" x=\"80.322266\"/>\n", "      <use xlink:href=\"#DejaVuSans-69\" x=\"141.601562\"/>\n", "      <use xlink:href=\"#DejaVuSans-6e\" x=\"169.384766\"/>\n", "     </g>\n", "    </g>\n", "   </g>\n", "  </g>\n", " </g>\n", " <defs>\n", "  <clipPath id=\"pe78bfcb2b3\">\n", "   <rect x=\"40.603125\" y=\"7.2\" width=\"195.3\" height=\"138.6\"/>\n", "  </clipPath>\n", " </defs>\n", "</svg>\n"], "text/plain": ["<Figure size 350x250 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["import torch\n", "from torch import nn\n", "from d2l import torch as d2l\n", "\n", "# 加载数据\n", "batch_size, num_steps, device = 32, 35, d2l.try_gpu()\n", "train_iter, vocab = d2l.load_data_time_machine(batch_size, num_steps)\n", "# 通过设置“bidirective=True”来定义双向LSTM模型\n", "vocab_size, num_hiddens, num_layers = len(vocab), 256, 2\n", "num_inputs = vocab_size\n", "lstm_layer = nn.LSTM(num_inputs, num_hiddens, num_layers, bidirectional=True)\n", "model = d2l.RNNModel(lstm_layer, len(vocab))\n", "model = model.to(device)\n", "# 训练模型\n", "num_epochs, lr = 500, 1\n", "d2l.train_ch8(model, train_iter, vocab, lr, num_epochs, device)"]}, {"cell_type": "markdown", "id": "2f0bf1b3", "metadata": {"origin_pos": 4}, "source": ["上述结果显然令人瞠目结舌。\n", "关于如何更有效地使用双向循环神经网络的讨论，\n", "请参阅 :numref:`sec_sentiment_rnn`中的情感分类应用。\n", "\n", "## 小结\n", "\n", "* 在双向循环神经网络中，每个时间步的隐状态由当前时间步的前后数据同时决定。\n", "* 双向循环神经网络与概率图模型中的“前向-后向”算法具有相似性。\n", "* 双向循环神经网络主要用于序列编码和给定双向上下文的观测估计。\n", "* 由于梯度链更长，因此双向循环神经网络的训练代价非常高。\n", "\n", "## 练习\n", "\n", "1. 如果不同方向使用不同数量的隐藏单位，$\\mathbf{H_t}$的形状会发生怎样的变化？\n", "1. 设计一个具有多个隐藏层的双向循环神经网络。\n", "1. 在自然语言中一词多义很常见。例如，“bank”一词在不同的上下文“i went to the bank to deposit cash”和“i went to the bank to sit down”中有不同的含义。如何设计一个神经网络模型，使其在给定上下文序列和单词的情况下，返回该单词在此上下文中的向量表示？哪种类型的神经网络架构更适合处理一词多义？\n"]}, {"cell_type": "markdown", "id": "a8db0fcd", "metadata": {"origin_pos": 6, "tab": ["pytorch"]}, "source": ["[Discussions](https://discuss.d2l.ai/t/2773)\n"]}], "metadata": {"language_info": {"name": "python"}}, "nbformat": 4, "nbformat_minor": 5}