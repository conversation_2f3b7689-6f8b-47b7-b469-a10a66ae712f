{"cells": [{"cell_type": "markdown", "id": "2886088f", "metadata": {"origin_pos": 0}, "source": ["## 英汉术语对照\n", "\n", "鞍点，saddle point\n", "\n", "变换，transform\n", "\n", "编码器，encoder\n", "\n", "标签，label\n", "\n", "步幅，stride\n", "\n", "参数，parameter\n", "\n", "长短期记忆网络，long short-term memory (LSTM)\n", "\n", "超参数，hyperparameter\n", "\n", "层序softmax，hierarchical softmax\n", "\n", "查准率，precision\n", "\n", "成本，cost\n", "\n", "词表，vocabulary\n", "\n", "词嵌入，word embedding\n", "\n", "词向量，word vector\n", "\n", "词元，token\n", "\n", "词元分析器，tokenizer\n", "\n", "词元化，tokenize\n", "\n", "汇聚层，pooling layer\n", "\n", "稠密，dense\n", "\n", "大小，size\n", "\n", "导入，import\n", "\n", "轮，epoch\n", "\n", "暂退法，dropout\n", "\n", "动量法，momentum (method)\n", "\n", "独立同分布，independent and identically distributed (i.i.d.)\n", "\n", "端到端，end-to-end\n", "\n", "多层感知机，multilayer perceptron\n", "\n", "多头注意力，multi-head attention\n", "\n", "二元分类，binary classification\n", "\n", "二元，bigram\n", "\n", "子采样，subsample\n", "\n", "发散，diverge\n", "\n", "泛化，generalization\n", "\n", "泛化误差，generalization error\n", "\n", "方差，variance\n", "\n", "分类，classification\n", "\n", "分类器，classifier\n", "\n", "负采样，negative sampling\n", "\n", "感受野，receptive field\n", "\n", "格拉姆矩阵，Gram matrix\n", "\n", "共现，co-occurrence\n", "\n", "广播，broadcast\n", "\n", "规范化，normalization\n", "\n", "过拟合，overfitting\n", "\n", "核回归，kernel regression\n", "\n", "恒等映射，identity mapping\n", "\n", "假设，hypothesis\n", "\n", "基准，baseline\n", "\n", "激活函数，activation function\n", "\n", "解码器，decoder\n", "\n", "近似法，approximate method\n", "\n", "经验风险最小化，empirical risk minimization\n", "\n", "局部最小值，local minimum\n", "\n", "卷积核，convolutional kernel\n", "\n", "卷积神经网络，convolutional neural network\n", "\n", "决策边界，decision boundary\n", "\n", "均值，mean\n", "\n", "均方误差，mean squared error\n", "\n", "均匀采样，uniform sampling\n", "\n", "块，block\n", "\n", "困惑度，perplexity\n", "\n", "拉普拉斯平滑，Laplace smoothing\n", "\n", "连结，concatenate\n", "\n", "类，class\n", "\n", "交叉熵，cross-entropy\n", "\n", "连续词袋，continous bag-of-words (CBOW)\n", "\n", "零张量，zero tensor\n", "\n", "流水线，pipeline\n", "\n", "滤波器，filter\n", "\n", "门控循环单元，gated recurrent units (GRU)\n", "\n", "目标检测，object detection\n", "\n", "偏置，bias\n", "\n", "偏导数，partial derivative\n", "\n", "偏移量，offset\n", "\n", "批量，batch\n", "\n", "齐普夫定律，<PERSON><PERSON><PERSON>'s law\n", "\n", "欠拟合，underfitting\n", "\n", "情感分析，sentiment analysis\n", "\n", "全连接层，fully-connected layer\n", "\n", "权重，weight\n", "\n", "三元，trigram\n", "\n", "上采样，upsample\n", "\n", "上下文变量，context variable\n", "\n", "上下文窗口，context window\n", "\n", "上下文词，context word\n", "\n", "上下文向量，context vector\n", "\n", "实例/示例，instance\n", "\n", "收敛，converge\n", "\n", "属性，property\n", "\n", "数值方法，numerical method\n", "\n", "数据集，dataset\n", "\n", "数据示例，data instance\n", "\n", "数据样例，data example\n", "\n", "顺序分区，sequential partitioning\n", "\n", "softmax回归，softmax regression\n", "\n", "随机采样，random sampling\n", "\n", "损失函数，loss function\n", "\n", "双向循环神经网络，bidirectional recurrent neural network\n", "\n", "特征，feature\n", "\n", "特征图，feature map\n", "\n", "特征值，eigenvalue\n", "\n", "梯度，gradient\n", "\n", "梯度裁剪，gradient clipping\n", "\n", "梯度消失，vanishing gradients\n", "\n", "填充，padding\n", "\n", "跳元模型，skip-gram model\n", "\n", "调参，tune hyperparameter\n", "\n", "停用词，stop words\n", "\n", "通道，channel\n", "\n", "凸优化，convex optimization\n", "\n", "图像，image\n", "\n", "未知词元，unknown token\n", "\n", "无偏估计，unbiased estimate\n", "\n", "误差，error\n", "\n", "小批量，minibatch\n", "\n", "小批量梯度，minibatch gradient\n", "\n", "线性模型，linear model\n", "\n", "线性回归，linear regression\n", "\n", "协同过滤，collaborative filtering\n", "\n", "学习率，learning rate\n", "\n", "训练误差，training error\n", "\n", "循环神经网络，recurrent neural network (RNN)\n", "\n", "样例，example\n", "\n", "一维梯度下降，gradient descent in one-dimensional space\n", "\n", "一元，unigram\n", "\n", "隐藏变量，hidden variable\n", "\n", "隐藏层，hidden layer\n", "\n", "优化器，optimizer\n", "\n", "语料库，corpus\n", "\n", "运算符，operator\n", "\n", "自注意力，self-attention\n", "\n", "真实值，ground truth\n", "\n", "指标，metric\n", "\n", "支持向量机，support vector machine\n", "\n", "注意力机制，attention mechanism\n", "\n", "注意力模型，attention model\n", "\n", "注意力提示，attention cue\n", "\n", "准确率/精度，accuracy\n"]}], "metadata": {"language_info": {"name": "python"}}, "nbformat": 4, "nbformat_minor": 5}