{"cells": [{"cell_type": "markdown", "id": "edbfc2b5", "metadata": {"origin_pos": 0}, "source": ["# 循环神经网络\n", ":label:`chap_rnn`\n", "\n", "到目前为止，我们遇到过两种类型的数据：表格数据和图像数据。\n", "对于图像数据，我们设计了专门的卷积神经网络架构来为这类特殊的数据结构建模。\n", "换句话说，如果我们拥有一张图像，我们需要有效地利用其像素位置，\n", "假若我们对图像中的像素位置进行重排，就会对图像中内容的推断造成极大的困难。\n", "\n", "最重要的是，到目前为止我们默认数据都来自于某种分布，\n", "并且所有样本都是独立同分布的\n", "（independently and identically distributed，i.i.d.）。\n", "然而，大多数的数据并非如此。\n", "例如，文章中的单词是按顺序写的，如果顺序被随机地重排，就很难理解文章原始的意思。\n", "同样，视频中的图像帧、对话中的音频信号以及网站上的浏览行为都是有顺序的。\n", "因此，针对此类数据而设计特定模型，可能效果会更好。\n", "\n", "另一个问题来自这样一个事实：\n", "我们不仅仅可以接收一个序列作为输入，而是还可能期望继续猜测这个序列的后续。\n", "例如，一个任务可以是继续预测$2, 4, 6, 8, 10, \\ldots$。\n", "这在时间序列分析中是相当常见的，可以用来预测股市的波动、\n", "患者的体温曲线或者赛车所需的加速度。\n", "同理，我们需要能够处理这些数据的特定模型。\n", "\n", "简言之，如果说卷积神经网络可以有效地处理空间信息，\n", "那么本章的*循环神经网络*（recurrent neural network，RNN）则可以更好地处理序列信息。\n", "循环神经网络通过引入状态变量存储过去的信息和当前的输入，从而可以确定当前的输出。\n", "\n", "许多使用循环网络的例子都是基于文本数据的，因此我们将在本章中重点介绍语言模型。\n", "在对序列数据进行更详细的回顾之后，我们将介绍文本预处理的实用技术。\n", "然后，我们将讨论语言模型的基本概念，并将此讨论作为循环神经网络设计的灵感。\n", "最后，我们描述了循环神经网络的梯度计算方法，以探讨训练此类网络时可能遇到的问题。\n", "\n", ":begin_tab:toc\n", " - [sequence](sequence.ipynb)\n", " - [text-preprocessing](text-preprocessing.ipynb)\n", " - [language-models-and-dataset](language-models-and-dataset.ipynb)\n", " - [rnn](rnn.ipynb)\n", " - [rnn-scratch](rnn-scratch.ipynb)\n", " - [rnn-concise](rnn-concise.ipynb)\n", " - [bptt](bptt.ipynb)\n", ":end_tab:\n"]}], "metadata": {"language_info": {"name": "python"}}, "nbformat": 4, "nbformat_minor": 5}