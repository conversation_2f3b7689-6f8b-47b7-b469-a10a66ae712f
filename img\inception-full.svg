<?xml version="1.0" encoding="UTF-8"?>
<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="111pt" height="329pt" viewBox="0 0 111 329" version="1.1">
<defs>
<g>
<symbol overflow="visible" id="glyph0-0">
<path style="stroke:none;" d="M 1.125 0 L 1.125 -5.625 L 5.625 -5.625 L 5.625 0 Z M 1.265625 -0.140625 L 5.484375 -0.140625 L 5.484375 -5.484375 L 1.265625 -5.484375 Z M 1.265625 -0.140625 "/>
</symbol>
<symbol overflow="visible" id="glyph0-1">
<path style="stroke:none;" d="M 0.375 -1.703125 L 1.171875 -1.8125 C 1.265625 -1.363281 1.414062 -1.039062 1.625 -0.84375 C 1.84375 -0.644531 2.113281 -0.546875 2.4375 -0.546875 C 2.800781 -0.546875 3.109375 -0.671875 3.359375 -0.921875 C 3.617188 -1.179688 3.75 -1.503906 3.75 -1.890625 C 3.75 -2.253906 3.628906 -2.550781 3.390625 -2.78125 C 3.160156 -3.019531 2.863281 -3.140625 2.5 -3.140625 C 2.34375 -3.140625 2.15625 -3.109375 1.9375 -3.046875 L 2.03125 -3.75 C 2.082031 -3.738281 2.125 -3.734375 2.15625 -3.734375 C 2.488281 -3.734375 2.789062 -3.820312 3.0625 -4 C 3.332031 -4.175781 3.46875 -4.445312 3.46875 -4.8125 C 3.46875 -5.101562 3.367188 -5.34375 3.171875 -5.53125 C 2.972656 -5.71875 2.71875 -5.8125 2.40625 -5.8125 C 2.101562 -5.8125 1.847656 -5.710938 1.640625 -5.515625 C 1.441406 -5.328125 1.3125 -5.039062 1.25 -4.65625 L 0.453125 -4.796875 C 0.554688 -5.328125 0.773438 -5.738281 1.109375 -6.03125 C 1.453125 -6.320312 1.878906 -6.46875 2.390625 -6.46875 C 2.742188 -6.46875 3.066406 -6.390625 3.359375 -6.234375 C 3.660156 -6.085938 3.890625 -5.882812 4.046875 -5.625 C 4.203125 -5.363281 4.28125 -5.085938 4.28125 -4.796875 C 4.28125 -4.515625 4.203125 -4.257812 4.046875 -4.03125 C 3.898438 -3.800781 3.679688 -3.617188 3.390625 -3.484375 C 3.773438 -3.398438 4.070312 -3.21875 4.28125 -2.9375 C 4.488281 -2.664062 4.59375 -2.320312 4.59375 -1.90625 C 4.59375 -1.34375 4.382812 -0.863281 3.96875 -0.46875 C 3.5625 -0.0820312 3.046875 0.109375 2.421875 0.109375 C 1.859375 0.109375 1.390625 -0.0546875 1.015625 -0.390625 C 0.640625 -0.722656 0.425781 -1.160156 0.375 -1.703125 Z M 0.375 -1.703125 "/>
</symbol>
<symbol overflow="visible" id="glyph0-2">
<path style="stroke:none;" d=""/>
</symbol>
<symbol overflow="visible" id="glyph0-3">
<path style="stroke:none;" d="M 0.421875 -5.59375 L 0.421875 -6.359375 L 4.59375 -6.359375 L 4.59375 -5.75 C 4.1875 -5.3125 3.78125 -4.726562 3.375 -4 C 2.96875 -3.28125 2.65625 -2.535156 2.4375 -1.765625 C 2.28125 -1.234375 2.179688 -0.644531 2.140625 0 L 1.328125 0 C 1.335938 -0.507812 1.4375 -1.117188 1.625 -1.828125 C 1.8125 -2.546875 2.082031 -3.238281 2.4375 -3.90625 C 2.800781 -4.570312 3.179688 -5.132812 3.578125 -5.59375 Z M 0.421875 -5.59375 "/>
</symbol>
<symbol overflow="visible" id="glyph0-4">
<path style="stroke:none;" d="M 3.359375 0 L 2.5625 0 L 2.5625 -5.046875 C 2.375 -4.859375 2.125 -4.671875 1.8125 -4.484375 C 1.5 -4.304688 1.222656 -4.175781 0.984375 -4.09375 L 0.984375 -4.859375 C 1.421875 -5.054688 1.804688 -5.300781 2.140625 -5.59375 C 2.472656 -5.894531 2.707031 -6.1875 2.84375 -6.46875 L 3.359375 -6.46875 Z M 3.359375 0 "/>
</symbol>
<symbol overflow="visible" id="glyph0-5">
<path style="stroke:none;" d="M 4.53125 -0.765625 L 4.53125 0 L 0.265625 0 C 0.265625 -0.1875 0.296875 -0.367188 0.359375 -0.546875 C 0.472656 -0.835938 0.648438 -1.125 0.890625 -1.40625 C 1.128906 -1.6875 1.472656 -2.007812 1.921875 -2.375 C 2.617188 -2.957031 3.085938 -3.414062 3.328125 -3.75 C 3.578125 -4.082031 3.703125 -4.398438 3.703125 -4.703125 C 3.703125 -5.015625 3.585938 -5.273438 3.359375 -5.484375 C 3.140625 -5.703125 2.851562 -5.8125 2.5 -5.8125 C 2.113281 -5.8125 1.804688 -5.695312 1.578125 -5.46875 C 1.347656 -5.238281 1.234375 -4.921875 1.234375 -4.515625 L 0.421875 -4.609375 C 0.472656 -5.210938 0.679688 -5.671875 1.046875 -5.984375 C 1.410156 -6.304688 1.898438 -6.46875 2.515625 -6.46875 C 3.128906 -6.46875 3.613281 -6.296875 3.96875 -5.953125 C 4.332031 -5.609375 4.515625 -5.1875 4.515625 -4.6875 C 4.515625 -4.425781 4.460938 -4.171875 4.359375 -3.921875 C 4.253906 -3.671875 4.078125 -3.40625 3.828125 -3.125 C 3.585938 -2.851562 3.1875 -2.476562 2.625 -2 C 2.144531 -1.601562 1.835938 -1.332031 1.703125 -1.1875 C 1.566406 -1.039062 1.457031 -0.898438 1.375 -0.765625 Z M 4.53125 -0.765625 "/>
</symbol>
<symbol overflow="visible" id="glyph0-6">
<path style="stroke:none;" d="M 0.375 -1.6875 L 1.203125 -1.765625 C 1.265625 -1.359375 1.40625 -1.050781 1.625 -0.84375 C 1.851562 -0.644531 2.125 -0.546875 2.4375 -0.546875 C 2.820312 -0.546875 3.144531 -0.6875 3.40625 -0.96875 C 3.675781 -1.257812 3.8125 -1.640625 3.8125 -2.109375 C 3.8125 -2.566406 3.679688 -2.925781 3.421875 -3.1875 C 3.171875 -3.445312 2.84375 -3.578125 2.4375 -3.578125 C 2.175781 -3.578125 1.941406 -3.515625 1.734375 -3.390625 C 1.535156 -3.273438 1.375 -3.128906 1.25 -2.953125 L 0.515625 -3.046875 L 1.140625 -6.359375 L 4.34375 -6.359375 L 4.34375 -5.59375 L 1.765625 -5.59375 L 1.421875 -3.875 C 1.804688 -4.132812 2.210938 -4.265625 2.640625 -4.265625 C 3.203125 -4.265625 3.675781 -4.070312 4.0625 -3.6875 C 4.445312 -3.300781 4.640625 -2.800781 4.640625 -2.1875 C 4.640625 -1.601562 4.472656 -1.097656 4.140625 -0.671875 C 3.722656 -0.148438 3.15625 0.109375 2.4375 0.109375 C 1.851562 0.109375 1.375 -0.0507812 1 -0.375 C 0.632812 -0.707031 0.425781 -1.144531 0.375 -1.6875 Z M 0.375 -1.6875 "/>
</symbol>
<symbol overflow="visible" id="glyph1-0">
<path style="stroke:none;" d=""/>
</symbol>
<symbol overflow="visible" id="glyph1-1">
<path style="stroke:none;" d="M 3.75 -2.25 L 5.53125 -4.03125 C 5.625 -4.140625 5.65625 -4.15625 5.65625 -4.21875 C 5.65625 -4.328125 5.5625 -4.40625 5.46875 -4.40625 C 5.40625 -4.40625 5.359375 -4.375 5.28125 -4.28125 L 3.484375 -2.5 L 1.6875 -4.28125 C 1.59375 -4.390625 1.5625 -4.40625 1.5 -4.40625 C 1.40625 -4.40625 1.3125 -4.328125 1.3125 -4.21875 C 1.3125 -4.15625 1.359375 -4.109375 1.4375 -4.03125 L 3.21875 -2.25 L 1.4375 -0.453125 C 1.34375 -0.375 1.3125 -0.3125 1.3125 -0.265625 C 1.3125 -0.15625 1.40625 -0.078125 1.5 -0.078125 C 1.5625 -0.078125 1.59375 -0.09375 1.6875 -0.203125 L 3.484375 -1.984375 L 5.28125 -0.203125 C 5.359375 -0.109375 5.421875 -0.078125 5.46875 -0.078125 C 5.578125 -0.078125 5.65625 -0.15625 5.65625 -0.265625 C 5.65625 -0.328125 5.625 -0.34375 5.53125 -0.453125 Z M 3.75 -2.25 "/>
</symbol>
<symbol overflow="visible" id="glyph2-0">
<path style="stroke:none;" d="M 1.5 0 L 1.5 -7.5 L 7.5 -7.5 L 7.5 0 Z M 1.6875 -0.1875 L 7.3125 -0.1875 L 7.3125 -7.3125 L 1.6875 -7.3125 Z M 1.6875 -0.1875 "/>
</symbol>
<symbol overflow="visible" id="glyph2-1">
<path style="stroke:none;" d=""/>
</symbol>
<symbol overflow="visible" id="glyph3-0">
<path style="stroke:none;" d=""/>
</symbol>
<symbol overflow="visible" id="glyph3-1">
<path style="stroke:none;" d="M 0.546875 -3.84375 L 0.546875 -3.328125 L 1.40625 -3.328125 L 1.40625 -0.203125 L 0.5625 -0.171875 L 0.640625 0.390625 C 1.90625 0.3125 2.953125 0.21875 3.8125 0.125 L 3.8125 0.921875 L 4.4375 0.921875 L 4.4375 0.046875 C 4.609375 0.015625 4.796875 -0.015625 4.953125 -0.046875 L 4.953125 -0.5625 C 4.796875 -0.53125 4.609375 -0.5 4.4375 -0.484375 L 4.4375 -3.328125 L 8.484375 -3.328125 L 8.484375 -3.84375 Z M 1.984375 -0.234375 L 1.984375 -1.015625 L 3.8125 -1.015625 L 3.8125 -0.390625 C 3.265625 -0.328125 2.65625 -0.28125 1.984375 -0.234375 Z M 1.984375 -1.484375 L 1.984375 -2.15625 L 3.8125 -2.15625 L 3.8125 -1.484375 Z M 1.984375 -2.640625 L 1.984375 -3.328125 L 3.8125 -3.328125 L 3.8125 -2.640625 Z M 1.6875 -7.03125 L 1.6875 -4.4375 L 7.375 -4.4375 L 7.375 -7.03125 Z M 6.75 -4.90625 L 2.296875 -4.90625 L 2.296875 -5.515625 L 6.75 -5.515625 Z M 2.296875 -5.96875 L 2.296875 -6.546875 L 6.75 -6.546875 L 6.75 -5.96875 Z M 5.1875 -2.1875 C 5.375 -1.53125 5.703125 -0.9375 6.203125 -0.421875 C 5.828125 -0.09375 5.359375 0.171875 4.8125 0.40625 L 5.140625 0.90625 C 5.734375 0.65625 6.234375 0.34375 6.65625 -0.015625 C 7.0625 0.328125 7.5625 0.625 8.15625 0.90625 L 8.484375 0.375 C 7.921875 0.140625 7.46875 -0.125 7.0625 -0.421875 C 7.5 -0.921875 7.796875 -1.515625 7.96875 -2.21875 L 7.96875 -2.71875 L 4.90625 -2.71875 L 4.90625 -2.1875 Z M 6.609375 -0.8125 C 6.1875 -1.21875 5.90625 -1.671875 5.75 -2.1875 L 7.390625 -2.1875 C 7.234375 -1.6875 6.96875 -1.21875 6.609375 -0.8125 Z M 6.609375 -0.8125 "/>
</symbol>
<symbol overflow="visible" id="glyph3-2">
<path style="stroke:none;" d="M 0.546875 -4.984375 L 0.546875 -4.359375 L 3.9375 -4.359375 C 3.46875 -2.140625 2.28125 -0.5625 0.375 0.390625 L 0.78125 0.921875 C 2.6875 -0.078125 3.9375 -1.671875 4.484375 -3.859375 C 5.171875 -1.8125 6.40625 -0.25 8.21875 0.859375 L 8.625 0.34375 C 6.78125 -0.75 5.53125 -2.328125 4.90625 -4.359375 L 8.453125 -4.359375 L 8.453125 -4.984375 L 4.71875 -4.984375 C 4.78125 -5.546875 4.828125 -6.125 4.828125 -6.765625 L 4.828125 -7.34375 L 4.171875 -7.34375 L 4.171875 -6.609375 C 4.15625 -6.046875 4.125 -5.515625 4.046875 -4.984375 Z M 0.546875 -4.984375 "/>
</symbol>
<symbol overflow="visible" id="glyph3-3">
<path style="stroke:none;" d="M 4.125 -0.0625 L 4.125 -6.34375 L 8.40625 -6.34375 L 8.40625 -6.96875 L 3.46875 -6.96875 L 3.46875 0.59375 L 8.484375 0.59375 L 8.484375 -0.0625 Z M 1.09375 -7.1875 L 0.640625 -6.734375 C 1.390625 -6.25 1.96875 -5.796875 2.375 -5.359375 L 2.828125 -5.828125 C 2.359375 -6.265625 1.796875 -6.734375 1.09375 -7.1875 Z M 0.921875 -4.984375 L 0.453125 -4.53125 C 1.15625 -4.0625 1.6875 -3.625 2.09375 -3.203125 L 2.5625 -3.671875 C 2.109375 -4.109375 1.5625 -4.546875 0.921875 -4.984375 Z M 1.984375 -2.328125 C 1.59375 -1.34375 1.109375 -0.375 0.578125 0.5625 L 1.234375 0.859375 C 1.734375 -0.046875 2.171875 -1.046875 2.59375 -2.09375 Z M 1.984375 -2.328125 "/>
</symbol>
<symbol overflow="visible" id="glyph3-4">
<path style="stroke:none;" d="M 4.234375 -2.234375 L 4.234375 0.875 L 4.828125 0.875 L 4.828125 -2.296875 L 5.3125 -2.375 C 5.65625 -1.03125 6.59375 0.03125 8.125 0.8125 L 8.453125 0.265625 C 7.765625 -0.046875 7.1875 -0.421875 6.75 -0.859375 C 7.265625 -1.171875 7.703125 -1.46875 8.03125 -1.765625 L 7.6875 -2.21875 C 7.34375 -1.890625 6.90625 -1.578125 6.390625 -1.265625 C 6.125 -1.625 5.921875 -2.03125 5.8125 -2.46875 C 6.421875 -2.59375 6.984375 -2.75 7.5 -2.953125 L 7.203125 -3.453125 C 5.640625 -2.921875 3.640625 -2.640625 1.25 -2.640625 L 1.4375 -2.09375 C 2.453125 -2.09375 3.390625 -2.140625 4.234375 -2.234375 Z M 0.703125 -7.015625 L 0.703125 -6.5 L 1.390625 -6.5 L 1.390625 -3.890625 C 1.09375 -3.875 0.8125 -3.84375 0.5 -3.8125 L 0.5625 -3.28125 C 1.640625 -3.359375 2.65625 -3.484375 3.578125 -3.609375 L 3.578125 -3.109375 L 4.15625 -3.109375 L 4.15625 -3.6875 C 4.359375 -3.71875 4.5625 -3.75 4.78125 -3.78125 L 4.78125 -4.28125 C 4.578125 -4.25 4.359375 -4.21875 4.15625 -4.171875 L 4.15625 -6.5 L 4.78125 -6.5 L 4.78125 -7.015625 Z M 1.953125 -3.9375 L 1.953125 -4.546875 L 3.578125 -4.546875 L 3.578125 -4.109375 C 3.078125 -4.046875 2.53125 -3.984375 1.953125 -3.9375 Z M 1.953125 -5 L 1.953125 -5.515625 L 3.578125 -5.515625 L 3.578125 -5 Z M 1.953125 -5.96875 L 1.953125 -6.5 L 3.578125 -6.5 L 3.578125 -5.96875 Z M 5.390625 -5.875 L 5 -5.484375 C 5.484375 -5.171875 5.953125 -4.84375 6.359375 -4.546875 C 5.953125 -4.171875 5.46875 -3.890625 4.9375 -3.71875 L 5.1875 -3.1875 C 5.8125 -3.40625 6.359375 -3.734375 6.828125 -4.1875 C 7.21875 -3.890625 7.5625 -3.59375 7.859375 -3.28125 L 8.28125 -3.703125 C 7.96875 -4 7.609375 -4.296875 7.203125 -4.609375 C 7.5625 -5.0625 7.890625 -5.609375 8.140625 -6.265625 L 8.140625 -6.734375 L 5.046875 -6.734375 L 5.046875 -6.1875 L 7.484375 -6.1875 C 7.265625 -5.734375 7.03125 -5.3125 6.734375 -4.953125 C 6.328125 -5.265625 5.875 -5.5625 5.390625 -5.875 Z M 2.640625 -2.03125 C 2.390625 -1.8125 2.109375 -1.640625 1.8125 -1.46875 C 1.453125 -1.296875 1.078125 -1.15625 0.640625 -1.046875 L 0.953125 -0.546875 C 1.46875 -0.734375 1.90625 -0.90625 2.234375 -1.09375 C 2.53125 -1.265625 2.828125 -1.46875 3.125 -1.734375 Z M 3.34375 -1.046875 C 3 -0.765625 2.640625 -0.515625 2.25 -0.3125 C 1.796875 -0.078125 1.3125 0.109375 0.75 0.25 L 1.0625 0.765625 C 1.734375 0.53125 2.265625 0.3125 2.6875 0.078125 C 3.0625 -0.140625 3.453125 -0.40625 3.828125 -0.75 Z M 3.34375 -1.046875 "/>
</symbol>
<symbol overflow="visible" id="glyph3-5">
<path style="stroke:none;" d="M 2.921875 -3.90625 L 2.921875 -3.328125 L 7.796875 -3.328125 L 7.796875 -3.90625 Z M 2.28125 -2.4375 L 2.28125 -1.84375 L 4.25 -1.84375 C 3.625 -0.828125 3.09375 -0.203125 2.703125 0.03125 C 2.65625 0.046875 2.625 0.078125 2.5625 0.09375 L 2.703125 0.640625 C 4.40625 0.53125 5.96875 0.375 7.390625 0.1875 C 7.578125 0.40625 7.734375 0.640625 7.890625 0.875 L 8.40625 0.515625 C 8.015625 -0.0625 7.4375 -0.75 6.703125 -1.53125 L 6.21875 -1.234375 C 6.484375 -0.921875 6.75 -0.625 7 -0.328125 C 5.84375 -0.15625 4.671875 -0.03125 3.484375 0.046875 C 3.90625 -0.328125 4.390625 -0.96875 4.9375 -1.84375 L 8.390625 -1.84375 L 8.390625 -2.4375 Z M 7.296875 -6.390625 L 7.296875 -5.34375 L 2 -5.34375 L 2 -6.390625 Z M 2 -4.765625 L 7.921875 -4.765625 L 7.921875 -7 L 1.375 -7 L 1.375 -4.015625 C 1.359375 -2.203125 1.046875 -0.734375 0.421875 0.40625 L 0.890625 0.84375 C 1.625 -0.515625 2 -2.140625 2 -4.015625 Z M 2 -4.765625 "/>
</symbol>
<symbol overflow="visible" id="glyph3-6">
<path style="stroke:none;" d="M 2.359375 -7.234375 L 1.78125 -7.03125 C 2.046875 -6.671875 2.296875 -6.25 2.53125 -5.78125 L 3.078125 -6.03125 C 2.875 -6.453125 2.640625 -6.84375 2.359375 -7.234375 Z M 6.78125 -7.234375 C 6.5625 -6.78125 6.296875 -6.390625 5.953125 -6.0625 L 6.46875 -5.765625 C 6.828125 -6.125 7.125 -6.546875 7.359375 -7.015625 Z M 1.15625 -5.71875 L 1.15625 -5.125 L 3.765625 -5.125 C 3.625 -4.8125 3.4375 -4.5 3.234375 -4.21875 L 0.640625 -4.21875 L 0.640625 -3.625 L 2.78125 -3.625 C 2.15625 -2.921875 1.359375 -2.34375 0.390625 -1.921875 L 0.75 -1.359375 C 1.34375 -1.640625 1.890625 -1.984375 2.375 -2.375 L 2.375 0.078125 C 2.375 0.546875 2.640625 0.78125 3.1875 0.78125 L 6.515625 0.78125 C 7 0.78125 7.3125 0.703125 7.5 0.578125 C 7.6875 0.40625 7.8125 -0.03125 7.890625 -0.734375 L 7.25 -0.9375 C 7.203125 -0.484375 7.125 -0.1875 7.03125 -0.015625 C 6.953125 0.109375 6.71875 0.1875 6.34375 0.1875 L 3.453125 0.1875 C 3.171875 0.1875 3.03125 0.078125 3.03125 -0.125 L 3.03125 -2.15625 L 5.921875 -2.15625 L 5.921875 -1.484375 C 5.921875 -1.296875 5.796875 -1.203125 5.5625 -1.203125 C 5.1875 -1.203125 4.78125 -1.21875 4.328125 -1.234375 L 4.46875 -0.671875 C 4.90625 -0.65625 5.34375 -0.640625 5.8125 -0.640625 C 6.296875 -0.671875 6.546875 -0.890625 6.5625 -1.3125 L 6.5625 -2.53125 C 7.015625 -2.15625 7.5625 -1.796875 8.203125 -1.453125 L 8.625 -1.984375 C 7.53125 -2.46875 6.71875 -3 6.234375 -3.625 L 8.359375 -3.625 L 8.359375 -4.21875 L 3.984375 -4.21875 C 4.15625 -4.5 4.296875 -4.8125 4.453125 -5.125 L 7.859375 -5.125 L 7.859375 -5.71875 L 4.671875 -5.71875 C 4.828125 -6.203125 4.96875 -6.734375 5.0625 -7.296875 L 4.421875 -7.375 C 4.328125 -6.78125 4.1875 -6.234375 4.015625 -5.71875 Z M 3.578125 -3.625 L 5.578125 -3.625 C 5.78125 -3.3125 6.03125 -3.015625 6.34375 -2.734375 L 2.78125 -2.734375 C 3.0625 -3 3.328125 -3.296875 3.578125 -3.625 Z M 3.578125 -3.625 "/>
</symbol>
<symbol overflow="visible" id="glyph3-7">
<path style="stroke:none;" d="M 0.6875 -5.96875 C 1.125 -6.015625 1.53125 -6.09375 1.921875 -6.171875 L 1.921875 -4.890625 L 0.59375 -4.890625 L 0.59375 -4.265625 L 1.890625 -4.265625 C 1.578125 -3.21875 1.046875 -2.296875 0.328125 -1.484375 L 0.59375 -0.8125 C 1.140625 -1.515625 1.578125 -2.328125 1.921875 -3.203125 L 1.921875 0.859375 L 2.53125 0.859375 L 2.53125 -3.25 C 2.796875 -2.890625 3.109375 -2.390625 3.484375 -1.765625 L 3.84375 -2.296875 C 3.390625 -2.859375 2.96875 -3.359375 2.53125 -3.8125 L 2.53125 -4.265625 L 3.640625 -4.265625 L 3.640625 -4.890625 L 2.53125 -4.890625 L 2.53125 -6.3125 C 2.90625 -6.40625 3.265625 -6.5 3.59375 -6.625 L 3.359375 -7.234375 C 2.5625 -6.90625 1.625 -6.703125 0.5625 -6.59375 Z M 4.171875 -6.8125 L 4.171875 -2.625 L 8.0625 -2.625 L 8.0625 -6.8125 Z M 7.421875 -3.234375 L 4.828125 -3.234375 L 4.828125 -6.1875 L 7.421875 -6.1875 Z M 4.953125 -2.046875 C 4.546875 -1.046875 4.046875 -0.1875 3.46875 0.53125 L 4.015625 0.921875 C 4.59375 0.15625 5.109375 -0.734375 5.53125 -1.765625 Z M 7.15625 -2.09375 L 6.640625 -1.765625 C 7.265625 -0.796875 7.78125 0.09375 8.15625 0.90625 L 8.703125 0.515625 C 8.34375 -0.21875 7.828125 -1.09375 7.15625 -2.09375 Z M 7.15625 -2.09375 "/>
</symbol>
<symbol overflow="visible" id="glyph3-8">
<path style="stroke:none;" d="M 4.265625 -7.328125 C 3.21875 -6.1875 1.90625 -5.234375 0.328125 -4.46875 L 0.640625 -3.90625 C 2.234375 -4.703125 3.515625 -5.65625 4.5 -6.734375 C 5.5625 -5.59375 6.828125 -4.671875 8.328125 -3.953125 L 8.65625 -4.53125 C 7.171875 -5.21875 5.875 -6.140625 4.734375 -7.328125 Z M 1.984375 -4.484375 L 1.984375 -3.859375 L 4.171875 -3.859375 L 4.171875 -2.296875 L 1.5625 -2.296875 L 1.5625 -1.671875 L 4.171875 -1.671875 L 4.171875 -0.015625 L 0.75 -0.015625 L 0.75 0.625 L 8.25 0.625 L 8.25 -0.015625 L 4.828125 -0.015625 L 4.828125 -1.671875 L 7.4375 -1.671875 L 7.4375 -2.296875 L 4.828125 -2.296875 L 4.828125 -3.859375 L 7.015625 -3.859375 L 7.015625 -4.484375 Z M 1.984375 -4.484375 "/>
</symbol>
<symbol overflow="visible" id="glyph3-9">
<path style="stroke:none;" d="M 2.96875 -2.625 L 2.96875 -0.359375 L 6.140625 -0.359375 L 6.140625 -2.625 Z M 5.5625 -0.90625 L 3.5625 -0.90625 L 3.5625 -2.09375 L 5.5625 -2.09375 Z M 7.0625 -6.390625 L 7.0625 -5.40625 L 2.140625 -5.40625 L 2.140625 -6.390625 Z M 2.140625 -4.8125 L 7.6875 -4.8125 L 7.6875 -7 L 1.5 -7 L 1.5 -3.46875 C 1.5 -1.890625 1.125 -0.59375 0.390625 0.40625 L 0.890625 0.859375 C 1.703125 -0.34375 2.109375 -1.765625 2.140625 -3.4375 L 7.5 -3.4375 C 7.484375 -1.59375 7.46875 -0.5 7.40625 -0.140625 C 7.34375 0.15625 7.140625 0.296875 6.828125 0.296875 C 6.484375 0.296875 6.078125 0.28125 5.59375 0.265625 L 5.734375 0.8125 C 6.296875 0.84375 6.671875 0.84375 6.90625 0.84375 C 7.546875 0.84375 7.921875 0.59375 8 0.09375 C 8.09375 -0.421875 8.140625 -1.796875 8.140625 -4.015625 L 2.140625 -4.015625 Z M 2.140625 -4.8125 "/>
</symbol>
<symbol overflow="visible" id="glyph3-10">
<path style="stroke:none;" d="M 0.875 -6.953125 L 0.875 -6.3125 L 4.125 -6.3125 L 4.125 -2.921875 L 0.515625 -2.921875 L 0.515625 -2.296875 L 4.125 -2.296875 L 4.125 0.890625 L 4.796875 0.890625 L 4.796875 -2.296875 L 8.484375 -2.296875 L 8.484375 -2.921875 L 4.796875 -2.921875 L 4.796875 -6.3125 L 8.140625 -6.3125 L 8.140625 -6.953125 Z M 1.96875 -5.734375 L 1.375 -5.4375 C 1.703125 -4.84375 2.03125 -4.15625 2.34375 -3.390625 L 2.9375 -3.6875 C 2.640625 -4.421875 2.3125 -5.109375 1.96875 -5.734375 Z M 7.078125 -5.78125 C 6.8125 -5.109375 6.46875 -4.421875 6.015625 -3.71875 L 6.59375 -3.421875 C 7.03125 -4.109375 7.390625 -4.796875 7.671875 -5.515625 Z M 7.078125 -5.78125 "/>
</symbol>
<symbol overflow="visible" id="glyph3-11">
<path style="stroke:none;" d="M 6.765625 -2.515625 C 5.9375 -1.96875 4.9375 -1.5 3.78125 -1.125 L 4.0625 -0.515625 C 5.203125 -0.921875 6.15625 -1.390625 6.9375 -1.921875 Z M 4.953125 -4.421875 L 4.546875 -4.015625 C 5.140625 -3.5625 5.609375 -3.125 5.953125 -2.734375 L 6.40625 -3.171875 C 6.015625 -3.59375 5.53125 -4.015625 4.953125 -4.421875 Z M 4.71875 -7.40625 C 4.453125 -6.125 3.9375 -5.046875 3.171875 -4.15625 L 3.59375 -3.59375 C 4.046875 -4.109375 4.421875 -4.703125 4.734375 -5.359375 L 7.65625 -5.359375 C 7.640625 -2.625 7.59375 -1.015625 7.5 -0.484375 C 7.421875 -0.015625 7.15625 0.21875 6.6875 0.21875 C 6.328125 0.21875 5.90625 0.203125 5.421875 0.1875 L 5.578125 0.796875 C 6.15625 0.8125 6.5625 0.828125 6.796875 0.828125 C 7.59375 0.828125 8.03125 0.453125 8.125 -0.265625 C 8.234375 -0.984375 8.28125 -2.890625 8.28125 -5.984375 L 5 -5.984375 C 5.140625 -6.40625 5.28125 -6.859375 5.375 -7.328125 Z M 0.421875 -4.953125 L 0.421875 -4.328125 L 1.40625 -4.328125 L 1.40625 -1.125 C 1.0625 -1.03125 0.703125 -0.96875 0.328125 -0.90625 L 0.5 -0.21875 C 1.515625 -0.4375 2.453125 -0.71875 3.328125 -1.046875 L 3.328125 -1.71875 C 2.921875 -1.5625 2.5 -1.421875 2.0625 -1.296875 L 2.0625 -4.328125 L 3 -4.328125 L 3 -4.953125 L 2.0625 -4.953125 L 2.0625 -7.3125 L 1.40625 -7.3125 L 1.40625 -4.953125 Z M 0.421875 -4.953125 "/>
</symbol>
<symbol overflow="visible" id="glyph3-12">
<path style="stroke:none;" d="M 1.15625 -7.203125 L 0.71875 -6.78125 C 1.34375 -6.265625 1.859375 -5.796875 2.21875 -5.359375 L 2.6875 -5.8125 C 2.265625 -6.28125 1.75 -6.734375 1.15625 -7.203125 Z M 6.375 0.734375 L 8.4375 0.734375 L 8.59375 0.09375 C 8.359375 0.109375 8.015625 0.109375 7.59375 0.125 C 7.140625 0.140625 6.71875 0.140625 6.296875 0.140625 C 5.59375 0.140625 5.015625 0.140625 4.546875 0.125 C 4.015625 0.109375 3.625 0.03125 3.34375 -0.09375 C 3.09375 -0.203125 2.875 -0.390625 2.65625 -0.65625 C 2.578125 -0.734375 2.515625 -0.8125 2.4375 -0.859375 L 2.4375 -4.234375 L 0.546875 -4.234375 L 0.546875 -3.640625 L 1.84375 -3.640625 L 1.84375 -0.90625 C 1.46875 -0.734375 1.046875 -0.28125 0.5625 0.484375 L 1.046875 0.921875 C 1.546875 0.09375 1.90625 -0.328125 2.109375 -0.328125 C 2.21875 -0.328125 2.34375 -0.25 2.453125 -0.09375 C 2.734375 0.21875 3.046875 0.453125 3.40625 0.5625 C 3.765625 0.65625 4.21875 0.71875 4.796875 0.71875 C 5.359375 0.734375 5.890625 0.734375 6.375 0.734375 Z M 3.0625 -6.421875 L 3.0625 -5.828125 L 4.25 -5.828125 C 3.953125 -5.1875 3.625 -4.640625 3.265625 -4.21875 L 3.40625 -3.625 L 5.484375 -3.625 L 5.484375 -2.546875 L 2.984375 -2.546875 L 2.984375 -1.9375 L 5.484375 -1.9375 L 5.484375 -0.265625 L 6.109375 -0.265625 L 6.109375 -1.9375 L 8.453125 -1.9375 L 8.453125 -2.546875 L 6.109375 -2.546875 L 6.109375 -3.625 L 8.0625 -3.625 L 8.0625 -4.21875 L 6.109375 -4.21875 L 6.109375 -5.28125 L 5.484375 -5.28125 L 5.484375 -4.21875 L 4.03125 -4.21875 C 4.359375 -4.6875 4.65625 -5.21875 4.9375 -5.828125 L 8.34375 -5.828125 L 8.34375 -6.421875 L 5.1875 -6.421875 C 5.296875 -6.703125 5.390625 -6.96875 5.484375 -7.265625 L 4.875 -7.421875 C 4.765625 -7.078125 4.640625 -6.734375 4.515625 -6.421875 Z M 3.0625 -6.421875 "/>
</symbol>
<symbol overflow="visible" id="glyph3-13">
<path style="stroke:none;" d="M 4.765625 -5.828125 L 4.234375 -5.578125 C 4.421875 -5.25 4.59375 -4.890625 4.78125 -4.5 L 3.40625 -4.5 L 3.40625 -3.875 L 8.421875 -3.875 L 8.421875 -4.5 L 6.96875 -4.5 C 7.171875 -4.859375 7.359375 -5.265625 7.53125 -5.71875 L 6.921875 -5.9375 C 6.734375 -5.421875 6.53125 -4.953125 6.28125 -4.5 L 5.40625 -4.5 C 5.203125 -4.984375 5 -5.421875 4.765625 -5.828125 Z M 5.953125 -7.40625 L 5.28125 -7.296875 C 5.375 -7.078125 5.484375 -6.828125 5.59375 -6.546875 L 3.609375 -6.546875 L 3.609375 -5.953125 L 8.265625 -5.953125 L 8.265625 -6.546875 L 6.25 -6.546875 C 6.140625 -6.859375 6.046875 -7.15625 5.953125 -7.40625 Z M 1.453125 0.8125 C 1.984375 0.8125 2.265625 0.5625 2.265625 0.03125 L 2.265625 -2.625 C 2.515625 -2.75 2.765625 -2.875 3.015625 -3 L 3.015625 -3.65625 C 2.765625 -3.53125 2.515625 -3.40625 2.265625 -3.28125 L 2.265625 -5 L 3 -5 L 3 -5.625 L 2.265625 -5.625 L 2.265625 -7.328125 L 1.59375 -7.328125 L 1.59375 -5.625 L 0.5 -5.625 L 0.5 -5 L 1.59375 -5 L 1.59375 -3 C 1.171875 -2.828125 0.75 -2.703125 0.328125 -2.59375 L 0.5 -1.9375 C 0.859375 -2.0625 1.234375 -2.203125 1.59375 -2.34375 L 1.59375 -0.125 C 1.59375 0.109375 1.46875 0.21875 1.234375 0.21875 C 1.015625 0.21875 0.78125 0.203125 0.546875 0.1875 L 0.671875 0.8125 Z M 5.15625 -3.625 C 5.0625 -3.375 4.953125 -3.125 4.828125 -2.859375 L 3.28125 -2.859375 L 3.28125 -2.21875 L 4.484375 -2.21875 C 4.296875 -1.875 4.09375 -1.53125 3.84375 -1.15625 C 4.46875 -0.90625 5.0625 -0.671875 5.609375 -0.40625 C 5.046875 -0.09375 4.234375 0.15625 3.1875 0.34375 L 3.453125 0.9375 C 4.75 0.6875 5.6875 0.328125 6.28125 -0.09375 C 7 0.25 7.625 0.578125 8.15625 0.90625 L 8.515625 0.390625 C 7.953125 0.0625 7.359375 -0.25 6.734375 -0.53125 C 7.15625 -0.984375 7.4375 -1.5625 7.578125 -2.21875 L 8.5625 -2.21875 L 8.5625 -2.859375 L 5.484375 -2.859375 C 5.59375 -3.078125 5.6875 -3.3125 5.78125 -3.5625 Z M 6.96875 -2.21875 C 6.828125 -1.640625 6.546875 -1.171875 6.140625 -0.796875 C 5.65625 -1.015625 5.15625 -1.21875 4.640625 -1.421875 C 4.8125 -1.671875 4.984375 -1.9375 5.140625 -2.21875 Z M 6.96875 -2.21875 "/>
</symbol>
</g>
<clipPath id="clip1">
  <path d="M 42.5 285.363281 L 47.25 285.363281 L 47.25 290 L 42.5 290 Z M 42.5 285.363281 "/>
</clipPath>
<clipPath id="clip2">
  <path d="M 51.5 310.125 L 56.25 310.125 L 56.25 314.875 L 51.5 314.875 Z M 51.5 310.125 "/>
</clipPath>
<clipPath id="clip3">
  <path d="M 51.5 261 L 56.25 261 L 56.25 265.550781 L 51.5 265.550781 Z M 51.5 261 "/>
</clipPath>
<clipPath id="clip4">
  <path d="M 51.5 236.238281 L 56.25 236.238281 L 56.25 240.988281 L 51.5 240.988281 Z M 51.5 236.238281 "/>
</clipPath>
<clipPath id="clip5">
  <path d="M 42.5 211.675781 L 47.25 211.675781 L 47.25 216.425781 L 42.5 216.425781 Z M 42.5 211.675781 "/>
</clipPath>
<clipPath id="clip6">
  <path d="M 42.5 152 L 47.25 152 L 47.25 156.558594 L 42.5 156.558594 Z M 42.5 152 "/>
</clipPath>
<clipPath id="clip7">
  <path d="M 42.5 92.125 L 47.25 92.125 L 47.25 96.875 L 42.5 96.875 Z M 42.5 92.125 "/>
</clipPath>
<clipPath id="clip8">
  <path d="M 13 180.125 L 17.75 180.125 L 17.75 184.875 L 13 184.875 Z M 13 180.125 "/>
</clipPath>
<clipPath id="clip9">
  <path d="M 13 120.125 L 17.75 120.125 L 17.75 124.875 L 13 124.875 Z M 13 120.125 "/>
</clipPath>
<clipPath id="clip10">
  <path d="M 13 60.210938 L 17.75 60.210938 L 17.75 64.960938 L 13 64.960938 Z M 13 60.210938 "/>
</clipPath>
</defs>
<g id="surface1">
<path style=" stroke:none;fill-rule:nonzero;fill:rgb(69.804382%,85.098267%,100%);fill-opacity:1;" d="M 28.5 184.929688 L 46.644531 184.929688 L 46.644531 180.695312 L 28.5 180.695312 Z M 28.5 184.929688 "/>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 635.5 377.695312 L 653.644531 377.695312 L 653.644531 381.929688 L 635.5 381.929688 Z M 635.5 377.695312 " transform="matrix(1,0,0,1,-607,-197)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 656.5 381.28125 L 674.640625 381.28125 L 674.640625 385.515625 L 656.5 385.515625 Z M 656.5 381.28125 " transform="matrix(1,0,0,1,-607,-197)"/>
<path style=" stroke:none;fill-rule:nonzero;fill:rgb(39.99939%,74.902344%,100%);fill-opacity:1;" d="M 49.5 181.347656 L 67.640625 181.347656 L 67.640625 177.113281 L 49.5 177.113281 Z M 49.5 181.347656 "/>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 656.5 374.113281 L 674.640625 374.113281 L 674.640625 378.347656 L 656.5 378.347656 Z M 656.5 374.113281 " transform="matrix(1,0,0,1,-607,-197)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 665.570312 381.28125 L 665.570312 381.046875 " transform="matrix(1,0,0,1,-607,-197)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:butt;stroke-linejoin:miter;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 665.570312 380.246094 L 665.570312 381.046875 M 665.269531 381.046875 L 665.570312 380.246094 L 665.871094 381.046875 " transform="matrix(1,0,0,1,-607,-197)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 677.496094 381.28125 L 695.640625 381.28125 L 695.640625 385.515625 L 677.496094 385.515625 Z M 677.496094 381.28125 " transform="matrix(1,0,0,1,-607,-197)"/>
<path style=" stroke:none;fill-rule:nonzero;fill:rgb(39.99939%,74.902344%,100%);fill-opacity:1;" d="M 70.496094 181.347656 L 88.640625 181.347656 L 88.640625 177.113281 L 70.496094 177.113281 Z M 70.496094 181.347656 "/>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 677.496094 374.113281 L 695.640625 374.113281 L 695.640625 378.347656 L 677.496094 378.347656 Z M 677.496094 374.113281 " transform="matrix(1,0,0,1,-607,-197)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 686.570312 381.28125 L 686.570312 381.046875 " transform="matrix(1,0,0,1,-607,-197)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:butt;stroke-linejoin:miter;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 686.570312 380.246094 L 686.570312 381.046875 M 686.269531 381.046875 L 686.570312 380.246094 L 686.871094 381.046875 " transform="matrix(1,0,0,1,-607,-197)"/>
<path style=" stroke:none;fill-rule:nonzero;fill:rgb(69.804382%,85.098267%,100%);fill-opacity:1;" d="M 91.355469 188.289062 L 109.5 188.289062 L 109.5 184.058594 L 91.355469 184.058594 Z M 91.355469 188.289062 "/>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 698.355469 381.058594 L 716.5 381.058594 L 716.5 385.289062 L 698.355469 385.289062 Z M 698.355469 381.058594 " transform="matrix(1,0,0,1,-607,-197)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 698.355469 373.890625 L 716.5 373.890625 L 716.5 378.121094 L 698.355469 378.121094 Z M 698.355469 373.890625 " transform="matrix(1,0,0,1,-607,-197)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 707.429688 381.058594 L 707.429688 380.824219 " transform="matrix(1,0,0,1,-607,-197)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:butt;stroke-linejoin:miter;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 707.429688 380.023438 L 707.429688 380.824219 M 707.128906 380.824219 L 707.429688 380.023438 L 707.726562 380.824219 " transform="matrix(1,0,0,1,-607,-197)"/>
<path style=" stroke:none;fill-rule:nonzero;fill:rgb(69.804382%,85.098267%,100%);fill-opacity:1;" d="M 59.914062 195.683594 L 78.058594 195.683594 L 78.058594 191.449219 L 59.914062 191.449219 Z M 59.914062 195.683594 "/>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 666.914062 388.449219 L 685.058594 388.449219 L 685.058594 392.683594 L 666.914062 392.683594 Z M 666.914062 388.449219 " transform="matrix(1,0,0,1,-607,-197)"/>
<path style=" stroke:none;fill-rule:nonzero;fill:rgb(69.804382%,85.098267%,100%);fill-opacity:1;" d="M 59.914062 174.179688 L 78.058594 174.179688 L 78.058594 169.945312 L 59.914062 169.945312 Z M 59.914062 174.179688 "/>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 666.914062 366.945312 L 685.058594 366.945312 L 685.058594 371.179688 L 666.914062 371.179688 Z M 666.914062 366.945312 " transform="matrix(1,0,0,1,-607,-197)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:miter;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 679.113281 388.449219 L 681.207031 387.027344 " transform="matrix(1,0,0,1,-607,-197)"/>
<path style="fill-rule:nonzero;fill:rgb(0%,0%,0%);fill-opacity:1;stroke-width:1;stroke-linecap:butt;stroke-linejoin:miter;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 681.871094 386.578125 L 681.039062 386.78125 L 681.375 387.277344 Z M 681.871094 386.578125 " transform="matrix(1,0,0,1,-607,-197)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:miter;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 672.910156 388.449219 L 670.871094 387.042969 " transform="matrix(1,0,0,1,-607,-197)"/>
<path style="fill-rule:nonzero;fill:rgb(0%,0%,0%);fill-opacity:1;stroke-width:1;stroke-linecap:butt;stroke-linejoin:miter;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 670.210938 386.589844 L 670.699219 387.292969 L 671.039062 386.796875 Z M 670.210938 386.589844 " transform="matrix(1,0,0,1,-607,-197)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:miter;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 668.644531 374.113281 L 670.6875 372.710938 " transform="matrix(1,0,0,1,-607,-197)"/>
<path style="fill-rule:nonzero;fill:rgb(0%,0%,0%);fill-opacity:1;stroke-width:1;stroke-linecap:butt;stroke-linejoin:miter;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 671.34375 372.257812 L 670.515625 372.460938 L 670.855469 372.957031 Z M 671.34375 372.257812 " transform="matrix(1,0,0,1,-607,-197)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:miter;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 683.445312 374.113281 L 681.347656 372.691406 " transform="matrix(1,0,0,1,-607,-197)"/>
<path style="fill-rule:nonzero;fill:rgb(0%,0%,0%);fill-opacity:1;stroke-width:1;stroke-linecap:butt;stroke-linejoin:miter;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 680.683594 372.246094 L 681.179688 372.941406 L 681.515625 372.445312 Z M 680.683594 372.246094 " transform="matrix(1,0,0,1,-607,-197)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:miter;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 685.058594 390.859375 C 685.0625 390.859375 685.066406 390.859375 685.066406 390.859375 L 702.429688 390.859375 C 704.445312 390.859375 706.179688 389.667969 706.972656 387.949219 " transform="matrix(1,0,0,1,-607,-197)"/>
<path style="fill-rule:nonzero;fill:rgb(0%,0%,0%);fill-opacity:1;stroke-width:1;stroke-linecap:butt;stroke-linejoin:miter;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 707.105469 387.164062 L 706.675781 387.902344 L 707.265625 388 Z M 707.105469 387.164062 " transform="matrix(1,0,0,1,-607,-197)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:miter;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 666.914062 390.804688 C 666.90625 390.808594 666.898438 390.808594 666.890625 390.808594 L 649.570312 390.808594 C 646.808594 390.808594 644.570312 388.570312 644.570312 385.808594 L 644.570312 384.628906 " transform="matrix(1,0,0,1,-607,-197)"/>
<path style="fill-rule:nonzero;fill:rgb(0%,0%,0%);fill-opacity:1;stroke-width:1;stroke-linecap:butt;stroke-linejoin:miter;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 644.570312 383.832031 L 644.273438 384.628906 L 644.871094 384.628906 Z M 644.570312 383.832031 " transform="matrix(1,0,0,1,-607,-197)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:miter;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 644.570312 377.695312 L 644.570312 374.171875 C 644.570312 371.410156 646.808594 369.171875 649.570312 369.171875 L 664.214844 369.171875 " transform="matrix(1,0,0,1,-607,-197)"/>
<path style="fill-rule:nonzero;fill:rgb(0%,0%,0%);fill-opacity:1;stroke-width:1;stroke-linecap:butt;stroke-linejoin:miter;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 665.015625 369.171875 L 664.214844 368.871094 L 664.214844 369.472656 Z M 665.015625 369.171875 " transform="matrix(1,0,0,1,-607,-197)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:miter;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 707.394531 373.890625 C 707.109375 371.398438 704.996094 369.464844 702.429688 369.464844 L 687.757812 369.464844 " transform="matrix(1,0,0,1,-607,-197)"/>
<path style="fill-rule:nonzero;fill:rgb(0%,0%,0%);fill-opacity:1;stroke-width:1;stroke-linecap:butt;stroke-linejoin:miter;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 686.957031 369.460938 L 687.757812 369.765625 L 687.757812 369.164062 Z M 686.957031 369.460938 " transform="matrix(1,0,0,1,-607,-197)"/>
<path style=" stroke:none;fill-rule:nonzero;fill:rgb(69.804382%,85.098267%,100%);fill-opacity:1;" d="M 28.5 293.9375 L 109.5 293.9375 L 109.5 278.9375 L 28.5 278.9375 Z M 28.5 293.9375 "/>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 635.5 475.9375 L 716.5 475.9375 L 716.5 490.9375 L 635.5 490.9375 Z M 635.5 475.9375 " transform="matrix(1,0,0,1,-607,-197)"/>
<g style="fill:rgb(0%,0%,0%);fill-opacity:1;">
  <use xlink:href="#glyph0-1" x="34.8271" y="290.2401"/>
  <use xlink:href="#glyph0-2" x="39.8329" y="290.2401"/>
</g>
<g clip-path="url(#clip1)" clip-rule="nonzero">
<g style="fill:rgb(0%,0%,0%);fill-opacity:1;">
  <use xlink:href="#glyph1-1" x="41.362" y="289.97701"/>
</g>
<g style="fill:rgb(0%,0%,0%);fill-opacity:1;">
  <use xlink:href="#glyph1-1" x="41.362" y="289.97701"/>
</g>
</g>
<g style="fill:rgb(0%,0%,0%);fill-opacity:1;">
  <use xlink:href="#glyph2-1" x="47.333" y="290.2401"/>
</g>
<g style="fill:rgb(0%,0%,0%);fill-opacity:1;">
  <use xlink:href="#glyph0-1" x="50.667" y="290.2401"/>
  <use xlink:href="#glyph0-2" x="55.6728" y="290.2401"/>
</g>
<g style="fill:rgb(0%,0%,0%);fill-opacity:1;">
  <use xlink:href="#glyph3-1" x="58.1729" y="290.2401"/>
  <use xlink:href="#glyph3-2" x="67.1729" y="290.2401"/>
  <use xlink:href="#glyph3-3" x="76.1729" y="290.2401"/>
  <use xlink:href="#glyph3-4" x="85.1729" y="290.2401"/>
  <use xlink:href="#glyph3-5" x="94.1729" y="290.2401"/>
</g>
<path style=" stroke:none;fill-rule:nonzero;fill:rgb(39.99939%,74.902344%,100%);fill-opacity:1;" d="M 28.5 318.5 L 109.5 318.5 L 109.5 303.5 L 28.5 303.5 Z M 28.5 318.5 "/>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 635.5 500.5 L 716.5 500.5 L 716.5 515.5 L 635.5 515.5 Z M 635.5 500.5 " transform="matrix(1,0,0,1,-607,-197)"/>
<g style="fill:rgb(0%,0%,0%);fill-opacity:1;">
  <use xlink:href="#glyph0-3" x="43.8271" y="314.8037"/>
  <use xlink:href="#glyph0-2" x="48.8329" y="314.8037"/>
</g>
<g clip-path="url(#clip2)" clip-rule="nonzero">
<g style="fill:rgb(0%,0%,0%);fill-opacity:1;">
  <use xlink:href="#glyph1-1" x="50.362" y="314.73691"/>
</g>
<g style="fill:rgb(0%,0%,0%);fill-opacity:1;">
  <use xlink:href="#glyph1-1" x="50.362" y="314.73691"/>
</g>
</g>
<g style="fill:rgb(0%,0%,0%);fill-opacity:1;">
  <use xlink:href="#glyph2-1" x="56.333" y="314.8037"/>
</g>
<g style="fill:rgb(0%,0%,0%);fill-opacity:1;">
  <use xlink:href="#glyph0-3" x="59.667" y="314.8037"/>
  <use xlink:href="#glyph0-2" x="64.6728" y="314.8037"/>
</g>
<g style="fill:rgb(0%,0%,0%);fill-opacity:1;">
  <use xlink:href="#glyph3-6" x="67.1729" y="314.8037"/>
  <use xlink:href="#glyph3-7" x="76.1729" y="314.8037"/>
  <use xlink:href="#glyph3-5" x="85.1729" y="314.8037"/>
</g>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 635.5 451.371094 L 716.5 451.371094 L 716.5 466.371094 L 635.5 466.371094 Z M 635.5 451.371094 " transform="matrix(1,0,0,1,-607,-197)"/>
<g style="fill:rgb(0%,0%,0%);fill-opacity:1;">
  <use xlink:href="#glyph0-4" x="43.8271" y="265.6764"/>
  <use xlink:href="#glyph0-2" x="48.8329" y="265.6764"/>
</g>
<g clip-path="url(#clip3)" clip-rule="nonzero">
<g style="fill:rgb(0%,0%,0%);fill-opacity:1;">
  <use xlink:href="#glyph1-1" x="50.362" y="265.41331"/>
</g>
<g style="fill:rgb(0%,0%,0%);fill-opacity:1;">
  <use xlink:href="#glyph1-1" x="50.362" y="265.41331"/>
</g>
</g>
<g style="fill:rgb(0%,0%,0%);fill-opacity:1;">
  <use xlink:href="#glyph2-1" x="56.333" y="265.6764"/>
</g>
<g style="fill:rgb(0%,0%,0%);fill-opacity:1;">
  <use xlink:href="#glyph0-4" x="59.667" y="265.6764"/>
  <use xlink:href="#glyph0-2" x="64.6728" y="265.6764"/>
</g>
<g style="fill:rgb(0%,0%,0%);fill-opacity:1;">
  <use xlink:href="#glyph3-6" x="67.1729" y="265.6764"/>
  <use xlink:href="#glyph3-7" x="76.1729" y="265.6764"/>
  <use xlink:href="#glyph3-5" x="85.1729" y="265.6764"/>
</g>
<path style=" stroke:none;fill-rule:nonzero;fill:rgb(39.99939%,74.902344%,100%);fill-opacity:1;" d="M 28.5 244.808594 L 109.5 244.808594 L 109.5 229.808594 L 28.5 229.808594 Z M 28.5 244.808594 "/>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 635.5 426.808594 L 716.5 426.808594 L 716.5 441.808594 L 635.5 441.808594 Z M 635.5 426.808594 " transform="matrix(1,0,0,1,-607,-197)"/>
<g style="fill:rgb(0%,0%,0%);fill-opacity:1;">
  <use xlink:href="#glyph0-1" x="43.8271" y="241.1127"/>
  <use xlink:href="#glyph0-2" x="48.8329" y="241.1127"/>
</g>
<g clip-path="url(#clip4)" clip-rule="nonzero">
<g style="fill:rgb(0%,0%,0%);fill-opacity:1;">
  <use xlink:href="#glyph1-1" x="50.362" y="240.84961"/>
</g>
<g style="fill:rgb(0%,0%,0%);fill-opacity:1;">
  <use xlink:href="#glyph1-1" x="50.362" y="240.84961"/>
</g>
</g>
<g style="fill:rgb(0%,0%,0%);fill-opacity:1;">
  <use xlink:href="#glyph2-1" x="56.333" y="241.1127"/>
</g>
<g style="fill:rgb(0%,0%,0%);fill-opacity:1;">
  <use xlink:href="#glyph0-1" x="59.667" y="241.1127"/>
  <use xlink:href="#glyph0-2" x="64.6728" y="241.1127"/>
</g>
<g style="fill:rgb(0%,0%,0%);fill-opacity:1;">
  <use xlink:href="#glyph3-6" x="67.1729" y="241.1127"/>
  <use xlink:href="#glyph3-7" x="76.1729" y="241.1127"/>
  <use xlink:href="#glyph3-5" x="85.1729" y="241.1127"/>
</g>
<path style=" stroke:none;fill-rule:nonzero;fill:rgb(69.804382%,85.098267%,100%);fill-opacity:1;" d="M 28.5 220.246094 L 109.5 220.246094 L 109.5 205.246094 L 28.5 205.246094 Z M 28.5 220.246094 "/>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 635.5 402.246094 L 716.5 402.246094 L 716.5 417.246094 L 635.5 417.246094 Z M 635.5 402.246094 " transform="matrix(1,0,0,1,-607,-197)"/>
<g style="fill:rgb(0%,0%,0%);fill-opacity:1;">
  <use xlink:href="#glyph0-1" x="34.8271" y="216.5491"/>
  <use xlink:href="#glyph0-2" x="39.8329" y="216.5491"/>
</g>
<g clip-path="url(#clip5)" clip-rule="nonzero">
<g style="fill:rgb(0%,0%,0%);fill-opacity:1;">
  <use xlink:href="#glyph1-1" x="41.362" y="216.28601"/>
</g>
<g style="fill:rgb(0%,0%,0%);fill-opacity:1;">
  <use xlink:href="#glyph1-1" x="41.362" y="216.28601"/>
</g>
</g>
<g style="fill:rgb(0%,0%,0%);fill-opacity:1;">
  <use xlink:href="#glyph2-1" x="47.333" y="216.5491"/>
</g>
<g style="fill:rgb(0%,0%,0%);fill-opacity:1;">
  <use xlink:href="#glyph0-1" x="50.667" y="216.5491"/>
  <use xlink:href="#glyph0-2" x="55.6728" y="216.5491"/>
</g>
<g style="fill:rgb(0%,0%,0%);fill-opacity:1;">
  <use xlink:href="#glyph3-1" x="58.1729" y="216.5491"/>
  <use xlink:href="#glyph3-2" x="67.1729" y="216.5491"/>
  <use xlink:href="#glyph3-3" x="76.1729" y="216.5491"/>
  <use xlink:href="#glyph3-4" x="85.1729" y="216.5491"/>
  <use xlink:href="#glyph3-5" x="94.1729" y="216.5491"/>
</g>
<path style=" stroke:none;fill-rule:nonzero;fill:rgb(69.804382%,85.098267%,100%);fill-opacity:1;" d="M 28.5 160.382812 L 109.5 160.382812 L 109.5 145.382812 L 28.5 145.382812 Z M 28.5 160.382812 "/>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 635.5 342.382812 L 716.5 342.382812 L 716.5 357.382812 L 635.5 357.382812 Z M 635.5 342.382812 " transform="matrix(1,0,0,1,-607,-197)"/>
<g style="fill:rgb(0%,0%,0%);fill-opacity:1;">
  <use xlink:href="#glyph0-1" x="34.8271" y="156.6854"/>
  <use xlink:href="#glyph0-2" x="39.8329" y="156.6854"/>
</g>
<g clip-path="url(#clip6)" clip-rule="nonzero">
<g style="fill:rgb(0%,0%,0%);fill-opacity:1;">
  <use xlink:href="#glyph1-1" x="41.362" y="156.42231"/>
</g>
<g style="fill:rgb(0%,0%,0%);fill-opacity:1;">
  <use xlink:href="#glyph1-1" x="41.362" y="156.42231"/>
</g>
</g>
<g style="fill:rgb(0%,0%,0%);fill-opacity:1;">
  <use xlink:href="#glyph2-1" x="47.333" y="156.6854"/>
</g>
<g style="fill:rgb(0%,0%,0%);fill-opacity:1;">
  <use xlink:href="#glyph0-1" x="50.667" y="156.6854"/>
  <use xlink:href="#glyph0-2" x="55.6728" y="156.6854"/>
</g>
<g style="fill:rgb(0%,0%,0%);fill-opacity:1;">
  <use xlink:href="#glyph3-1" x="58.1729" y="156.6854"/>
  <use xlink:href="#glyph3-2" x="67.1729" y="156.6854"/>
  <use xlink:href="#glyph3-3" x="76.1729" y="156.6854"/>
  <use xlink:href="#glyph3-4" x="85.1729" y="156.6854"/>
  <use xlink:href="#glyph3-5" x="94.1729" y="156.6854"/>
</g>
<path style=" stroke:none;fill-rule:nonzero;fill:rgb(69.804382%,85.098267%,100%);fill-opacity:1;" d="M 28.5 125.066406 L 46.644531 125.066406 L 46.644531 120.832031 L 28.5 120.832031 Z M 28.5 125.066406 "/>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 635.5 317.832031 L 653.644531 317.832031 L 653.644531 322.066406 L 635.5 322.066406 Z M 635.5 317.832031 " transform="matrix(1,0,0,1,-607,-197)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 656.5 321.417969 L 674.640625 321.417969 L 674.640625 325.652344 L 656.5 325.652344 Z M 656.5 321.417969 " transform="matrix(1,0,0,1,-607,-197)"/>
<path style=" stroke:none;fill-rule:nonzero;fill:rgb(39.99939%,74.902344%,100%);fill-opacity:1;" d="M 49.5 121.484375 L 67.640625 121.484375 L 67.640625 117.25 L 49.5 117.25 Z M 49.5 121.484375 "/>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 656.5 314.25 L 674.640625 314.25 L 674.640625 318.484375 L 656.5 318.484375 Z M 656.5 314.25 " transform="matrix(1,0,0,1,-607,-197)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 665.570312 321.417969 L 665.570312 321.183594 " transform="matrix(1,0,0,1,-607,-197)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:butt;stroke-linejoin:miter;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 665.570312 320.382812 L 665.570312 321.183594 M 665.269531 321.183594 L 665.570312 320.382812 L 665.871094 321.183594 " transform="matrix(1,0,0,1,-607,-197)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 677.496094 321.417969 L 695.640625 321.417969 L 695.640625 325.652344 L 677.496094 325.652344 Z M 677.496094 321.417969 " transform="matrix(1,0,0,1,-607,-197)"/>
<path style=" stroke:none;fill-rule:nonzero;fill:rgb(39.99939%,74.902344%,100%);fill-opacity:1;" d="M 70.496094 121.484375 L 88.640625 121.484375 L 88.640625 117.25 L 70.496094 117.25 Z M 70.496094 121.484375 "/>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 677.496094 314.25 L 695.640625 314.25 L 695.640625 318.484375 L 677.496094 318.484375 Z M 677.496094 314.25 " transform="matrix(1,0,0,1,-607,-197)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 686.570312 321.417969 L 686.570312 321.183594 " transform="matrix(1,0,0,1,-607,-197)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:butt;stroke-linejoin:miter;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 686.570312 320.382812 L 686.570312 321.183594 M 686.269531 321.183594 L 686.570312 320.382812 L 686.871094 321.183594 " transform="matrix(1,0,0,1,-607,-197)"/>
<path style=" stroke:none;fill-rule:nonzero;fill:rgb(69.804382%,85.098267%,100%);fill-opacity:1;" d="M 91.355469 128.425781 L 109.5 128.425781 L 109.5 124.191406 L 91.355469 124.191406 Z M 91.355469 128.425781 "/>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 698.355469 321.191406 L 716.5 321.191406 L 716.5 325.425781 L 698.355469 325.425781 Z M 698.355469 321.191406 " transform="matrix(1,0,0,1,-607,-197)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 698.355469 314.027344 L 716.5 314.027344 L 716.5 318.257812 L 698.355469 318.257812 Z M 698.355469 314.027344 " transform="matrix(1,0,0,1,-607,-197)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 707.429688 321.191406 L 707.429688 320.957031 " transform="matrix(1,0,0,1,-607,-197)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:butt;stroke-linejoin:miter;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 707.429688 320.160156 L 707.429688 320.957031 M 707.128906 320.957031 L 707.429688 320.160156 L 707.726562 320.957031 " transform="matrix(1,0,0,1,-607,-197)"/>
<path style=" stroke:none;fill-rule:nonzero;fill:rgb(69.804382%,85.098267%,100%);fill-opacity:1;" d="M 59.914062 135.816406 L 78.058594 135.816406 L 78.058594 131.585938 L 59.914062 131.585938 Z M 59.914062 135.816406 "/>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 666.914062 328.585938 L 685.058594 328.585938 L 685.058594 332.816406 L 666.914062 332.816406 Z M 666.914062 328.585938 " transform="matrix(1,0,0,1,-607,-197)"/>
<path style=" stroke:none;fill-rule:nonzero;fill:rgb(69.804382%,85.098267%,100%);fill-opacity:1;" d="M 59.914062 114.316406 L 78.058594 114.316406 L 78.058594 110.082031 L 59.914062 110.082031 Z M 59.914062 114.316406 "/>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 666.914062 307.082031 L 685.058594 307.082031 L 685.058594 311.316406 L 666.914062 311.316406 Z M 666.914062 307.082031 " transform="matrix(1,0,0,1,-607,-197)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:miter;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 679.113281 328.585938 L 681.207031 327.164062 " transform="matrix(1,0,0,1,-607,-197)"/>
<path style="fill-rule:nonzero;fill:rgb(0%,0%,0%);fill-opacity:1;stroke-width:1;stroke-linecap:butt;stroke-linejoin:miter;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 681.871094 326.714844 L 681.039062 326.914062 L 681.375 327.414062 Z M 681.871094 326.714844 " transform="matrix(1,0,0,1,-607,-197)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:miter;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 672.910156 328.585938 L 670.871094 327.179688 " transform="matrix(1,0,0,1,-607,-197)"/>
<path style="fill-rule:nonzero;fill:rgb(0%,0%,0%);fill-opacity:1;stroke-width:1;stroke-linecap:butt;stroke-linejoin:miter;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 670.210938 326.726562 L 670.699219 327.429688 L 671.039062 326.933594 Z M 670.210938 326.726562 " transform="matrix(1,0,0,1,-607,-197)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:miter;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 668.644531 314.25 L 670.6875 312.847656 " transform="matrix(1,0,0,1,-607,-197)"/>
<path style="fill-rule:nonzero;fill:rgb(0%,0%,0%);fill-opacity:1;stroke-width:1;stroke-linecap:butt;stroke-linejoin:miter;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 671.34375 312.390625 L 670.515625 312.597656 L 670.855469 313.09375 Z M 671.34375 312.390625 " transform="matrix(1,0,0,1,-607,-197)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:miter;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 683.445312 314.25 L 681.347656 312.828125 " transform="matrix(1,0,0,1,-607,-197)"/>
<path style="fill-rule:nonzero;fill:rgb(0%,0%,0%);fill-opacity:1;stroke-width:1;stroke-linecap:butt;stroke-linejoin:miter;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 680.683594 312.378906 L 681.179688 313.078125 L 681.515625 312.582031 Z M 680.683594 312.378906 " transform="matrix(1,0,0,1,-607,-197)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:miter;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 685.058594 330.996094 C 685.0625 330.996094 685.066406 330.996094 685.066406 330.996094 L 702.429688 330.996094 C 704.445312 330.996094 706.179688 329.804688 706.972656 328.085938 " transform="matrix(1,0,0,1,-607,-197)"/>
<path style="fill-rule:nonzero;fill:rgb(0%,0%,0%);fill-opacity:1;stroke-width:1;stroke-linecap:butt;stroke-linejoin:miter;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 707.105469 327.300781 L 706.675781 328.035156 L 707.265625 328.136719 Z M 707.105469 327.300781 " transform="matrix(1,0,0,1,-607,-197)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:miter;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 666.914062 330.941406 C 666.90625 330.945312 666.898438 330.945312 666.890625 330.945312 L 649.570312 330.945312 C 646.808594 330.945312 644.570312 328.707031 644.570312 325.945312 L 644.570312 324.765625 " transform="matrix(1,0,0,1,-607,-197)"/>
<path style="fill-rule:nonzero;fill:rgb(0%,0%,0%);fill-opacity:1;stroke-width:1;stroke-linecap:butt;stroke-linejoin:miter;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 644.570312 323.964844 L 644.273438 324.765625 L 644.871094 324.765625 Z M 644.570312 323.964844 " transform="matrix(1,0,0,1,-607,-197)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:miter;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 644.570312 317.832031 L 644.570312 314.308594 C 644.570312 311.546875 646.808594 309.308594 649.570312 309.308594 L 664.214844 309.308594 " transform="matrix(1,0,0,1,-607,-197)"/>
<path style="fill-rule:nonzero;fill:rgb(0%,0%,0%);fill-opacity:1;stroke-width:1;stroke-linecap:butt;stroke-linejoin:miter;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 665.015625 309.308594 L 664.214844 309.007812 L 664.214844 309.609375 Z M 665.015625 309.308594 " transform="matrix(1,0,0,1,-607,-197)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:miter;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 707.394531 314.027344 C 707.109375 311.535156 704.996094 309.601562 702.429688 309.601562 L 687.757812 309.601562 " transform="matrix(1,0,0,1,-607,-197)"/>
<path style="fill-rule:nonzero;fill:rgb(0%,0%,0%);fill-opacity:1;stroke-width:1;stroke-linecap:butt;stroke-linejoin:miter;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 686.957031 309.597656 L 687.757812 309.902344 L 687.757812 309.300781 Z M 686.957031 309.597656 " transform="matrix(1,0,0,1,-607,-197)"/>
<path style=" stroke:none;fill-rule:nonzero;fill:rgb(69.804382%,85.098267%,100%);fill-opacity:1;" d="M 28.5 65.203125 L 46.644531 65.203125 L 46.644531 60.96875 L 28.5 60.96875 Z M 28.5 65.203125 "/>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 635.5 257.96875 L 653.644531 257.96875 L 653.644531 262.203125 L 635.5 262.203125 Z M 635.5 257.96875 " transform="matrix(1,0,0,1,-607,-197)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 656.5 261.554688 L 674.640625 261.554688 L 674.640625 265.785156 L 656.5 265.785156 Z M 656.5 261.554688 " transform="matrix(1,0,0,1,-607,-197)"/>
<path style=" stroke:none;fill-rule:nonzero;fill:rgb(39.99939%,74.902344%,100%);fill-opacity:1;" d="M 49.5 61.617188 L 67.640625 61.617188 L 67.640625 57.386719 L 49.5 57.386719 Z M 49.5 61.617188 "/>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 656.5 254.386719 L 674.640625 254.386719 L 674.640625 258.617188 L 656.5 258.617188 Z M 656.5 254.386719 " transform="matrix(1,0,0,1,-607,-197)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 665.570312 261.554688 L 665.570312 261.320312 " transform="matrix(1,0,0,1,-607,-197)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:butt;stroke-linejoin:miter;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 665.570312 260.519531 L 665.570312 261.320312 M 665.269531 261.320312 L 665.570312 260.519531 L 665.871094 261.320312 " transform="matrix(1,0,0,1,-607,-197)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 677.496094 261.554688 L 695.640625 261.554688 L 695.640625 265.785156 L 677.496094 265.785156 Z M 677.496094 261.554688 " transform="matrix(1,0,0,1,-607,-197)"/>
<path style=" stroke:none;fill-rule:nonzero;fill:rgb(39.99939%,74.902344%,100%);fill-opacity:1;" d="M 70.496094 61.617188 L 88.640625 61.617188 L 88.640625 57.386719 L 70.496094 57.386719 Z M 70.496094 61.617188 "/>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 677.496094 254.386719 L 695.640625 254.386719 L 695.640625 258.617188 L 677.496094 258.617188 Z M 677.496094 254.386719 " transform="matrix(1,0,0,1,-607,-197)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 686.570312 261.554688 L 686.570312 261.320312 " transform="matrix(1,0,0,1,-607,-197)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:butt;stroke-linejoin:miter;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 686.570312 260.519531 L 686.570312 261.320312 M 686.269531 261.320312 L 686.570312 260.519531 L 686.871094 261.320312 " transform="matrix(1,0,0,1,-607,-197)"/>
<path style=" stroke:none;fill-rule:nonzero;fill:rgb(69.804382%,85.098267%,100%);fill-opacity:1;" d="M 91.355469 68.5625 L 109.5 68.5625 L 109.5 64.328125 L 91.355469 64.328125 Z M 91.355469 68.5625 "/>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 698.355469 261.328125 L 716.5 261.328125 L 716.5 265.5625 L 698.355469 265.5625 Z M 698.355469 261.328125 " transform="matrix(1,0,0,1,-607,-197)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 698.355469 254.160156 L 716.5 254.160156 L 716.5 258.394531 L 698.355469 258.394531 Z M 698.355469 254.160156 " transform="matrix(1,0,0,1,-607,-197)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 707.429688 261.328125 L 707.429688 261.09375 " transform="matrix(1,0,0,1,-607,-197)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:butt;stroke-linejoin:miter;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 707.429688 260.296875 L 707.429688 261.09375 M 707.128906 261.09375 L 707.429688 260.296875 L 707.726562 261.09375 " transform="matrix(1,0,0,1,-607,-197)"/>
<path style=" stroke:none;fill-rule:nonzero;fill:rgb(69.804382%,85.098267%,100%);fill-opacity:1;" d="M 59.914062 75.953125 L 78.058594 75.953125 L 78.058594 71.722656 L 59.914062 71.722656 Z M 59.914062 75.953125 "/>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 666.914062 268.722656 L 685.058594 268.722656 L 685.058594 272.953125 L 666.914062 272.953125 Z M 666.914062 268.722656 " transform="matrix(1,0,0,1,-607,-197)"/>
<path style=" stroke:none;fill-rule:nonzero;fill:rgb(69.804382%,85.098267%,100%);fill-opacity:1;" d="M 59.914062 54.453125 L 78.058594 54.453125 L 78.058594 50.21875 L 59.914062 50.21875 Z M 59.914062 54.453125 "/>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 666.914062 247.21875 L 685.058594 247.21875 L 685.058594 251.453125 L 666.914062 251.453125 Z M 666.914062 247.21875 " transform="matrix(1,0,0,1,-607,-197)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:miter;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 679.113281 268.722656 L 681.207031 267.300781 " transform="matrix(1,0,0,1,-607,-197)"/>
<path style="fill-rule:nonzero;fill:rgb(0%,0%,0%);fill-opacity:1;stroke-width:1;stroke-linecap:butt;stroke-linejoin:miter;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 681.871094 266.851562 L 681.039062 267.050781 L 681.375 267.550781 Z M 681.871094 266.851562 " transform="matrix(1,0,0,1,-607,-197)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:miter;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 672.910156 268.722656 L 670.871094 267.316406 " transform="matrix(1,0,0,1,-607,-197)"/>
<path style="fill-rule:nonzero;fill:rgb(0%,0%,0%);fill-opacity:1;stroke-width:1;stroke-linecap:butt;stroke-linejoin:miter;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 670.210938 266.863281 L 670.699219 267.566406 L 671.039062 267.070312 Z M 670.210938 266.863281 " transform="matrix(1,0,0,1,-607,-197)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:miter;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 668.644531 254.386719 L 670.6875 252.980469 " transform="matrix(1,0,0,1,-607,-197)"/>
<path style="fill-rule:nonzero;fill:rgb(0%,0%,0%);fill-opacity:1;stroke-width:1;stroke-linecap:butt;stroke-linejoin:miter;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 671.34375 252.527344 L 670.515625 252.734375 L 670.855469 253.230469 Z M 671.34375 252.527344 " transform="matrix(1,0,0,1,-607,-197)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:miter;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 683.445312 254.386719 L 681.347656 252.964844 " transform="matrix(1,0,0,1,-607,-197)"/>
<path style="fill-rule:nonzero;fill:rgb(0%,0%,0%);fill-opacity:1;stroke-width:1;stroke-linecap:butt;stroke-linejoin:miter;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 680.683594 252.515625 L 681.179688 253.214844 L 681.515625 252.71875 Z M 680.683594 252.515625 " transform="matrix(1,0,0,1,-607,-197)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:miter;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 685.058594 271.132812 C 685.0625 271.132812 685.066406 271.132812 685.066406 271.132812 L 702.429688 271.132812 C 704.445312 271.132812 706.179688 269.941406 706.972656 268.222656 " transform="matrix(1,0,0,1,-607,-197)"/>
<path style="fill-rule:nonzero;fill:rgb(0%,0%,0%);fill-opacity:1;stroke-width:1;stroke-linecap:butt;stroke-linejoin:miter;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 707.105469 267.433594 L 706.675781 268.171875 L 707.265625 268.273438 Z M 707.105469 267.433594 " transform="matrix(1,0,0,1,-607,-197)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:miter;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 666.914062 271.078125 C 666.90625 271.078125 666.898438 271.082031 666.890625 271.082031 L 649.570312 271.082031 C 646.808594 271.082031 644.570312 268.84375 644.570312 266.082031 L 644.570312 264.902344 " transform="matrix(1,0,0,1,-607,-197)"/>
<path style="fill-rule:nonzero;fill:rgb(0%,0%,0%);fill-opacity:1;stroke-width:1;stroke-linecap:butt;stroke-linejoin:miter;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 644.570312 264.101562 L 644.273438 264.902344 L 644.871094 264.902344 Z M 644.570312 264.101562 " transform="matrix(1,0,0,1,-607,-197)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:miter;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 644.570312 257.96875 L 644.570312 254.445312 C 644.570312 251.683594 646.808594 249.445312 649.570312 249.445312 L 664.214844 249.445312 " transform="matrix(1,0,0,1,-607,-197)"/>
<path style="fill-rule:nonzero;fill:rgb(0%,0%,0%);fill-opacity:1;stroke-width:1;stroke-linecap:butt;stroke-linejoin:miter;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 665.015625 249.445312 L 664.214844 249.144531 L 664.214844 249.746094 Z M 665.015625 249.445312 " transform="matrix(1,0,0,1,-607,-197)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:miter;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 707.394531 254.160156 C 707.109375 251.671875 704.996094 249.738281 702.429688 249.738281 L 687.757812 249.738281 " transform="matrix(1,0,0,1,-607,-197)"/>
<path style="fill-rule:nonzero;fill:rgb(0%,0%,0%);fill-opacity:1;stroke-width:1;stroke-linecap:butt;stroke-linejoin:miter;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 686.957031 249.734375 L 687.757812 250.035156 L 687.757812 249.4375 Z M 686.957031 249.734375 " transform="matrix(1,0,0,1,-607,-197)"/>
<path style=" stroke:none;fill-rule:nonzero;fill:rgb(69.804382%,85.098267%,100%);fill-opacity:1;" d="M 28.5 100.519531 L 109.5 100.519531 L 109.5 85.519531 L 28.5 85.519531 Z M 28.5 100.519531 "/>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 635.5 282.519531 L 716.5 282.519531 L 716.5 297.519531 L 635.5 297.519531 Z M 635.5 282.519531 " transform="matrix(1,0,0,1,-607,-197)"/>
<g style="fill:rgb(0%,0%,0%);fill-opacity:1;">
  <use xlink:href="#glyph0-1" x="34.8271" y="96.8218"/>
  <use xlink:href="#glyph0-2" x="39.8329" y="96.8218"/>
</g>
<g clip-path="url(#clip7)" clip-rule="nonzero">
<g style="fill:rgb(0%,0%,0%);fill-opacity:1;">
  <use xlink:href="#glyph1-1" x="41.362" y="96.73691"/>
</g>
<g style="fill:rgb(0%,0%,0%);fill-opacity:1;">
  <use xlink:href="#glyph1-1" x="41.362" y="96.73691"/>
</g>
</g>
<g style="fill:rgb(0%,0%,0%);fill-opacity:1;">
  <use xlink:href="#glyph2-1" x="47.333" y="96.8218"/>
</g>
<g style="fill:rgb(0%,0%,0%);fill-opacity:1;">
  <use xlink:href="#glyph0-1" x="50.667" y="96.8218"/>
  <use xlink:href="#glyph0-2" x="55.6728" y="96.8218"/>
</g>
<g style="fill:rgb(0%,0%,0%);fill-opacity:1;">
  <use xlink:href="#glyph3-1" x="58.1729" y="96.8218"/>
  <use xlink:href="#glyph3-2" x="67.1729" y="96.8218"/>
  <use xlink:href="#glyph3-3" x="76.1729" y="96.8218"/>
  <use xlink:href="#glyph3-4" x="85.1729" y="96.8218"/>
  <use xlink:href="#glyph3-5" x="94.1729" y="96.8218"/>
</g>
<path style=" stroke:none;fill-rule:nonzero;fill:rgb(69.804382%,85.098267%,100%);fill-opacity:1;" d="M 28.5 40.65625 L 109.5 40.65625 L 109.5 25.65625 L 28.5 25.65625 Z M 28.5 40.65625 "/>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 635.5 222.65625 L 716.5 222.65625 L 716.5 237.65625 L 635.5 237.65625 Z M 635.5 222.65625 " transform="matrix(1,0,0,1,-607,-197)"/>
<g style="fill:rgb(0%,0%,0%);fill-opacity:1;">
  <use xlink:href="#glyph3-8" x="37.5" y="36.6544"/>
  <use xlink:href="#glyph3-9" x="46.5" y="36.6544"/>
  <use xlink:href="#glyph3-10" x="55.5" y="36.6544"/>
  <use xlink:href="#glyph3-11" x="64.5" y="36.6544"/>
  <use xlink:href="#glyph3-3" x="73.5" y="36.6544"/>
  <use xlink:href="#glyph3-4" x="82.5" y="36.6544"/>
  <use xlink:href="#glyph3-5" x="91.5" y="36.6544"/>
</g>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 635.5 198.089844 L 716.5 198.089844 L 716.5 213.089844 L 635.5 213.089844 Z M 635.5 198.089844 " transform="matrix(1,0,0,1,-607,-197)"/>
<g style="fill:rgb(0%,0%,0%);fill-opacity:1;">
  <use xlink:href="#glyph3-8" x="51" y="12.0908"/>
  <use xlink:href="#glyph3-12" x="60" y="12.0908"/>
  <use xlink:href="#glyph3-13" x="69" y="12.0908"/>
  <use xlink:href="#glyph3-5" x="78" y="12.0908"/>
</g>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:miter;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 676 500.5 L 676 496.035156 " transform="matrix(1,0,0,1,-607,-197)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:butt;stroke-linejoin:miter;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 676 492.835938 L 676 496.035156 M 674.800781 496.035156 L 676 492.835938 L 677.199219 496.035156 " transform="matrix(1,0,0,1,-607,-197)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:miter;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 676 475.9375 L 676 471.472656 " transform="matrix(1,0,0,1,-607,-197)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:butt;stroke-linejoin:miter;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 676 468.273438 L 676 471.472656 M 674.800781 471.472656 L 676 468.273438 L 677.199219 471.472656 " transform="matrix(1,0,0,1,-607,-197)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:miter;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 676 451.371094 L 676 446.910156 " transform="matrix(1,0,0,1,-607,-197)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:butt;stroke-linejoin:miter;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 676 443.710938 L 676 446.910156 M 674.800781 446.910156 L 676 443.710938 L 677.199219 446.910156 " transform="matrix(1,0,0,1,-607,-197)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:miter;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 676 426.808594 L 676 422.34375 " transform="matrix(1,0,0,1,-607,-197)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:butt;stroke-linejoin:miter;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 676 419.144531 L 676 422.34375 M 674.800781 422.34375 L 676 419.144531 L 677.199219 422.34375 " transform="matrix(1,0,0,1,-607,-197)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:miter;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 676 402.246094 L 676 400.15625 L 675.992188 397.511719 L 675.984375 400.15625 L 675.984375 397.78125 " transform="matrix(1,0,0,1,-607,-197)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:butt;stroke-linejoin:miter;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 675.984375 394.582031 L 675.984375 397.78125 M 674.785156 397.78125 L 675.984375 394.582031 L 677.1875 397.78125 " transform="matrix(1,0,0,1,-607,-197)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:miter;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 676 222.65625 L 676 218.191406 " transform="matrix(1,0,0,1,-607,-197)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:butt;stroke-linejoin:miter;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 676 214.992188 L 676 218.191406 M 674.800781 218.191406 L 676 214.992188 L 677.199219 218.191406 " transform="matrix(1,0,0,1,-607,-197)"/>
<g style="fill:rgb(0%,0%,0%);fill-opacity:1;">
  <use xlink:href="#glyph0-5" x="5.2471" y="184.8135"/>
  <use xlink:href="#glyph0-2" x="10.2529" y="184.8135"/>
</g>
<g clip-path="url(#clip8)" clip-rule="nonzero">
<g style="fill:rgb(0%,0%,0%);fill-opacity:1;">
  <use xlink:href="#glyph1-1" x="11.862" y="184.73691"/>
</g>
<g style="fill:rgb(0%,0%,0%);fill-opacity:1;">
  <use xlink:href="#glyph1-1" x="11.862" y="184.73691"/>
</g>
</g>
<g style="fill:rgb(0%,0%,0%);fill-opacity:1;">
  <use xlink:href="#glyph0-6" x="5.2471" y="124.9499"/>
  <use xlink:href="#glyph0-2" x="10.2529" y="124.9499"/>
</g>
<g clip-path="url(#clip9)" clip-rule="nonzero">
<g style="fill:rgb(0%,0%,0%);fill-opacity:1;">
  <use xlink:href="#glyph1-1" x="11.862" y="124.73691"/>
</g>
<g style="fill:rgb(0%,0%,0%);fill-opacity:1;">
  <use xlink:href="#glyph1-1" x="11.862" y="124.73691"/>
</g>
</g>
<g style="fill:rgb(0%,0%,0%);fill-opacity:1;">
  <use xlink:href="#glyph0-5" x="5.2471" y="65.0862"/>
  <use xlink:href="#glyph0-2" x="10.2529" y="65.0862"/>
</g>
<g clip-path="url(#clip10)" clip-rule="nonzero">
<g style="fill:rgb(0%,0%,0%);fill-opacity:1;">
  <use xlink:href="#glyph1-1" x="11.862" y="64.82311"/>
</g>
<g style="fill:rgb(0%,0%,0%);fill-opacity:1;">
  <use xlink:href="#glyph1-1" x="11.862" y="64.82311"/>
</g>
</g>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:miter;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 675.984375 366.945312 L 675.984375 362.480469 " transform="matrix(1,0,0,1,-607,-197)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:butt;stroke-linejoin:miter;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 675.996094 359.28125 L 675.984375 362.480469 M 674.785156 362.476562 L 675.996094 359.28125 L 677.1875 362.484375 " transform="matrix(1,0,0,1,-607,-197)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:miter;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 676 342.382812 L 676 340.292969 L 675.992188 337.648438 L 675.984375 340.292969 L 675.984375 337.917969 " transform="matrix(1,0,0,1,-607,-197)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:butt;stroke-linejoin:miter;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 675.984375 334.71875 L 675.984375 337.917969 M 674.785156 337.917969 L 675.984375 334.71875 L 677.1875 337.917969 " transform="matrix(1,0,0,1,-607,-197)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:miter;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 675.984375 307.082031 L 675.984375 302.617188 " transform="matrix(1,0,0,1,-607,-197)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:butt;stroke-linejoin:miter;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 675.996094 299.417969 L 675.984375 302.617188 M 674.785156 302.613281 L 675.996094 299.417969 L 677.1875 302.621094 " transform="matrix(1,0,0,1,-607,-197)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:miter;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 676 282.519531 L 676 280.429688 L 675.992188 277.785156 L 675.984375 280.429688 L 675.984375 278.054688 " transform="matrix(1,0,0,1,-607,-197)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:butt;stroke-linejoin:miter;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 675.984375 274.855469 L 675.984375 278.054688 M 674.785156 278.054688 L 675.984375 274.855469 L 677.1875 278.054688 " transform="matrix(1,0,0,1,-607,-197)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:miter;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 675.984375 247.21875 L 675.984375 242.753906 " transform="matrix(1,0,0,1,-607,-197)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:butt;stroke-linejoin:miter;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 675.996094 239.554688 L 675.984375 242.753906 M 674.785156 242.75 L 675.996094 239.554688 L 677.1875 242.757812 " transform="matrix(1,0,0,1,-607,-197)"/>
</g>
</svg>
