{"cells": [{"cell_type": "markdown", "id": "b3ea00d4", "metadata": {"origin_pos": 0}, "source": ["# 全卷积网络\n", ":label:`sec_fcn`\n", "\n", "如 :numref:`sec_semantic_segmentation`中所介绍的那样，语义分割是对图像中的每个像素分类。\n", "*全卷积网络*（fully convolutional network，FCN）采用卷积神经网络实现了从图像像素到像素类别的变换 :cite:`Long.Shelhamer.Darrell.2015`。\n", "与我们之前在图像分类或目标检测部分介绍的卷积神经网络不同，全卷积网络将中间层特征图的高和宽变换回输入图像的尺寸：这是通过在 :numref:`sec_transposed_conv`中引入的*转置卷积*（transposed convolution）实现的。\n", "因此，输出的类别预测与输入图像在像素级别上具有一一对应关系：通道维的输出即该位置对应像素的类别预测。\n"]}, {"cell_type": "code", "execution_count": 1, "id": "a0f2bef2", "metadata": {"execution": {"iopub.execute_input": "2022-12-07T16:48:36.475116Z", "iopub.status.busy": "2022-12-07T16:48:36.474810Z", "iopub.status.idle": "2022-12-07T16:48:40.126096Z", "shell.execute_reply": "2022-12-07T16:48:40.125026Z"}, "origin_pos": 2, "tab": ["pytorch"]}, "outputs": [], "source": ["%matplotlib inline\n", "import torch\n", "import torchvision\n", "from torch import nn\n", "from torch.nn import functional as F\n", "from d2l import torch as d2l"]}, {"cell_type": "markdown", "id": "c005d9b8", "metadata": {"origin_pos": 4}, "source": ["## 构造模型\n", "\n", "下面我们了解一下全卷积网络模型最基本的设计。\n", "如 :numref:`fig_fcn`所示，全卷积网络先使用卷积神经网络抽取图像特征，然后通过$1\\times 1$卷积层将通道数变换为类别个数，最后在 :numref:`sec_transposed_conv`中通过转置卷积层将特征图的高和宽变换为输入图像的尺寸。\n", "因此，模型输出与输入图像的高和宽相同，且最终输出通道包含了该空间位置像素的类别预测。\n", "\n", "![全卷积网络](../img/fcn.svg)\n", ":label:`fig_fcn`\n", "\n", "下面，我们[**使用在ImageNet数据集上预训练的ResNet-18模型来提取图像特征**]，并将该网络记为`pretrained_net`。\n", "ResNet-18模型的最后几层包括全局平均汇聚层和全连接层，然而全卷积网络中不需要它们。\n"]}, {"cell_type": "code", "execution_count": 2, "id": "2010ccf6", "metadata": {"execution": {"iopub.execute_input": "2022-12-07T16:48:40.131351Z", "iopub.status.busy": "2022-12-07T16:48:40.130696Z", "iopub.status.idle": "2022-12-07T16:48:40.444764Z", "shell.execute_reply": "2022-12-07T16:48:40.443615Z"}, "origin_pos": 6, "tab": ["pytorch"]}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["/home/<USER>/miniconda3/envs/d2l-zh-release-1/lib/python3.9/site-packages/torchvision/models/_utils.py:208: UserWarning: The parameter 'pretrained' is deprecated since 0.13 and will be removed in 0.15, please use 'weights' instead.\n", "  warnings.warn(\n", "/home/<USER>/miniconda3/envs/d2l-zh-release-1/lib/python3.9/site-packages/torchvision/models/_utils.py:223: UserWarning: Arguments other than a weight enum or `None` for 'weights' are deprecated since 0.13 and will be removed in 0.15. The current behavior is equivalent to passing `weights=ResNet18_Weights.IMAGENET1K_V1`. You can also use `weights=ResNet18_Weights.DEFAULT` to get the most up-to-date weights.\n", "  warnings.warn(msg)\n"]}, {"data": {"text/plain": ["[Sequential(\n", "   (0): BasicBlock(\n", "     (conv1): Conv2d(256, 512, kernel_size=(3, 3), stride=(2, 2), padding=(1, 1), bias=False)\n", "     (bn1): BatchNorm2d(512, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)\n", "     (relu): ReLU(inplace=True)\n", "     (conv2): Conv2d(512, 512, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1), bias=False)\n", "     (bn2): BatchNorm2d(512, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)\n", "     (downsample): Sequential(\n", "       (0): Conv2d(256, 512, kernel_size=(1, 1), stride=(2, 2), bias=False)\n", "       (1): BatchNorm2d(512, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)\n", "     )\n", "   )\n", "   (1): BasicBlock(\n", "     (conv1): Conv2d(512, 512, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1), bias=False)\n", "     (bn1): BatchNorm2d(512, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)\n", "     (relu): ReLU(inplace=True)\n", "     (conv2): Conv2d(512, 512, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1), bias=False)\n", "     (bn2): BatchNorm2d(512, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)\n", "   )\n", " ),\n", " AdaptiveAvgPool2d(output_size=(1, 1)),\n", " Linear(in_features=512, out_features=1000, bias=True)]"]}, "execution_count": 2, "metadata": {}, "output_type": "execute_result"}], "source": ["pretrained_net = torchvision.models.resnet18(pretrained=True)\n", "list(pretrained_net.children())[-3:]"]}, {"cell_type": "markdown", "id": "81cbf850", "metadata": {"origin_pos": 8}, "source": ["接下来，我们[**创建一个全卷积网络`net`**]。\n", "它复制了ResNet-18中大部分的预训练层，除了最后的全局平均汇聚层和最接近输出的全连接层。\n"]}, {"cell_type": "code", "execution_count": 3, "id": "4a256279", "metadata": {"execution": {"iopub.execute_input": "2022-12-07T16:48:40.448469Z", "iopub.status.busy": "2022-12-07T16:48:40.447672Z", "iopub.status.idle": "2022-12-07T16:48:40.452899Z", "shell.execute_reply": "2022-12-07T16:48:40.451796Z"}, "origin_pos": 10, "tab": ["pytorch"]}, "outputs": [], "source": ["net = nn.Sequential(*list(pretrained_net.children())[:-2])"]}, {"cell_type": "markdown", "id": "02b56f45", "metadata": {"origin_pos": 11}, "source": ["给定高度为320和宽度为480的输入，`net`的前向传播将输入的高和宽减小至原来的$1/32$，即10和15。\n"]}, {"cell_type": "code", "execution_count": 4, "id": "fd06b85e", "metadata": {"execution": {"iopub.execute_input": "2022-12-07T16:48:40.457152Z", "iopub.status.busy": "2022-12-07T16:48:40.456496Z", "iopub.status.idle": "2022-12-07T16:48:40.523073Z", "shell.execute_reply": "2022-12-07T16:48:40.522244Z"}, "origin_pos": 13, "tab": ["pytorch"]}, "outputs": [{"data": {"text/plain": ["torch.Size([1, 512, 10, 15])"]}, "execution_count": 4, "metadata": {}, "output_type": "execute_result"}], "source": ["X = torch.rand(size=(1, 3, 320, 480))\n", "net(X).shape"]}, {"cell_type": "markdown", "id": "f548587e", "metadata": {"origin_pos": 15}, "source": ["接下来[**使用$1\\times1$卷积层将输出通道数转换为Pascal VOC2012数据集的类数（21类）。**]\n", "最后需要(**将特征图的高度和宽度增加32倍**)，从而将其变回输入图像的高和宽。\n", "回想一下 :numref:`sec_padding`中卷积层输出形状的计算方法：\n", "由于$(320-64+16\\times2+32)/32=10$且$(480-64+16\\times2+32)/32=15$，我们构造一个步幅为$32$的转置卷积层，并将卷积核的高和宽设为$64$，填充为$16$。\n", "我们可以看到如果步幅为$s$，填充为$s/2$（假设$s/2$是整数）且卷积核的高和宽为$2s$，转置卷积核会将输入的高和宽分别放大$s$倍。\n"]}, {"cell_type": "code", "execution_count": 5, "id": "f7d7002a", "metadata": {"execution": {"iopub.execute_input": "2022-12-07T16:48:40.527754Z", "iopub.status.busy": "2022-12-07T16:48:40.527109Z", "iopub.status.idle": "2022-12-07T16:48:40.548726Z", "shell.execute_reply": "2022-12-07T16:48:40.547795Z"}, "origin_pos": 17, "tab": ["pytorch"]}, "outputs": [], "source": ["num_classes = 21\n", "net.add_module('final_conv', nn.Conv2d(512, num_classes, kernel_size=1))\n", "net.add_module('transpose_conv', nn.ConvTranspose2d(num_classes, num_classes,\n", "                                    kernel_size=64, padding=16, stride=32))"]}, {"cell_type": "markdown", "id": "cb0b9cd7", "metadata": {"origin_pos": 19}, "source": ["## [**初始化转置卷积层**]\n", "\n", "在图像处理中，我们有时需要将图像放大，即*上采样*（upsampling）。\n", "*双线性插值*（bilinear interpolation）\n", "是常用的上采样方法之一，它也经常用于初始化转置卷积层。\n", "\n", "为了解释双线性插值，假设给定输入图像，我们想要计算上采样输出图像上的每个像素。\n", "\n", "1. 将输出图像的坐标$(x,y)$映射到输入图像的坐标$(x',y')$上。\n", "例如，根据输入与输出的尺寸之比来映射。\n", "请注意，映射后的$x′$和$y′$是实数。\n", "2. 在输入图像上找到离坐标$(x',y')$最近的4个像素。\n", "3. 输出图像在坐标$(x,y)$上的像素依据输入图像上这4个像素及其与$(x',y')$的相对距离来计算。\n", "\n", "双线性插值的上采样可以通过转置卷积层实现，内核由以下`bilinear_kernel`函数构造。\n", "限于篇幅，我们只给出`bilinear_kernel`函数的实现，不讨论算法的原理。\n"]}, {"cell_type": "code", "execution_count": 6, "id": "3612cc4b", "metadata": {"execution": {"iopub.execute_input": "2022-12-07T16:48:40.552469Z", "iopub.status.busy": "2022-12-07T16:48:40.551923Z", "iopub.status.idle": "2022-12-07T16:48:40.557812Z", "shell.execute_reply": "2022-12-07T16:48:40.557073Z"}, "origin_pos": 21, "tab": ["pytorch"]}, "outputs": [], "source": ["def bilinear_kernel(in_channels, out_channels, kernel_size):\n", "    factor = (kernel_size + 1) // 2\n", "    if kernel_size % 2 == 1:\n", "        center = factor - 1\n", "    else:\n", "        center = factor - 0.5\n", "    og = (torch.arange(kernel_size).reshape(-1, 1),\n", "          torch.arange(kernel_size).reshape(1, -1))\n", "    filt = (1 - torch.abs(og[0] - center) / factor) * \\\n", "           (1 - torch.abs(og[1] - center) / factor)\n", "    weight = torch.zeros((in_channels, out_channels,\n", "                          kernel_size, kernel_size))\n", "    weight[range(in_channels), range(out_channels), :, :] = filt\n", "    return weight"]}, {"cell_type": "markdown", "id": "ef34a7e7", "metadata": {"origin_pos": 23}, "source": ["让我们用[**双线性插值的上采样实验**]它由转置卷积层实现。\n", "我们构造一个将输入的高和宽放大2倍的转置卷积层，并将其卷积核用`bilinear_kernel`函数初始化。\n"]}, {"cell_type": "code", "execution_count": 7, "id": "cc67f96c", "metadata": {"execution": {"iopub.execute_input": "2022-12-07T16:48:40.561082Z", "iopub.status.busy": "2022-12-07T16:48:40.560572Z", "iopub.status.idle": "2022-12-07T16:48:40.566460Z", "shell.execute_reply": "2022-12-07T16:48:40.565462Z"}, "origin_pos": 25, "tab": ["pytorch"]}, "outputs": [], "source": ["conv_trans = nn.ConvTranspose2d(3, 3, kernel_size=4, padding=1, stride=2,\n", "                                bias=False)\n", "conv_trans.weight.data.copy_(bilinear_kernel(3, 3, 4));"]}, {"cell_type": "markdown", "id": "40cc183b", "metadata": {"origin_pos": 27}, "source": ["读取图像`X`，将上采样的结果记作`Y`。为了打印图像，我们需要调整通道维的位置。\n"]}, {"cell_type": "code", "execution_count": 8, "id": "d208a70a", "metadata": {"execution": {"iopub.execute_input": "2022-12-07T16:48:40.569766Z", "iopub.status.busy": "2022-12-07T16:48:40.569245Z", "iopub.status.idle": "2022-12-07T16:48:40.626082Z", "shell.execute_reply": "2022-12-07T16:48:40.625223Z"}, "origin_pos": 29, "tab": ["pytorch"]}, "outputs": [], "source": ["img = torchvision.transforms.ToTensor()(d2l.Image.open('../img/catdog.jpg'))\n", "X = img.unsqueeze(0)\n", "Y = conv_trans(X)\n", "out_img = Y[0].permute(1, 2, 0).detach()"]}, {"cell_type": "markdown", "id": "46848d3b", "metadata": {"origin_pos": 31}, "source": ["可以看到，转置卷积层将图像的高和宽分别放大了2倍。\n", "除了坐标刻度不同，双线性插值放大的图像和在 :numref:`sec_bbox`中打印出的原图看上去没什么两样。\n"]}, {"cell_type": "code", "execution_count": 9, "id": "5752cf84", "metadata": {"execution": {"iopub.execute_input": "2022-12-07T16:48:40.630011Z", "iopub.status.busy": "2022-12-07T16:48:40.629419Z", "iopub.status.idle": "2022-12-07T16:48:41.381845Z", "shell.execute_reply": "2022-12-07T16:48:41.380536Z"}, "origin_pos": 33, "tab": ["pytorch"]}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["input image shape: torch.<PERSON>ze([561, 728, 3])\n", "output image shape: torch.Size([1122, 1456, 3])\n"]}, {"data": {"image/svg+xml": ["<?xml version=\"1.0\" encoding=\"utf-8\" standalone=\"no\"?>\n", "<!DOCTYPE svg PUBLIC \"-//W3C//DTD SVG 1.1//EN\"\n", "  \"http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd\">\n", "<svg xmlns:xlink=\"http://www.w3.org/1999/xlink\" width=\"226.708824pt\" height=\"173.415579pt\" viewBox=\"0 0 226.**********.415579\" xmlns=\"http://www.w3.org/2000/svg\" version=\"1.1\">\n", " <metadata>\n", "  <rdf:RDF xmlns:dc=\"http://purl.org/dc/elements/1.1/\" xmlns:cc=\"http://creativecommons.org/ns#\" xmlns:rdf=\"http://www.w3.org/1999/02/22-rdf-syntax-ns#\">\n", "   <cc:Work>\n", "    <dc:type rdf:resource=\"http://purl.org/dc/dcmitype/StillImage\"/>\n", "    <dc:date>2022-12-07T16:48:41.152439</dc:date>\n", "    <dc:format>image/svg+xml</dc:format>\n", "    <dc:creator>\n", "     <cc:Agent>\n", "      <dc:title>Matplotlib v3.5.1, https://matplotlib.org/</dc:title>\n", "     </cc:Agent>\n", "    </dc:creator>\n", "   </cc:Work>\n", "  </rdf:RDF>\n", " </metadata>\n", " <defs>\n", "  <style type=\"text/css\">*{stroke-linejoin: round; stroke-linecap: butt}</style>\n", " </defs>\n", " <g id=\"figure_1\">\n", "  <g id=\"patch_1\">\n", "   <path d=\"M 0 173.415579 \n", "L 226.**********.415579 \n", "L 226.708824 0 \n", "L 0 0 \n", "z\n", "\" style=\"fill: #ffffff\"/>\n", "  </g>\n", "  <g id=\"axes_1\">\n", "   <g id=\"patch_2\">\n", "    <path d=\"M 39.65 149.537454 \n", "L 219.**********.537454 \n", "L 219.508824 10.937454 \n", "L 39.65 10.937454 \n", "z\n", "\" style=\"fill: #ffffff\"/>\n", "   </g>\n", "   <g clip-path=\"url(#p3765bda00d)\">\n", "    <image xlink:href=\"data:image/png;base64,\n", "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\" id=\"imagebac161b128\" transform=\"scale(1 -1)translate(0 -138.96)\" x=\"40\" y=\"-10.455579\" width=\"180\" height=\"138.96\"/>\n", "   </g>\n", "   <g id=\"matplotlib.axis_1\">\n", "    <g id=\"xtick_1\">\n", "     <g id=\"line2d_1\">\n", "      <defs>\n", "       <path id=\"m683569e4b2\" d=\"M 0 0 \n", "L 0 3.5 \n", "\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </defs>\n", "      <g>\n", "       <use xlink:href=\"#m683569e4b2\" x=\"39.711765\" y=\"149.537454\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_1\">\n", "      <!-- 0 -->\n", "      <g transform=\"translate(36.530515 164.135892)scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-30\" d=\"M 2034 4250 \n", "Q 1547 4250 1301 3770 \n", "Q 1056 3291 1056 2328 \n", "Q 1056 1369 1301 889 \n", "Q 1547 409 2034 409 \n", "Q 2525 409 2770 889 \n", "Q 3016 1369 3016 2328 \n", "Q 3016 3291 2770 3770 \n", "Q 2525 4250 2034 4250 \n", "z\n", "M 2034 4750 \n", "Q 2819 4750 3233 4129 \n", "Q 3647 3509 3647 2328 \n", "Q 3647 1150 3233 529 \n", "Q 2819 -91 2034 -91 \n", "Q 1250 -91 836 529 \n", "Q 422 1150 422 2328 \n", "Q 422 3509 836 4129 \n", "Q 1250 4750 2034 4750 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_2\">\n", "     <g id=\"line2d_2\">\n", "      <g>\n", "       <use xlink:href=\"#m683569e4b2\" x=\"101.476471\" y=\"149.537454\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_2\">\n", "      <!-- 500 -->\n", "      <g transform=\"translate(91.932721 164.135892)scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-35\" d=\"M 691 4666 \n", "L 3169 4666 \n", "L 3169 4134 \n", "L 1269 4134 \n", "L 1269 2991 \n", "Q 1406 3038 1543 3061 \n", "Q 1681 3084 1819 3084 \n", "Q 2600 3084 3056 2656 \n", "Q 3513 2228 3513 1497 \n", "Q 3513 744 3044 326 \n", "Q 2575 -91 1722 -91 \n", "Q 1428 -91 1123 -41 \n", "Q 819 9 494 109 \n", "L 494 744 \n", "Q 775 591 1075 516 \n", "Q 1375 441 1709 441 \n", "Q 2250 441 2565 725 \n", "Q 2881 1009 2881 1497 \n", "Q 2881 1984 2565 2268 \n", "Q 2250 2553 1709 2553 \n", "Q 1456 2553 1204 2497 \n", "Q 953 2441 691 2322 \n", "L 691 4666 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-35\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"127.246094\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_3\">\n", "     <g id=\"line2d_3\">\n", "      <g>\n", "       <use xlink:href=\"#m683569e4b2\" x=\"163.241176\" y=\"149.537454\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_3\">\n", "      <!-- 1000 -->\n", "      <g transform=\"translate(150.516176 164.135892)scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-31\" d=\"M 794 531 \n", "L 1825 531 \n", "L 1825 4091 \n", "L 703 3866 \n", "L 703 4441 \n", "L 1819 4666 \n", "L 2450 4666 \n", "L 2450 531 \n", "L 3481 531 \n", "L 3481 0 \n", "L 794 0 \n", "L 794 531 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-31\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"127.246094\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"190.869141\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "   </g>\n", "   <g id=\"matplotlib.axis_2\">\n", "    <g id=\"ytick_1\">\n", "     <g id=\"line2d_4\">\n", "      <defs>\n", "       <path id=\"m48cb0fb4ba\" d=\"M 0 0 \n", "L -3.5 0 \n", "\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </defs>\n", "      <g>\n", "       <use xlink:href=\"#m48cb0fb4ba\" x=\"39.65\" y=\"10.999219\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_4\">\n", "      <!-- 0 -->\n", "      <g transform=\"translate(26.2875 14.798438)scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_2\">\n", "     <g id=\"line2d_5\">\n", "      <g>\n", "       <use xlink:href=\"#m48cb0fb4ba\" x=\"39.65\" y=\"35.705101\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_5\">\n", "      <!-- 200 -->\n", "      <g transform=\"translate(13.5625 39.50432)scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-32\" d=\"M 1228 531 \n", "L 3431 531 \n", "L 3431 0 \n", "L 469 0 \n", "L 469 531 \n", "Q 828 903 1448 1529 \n", "Q 2069 2156 2228 2338 \n", "Q 2531 2678 2651 2914 \n", "Q 2772 3150 2772 3378 \n", "Q 2772 3750 2511 3984 \n", "Q 2250 4219 1831 4219 \n", "Q 1534 4219 1204 4116 \n", "Q 875 4013 500 3803 \n", "L 500 4441 \n", "Q 881 4594 1212 4672 \n", "Q 1544 4750 1819 4750 \n", "Q 2544 4750 2975 4387 \n", "Q 3406 4025 3406 3419 \n", "Q 3406 3131 3298 2873 \n", "Q 3191 2616 2906 2266 \n", "Q 2828 2175 2409 1742 \n", "Q 1991 1309 1228 531 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-32\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"127.246094\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_3\">\n", "     <g id=\"line2d_6\">\n", "      <g>\n", "       <use xlink:href=\"#m48cb0fb4ba\" x=\"39.65\" y=\"60.410983\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_6\">\n", "      <!-- 400 -->\n", "      <g transform=\"translate(13.5625 64.210202)scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-34\" d=\"M 2419 4116 \n", "L 825 1625 \n", "L 2419 1625 \n", "L 2419 4116 \n", "z\n", "M 2253 4666 \n", "L 3047 4666 \n", "L 3047 1625 \n", "L 3713 1625 \n", "L 3713 1100 \n", "L 3047 1100 \n", "L 3047 0 \n", "L 2419 0 \n", "L 2419 1100 \n", "L 313 1100 \n", "L 313 1709 \n", "L 2253 4666 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-34\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"127.246094\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_4\">\n", "     <g id=\"line2d_7\">\n", "      <g>\n", "       <use xlink:href=\"#m48cb0fb4ba\" x=\"39.65\" y=\"85.116866\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_7\">\n", "      <!-- 600 -->\n", "      <g transform=\"translate(13.5625 88.916085)scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-36\" d=\"M 2113 2584 \n", "Q 1688 2584 1439 2293 \n", "Q 1191 2003 1191 1497 \n", "Q 1191 994 1439 701 \n", "Q 1688 409 2113 409 \n", "Q 2538 409 2786 701 \n", "Q 3034 994 3034 1497 \n", "Q 3034 2003 2786 2293 \n", "Q 2538 2584 2113 2584 \n", "z\n", "M 3366 4563 \n", "L 3366 3988 \n", "Q 3128 4100 2886 4159 \n", "Q 2644 4219 2406 4219 \n", "Q 1781 4219 1451 3797 \n", "Q 1122 3375 1075 2522 \n", "Q 1259 2794 1537 2939 \n", "Q 1816 3084 2150 3084 \n", "Q 2853 3084 3261 2657 \n", "Q 3669 2231 3669 1497 \n", "Q 3669 778 3244 343 \n", "Q 2819 -91 2113 -91 \n", "Q 1303 -91 875 529 \n", "Q 447 1150 447 2328 \n", "Q 447 3434 972 4092 \n", "Q 1497 4750 2381 4750 \n", "Q 2619 4750 2861 4703 \n", "Q 3103 4656 3366 4563 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-36\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"127.246094\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_5\">\n", "     <g id=\"line2d_8\">\n", "      <g>\n", "       <use xlink:href=\"#m48cb0fb4ba\" x=\"39.65\" y=\"109.822748\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_8\">\n", "      <!-- 800 -->\n", "      <g transform=\"translate(13.5625 113.621967)scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-38\" d=\"M 2034 2216 \n", "Q 1584 2216 1326 1975 \n", "Q 1069 1734 1069 1313 \n", "Q 1069 891 1326 650 \n", "Q 1584 409 2034 409 \n", "Q 2484 409 2743 651 \n", "Q 3003 894 3003 1313 \n", "Q 3003 1734 2745 1975 \n", "Q 2488 2216 2034 2216 \n", "z\n", "M 1403 2484 \n", "Q 997 2584 770 2862 \n", "Q 544 3141 544 3541 \n", "Q 544 4100 942 4425 \n", "Q 1341 4750 2034 4750 \n", "Q 2731 4750 3128 4425 \n", "Q 3525 4100 3525 3541 \n", "Q 3525 3141 3298 2862 \n", "Q 3072 2584 2669 2484 \n", "Q 3125 2378 3379 2068 \n", "Q 3634 1759 3634 1313 \n", "Q 3634 634 3220 271 \n", "Q 2806 -91 2034 -91 \n", "Q 1263 -91 848 271 \n", "Q 434 634 434 1313 \n", "Q 434 1759 690 2068 \n", "Q 947 2378 1403 2484 \n", "z\n", "M 1172 3481 \n", "Q 1172 3119 1398 2916 \n", "Q 1625 2713 2034 2713 \n", "Q 2441 2713 2670 2916 \n", "Q 2900 3119 2900 3481 \n", "Q 2900 3844 2670 4047 \n", "Q 2441 4250 2034 4250 \n", "Q 1625 4250 1398 4047 \n", "Q 1172 3844 1172 3481 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-38\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"127.246094\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_6\">\n", "     <g id=\"line2d_9\">\n", "      <g>\n", "       <use xlink:href=\"#m48cb0fb4ba\" x=\"39.65\" y=\"134.528631\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_9\">\n", "      <!-- 1000 -->\n", "      <g transform=\"translate(7.2 138.327849)scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-31\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"127.246094\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"190.869141\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "   </g>\n", "   <g id=\"patch_3\">\n", "    <path d=\"M 39.65 149.537454 \n", "L 39.65 10.937454 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_4\">\n", "    <path d=\"M 219.**********.537454 \n", "L 219.508824 10.937454 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_5\">\n", "    <path d=\"M 39.65 149.537454 \n", "L 219.**********.537454 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_6\">\n", "    <path d=\"M 39.65 10.937454 \n", "L 219.508824 10.937454 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "  </g>\n", " </g>\n", " <defs>\n", "  <clipPath id=\"p3765bda00d\">\n", "   <rect x=\"39.65\" y=\"10.937454\" width=\"179.858824\" height=\"138.6\"/>\n", "  </clipPath>\n", " </defs>\n", "</svg>\n"], "text/plain": ["<Figure size 350x250 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["d2l.set_figsize()\n", "print('input image shape:', img.permute(1, 2, 0).shape)\n", "d2l.plt.imshow(img.permute(1, 2, 0));\n", "print('output image shape:', out_img.shape)\n", "d2l.plt.imshow(out_img);"]}, {"cell_type": "markdown", "id": "57467c74", "metadata": {"origin_pos": 35}, "source": ["全卷积网络[**用双线性插值的上采样初始化转置卷积层。对于$1\\times 1$卷积层，我们使用Xavier初始化参数。**]\n"]}, {"cell_type": "code", "execution_count": 10, "id": "1f110836", "metadata": {"execution": {"iopub.execute_input": "2022-12-07T16:48:41.387401Z", "iopub.status.busy": "2022-12-07T16:48:41.386567Z", "iopub.status.idle": "2022-12-07T16:48:41.401057Z", "shell.execute_reply": "2022-12-07T16:48:41.399862Z"}, "origin_pos": 37, "tab": ["pytorch"]}, "outputs": [], "source": ["W = bilinear_kernel(num_classes, num_classes, 64)\n", "net.transpose_conv.weight.data.copy_(W);"]}, {"cell_type": "markdown", "id": "e001ea71", "metadata": {"origin_pos": 39}, "source": ["## [**读取数据集**]\n", "\n", "我们用 :numref:`sec_semantic_segmentation`中介绍的语义分割读取数据集。\n", "指定随机裁剪的输出图像的形状为$320\\times 480$：高和宽都可以被$32$整除。\n"]}, {"cell_type": "code", "execution_count": 11, "id": "297c01a8", "metadata": {"execution": {"iopub.execute_input": "2022-12-07T16:48:41.406109Z", "iopub.status.busy": "2022-12-07T16:48:41.405552Z", "iopub.status.idle": "2022-12-07T16:49:10.180323Z", "shell.execute_reply": "2022-12-07T16:49:10.179515Z"}, "origin_pos": 40, "tab": ["pytorch"]}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["read 1114 examples\n"]}, {"name": "stdout", "output_type": "stream", "text": ["read 1078 examples\n"]}], "source": ["batch_size, crop_size = 32, (320, 480)\n", "train_iter, test_iter = d2l.load_data_voc(batch_size, crop_size)"]}, {"cell_type": "markdown", "id": "f99bb3a2", "metadata": {"origin_pos": 42}, "source": ["## [**训练**]\n", "\n", "现在我们可以训练全卷积网络了。\n", "这里的损失函数和准确率计算与图像分类中的并没有本质上的不同，因为我们使用转置卷积层的通道来预测像素的类别，所以需要在损失计算中指定通道维。\n", "此外，模型基于每个像素的预测类别是否正确来计算准确率。\n"]}, {"cell_type": "code", "execution_count": 12, "id": "8b36ff5b", "metadata": {"execution": {"iopub.execute_input": "2022-12-07T16:49:10.183912Z", "iopub.status.busy": "2022-12-07T16:49:10.183331Z", "iopub.status.idle": "2022-12-07T16:50:04.171945Z", "shell.execute_reply": "2022-12-07T16:50:04.170746Z"}, "origin_pos": 44, "tab": ["pytorch"]}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["loss 0.443, train acc 0.863, test acc 0.852\n", "265.6 examples/sec on [device(type='cuda', index=0), device(type='cuda', index=1)]\n"]}, {"data": {"image/svg+xml": ["<?xml version=\"1.0\" encoding=\"utf-8\" standalone=\"no\"?>\n", "<!DOCTYPE svg PUBLIC \"-//W3C//DTD SVG 1.1//EN\"\n", "  \"http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd\">\n", "<svg xmlns:xlink=\"http://www.w3.org/1999/xlink\" width=\"235.784375pt\" height=\"187.155469pt\" viewBox=\"0 0 235.**********.155469\" xmlns=\"http://www.w3.org/2000/svg\" version=\"1.1\">\n", " <metadata>\n", "  <rdf:RDF xmlns:dc=\"http://purl.org/dc/elements/1.1/\" xmlns:cc=\"http://creativecommons.org/ns#\" xmlns:rdf=\"http://www.w3.org/1999/02/22-rdf-syntax-ns#\">\n", "   <cc:Work>\n", "    <dc:type rdf:resource=\"http://purl.org/dc/dcmitype/StillImage\"/>\n", "    <dc:date>2022-12-07T16:50:04.127023</dc:date>\n", "    <dc:format>image/svg+xml</dc:format>\n", "    <dc:creator>\n", "     <cc:Agent>\n", "      <dc:title>Matplotlib v3.5.1, https://matplotlib.org/</dc:title>\n", "     </cc:Agent>\n", "    </dc:creator>\n", "   </cc:Work>\n", "  </rdf:RDF>\n", " </metadata>\n", " <defs>\n", "  <style type=\"text/css\">*{stroke-linejoin: round; stroke-linecap: butt}</style>\n", " </defs>\n", " <g id=\"figure_1\">\n", "  <g id=\"patch_1\">\n", "   <path d=\"M 0 187.155469 \n", "L 235.**********.155469 \n", "L 235.784375 0 \n", "L 0 0 \n", "z\n", "\" style=\"fill: #ffffff\"/>\n", "  </g>\n", "  <g id=\"axes_1\">\n", "   <g id=\"patch_2\">\n", "    <path d=\"M 30.**********.599219 \n", "L 225.**********.599219 \n", "L 225.403125 10.999219 \n", "L 30.103125 10.999219 \n", "z\n", "\" style=\"fill: #ffffff\"/>\n", "   </g>\n", "   <g id=\"matplotlib.axis_1\">\n", "    <g id=\"xtick_1\">\n", "     <g id=\"line2d_1\">\n", "      <path d=\"M 30.**********.599219 \n", "L 30.103125 10.999219 \n", "\" clip-path=\"url(#pe92cc41de1)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_2\">\n", "      <defs>\n", "       <path id=\"m20d972ccd6\" d=\"M 0 0 \n", "L 0 3.5 \n", "\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </defs>\n", "      <g>\n", "       <use xlink:href=\"#m20d972ccd6\" x=\"30.103125\" y=\"149.599219\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_1\">\n", "      <!-- 1 -->\n", "      <g transform=\"translate(26.921875 164.197656)scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-31\" d=\"M 794 531 \n", "L 1825 531 \n", "L 1825 4091 \n", "L 703 3866 \n", "L 703 4441 \n", "L 1819 4666 \n", "L 2450 4666 \n", "L 2450 531 \n", "L 3481 531 \n", "L 3481 0 \n", "L 794 0 \n", "L 794 531 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-31\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_2\">\n", "     <g id=\"line2d_3\">\n", "      <path d=\"M 78.928125 149.599219 \n", "L 78.928125 10.999219 \n", "\" clip-path=\"url(#pe92cc41de1)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_4\">\n", "      <g>\n", "       <use xlink:href=\"#m20d972ccd6\" x=\"78.928125\" y=\"149.599219\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_2\">\n", "      <!-- 2 -->\n", "      <g transform=\"translate(75.746875 164.197656)scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-32\" d=\"M 1228 531 \n", "L 3431 531 \n", "L 3431 0 \n", "L 469 0 \n", "L 469 531 \n", "Q 828 903 1448 1529 \n", "Q 2069 2156 2228 2338 \n", "Q 2531 2678 2651 2914 \n", "Q 2772 3150 2772 3378 \n", "Q 2772 3750 2511 3984 \n", "Q 2250 4219 1831 4219 \n", "Q 1534 4219 1204 4116 \n", "Q 875 4013 500 3803 \n", "L 500 4441 \n", "Q 881 4594 1212 4672 \n", "Q 1544 4750 1819 4750 \n", "Q 2544 4750 2975 4387 \n", "Q 3406 4025 3406 3419 \n", "Q 3406 3131 3298 2873 \n", "Q 3191 2616 2906 2266 \n", "Q 2828 2175 2409 1742 \n", "Q 1991 1309 1228 531 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-32\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_3\">\n", "     <g id=\"line2d_5\">\n", "      <path d=\"M 127.753125 149.599219 \n", "L 127.753125 10.999219 \n", "\" clip-path=\"url(#pe92cc41de1)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_6\">\n", "      <g>\n", "       <use xlink:href=\"#m20d972ccd6\" x=\"127.753125\" y=\"149.599219\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_3\">\n", "      <!-- 3 -->\n", "      <g transform=\"translate(124.571875 164.197656)scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-33\" d=\"M 2597 2516 \n", "Q 3050 2419 3304 2112 \n", "Q 3559 1806 3559 1356 \n", "Q 3559 666 3084 287 \n", "Q 2609 -91 1734 -91 \n", "Q 1441 -91 1130 -33 \n", "Q 819 25 488 141 \n", "L 488 750 \n", "Q 750 597 1062 519 \n", "Q 1375 441 1716 441 \n", "Q 2309 441 2620 675 \n", "Q 2931 909 2931 1356 \n", "Q 2931 1769 2642 2001 \n", "Q 2353 2234 1838 2234 \n", "L 1294 2234 \n", "L 1294 2753 \n", "L 1863 2753 \n", "Q 2328 2753 2575 2939 \n", "Q 2822 3125 2822 3475 \n", "Q 2822 3834 2567 4026 \n", "Q 2313 4219 1838 4219 \n", "Q 1578 4219 1281 4162 \n", "Q 984 4106 628 3988 \n", "L 628 4550 \n", "Q 988 4650 1302 4700 \n", "Q 1616 4750 1894 4750 \n", "Q 2613 4750 3031 4423 \n", "Q 3450 4097 3450 3541 \n", "Q 3450 3153 3228 2886 \n", "Q 3006 2619 2597 2516 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-33\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_4\">\n", "     <g id=\"line2d_7\">\n", "      <path d=\"M 176.578125 149.599219 \n", "L 176.578125 10.999219 \n", "\" clip-path=\"url(#pe92cc41de1)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_8\">\n", "      <g>\n", "       <use xlink:href=\"#m20d972ccd6\" x=\"176.578125\" y=\"149.599219\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_4\">\n", "      <!-- 4 -->\n", "      <g transform=\"translate(173.396875 164.197656)scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-34\" d=\"M 2419 4116 \n", "L 825 1625 \n", "L 2419 1625 \n", "L 2419 4116 \n", "z\n", "M 2253 4666 \n", "L 3047 4666 \n", "L 3047 1625 \n", "L 3713 1625 \n", "L 3713 1100 \n", "L 3047 1100 \n", "L 3047 0 \n", "L 2419 0 \n", "L 2419 1100 \n", "L 313 1100 \n", "L 313 1709 \n", "L 2253 4666 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-34\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_5\">\n", "     <g id=\"line2d_9\">\n", "      <path d=\"M 225.**********.599219 \n", "L 225.403125 10.999219 \n", "\" clip-path=\"url(#pe92cc41de1)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_10\">\n", "      <g>\n", "       <use xlink:href=\"#m20d972ccd6\" x=\"225.403125\" y=\"149.599219\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_5\">\n", "      <!-- 5 -->\n", "      <g transform=\"translate(222.221875 164.197656)scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-35\" d=\"M 691 4666 \n", "L 3169 4666 \n", "L 3169 4134 \n", "L 1269 4134 \n", "L 1269 2991 \n", "Q 1406 3038 1543 3061 \n", "Q 1681 3084 1819 3084 \n", "Q 2600 3084 3056 2656 \n", "Q 3513 2228 3513 1497 \n", "Q 3513 744 3044 326 \n", "Q 2575 -91 1722 -91 \n", "Q 1428 -91 1123 -41 \n", "Q 819 9 494 109 \n", "L 494 744 \n", "Q 775 591 1075 516 \n", "Q 1375 441 1709 441 \n", "Q 2250 441 2565 725 \n", "Q 2881 1009 2881 1497 \n", "Q 2881 1984 2565 2268 \n", "Q 2250 2553 1709 2553 \n", "Q 1456 2553 1204 2497 \n", "Q 953 2441 691 2322 \n", "L 691 4666 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-35\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"text_6\">\n", "     <!-- epoch -->\n", "     <g transform=\"translate(112.525 177.875781)scale(0.1 -0.1)\">\n", "      <defs>\n", "       <path id=\"DejaVuSans-65\" d=\"M 3597 1894 \n", "L 3597 1613 \n", "L 953 1613 \n", "Q 991 1019 1311 708 \n", "Q 1631 397 2203 397 \n", "Q 2534 397 2845 478 \n", "Q 3156 559 3463 722 \n", "L 3463 178 \n", "Q 3153 47 2828 -22 \n", "Q 2503 -91 2169 -91 \n", "Q 1331 -91 842 396 \n", "Q 353 884 353 1716 \n", "Q 353 2575 817 3079 \n", "Q 1281 3584 2069 3584 \n", "Q 2775 3584 3186 3129 \n", "Q 3597 2675 3597 1894 \n", "z\n", "M 3022 2063 \n", "Q 3016 2534 2758 2815 \n", "Q 2500 3097 2075 3097 \n", "Q 1594 3097 1305 2825 \n", "Q 1016 2553 972 2059 \n", "L 3022 2063 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-70\" d=\"M 1159 525 \n", "L 1159 -1331 \n", "L 581 -1331 \n", "L 581 3500 \n", "L 1159 3500 \n", "L 1159 2969 \n", "Q 1341 3281 1617 3432 \n", "Q 1894 3584 2278 3584 \n", "Q 2916 3584 3314 3078 \n", "Q 3713 2572 3713 1747 \n", "Q 3713 922 3314 415 \n", "Q 2916 -91 2278 -91 \n", "Q 1894 -91 1617 61 \n", "Q 1341 213 1159 525 \n", "z\n", "M 3116 1747 \n", "Q 3116 2381 2855 2742 \n", "Q 2594 3103 2138 3103 \n", "Q 1681 3103 1420 2742 \n", "Q 1159 2381 1159 1747 \n", "Q 1159 1113 1420 752 \n", "Q 1681 391 2138 391 \n", "Q 2594 391 2855 752 \n", "Q 3116 1113 3116 1747 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-6f\" d=\"M 1959 3097 \n", "Q 1497 3097 1228 2736 \n", "Q 959 2375 959 1747 \n", "Q 959 1119 1226 758 \n", "Q 1494 397 1959 397 \n", "Q 2419 397 2687 759 \n", "Q 2956 1122 2956 1747 \n", "Q 2956 2369 2687 2733 \n", "Q 2419 3097 1959 3097 \n", "z\n", "M 1959 3584 \n", "Q 2709 3584 3137 3096 \n", "Q 3566 2609 3566 1747 \n", "Q 3566 888 3137 398 \n", "Q 2709 -91 1959 -91 \n", "Q 1206 -91 779 398 \n", "Q 353 888 353 1747 \n", "Q 353 2609 779 3096 \n", "Q 1206 3584 1959 3584 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-63\" d=\"M 3122 3366 \n", "L 3122 2828 \n", "Q 2878 2963 2633 3030 \n", "Q 2388 3097 2138 3097 \n", "Q 1578 3097 1268 2742 \n", "Q 959 2388 959 1747 \n", "Q 959 1106 1268 751 \n", "Q 1578 397 2138 397 \n", "Q 2388 397 2633 464 \n", "Q 2878 531 3122 666 \n", "L 3122 134 \n", "Q 2881 22 2623 -34 \n", "Q 2366 -91 2075 -91 \n", "Q 1284 -91 818 406 \n", "Q 353 903 353 1747 \n", "Q 353 2603 823 3093 \n", "Q 1294 3584 2113 3584 \n", "Q 2378 3584 2631 3529 \n", "Q 2884 3475 3122 3366 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-68\" d=\"M 3513 2113 \n", "L 3513 0 \n", "L 2938 0 \n", "L 2938 2094 \n", "Q 2938 2591 2744 2837 \n", "Q 2550 3084 2163 3084 \n", "Q 1697 3084 1428 2787 \n", "Q 1159 2491 1159 1978 \n", "L 1159 0 \n", "L 581 0 \n", "L 581 4863 \n", "L 1159 4863 \n", "L 1159 2956 \n", "Q 1366 3272 1645 3428 \n", "Q 1925 3584 2291 3584 \n", "Q 2894 3584 3203 3211 \n", "Q 3513 2838 3513 2113 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "      </defs>\n", "      <use xlink:href=\"#DejaVuSans-65\"/>\n", "      <use xlink:href=\"#DejaVuSans-70\" x=\"61.523438\"/>\n", "      <use xlink:href=\"#DejaVuSans-6f\" x=\"125\"/>\n", "      <use xlink:href=\"#DejaVuSans-63\" x=\"186.181641\"/>\n", "      <use xlink:href=\"#DejaVuSans-68\" x=\"241.162109\"/>\n", "     </g>\n", "    </g>\n", "   </g>\n", "   <g id=\"matplotlib.axis_2\">\n", "    <g id=\"ytick_1\">\n", "     <g id=\"line2d_11\">\n", "      <path d=\"M 30.**********.599219 \n", "L 225.**********.599219 \n", "\" clip-path=\"url(#pe92cc41de1)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_12\">\n", "      <defs>\n", "       <path id=\"mf24f395f07\" d=\"M 0 0 \n", "L -3.5 0 \n", "\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </defs>\n", "      <g>\n", "       <use xlink:href=\"#mf24f395f07\" x=\"30.103125\" y=\"149.599219\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_7\">\n", "      <!-- 0.0 -->\n", "      <g transform=\"translate(7.2 153.398438)scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-30\" d=\"M 2034 4250 \n", "Q 1547 4250 1301 3770 \n", "Q 1056 3291 1056 2328 \n", "Q 1056 1369 1301 889 \n", "Q 1547 409 2034 409 \n", "Q 2525 409 2770 889 \n", "Q 3016 1369 3016 2328 \n", "Q 3016 3291 2770 3770 \n", "Q 2525 4250 2034 4250 \n", "z\n", "M 2034 4750 \n", "Q 2819 4750 3233 4129 \n", "Q 3647 3509 3647 2328 \n", "Q 3647 1150 3233 529 \n", "Q 2819 -91 2034 -91 \n", "Q 1250 -91 836 529 \n", "Q 422 1150 422 2328 \n", "Q 422 3509 836 4129 \n", "Q 1250 4750 2034 4750 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "        <path id=\"DejaVuSans-2e\" d=\"M 684 794 \n", "L 1344 794 \n", "L 1344 0 \n", "L 684 0 \n", "L 684 794 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"95.410156\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_2\">\n", "     <g id=\"line2d_13\">\n", "      <path d=\"M 30.103125 121.879219 \n", "L 225.403125 121.879219 \n", "\" clip-path=\"url(#pe92cc41de1)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_14\">\n", "      <g>\n", "       <use xlink:href=\"#mf24f395f07\" x=\"30.103125\" y=\"121.879219\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_8\">\n", "      <!-- 0.2 -->\n", "      <g transform=\"translate(7.2 125.678438)scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-32\" x=\"95.410156\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_3\">\n", "     <g id=\"line2d_15\">\n", "      <path d=\"M 30.103125 94.159219 \n", "L 225.403125 94.159219 \n", "\" clip-path=\"url(#pe92cc41de1)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_16\">\n", "      <g>\n", "       <use xlink:href=\"#mf24f395f07\" x=\"30.103125\" y=\"94.159219\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_9\">\n", "      <!-- 0.4 -->\n", "      <g transform=\"translate(7.2 97.958438)scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-34\" x=\"95.410156\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_4\">\n", "     <g id=\"line2d_17\">\n", "      <path d=\"M 30.103125 66.439219 \n", "L 225.403125 66.439219 \n", "\" clip-path=\"url(#pe92cc41de1)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_18\">\n", "      <g>\n", "       <use xlink:href=\"#mf24f395f07\" x=\"30.103125\" y=\"66.439219\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_10\">\n", "      <!-- 0.6 -->\n", "      <g transform=\"translate(7.2 70.238437)scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-36\" d=\"M 2113 2584 \n", "Q 1688 2584 1439 2293 \n", "Q 1191 2003 1191 1497 \n", "Q 1191 994 1439 701 \n", "Q 1688 409 2113 409 \n", "Q 2538 409 2786 701 \n", "Q 3034 994 3034 1497 \n", "Q 3034 2003 2786 2293 \n", "Q 2538 2584 2113 2584 \n", "z\n", "M 3366 4563 \n", "L 3366 3988 \n", "Q 3128 4100 2886 4159 \n", "Q 2644 4219 2406 4219 \n", "Q 1781 4219 1451 3797 \n", "Q 1122 3375 1075 2522 \n", "Q 1259 2794 1537 2939 \n", "Q 1816 3084 2150 3084 \n", "Q 2853 3084 3261 2657 \n", "Q 3669 2231 3669 1497 \n", "Q 3669 778 3244 343 \n", "Q 2819 -91 2113 -91 \n", "Q 1303 -91 875 529 \n", "Q 447 1150 447 2328 \n", "Q 447 3434 972 4092 \n", "Q 1497 4750 2381 4750 \n", "Q 2619 4750 2861 4703 \n", "Q 3103 4656 3366 4563 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-36\" x=\"95.410156\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_5\">\n", "     <g id=\"line2d_19\">\n", "      <path d=\"M 30.103125 38.719219 \n", "L 225.403125 38.719219 \n", "\" clip-path=\"url(#pe92cc41de1)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_20\">\n", "      <g>\n", "       <use xlink:href=\"#mf24f395f07\" x=\"30.103125\" y=\"38.719219\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_11\">\n", "      <!-- 0.8 -->\n", "      <g transform=\"translate(7.2 42.518438)scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-38\" d=\"M 2034 2216 \n", "Q 1584 2216 1326 1975 \n", "Q 1069 1734 1069 1313 \n", "Q 1069 891 1326 650 \n", "Q 1584 409 2034 409 \n", "Q 2484 409 2743 651 \n", "Q 3003 894 3003 1313 \n", "Q 3003 1734 2745 1975 \n", "Q 2488 2216 2034 2216 \n", "z\n", "M 1403 2484 \n", "Q 997 2584 770 2862 \n", "Q 544 3141 544 3541 \n", "Q 544 4100 942 4425 \n", "Q 1341 4750 2034 4750 \n", "Q 2731 4750 3128 4425 \n", "Q 3525 4100 3525 3541 \n", "Q 3525 3141 3298 2862 \n", "Q 3072 2584 2669 2484 \n", "Q 3125 2378 3379 2068 \n", "Q 3634 1759 3634 1313 \n", "Q 3634 634 3220 271 \n", "Q 2806 -91 2034 -91 \n", "Q 1263 -91 848 271 \n", "Q 434 634 434 1313 \n", "Q 434 1759 690 2068 \n", "Q 947 2378 1403 2484 \n", "z\n", "M 1172 3481 \n", "Q 1172 3119 1398 2916 \n", "Q 1625 2713 2034 2713 \n", "Q 2441 2713 2670 2916 \n", "Q 2900 3119 2900 3481 \n", "Q 2900 3844 2670 4047 \n", "Q 2441 4250 2034 4250 \n", "Q 1625 4250 1398 4047 \n", "Q 1172 3844 1172 3481 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-38\" x=\"95.410156\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_6\">\n", "     <g id=\"line2d_21\">\n", "      <path d=\"M 30.103125 10.999219 \n", "L 225.403125 10.999219 \n", "\" clip-path=\"url(#pe92cc41de1)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_22\">\n", "      <g>\n", "       <use xlink:href=\"#mf24f395f07\" x=\"30.103125\" y=\"10.999219\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_12\">\n", "      <!-- 1.0 -->\n", "      <g transform=\"translate(7.2 14.798438)scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-31\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"95.410156\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "   </g>\n", "   <g id=\"line2d_23\">\n", "    <path d=\"M 28.650269 -1 \n", "L 30.103125 0.845647 \n", "L 38.719301 53.833044 \n", "L 47.335478 51.249996 \n", "L 55.951654 56.51358 \n", "L 64.567831 58.936852 \n", "L 73.184007 61.042167 \n", "L 78.928125 61.506048 \n", "L 87.544301 66.689447 \n", "L 96.160478 69.321475 \n", "L 104.776654 72.327551 \n", "L 113.392831 72.578399 \n", "L 122.009007 72.874077 \n", "L 127.753125 73.108003 \n", "L 136.369301 86.883197 \n", "L 144.985478 85.28174 \n", "L 153.601654 83.21543 \n", "L 162.217831 80.338014 \n", "L 170.834007 80.111372 \n", "L 176.578125 80.8979 \n", "L 185.194301 91.846678 \n", "L 193.810478 92.154744 \n", "L 202.426654 91.40909 \n", "L 211.042831 88.884908 \n", "L 219.659007 88.094709 \n", "L 225.403125 88.157289 \n", "\" clip-path=\"url(#pe92cc41de1)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_24\">\n", "    <path d=\"M -1 54.431053 \n", "L 7.126654 52.071621 \n", "L 15.742831 48.473314 \n", "L 24.359007 46.4592 \n", "L 30.103125 45.24345 \n", "L 38.719301 36.859737 \n", "L 47.335478 37.81511 \n", "L 55.951654 36.417891 \n", "L 64.567831 35.961579 \n", "L 73.184007 35.458149 \n", "L 78.928125 35.469947 \n", "L 87.544301 34.612352 \n", "L 96.160478 34.192346 \n", "L 104.776654 33.530365 \n", "L 113.392831 33.476791 \n", "L 122.009007 33.447737 \n", "L 127.753125 33.413151 \n", "L 136.369301 30.08933 \n", "L 144.985478 30.578577 \n", "L 153.601654 30.979538 \n", "L 162.217831 31.855427 \n", "L 170.834007 31.957684 \n", "L 176.578125 31.738734 \n", "L 185.194301 28.728032 \n", "L 193.810478 28.640373 \n", "L 202.426654 29.07703 \n", "L 211.042831 29.806254 \n", "L 219.659007 30.007734 \n", "L 225.403125 30.001656 \n", "\" clip-path=\"url(#pe92cc41de1)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #bf00bf; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_25\">\n", "    <path d=\"M 30.103125 36.196196 \n", "L 78.928125 33.527589 \n", "L 127.753125 32.738423 \n", "L 176.578125 32.122596 \n", "L 225.403125 31.447183 \n", "\" clip-path=\"url(#pe92cc41de1)\" style=\"fill: none; stroke-dasharray: 9.6,2.4,1.5,2.4; stroke-dashoffset: 0; stroke: #008000; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"patch_3\">\n", "    <path d=\"M 30.**********.599219 \n", "L 30.103125 10.999219 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_4\">\n", "    <path d=\"M 225.**********.599219 \n", "L 225.403125 10.999219 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_5\">\n", "    <path d=\"M 30.**********.599219 \n", "L 225.**********.599219 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_6\">\n", "    <path d=\"M 30.103125 10.999219 \n", "L 225.403125 10.999219 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"legend_1\">\n", "    <g id=\"patch_7\">\n", "     <path d=\"M 37.103125 144.599219 \n", "L 114.871875 144.599219 \n", "Q 116.871875 144.599219 116.871875 142.599219 \n", "L 116.871875 99.564844 \n", "Q 116.871875 97.564844 114.871875 97.564844 \n", "L 37.103125 97.564844 \n", "Q 35.103125 97.564844 35.103125 99.564844 \n", "L 35.103125 142.599219 \n", "Q 35.103125 144.599219 37.103125 144.599219 \n", "z\n", "\" style=\"fill: #ffffff; opacity: 0.8; stroke: #cccccc; stroke-linejoin: miter\"/>\n", "    </g>\n", "    <g id=\"line2d_26\">\n", "     <path d=\"M 39.103125 105.663281 \n", "L 49.103125 105.663281 \n", "L 59.103125 105.663281 \n", "\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "    </g>\n", "    <g id=\"text_13\">\n", "     <!-- train loss -->\n", "     <g transform=\"translate(67.103125 109.163281)scale(0.1 -0.1)\">\n", "      <defs>\n", "       <path id=\"DejaVuSans-74\" d=\"M 1172 4494 \n", "L 1172 3500 \n", "L 2356 3500 \n", "L 2356 3053 \n", "L 1172 3053 \n", "L 1172 1153 \n", "Q 1172 725 1289 603 \n", "Q 1406 481 1766 481 \n", "L 2356 481 \n", "L 2356 0 \n", "L 1766 0 \n", "Q 1100 0 847 248 \n", "Q 594 497 594 1153 \n", "L 594 3053 \n", "L 172 3053 \n", "L 172 3500 \n", "L 594 3500 \n", "L 594 4494 \n", "L 1172 4494 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-72\" d=\"M 2631 2963 \n", "Q 2534 3019 2420 3045 \n", "Q 2306 3072 2169 3072 \n", "Q 1681 3072 1420 2755 \n", "Q 1159 2438 1159 1844 \n", "L 1159 0 \n", "L 581 0 \n", "L 581 3500 \n", "L 1159 3500 \n", "L 1159 2956 \n", "Q 1341 3275 1631 3429 \n", "Q 1922 3584 2338 3584 \n", "Q 2397 3584 2469 3576 \n", "Q 2541 3569 2628 3553 \n", "L 2631 2963 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-61\" d=\"M 2194 1759 \n", "Q 1497 1759 1228 1600 \n", "Q 959 1441 959 1056 \n", "Q 959 750 1161 570 \n", "Q 1363 391 1709 391 \n", "Q 2188 391 2477 730 \n", "Q 2766 1069 2766 1631 \n", "L 2766 1759 \n", "L 2194 1759 \n", "z\n", "M 3341 1997 \n", "L 3341 0 \n", "L 2766 0 \n", "L 2766 531 \n", "Q 2569 213 2275 61 \n", "Q 1981 -91 1556 -91 \n", "Q 1019 -91 701 211 \n", "Q 384 513 384 1019 \n", "Q 384 1609 779 1909 \n", "Q 1175 2209 1959 2209 \n", "L 2766 2209 \n", "L 2766 2266 \n", "Q 2766 2663 2505 2880 \n", "Q 2244 3097 1772 3097 \n", "Q 1472 3097 1187 3025 \n", "Q 903 2953 641 2809 \n", "L 641 3341 \n", "Q 956 3463 1253 3523 \n", "Q 1550 3584 1831 3584 \n", "Q 2591 3584 2966 3190 \n", "Q 3341 2797 3341 1997 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-69\" d=\"M 603 3500 \n", "L 1178 3500 \n", "L 1178 0 \n", "L 603 0 \n", "L 603 3500 \n", "z\n", "M 603 4863 \n", "L 1178 4863 \n", "L 1178 4134 \n", "L 603 4134 \n", "L 603 4863 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-6e\" d=\"M 3513 2113 \n", "L 3513 0 \n", "L 2938 0 \n", "L 2938 2094 \n", "Q 2938 2591 2744 2837 \n", "Q 2550 3084 2163 3084 \n", "Q 1697 3084 1428 2787 \n", "Q 1159 2491 1159 1978 \n", "L 1159 0 \n", "L 581 0 \n", "L 581 3500 \n", "L 1159 3500 \n", "L 1159 2956 \n", "Q 1366 3272 1645 3428 \n", "Q 1925 3584 2291 3584 \n", "Q 2894 3584 3203 3211 \n", "Q 3513 2838 3513 2113 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-20\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-6c\" d=\"M 603 4863 \n", "L 1178 4863 \n", "L 1178 0 \n", "L 603 0 \n", "L 603 4863 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-73\" d=\"M 2834 3397 \n", "L 2834 2853 \n", "Q 2591 2978 2328 3040 \n", "Q 2066 3103 1784 3103 \n", "Q 1356 3103 1142 2972 \n", "Q 928 2841 928 2578 \n", "Q 928 2378 1081 2264 \n", "Q 1234 2150 1697 2047 \n", "L 1894 2003 \n", "Q 2506 1872 2764 1633 \n", "Q 3022 1394 3022 966 \n", "Q 3022 478 2636 193 \n", "Q 2250 -91 1575 -91 \n", "Q 1294 -91 989 -36 \n", "Q 684 19 347 128 \n", "L 347 722 \n", "Q 666 556 975 473 \n", "Q 1284 391 1588 391 \n", "Q 1994 391 2212 530 \n", "Q 2431 669 2431 922 \n", "Q 2431 1156 2273 1281 \n", "Q 2116 1406 1581 1522 \n", "L 1381 1569 \n", "Q 847 1681 609 1914 \n", "Q 372 2147 372 2553 \n", "Q 372 3047 722 3315 \n", "Q 1072 3584 1716 3584 \n", "Q 2034 3584 2315 3537 \n", "Q 2597 3491 2834 3397 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "      </defs>\n", "      <use xlink:href=\"#DejaVuSans-74\"/>\n", "      <use xlink:href=\"#DejaVuSans-72\" x=\"39.208984\"/>\n", "      <use xlink:href=\"#DejaVuSans-61\" x=\"80.322266\"/>\n", "      <use xlink:href=\"#DejaVuSans-69\" x=\"141.601562\"/>\n", "      <use xlink:href=\"#DejaVuSans-6e\" x=\"169.384766\"/>\n", "      <use xlink:href=\"#DejaVuSans-20\" x=\"232.763672\"/>\n", "      <use xlink:href=\"#DejaVuSans-6c\" x=\"264.550781\"/>\n", "      <use xlink:href=\"#DejaVuSans-6f\" x=\"292.333984\"/>\n", "      <use xlink:href=\"#DejaVuSans-73\" x=\"353.515625\"/>\n", "      <use xlink:href=\"#DejaVuSans-73\" x=\"405.615234\"/>\n", "     </g>\n", "    </g>\n", "    <g id=\"line2d_27\">\n", "     <path d=\"M 39.103125 120.341406 \n", "L 49.103125 120.341406 \n", "L 59.103125 120.341406 \n", "\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #bf00bf; stroke-width: 1.5\"/>\n", "    </g>\n", "    <g id=\"text_14\">\n", "     <!-- train acc -->\n", "     <g transform=\"translate(67.103125 123.841406)scale(0.1 -0.1)\">\n", "      <use xlink:href=\"#DejaVuSans-74\"/>\n", "      <use xlink:href=\"#DejaVuSans-72\" x=\"39.208984\"/>\n", "      <use xlink:href=\"#DejaVuSans-61\" x=\"80.322266\"/>\n", "      <use xlink:href=\"#DejaVuSans-69\" x=\"141.601562\"/>\n", "      <use xlink:href=\"#DejaVuSans-6e\" x=\"169.384766\"/>\n", "      <use xlink:href=\"#DejaVuSans-20\" x=\"232.763672\"/>\n", "      <use xlink:href=\"#DejaVuSans-61\" x=\"264.550781\"/>\n", "      <use xlink:href=\"#DejaVuSans-63\" x=\"325.830078\"/>\n", "      <use xlink:href=\"#DejaVuSans-63\" x=\"380.810547\"/>\n", "     </g>\n", "    </g>\n", "    <g id=\"line2d_28\">\n", "     <path d=\"M 39.103125 135.019531 \n", "L 49.103125 135.019531 \n", "L 59.103125 135.019531 \n", "\" style=\"fill: none; stroke-dasharray: 9.6,2.4,1.5,2.4; stroke-dashoffset: 0; stroke: #008000; stroke-width: 1.5\"/>\n", "    </g>\n", "    <g id=\"text_15\">\n", "     <!-- test acc -->\n", "     <g transform=\"translate(67.103125 138.519531)scale(0.1 -0.1)\">\n", "      <use xlink:href=\"#DejaVuSans-74\"/>\n", "      <use xlink:href=\"#DejaVuSans-65\" x=\"39.208984\"/>\n", "      <use xlink:href=\"#DejaVuSans-73\" x=\"100.732422\"/>\n", "      <use xlink:href=\"#DejaVuSans-74\" x=\"152.832031\"/>\n", "      <use xlink:href=\"#DejaVuSans-20\" x=\"192.041016\"/>\n", "      <use xlink:href=\"#DejaVuSans-61\" x=\"223.828125\"/>\n", "      <use xlink:href=\"#DejaVuSans-63\" x=\"285.107422\"/>\n", "      <use xlink:href=\"#DejaVuSans-63\" x=\"340.087891\"/>\n", "     </g>\n", "    </g>\n", "   </g>\n", "  </g>\n", " </g>\n", " <defs>\n", "  <clipPath id=\"pe92cc41de1\">\n", "   <rect x=\"30.103125\" y=\"10.999219\" width=\"195.3\" height=\"138.6\"/>\n", "  </clipPath>\n", " </defs>\n", "</svg>\n"], "text/plain": ["<Figure size 350x250 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["def loss(inputs, targets):\n", "    return F.cross_entropy(inputs, targets, reduction='none').mean(1).mean(1)\n", "\n", "num_epochs, lr, wd, devices = 5, 0.001, 1e-3, d2l.try_all_gpus()\n", "trainer = torch.optim.SGD(net.parameters(), lr=lr, weight_decay=wd)\n", "d2l.train_ch13(net, train_iter, test_iter, loss, trainer, num_epochs, devices)"]}, {"cell_type": "markdown", "id": "05a8cac6", "metadata": {"origin_pos": 46}, "source": ["## [**预测**]\n", "\n", "在预测时，我们需要将输入图像在各个通道做标准化，并转成卷积神经网络所需要的四维输入格式。\n"]}, {"cell_type": "code", "execution_count": 13, "id": "f2a8910f", "metadata": {"execution": {"iopub.execute_input": "2022-12-07T16:50:04.176905Z", "iopub.status.busy": "2022-12-07T16:50:04.176543Z", "iopub.status.idle": "2022-12-07T16:50:04.182782Z", "shell.execute_reply": "2022-12-07T16:50:04.181964Z"}, "origin_pos": 48, "tab": ["pytorch"]}, "outputs": [], "source": ["def predict(img):\n", "    X = test_iter.dataset.normalize_image(img).unsqueeze(0)\n", "    pred = net(X.to(devices[0])).argmax(dim=1)\n", "    return pred.reshape(pred.shape[1], pred.shape[2])"]}, {"cell_type": "markdown", "id": "eeb1ee1f", "metadata": {"origin_pos": 50}, "source": ["为了[**可视化预测的类别**]给每个像素，我们将预测类别映射回它们在数据集中的标注颜色。\n"]}, {"cell_type": "code", "execution_count": 14, "id": "ae819fed", "metadata": {"execution": {"iopub.execute_input": "2022-12-07T16:50:04.186807Z", "iopub.status.busy": "2022-12-07T16:50:04.186265Z", "iopub.status.idle": "2022-12-07T16:50:04.190468Z", "shell.execute_reply": "2022-12-07T16:50:04.189708Z"}, "origin_pos": 52, "tab": ["pytorch"]}, "outputs": [], "source": ["def label2image(pred):\n", "    colormap = torch.tensor(d2l.VOC_COLORMAP, device=devices[0])\n", "    X = pred.long()\n", "    return colormap[X, :]"]}, {"cell_type": "markdown", "id": "bda10bc5", "metadata": {"origin_pos": 54}, "source": ["测试数据集中的图像大小和形状各异。\n", "由于模型使用了步幅为32的转置卷积层，因此当输入图像的高或宽无法被32整除时，转置卷积层输出的高或宽会与输入图像的尺寸有偏差。\n", "为了解决这个问题，我们可以在图像中截取多块高和宽为32的整数倍的矩形区域，并分别对这些区域中的像素做前向传播。\n", "请注意，这些区域的并集需要完整覆盖输入图像。\n", "当一个像素被多个区域所覆盖时，它在不同区域前向传播中转置卷积层输出的平均值可以作为`softmax`运算的输入，从而预测类别。\n", "\n", "为简单起见，我们只读取几张较大的测试图像，并从图像的左上角开始截取形状为$320\\times480$的区域用于预测。\n", "对于这些测试图像，我们逐一打印它们截取的区域，再打印预测结果，最后打印标注的类别。\n"]}, {"cell_type": "code", "execution_count": 15, "id": "add2119a", "metadata": {"execution": {"iopub.execute_input": "2022-12-07T16:50:04.194528Z", "iopub.status.busy": "2022-12-07T16:50:04.194030Z", "iopub.status.idle": "2022-12-07T16:50:23.687766Z", "shell.execute_reply": "2022-12-07T16:50:23.686743Z"}, "origin_pos": 56, "tab": ["pytorch"]}, "outputs": [{"data": {"image/svg+xml": ["<?xml version=\"1.0\" encoding=\"utf-8\" standalone=\"no\"?>\n", "<!DOCTYPE svg PUBLIC \"-//W3C//DTD SVG 1.1//EN\"\n", "  \"http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd\">\n", "<svg xmlns:xlink=\"http://www.w3.org/1999/xlink\" width=\"464.3pt\" height=\"317.400358pt\" viewBox=\"0 0 464.3 317.400358\" xmlns=\"http://www.w3.org/2000/svg\" version=\"1.1\">\n", " <metadata>\n", "  <rdf:RDF xmlns:dc=\"http://purl.org/dc/elements/1.1/\" xmlns:cc=\"http://creativecommons.org/ns#\" xmlns:rdf=\"http://www.w3.org/1999/02/22-rdf-syntax-ns#\">\n", "   <cc:Work>\n", "    <dc:type rdf:resource=\"http://purl.org/dc/dcmitype/StillImage\"/>\n", "    <dc:date>2022-12-07T16:50:23.525233</dc:date>\n", "    <dc:format>image/svg+xml</dc:format>\n", "    <dc:creator>\n", "     <cc:Agent>\n", "      <dc:title>Matplotlib v3.5.1, https://matplotlib.org/</dc:title>\n", "     </cc:Agent>\n", "    </dc:creator>\n", "   </cc:Work>\n", "  </rdf:RDF>\n", " </metadata>\n", " <defs>\n", "  <style type=\"text/css\">*{stroke-linejoin: round; stroke-linecap: butt}</style>\n", " </defs>\n", " <g id=\"figure_1\">\n", "  <g id=\"patch_1\">\n", "   <path d=\"M 0 317.400358 \n", "L 464.3 317.400358 \n", "L 464.3 -0 \n", "L 0 -0 \n", "z\n", "\" style=\"fill: #ffffff\"/>\n", "  </g>\n", "  <g id=\"axes_1\">\n", "   <g id=\"patch_2\">\n", "    <path d=\"M 10.7 71.895652 \n", "L 107.743478 71.895652 \n", "L 107.743478 7.2 \n", "L 10.7 7.2 \n", "z\n", "\" style=\"fill: #ffffff\"/>\n", "   </g>\n", "   <g clip-path=\"url(#p02414c4586)\">\n", "    <image xlink:href=\"data:image/png;base64,\n", "iVBORw0KGgoAAAANSUhEUgAAAIcAAABaCAYAAACSR0X7AABDdklEQVR4nKW92ZNk153f9znLvTf3rK1r7Q1AY2ksJIYAh+KQw2U049EsipHsB0fYYdmS7XDYEX5ROPxfOPzgF9tvlhRhjWJkj2RZo5E1C0kRQ4KcIQFiIRpAo/fuqq4117ucxQ/nZlZ2IjOrmnMCjcq6ee85557f7/yW7+/3OyWeefaqP7h3j7/1UhMtCg5thRsPUp5rNaldqHP55Tdpb24yzDM6x11a7RWEVkg8HhAInC8/SRCAEAIQqPKCcx6EQClVfheawPH2D99if28P4cB5gT/9Go/He48Qkje//GV++Su/TKQT7t69w82PP+avfvoOwsCwyCmsBe8Bj3OO404HpTR4jxQCRJinFAIBeO/JTIFA0KjV+O/++/+WWq0CCLSOwXuUhN3H+xyfdHDOIYRgdXUVrXWYf/ku3vvydxgM+3Q7PTY2NkB48DJMq3wX5xwPHjzgj//oX1NJYrRSbF+8xGAwZHhyiMexc+kS3/r2byBEWGXh4aTT4X/6n/8XcmO4uLPO3/8v/h5JFOEKweHjB3z8s/e59uLL7P3pn8D2Bh+fHPLFL7+Bzwt8d5+WPWG1UUHVVtBaUBSOY9UkWb/KMMsxtkA4gTUDtBYgY3RNC16+UuHVC0OOUkU6VDy/tUGSxETWsm0d5uiYQbfH5YuXyaXEl4QTQuBcYBKHRwmBwOMFhOVyACgpEALwDkEglCgJpqQu7/QICQiJACQCIQXOO5TQuNxy7/4DlpdXefba86T9Ae/+9Gfk3mC9DwQn8Id1viRIIOCIiHJ8jx//TLOUer2GQCLCBMbEtsaWTAFSSibbE0wuAqsDSPHkfZTrNJrP6E6tNbLsosgzhJBIqbDW432YsxceKSTee+qNFhcvXube/VsgBNUoQsgYy4B3f/JXXL14kaOPPqBAsHTpMvb2R3z61nfYWF/nwtYK7ZVrxM02rtpiaCTDokChydMUvKFZrwAKfBXvHVlu0NfWq7xajZHCUavCEvBX94+5uLHCpaUV4koTfdzn1biB2j2k8AVWStIkIk0iTBThkZhI4ZXECYlE4gDvXLlLS6oIjywZZbSeSRzRbLRI8wybF3gZJI4sF18JhReCleU2y40GWkuyPEXGEVIIFJ5YeLwMksc6cE8Qrlzo0e4uWVZIgVYKpyNAhL6EwIswP2cMRVHgvUNKiXOByFLKKcYQeEqmLxmfCekXpMdoo/iwBkoQRxotJQhBnuXoOAn3CjDGUBQ5OtJhIwFKC37v936H/+1//d/pPt7jsw9+TOQLhvfuUDt4xIOKprq8SbRxhZ4t+K3f+jUaq+uI6gpOCKyTDKwJ74ajkkQ44VEuofDgrMF5h7W+ZGmPPsxifmq30dIS5R2ubzX5r77+LYo05U/+/LsM7t/j+gsvkLcaaGNIckdUFFStpZXmiKMONhtincEpgYk0tlrFVKuYJIIoIlcSJ1WQCqOFLUW8AnZ2LtLPU25+9DFSSOyIc0RQPUiB9I4lJHlWIJMciUNaC84SlVvQebBC4JwtmVCMJcRoi4tyTIFARjFaRwgJzhvyPAtMICVaSRrNBmmekWZZeKJknGnpIcaqCgQS70avWC706EJ5kxAQRREjGZNmKTFhrghJnuc83n+MKUwpSRxKS9JhwVYMb1zZpHq4S3Ntnfjy87x9/xGvr7Z44aUX0NUWAk+2+wlOagzgC4F3gdG98XgZJL5xHusNWmlUFCNV+XJCgbdopSPSqIZSEVqBcIbcRfRdDmmOs46oXqVvDRLoxQIfa6SIUFTBWqQTxMai8wLd76O6Q+LHRzAcIoxFJBrVqOEbTWg3yapVXKUCWhMJwUmnh3GGpFJFCoE1BuMdznsSPHiBjypsvPACvYPH9I6PkIMhhS+QktK28TjnscLjvXyCaAJwpSqRAKWKUAIcQQceH59QrVZoNhsIIcgLg3MDsrxAyGBjiQnhEKSDLyVGGF8E2uJxOG/HTDRmEhx4iUKipcC5cofqGKU1mQ8SrlKpsLa6gUOUczTgBXY45G88u8H1b32T6MIVEIrB7iH72fdpNJboHD5geSvBWYEpHNqBLwyFAaQnUgotFeGVBVEp1JxzOOEYyVbhJd4L9CvXXuajO5+gvCeQ36KlokhzKsqxc/kSDg/e48udKAgPG+Gw5UsPtcRHMdQipF9GOYfAIa1DZTlRbkgGQ+T+HaI8RQuLl4rXV7ZxjRrF4R52ewtfrTKQggKJdQJrLVk6IKlW6Ax6VFfqxEstHncGCKmJSmJ471HSIoXAocbiX5SEkVKWxm1pRJb/hAx2RqPeolqr4hE45wCFdRKQSCFxwpXMJieYRBAsaAHl4nrvOTh4jKdAK42ONFopIqnxSoFwGAy5MUGKKY1QEqkU6TBFSUmaFcSJ4uhwn9t37qDjCq1anXqzSX11jb39fVaWriKEITMGKzwyTsg7RxSrBUIorDHoOEZFVeIkSC9ng2rzDpACZMnaQqGCJzHiejwCffdgFyctFo0UCu1gY22Njz77lGa9RSTV2AM5Fc/gRclnMly3eGRpGDoJTig8CqE9MolJBRz7JQSeyAti49HeY12Byi1rKkGf9OHxEc57RJJQ27xANxLklTa1WJLv3ednN2+hKwmpFSihkN7ivcCVBhxO4IULKkvKwNQjqTGhBnypZnwp5qX0KCXxpSRx3oN3QTKNGG3CkB4ZoYHZfLmogiSu8MLzL1OYgjRNSYcZeZ5irQHnsEVOmmWsLi/jvcVYh9QKJWXJ0J68u8fNf/+vuP3xHb7/6SOOjSfSgr/17V/l+fYqvb1HXH5Z4FRCo1rDe0fUqNOSAiUdOq4xFAKLxFmLsQbv/MRcAW9xbvQOYWwvfGnjeKSS6Dztg9cgJQ5B5obcvX0D5wxoRyQKKIqRbA7egPcgPbY0YqQIBijOoaTEiaDTlZB4IXBSYJ1HlVxphWBY0UipULJOgSO7sIQieAgyL1CFpVcYIuNZEopk94TejdtcMD0sEUUlQsrgqQR/OYzpRy6rVMgRE3Cq8kfMIX2QqaI0IHNjiIocpYPUiUpiB8YJEuNUVQUinkoNSvUCSZLwy1/+CpVKNWwaa0sDsyArUob9IZ/dusmfPtqlyA040EKipUQJAcIS5Sl+r0NTa377K69y0uly78FD9L13kHmVFZPx8Dv/iJSYJKqxlYAXktryJr3DR9TXn8UYR2E93hiklnjpx+74iAEEjsKYYHBbUXpkEqklOI9eym4hvMIqgTAFRENObn1IU0QYZzje3SOu1Us96sPCIMB7bFEELKJcOoHH2dJNDKYkSgcdV+QZmfPj3aa1xlk3JoaWGo+nKCxegFYRXmi01hxoRSXRtK5s4Pspx48eIyMdON650mH2I58yvORYTJZ7ZdKXHDEI4MufsYeqCgw7UhleQKx1cNc9Y4M2dCtPd+JIvQgPWPb2HpAkCXEcE+kIqSQm63P3h3/G49zzvR+/SyWpsHbhAtVaFak19x88QmjASqxXFO0NvvSbv0W1tcTx3mOOPvgh/uADnnnzP+D+X34fc3JCEa+wtlRhqy5ROCqtBkePhyRaEMcRcZTgSvvLeov3HmMMHjAmxxpLpCNQEqUUSkcIpcef9fXmAdZLvFD0M8n+QNO8dImDgy4b115jfXsHH1fDIpe7zXuPwxMnlaDLXLCmg2RxWGfxzuGdxbkCZy3CeaRQp1iDc8HlNRaUx3gXVIA7tQukkHiXURSawqYc7B+xVK9z4dmrEMWI7/35WCL48n+iZA5RqhXvPFL4IGFKD2mMc+BxQhIZT3b/IfnjfWQU44QgrlZKc9OjalXcyMuR4d8pP8pSZTHGe/b29pE+2G+M1JczdG5+wJ2DPs36MttbF0iiCOs9ufXlrlUgBcbnYDI++slfcO0rX6O+2ia5dp27+x9yolscyws0Lz7DhZ0XeXjjBmkRNmUO1HRE9+gB3vTRWpLbsCa6xJMCEAlGS4QLa+y8BxWkhrMe54rgoLSTHIHDokjiGiRtqrUaL20/R3upTZoOyQqLMQXOOpyxOGuhXHDnbNCn1o6RwMBIDmtMKVnCMkspg8nrLdY6Iq2CGJMKIYKoFkKiZMA2EB6cQwqIVIWombC+vM7lZy7zo/d/hpMevEX4YFuM5IT0QcwLIcbI1yT0MJIoTkjwAicl/VoVUY1p1usILzBSYo2lEB6hNJEgeCQju2Win5H94QFlHfHjI4RwGG/xSqCTBKljGiubRA9/htdNep0TBggcHivUGEORzpOsbHHlza+RFrD36ATjjpBphrj8Szw6POFQJJjugGvNJj+7/SGHhQBhKKwlbq9hBvtUlMB6i3IRRjqcdyXiDN4FkNHicMHdQHmHsAYhJQ5POuijhXIBwvFQkyl50aUexVBtYo0gihJUJIKv7oM08DYQ3JiCPM8ZDgcUziFlwBi8c3hj8EWwCbz3WBf8Gm8tQgqElBSpB1GKNCXwWGJdxSmNsCVWgceJnMJkVJttVi5vce/RPVyaor3CBgB/JD9QBK9qRLyxEe396e9i7IDipIMI6s0qzWYLY4P4lZFCVRJi6/BCBTLKkWop2WNks5TQuCyR1Gy5QZbmdI6PyYYp7VaLekMSr24R6w+DEU+wZYJkA4kP0tdKqkmNna2rJQFHqK/F2lcw1nGxtsTP/+KPuPPOj2kt1RkOh9hhDr0MJzz58T5SC/I0DZLbG7w1eA/GWlASKWI8HqVVkCpS4pRGCokCojhB+5IACo+VIJXBmy55dsjQCbzUaEaWftCtnpGFH5C+SDdxLiXNc6yR2KLAInAIpBMYU0qafIi3NhiMSlOJNEmkkMoFl05oUIIojoOItgVFWpB7QFs6ewccf+ePqck69dUVZKRwmcMKh/CytLIhEiNbJzChLw2xMVo6Um14FAplFf1hQb0BOo5PYydAii8teZA+7LhgUZ1KjpHdIb2gEB4nPJVWg9pSa4yzeAQ6l8jGCtqpsXE+/lFKDlfu6MNsiEPinEEhEdbiTIG0EFUr+Myw9/G7XHzzb+D2jzGpoft4j9zkVHKPT7vI4yH58QkkwYYw1gQDNFIYQEhFIQWoGB1HAdKXCqk1zlm0FAG3kN4FGNx4qsJSjYOhZ12OdxZrwVpXeigCRIklIEupAlqCioFIgQVnJdZ6ssyRFTlDM8TkOXgFXpOhsV6ipKNaqxDpNv2s4GTvgOdeeJH333mXtD/ACUgqCY3GMnJrGUnO/uERQkfIvAigjQwMq4TASco4zgi6PkUzTzVLMK2lVGG3pT0GfYWSGiWDNBOAkZJKrQlIhBRj9RGM0FMjV0BgUjzCGIxzpxCZlCAUyhdQi2GokFKMGcqXEihSMcZb0kHKhzd+jiao7orWRFFEFOlg5CtFimbn4jrJ2kbYADXN0CtSJ6C9waCXUBOWfhwhlKMSxSRxBZnmmLxg2OmiddjAaEm12SKqtyiMIc961Bs1NID1Afl3ASbACoXXa0FM+4A6amuJhQ2SxWV4kwcs3lis9VhnENZTFDBMXbDaZEQljol0jZg1kuqQLB1QFBlFbrB4FI5EWIadA47JME5QDHvs7z2iVXFc3H6BPKoSC8f68jqqVQdT0O6nSGFxSiEc4/iGJMQOxAi4Kw1RJgCwSawiAGgEVDZLMQi0jpBlIMwriagHJSTwOF8CSSNtNcFso3G6vRDF1TpGKY3SUYgQ9x+xtey5NzyVOL6UQlIphFSgLJGOubyzgxYx1o8M+2DbOecYdvvkgz4P94fc/8FfYpzjoxufcLx/Qv+kwy+9+grOwtHxTT65fZekUWXtwgWMK9h99JjVlVXy1OAlDAY5raUmW9JTMUOSpMrDvT1q/RhNaRQ5BFlh6aVQHw6xw37gbi9xwoOzAUL2AuNijBFYU2BNhrN5sPzLF8EbNBJXWLq9IVleIFVMFCVYImTcJFIGZzLAM5RVdK3FSqWJiKvECta3L1MRrxIndVIjMTYjHfY4to77H3/EbpqihMAEfzTs6CeCa8GvZxSqPZXgpyCYH4F7jjQdksYhAGekCuJVKmQcU+QZhTEYU1AUebAvZGlAiiBRZJmOoAj9SQHemdLNBuEdJnVY1pB0A2s4X0a1HUoqtBAh7qEEUZKgRUQkTqF/7x3eeroPHrLaKBjaiCtXnuPh8S7NVpvl1gU++ehTiBJW1lZIoogHR336aUq326darVGp1Ki3WlSqhsPOCZVaghJwfHLC6to6j27e4t7uLhub6yEwiPXkTuKlBO1ItCCq13GuwBYW4x1CebwrAuiFR6sgwoWOcVYyNB5XWASGSAgyk5MZgbUVjvsDfvj22wyHOc32EkdHXfCeZ6/s8Or1bVw+xANd2cN6gRSKSNdpr6zy6b1POD4+Ju/uU19ZZ2V1i7vHx6RFH+8i8IExrXdIwDhHbgyDwRCEHkPmchRRLXdpwGxEMAoJXpcpiiAtlS7hdnC5oTN4hNSSSiUBRHAbnSmlpgmEFkH6SKCeaCQh3K60RmgNcYSIK2S9Y0BiTJACY8IjyKzDOE9kLZEo4e3A6aX9K7Gu4PCTd7j27DPcu/UApSxCeAb9IXl6wqtfeIVLV3Zo1psMhykvvvQcveMBaXrMsDtkrd2i3+1RdLtkheGFF19gqdbgJO2zvrbEenuVjZ1t2u1ltJQOXwIvXoRAlDUWLRO8iPDCgjPgQQG6FIN5UWCtxeOwJgv6FIU1GucjtEwQOhDr/o1PebDbR0g47j3GWc/2zjat1S26qaKZbGBdTuYs0nmkdBwePCaOYh49ekSR9zCDLpXGBe7du83JYAi5IbchtlDkAUvxLuSUmDKy6l2pWiAkA52aj6fNhxD67t5j3HK7dKlLjAS4ePUaX3jjyzTqdbQumUowzvMYYTPGBRd/2O9y++YnDLod8ixn2O/ihSeOYmLh8L0TYhkxzDJ0pcLa6hq1ag0ZV3jm4ir5wW2KyibdXgcpdMnYjOH/w9u3qSaGStKg2Wyy9/hhkOjOYfOUSizoHR3h85zeUYdaNaJZWyftRxxWU4g8q5UGDz77hIvPPkN7pUW2d0i1VUM4RY4hSjSNZmlzBNvIgpPU4jiAIDoKEHMZMwlubPjdOYcWGuEchSmwhjJnoKCwjrQwmKLAmBy84PHj4+BLS4VzwXNpNJqlDRCh63Wkq6NsQTHsEccVhmlOLzc0V1eww4ikVSOux+zd3kUIR2osw+EQLyRxrU69VqdZb9Botqk1GqRZyt6dh2xc3MEpQWGKoGZECKwFNzzDGEtR5KSFx4iYUaQ1d8FL+fiTT/jpe++RphmNWpVKNaFWTUiSCFMUZFmKKyyFDbaXLw3QJI6pJAmRVnjn6GOoxJoVkbFJlw9PHmPMCq9evx6ANCGo1JZYku9zpCxEwb20wqN9QN6cM9z76F1eeu0lvFUsrebc/PQhEkU7SdisLyH6BWk/Jz84odvrE1ciVjY26XY6dLoDmsttTg4P8MaQpUPSnkaagmHaZ1ipkw4HaO1xNkePOFL4cicISzrsUymCPeBKHWqNRTDCK4LFjLcoAYlWaB9TCIWONLGJyE1BYQqKvKDerAMerRVax/R6PazNOekes7p6Ca013gVwrBAanSSkWUFSrbDdaiJ8gctPONrdQwqNFxKRCLautEmiJIT6VemqKk1eAnB3du/Q2lilHtXRlYhBmtLv90PehrVkWUpehuSTJOGzO3egTBWwxiBVcDmFjkjiCl0/5OikQ6/bociHeBvCB1FUpdFo0mi2kDpGa4kvPNZbokgQRxEilqBjXNKgPtyj1+9jZIXMC2IVIwUYFSNXrhE//Jhs5Xm8VHgnsF4h8BztPsIVKZWtV/BHu8TSMsw/wSnB1tVLrK1uU6SGvNfBmCGN7R12dx/iJHT7GZWkws1P77CxfoH1CxscZwX58QmXak18VRNXEmrVKvuHezgkOojQAMQ4QEeCIk/xRYYVwYPxLkDbDo93AW3Lc4/wHkmIWkaJJpYRthSvWZoyGAbH74Vrz/LxjU+x1rK1tc1gMOTWrdusLrW4urnJ8cExVgSC1GsNokqN/aNdPr35KYUpaPicwdEuzeUmkVTsdY7odPqkWUZeZEHlyYhGo8kzzz5DmqX09vep759w/8Yn+GqF3KQY41BahySewYA8S4OakAqtQgwl0jH1JHgZ9XoN5x39wRBrLc55jk/6HOwfMuwds7rUoMhzMtsn0jFffvNFGq021VqdeqMO5caTSoETCBTuaIn00cfIWw9RWrO8sk6lUkUJD0IjTJV29wZG5/jVZwjhSg/Oc+v7/x+/9NU3aG3s4HWCORIoG/CK1vIaKxubZP2UQ5OxcmGHbmdIfdgnqTXZuLjNYafDpeeu8PzVqzz69GbIX93ZInv0GGMVR8cnbKxv0GqvsrK6hkYKpHBEsSJNRcimsgXC2VEOC0qqEIUU4JBIWQa9SnTQmgJrXUA/fXAoo7hKQ1VwztJqrfC3/3bCzZu36PZ6XFi7wOuvv87qcoOlegMVJRjhcNYTRTGNehVPRL1Zx+ORgw7eO1bakrqr8N23fsxJb4BxYEyBVgrv4aWXrnP58mUiJfGFo3/9ZepLLR4fHdJsNhGOYKgCSskSqvA47ylyg1ISawriKKIoDHES0xkMQQhajQbeC4bDIZVqld29XbrD8N5xpPiN33iZrc11hAxeiy2yIMlKVSoBrxRRUiXVEY1Gwol3YFKEDSpEihwvWyRr10kf/iXJ8g5Gxggsnd3H+PSAjWd/FwPoZovDfcPa1ga3Dg+C7WQdMi+QWlIYw97uI27fuclRa4lGu0aWZqxdWCEfDnh0uM/Vl66zvLTMz96/Qf/giCSpkA9C5lu7vYQOwUuPlBonFM4r8sKV1r3CeRvS1ZzDOgNS4m2KtW7Cvw8AWkheUUiVoGMBUoXgmjC8+sorpOmAGzc6PPvMVb746ssoDa1mC6XjgLgiiZKYOI5JN3PiWOOFRxd99AdHXLx8kc/2cnYPO2W+RYjy5s4ihODw6JBnn30GLct5lYaiF4LtrW2UEqRpivceLRXWhSQhZy3dbo9Gs0m316FSSbDOc3zSxfZC6mBSSahUKhQf5wyHQ4wxATuREo/i4LjDJ5/dCcyTxOxsb9Fut0CGKPYoqUgndSAKYw08zUYDpeKQbSYcDz+9yfB4wJZ5wCc//Hdk8QWsh/7DO1x96SVEtR4i4FGCs5LVnauog70QMHEhr6YwBcXxIQLP7uN9NtfW2b+7x/alTYR1dB7uce3ZZ9jYWAfr+dVvfZu0DBsoFTysSrWCNkaDl2RW0U8hRdBNDUKBUBHKR0jliaQscQGPs6dZT1KI4OuXqXpIKPKCIs3xIthVB0cn7O8f0z3pkuiEm599RqUa8+qLL3Ll0pVxwo1A8GD3EcM0Iy8M2XCAVJIrly7x/OvfRAhB0rsb3ExrRyE+Rok2xoRIi9YqJOpoRUTE2toa1lvajSWSJCEvCprVOqPSB+89SbVKrVaj0aiTxDEnvR5HJz2WV5aRYonN9TV0FLG8tEznJOAUOop47bXXePnllzk5OeHGpzdRShNpyU/ffYftrQ3efONNLqxvlJ6RxKkE3bqAkVWkGoLUATD0HuckDz79jJiCfSkwJx/yaLDPg4ePWG4qXv/mf0Zmg+sqgEprCd/tloh1SLy6e+8eew/u47MC5z3XLl4hqVTZ3Nkmz1JWlpe59PKr5f0SIRVKaZKx83bqy+nD5KuoyINqUFmtor1EDDwybpBEcQiM+pBjMUIghRBYH/S3EiHs6YFOr8vB3iF7e7s8erjL/QcPGHaPqTZXWFpucf/+PQb9lDzLuPbsc1y+cjVEYDmVQjs72yGNTYRgIJS7swyaXdy5yOtfeI1ev0+n2+Phw4c8//zzpOmQL7z6Gs16DeE9XpyWBLTbbfb2HrNx4UKZryHQcUSZogEIWnJEIIeKY7q9HkklYWNpKRjSQlKvN/jNX/91ur0uf/THf8zK6gUKU/CHf/iH9Pv9YGNISRwnvPzydR7u7vPWD37Al998k2vPPosQIeJZvfIi7Qf3OLj1cchYUwHKt8aSC09eCG7uCV5aU5heSq/T4St/4+vU2+tAyPF0wtK6/BzrMsK985cIHfp57dVXca+8XDK9CymEQiAps9wI14VUAGVMViAnQg2jD/ra13+n9N0VhdXkxpAbixMgfRGkRZm3IEb1IT5kfGEdloAGHvaOuPHxp/zJn3yX3Ud7IARbm5usDHNuPrjB45UlrDV0+33qOuHg3gPeGQz4la9+Fa3LPCtf/hyl45UTHxnCAkGiI/6b//IfkDvL7/+zP6DT6fCf/if/MRfWVoh0VKrIsEtHOTjVSiWoPh+8LOcCQDURFSOOY/r9Ph44OTlBxxHbq6vEUUJ/0C/zPGFjfZULF1b5pde/xIcffcRbb701zrBKkoRqtcrx8Qk//ek7vPbaK5x0+9y5e49mrc7OzhZKKlRUQUdVhJDESYySilBEJfnGb/82Dk/34ISjG9/jhaWM/qDPtdffQMpQqiBLKQQeVWvjjUcqhVQ62IhCl267Qo22bllfBAIhdYgrTUStfZkbNpLiAPrBfq8EWGQZaQy5hKLMiXATNRkBIKLMuxjlYAiybMinn37GH/zBH9Lt9hklzd29c4+iFlNXkjwvGAxTrAUjIRWCrNvjrR/+kK995c1SJ49Al+BBqLKcQQnwztI96bG0tITAk/d6vP/+e9TrNbY3NqnGOuSAlAwlXHhp5z1RpIiiiCLPqdXrSAFaleOVCyGV5DDL6HQ6YXfXamTDIZVKgvMOYz1xHGOtpdPrc3Rywo9+9OMyt0WUEU3J8fFxuWvhxo2PefPNNyis56DTp9EeomSQEINhn8IW3LrzGUoKKkrQpOAkl1Q0DK1g17b56ff/iAsbW+x2O9DLaElDgaIeSXo+5sHuHjYzDPspzkt8uaGDlxk8ypFA8JSpjU5SOBNYQozizGU4nzJGJED81m/8mldKE8cxcRQFKBlPXhi0inA+QMS1Wg1T5NgiLFKv16Far1Kt1ZFS8faPfsSHP78xSskKnSOIlKRWSZBaMximOOfRUcSX3niD4bCPN4ZaJUarkOTqvEdHmmajQa1aI4oidBSBlOztPubizkWMSekPUv7F//Ov0Urzd3/vb1OvxtQbDaxzGBOM6CiKaDTqeBFyPIrccnx4yGA4ZHtri0ajgZKSwWDAYDCg2++XZZVw0ulSq9fRWmKtRakIUWZJPdp7zP7hIT/5yTtMyOKypGCVw8NDhmmKEIJf/vIvc/36dZ679ixFlnH71i2OOx0e7d/HqQKbWvr9Li9ttvhiy/DxzQPqyw1upIrbDzs814hJs5y0mhBXG3x91XJv94RLV7d5ey/jk7sHXLl0iVdf/QKSmOVWkzTPGdW/dE6OWFlZJtYRSGgtrdDr9Dk5OaGwGVIq4iiB0nRotdpUkxppkaI/udtguSX45jeepd2oM0xTPPD+B+/TajUoioyl9jIXL17k4OgxR0cdbGFoNuq8+MLzLK0u8eff+w637twppUvwcuq1Co16nU63g04qJEmC1jr07z33794OYlEIeielcVu6pN7bcT8BhwEvBNY4fv7hh4DEOsfq6hreeb77/beQCrQKScVRFKOUItIRcRxRqSQktToHh8d8cuNjdKRpt9pcvHiR5689hzGGx48fo5SiWkvQOuLKlctEUUyzWWdzY4PLl6/wVz/9Gf/4n/w+29s7HBwePcEYAM8880wAz6Rkd2+PPM+x1rKyvEyz2SKLUh7tPubo5Jg4rlPkBSurDZ5/cYnh8YCfZ0OWrmwwTJrk9+/RbBt0e5Va7lhdW+Gll14mHuxzYekR69df5/JH99nv/5hf//XfZP3CNkklxliLyQy7e3tEeYaONOsXNvjoxoesr18gjmMuXV2lfdLj5z9/H+9NUN3WIqWk1arxwrUXGAxz9KXLyzxzcZNr1y6ilaTT7dCsN3n26hWklBRFSpFZmu02m5urCCE5Pj5idXmF7kmHWrXC8ckxOM/GhaCP2806caSxheH4pElhHXleECtBEmmSapUkjkv8NZTgeRcytRGU7rAcR1tNmZ0jdITyPoSwy+wpoUIRlDMCIyzGWpzvB7uiDLQJAdVajVq1TqPRwFiLtZaHD+7xeO8htWqVpFLB+RBZ1koRx0n4pwQfV2IajRZ/+dMPaLVXkaqCH2W7jzR2Cct3Ol3W1tZ4tLsLwPLKMjs7O6yvr1MUBc889yxvrq5Sb9Rp1mvEccJgOOT45JhLFy+RVKsor1j60ff54z/+l4ham7ihWN9Y5dJWgxcurZPEVyFP0b7KWz8a8MO/eIvV1XW+8MUvcvXKMySVKi+88DybW1t0Ox1u3rzD8eNdrl17jq99/RvgwVjHt7/9TdJ0wHCYMRjk3Lz5MWk64KjTpd1uo9PuR9y+84D+4CbbW1vU6zUa1Tq1SlKm4Ru8sUFHl7mO3lkirVi9sESaFyy3W/zqV7+MEJ7hcIizhkF/SDrMyfM81GYIiYoiqtVqmWkVKtM8EiFDFnpUunSUoXClojLuU0LaZRYZJCjnT0P03oeiZ++Q0pW2R2kzjfIvXEBya9UahTUoBFJEOAfWSbI8IL9KKQrnwRuKwpIJQa8/5P0PP2FnZ5uVtU2OT/osLbW4d+9UeHjvuX37NqsrK9y9e4eiKKhUKrz66qs0m03iOObChQv8w3/4D4kizbvvvsOw36XRbtGsN3jv/Q+5/vJLJHGCd3Dx8kW+8fVvoaUgNwUnJ8d88uF79IeC119/nQs76/zqVcfy6kUOup5Lz12n3VxFaxnCER46x0cgJJeW1rCiykvPXaPbOcH7wNRFYTDGUKlUePsH73Hv9j2+8PpVNjc3cFagv/mtb5EXQ/Isp9M9Zn9/j3t3b7PUbrK6vMLahZWx91CaeFgTQKObt27w0UcfcXBwjDMhmJWmOTqKAYnSEYnURM6htQ55ol4Eo3fkQolRMY8EWeaqlt95L8ahcwnoiLFLrVQZUi+DgcYabGECUlsyUih7CEBepCOq9ToqisiyDCUlSZLgcRRFQZFbqtUK+FDHap0lH2TBvur32Ll8iUqcYPKUleUGcJVbtz6j1+2NlUuapjx48KD0CARffvMNvviFL9BoNCiKHCXhwb07DPp9Yim5cu1FMlvQajX41a9/HW8L9h/e4crzr7GqNVcvbmK85+bN23z4wUeIqMnVa18A3WbvcZcrly/z2hu/wcGdt1jajnCqTZHnwVUVAm9DUjH7x1T7Q/LeECsVnrAJ87zA2oJqVfHKKy9zfP9jTOcucfwSeIW+euUKpsh4+PABzUaD7e0dsmxIr9+l0+3z4NH7IMDYsDvjSJKlAw4ODuj3+pjCkhcFxgSiIFW58wWRLCvJyxiMVgKhFM6NqtnV2C0e1SadFt2cFhGNKtxFqSPcmNkUzjmstWOxXhQFxoQd4b0f36e1plJJ0FqRJDFJkoRSyyxD67DzvQv3O2fp9zp453n8eI8Lq6vkgwG1apXCGtZaDR4+esjXvvZ1vve97zIYDJ8otZRS8sUvfpG/9/f+c3Z2LtLtdUmkoFZNqMQx7UYdIQTGGvqdEyIlMSZIxffe+RQR1Wi12wgnsd4hHGRDxQ/+4iP+7E/fYXtzmf/hf/yv6Xa6DDKLkhX6+5+QrG6Vheil51k6B4N0yOqv/BKi3WJUfAWCJInBh3zd5dUqr77+Bj/58dtcf9OjIone2FgH73n+hRexNnCcdQVplmNMwccff8gPfvAjhId6NWGvd8yg38NajyksxpRIZUnUSEdoHQfjknBWhyzVhZaqPHMjqJRRHEKOcA0hkCPoe1SDXqbfjWpRfAnWaB1yHYqiGBPEGBMQ0DwnTVOKogjVa1E0ZrQoilAqRGGlDJ6ILHNGa7Uaw8GQfr8bmEpKKskFojJYZ/OC9z/8kDRLeeedd7h27Tn+wT/4+/zgh29z9+5djLFcuLDG17/+df7mr/1NdnZ2ALh3/y4/+clPaDbqPHP1CltbmywtLRNFEUKGUgwZKZxw/MqvfwOFQ6Bx2qKcYmmpwW/99je4d+eA/t57LOu79DrHrCyvoZCY1nWSegSE+iNZVvUHSMLTfvlZrDBYAlYlvcdJcbohS0a6+MI1ht6DkMHdv/7KK6GUoAxAjepJC2PJsiGffPJz0v6AWkWz92gfZ8JZD84bPBap5biqSghBHCcBAynT6DyuTFqR4RCXMkm3KCxxHIdUuxLjmDz7YvLQldHvJcOXEHlgDmPMWOePJEcURUgp6Xa7OOeI45hqtTrusygKut0u9Xo9qBBrabfbpGnK7Tu3SYfDMr1AlcCa4P6jRyRxhWazyeO9PbrdDrdv3WZzY53f/Z3f4fKly7SX2rRabWq1GlJKOp0T7ty5y+3bt/mX/+r/ZdAboJQkSSKWltr8h3/n7/DLX34zqFTg7me3UEqwc+UKeBXqcQSApV5L+N3f/RXu/vDnxPKYk8OHLK+u45XDW4ESlbGRHv4rE5eR5T7TSO/KNEgxQhvGayeEI9Hw8ssv4oRHeInO0pRapVJWsTl2d/e4fesex8c9nMvpnpwgpeHwsIcUCuMMQoaEGEE1uJtSjKWAKOtQREn0UUmDFCrkHKvAKNWqKxlDPSGOp5ljVMCd53mZgX0qBbw/BaYmmUgIQZ7nSBl8/aWlJeI4xhjD0dERzWaTk5MTnHNEUUSz2aQoCnb3dnHehdII5zCFx9ki4EbOMRjkHB0dY2xBox4yse7evcf9e/ep1Wqsra1y9epVvvorX+PBg/vs7+8TxTGPH+9SiSJkrUqe5RR5wdHhEVevXGV7awuQdLs9/u9/8S/4u//R75XAVIAfR0dH4eHa8xcx+1/m9u0PMA8OeOZFgXACkxfQDAVi/ZMT8nzA0upaSaOylCSkRwfAc4SUT2y+IOJdQFhLWEw/vP+A9Qsr5c1Qqya0WnX+/E//jN3d+5x09wM8KyTeW5QKWUlC6pDGr/RYSigV8PqiKIjjgBeMj0IoczhHxqcsq/Onv59mjNHvtVqN6Xb6XJnqX17TWuPw1Ot1du/fJz06Zr/XBykpvOPixYusrq2x1G5TqVTIsgznHNvbW5iioCgK0mFKnudlYrHBWIMpTGnTFGNbJ01TjPUcnXR58GiX99//kD/7s++wtLJEtVrh0qXLHBwc8tyzV8aqz5Q20r17d0iHPWq1OoNBRhRHFHlB5/g4qD8docsSiVFgbfX5r/JvfnyDi3EfvKNWb1Ct1/A+xKL+4gdvs3NxmZWVjQBoipCAWm4/vHdkWUG3d8Dq6noIMzA6i21UrMoo/qTLJNvycQ1x5MiLA7rdfXASrUeEjINdUKbbB6KMKtYitFZjUR/HScArptTGJDNMS4t5amX083MHzgkx0R9j8RxFEVpLLqyu8Pxzz1KJKzhrWVpeRicxURwzGAzY2tpka2uLbrfL5sYm+/t7JEmM956izJF1xpYGbhFcP2tDGYMpyItQ8ZcOQ0FXluUMBgM63S55Fko3isIQ6QjVaGGKnJASGKr/vvu975a5MsEz897yT//Pf0ocRSgdisjrtTqVSiUg2ElCYYLqHA77vPfee1SSagivq1CU9MH77/Pctd8gyzOEGoU7giQIK6f58ds/hqjHr6xtBIYQrrQ7whqOIYJ/92//jV9fWy3xJs/DB7f5J//kH3Pv7iOcY3x2RHluDUpHpVGnS8mgSv0cjYk/yQDTEmHy+8lr08QeMwBhxyipcNYhlaRSrZQgVRQq2aOIOIrRkSKOyloRJUpVMKDT6WGMod1uo6Si2WxyeHTIxuYGWZZx9coVms0GSVwJRuLEoW/OO6xzZXa6IS/ycdCuKAzD4YBut0elkvDg7m363Q5SSfr9PlpHwa6JY1QU02g0iJMYrWKcsxR5TpFlDIYpwzQNGWzdLsPhkF6/z3DQJ8sysjQly3MKU2DygqzIyzUrjXUV1DN49nYPuHx5k3qtSaVSQWtNHCckSUKlEhPHVT547yO2L69y7dp14rhCHEdUa9XSs9NjGuj9gyPWL6zhhefk5Ih/+vv/jPv3dxFCESfB61BlWb4uYelRUk8UxeOA00iCTDLFJHNMq5YgVQJeERgtVHUlSTinK44jIh2qvHS5i9I0xVpLo9k4NUqlHCfsTEofKRX37t3lo5//nCIvuHT5Cjs7OxhrqdXrRNUKkVY0my0azRZxEtRgeIdQ/ui9Q9gA5xtj6PZ6WGt48OAhvV4vwO3VKq12m3Z7ifd+9i4Hew/IsywQsrABzbV2PD+lNUlcoVqv02wt0Ww2abXbrK0uU6vXqFVrNBvtUMwUuHNcwpAXOdYG1ZZmGaYwDIcpaTYkTVMGgz793pA8G5LnWQh2Dgb0+ocUhaUwOdYUpMOMw84eH3zw8XhTqolTiJRSVCoVxHe/8+e+3W7hfM4f/PPf54c/eBtnwwk1SkdEcVTGKqIxkUQJcY8IqpQijiKiOJyrGSfx2HiMokDk0eBKqTABrVEy1I2MJYXzCBnUkrMhs92YAiEE7aXlsEN8yCUZGb+UrvJYhSk11rCdTqe8Jrh//yEb65sMBv2wm5KEZrPJ+vr6WGyrMjbjvCPLMrrdHifHJxwdHZFlKUorXnzhRayzNOp1kkoVpUKSkXOOvUcPWV1ZHh9L4azDFAV5eZpPlmUMhynD4TDk2A4GFEWosSlsKK/Y3trhS2+8GYTCxMFzo3/jYq0yvO4943yYoA6C3SDE6VGglFV93oO1ZuzZWWuxNmBUxpggyYqCovwsfvbuX3nvPY92b/OP/tH/Qb9fUKvWqdUaNJpNKtUK1aRKkiQklaR0P1Wp1zWR1kE/ju2LUl2ULqsow8Kj80WfVBnBGvd+VNYjeOv732Hv4R5FXoBSKCXZ2t7ha7/6TZQOx1hS5n5+TmKVsDteUBRZySgSIX0ZpTzFRJIkodFolHZSMBS73S79fp9+v0+apiRJQqvVYnl5maWlpXJjSOK4PDvVBS/K2VA2XmTpE5lUo5oZ70eIb1DsgVDl4RQjxLgk2OkJhH7inpDkPYrjODcRhJ8MyU88O/mTqe9H0mIE3E3fN2qakkD7+4e8/vqX0CohTiokSaWUBDqANBOqICzwaT6HKN1VIScBrTDA4eHR+GiotdXVJz2RkQEk/FgSLK+ssr6+RaPRolqrU6tXybKCWr2BEL6cixjlLoV0QCXHEL+SCilCdDXLsrH3EiD9U8N2OBxycPCYLMvJsgCYJUmFVqvNxsYG7XabJEmecKeLPCeKNKZwHB4d8ujhI/b2HlOtxrzy8kvB6/SjbCvGhr8b4Q4lMwRC+1NmwZbgni8BQv8EY00Sz/tTgMKXqWwCniD0tCHPxPez2rzrGlTAC5IGly5fQ5fuqBwd/6MkcnQ638jrEKP4mC7drLBrg64fSQ44OjxiZWWVKEro9/sUxtJqtUZTCi/lPMhwcJSUkte+8CXqjUZIOkbiMaTDlGq1ehoz8CFTvSgMRZ6xtNwuK9mDraHEk6mHwWYK9kmn0yGOY3Z3d9E6GKcXL16k1WpRqVRCwfMkIcqfwWbw9Lpdvv/9H3Pjxk2cK2i2G2xsrGKdLzO0SmYYEdhTRntLsK6k6iTRfckik2NOfl5E1Ol75kmBWVLkrGf06MSXjc1tlNThtJ1S3wXddsrhoxYKpkGMjmEQn3c9hXAc+nDG1/e//12uX79OpRKTJBVg8rjoU5XiXUiYiaK4RCbLg2dNsF3SbMC7P30XpRXpIKfZatFuN1hdWxkzhzEmHFndbBBFgjQdlh5VUH+VSoWlpSWWl5fH1vykuz1a8EnGKIo8VMcVwS7Y2tzk/Z/dxIsgTfI8xxhbYkGMpcCpajglPn7i8+i6Z2ItZhNz1KYZZZFamPXcJDPNYr4nEoyDC6xYWVqh0WiiVAiGGWNLg8WWBT22LEfw46CWlE96J6OBRGlHFEXB+voGv/Zr3+Tg4GBMkImZjCckRIi5xC4hiaNwJroQOK+o1SRRpIEar776GvV6FVwwPvM8pdPpBDDKlNiEg+vL10FAJamidURRnvu5ubnJKJ4j1el5pdOLZAtDlmUUefCQbGkdOufY2l5neXkpVIb5YNiZwqDjcOgdboq4o88TTOdKe2IcL5pBzFlEnyVFJgk/j9Cz2qwxJlWTllKVcw+6uV6vT1iyI8ZwT3zO86zs4Ek8Y5pB2u0g7iuVCqurq2xuXoBxxmJQKWO9WQI11kbESUKWpiSVKsKN3F1Ft9tFCLhz+85Y5WVZDoBUgkqlympriWazhY41/f6Ahw/3eLx3QKfT5Rvf+FqY06ggS57OexSXybOMPMvHVfBMGoXel+pKcenKNg8fPcS7CO8seZEj1OlBNmP1MUmoCakxS31ME3/e7l5E7LOIP9lGRv30XEb96Eli9fv9sVsXEkZOXZ7Jz1qr0iUSE2rl8zvw2rUXODk5Rikd/sREedLwKN9i5JJOQu+jA0qklNy8+SmXL1+mUqkCnrt3b1OtVQFBnmesra2xublFUqmRJAnOefZ2H/Ojt39CrzfAe0dR5EipePmV62xtbyFL91mWBmKeF+FQvCx4HZMqJajVUo2O1UX4vLm1SlHYYGt4izEZysaIsVv5JHHGiz5Gp59UX4sIfh7GWPT8NOGnVctcg3TyC2MKjo+DETmKmI79a+fK6nSD1pKiTKw5bf4JZHHUms1W8J3LzKjRcMPhsIyMNqjX62OjbxTrqFQqPPvss2ilSaIE6xzb2zusrq6gdaU8lknQ6/c4ODjm3Xfeo1ar8fjxPnhJtVqlMJb1jXU2Ntb5pde/QBQF4zvPc9JBSpal46juaPE+9290HT9BTEujWaVarYUSTm9x+RAqjXIlTvubJtJ5pMEkQzyN5Jjuf7JNezGzbI/p3/WpMRm+LIqC/f39cTpfFEXESYhoWlMwOhpSh/LO8s9OhIPI+v2Ug4NDuiddhmmfjz76MORYVKt8+5vf4vLly+MJZqXoBh/sHBcKqeMoIdIxWiuMMQgRbAtjLZWkwZ3bewwGfW7evMXS0hLNZoPj42MiHWOto5JUkFKiY8Err77C+saFMu3NcnhwEFBWU6bg86ThOfp5KvpH10c4xWjbh3TCjc01Dg8foXyBt/k4uFX6tOHWkd85dj/9mPjzcIfJa+dhhnmfPye1pp6b5+mM1copY5z+dM7R7/e5efMmn332GVvb22Rpyu7uQ6Io+P46UjTqLbyHizuX+fTmp/R6GVJKqhXNzc9u0D05pt5sUG/U2d7eHi9KnufcunWLnZ3tcUGQUAKpTlP3RrEDHcWkac7/9c//Nd3uEHCsb6wiZUSkw1yiKOR2VCoJGxtrXLlymdW1ZYQItbF7e3tYY5/AD07tAT+iPZO/jj74SdqO1zegkJcub/Lo0X2Es2S9Ho21kdqYrwoWEf8XVSGz+pn18yz7Y9qw1U+qgicHuXr1MsO0x8nxAYWxPHy4S1ZYlFQkSaguy3MY9C39wQApI4RQGJvT6XRQUlOvNfjaV78WjlEOipharcYLL7xAu90eTygkulbDMUllXCPEdeDDD37O6uoF/tZvf4G3/v0PQ8FRmqGjUFD18isvcfnypTKzK8DRx8fHY1tp9MKzDK/xtSlbYHo5xvViI0IIx+bmWki0cQU1Hc43CVkTZaXZOeg8S/VM7+B5UuBpDNbzSI/pz3qRUSKl5KUXr/Phz9/n4OCQ7Z2L3Lp1D+c86XBAUqlQq1U4ONjj4uUNdh89Jo6bHBzs02q12dzY4Nu/9m3aS21Gf5xLl7t8tURLy6mXYf74CemSJAn7+0fcvHmX3/zNb/HOu++yvbPFwcEBaxeWqTeqPP/8NeI4otfrkmWn1f+zoOHzAEaL8IUnCImg2apTr1dADPHFsKwj1mMpMx0umDXOLINxetxFNsgsN/a8LvA8uo8lx8yZj1uI0L36yhcRIuRb5nlewuQyoKdSlVFUxd7jfeIoopJUSSpJmSMpAnLpQ0Ty8zkbMBj0qZSlECPDV2tNluW8/cMf8+1vf51mq8GX3vgi1liGwx3q9TpFkdPrdcdexSwU8IkFGidMnt/Ae+L58bwBQp7LxtYavd5dTgaGhhfh2Alh8KME6RESLGb3dRYh56mMeRJj0XvNwjQmv5t+Vi+a5BOoqLNEUcTKynIJZZ8OMPq3tLQSjrceYz6nonzSXT19LkROA6QdlfeEgJ4xlr/8y5/wla+8ydb2RkgT1OEcLiGg1+vOXQQ4ddAn3ccJ3gjzm7OO4z/LNu/78U6WXLlymZ++fY/qwLHtLFJHcHo+8eiBz0mR88Lek2MuapN/oHCWxHwaCTZqetJtO2uSeZ5zeHhErZaNC3UmYeeyy5lYwey/ruipVKpEUTy+JySbCLIs40tfeh3vHA8fPqQoirkS4sk+y89PfDEyKKd22bw+PEzilk8u3CkuJIRnfXONgdWcDD2FDUdrT/c+rQIWzf08UPioLbIjZo01a4xFc9LTYeLzTKjX69Hv94miiEql8gRwdppsI8cTXTSZSqUyxk+KoiBNe2RZVsYrTjPLpxdkEcfPJHrJIdP3zwKEghoQn+vndHFPr9VqFVqtRvgLC6ZAyDrjIskFO/Zprs+zm+bZHfPUxTz6zlM3TzDHvDZLz3nvx+BWgLVPkc5RIpCail1MoqMjtBWCRBrNY5r4i/TkXK4fq5KJhZzDHJNzG0vBiS7m7cDJeW1trfPo0cOQIMMpZjSPEee189gLi+yEyb9jt3DzzPF+psfSk2n9szqbXojpTiYXdxR/eZo2nxFOjb95Rtu8hVikdmbtrMlnR2MyQeTJ5ynjLOFzSB+4ePki9x/cKzPJPGVq59w5jMadBYJNq9+z2jxmP4vR5rnJT/Txzjvv+Hnu0SJL+Bex9uf1NTmxWfdNS5RZi7poTpNMcZ7FmzfHefO21nJwcMD6+vrnRPw8d3nynabnOc0gs9TK5O/zxpllmM6ay7x+9fTCT3c8b4eexyg8b1tkWc8DaRaN+TRzGBFkkfRZxFAjYm5ubn7u+qwdusj+ml7vWfctYoTzvOus8ebdq6dvfFqLdrrNY7Cz7p31+6wJz7v3LKk26/tZEuVp+px3/6zNNovgs9ZqmqHmjXNe52HW5p7V36zv9eQN0y8xb7fOe9HziOHJPma9yLw2zbTTc1xEyFm/TzPIeea9iFizfp/FLPNtrNM2zTRPsy6Tnxet0Xn6WoiQPk2H8xboPBy+qJ959531ed4OnMcYTyu6fxH1OT2v86jT6e/m9bFoPvPGnKVmJq9/jjmeRjxPd3iWNFmkCuaNd9b4867NBt1mM8SsuZ819lm7eR4RFqnSWe7lPOadN9b0tWnGmEe/WfN8gjlmqZPpNmuA6cHn9fO0ev0slTRrF82zLWZdm/d58j3mzf+8dsG8PkbXptP0Rt+dtZGm12H6+i8i2ab71LPE11k6cbJ9Hj5/si3i2sl7zpI603Oc7PssNTI9xnmveT8fdziPGpy1Sc4S77OYfZ6XuEhNndXX9Niz5qcnGeG8+n5aQpynPc2OXtT/Wbv5PLbCZN+ziP+0hD9PO49UnMUM553HojbPRV4k0T4nOc6awHndrLOeX6RPF12fbrNeehYjzJrPL6LOFs1xkSo+b5unikbtaW24RW2RkTr2VhaJrHkDnpe4s+5fJDXmjTP9MtO/z1qwef1Pz2HW53lz+UXtj1+kTRPuPJL9F7FTFnk+cw3S6evzdtvTMNG8vma1WXOYJwZnfZ4c6zzvtEjKTM/hrPum23nczekxzrq+iKiT956lUkbfzZIeenrxzrPLJr9f9Mz0PbP+zbLM5y3ILLF7lnpYpFbOkmRnSZKz5jw53+n7F6mPSQ9mco1mvfciibJI2p6n6emJTL7krM+L2iLiTBJjOlVwNP4oE31en4sYY/Ke6d+n5zDyQs5ipmkmmUXo8xjA8+Z61nNPI3Em53cWA53HxRZCfF5yTE/2PKJ2VlukPhaBMYsWZN61eaJz0Xjz5nqWGprV7zwJMG+ss+Y7ee+8togBziMtpucwa931IkKfxTiL2izmWMQwsyY/+cw0l896oXlEWiRhnmZTTI8xa+3OI91mPXPWd0+jHuZtvnkqbx5j6+lO5nH0ecXjrN03maE0i0HmGVqzvp81p1mqZPqe6TYuphLic0DeWQw877uzXP3ptT0vIy9SD/PuOc+aLXoPmMEcMBsYOu8LjD7PEtPzRPOivp/WiJrsZx5WMP39qHB71j3nHesXmdfTqpZ5G3me0T5q8zy6s2yTma7seSf2NG0WszyNgXkeKTE9t/Oqw9ECTzLJLyI5nkbizWLS8/Y5TcxFG+EsSTPrvlF/etYEzmKKRYuz6P5ZNsikh3JeMXvetohJZhmFIwYJB9icDxA8z/hn4RbzCDivz7Mk2yxmOY/HMt3HuUL285hlcnEXMcG85+Z9/zRtuq95zDlrd8zqa1F/0zbFWQu8aKyzbI+nbfPW9DwG6DyJdyZCehbRZjHGov5Gg89TD3/dBZvHlIsY/K/DmNP9Pa1LO/15up3X/nna9ToPs5xRK/v5B6Z/n7XQixZ8HncvkjSLdsQi8f80dsNZBF6k+5+mn7O+W2Q/LGqzJN55pMYipjxXkcS8nTZLjUwzyHnsk/Pu3EmGmCV9ZvW96H2m5zjd5+Q402NPzmfROPNUyqLfF22s6bHPsxaL+lykJZ6Iys56cJE4nr5nHoPMm8x5rsNsNTTPNniaoqBZ3tJ5dt+sPubN67x2yXnmOt3/Lyphpuc1T7XIyQfO286SGL/ohGe1s3Txeft52vFnSZFfpL+zVM+8e2Y9c17jd9HGnGcjzpIwcpoTz7vAixhjnsR5WuZ5Gv9/3rVF/SySlk/DaOdRNWf1d54N8tcx0ufZhovGHsvg6QnM4+7zMsK8yZ13N07r+0Uv8jTEfBrmfFpJdNYanqf/Rep61lhP02bRYJEpMLOoafLmWWDRWS933hcctWm9N8/IOmv8WTbE9DxmPXuWHTHrHefdu+jZWXObfG66PS0D/CLe0aL2uQTjWdw1q80TVbN+X7Szz8sYk/cvmstZu3YWs87Su4sY/CzmmfVO04zxtKp03kb56zDQvHmMJcdZhski22Lyvll/vG+WqILT034m+508J2SeRJonvc5jf8yTYvPeZ3K8s1TsWW1eH9PqchEuMU3IWRJr3hrMY+ZFUkwIwf8P/yAhkYuOiAwAAAAASUVORK5CYII=\" id=\"image5d72583e7f\" transform=\"scale(1 -1)translate(0 -64.8)\" x=\"10.7\" y=\"-7.095652\" width=\"97.2\" height=\"64.8\"/>\n", "   </g>\n", "   <g id=\"patch_3\">\n", "    <path d=\"M 10.7 71.895652 \n", "L 10.7 7.2 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_4\">\n", "    <path d=\"M 107.743478 71.895652 \n", "L 107.743478 7.2 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_5\">\n", "    <path d=\"M 10.7 71.895652 \n", "L 107.743478 71.895652 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_6\">\n", "    <path d=\"M 10.7 7.2 \n", "L 107.743478 7.2 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "  </g>\n", "  <g id=\"axes_2\">\n", "   <g id=\"patch_7\">\n", "    <path d=\"M 127.152174 71.895652 \n", "L 224.195652 71.895652 \n", "L 224.195652 7.2 \n", "L 127.152174 7.2 \n", "z\n", "\" style=\"fill: #ffffff\"/>\n", "   </g>\n", "   <g clip-path=\"url(#p2bc0b4b685)\">\n", "    <image xlink:href=\"data:image/png;base64,\n", "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\" id=\"image151c307eaa\" transform=\"scale(1 -1)translate(0 -64.8)\" x=\"127.152174\" y=\"-7.095652\" width=\"97.2\" height=\"64.8\"/>\n", "   </g>\n", "   <g id=\"patch_8\">\n", "    <path d=\"M 127.152174 71.895652 \n", "L 127.152174 7.2 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_9\">\n", "    <path d=\"M 224.195652 71.895652 \n", "L 224.195652 7.2 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_10\">\n", "    <path d=\"M 127.152174 71.895652 \n", "L 224.195652 71.895652 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_11\">\n", "    <path d=\"M 127.152174 7.2 \n", "L 224.195652 7.2 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "  </g>\n", "  <g id=\"axes_3\">\n", "   <g id=\"patch_12\">\n", "    <path d=\"M 243.604348 71.895652 \n", "L 340.647826 71.895652 \n", "L 340.647826 7.2 \n", "L 243.604348 7.2 \n", "z\n", "\" style=\"fill: #ffffff\"/>\n", "   </g>\n", "   <g clip-path=\"url(#p01232eab82)\">\n", "    <image xlink:href=\"data:image/png;base64,\n", "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*****************************==\" id=\"image9fe0dc3ad7\" transform=\"scale(1 -1)translate(0 -64.8)\" x=\"243.604348\" y=\"-7.095652\" width=\"97.2\" height=\"64.8\"/>\n", "   </g>\n", "   <g id=\"patch_13\">\n", "    <path d=\"M 243.604348 71.895652 \n", "L 243.604348 7.2 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_14\">\n", "    <path d=\"M 340.647826 71.895652 \n", "L 340.647826 7.2 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_15\">\n", "    <path d=\"M 243.604348 71.895652 \n", "L 340.647826 71.895652 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_16\">\n", "    <path d=\"M 243.604348 7.2 \n", "L 340.647826 7.2 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "  </g>\n", "  <g id=\"axes_4\">\n", "   <g id=\"patch_17\">\n", "    <path d=\"M 360.056522 71.895652 \n", "L 457.1 71.895652 \n", "L 457.1 7.2 \n", "L 360.056522 7.2 \n", "z\n", "\" style=\"fill: #ffffff\"/>\n", "   </g>\n", "   <g clip-path=\"url(#p637beca681)\">\n", "    <image xlink:href=\"data:image/png;base64,\n", "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\" id=\"imagecb64bafff6\" transform=\"scale(1 -1)translate(0 -64.8)\" x=\"360.056522\" y=\"-7.095652\" width=\"97.2\" height=\"64.8\"/>\n", "   </g>\n", "   <g id=\"patch_18\">\n", "    <path d=\"M 360.056522 71.895652 \n", "L 360.056522 7.2 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_19\">\n", "    <path d=\"M 457.1 71.895652 \n", "L 457.1 7.2 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_20\">\n", "    <path d=\"M 360.056522 71.895652 \n", "L 457.1 71.895652 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_21\">\n", "    <path d=\"M 360.056522 7.2 \n", "L 457.1 7.2 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "  </g>\n", "  <g id=\"axes_5\">\n", "   <g id=\"patch_22\">\n", "    <path d=\"M 10.7 189.298005 \n", "L 107.743478 189.298005 \n", "L 107.743478 124.602353 \n", "L 10.7 124.602353 \n", "z\n", "\" style=\"fill: #ffffff\"/>\n", "   </g>\n", "   <g clip-path=\"url(#pefbc4b864a)\">\n", "    <image xlink:href=\"data:image/png;base64,\n", "iVBORw0KGgoAAAANSUhEUgAAAIcAAABaCAYAAACSR0X7AAAEhklEQVR4nO3cW2tcVRyG8WdyMtFWW+sZKoioF4qKrSh44YWgl34lP4xfQK8U71U8gEXxhKKd2jZqnMY2kyaTw/bi3ZupOv85JLNnrT15fzC0pC1dIQ9rr31YuwUUmA2wkHoAli/HYSHHYSHHYSHHYSHHYSHHYSHHYSHHYSHHYSHHYSHHYSHHYSHHYSHHYSHHYaGllP/xCnrS6HaqQdhQtcexACwD95Wfh4DzwCngDHAA/AnsAleAv4EOCqZb/rml0aKGxwTvAZ5EEZwtP/eiUBZH/NvD8rMD3AKuAZvAHyia7fLjZxvrN7U4WsADwEXgKRRE9fXjqgbYA/aADeB3NONU8dzGwUzbseOoongZeA5YYzpBjKNAs8wWsA58B/wC3MShTMOR40gZxSDVN7GNDkFt4HL5+20UkU1m4jgWgceBl9C6InUUkaL8dNHh5y/gNxTJdbTQ3UVrG3A8g0wUxzngDbSmWK5rRDUr0LqlOoXuojCuolh+ReuY3UTjy8lYcSwBzwOvo7OOHGeKaajWMDeAn4Af0KzT5WSuYUbGcQZ4E3iG0aeh8+YAhdEuP1dROJFltA7bQ1E1/eJeGEcLrSneQt/wvM4W4yqAfYYfbhaBVfpRraNFcbUw3qt5jNM2MI4F4LXyszrrEc2ZKqpNFMklFE0TQvlfHGtobXGRhDde5lQVygbwDQplK+mIhmu9/Z84zqHL3if9MFK3Aq1fviTfSFrvnMyFeDYKdA3mXXToyYmf58jAFlrA5sZxJHYIfEqeC1THkdgVdMEtR44joUPgM3QGkyPHkVAH3cvJleNIpED3brZTD2QIx5FID/gq9SBGcByJrKPDSs4cRwI9tBDN/cl6xzFjPeAD4NvUAxmD763NSIEuj3+EHoRuwj0LxzEDBXqe4z30CGJTOI6aHaIroO+T553XYRxHjXaAT4CPyfPeySiOowYFOlX9ED391YT1xSCOY4qq3XeXgM/RzrsmcxzHVNB/U8AXwI9oA/g8cBxHVKA1RRtd0GrTzHXFMEsFfl50XNUscQP4GvgezRhNXVOM0roAxdodX1gFnkWbmeYhmqP84Hpo/bCLQthHD+X0yl83OBnbJQfuWzkNvABcQG/jaco19urFLzfRD/Q6/TcGTTLlVxuQ9ulvtJ7X2WGYodshTwFPAK8Aj5V/OYfZpBrwDrqz2aG/VXETvTpqn3yfsGqKsTZSrwBPA68Cj6Btf7OOpECzQAfNBJfRzHALR1CXiV7BsIJmkBfLX+9GO+Sqw840g6l2vHfRezV+RmcEHfK/1T0vjvxmnxV0HvwgOvw8ijZc34/WKctMtla5c0/pNbToa6Pj/85RB2nHMtW3CbZQMKeBh4G70CHoPP0LKmfR2wYr2+h0sI1miE10VuAY0qvlVZPDLPHv93wc4DVDrmYehzVHUy5hWAKOw0KOw0KOw0KOw0KOw0KOw0KOw0KOw0KOw0KOw0KOw0KOw0KOw0KOw0KOw0KOw0KOw0KOw0KOw0KOw0KOw0KOw0KOw0KOw0KOw0KOw0KOw0KOw0KOw0L/AF5hFN5r8Iq6AAAAAElFTkSuQmCC\" id=\"image3d15603574\" transform=\"scale(1 -1)translate(0 -64.8)\" x=\"10.7\" y=\"-124.498005\" width=\"97.2\" height=\"64.8\"/>\n", "   </g>\n", "   <g id=\"patch_23\">\n", "    <path d=\"M 10.7 189.298005 \n", "L 10.7 124.602353 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_24\">\n", "    <path d=\"M 107.743478 189.298005 \n", "L 107.743478 124.602353 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_25\">\n", "    <path d=\"M 10.7 189.298005 \n", "L 107.743478 189.298005 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_26\">\n", "    <path d=\"M 10.7 124.602353 \n", "L 107.743478 124.602353 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "  </g>\n", "  <g id=\"axes_6\">\n", "   <g id=\"patch_27\">\n", "    <path d=\"M 127.152174 189.298005 \n", "L 224.195652 189.298005 \n", "L 224.195652 124.602353 \n", "L 127.152174 124.602353 \n", "z\n", "\" style=\"fill: #ffffff\"/>\n", "   </g>\n", "   <g clip-path=\"url(#pb71b10ab6f)\">\n", "    <image xlink:href=\"data:image/png;base64,\n", "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\" id=\"imageac0c80dd7b\" transform=\"scale(1 -1)translate(0 -64.8)\" x=\"127.152174\" y=\"-124.498005\" width=\"97.2\" height=\"64.8\"/>\n", "   </g>\n", "   <g id=\"patch_28\">\n", "    <path d=\"M 127.152174 189.298005 \n", "L 127.152174 124.602353 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_29\">\n", "    <path d=\"M 224.195652 189.298005 \n", "L 224.195652 124.602353 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_30\">\n", "    <path d=\"M 127.152174 189.298005 \n", "L 224.195652 189.298005 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_31\">\n", "    <path d=\"M 127.152174 124.602353 \n", "L 224.195652 124.602353 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "  </g>\n", "  <g id=\"axes_7\">\n", "   <g id=\"patch_32\">\n", "    <path d=\"M 243.604348 189.298005 \n", "L 340.647826 189.298005 \n", "L 340.647826 124.602353 \n", "L 243.604348 124.602353 \n", "z\n", "\" style=\"fill: #ffffff\"/>\n", "   </g>\n", "   <g clip-path=\"url(#p5f64eebe9e)\">\n", "    <image xlink:href=\"data:image/png;base64,\n", "iVBORw0KGgoAAAANSUhEUgAAAIcAAABaCAYAAACSR0X7AAADgUlEQVR4nO3azWsUZxzA8e/sbLK6ScyLL7WNqYIUkVwUVFAQ8diLB0vpX+L/0osnj/ZYeqjmqqGFiop4kYii4EtURJOGyu708GSatOZH07KzL+73AyGBhJlZnu8+eeaZzYACaQu1Xl+A+pdxKGQcChmHQsahkHEoZBwKGUcfq+d5b8/f07Nrk9r61ygwQXO0zqULZ9kxWuP+kyc8Wl7m7uPHvF1dpdVud+WKMtwh7aI6sANoAtPACDBHimIGmCDFMU5eg5OHc749nXF0FhojbV6/f8/S8+f8dOsWvy0t8W5trdKrNY5KlDPANLCfNOhzwBgwBeTrvy9l4ZFGcjiwG84ehfPzcGCmIK8VPFpe5ofFRX6+fZsPrVYlr8I4tiUjDeYY8BkbS7VR0qD/c3AnSWFMkEKIB/+/aDbgyOfw9XE4dggmmy2uLt7g8sJCJYNoHKEM2AscAQ6S3vHltL/V33ZPBkyNwYnD8N2Zd1y68j1vVlY6fh4XpB8pozgFzJPWCN0d/H9TAG9W4NodePhihNU/qhlG4/ibL4BzwJf0YxRbefCsumMbx1/mgG9I64X+j2JDTloLve34kd0EAwY3DEjv7+lKjmwcAx1GqZrrHvI4PoUwIL2OzhviOD6VMMo9mM4b0jhmgIsMfhilau4rhjSOeWC81xfRIQXQoIqhHNId0py063kaOER65w3iDFIAr4GrQOc3PIY0jlIZycn17zsZnEgK4CnwI1WEAUMfRykD9gBfAbtJT1IbbDw4Kz9r0Q8KYA34FbgJ/F7ZmYxjS2UQk+s/7yGFMk56KtskPYirs3GnUPWMUwAt4CFwHXhB1UNnHP9LnTSzNEkBTZECmiTdCe0iRdOpj/m1STEsAEukSKpnHB2VkWacXaRQ5oBZYB9p5qlv+rvtaAMvgV+Ae6R/J91jHF3RIK1lZkhPfmf5eG+iRVpgbp4VXgH36XYUJePoiWhx250PDm+XcSjUL/dn6kPGoZBxKGQcChmHQsahkHEoZBwKGYdCxqGQcShkHAoZh0LGoZBxKGQcChmHQsahkHEoZBwKGYdCxqGQcShkHAoZh0LGoZBxKGQcChmHQsahkHEoZBwKGYdCxqGQcShkHAoZh0LGoZBxKGQcChmHQsahkHEoZBwKGYdCxqGQcShkHAoZh0LGoZBxKGQcChmHQsahkHEoZBwKGYdCxqHQn/6ffxEXdHLqAAAAAElFTkSuQmCC\" id=\"image8fa0013da3\" transform=\"scale(1 -1)translate(0 -64.8)\" x=\"243.604348\" y=\"-124.498005\" width=\"97.2\" height=\"64.8\"/>\n", "   </g>\n", "   <g id=\"patch_33\">\n", "    <path d=\"M 243.604348 189.298005 \n", "L 243.604348 124.602353 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_34\">\n", "    <path d=\"M 340.647826 189.298005 \n", "L 340.647826 124.602353 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_35\">\n", "    <path d=\"M 243.604348 189.298005 \n", "L 340.647826 189.298005 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_36\">\n", "    <path d=\"M 243.604348 124.602353 \n", "L 340.647826 124.602353 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "  </g>\n", "  <g id=\"axes_8\">\n", "   <g id=\"patch_37\">\n", "    <path d=\"M 360.056522 189.298005 \n", "L 457.1 189.298005 \n", "L 457.1 124.602353 \n", "L 360.056522 124.602353 \n", "z\n", "\" style=\"fill: #ffffff\"/>\n", "   </g>\n", "   <g clip-path=\"url(#p19f9c053cb)\">\n", "    <image xlink:href=\"data:image/png;base64,\n", "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\" id=\"imageef1158c79e\" transform=\"scale(1 -1)translate(0 -64.8)\" x=\"360.056522\" y=\"-124.498005\" width=\"97.2\" height=\"64.8\"/>\n", "   </g>\n", "   <g id=\"patch_38\">\n", "    <path d=\"M 360.056522 189.298005 \n", "L 360.056522 124.602353 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_39\">\n", "    <path d=\"M 457.1 189.298005 \n", "L 457.1 124.602353 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_40\">\n", "    <path d=\"M 360.056522 189.298005 \n", "L 457.1 189.298005 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_41\">\n", "    <path d=\"M 360.056522 124.602353 \n", "L 457.1 124.602353 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "  </g>\n", "  <g id=\"axes_9\">\n", "   <g id=\"patch_42\">\n", "    <path d=\"M 10.7 306.700358 \n", "L 107.743478 306.700358 \n", "L 107.743478 242.004706 \n", "L 10.7 242.004706 \n", "z\n", "\" style=\"fill: #ffffff\"/>\n", "   </g>\n", "   <g clip-path=\"url(#p4c9b5b2f9b)\">\n", "    <image xlink:href=\"data:image/png;base64,\n", "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\" id=\"imagec52c1a0f25\" transform=\"scale(1 -1)translate(0 -64.8)\" x=\"10.7\" y=\"-241.900358\" width=\"97.2\" height=\"64.8\"/>\n", "   </g>\n", "   <g id=\"patch_43\">\n", "    <path d=\"M 10.7 306.700358 \n", "L 10.7 242.004706 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_44\">\n", "    <path d=\"M 107.743478 306.700358 \n", "L 107.743478 242.004706 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_45\">\n", "    <path d=\"M 10.7 306.700358 \n", "L 107.743478 306.700358 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_46\">\n", "    <path d=\"M 10.7 242.004706 \n", "L 107.743478 242.004706 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "  </g>\n", "  <g id=\"axes_10\">\n", "   <g id=\"patch_47\">\n", "    <path d=\"M 127.152174 306.700358 \n", "L 224.195652 306.700358 \n", "L 224.195652 242.004706 \n", "L 127.152174 242.004706 \n", "z\n", "\" style=\"fill: #ffffff\"/>\n", "   </g>\n", "   <g clip-path=\"url(#p3cf779418a)\">\n", "    <image xlink:href=\"data:image/png;base64,\n", "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\" id=\"imagef90ee1671e\" transform=\"scale(1 -1)translate(0 -64.8)\" x=\"127.152174\" y=\"-241.900358\" width=\"97.2\" height=\"64.8\"/>\n", "   </g>\n", "   <g id=\"patch_48\">\n", "    <path d=\"M 127.152174 306.700358 \n", "L 127.152174 242.004706 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_49\">\n", "    <path d=\"M 224.195652 306.700358 \n", "L 224.195652 242.004706 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_50\">\n", "    <path d=\"M 127.152174 306.700358 \n", "L 224.195652 306.700358 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_51\">\n", "    <path d=\"M 127.152174 242.004706 \n", "L 224.195652 242.004706 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "  </g>\n", "  <g id=\"axes_11\">\n", "   <g id=\"patch_52\">\n", "    <path d=\"M 243.604348 306.700358 \n", "L 340.647826 306.700358 \n", "L 340.647826 242.004706 \n", "L 243.604348 242.004706 \n", "z\n", "\" style=\"fill: #ffffff\"/>\n", "   </g>\n", "   <g clip-path=\"url(#p8541045816)\">\n", "    <image xlink:href=\"data:image/png;base64,\n", "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\" id=\"image0c08d0048b\" transform=\"scale(1 -1)translate(0 -64.8)\" x=\"243.604348\" y=\"-241.900358\" width=\"97.2\" height=\"64.8\"/>\n", "   </g>\n", "   <g id=\"patch_53\">\n", "    <path d=\"M 243.604348 306.700358 \n", "L 243.604348 242.004706 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_54\">\n", "    <path d=\"M 340.647826 306.700358 \n", "L 340.647826 242.004706 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_55\">\n", "    <path d=\"M 243.604348 306.700358 \n", "L 340.647826 306.700358 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_56\">\n", "    <path d=\"M 243.604348 242.004706 \n", "L 340.647826 242.004706 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "  </g>\n", "  <g id=\"axes_12\">\n", "   <g id=\"patch_57\">\n", "    <path d=\"M 360.056522 306.700358 \n", "L 457.1 306.700358 \n", "L 457.1 242.004706 \n", "L 360.056522 242.004706 \n", "z\n", "\" style=\"fill: #ffffff\"/>\n", "   </g>\n", "   <g clip-path=\"url(#p116b6203aa)\">\n", "    <image xlink:href=\"data:image/png;base64,\n", "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\" id=\"imagec6692d09d7\" transform=\"scale(1 -1)translate(0 -64.8)\" x=\"360.056522\" y=\"-241.900358\" width=\"97.2\" height=\"64.8\"/>\n", "   </g>\n", "   <g id=\"patch_58\">\n", "    <path d=\"M 360.056522 306.700358 \n", "L 360.056522 242.004706 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_59\">\n", "    <path d=\"M 457.1 306.700358 \n", "L 457.1 242.004706 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_60\">\n", "    <path d=\"M 360.056522 306.700358 \n", "L 457.1 306.700358 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_61\">\n", "    <path d=\"M 360.056522 242.004706 \n", "L 457.1 242.004706 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "  </g>\n", " </g>\n", " <defs>\n", "  <clipPath id=\"p02414c4586\">\n", "   <rect x=\"10.7\" y=\"7.2\" width=\"97.043478\" height=\"64.695652\"/>\n", "  </clipPath>\n", "  <clipPath id=\"p2bc0b4b685\">\n", "   <rect x=\"127.152174\" y=\"7.2\" width=\"97.043478\" height=\"64.695652\"/>\n", "  </clipPath>\n", "  <clipPath id=\"p01232eab82\">\n", "   <rect x=\"243.604348\" y=\"7.2\" width=\"97.043478\" height=\"64.695652\"/>\n", "  </clipPath>\n", "  <clipPath id=\"p637beca681\">\n", "   <rect x=\"360.056522\" y=\"7.2\" width=\"97.043478\" height=\"64.695652\"/>\n", "  </clipPath>\n", "  <clipPath id=\"pefbc4b864a\">\n", "   <rect x=\"10.7\" y=\"124.602353\" width=\"97.043478\" height=\"64.695652\"/>\n", "  </clipPath>\n", "  <clipPath id=\"pb71b10ab6f\">\n", "   <rect x=\"127.152174\" y=\"124.602353\" width=\"97.043478\" height=\"64.695652\"/>\n", "  </clipPath>\n", "  <clipPath id=\"p5f64eebe9e\">\n", "   <rect x=\"243.604348\" y=\"124.602353\" width=\"97.043478\" height=\"64.695652\"/>\n", "  </clipPath>\n", "  <clipPath id=\"p19f9c053cb\">\n", "   <rect x=\"360.056522\" y=\"124.602353\" width=\"97.043478\" height=\"64.695652\"/>\n", "  </clipPath>\n", "  <clipPath id=\"p4c9b5b2f9b\">\n", "   <rect x=\"10.7\" y=\"242.004706\" width=\"97.043478\" height=\"64.695652\"/>\n", "  </clipPath>\n", "  <clipPath id=\"p3cf779418a\">\n", "   <rect x=\"127.152174\" y=\"242.004706\" width=\"97.043478\" height=\"64.695652\"/>\n", "  </clipPath>\n", "  <clipPath id=\"p8541045816\">\n", "   <rect x=\"243.604348\" y=\"242.004706\" width=\"97.043478\" height=\"64.695652\"/>\n", "  </clipPath>\n", "  <clipPath id=\"p116b6203aa\">\n", "   <rect x=\"360.056522\" y=\"242.004706\" width=\"97.043478\" height=\"64.695652\"/>\n", "  </clipPath>\n", " </defs>\n", "</svg>\n"], "text/plain": ["<Figure size 800x600 with 12 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["voc_dir = d2l.download_extract('voc2012', 'VOCdevkit/VOC2012')\n", "test_images, test_labels = d2l.read_voc_images(voc_dir, False)\n", "n, imgs = 4, []\n", "for i in range(n):\n", "    crop_rect = (0, 0, 320, 480)\n", "    X = torchvision.transforms.functional.crop(test_images[i], *crop_rect)\n", "    pred = label2image(predict(X))\n", "    imgs += [X.permute(1,2,0), pred.cpu(),\n", "             torchvision.transforms.functional.crop(\n", "                 test_labels[i], *crop_rect).permute(1,2,0)]\n", "d2l.show_images(imgs[::3] + imgs[1::3] + imgs[2::3], 3, n, scale=2);"]}, {"cell_type": "markdown", "id": "4bfa6534", "metadata": {"origin_pos": 58}, "source": ["## 小结\n", "\n", "* 全卷积网络先使用卷积神经网络抽取图像特征，然后通过$1\\times 1$卷积层将通道数变换为类别个数，最后通过转置卷积层将特征图的高和宽变换为输入图像的尺寸。\n", "* 在全卷积网络中，我们可以将转置卷积层初始化为双线性插值的上采样。\n", "\n", "## 练习\n", "\n", "1. 如果将转置卷积层改用Xavier随机初始化，结果有什么变化？\n", "1. 调节超参数，能进一步提升模型的精度吗？\n", "1. 预测测试图像中所有像素的类别。\n", "1. 最初的全卷积网络的论文中 :cite:`<PERSON>.Shelhamer.Darrell.2015`还使用了某些卷积神经网络中间层的输出。试着实现这个想法。\n"]}, {"cell_type": "markdown", "id": "97c09135", "metadata": {"origin_pos": 60, "tab": ["pytorch"]}, "source": ["[Discussions](https://discuss.d2l.ai/t/3297)\n"]}], "metadata": {"language_info": {"name": "python"}}, "nbformat": 4, "nbformat_minor": 5}