{"cells": [{"cell_type": "markdown", "id": "16f6d4db", "metadata": {"origin_pos": 0}, "source": ["# 现代卷积神经网络\n", ":label:`chap_modern_cnn`\n", "\n", "上一章我们介绍了卷积神经网络的基本原理，本章将介绍现代的卷积神经网络架构，许多现代卷积神经网络的研究都是建立在这一章的基础上的。\n", "在本章中的每一个模型都曾一度占据主导地位，其中许多模型都是ImageNet竞赛的优胜者。ImageNet竞赛自2010年以来，一直是计算机视觉中监督学习进展的指向标。\n", "\n", "这些模型包括：\n", "\n", "- AlexNet。它是第一个在大规模视觉竞赛中击败传统计算机视觉模型的大型神经网络；\n", "- 使用重复块的网络（VGG）。它利用许多重复的神经网络块；\n", "- 网络中的网络（NiN）。它重复使用由卷积层和$1\\times 1$卷积层（用来代替全连接层）来构建深层网络;\n", "- 含并行连结的网络（GoogLeNet）。它使用并行连结的网络，通过不同窗口大小的卷积层和最大汇聚层来并行抽取信息；\n", "- 残差网络（ResNet）。它通过残差块构建跨层的数据通道，是计算机视觉中最流行的体系架构；\n", "- 稠密连接网络（DenseNet）。它的计算成本很高，但给我们带来了更好的效果。\n", "\n", "虽然深度神经网络的概念非常简单——将神经网络堆叠在一起。但由于不同的网络架构和超参数选择，这些神经网络的性能会发生很大变化。\n", "本章介绍的神经网络是将人类直觉和相关数学见解结合后，经过大量研究试错后的结晶。\n", "我们会按时间顺序介绍这些模型，在追寻历史的脉络的同时，帮助培养对该领域发展的直觉。这将有助于研究开发自己的架构。\n", "例如，本章介绍的批量规范化（batch normalization）和残差网络（ResNet）为设计和训练深度神经网络提供了重要思想指导。\n", "\n", ":begin_tab:toc\n", " - [alexnet](alexnet.ipynb)\n", " - [vgg](vgg.ipynb)\n", " - [nin](nin.ipynb)\n", " - [googlenet](googlenet.ipynb)\n", " - [batch-norm](batch-norm.ipynb)\n", " - [resnet](resnet.ipynb)\n", " - [densenet](densenet.ipynb)\n", ":end_tab:\n"]}], "metadata": {"language_info": {"name": "python"}}, "nbformat": 4, "nbformat_minor": 5}