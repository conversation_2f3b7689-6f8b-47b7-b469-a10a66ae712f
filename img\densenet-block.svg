<?xml version="1.0" encoding="UTF-8"?>
<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="177pt" height="92pt" viewBox="0 0 177 92" version="1.1">
<defs>
<g>
<symbol overflow="visible" id="glyph0-0">
<path style="stroke:none;" d="M 1.125 0 L 1.125 -5.625 L 5.625 -5.625 L 5.625 0 Z M 1.265625 -0.140625 L 5.484375 -0.140625 L 5.484375 -5.484375 L 1.265625 -5.484375 Z M 1.265625 -0.140625 "/>
</symbol>
<symbol overflow="visible" id="glyph0-1">
<path style="stroke:none;" d="M 0.65625 0 L 0.65625 -6.4375 L 3.078125 -6.4375 C 3.566406 -6.4375 3.957031 -6.367188 4.25 -6.234375 C 4.550781 -6.109375 4.785156 -5.910156 4.953125 -5.640625 C 5.128906 -5.367188 5.21875 -5.085938 5.21875 -4.796875 C 5.21875 -4.515625 5.140625 -4.253906 4.984375 -4.015625 C 4.835938 -3.773438 4.613281 -3.578125 4.3125 -3.421875 C 4.695312 -3.304688 4.992188 -3.113281 5.203125 -2.84375 C 5.421875 -2.570312 5.53125 -2.25 5.53125 -1.875 C 5.53125 -1.570312 5.460938 -1.289062 5.328125 -1.03125 C 5.203125 -0.769531 5.046875 -0.566406 4.859375 -0.421875 C 4.671875 -0.285156 4.4375 -0.179688 4.15625 -0.109375 C 3.875 -0.0351562 3.523438 0 3.109375 0 Z M 1.515625 -3.734375 L 2.90625 -3.734375 C 3.28125 -3.734375 3.550781 -3.757812 3.71875 -3.8125 C 3.9375 -3.875 4.097656 -3.976562 4.203125 -4.125 C 4.316406 -4.28125 4.375 -4.46875 4.375 -4.6875 C 4.375 -4.90625 4.320312 -5.09375 4.21875 -5.25 C 4.113281 -5.414062 3.96875 -5.53125 3.78125 -5.59375 C 3.59375 -5.65625 3.265625 -5.6875 2.796875 -5.6875 L 1.515625 -5.6875 Z M 1.515625 -0.765625 L 3.109375 -0.765625 C 3.390625 -0.765625 3.585938 -0.773438 3.703125 -0.796875 C 3.890625 -0.828125 4.050781 -0.882812 4.1875 -0.96875 C 4.320312 -1.050781 4.429688 -1.171875 4.515625 -1.328125 C 4.597656 -1.484375 4.640625 -1.664062 4.640625 -1.875 C 4.640625 -2.113281 4.578125 -2.320312 4.453125 -2.5 C 4.328125 -2.675781 4.15625 -2.796875 3.9375 -2.859375 C 3.71875 -2.929688 3.40625 -2.96875 3 -2.96875 L 1.515625 -2.96875 Z M 1.515625 -0.765625 "/>
</symbol>
<symbol overflow="visible" id="glyph0-2">
<path style="stroke:none;" d="M -0.015625 0 L 2.46875 -6.4375 L 3.375 -6.4375 L 6.015625 0 L 5.046875 0 L 4.296875 -1.953125 L 1.59375 -1.953125 L 0.890625 0 Z M 1.84375 -2.640625 L 4.03125 -2.640625 L 3.359375 -4.4375 C 3.148438 -4.976562 3 -5.421875 2.90625 -5.765625 C 2.820312 -5.347656 2.703125 -4.9375 2.546875 -4.53125 Z M 1.84375 -2.640625 "/>
</symbol>
<symbol overflow="visible" id="glyph0-3">
<path style="stroke:none;" d="M 2.25 -1.046875 L 2.25 -2.8125 L 0.5 -2.8125 L 0.5 -3.546875 L 2.25 -3.546875 L 2.25 -5.296875 L 3 -5.296875 L 3 -3.546875 L 4.75 -3.546875 L 4.75 -2.8125 L 3 -2.8125 L 3 -1.046875 Z M 2.25 -1.046875 "/>
</symbol>
<symbol overflow="visible" id="glyph0-4">
<path style="stroke:none;" d="M 0.609375 1.78125 L 0.609375 -6.4375 L 2.359375 -6.4375 L 2.359375 -5.78125 L 1.40625 -5.78125 L 1.40625 1.140625 L 2.359375 1.140625 L 2.359375 1.78125 Z M 0.609375 1.78125 "/>
</symbol>
<symbol overflow="visible" id="glyph0-5">
<path style="stroke:none;" d=""/>
</symbol>
<symbol overflow="visible" id="glyph0-6">
<path style="stroke:none;" d="M 0.796875 0 L 0.796875 -0.90625 L 1.703125 -0.90625 L 1.703125 0 C 1.703125 0.332031 1.640625 0.597656 1.515625 0.796875 C 1.398438 1.003906 1.21875 1.164062 0.96875 1.28125 L 0.75 0.9375 C 0.914062 0.863281 1.035156 0.753906 1.109375 0.609375 C 1.191406 0.472656 1.238281 0.269531 1.25 0 Z M 0.796875 0 "/>
</symbol>
<symbol overflow="visible" id="glyph0-7">
<path style="stroke:none;" d="M 1.921875 1.78125 L 0.171875 1.78125 L 0.171875 1.140625 L 1.125 1.140625 L 1.125 -5.78125 L 0.171875 -5.78125 L 0.171875 -6.4375 L 1.921875 -6.4375 Z M 1.921875 1.78125 "/>
</symbol>
</g>
</defs>
<g id="surface1">
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 103.5 184.5 L 166.5 184.5 L 166.5 202.5 L 103.5 202.5 Z M 103.5 184.5 " transform="matrix(1,0,0,1,-103,-152)"/>
<g style="fill:rgb(0%,0%,0%);fill-opacity:1;">
  <use xlink:href="#glyph0-1" x="28.9985" y="44.3528"/>
</g>
<path style="fill-rule:nonzero;fill:rgb(69.804382%,85.098267%,100%);fill-opacity:1;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 103.5 225 L 166.5 225 L 166.5 243 L 103.5 243 Z M 103.5 225 " transform="matrix(1,0,0,1,-103,-152)"/>
<g style="fill:rgb(0%,0%,0%);fill-opacity:1;">
  <use xlink:href="#glyph0-2" x="28.9985" y="84.8528"/>
</g>
<path style="fill-rule:nonzero;fill:rgb(39.99939%,74.902344%,100%);fill-opacity:1;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 140.074219 154.976562 C 142.710938 157.613281 142.710938 161.886719 140.074219 164.523438 C 137.4375 167.160156 133.164062 167.160156 130.527344 164.523438 C 127.894531 161.886719 127.894531 157.613281 130.527344 154.976562 C 133.164062 152.339844 137.4375 152.339844 140.074219 154.976562 " transform="matrix(1,0,0,1,-103,-152)"/>
<g style="fill:rgb(0%,0%,0%);fill-opacity:1;">
  <use xlink:href="#glyph0-3" x="29.674" y="10.6028"/>
</g>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 135 225 L 135 208.398438 " transform="matrix(1,0,0,1,-103,-152)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:butt;stroke-linejoin:miter;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 135 204.398438 L 135 208.398438 M 133.5 208.398438 L 135 204.398438 L 136.5 208.398438 " transform="matrix(1,0,0,1,-103,-152)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 135.082031 184.5 L 135.1875 172.398438 " transform="matrix(1,0,0,1,-103,-152)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:butt;stroke-linejoin:miter;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 135.222656 168.398438 L 135.1875 172.398438 M 133.6875 172.386719 L 135.222656 168.398438 L 136.6875 172.414062 " transform="matrix(1,0,0,1,-103,-152)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 135 217.082031 L 172.5 217.082031 C 174.15625 217.082031 175.5 215.738281 175.5 214.082031 L 175.5 162.75 C 175.5 161.09375 174.15625 159.75 172.5 159.75 L 147.953125 159.75 " transform="matrix(1,0,0,1,-103,-152)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:butt;stroke-linejoin:miter;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 143.953125 159.75 L 147.953125 159.75 M 147.953125 161.25 L 143.953125 159.75 L 147.953125 158.25 " transform="matrix(1,0,0,1,-103,-152)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 207 184.5 L 270 184.5 L 270 202.5 L 207 202.5 Z M 207 184.5 " transform="matrix(1,0,0,1,-103,-152)"/>
<g style="fill:rgb(0%,0%,0%);fill-opacity:1;">
  <use xlink:href="#glyph0-1" x="132.4985" y="44.3528"/>
</g>
<path style="fill-rule:nonzero;fill:rgb(69.804382%,85.098267%,100%);fill-opacity:1;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 207 225 L 270 225 L 270 243 L 207 243 Z M 207 225 " transform="matrix(1,0,0,1,-103,-152)"/>
<g style="fill:rgb(0%,0%,0%);fill-opacity:1;">
  <use xlink:href="#glyph0-2" x="132.4985" y="84.8528"/>
</g>
<path style="fill-rule:nonzero;fill:rgb(39.99939%,74.902344%,100%);fill-opacity:1;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 230.707031 153 L 248.242188 153 C 250.316406 153 252 156.023438 252 159.75 C 252 163.476562 250.316406 166.5 248.242188 166.5 L 230.707031 166.5 C 228.632812 166.5 226.949219 163.476562 226.949219 159.75 C 226.949219 156.023438 228.632812 153 230.707031 153 Z M 230.707031 153 " transform="matrix(1,0,0,1,-103,-152)"/>
<g style="fill:rgb(0%,0%,0%);fill-opacity:1;">
  <use xlink:href="#glyph0-4" x="130.2228" y="10.6028"/>
  <use xlink:href="#glyph0-5" x="132.723" y="10.6028"/>
  <use xlink:href="#glyph0-6" x="135.2232" y="10.6028"/>
  <use xlink:href="#glyph0-5" x="137.7234" y="10.6028"/>
  <use xlink:href="#glyph0-7" x="140.2236" y="10.6028"/>
</g>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 238.5 225 L 238.5 208.398438 " transform="matrix(1,0,0,1,-103,-152)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:butt;stroke-linejoin:miter;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 238.5 204.398438 L 238.5 208.398438 M 237 208.398438 L 238.5 204.398438 L 240 208.398438 " transform="matrix(1,0,0,1,-103,-152)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 238.757812 184.5 L 239.109375 172.398438 " transform="matrix(1,0,0,1,-103,-152)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:butt;stroke-linejoin:miter;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 239.222656 168.398438 L 239.109375 172.398438 M 237.609375 172.355469 L 239.222656 168.398438 L 240.609375 172.441406 " transform="matrix(1,0,0,1,-103,-152)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 238.5 216.667969 L 276 216.667969 C 277.65625 216.667969 279 215.324219 279 213.667969 L 279 162.75 C 279 161.09375 277.65625 159.75 276 159.75 L 257.898438 159.75 " transform="matrix(1,0,0,1,-103,-152)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:butt;stroke-linejoin:miter;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 253.898438 159.75 L 257.898438 159.75 M 257.898438 161.25 L 253.898438 159.75 L 257.898438 158.25 " transform="matrix(1,0,0,1,-103,-152)"/>
</g>
</svg>
