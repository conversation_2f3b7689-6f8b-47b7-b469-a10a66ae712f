{"cells": [{"cell_type": "markdown", "id": "47c4023b", "metadata": {"origin_pos": 0}, "source": ["# 自然语言处理：预训练\n", ":label:`chap_nlp_pretrain`\n", "\n", "人与人之间需要交流。\n", "出于人类这种基本需要，每天都有大量的书面文本产生。\n", "比如，社交媒体、聊天应用、电子邮件、产品评论、新闻文章、\n", "研究论文和书籍中的丰富文本，\n", "使计算机能够理解它们以提供帮助或基于人类语言做出决策变得至关重要。\n", "\n", "*自然语言处理*是指研究使用自然语言的计算机和人类之间的交互。\n", "在实践中，使用自然语言处理技术来处理和分析文本数据是非常常见的，\n", "例如 :numref:`sec_language_model`的语言模型\n", "和 :numref:`sec_machine_translation`的机器翻译模型。\n", "\n", "要理解文本，我们可以从学习它的表示开始。\n", "利用来自大型语料库的现有文本序列，\n", "*自监督学习*（self-supervised learning）\n", "已被广泛用于预训练文本表示，\n", "例如通过使用周围文本的其它部分来预测文本的隐藏部分。\n", "通过这种方式，模型可以通过有监督地从*海量*文本数据中学习，而不需要*昂贵*的标签标注！\n", "\n", "本章我们将看到：当将每个单词或子词视为单个词元时，\n", "可以在大型语料库上使用word2vec、GloVe或子词嵌入模型预先训练每个词元的词元。\n", "经过预训练后，每个词元的表示可以是一个向量。\n", "但是，无论上下文是什么，它都保持不变。\n", "例如，“bank”（可以译作银行或者河岸）的向量表示在\n", "“go to the bank to deposit some money”（去银行存点钱）\n", "和“go to the bank to sit down”（去河岸坐下来）中是相同的。\n", "因此，许多较新的预训练模型使相同词元的表示适应于不同的上下文，\n", "其中包括基于Transformer编码器的更深的自监督模型BERT。\n", "在本章中，我们将重点讨论如何预训练文本的这种表示，\n", "如 :numref:`fig_nlp-map-pretrain`中所强调的那样。\n", "\n", "![预训练好的文本表示可以放入各种深度学习架构，应用于不同自然语言处理任务（本章主要研究上游文本的预训练）](../img/nlp-map-pretrain.svg)\n", ":label:`fig_nlp-map-pretrain`\n", "\n", " :numref:`fig_nlp-map-pretrain`显示了\n", "预训练好的文本表示可以放入各种深度学习架构，应用于不同自然语言处理任务。\n", "我们将在 :numref:`chap_nlp_app`中介绍它们。\n", "\n", ":begin_tab:toc\n", " - [word2vec](word2vec.ipynb)\n", " - [approx-training](approx-training.ipynb)\n", " - [word-embedding-dataset](word-embedding-dataset.ipynb)\n", " - [word2vec-pretraining](word2vec-pretraining.ipynb)\n", " - [glove](glove.ipynb)\n", " - [subword-embedding](subword-embedding.ipynb)\n", " - [similarity-analogy](similarity-analogy.ipynb)\n", " - [bert](bert.ipynb)\n", " - [bert-dataset](bert-dataset.ipynb)\n", " - [bert-pretraining](bert-pretraining.ipynb)\n", ":end_tab:\n"]}], "metadata": {"language_info": {"name": "python"}}, "nbformat": 4, "nbformat_minor": 5}