{"cells": [{"cell_type": "markdown", "id": "ad8ad555", "metadata": {"origin_pos": 0}, "source": ["# 语义分割和数据集\n", ":label:`sec_semantic_segmentation`\n", "\n", "在 :numref:`sec_bbox`— :numref:`sec_rcnn`中讨论的目标检测问题中，我们一直使用方形边界框来标注和预测图像中的目标。\n", "本节将探讨*语义分割*（semantic segmentation）问题，它重点关注于如何将图像分割成属于不同语义类别的区域。\n", "与目标检测不同，语义分割可以识别并理解图像中每一个像素的内容：其语义区域的标注和预测是像素级的。\n", " :numref:`fig_segmentation`展示了语义分割中图像有关狗、猫和背景的标签。\n", "与目标检测相比，语义分割标注的像素级的边框显然更加精细。\n", "\n", "![语义分割中图像有关狗、猫和背景的标签](../img/segmentation.svg)\n", ":label:`fig_segmentation`\n", "\n", "## 图像分割和实例分割\n", "\n", "计算机视觉领域还有2个与语义分割相似的重要问题，即*图像分割*（image segmentation）和*实例分割*（instance segmentation）。\n", "我们在这里将它们同语义分割简单区分一下。\n", "\n", "* *图像分割*将图像划分为若干组成区域，这类问题的方法通常利用图像中像素之间的相关性。它在训练时不需要有关图像像素的标签信息，在预测时也无法保证分割出的区域具有我们希望得到的语义。以 :numref:`fig_segmentation`中的图像作为输入，图像分割可能会将狗分为两个区域：一个覆盖以黑色为主的嘴和眼睛，另一个覆盖以黄色为主的其余部分身体。\n", "* *实例分割*也叫*同时检测并分割*（simultaneous detection and segmentation），它研究如何识别图像中各个目标实例的像素级区域。与语义分割不同，实例分割不仅需要区分语义，还要区分不同的目标实例。例如，如果图像中有两条狗，则实例分割需要区分像素属于的两条狗中的哪一条。\n", "\n", "## Pascal VOC2012 语义分割数据集\n", "\n", "[**最重要的语义分割数据集之一是[Pascal VOC2012](http://host.robots.ox.ac.uk/pascal/VOC/voc2012/)。**]\n", "下面我们深入了解一下这个数据集。\n"]}, {"cell_type": "code", "execution_count": 1, "id": "673fcf64", "metadata": {"execution": {"iopub.execute_input": "2022-12-07T16:32:29.771164Z", "iopub.status.busy": "2022-12-07T16:32:29.770455Z", "iopub.status.idle": "2022-12-07T16:32:32.151395Z", "shell.execute_reply": "2022-12-07T16:32:32.150569Z"}, "origin_pos": 2, "tab": ["pytorch"]}, "outputs": [], "source": ["%matplotlib inline\n", "import os\n", "import torch\n", "import torchvision\n", "from d2l import torch as d2l"]}, {"cell_type": "markdown", "id": "62c28cbb", "metadata": {"origin_pos": 4}, "source": ["数据集的tar文件大约为2GB，所以下载可能需要一段时间。\n", "提取出的数据集位于`../data/VOCdevkit/VOC2012`。\n"]}, {"cell_type": "code", "execution_count": 2, "id": "6162a6e1", "metadata": {"execution": {"iopub.execute_input": "2022-12-07T16:32:32.155421Z", "iopub.status.busy": "2022-12-07T16:32:32.154866Z", "iopub.status.idle": "2022-12-07T16:33:39.018052Z", "shell.execute_reply": "2022-12-07T16:33:39.017222Z"}, "origin_pos": 5, "tab": ["pytorch"]}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Downloading ../data/VOCtrainval_11-May-2012.tar from http://d2l-data.s3-accelerate.amazonaws.com/VOCtrainval_11-May-2012.tar...\n"]}], "source": ["#@save\n", "d2l.DATA_HUB['voc2012'] = (d2l.DATA_URL + 'VOCtrainval_11-May-2012.tar',\n", "                           '4e443f8a2eca6b1dac8a6c57641b67dd40621a49')\n", "\n", "voc_dir = d2l.download_extract('voc2012', 'VOCdevkit/VOC2012')"]}, {"cell_type": "markdown", "id": "9df1311a", "metadata": {"origin_pos": 6}, "source": ["进入路径`../data/VOCdevkit/VOC2012`之后，我们可以看到数据集的不同组件。\n", "`ImageSets/Segmentation`路径包含用于训练和测试样本的文本文件，而`JPEGImages`和`SegmentationClass`路径分别存储着每个示例的输入图像和标签。\n", "此处的标签也采用图像格式，其尺寸和它所标注的输入图像的尺寸相同。\n", "此外，标签中颜色相同的像素属于同一个语义类别。\n", "下面将`read_voc_images`函数定义为[**将所有输入的图像和标签读入内存**]。\n"]}, {"cell_type": "code", "execution_count": 3, "id": "f258abf4", "metadata": {"execution": {"iopub.execute_input": "2022-12-07T16:33:39.022037Z", "iopub.status.busy": "2022-12-07T16:33:39.021374Z", "iopub.status.idle": "2022-12-07T16:33:44.596881Z", "shell.execute_reply": "2022-12-07T16:33:44.596034Z"}, "origin_pos": 8, "tab": ["pytorch"]}, "outputs": [], "source": ["#@save\n", "def read_voc_images(voc_dir, is_train=True):\n", "    \"\"\"读取所有VOC图像并标注\"\"\"\n", "    txt_fname = os.path.join(voc_dir, 'ImageSets', 'Segmentation',\n", "                             'train.txt' if is_train else 'val.txt')\n", "    mode = torchvision.io.image.ImageReadMode.RGB\n", "    with open(txt_fname, 'r') as f:\n", "        images = f.read().split()\n", "    features, labels = [], []\n", "    for i, fname in enumerate(images):\n", "        features.append(torchvision.io.read_image(os.path.join(\n", "            voc_dir, 'JPEGImages', f'{fname}.jpg')))\n", "        labels.append(torchvision.io.read_image(os.path.join(\n", "            voc_dir, 'SegmentationClass' ,f'{fname}.png'), mode))\n", "    return features, labels\n", "\n", "train_features, train_labels = read_voc_images(voc_dir, True)"]}, {"cell_type": "markdown", "id": "fd357e8b", "metadata": {"origin_pos": 10}, "source": ["下面我们[**绘制前5个输入图像及其标签**]。\n", "在标签图像中，白色和黑色分别表示边框和背景，而其他颜色则对应不同的类别。\n"]}, {"cell_type": "code", "execution_count": 4, "id": "468faab4", "metadata": {"execution": {"iopub.execute_input": "2022-12-07T16:33:44.600677Z", "iopub.status.busy": "2022-12-07T16:33:44.600206Z", "iopub.status.idle": "2022-12-07T16:33:45.265432Z", "shell.execute_reply": "2022-12-07T16:33:45.264631Z"}, "origin_pos": 12, "tab": ["pytorch"]}, "outputs": [{"data": {"image/png": "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********************************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\n", "text/plain": ["<Figure size 750x300 with 10 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["n = 5\n", "imgs = train_features[0:n] + train_labels[0:n]\n", "imgs = [img.permute(1,2,0) for img in imgs]\n", "d2l.show_images(imgs, 2, n);"]}, {"cell_type": "markdown", "id": "dc4fe95b", "metadata": {"origin_pos": 14}, "source": ["接下来，我们[**列举RGB颜色值和类名**]。\n"]}, {"cell_type": "code", "execution_count": 5, "id": "1cbe2bf7", "metadata": {"execution": {"iopub.execute_input": "2022-12-07T16:33:45.269715Z", "iopub.status.busy": "2022-12-07T16:33:45.269033Z", "iopub.status.idle": "2022-12-07T16:33:45.275684Z", "shell.execute_reply": "2022-12-07T16:33:45.274936Z"}, "origin_pos": 15, "tab": ["pytorch"]}, "outputs": [], "source": ["#@save\n", "VOC_COLORMAP = [[0, 0, 0], [128, 0, 0], [0, 128, 0], [128, 128, 0],\n", "                [0, 0, 128], [128, 0, 128], [0, 128, 128], [128, 128, 128],\n", "                [64, 0, 0], [192, 0, 0], [64, 128, 0], [192, 128, 0],\n", "                [64, 0, 128], [192, 0, 128], [64, 128, 128], [192, 128, 128],\n", "                [0, 64, 0], [128, 64, 0], [0, 192, 0], [128, 192, 0],\n", "                [0, 64, 128]]\n", "\n", "#@save\n", "VOC_CLASSES = ['background', 'aeroplane', 'bicycle', 'bird', 'boat',\n", "               'bottle', 'bus', 'car', 'cat', 'chair', 'cow',\n", "               'diningtable', 'dog', 'horse', 'motorbike', 'person',\n", "               'potted plant', 'sheep', 'sofa', 'train', 'tv/monitor']"]}, {"cell_type": "markdown", "id": "78ebb38e", "metadata": {"origin_pos": 16}, "source": ["通过上面定义的两个常量，我们可以方便地[**查找标签中每个像素的类索引**]。\n", "我们定义了`voc_colormap2label`函数来构建从上述RGB颜色值到类别索引的映射，而`voc_label_indices`函数将RGB值映射到在Pascal VOC2012数据集中的类别索引。\n"]}, {"cell_type": "code", "execution_count": 6, "id": "7e806235", "metadata": {"execution": {"iopub.execute_input": "2022-12-07T16:33:45.278965Z", "iopub.status.busy": "2022-12-07T16:33:45.278381Z", "iopub.status.idle": "2022-12-07T16:33:45.284167Z", "shell.execute_reply": "2022-12-07T16:33:45.283437Z"}, "origin_pos": 18, "tab": ["pytorch"]}, "outputs": [], "source": ["#@save\n", "def voc_colormap2label():\n", "    \"\"\"构建从RGB到VOC类别索引的映射\"\"\"\n", "    colormap2label = torch.zeros(256 ** 3, dtype=torch.long)\n", "    for i, colormap in enumerate(VOC_COLORMAP):\n", "        colormap2label[\n", "            (colormap[0] * 256 + colormap[1]) * 256 + colormap[2]] = i\n", "    return colormap2label\n", "\n", "#@save\n", "def voc_label_indices(colormap, colormap2label):\n", "    \"\"\"将VOC标签中的RGB值映射到它们的类别索引\"\"\"\n", "    colormap = colormap.permute(1, 2, 0).numpy().astype('int32')\n", "    idx = ((colormap[:, :, 0] * 256 + colormap[:, :, 1]) * 256\n", "           + colormap[:, :, 2])\n", "    return colormap2label[idx]"]}, {"cell_type": "markdown", "id": "7affb616", "metadata": {"origin_pos": 20}, "source": ["[**例如**]，在第一张样本图像中，飞机头部区域的类别索引为1，而背景索引为0。\n"]}, {"cell_type": "code", "execution_count": 7, "id": "eea92919", "metadata": {"execution": {"iopub.execute_input": "2022-12-07T16:33:45.287587Z", "iopub.status.busy": "2022-12-07T16:33:45.287052Z", "iopub.status.idle": "2022-12-07T16:33:45.335509Z", "shell.execute_reply": "2022-12-07T16:33:45.334601Z"}, "origin_pos": 21, "tab": ["pytorch"]}, "outputs": [{"data": {"text/plain": ["(tensor([[0, 0, 0, 0, 0, 0, 0, 0, 0, 1],\n", "         [0, 0, 0, 0, 0, 0, 0, 1, 1, 1],\n", "         [0, 0, 0, 0, 0, 0, 1, 1, 1, 1],\n", "         [0, 0, 0, 0, 0, 1, 1, 1, 1, 1],\n", "         [0, 0, 0, 0, 0, 1, 1, 1, 1, 1],\n", "         [0, 0, 0, 0, 1, 1, 1, 1, 1, 1],\n", "         [0, 0, 0, 0, 0, 1, 1, 1, 1, 1],\n", "         [0, 0, 0, 0, 0, 1, 1, 1, 1, 1],\n", "         [0, 0, 0, 0, 0, 0, 1, 1, 1, 1],\n", "         [0, 0, 0, 0, 0, 0, 0, 0, 1, 1]]),\n", " 'aeroplane')"]}, "execution_count": 7, "metadata": {}, "output_type": "execute_result"}], "source": ["y = voc_label_indices(train_labels[0], voc_colormap2label())\n", "y[105:115, 130:140], VOC_CLASSES[1]"]}, {"cell_type": "markdown", "id": "bb9a66be", "metadata": {"origin_pos": 22}, "source": ["### 预处理数据\n", "\n", "在之前的实验，例如 :numref:`sec_alexnet`— :numref:`sec_googlenet`中，我们通过再缩放图像使其符合模型的输入形状。\n", "然而在语义分割中，这样做需要将预测的像素类别重新映射回原始尺寸的输入图像。\n", "这样的映射可能不够精确，尤其在不同语义的分割区域。\n", "为了避免这个问题，我们将图像裁剪为固定尺寸，而不是再缩放。\n", "具体来说，我们[**使用图像增广中的随机裁剪，裁剪输入图像和标签的相同区域**]。\n"]}, {"cell_type": "code", "execution_count": 8, "id": "78979269", "metadata": {"execution": {"iopub.execute_input": "2022-12-07T16:33:45.339682Z", "iopub.status.busy": "2022-12-07T16:33:45.339361Z", "iopub.status.idle": "2022-12-07T16:33:45.344434Z", "shell.execute_reply": "2022-12-07T16:33:45.343681Z"}, "origin_pos": 24, "tab": ["pytorch"]}, "outputs": [], "source": ["#@save\n", "def voc_rand_crop(feature, label, height, width):\n", "    \"\"\"随机裁剪特征和标签图像\"\"\"\n", "    rect = torchvision.transforms.RandomCrop.get_params(\n", "        feature, (height, width))\n", "    feature = torchvision.transforms.functional.crop(feature, *rect)\n", "    label = torchvision.transforms.functional.crop(label, *rect)\n", "    return feature, label"]}, {"cell_type": "code", "execution_count": 9, "id": "f31cffc3", "metadata": {"execution": {"iopub.execute_input": "2022-12-07T16:33:45.347736Z", "iopub.status.busy": "2022-12-07T16:33:45.347204Z", "iopub.status.idle": "2022-12-07T16:33:45.832112Z", "shell.execute_reply": "2022-12-07T16:33:45.831302Z"}, "origin_pos": 27, "tab": ["pytorch"]}, "outputs": [{"data": {"image/png": "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\n", "text/plain": ["<Figure size 750x300 with 10 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["imgs = []\n", "for _ in range(n):\n", "    imgs += voc_rand_crop(train_features[0], train_labels[0], 200, 300)\n", "\n", "imgs = [img.permute(1, 2, 0) for img in imgs]\n", "d2l.show_images(imgs[::2] + imgs[1::2], 2, n);"]}, {"cell_type": "markdown", "id": "7718394b", "metadata": {"origin_pos": 29}, "source": ["### [**自定义语义分割数据集类**]\n", "\n", "我们通过继承高级API提供的`Dataset`类，自定义了一个语义分割数据集类`VOCSegDataset`。\n", "通过实现`__getitem__`函数，我们可以任意访问数据集中索引为`idx`的输入图像及其每个像素的类别索引。\n", "由于数据集中有些图像的尺寸可能小于随机裁剪所指定的输出尺寸，这些样本可以通过自定义的`filter`函数移除掉。\n", "此外，我们还定义了`normalize_image`函数，从而对输入图像的RGB三个通道的值分别做标准化。\n"]}, {"cell_type": "code", "execution_count": 10, "id": "c2dcb2c3", "metadata": {"execution": {"iopub.execute_input": "2022-12-07T16:33:45.835841Z", "iopub.status.busy": "2022-12-07T16:33:45.835299Z", "iopub.status.idle": "2022-12-07T16:33:45.843459Z", "shell.execute_reply": "2022-12-07T16:33:45.842705Z"}, "origin_pos": 31, "tab": ["pytorch"]}, "outputs": [], "source": ["#@save\n", "class VOCSegDataset(torch.utils.data.Dataset):\n", "    \"\"\"一个用于加载VOC数据集的自定义数据集\"\"\"\n", "\n", "    def __init__(self, is_train, crop_size, voc_dir):\n", "        self.transform = torchvision.transforms.Normalize(\n", "            mean=[0.485, 0.456, 0.406], std=[0.229, 0.224, 0.225])\n", "        self.crop_size = crop_size\n", "        features, labels = read_voc_images(voc_dir, is_train=is_train)\n", "        self.features = [self.normalize_image(feature)\n", "                         for feature in self.filter(features)]\n", "        self.labels = self.filter(labels)\n", "        self.colormap2label = voc_colormap2label()\n", "        print('read ' + str(len(self.features)) + ' examples')\n", "\n", "    def normalize_image(self, img):\n", "        return self.transform(img.float() / 255)\n", "\n", "    def filter(self, imgs):\n", "        return [img for img in imgs if (\n", "            img.shape[1] >= self.crop_size[0] and\n", "            img.shape[2] >= self.crop_size[1])]\n", "\n", "    def __getitem__(self, idx):\n", "        feature, label = voc_rand_crop(self.features[idx], self.labels[idx],\n", "                                       *self.crop_size)\n", "        return (feature, voc_label_indices(label, self.colormap2label))\n", "\n", "    def __len__(self):\n", "        return len(self.features)"]}, {"cell_type": "markdown", "id": "dced00ff", "metadata": {"origin_pos": 33}, "source": ["### [**读取数据集**]\n", "\n", "我们通过自定义的`VOCSegDataset`类来分别创建训练集和测试集的实例。\n", "假设我们指定随机裁剪的输出图像的形状为$320\\times 480$，\n", "下面我们可以查看训练集和测试集所保留的样本个数。\n"]}, {"cell_type": "code", "execution_count": 11, "id": "237d50c6", "metadata": {"execution": {"iopub.execute_input": "2022-12-07T16:33:45.848300Z", "iopub.status.busy": "2022-12-07T16:33:45.847783Z", "iopub.status.idle": "2022-12-07T16:33:58.294326Z", "shell.execute_reply": "2022-12-07T16:33:58.293040Z"}, "origin_pos": 34, "tab": ["pytorch"]}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["read 1114 examples\n"]}, {"name": "stdout", "output_type": "stream", "text": ["read 1078 examples\n"]}], "source": ["crop_size = (320, 480)\n", "voc_train = VOCSegDataset(True, crop_size, voc_dir)\n", "voc_test = VOCSegDataset(False, crop_size, voc_dir)"]}, {"cell_type": "markdown", "id": "873d09ab", "metadata": {"origin_pos": 35}, "source": ["设批量大小为64，我们定义训练集的迭代器。\n", "打印第一个小批量的形状会发现：与图像分类或目标检测不同，这里的标签是一个三维数组。\n"]}, {"cell_type": "code", "execution_count": 12, "id": "8c4bf912", "metadata": {"execution": {"iopub.execute_input": "2022-12-07T16:33:58.299530Z", "iopub.status.busy": "2022-12-07T16:33:58.298871Z", "iopub.status.idle": "2022-12-07T16:33:59.935022Z", "shell.execute_reply": "2022-12-07T16:33:59.934033Z"}, "origin_pos": 37, "tab": ["pytorch"]}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["torch.<PERSON><PERSON>([64, 3, 320, 480])\n", "torch.<PERSON><PERSON>([64, 320, 480])\n"]}], "source": ["batch_size = 64\n", "train_iter = torch.utils.data.DataLoader(voc_train, batch_size, shuffle=True,\n", "                                    drop_last=True,\n", "                                    num_workers=d2l.get_dataloader_workers())\n", "for X, Y in train_iter:\n", "    print(X.shape)\n", "    print(Y.shape)\n", "    break"]}, {"cell_type": "markdown", "id": "be3d7aa1", "metadata": {"origin_pos": 39}, "source": ["### [**整合所有组件**]\n", "\n", "最后，我们定义以下`load_data_voc`函数来下载并读取Pascal VOC2012语义分割数据集。\n", "它返回训练集和测试集的数据迭代器。\n"]}, {"cell_type": "code", "execution_count": 13, "id": "cff03cb3", "metadata": {"execution": {"iopub.execute_input": "2022-12-07T16:33:59.939386Z", "iopub.status.busy": "2022-12-07T16:33:59.938699Z", "iopub.status.idle": "2022-12-07T16:33:59.944655Z", "shell.execute_reply": "2022-12-07T16:33:59.943933Z"}, "origin_pos": 41, "tab": ["pytorch"]}, "outputs": [], "source": ["#@save\n", "def load_data_voc(batch_size, crop_size):\n", "    \"\"\"加载VOC语义分割数据集\"\"\"\n", "    voc_dir = d2l.download_extract('voc2012', os.path.join(\n", "        'VOCdevkit', 'VOC2012'))\n", "    num_workers = d2l.get_dataloader_workers()\n", "    train_iter = torch.utils.data.DataLoader(\n", "        VOCSegDataset(True, crop_size, voc_dir), batch_size,\n", "        shuffle=True, drop_last=True, num_workers=num_workers)\n", "    test_iter = torch.utils.data.DataLoader(\n", "        VOCSegDataset(False, crop_size, voc_dir), batch_size,\n", "        drop_last=True, num_workers=num_workers)\n", "    return train_iter, test_iter"]}, {"cell_type": "markdown", "id": "b4abfdaf", "metadata": {"origin_pos": 43}, "source": ["## 小结\n", "\n", "* 语义分割通过将图像划分为属于不同语义类别的区域，来识别并理解图像中像素级别的内容。\n", "* 语义分割的一个重要的数据集叫做Pascal VOC2012。\n", "* 由于语义分割的输入图像和标签在像素上一一对应，输入图像会被随机裁剪为固定尺寸而不是缩放。\n", "\n", "## 练习\n", "\n", "1. 如何在自动驾驶和医疗图像诊断中应用语义分割？还能想到其他领域的应用吗？\n", "1. 回想一下 :numref:`sec_image_augmentation`中对数据增强的描述。图像分类中使用的哪种图像增强方法是难以用于语义分割的？\n"]}, {"cell_type": "markdown", "id": "38978328", "metadata": {"origin_pos": 45, "tab": ["pytorch"]}, "source": ["[Discussions](https://discuss.d2l.ai/t/3295)\n"]}], "metadata": {"language_info": {"name": "python"}}, "nbformat": 4, "nbformat_minor": 5}