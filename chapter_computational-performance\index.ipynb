{"cells": [{"cell_type": "markdown", "id": "023693d4", "metadata": {"origin_pos": 0}, "source": ["# 计算性能\n", ":label:`chap_performance`\n", "\n", "在深度学习中，数据集和模型通常都很大，导致计算量也会很大。\n", "因此，计算的性能非常重要。\n", "本章将集中讨论影响计算性能的主要因素：命令式编程、符号编程、\n", "异步计算、自动并行和多GPU计算。\n", "通过学习本章，对于前几章中实现的那些模型，可以进一步提高它们的计算性能。\n", "例如，我们可以在不影响准确性的前提下，大大减少训练时间。\n", "\n", ":begin_tab:toc\n", " - [hybridize](hybridize.ipynb)\n", " - [async-computation](async-computation.ipynb)\n", " - [auto-parallelism](auto-parallelism.ipynb)\n", " - [hardware](hardware.ipynb)\n", " - [multiple-gpus](multiple-gpus.ipynb)\n", " - [multiple-gpus-concise](multiple-gpus-concise.ipynb)\n", " - [parameterserver](parameterserver.ipynb)\n", ":end_tab:\n"]}], "metadata": {"language_info": {"name": "python"}}, "nbformat": 4, "nbformat_minor": 5}