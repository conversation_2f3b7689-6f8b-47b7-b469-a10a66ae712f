{"cells": [{"cell_type": "markdown", "id": "f265b812", "metadata": {"origin_pos": 0}, "source": ["# 微调\n", ":label:`sec_fine_tuning`\n", "\n", "前面的一些章节介绍了如何在只有6万张图像的Fashion-MNIST训练数据集上训练模型。\n", "我们还描述了学术界当下使用最广泛的大规模图像数据集ImageNet，它有超过1000万的图像和1000类的物体。\n", "然而，我们平常接触到的数据集的规模通常在这两者之间。\n", "\n", "假如我们想识别图片中不同类型的椅子，然后向用户推荐购买链接。\n", "一种可能的方法是首先识别100把普通椅子，为每把椅子拍摄1000张不同角度的图像，然后在收集的图像数据集上训练一个分类模型。\n", "尽管这个椅子数据集可能大于Fashion-MNIST数据集，但实例数量仍然不到ImageNet中的十分之一。\n", "适合ImageNet的复杂模型可能会在这个椅子数据集上过拟合。\n", "此外，由于训练样本数量有限，训练模型的准确性可能无法满足实际要求。\n", "\n", "为了解决上述问题，一个显而易见的解决方案是收集更多的数据。\n", "但是，收集和标记数据可能需要大量的时间和金钱。\n", "例如，为了收集ImageNet数据集，研究人员花费了数百万美元的研究资金。\n", "尽管目前的数据收集成本已大幅降低，但这一成本仍不能忽视。\n", "\n", "另一种解决方案是应用*迁移学习*（transfer learning）将从*源数据集*学到的知识迁移到*目标数据集*。\n", "例如，尽管ImageNet数据集中的大多数图像与椅子无关，但在此数据集上训练的模型可能会提取更通用的图像特征，这有助于识别边缘、纹理、形状和对象组合。\n", "这些类似的特征也可能有效地识别椅子。\n", "\n", "## 步骤\n", "\n", "本节将介绍迁移学习中的常见技巧:*微调*（fine-tuning）。如 :numref:`fig_finetune`所示，微调包括以下四个步骤。\n", "\n", "1. 在源数据集（例如ImageNet数据集）上预训练神经网络模型，即*源模型*。\n", "1. 创建一个新的神经网络模型，即*目标模型*。这将复制源模型上的所有模型设计及其参数（输出层除外）。我们假定这些模型参数包含从源数据集中学到的知识，这些知识也将适用于目标数据集。我们还假设源模型的输出层与源数据集的标签密切相关；因此不在目标模型中使用该层。\n", "1. 向目标模型添加输出层，其输出数是目标数据集中的类别数。然后随机初始化该层的模型参数。\n", "1. 在目标数据集（如椅子数据集）上训练目标模型。输出层将从头开始进行训练，而所有其他层的参数将根据源模型的参数进行微调。\n", "\n", "![微调。](../img/finetune.svg)\n", ":label:`fig_finetune`\n", "\n", "当目标数据集比源数据集小得多时，微调有助于提高模型的泛化能力。\n", "\n", "## 热狗识别\n", "\n", "让我们通过具体案例演示微调：热狗识别。\n", "我们将在一个小型数据集上微调ResNet模型。该模型已在ImageNet数据集上进行了预训练。\n", "这个小型数据集包含数千张包含热狗和不包含热狗的图像，我们将使用微调模型来识别图像中是否包含热狗。\n"]}, {"cell_type": "code", "execution_count": 1, "id": "6ff22d3e", "metadata": {"execution": {"iopub.execute_input": "2022-12-07T16:37:45.116938Z", "iopub.status.busy": "2022-12-07T16:37:45.116615Z", "iopub.status.idle": "2022-12-07T16:37:49.241932Z", "shell.execute_reply": "2022-12-07T16:37:49.241077Z"}, "origin_pos": 2, "tab": ["pytorch"]}, "outputs": [], "source": ["%matplotlib inline\n", "import os\n", "import torch\n", "import torchvision\n", "from torch import nn\n", "from d2l import torch as d2l"]}, {"cell_type": "markdown", "id": "2d138ea1", "metadata": {"origin_pos": 4}, "source": ["### 获取数据集\n", "\n", "我们使用的[**热狗数据集来源于网络**]。\n", "该数据集包含1400张热狗的“正类”图像，以及包含尽可能多的其他食物的“负类”图像。\n", "含着两个类别的1000张图片用于训练，其余的则用于测试。\n", "\n", "解压下载的数据集，我们获得了两个文件夹`hotdog/train`和`hotdog/test`。\n", "这两个文件夹都有`hotdog`（有热狗）和`not-hotdog`（无热狗）两个子文件夹，\n", "子文件夹内都包含相应类的图像。\n"]}, {"cell_type": "code", "execution_count": 2, "id": "e960c12b", "metadata": {"execution": {"iopub.execute_input": "2022-12-07T16:37:49.246023Z", "iopub.status.busy": "2022-12-07T16:37:49.245639Z", "iopub.status.idle": "2022-12-07T16:37:57.315163Z", "shell.execute_reply": "2022-12-07T16:37:57.314243Z"}, "origin_pos": 5, "tab": ["pytorch"]}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Downloading ../data/hotdog.zip from http://d2l-data.s3-accelerate.amazonaws.com/hotdog.zip...\n"]}], "source": ["#@save\n", "d2l.DATA_HUB['hotdog'] = (d2l.DATA_URL + 'hotdog.zip',\n", "                         'fba480ffa8aa7e0febbb511d181409f899b9baa5')\n", "\n", "data_dir = d2l.download_extract('hotdog')"]}, {"cell_type": "markdown", "id": "c0f79cc3", "metadata": {"origin_pos": 6}, "source": ["我们创建两个实例来分别读取训练和测试数据集中的所有图像文件。\n"]}, {"cell_type": "code", "execution_count": 3, "id": "fadf7f61", "metadata": {"execution": {"iopub.execute_input": "2022-12-07T16:37:57.320559Z", "iopub.status.busy": "2022-12-07T16:37:57.320091Z", "iopub.status.idle": "2022-12-07T16:37:57.332319Z", "shell.execute_reply": "2022-12-07T16:37:57.331564Z"}, "origin_pos": 8, "tab": ["pytorch"]}, "outputs": [], "source": ["train_imgs = torchvision.datasets.ImageFolder(os.path.join(data_dir, 'train'))\n", "test_imgs = torchvision.datasets.ImageFolder(os.path.join(data_dir, 'test'))"]}, {"cell_type": "markdown", "id": "5e3dbe86", "metadata": {"origin_pos": 10}, "source": ["下面显示了前8个正类样本图片和最后8张负类样本图片。正如所看到的，[**图像的大小和纵横比各有不同**]。\n"]}, {"cell_type": "code", "execution_count": 4, "id": "9fd1ad03", "metadata": {"execution": {"iopub.execute_input": "2022-12-07T16:37:57.337195Z", "iopub.status.busy": "2022-12-07T16:37:57.336741Z", "iopub.status.idle": "2022-12-07T16:37:58.109659Z", "shell.execute_reply": "2022-12-07T16:37:58.108824Z"}, "origin_pos": 11, "tab": ["pytorch"]}, "outputs": [{"data": {"image/png": "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********************************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\n", "text/plain": ["<Figure size 1120x280 with 16 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["hotdogs = [train_imgs[i][0] for i in range(8)]\n", "not_hotdogs = [train_imgs[-i - 1][0] for i in range(8)]\n", "d2l.show_images(hotdogs + not_hotdogs, 2, 8, scale=1.4);"]}, {"cell_type": "markdown", "id": "ae506fa5", "metadata": {"origin_pos": 12}, "source": ["在训练期间，我们首先从图像中裁切随机大小和随机长宽比的区域，然后将该区域缩放为$224 \\times 224$输入图像。\n", "在测试过程中，我们将图像的高度和宽度都缩放到256像素，然后裁剪中央$224 \\times 224$区域作为输入。\n", "此外，对于RGB（红、绿和蓝）颜色通道，我们分别*标准化*每个通道。\n", "具体而言，该通道的每个值减去该通道的平均值，然后将结果除以该通道的标准差。\n", "\n", "[~~数据增广~~]\n"]}, {"cell_type": "code", "execution_count": 5, "id": "e6610d49", "metadata": {"execution": {"iopub.execute_input": "2022-12-07T16:37:58.123563Z", "iopub.status.busy": "2022-12-07T16:37:58.122794Z", "iopub.status.idle": "2022-12-07T16:37:58.129504Z", "shell.execute_reply": "2022-12-07T16:37:58.128693Z"}, "origin_pos": 14, "tab": ["pytorch"]}, "outputs": [], "source": ["# 使用RGB通道的均值和标准差，以标准化每个通道\n", "normalize = torchvision.transforms.Normalize(\n", "    [0.485, 0.456, 0.406], [0.229, 0.224, 0.225])\n", "\n", "train_augs = torchvision.transforms.Compose([\n", "    torchvision.transforms.RandomResizedCrop(224),\n", "    torchvision.transforms.RandomHorizontalFlip(),\n", "    torchvision.transforms.<PERSON><PERSON><PERSON><PERSON>(),\n", "    normalize])\n", "\n", "test_augs = torchvision.transforms.Compose([\n", "    torchvision.transforms.Resize([256, 256]),\n", "    torchvision.transforms.CenterCrop(224),\n", "    torchvision.transforms.<PERSON><PERSON><PERSON><PERSON>(),\n", "    normalize])"]}, {"cell_type": "markdown", "id": "1be5b592", "metadata": {"origin_pos": 16}, "source": ["### [**定义和初始化模型**]\n", "\n", "我们使用在ImageNet数据集上预训练的ResNet-18作为源模型。\n", "在这里，我们指定`pretrained=True`以自动下载预训练的模型参数。\n", "如果首次使用此模型，则需要连接互联网才能下载。\n"]}, {"cell_type": "code", "execution_count": 6, "id": "63afca77", "metadata": {"execution": {"iopub.execute_input": "2022-12-07T16:37:58.133958Z", "iopub.status.busy": "2022-12-07T16:37:58.133274Z", "iopub.status.idle": "2022-12-07T16:37:58.407434Z", "shell.execute_reply": "2022-12-07T16:37:58.406531Z"}, "origin_pos": 18, "tab": ["pytorch"]}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["/home/<USER>/miniconda3/envs/d2l-zh-release-1/lib/python3.9/site-packages/torchvision/models/_utils.py:208: UserWarning: The parameter 'pretrained' is deprecated since 0.13 and will be removed in 0.15, please use 'weights' instead.\n", "  warnings.warn(\n", "/home/<USER>/miniconda3/envs/d2l-zh-release-1/lib/python3.9/site-packages/torchvision/models/_utils.py:223: UserWarning: Arguments other than a weight enum or `None` for 'weights' are deprecated since 0.13 and will be removed in 0.15. The current behavior is equivalent to passing `weights=ResNet18_Weights.IMAGENET1K_V1`. You can also use `weights=ResNet18_Weights.DEFAULT` to get the most up-to-date weights.\n", "  warnings.warn(msg)\n"]}], "source": ["pretrained_net = torchvision.models.resnet18(pretrained=True)"]}, {"cell_type": "markdown", "id": "31546d8d", "metadata": {"origin_pos": 21, "tab": ["pytorch"]}, "source": ["预训练的源模型实例包含许多特征层和一个输出层`fc`。\n", "此划分的主要目的是促进对除输出层以外所有层的模型参数进行微调。\n", "下面给出了源模型的成员变量`fc`。\n"]}, {"cell_type": "code", "execution_count": 7, "id": "82bb9025", "metadata": {"execution": {"iopub.execute_input": "2022-12-07T16:37:58.412929Z", "iopub.status.busy": "2022-12-07T16:37:58.412442Z", "iopub.status.idle": "2022-12-07T16:37:58.418165Z", "shell.execute_reply": "2022-12-07T16:37:58.417415Z"}, "origin_pos": 24, "tab": ["pytorch"]}, "outputs": [{"data": {"text/plain": ["Linear(in_features=512, out_features=1000, bias=True)"]}, "execution_count": 7, "metadata": {}, "output_type": "execute_result"}], "source": ["pretrained_net.fc"]}, {"cell_type": "markdown", "id": "585783fb", "metadata": {"origin_pos": 25}, "source": ["在ResNet的全局平均汇聚层后，全连接层转换为ImageNet数据集的1000个类输出。\n", "之后，我们构建一个新的神经网络作为目标模型。\n", "它的定义方式与预训练源模型的定义方式相同，只是最终层中的输出数量被设置为目标数据集中的类数（而不是1000个）。\n", "\n", "在下面的代码中，目标模型`finetune_net`中成员变量`features`的参数被初始化为源模型相应层的模型参数。\n", "由于模型参数是在ImageNet数据集上预训练的，并且足够好，因此通常只需要较小的学习率即可微调这些参数。\n", "\n", "成员变量`output`的参数是随机初始化的，通常需要更高的学习率才能从头开始训练。\n", "假设`Trainer`实例中的学习率为$\\eta$，我们将成员变量`output`中参数的学习率设置为$10\\eta$。\n"]}, {"cell_type": "code", "execution_count": 8, "id": "43500069", "metadata": {"execution": {"iopub.execute_input": "2022-12-07T16:37:58.422637Z", "iopub.status.busy": "2022-12-07T16:37:58.422074Z", "iopub.status.idle": "2022-12-07T16:37:58.669492Z", "shell.execute_reply": "2022-12-07T16:37:58.668626Z"}, "origin_pos": 27, "tab": ["pytorch"]}, "outputs": [], "source": ["finetune_net = torchvision.models.resnet18(pretrained=True)\n", "finetune_net.fc = nn.Linear(finetune_net.fc.in_features, 2)\n", "nn.init.xavier_uniform_(finetune_net.fc.weight);"]}, {"cell_type": "markdown", "id": "49531348", "metadata": {"origin_pos": 29}, "source": ["### [**微调模型**]\n", "\n", "首先，我们定义了一个训练函数`train_fine_tuning`，该函数使用微调，因此可以多次调用。\n"]}, {"cell_type": "code", "execution_count": 9, "id": "c6b3e101", "metadata": {"execution": {"iopub.execute_input": "2022-12-07T16:37:58.674339Z", "iopub.status.busy": "2022-12-07T16:37:58.673792Z", "iopub.status.idle": "2022-12-07T16:37:58.681528Z", "shell.execute_reply": "2022-12-07T16:37:58.680703Z"}, "origin_pos": 31, "tab": ["pytorch"]}, "outputs": [], "source": ["# 如果param_group=True，输出层中的模型参数将使用十倍的学习率\n", "def train_fine_tuning(net, learning_rate, batch_size=128, num_epochs=5,\n", "                      param_group=True):\n", "    train_iter = torch.utils.data.DataLoader(torchvision.datasets.ImageFolder(\n", "        os.path.join(data_dir, 'train'), transform=train_augs),\n", "        batch_size=batch_size, shuffle=True)\n", "    test_iter = torch.utils.data.DataLoader(torchvision.datasets.ImageFolder(\n", "        os.path.join(data_dir, 'test'), transform=test_augs),\n", "        batch_size=batch_size)\n", "    devices = d2l.try_all_gpus()\n", "    loss = nn.CrossEntropyLoss(reduction=\"none\")\n", "    if param_group:\n", "        params_1x = [param for name, param in net.named_parameters()\n", "             if name not in [\"fc.weight\", \"fc.bias\"]]\n", "        trainer = torch.optim.SGD([{'params': params_1x},\n", "                                   {'params': net.fc.parameters(),\n", "                                    'lr': learning_rate * 10}],\n", "                                lr=learning_rate, weight_decay=0.001)\n", "    else:\n", "        trainer = torch.optim.SGD(net.parameters(), lr=learning_rate,\n", "                                  weight_decay=0.001)\n", "    d2l.train_ch13(net, train_iter, test_iter, loss, trainer, num_epochs,\n", "                   devices)"]}, {"cell_type": "markdown", "id": "c0944654", "metadata": {"origin_pos": 33}, "source": ["我们[**使用较小的学习率**]，通过*微调*预训练获得的模型参数。\n"]}, {"cell_type": "code", "execution_count": 10, "id": "4ca4ef19", "metadata": {"execution": {"iopub.execute_input": "2022-12-07T16:37:58.685559Z", "iopub.status.busy": "2022-12-07T16:37:58.684806Z", "iopub.status.idle": "2022-12-07T16:39:23.696907Z", "shell.execute_reply": "2022-12-07T16:39:23.696074Z"}, "origin_pos": 35, "tab": ["pytorch"]}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["loss 0.177, train acc 0.932, test acc 0.943\n", "968.4 examples/sec on [device(type='cuda', index=0), device(type='cuda', index=1)]\n"]}, {"data": {"image/svg+xml": ["<?xml version=\"1.0\" encoding=\"utf-8\" standalone=\"no\"?>\n", "<!DOCTYPE svg PUBLIC \"-//W3C//DTD SVG 1.1//EN\"\n", "  \"http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd\">\n", "<svg xmlns:xlink=\"http://www.w3.org/1999/xlink\" width=\"235.784375pt\" height=\"187.155469pt\" viewBox=\"0 0 235.**********.155469\" xmlns=\"http://www.w3.org/2000/svg\" version=\"1.1\">\n", " <metadata>\n", "  <rdf:RDF xmlns:dc=\"http://purl.org/dc/elements/1.1/\" xmlns:cc=\"http://creativecommons.org/ns#\" xmlns:rdf=\"http://www.w3.org/1999/02/22-rdf-syntax-ns#\">\n", "   <cc:Work>\n", "    <dc:type rdf:resource=\"http://purl.org/dc/dcmitype/StillImage\"/>\n", "    <dc:date>2022-12-07T16:39:23.651664</dc:date>\n", "    <dc:format>image/svg+xml</dc:format>\n", "    <dc:creator>\n", "     <cc:Agent>\n", "      <dc:title>Matplotlib v3.5.1, https://matplotlib.org/</dc:title>\n", "     </cc:Agent>\n", "    </dc:creator>\n", "   </cc:Work>\n", "  </rdf:RDF>\n", " </metadata>\n", " <defs>\n", "  <style type=\"text/css\">*{stroke-linejoin: round; stroke-linecap: butt}</style>\n", " </defs>\n", " <g id=\"figure_1\">\n", "  <g id=\"patch_1\">\n", "   <path d=\"M 0 187.155469 \n", "L 235.**********.155469 \n", "L 235.784375 0 \n", "L 0 0 \n", "z\n", "\" style=\"fill: #ffffff\"/>\n", "  </g>\n", "  <g id=\"axes_1\">\n", "   <g id=\"patch_2\">\n", "    <path d=\"M 30.**********.599219 \n", "L 225.**********.599219 \n", "L 225.403125 10.999219 \n", "L 30.103125 10.999219 \n", "z\n", "\" style=\"fill: #ffffff\"/>\n", "   </g>\n", "   <g id=\"matplotlib.axis_1\">\n", "    <g id=\"xtick_1\">\n", "     <g id=\"line2d_1\">\n", "      <path d=\"M 30.**********.599219 \n", "L 30.103125 10.999219 \n", "\" clip-path=\"url(#p507324c0eb)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_2\">\n", "      <defs>\n", "       <path id=\"m6c50df74bb\" d=\"M 0 0 \n", "L 0 3.5 \n", "\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </defs>\n", "      <g>\n", "       <use xlink:href=\"#m6c50df74bb\" x=\"30.103125\" y=\"149.599219\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_1\">\n", "      <!-- 1 -->\n", "      <g transform=\"translate(26.921875 164.197656)scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-31\" d=\"M 794 531 \n", "L 1825 531 \n", "L 1825 4091 \n", "L 703 3866 \n", "L 703 4441 \n", "L 1819 4666 \n", "L 2450 4666 \n", "L 2450 531 \n", "L 3481 531 \n", "L 3481 0 \n", "L 794 0 \n", "L 794 531 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-31\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_2\">\n", "     <g id=\"line2d_3\">\n", "      <path d=\"M 78.928125 149.599219 \n", "L 78.928125 10.999219 \n", "\" clip-path=\"url(#p507324c0eb)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_4\">\n", "      <g>\n", "       <use xlink:href=\"#m6c50df74bb\" x=\"78.928125\" y=\"149.599219\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_2\">\n", "      <!-- 2 -->\n", "      <g transform=\"translate(75.746875 164.197656)scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-32\" d=\"M 1228 531 \n", "L 3431 531 \n", "L 3431 0 \n", "L 469 0 \n", "L 469 531 \n", "Q 828 903 1448 1529 \n", "Q 2069 2156 2228 2338 \n", "Q 2531 2678 2651 2914 \n", "Q 2772 3150 2772 3378 \n", "Q 2772 3750 2511 3984 \n", "Q 2250 4219 1831 4219 \n", "Q 1534 4219 1204 4116 \n", "Q 875 4013 500 3803 \n", "L 500 4441 \n", "Q 881 4594 1212 4672 \n", "Q 1544 4750 1819 4750 \n", "Q 2544 4750 2975 4387 \n", "Q 3406 4025 3406 3419 \n", "Q 3406 3131 3298 2873 \n", "Q 3191 2616 2906 2266 \n", "Q 2828 2175 2409 1742 \n", "Q 1991 1309 1228 531 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-32\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_3\">\n", "     <g id=\"line2d_5\">\n", "      <path d=\"M 127.753125 149.599219 \n", "L 127.753125 10.999219 \n", "\" clip-path=\"url(#p507324c0eb)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_6\">\n", "      <g>\n", "       <use xlink:href=\"#m6c50df74bb\" x=\"127.753125\" y=\"149.599219\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_3\">\n", "      <!-- 3 -->\n", "      <g transform=\"translate(124.571875 164.197656)scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-33\" d=\"M 2597 2516 \n", "Q 3050 2419 3304 2112 \n", "Q 3559 1806 3559 1356 \n", "Q 3559 666 3084 287 \n", "Q 2609 -91 1734 -91 \n", "Q 1441 -91 1130 -33 \n", "Q 819 25 488 141 \n", "L 488 750 \n", "Q 750 597 1062 519 \n", "Q 1375 441 1716 441 \n", "Q 2309 441 2620 675 \n", "Q 2931 909 2931 1356 \n", "Q 2931 1769 2642 2001 \n", "Q 2353 2234 1838 2234 \n", "L 1294 2234 \n", "L 1294 2753 \n", "L 1863 2753 \n", "Q 2328 2753 2575 2939 \n", "Q 2822 3125 2822 3475 \n", "Q 2822 3834 2567 4026 \n", "Q 2313 4219 1838 4219 \n", "Q 1578 4219 1281 4162 \n", "Q 984 4106 628 3988 \n", "L 628 4550 \n", "Q 988 4650 1302 4700 \n", "Q 1616 4750 1894 4750 \n", "Q 2613 4750 3031 4423 \n", "Q 3450 4097 3450 3541 \n", "Q 3450 3153 3228 2886 \n", "Q 3006 2619 2597 2516 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-33\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_4\">\n", "     <g id=\"line2d_7\">\n", "      <path d=\"M 176.578125 149.599219 \n", "L 176.578125 10.999219 \n", "\" clip-path=\"url(#p507324c0eb)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_8\">\n", "      <g>\n", "       <use xlink:href=\"#m6c50df74bb\" x=\"176.578125\" y=\"149.599219\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_4\">\n", "      <!-- 4 -->\n", "      <g transform=\"translate(173.396875 164.197656)scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-34\" d=\"M 2419 4116 \n", "L 825 1625 \n", "L 2419 1625 \n", "L 2419 4116 \n", "z\n", "M 2253 4666 \n", "L 3047 4666 \n", "L 3047 1625 \n", "L 3713 1625 \n", "L 3713 1100 \n", "L 3047 1100 \n", "L 3047 0 \n", "L 2419 0 \n", "L 2419 1100 \n", "L 313 1100 \n", "L 313 1709 \n", "L 2253 4666 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-34\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_5\">\n", "     <g id=\"line2d_9\">\n", "      <path d=\"M 225.**********.599219 \n", "L 225.403125 10.999219 \n", "\" clip-path=\"url(#p507324c0eb)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_10\">\n", "      <g>\n", "       <use xlink:href=\"#m6c50df74bb\" x=\"225.403125\" y=\"149.599219\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_5\">\n", "      <!-- 5 -->\n", "      <g transform=\"translate(222.221875 164.197656)scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-35\" d=\"M 691 4666 \n", "L 3169 4666 \n", "L 3169 4134 \n", "L 1269 4134 \n", "L 1269 2991 \n", "Q 1406 3038 1543 3061 \n", "Q 1681 3084 1819 3084 \n", "Q 2600 3084 3056 2656 \n", "Q 3513 2228 3513 1497 \n", "Q 3513 744 3044 326 \n", "Q 2575 -91 1722 -91 \n", "Q 1428 -91 1123 -41 \n", "Q 819 9 494 109 \n", "L 494 744 \n", "Q 775 591 1075 516 \n", "Q 1375 441 1709 441 \n", "Q 2250 441 2565 725 \n", "Q 2881 1009 2881 1497 \n", "Q 2881 1984 2565 2268 \n", "Q 2250 2553 1709 2553 \n", "Q 1456 2553 1204 2497 \n", "Q 953 2441 691 2322 \n", "L 691 4666 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-35\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"text_6\">\n", "     <!-- epoch -->\n", "     <g transform=\"translate(112.525 177.875781)scale(0.1 -0.1)\">\n", "      <defs>\n", "       <path id=\"DejaVuSans-65\" d=\"M 3597 1894 \n", "L 3597 1613 \n", "L 953 1613 \n", "Q 991 1019 1311 708 \n", "Q 1631 397 2203 397 \n", "Q 2534 397 2845 478 \n", "Q 3156 559 3463 722 \n", "L 3463 178 \n", "Q 3153 47 2828 -22 \n", "Q 2503 -91 2169 -91 \n", "Q 1331 -91 842 396 \n", "Q 353 884 353 1716 \n", "Q 353 2575 817 3079 \n", "Q 1281 3584 2069 3584 \n", "Q 2775 3584 3186 3129 \n", "Q 3597 2675 3597 1894 \n", "z\n", "M 3022 2063 \n", "Q 3016 2534 2758 2815 \n", "Q 2500 3097 2075 3097 \n", "Q 1594 3097 1305 2825 \n", "Q 1016 2553 972 2059 \n", "L 3022 2063 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-70\" d=\"M 1159 525 \n", "L 1159 -1331 \n", "L 581 -1331 \n", "L 581 3500 \n", "L 1159 3500 \n", "L 1159 2969 \n", "Q 1341 3281 1617 3432 \n", "Q 1894 3584 2278 3584 \n", "Q 2916 3584 3314 3078 \n", "Q 3713 2572 3713 1747 \n", "Q 3713 922 3314 415 \n", "Q 2916 -91 2278 -91 \n", "Q 1894 -91 1617 61 \n", "Q 1341 213 1159 525 \n", "z\n", "M 3116 1747 \n", "Q 3116 2381 2855 2742 \n", "Q 2594 3103 2138 3103 \n", "Q 1681 3103 1420 2742 \n", "Q 1159 2381 1159 1747 \n", "Q 1159 1113 1420 752 \n", "Q 1681 391 2138 391 \n", "Q 2594 391 2855 752 \n", "Q 3116 1113 3116 1747 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-6f\" d=\"M 1959 3097 \n", "Q 1497 3097 1228 2736 \n", "Q 959 2375 959 1747 \n", "Q 959 1119 1226 758 \n", "Q 1494 397 1959 397 \n", "Q 2419 397 2687 759 \n", "Q 2956 1122 2956 1747 \n", "Q 2956 2369 2687 2733 \n", "Q 2419 3097 1959 3097 \n", "z\n", "M 1959 3584 \n", "Q 2709 3584 3137 3096 \n", "Q 3566 2609 3566 1747 \n", "Q 3566 888 3137 398 \n", "Q 2709 -91 1959 -91 \n", "Q 1206 -91 779 398 \n", "Q 353 888 353 1747 \n", "Q 353 2609 779 3096 \n", "Q 1206 3584 1959 3584 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-63\" d=\"M 3122 3366 \n", "L 3122 2828 \n", "Q 2878 2963 2633 3030 \n", "Q 2388 3097 2138 3097 \n", "Q 1578 3097 1268 2742 \n", "Q 959 2388 959 1747 \n", "Q 959 1106 1268 751 \n", "Q 1578 397 2138 397 \n", "Q 2388 397 2633 464 \n", "Q 2878 531 3122 666 \n", "L 3122 134 \n", "Q 2881 22 2623 -34 \n", "Q 2366 -91 2075 -91 \n", "Q 1284 -91 818 406 \n", "Q 353 903 353 1747 \n", "Q 353 2603 823 3093 \n", "Q 1294 3584 2113 3584 \n", "Q 2378 3584 2631 3529 \n", "Q 2884 3475 3122 3366 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-68\" d=\"M 3513 2113 \n", "L 3513 0 \n", "L 2938 0 \n", "L 2938 2094 \n", "Q 2938 2591 2744 2837 \n", "Q 2550 3084 2163 3084 \n", "Q 1697 3084 1428 2787 \n", "Q 1159 2491 1159 1978 \n", "L 1159 0 \n", "L 581 0 \n", "L 581 4863 \n", "L 1159 4863 \n", "L 1159 2956 \n", "Q 1366 3272 1645 3428 \n", "Q 1925 3584 2291 3584 \n", "Q 2894 3584 3203 3211 \n", "Q 3513 2838 3513 2113 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "      </defs>\n", "      <use xlink:href=\"#DejaVuSans-65\"/>\n", "      <use xlink:href=\"#DejaVuSans-70\" x=\"61.523438\"/>\n", "      <use xlink:href=\"#DejaVuSans-6f\" x=\"125\"/>\n", "      <use xlink:href=\"#DejaVuSans-63\" x=\"186.181641\"/>\n", "      <use xlink:href=\"#DejaVuSans-68\" x=\"241.162109\"/>\n", "     </g>\n", "    </g>\n", "   </g>\n", "   <g id=\"matplotlib.axis_2\">\n", "    <g id=\"ytick_1\">\n", "     <g id=\"line2d_11\">\n", "      <path d=\"M 30.**********.599219 \n", "L 225.**********.599219 \n", "\" clip-path=\"url(#p507324c0eb)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_12\">\n", "      <defs>\n", "       <path id=\"m45904d9e40\" d=\"M 0 0 \n", "L -3.5 0 \n", "\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </defs>\n", "      <g>\n", "       <use xlink:href=\"#m45904d9e40\" x=\"30.103125\" y=\"149.599219\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_7\">\n", "      <!-- 0.0 -->\n", "      <g transform=\"translate(7.2 153.398438)scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-30\" d=\"M 2034 4250 \n", "Q 1547 4250 1301 3770 \n", "Q 1056 3291 1056 2328 \n", "Q 1056 1369 1301 889 \n", "Q 1547 409 2034 409 \n", "Q 2525 409 2770 889 \n", "Q 3016 1369 3016 2328 \n", "Q 3016 3291 2770 3770 \n", "Q 2525 4250 2034 4250 \n", "z\n", "M 2034 4750 \n", "Q 2819 4750 3233 4129 \n", "Q 3647 3509 3647 2328 \n", "Q 3647 1150 3233 529 \n", "Q 2819 -91 2034 -91 \n", "Q 1250 -91 836 529 \n", "Q 422 1150 422 2328 \n", "Q 422 3509 836 4129 \n", "Q 1250 4750 2034 4750 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "        <path id=\"DejaVuSans-2e\" d=\"M 684 794 \n", "L 1344 794 \n", "L 1344 0 \n", "L 684 0 \n", "L 684 794 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"95.410156\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_2\">\n", "     <g id=\"line2d_13\">\n", "      <path d=\"M 30.103125 121.879219 \n", "L 225.403125 121.879219 \n", "\" clip-path=\"url(#p507324c0eb)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_14\">\n", "      <g>\n", "       <use xlink:href=\"#m45904d9e40\" x=\"30.103125\" y=\"121.879219\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_8\">\n", "      <!-- 0.2 -->\n", "      <g transform=\"translate(7.2 125.678438)scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-32\" x=\"95.410156\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_3\">\n", "     <g id=\"line2d_15\">\n", "      <path d=\"M 30.103125 94.159219 \n", "L 225.403125 94.159219 \n", "\" clip-path=\"url(#p507324c0eb)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_16\">\n", "      <g>\n", "       <use xlink:href=\"#m45904d9e40\" x=\"30.103125\" y=\"94.159219\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_9\">\n", "      <!-- 0.4 -->\n", "      <g transform=\"translate(7.2 97.958438)scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-34\" x=\"95.410156\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_4\">\n", "     <g id=\"line2d_17\">\n", "      <path d=\"M 30.103125 66.439219 \n", "L 225.403125 66.439219 \n", "\" clip-path=\"url(#p507324c0eb)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_18\">\n", "      <g>\n", "       <use xlink:href=\"#m45904d9e40\" x=\"30.103125\" y=\"66.439219\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_10\">\n", "      <!-- 0.6 -->\n", "      <g transform=\"translate(7.2 70.238437)scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-36\" d=\"M 2113 2584 \n", "Q 1688 2584 1439 2293 \n", "Q 1191 2003 1191 1497 \n", "Q 1191 994 1439 701 \n", "Q 1688 409 2113 409 \n", "Q 2538 409 2786 701 \n", "Q 3034 994 3034 1497 \n", "Q 3034 2003 2786 2293 \n", "Q 2538 2584 2113 2584 \n", "z\n", "M 3366 4563 \n", "L 3366 3988 \n", "Q 3128 4100 2886 4159 \n", "Q 2644 4219 2406 4219 \n", "Q 1781 4219 1451 3797 \n", "Q 1122 3375 1075 2522 \n", "Q 1259 2794 1537 2939 \n", "Q 1816 3084 2150 3084 \n", "Q 2853 3084 3261 2657 \n", "Q 3669 2231 3669 1497 \n", "Q 3669 778 3244 343 \n", "Q 2819 -91 2113 -91 \n", "Q 1303 -91 875 529 \n", "Q 447 1150 447 2328 \n", "Q 447 3434 972 4092 \n", "Q 1497 4750 2381 4750 \n", "Q 2619 4750 2861 4703 \n", "Q 3103 4656 3366 4563 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-36\" x=\"95.410156\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_5\">\n", "     <g id=\"line2d_19\">\n", "      <path d=\"M 30.103125 38.719219 \n", "L 225.403125 38.719219 \n", "\" clip-path=\"url(#p507324c0eb)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_20\">\n", "      <g>\n", "       <use xlink:href=\"#m45904d9e40\" x=\"30.103125\" y=\"38.719219\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_11\">\n", "      <!-- 0.8 -->\n", "      <g transform=\"translate(7.2 42.518438)scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-38\" d=\"M 2034 2216 \n", "Q 1584 2216 1326 1975 \n", "Q 1069 1734 1069 1313 \n", "Q 1069 891 1326 650 \n", "Q 1584 409 2034 409 \n", "Q 2484 409 2743 651 \n", "Q 3003 894 3003 1313 \n", "Q 3003 1734 2745 1975 \n", "Q 2488 2216 2034 2216 \n", "z\n", "M 1403 2484 \n", "Q 997 2584 770 2862 \n", "Q 544 3141 544 3541 \n", "Q 544 4100 942 4425 \n", "Q 1341 4750 2034 4750 \n", "Q 2731 4750 3128 4425 \n", "Q 3525 4100 3525 3541 \n", "Q 3525 3141 3298 2862 \n", "Q 3072 2584 2669 2484 \n", "Q 3125 2378 3379 2068 \n", "Q 3634 1759 3634 1313 \n", "Q 3634 634 3220 271 \n", "Q 2806 -91 2034 -91 \n", "Q 1263 -91 848 271 \n", "Q 434 634 434 1313 \n", "Q 434 1759 690 2068 \n", "Q 947 2378 1403 2484 \n", "z\n", "M 1172 3481 \n", "Q 1172 3119 1398 2916 \n", "Q 1625 2713 2034 2713 \n", "Q 2441 2713 2670 2916 \n", "Q 2900 3119 2900 3481 \n", "Q 2900 3844 2670 4047 \n", "Q 2441 4250 2034 4250 \n", "Q 1625 4250 1398 4047 \n", "Q 1172 3844 1172 3481 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-38\" x=\"95.410156\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_6\">\n", "     <g id=\"line2d_21\">\n", "      <path d=\"M 30.103125 10.999219 \n", "L 225.403125 10.999219 \n", "\" clip-path=\"url(#p507324c0eb)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_22\">\n", "      <g>\n", "       <use xlink:href=\"#m45904d9e40\" x=\"30.103125\" y=\"10.999219\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_12\">\n", "      <!-- 1.0 -->\n", "      <g transform=\"translate(7.2 14.798438)scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-31\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"95.410156\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "   </g>\n", "   <g id=\"line2d_23\">\n", "    <path d=\"M 36.409605 -1 \n", "L 39.257812 108.248898 \n", "L 48.4125 115.855381 \n", "L 57.567188 93.718463 \n", "L 66.721875 94.648455 \n", "L 75.876563 95.561305 \n", "L 78.928125 95.866692 \n", "L 88.082813 105.666649 \n", "L 97.2375 113.397499 \n", "L 106.392188 112.087763 \n", "L 115.546875 114.976015 \n", "L 124.701563 115.874735 \n", "L 127.753125 115.516891 \n", "L 136.907813 110.034292 \n", "L 146.0625 110.465383 \n", "L 155.217187 114.19028 \n", "L 164.371875 114.328185 \n", "L 173.526563 115.185126 \n", "L 176.578125 113.40631 \n", "L 185.732812 126.59211 \n", "L 194.8875 126.671427 \n", "L 204.042188 124.103753 \n", "L 213.196875 125.38178 \n", "L 222.351562 125.746052 \n", "L 225.403125 125.031261 \n", "\" clip-path=\"url(#p507324c0eb)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_24\">\n", "    <path d=\"M -1 84.477999 \n", "L -0.4125 84.269531 \n", "L 8.742188 77.892969 \n", "L 17.896875 65.771484 \n", "L 27.051562 58.426406 \n", "L 30.103125 57.083719 \n", "L 39.257812 25.075781 \n", "L 48.4125 22.549219 \n", "L 57.567188 27.602344 \n", "L 66.721875 27.421875 \n", "L 75.876563 26.952656 \n", "L 78.928125 27.007519 \n", "L 88.082813 25.075781 \n", "L 97.2375 23.632031 \n", "L 106.392188 23.752344 \n", "L 115.546875 23.180859 \n", "L 124.701563 22.549219 \n", "L 127.753125 22.572319 \n", "L 136.907813 22.549219 \n", "L 146.0625 23.632031 \n", "L 155.217187 23.511719 \n", "L 164.371875 23.451563 \n", "L 173.526563 23.054531 \n", "L 176.578125 23.542519 \n", "L 185.732812 19.300781 \n", "L 194.8875 19.842188 \n", "L 204.042188 21.225781 \n", "L 213.196875 20.203125 \n", "L 222.351562 20.094844 \n", "L 225.403125 20.354719 \n", "\" clip-path=\"url(#p507324c0eb)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #bf00bf; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_25\">\n", "    <path d=\"M 30.103125 23.473219 \n", "L 78.928125 31.442719 \n", "L 127.753125 25.378969 \n", "L 176.578125 22.953469 \n", "L 225.403125 18.968719 \n", "\" clip-path=\"url(#p507324c0eb)\" style=\"fill: none; stroke-dasharray: 9.6,2.4,1.5,2.4; stroke-dashoffset: 0; stroke: #008000; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"patch_3\">\n", "    <path d=\"M 30.**********.599219 \n", "L 30.103125 10.999219 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_4\">\n", "    <path d=\"M 225.**********.599219 \n", "L 225.403125 10.999219 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_5\">\n", "    <path d=\"M 30.**********.599219 \n", "L 225.**********.599219 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_6\">\n", "    <path d=\"M 30.103125 10.999219 \n", "L 225.403125 10.999219 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"legend_1\">\n", "    <g id=\"patch_7\">\n", "     <path d=\"M 140.634375 103.816406 \n", "L 218.403125 103.816406 \n", "Q 220.403125 103.816406 220.403125 101.816406 \n", "L 220.403125 58.782031 \n", "Q 220.403125 56.782031 218.403125 56.782031 \n", "L 140.634375 56.782031 \n", "Q 138.634375 56.782031 138.634375 58.782031 \n", "L 138.634375 101.816406 \n", "Q 138.634375 103.816406 140.634375 103.816406 \n", "z\n", "\" style=\"fill: #ffffff; opacity: 0.8; stroke: #cccccc; stroke-linejoin: miter\"/>\n", "    </g>\n", "    <g id=\"line2d_26\">\n", "     <path d=\"M 142.634375 64.880469 \n", "L 152.634375 64.880469 \n", "L 162.634375 64.880469 \n", "\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "    </g>\n", "    <g id=\"text_13\">\n", "     <!-- train loss -->\n", "     <g transform=\"translate(170.634375 68.380469)scale(0.1 -0.1)\">\n", "      <defs>\n", "       <path id=\"DejaVuSans-74\" d=\"M 1172 4494 \n", "L 1172 3500 \n", "L 2356 3500 \n", "L 2356 3053 \n", "L 1172 3053 \n", "L 1172 1153 \n", "Q 1172 725 1289 603 \n", "Q 1406 481 1766 481 \n", "L 2356 481 \n", "L 2356 0 \n", "L 1766 0 \n", "Q 1100 0 847 248 \n", "Q 594 497 594 1153 \n", "L 594 3053 \n", "L 172 3053 \n", "L 172 3500 \n", "L 594 3500 \n", "L 594 4494 \n", "L 1172 4494 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-72\" d=\"M 2631 2963 \n", "Q 2534 3019 2420 3045 \n", "Q 2306 3072 2169 3072 \n", "Q 1681 3072 1420 2755 \n", "Q 1159 2438 1159 1844 \n", "L 1159 0 \n", "L 581 0 \n", "L 581 3500 \n", "L 1159 3500 \n", "L 1159 2956 \n", "Q 1341 3275 1631 3429 \n", "Q 1922 3584 2338 3584 \n", "Q 2397 3584 2469 3576 \n", "Q 2541 3569 2628 3553 \n", "L 2631 2963 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-61\" d=\"M 2194 1759 \n", "Q 1497 1759 1228 1600 \n", "Q 959 1441 959 1056 \n", "Q 959 750 1161 570 \n", "Q 1363 391 1709 391 \n", "Q 2188 391 2477 730 \n", "Q 2766 1069 2766 1631 \n", "L 2766 1759 \n", "L 2194 1759 \n", "z\n", "M 3341 1997 \n", "L 3341 0 \n", "L 2766 0 \n", "L 2766 531 \n", "Q 2569 213 2275 61 \n", "Q 1981 -91 1556 -91 \n", "Q 1019 -91 701 211 \n", "Q 384 513 384 1019 \n", "Q 384 1609 779 1909 \n", "Q 1175 2209 1959 2209 \n", "L 2766 2209 \n", "L 2766 2266 \n", "Q 2766 2663 2505 2880 \n", "Q 2244 3097 1772 3097 \n", "Q 1472 3097 1187 3025 \n", "Q 903 2953 641 2809 \n", "L 641 3341 \n", "Q 956 3463 1253 3523 \n", "Q 1550 3584 1831 3584 \n", "Q 2591 3584 2966 3190 \n", "Q 3341 2797 3341 1997 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-69\" d=\"M 603 3500 \n", "L 1178 3500 \n", "L 1178 0 \n", "L 603 0 \n", "L 603 3500 \n", "z\n", "M 603 4863 \n", "L 1178 4863 \n", "L 1178 4134 \n", "L 603 4134 \n", "L 603 4863 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-6e\" d=\"M 3513 2113 \n", "L 3513 0 \n", "L 2938 0 \n", "L 2938 2094 \n", "Q 2938 2591 2744 2837 \n", "Q 2550 3084 2163 3084 \n", "Q 1697 3084 1428 2787 \n", "Q 1159 2491 1159 1978 \n", "L 1159 0 \n", "L 581 0 \n", "L 581 3500 \n", "L 1159 3500 \n", "L 1159 2956 \n", "Q 1366 3272 1645 3428 \n", "Q 1925 3584 2291 3584 \n", "Q 2894 3584 3203 3211 \n", "Q 3513 2838 3513 2113 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-20\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-6c\" d=\"M 603 4863 \n", "L 1178 4863 \n", "L 1178 0 \n", "L 603 0 \n", "L 603 4863 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-73\" d=\"M 2834 3397 \n", "L 2834 2853 \n", "Q 2591 2978 2328 3040 \n", "Q 2066 3103 1784 3103 \n", "Q 1356 3103 1142 2972 \n", "Q 928 2841 928 2578 \n", "Q 928 2378 1081 2264 \n", "Q 1234 2150 1697 2047 \n", "L 1894 2003 \n", "Q 2506 1872 2764 1633 \n", "Q 3022 1394 3022 966 \n", "Q 3022 478 2636 193 \n", "Q 2250 -91 1575 -91 \n", "Q 1294 -91 989 -36 \n", "Q 684 19 347 128 \n", "L 347 722 \n", "Q 666 556 975 473 \n", "Q 1284 391 1588 391 \n", "Q 1994 391 2212 530 \n", "Q 2431 669 2431 922 \n", "Q 2431 1156 2273 1281 \n", "Q 2116 1406 1581 1522 \n", "L 1381 1569 \n", "Q 847 1681 609 1914 \n", "Q 372 2147 372 2553 \n", "Q 372 3047 722 3315 \n", "Q 1072 3584 1716 3584 \n", "Q 2034 3584 2315 3537 \n", "Q 2597 3491 2834 3397 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "      </defs>\n", "      <use xlink:href=\"#DejaVuSans-74\"/>\n", "      <use xlink:href=\"#DejaVuSans-72\" x=\"39.208984\"/>\n", "      <use xlink:href=\"#DejaVuSans-61\" x=\"80.322266\"/>\n", "      <use xlink:href=\"#DejaVuSans-69\" x=\"141.601562\"/>\n", "      <use xlink:href=\"#DejaVuSans-6e\" x=\"169.384766\"/>\n", "      <use xlink:href=\"#DejaVuSans-20\" x=\"232.763672\"/>\n", "      <use xlink:href=\"#DejaVuSans-6c\" x=\"264.550781\"/>\n", "      <use xlink:href=\"#DejaVuSans-6f\" x=\"292.333984\"/>\n", "      <use xlink:href=\"#DejaVuSans-73\" x=\"353.515625\"/>\n", "      <use xlink:href=\"#DejaVuSans-73\" x=\"405.615234\"/>\n", "     </g>\n", "    </g>\n", "    <g id=\"line2d_27\">\n", "     <path d=\"M 142.634375 79.558594 \n", "L 152.634375 79.558594 \n", "L 162.634375 79.558594 \n", "\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #bf00bf; stroke-width: 1.5\"/>\n", "    </g>\n", "    <g id=\"text_14\">\n", "     <!-- train acc -->\n", "     <g transform=\"translate(170.634375 83.058594)scale(0.1 -0.1)\">\n", "      <use xlink:href=\"#DejaVuSans-74\"/>\n", "      <use xlink:href=\"#DejaVuSans-72\" x=\"39.208984\"/>\n", "      <use xlink:href=\"#DejaVuSans-61\" x=\"80.322266\"/>\n", "      <use xlink:href=\"#DejaVuSans-69\" x=\"141.601562\"/>\n", "      <use xlink:href=\"#DejaVuSans-6e\" x=\"169.384766\"/>\n", "      <use xlink:href=\"#DejaVuSans-20\" x=\"232.763672\"/>\n", "      <use xlink:href=\"#DejaVuSans-61\" x=\"264.550781\"/>\n", "      <use xlink:href=\"#DejaVuSans-63\" x=\"325.830078\"/>\n", "      <use xlink:href=\"#DejaVuSans-63\" x=\"380.810547\"/>\n", "     </g>\n", "    </g>\n", "    <g id=\"line2d_28\">\n", "     <path d=\"M 142.634375 94.236719 \n", "L 152.634375 94.236719 \n", "L 162.634375 94.236719 \n", "\" style=\"fill: none; stroke-dasharray: 9.6,2.4,1.5,2.4; stroke-dashoffset: 0; stroke: #008000; stroke-width: 1.5\"/>\n", "    </g>\n", "    <g id=\"text_15\">\n", "     <!-- test acc -->\n", "     <g transform=\"translate(170.634375 97.736719)scale(0.1 -0.1)\">\n", "      <use xlink:href=\"#DejaVuSans-74\"/>\n", "      <use xlink:href=\"#DejaVuSans-65\" x=\"39.208984\"/>\n", "      <use xlink:href=\"#DejaVuSans-73\" x=\"100.732422\"/>\n", "      <use xlink:href=\"#DejaVuSans-74\" x=\"152.832031\"/>\n", "      <use xlink:href=\"#DejaVuSans-20\" x=\"192.041016\"/>\n", "      <use xlink:href=\"#DejaVuSans-61\" x=\"223.828125\"/>\n", "      <use xlink:href=\"#DejaVuSans-63\" x=\"285.107422\"/>\n", "      <use xlink:href=\"#DejaVuSans-63\" x=\"340.087891\"/>\n", "     </g>\n", "    </g>\n", "   </g>\n", "  </g>\n", " </g>\n", " <defs>\n", "  <clipPath id=\"p507324c0eb\">\n", "   <rect x=\"30.103125\" y=\"10.999219\" width=\"195.3\" height=\"138.6\"/>\n", "  </clipPath>\n", " </defs>\n", "</svg>\n"], "text/plain": ["<Figure size 350x250 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["train_fine_tuning(finetune_net, 5e-5)"]}, {"cell_type": "markdown", "id": "3a04d31e", "metadata": {"origin_pos": 36}, "source": ["[**为了进行比较，**]我们定义了一个相同的模型，但是将其(**所有模型参数初始化为随机值**)。\n", "由于整个模型需要从头开始训练，因此我们需要使用更大的学习率。\n"]}, {"cell_type": "code", "execution_count": 11, "id": "a72ea239", "metadata": {"execution": {"iopub.execute_input": "2022-12-07T16:39:23.702375Z", "iopub.status.busy": "2022-12-07T16:39:23.701792Z", "iopub.status.idle": "2022-12-07T16:40:45.446588Z", "shell.execute_reply": "2022-12-07T16:40:45.445443Z"}, "origin_pos": 38, "tab": ["pytorch"]}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["loss 0.413, train acc 0.815, test acc 0.859\n", "1627.1 examples/sec on [device(type='cuda', index=0), device(type='cuda', index=1)]\n"]}, {"data": {"image/svg+xml": ["<?xml version=\"1.0\" encoding=\"utf-8\" standalone=\"no\"?>\n", "<!DOCTYPE svg PUBLIC \"-//W3C//DTD SVG 1.1//EN\"\n", "  \"http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd\">\n", "<svg xmlns:xlink=\"http://www.w3.org/1999/xlink\" width=\"235.784375pt\" height=\"187.155469pt\" viewBox=\"0 0 235.**********.155469\" xmlns=\"http://www.w3.org/2000/svg\" version=\"1.1\">\n", " <metadata>\n", "  <rdf:RDF xmlns:dc=\"http://purl.org/dc/elements/1.1/\" xmlns:cc=\"http://creativecommons.org/ns#\" xmlns:rdf=\"http://www.w3.org/1999/02/22-rdf-syntax-ns#\">\n", "   <cc:Work>\n", "    <dc:type rdf:resource=\"http://purl.org/dc/dcmitype/StillImage\"/>\n", "    <dc:date>2022-12-07T16:40:45.399431</dc:date>\n", "    <dc:format>image/svg+xml</dc:format>\n", "    <dc:creator>\n", "     <cc:Agent>\n", "      <dc:title>Matplotlib v3.5.1, https://matplotlib.org/</dc:title>\n", "     </cc:Agent>\n", "    </dc:creator>\n", "   </cc:Work>\n", "  </rdf:RDF>\n", " </metadata>\n", " <defs>\n", "  <style type=\"text/css\">*{stroke-linejoin: round; stroke-linecap: butt}</style>\n", " </defs>\n", " <g id=\"figure_1\">\n", "  <g id=\"patch_1\">\n", "   <path d=\"M 0 187.155469 \n", "L 235.**********.155469 \n", "L 235.784375 0 \n", "L 0 0 \n", "z\n", "\" style=\"fill: #ffffff\"/>\n", "  </g>\n", "  <g id=\"axes_1\">\n", "   <g id=\"patch_2\">\n", "    <path d=\"M 30.**********.599219 \n", "L 225.**********.599219 \n", "L 225.403125 10.999219 \n", "L 30.103125 10.999219 \n", "z\n", "\" style=\"fill: #ffffff\"/>\n", "   </g>\n", "   <g id=\"matplotlib.axis_1\">\n", "    <g id=\"xtick_1\">\n", "     <g id=\"line2d_1\">\n", "      <path d=\"M 30.**********.599219 \n", "L 30.103125 10.999219 \n", "\" clip-path=\"url(#p92c803057f)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_2\">\n", "      <defs>\n", "       <path id=\"mf6539076c3\" d=\"M 0 0 \n", "L 0 3.5 \n", "\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </defs>\n", "      <g>\n", "       <use xlink:href=\"#mf6539076c3\" x=\"30.103125\" y=\"149.599219\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_1\">\n", "      <!-- 1 -->\n", "      <g transform=\"translate(26.921875 164.197656)scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-31\" d=\"M 794 531 \n", "L 1825 531 \n", "L 1825 4091 \n", "L 703 3866 \n", "L 703 4441 \n", "L 1819 4666 \n", "L 2450 4666 \n", "L 2450 531 \n", "L 3481 531 \n", "L 3481 0 \n", "L 794 0 \n", "L 794 531 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-31\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_2\">\n", "     <g id=\"line2d_3\">\n", "      <path d=\"M 78.928125 149.599219 \n", "L 78.928125 10.999219 \n", "\" clip-path=\"url(#p92c803057f)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_4\">\n", "      <g>\n", "       <use xlink:href=\"#mf6539076c3\" x=\"78.928125\" y=\"149.599219\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_2\">\n", "      <!-- 2 -->\n", "      <g transform=\"translate(75.746875 164.197656)scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-32\" d=\"M 1228 531 \n", "L 3431 531 \n", "L 3431 0 \n", "L 469 0 \n", "L 469 531 \n", "Q 828 903 1448 1529 \n", "Q 2069 2156 2228 2338 \n", "Q 2531 2678 2651 2914 \n", "Q 2772 3150 2772 3378 \n", "Q 2772 3750 2511 3984 \n", "Q 2250 4219 1831 4219 \n", "Q 1534 4219 1204 4116 \n", "Q 875 4013 500 3803 \n", "L 500 4441 \n", "Q 881 4594 1212 4672 \n", "Q 1544 4750 1819 4750 \n", "Q 2544 4750 2975 4387 \n", "Q 3406 4025 3406 3419 \n", "Q 3406 3131 3298 2873 \n", "Q 3191 2616 2906 2266 \n", "Q 2828 2175 2409 1742 \n", "Q 1991 1309 1228 531 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-32\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_3\">\n", "     <g id=\"line2d_5\">\n", "      <path d=\"M 127.753125 149.599219 \n", "L 127.753125 10.999219 \n", "\" clip-path=\"url(#p92c803057f)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_6\">\n", "      <g>\n", "       <use xlink:href=\"#mf6539076c3\" x=\"127.753125\" y=\"149.599219\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_3\">\n", "      <!-- 3 -->\n", "      <g transform=\"translate(124.571875 164.197656)scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-33\" d=\"M 2597 2516 \n", "Q 3050 2419 3304 2112 \n", "Q 3559 1806 3559 1356 \n", "Q 3559 666 3084 287 \n", "Q 2609 -91 1734 -91 \n", "Q 1441 -91 1130 -33 \n", "Q 819 25 488 141 \n", "L 488 750 \n", "Q 750 597 1062 519 \n", "Q 1375 441 1716 441 \n", "Q 2309 441 2620 675 \n", "Q 2931 909 2931 1356 \n", "Q 2931 1769 2642 2001 \n", "Q 2353 2234 1838 2234 \n", "L 1294 2234 \n", "L 1294 2753 \n", "L 1863 2753 \n", "Q 2328 2753 2575 2939 \n", "Q 2822 3125 2822 3475 \n", "Q 2822 3834 2567 4026 \n", "Q 2313 4219 1838 4219 \n", "Q 1578 4219 1281 4162 \n", "Q 984 4106 628 3988 \n", "L 628 4550 \n", "Q 988 4650 1302 4700 \n", "Q 1616 4750 1894 4750 \n", "Q 2613 4750 3031 4423 \n", "Q 3450 4097 3450 3541 \n", "Q 3450 3153 3228 2886 \n", "Q 3006 2619 2597 2516 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-33\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_4\">\n", "     <g id=\"line2d_7\">\n", "      <path d=\"M 176.578125 149.599219 \n", "L 176.578125 10.999219 \n", "\" clip-path=\"url(#p92c803057f)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_8\">\n", "      <g>\n", "       <use xlink:href=\"#mf6539076c3\" x=\"176.578125\" y=\"149.599219\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_4\">\n", "      <!-- 4 -->\n", "      <g transform=\"translate(173.396875 164.197656)scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-34\" d=\"M 2419 4116 \n", "L 825 1625 \n", "L 2419 1625 \n", "L 2419 4116 \n", "z\n", "M 2253 4666 \n", "L 3047 4666 \n", "L 3047 1625 \n", "L 3713 1625 \n", "L 3713 1100 \n", "L 3047 1100 \n", "L 3047 0 \n", "L 2419 0 \n", "L 2419 1100 \n", "L 313 1100 \n", "L 313 1709 \n", "L 2253 4666 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-34\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_5\">\n", "     <g id=\"line2d_9\">\n", "      <path d=\"M 225.**********.599219 \n", "L 225.403125 10.999219 \n", "\" clip-path=\"url(#p92c803057f)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_10\">\n", "      <g>\n", "       <use xlink:href=\"#mf6539076c3\" x=\"225.403125\" y=\"149.599219\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_5\">\n", "      <!-- 5 -->\n", "      <g transform=\"translate(222.221875 164.197656)scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-35\" d=\"M 691 4666 \n", "L 3169 4666 \n", "L 3169 4134 \n", "L 1269 4134 \n", "L 1269 2991 \n", "Q 1406 3038 1543 3061 \n", "Q 1681 3084 1819 3084 \n", "Q 2600 3084 3056 2656 \n", "Q 3513 2228 3513 1497 \n", "Q 3513 744 3044 326 \n", "Q 2575 -91 1722 -91 \n", "Q 1428 -91 1123 -41 \n", "Q 819 9 494 109 \n", "L 494 744 \n", "Q 775 591 1075 516 \n", "Q 1375 441 1709 441 \n", "Q 2250 441 2565 725 \n", "Q 2881 1009 2881 1497 \n", "Q 2881 1984 2565 2268 \n", "Q 2250 2553 1709 2553 \n", "Q 1456 2553 1204 2497 \n", "Q 953 2441 691 2322 \n", "L 691 4666 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-35\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"text_6\">\n", "     <!-- epoch -->\n", "     <g transform=\"translate(112.525 177.875781)scale(0.1 -0.1)\">\n", "      <defs>\n", "       <path id=\"DejaVuSans-65\" d=\"M 3597 1894 \n", "L 3597 1613 \n", "L 953 1613 \n", "Q 991 1019 1311 708 \n", "Q 1631 397 2203 397 \n", "Q 2534 397 2845 478 \n", "Q 3156 559 3463 722 \n", "L 3463 178 \n", "Q 3153 47 2828 -22 \n", "Q 2503 -91 2169 -91 \n", "Q 1331 -91 842 396 \n", "Q 353 884 353 1716 \n", "Q 353 2575 817 3079 \n", "Q 1281 3584 2069 3584 \n", "Q 2775 3584 3186 3129 \n", "Q 3597 2675 3597 1894 \n", "z\n", "M 3022 2063 \n", "Q 3016 2534 2758 2815 \n", "Q 2500 3097 2075 3097 \n", "Q 1594 3097 1305 2825 \n", "Q 1016 2553 972 2059 \n", "L 3022 2063 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-70\" d=\"M 1159 525 \n", "L 1159 -1331 \n", "L 581 -1331 \n", "L 581 3500 \n", "L 1159 3500 \n", "L 1159 2969 \n", "Q 1341 3281 1617 3432 \n", "Q 1894 3584 2278 3584 \n", "Q 2916 3584 3314 3078 \n", "Q 3713 2572 3713 1747 \n", "Q 3713 922 3314 415 \n", "Q 2916 -91 2278 -91 \n", "Q 1894 -91 1617 61 \n", "Q 1341 213 1159 525 \n", "z\n", "M 3116 1747 \n", "Q 3116 2381 2855 2742 \n", "Q 2594 3103 2138 3103 \n", "Q 1681 3103 1420 2742 \n", "Q 1159 2381 1159 1747 \n", "Q 1159 1113 1420 752 \n", "Q 1681 391 2138 391 \n", "Q 2594 391 2855 752 \n", "Q 3116 1113 3116 1747 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-6f\" d=\"M 1959 3097 \n", "Q 1497 3097 1228 2736 \n", "Q 959 2375 959 1747 \n", "Q 959 1119 1226 758 \n", "Q 1494 397 1959 397 \n", "Q 2419 397 2687 759 \n", "Q 2956 1122 2956 1747 \n", "Q 2956 2369 2687 2733 \n", "Q 2419 3097 1959 3097 \n", "z\n", "M 1959 3584 \n", "Q 2709 3584 3137 3096 \n", "Q 3566 2609 3566 1747 \n", "Q 3566 888 3137 398 \n", "Q 2709 -91 1959 -91 \n", "Q 1206 -91 779 398 \n", "Q 353 888 353 1747 \n", "Q 353 2609 779 3096 \n", "Q 1206 3584 1959 3584 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-63\" d=\"M 3122 3366 \n", "L 3122 2828 \n", "Q 2878 2963 2633 3030 \n", "Q 2388 3097 2138 3097 \n", "Q 1578 3097 1268 2742 \n", "Q 959 2388 959 1747 \n", "Q 959 1106 1268 751 \n", "Q 1578 397 2138 397 \n", "Q 2388 397 2633 464 \n", "Q 2878 531 3122 666 \n", "L 3122 134 \n", "Q 2881 22 2623 -34 \n", "Q 2366 -91 2075 -91 \n", "Q 1284 -91 818 406 \n", "Q 353 903 353 1747 \n", "Q 353 2603 823 3093 \n", "Q 1294 3584 2113 3584 \n", "Q 2378 3584 2631 3529 \n", "Q 2884 3475 3122 3366 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-68\" d=\"M 3513 2113 \n", "L 3513 0 \n", "L 2938 0 \n", "L 2938 2094 \n", "Q 2938 2591 2744 2837 \n", "Q 2550 3084 2163 3084 \n", "Q 1697 3084 1428 2787 \n", "Q 1159 2491 1159 1978 \n", "L 1159 0 \n", "L 581 0 \n", "L 581 4863 \n", "L 1159 4863 \n", "L 1159 2956 \n", "Q 1366 3272 1645 3428 \n", "Q 1925 3584 2291 3584 \n", "Q 2894 3584 3203 3211 \n", "Q 3513 2838 3513 2113 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "      </defs>\n", "      <use xlink:href=\"#DejaVuSans-65\"/>\n", "      <use xlink:href=\"#DejaVuSans-70\" x=\"61.523438\"/>\n", "      <use xlink:href=\"#DejaVuSans-6f\" x=\"125\"/>\n", "      <use xlink:href=\"#DejaVuSans-63\" x=\"186.181641\"/>\n", "      <use xlink:href=\"#DejaVuSans-68\" x=\"241.162109\"/>\n", "     </g>\n", "    </g>\n", "   </g>\n", "   <g id=\"matplotlib.axis_2\">\n", "    <g id=\"ytick_1\">\n", "     <g id=\"line2d_11\">\n", "      <path d=\"M 30.**********.599219 \n", "L 225.**********.599219 \n", "\" clip-path=\"url(#p92c803057f)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_12\">\n", "      <defs>\n", "       <path id=\"m30ec4373bf\" d=\"M 0 0 \n", "L -3.5 0 \n", "\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </defs>\n", "      <g>\n", "       <use xlink:href=\"#m30ec4373bf\" x=\"30.103125\" y=\"149.599219\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_7\">\n", "      <!-- 0.0 -->\n", "      <g transform=\"translate(7.2 153.398438)scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-30\" d=\"M 2034 4250 \n", "Q 1547 4250 1301 3770 \n", "Q 1056 3291 1056 2328 \n", "Q 1056 1369 1301 889 \n", "Q 1547 409 2034 409 \n", "Q 2525 409 2770 889 \n", "Q 3016 1369 3016 2328 \n", "Q 3016 3291 2770 3770 \n", "Q 2525 4250 2034 4250 \n", "z\n", "M 2034 4750 \n", "Q 2819 4750 3233 4129 \n", "Q 3647 3509 3647 2328 \n", "Q 3647 1150 3233 529 \n", "Q 2819 -91 2034 -91 \n", "Q 1250 -91 836 529 \n", "Q 422 1150 422 2328 \n", "Q 422 3509 836 4129 \n", "Q 1250 4750 2034 4750 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "        <path id=\"DejaVuSans-2e\" d=\"M 684 794 \n", "L 1344 794 \n", "L 1344 0 \n", "L 684 0 \n", "L 684 794 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"95.410156\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_2\">\n", "     <g id=\"line2d_13\">\n", "      <path d=\"M 30.103125 121.879219 \n", "L 225.403125 121.879219 \n", "\" clip-path=\"url(#p92c803057f)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_14\">\n", "      <g>\n", "       <use xlink:href=\"#m30ec4373bf\" x=\"30.103125\" y=\"121.879219\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_8\">\n", "      <!-- 0.2 -->\n", "      <g transform=\"translate(7.2 125.678438)scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-32\" x=\"95.410156\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_3\">\n", "     <g id=\"line2d_15\">\n", "      <path d=\"M 30.103125 94.159219 \n", "L 225.403125 94.159219 \n", "\" clip-path=\"url(#p92c803057f)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_16\">\n", "      <g>\n", "       <use xlink:href=\"#m30ec4373bf\" x=\"30.103125\" y=\"94.159219\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_9\">\n", "      <!-- 0.4 -->\n", "      <g transform=\"translate(7.2 97.958438)scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-34\" x=\"95.410156\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_4\">\n", "     <g id=\"line2d_17\">\n", "      <path d=\"M 30.103125 66.439219 \n", "L 225.403125 66.439219 \n", "\" clip-path=\"url(#p92c803057f)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_18\">\n", "      <g>\n", "       <use xlink:href=\"#m30ec4373bf\" x=\"30.103125\" y=\"66.439219\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_10\">\n", "      <!-- 0.6 -->\n", "      <g transform=\"translate(7.2 70.238437)scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-36\" d=\"M 2113 2584 \n", "Q 1688 2584 1439 2293 \n", "Q 1191 2003 1191 1497 \n", "Q 1191 994 1439 701 \n", "Q 1688 409 2113 409 \n", "Q 2538 409 2786 701 \n", "Q 3034 994 3034 1497 \n", "Q 3034 2003 2786 2293 \n", "Q 2538 2584 2113 2584 \n", "z\n", "M 3366 4563 \n", "L 3366 3988 \n", "Q 3128 4100 2886 4159 \n", "Q 2644 4219 2406 4219 \n", "Q 1781 4219 1451 3797 \n", "Q 1122 3375 1075 2522 \n", "Q 1259 2794 1537 2939 \n", "Q 1816 3084 2150 3084 \n", "Q 2853 3084 3261 2657 \n", "Q 3669 2231 3669 1497 \n", "Q 3669 778 3244 343 \n", "Q 2819 -91 2113 -91 \n", "Q 1303 -91 875 529 \n", "Q 447 1150 447 2328 \n", "Q 447 3434 972 4092 \n", "Q 1497 4750 2381 4750 \n", "Q 2619 4750 2861 4703 \n", "Q 3103 4656 3366 4563 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-36\" x=\"95.410156\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_5\">\n", "     <g id=\"line2d_19\">\n", "      <path d=\"M 30.103125 38.719219 \n", "L 225.403125 38.719219 \n", "\" clip-path=\"url(#p92c803057f)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_20\">\n", "      <g>\n", "       <use xlink:href=\"#m30ec4373bf\" x=\"30.103125\" y=\"38.719219\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_11\">\n", "      <!-- 0.8 -->\n", "      <g transform=\"translate(7.2 42.518438)scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-38\" d=\"M 2034 2216 \n", "Q 1584 2216 1326 1975 \n", "Q 1069 1734 1069 1313 \n", "Q 1069 891 1326 650 \n", "Q 1584 409 2034 409 \n", "Q 2484 409 2743 651 \n", "Q 3003 894 3003 1313 \n", "Q 3003 1734 2745 1975 \n", "Q 2488 2216 2034 2216 \n", "z\n", "M 1403 2484 \n", "Q 997 2584 770 2862 \n", "Q 544 3141 544 3541 \n", "Q 544 4100 942 4425 \n", "Q 1341 4750 2034 4750 \n", "Q 2731 4750 3128 4425 \n", "Q 3525 4100 3525 3541 \n", "Q 3525 3141 3298 2862 \n", "Q 3072 2584 2669 2484 \n", "Q 3125 2378 3379 2068 \n", "Q 3634 1759 3634 1313 \n", "Q 3634 634 3220 271 \n", "Q 2806 -91 2034 -91 \n", "Q 1263 -91 848 271 \n", "Q 434 634 434 1313 \n", "Q 434 1759 690 2068 \n", "Q 947 2378 1403 2484 \n", "z\n", "M 1172 3481 \n", "Q 1172 3119 1398 2916 \n", "Q 1625 2713 2034 2713 \n", "Q 2441 2713 2670 2916 \n", "Q 2900 3119 2900 3481 \n", "Q 2900 3844 2670 4047 \n", "Q 2441 4250 2034 4250 \n", "Q 1625 4250 1398 4047 \n", "Q 1172 3844 1172 3481 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-38\" x=\"95.410156\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_6\">\n", "     <g id=\"line2d_21\">\n", "      <path d=\"M 30.103125 10.999219 \n", "L 225.403125 10.999219 \n", "\" clip-path=\"url(#p92c803057f)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_22\">\n", "      <g>\n", "       <use xlink:href=\"#m30ec4373bf\" x=\"30.103125\" y=\"10.999219\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_12\">\n", "      <!-- 1.0 -->\n", "      <g transform=\"translate(7.2 14.798438)scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-31\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"95.410156\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "   </g>\n", "   <g id=\"line2d_23\">\n", "    <path d=\"M 37.136259 -1 \n", "L 39.257812 55.427616 \n", "L 48.4125 70.544869 \n", "L 57.567188 71.687795 \n", "L 66.721875 76.877418 \n", "L 75.876563 73.62936 \n", "L 78.928125 73.959887 \n", "L 88.082813 86.256756 \n", "L 97.2375 89.543337 \n", "L 106.392188 87.777374 \n", "L 115.546875 84.522002 \n", "L 124.701563 88.147529 \n", "L 127.753125 87.913968 \n", "L 136.907813 90.654226 \n", "L 146.0625 92.568845 \n", "L 155.217187 86.388394 \n", "L 164.371875 90.546507 \n", "L 173.526563 92.717053 \n", "L 176.578125 92.416953 \n", "L 185.732812 100.254443 \n", "L 194.8875 96.476 \n", "L 204.042188 89.534222 \n", "L 213.196875 90.981901 \n", "L 222.351562 92.104997 \n", "L 225.403125 92.334069 \n", "\" clip-path=\"url(#p92c803057f)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_24\">\n", "    <path d=\"M -1 80.368708 \n", "L -0.4125 80.299219 \n", "L 8.742188 82.344531 \n", "L 17.896875 74.704688 \n", "L 27.051562 70.770469 \n", "L 30.103125 69.765619 \n", "L 39.257812 43.122656 \n", "L 48.4125 41.678906 \n", "L 57.567188 41.558594 \n", "L 66.721875 39.603516 \n", "L 75.876563 40.523906 \n", "L 78.928125 40.936819 \n", "L 88.082813 36.986719 \n", "L 97.2375 34.821094 \n", "L 106.392188 36.505469 \n", "L 115.546875 37.257422 \n", "L 124.701563 35.687344 \n", "L 127.753125 36.085819 \n", "L 136.907813 35.542969 \n", "L 146.0625 34.099219 \n", "L 155.217187 35.182031 \n", "L 164.371875 34.189453 \n", "L 173.526563 33.593906 \n", "L 176.578125 33.868219 \n", "L 185.732812 34.099219 \n", "L 194.8875 35.3625 \n", "L 204.042188 37.588281 \n", "L 213.196875 37.257422 \n", "L 222.351562 36.842344 \n", "L 225.403125 36.640219 \n", "\" clip-path=\"url(#p92c803057f)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #bf00bf; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_25\">\n", "    <path d=\"M 30.103125 35.600719 \n", "L 78.928125 32.482219 \n", "L 127.753125 31.442719 \n", "L 176.578125 35.600719 \n", "L 225.403125 30.576469 \n", "\" clip-path=\"url(#p92c803057f)\" style=\"fill: none; stroke-dasharray: 9.6,2.4,1.5,2.4; stroke-dashoffset: 0; stroke: #008000; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"patch_3\">\n", "    <path d=\"M 30.**********.599219 \n", "L 30.103125 10.999219 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_4\">\n", "    <path d=\"M 225.**********.599219 \n", "L 225.403125 10.999219 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_5\">\n", "    <path d=\"M 30.**********.599219 \n", "L 225.**********.599219 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_6\">\n", "    <path d=\"M 30.103125 10.999219 \n", "L 225.403125 10.999219 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"legend_1\">\n", "    <g id=\"patch_7\">\n", "     <path d=\"M 37.103125 144.599219 \n", "L 114.871875 144.599219 \n", "Q 116.871875 144.599219 116.871875 142.599219 \n", "L 116.871875 99.564844 \n", "Q 116.871875 97.564844 114.871875 97.564844 \n", "L 37.103125 97.564844 \n", "Q 35.103125 97.564844 35.103125 99.564844 \n", "L 35.103125 142.599219 \n", "Q 35.103125 144.599219 37.103125 144.599219 \n", "z\n", "\" style=\"fill: #ffffff; opacity: 0.8; stroke: #cccccc; stroke-linejoin: miter\"/>\n", "    </g>\n", "    <g id=\"line2d_26\">\n", "     <path d=\"M 39.103125 105.663281 \n", "L 49.103125 105.663281 \n", "L 59.103125 105.663281 \n", "\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "    </g>\n", "    <g id=\"text_13\">\n", "     <!-- train loss -->\n", "     <g transform=\"translate(67.103125 109.163281)scale(0.1 -0.1)\">\n", "      <defs>\n", "       <path id=\"DejaVuSans-74\" d=\"M 1172 4494 \n", "L 1172 3500 \n", "L 2356 3500 \n", "L 2356 3053 \n", "L 1172 3053 \n", "L 1172 1153 \n", "Q 1172 725 1289 603 \n", "Q 1406 481 1766 481 \n", "L 2356 481 \n", "L 2356 0 \n", "L 1766 0 \n", "Q 1100 0 847 248 \n", "Q 594 497 594 1153 \n", "L 594 3053 \n", "L 172 3053 \n", "L 172 3500 \n", "L 594 3500 \n", "L 594 4494 \n", "L 1172 4494 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-72\" d=\"M 2631 2963 \n", "Q 2534 3019 2420 3045 \n", "Q 2306 3072 2169 3072 \n", "Q 1681 3072 1420 2755 \n", "Q 1159 2438 1159 1844 \n", "L 1159 0 \n", "L 581 0 \n", "L 581 3500 \n", "L 1159 3500 \n", "L 1159 2956 \n", "Q 1341 3275 1631 3429 \n", "Q 1922 3584 2338 3584 \n", "Q 2397 3584 2469 3576 \n", "Q 2541 3569 2628 3553 \n", "L 2631 2963 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-61\" d=\"M 2194 1759 \n", "Q 1497 1759 1228 1600 \n", "Q 959 1441 959 1056 \n", "Q 959 750 1161 570 \n", "Q 1363 391 1709 391 \n", "Q 2188 391 2477 730 \n", "Q 2766 1069 2766 1631 \n", "L 2766 1759 \n", "L 2194 1759 \n", "z\n", "M 3341 1997 \n", "L 3341 0 \n", "L 2766 0 \n", "L 2766 531 \n", "Q 2569 213 2275 61 \n", "Q 1981 -91 1556 -91 \n", "Q 1019 -91 701 211 \n", "Q 384 513 384 1019 \n", "Q 384 1609 779 1909 \n", "Q 1175 2209 1959 2209 \n", "L 2766 2209 \n", "L 2766 2266 \n", "Q 2766 2663 2505 2880 \n", "Q 2244 3097 1772 3097 \n", "Q 1472 3097 1187 3025 \n", "Q 903 2953 641 2809 \n", "L 641 3341 \n", "Q 956 3463 1253 3523 \n", "Q 1550 3584 1831 3584 \n", "Q 2591 3584 2966 3190 \n", "Q 3341 2797 3341 1997 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-69\" d=\"M 603 3500 \n", "L 1178 3500 \n", "L 1178 0 \n", "L 603 0 \n", "L 603 3500 \n", "z\n", "M 603 4863 \n", "L 1178 4863 \n", "L 1178 4134 \n", "L 603 4134 \n", "L 603 4863 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-6e\" d=\"M 3513 2113 \n", "L 3513 0 \n", "L 2938 0 \n", "L 2938 2094 \n", "Q 2938 2591 2744 2837 \n", "Q 2550 3084 2163 3084 \n", "Q 1697 3084 1428 2787 \n", "Q 1159 2491 1159 1978 \n", "L 1159 0 \n", "L 581 0 \n", "L 581 3500 \n", "L 1159 3500 \n", "L 1159 2956 \n", "Q 1366 3272 1645 3428 \n", "Q 1925 3584 2291 3584 \n", "Q 2894 3584 3203 3211 \n", "Q 3513 2838 3513 2113 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-20\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-6c\" d=\"M 603 4863 \n", "L 1178 4863 \n", "L 1178 0 \n", "L 603 0 \n", "L 603 4863 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-73\" d=\"M 2834 3397 \n", "L 2834 2853 \n", "Q 2591 2978 2328 3040 \n", "Q 2066 3103 1784 3103 \n", "Q 1356 3103 1142 2972 \n", "Q 928 2841 928 2578 \n", "Q 928 2378 1081 2264 \n", "Q 1234 2150 1697 2047 \n", "L 1894 2003 \n", "Q 2506 1872 2764 1633 \n", "Q 3022 1394 3022 966 \n", "Q 3022 478 2636 193 \n", "Q 2250 -91 1575 -91 \n", "Q 1294 -91 989 -36 \n", "Q 684 19 347 128 \n", "L 347 722 \n", "Q 666 556 975 473 \n", "Q 1284 391 1588 391 \n", "Q 1994 391 2212 530 \n", "Q 2431 669 2431 922 \n", "Q 2431 1156 2273 1281 \n", "Q 2116 1406 1581 1522 \n", "L 1381 1569 \n", "Q 847 1681 609 1914 \n", "Q 372 2147 372 2553 \n", "Q 372 3047 722 3315 \n", "Q 1072 3584 1716 3584 \n", "Q 2034 3584 2315 3537 \n", "Q 2597 3491 2834 3397 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "      </defs>\n", "      <use xlink:href=\"#DejaVuSans-74\"/>\n", "      <use xlink:href=\"#DejaVuSans-72\" x=\"39.208984\"/>\n", "      <use xlink:href=\"#DejaVuSans-61\" x=\"80.322266\"/>\n", "      <use xlink:href=\"#DejaVuSans-69\" x=\"141.601562\"/>\n", "      <use xlink:href=\"#DejaVuSans-6e\" x=\"169.384766\"/>\n", "      <use xlink:href=\"#DejaVuSans-20\" x=\"232.763672\"/>\n", "      <use xlink:href=\"#DejaVuSans-6c\" x=\"264.550781\"/>\n", "      <use xlink:href=\"#DejaVuSans-6f\" x=\"292.333984\"/>\n", "      <use xlink:href=\"#DejaVuSans-73\" x=\"353.515625\"/>\n", "      <use xlink:href=\"#DejaVuSans-73\" x=\"405.615234\"/>\n", "     </g>\n", "    </g>\n", "    <g id=\"line2d_27\">\n", "     <path d=\"M 39.103125 120.341406 \n", "L 49.103125 120.341406 \n", "L 59.103125 120.341406 \n", "\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #bf00bf; stroke-width: 1.5\"/>\n", "    </g>\n", "    <g id=\"text_14\">\n", "     <!-- train acc -->\n", "     <g transform=\"translate(67.103125 123.841406)scale(0.1 -0.1)\">\n", "      <use xlink:href=\"#DejaVuSans-74\"/>\n", "      <use xlink:href=\"#DejaVuSans-72\" x=\"39.208984\"/>\n", "      <use xlink:href=\"#DejaVuSans-61\" x=\"80.322266\"/>\n", "      <use xlink:href=\"#DejaVuSans-69\" x=\"141.601562\"/>\n", "      <use xlink:href=\"#DejaVuSans-6e\" x=\"169.384766\"/>\n", "      <use xlink:href=\"#DejaVuSans-20\" x=\"232.763672\"/>\n", "      <use xlink:href=\"#DejaVuSans-61\" x=\"264.550781\"/>\n", "      <use xlink:href=\"#DejaVuSans-63\" x=\"325.830078\"/>\n", "      <use xlink:href=\"#DejaVuSans-63\" x=\"380.810547\"/>\n", "     </g>\n", "    </g>\n", "    <g id=\"line2d_28\">\n", "     <path d=\"M 39.103125 135.019531 \n", "L 49.103125 135.019531 \n", "L 59.103125 135.019531 \n", "\" style=\"fill: none; stroke-dasharray: 9.6,2.4,1.5,2.4; stroke-dashoffset: 0; stroke: #008000; stroke-width: 1.5\"/>\n", "    </g>\n", "    <g id=\"text_15\">\n", "     <!-- test acc -->\n", "     <g transform=\"translate(67.103125 138.519531)scale(0.1 -0.1)\">\n", "      <use xlink:href=\"#DejaVuSans-74\"/>\n", "      <use xlink:href=\"#DejaVuSans-65\" x=\"39.208984\"/>\n", "      <use xlink:href=\"#DejaVuSans-73\" x=\"100.732422\"/>\n", "      <use xlink:href=\"#DejaVuSans-74\" x=\"152.832031\"/>\n", "      <use xlink:href=\"#DejaVuSans-20\" x=\"192.041016\"/>\n", "      <use xlink:href=\"#DejaVuSans-61\" x=\"223.828125\"/>\n", "      <use xlink:href=\"#DejaVuSans-63\" x=\"285.107422\"/>\n", "      <use xlink:href=\"#DejaVuSans-63\" x=\"340.087891\"/>\n", "     </g>\n", "    </g>\n", "   </g>\n", "  </g>\n", " </g>\n", " <defs>\n", "  <clipPath id=\"p92c803057f\">\n", "   <rect x=\"30.103125\" y=\"10.999219\" width=\"195.3\" height=\"138.6\"/>\n", "  </clipPath>\n", " </defs>\n", "</svg>\n"], "text/plain": ["<Figure size 350x250 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["scratch_net = torchvision.models.resnet18()\n", "scratch_net.fc = nn.Linear(scratch_net.fc.in_features, 2)\n", "train_fine_tuning(scratch_net, 5e-4, param_group=False)"]}, {"cell_type": "markdown", "id": "5e371a53", "metadata": {"origin_pos": 40}, "source": ["意料之中，微调模型往往表现更好，因为它的初始参数值更有效。\n", "\n", "## 小结\n", "\n", "* 迁移学习将从源数据集中学到的知识*迁移*到目标数据集，微调是迁移学习的常见技巧。\n", "* 除输出层外，目标模型从源模型中复制所有模型设计及其参数，并根据目标数据集对这些参数进行微调。但是，目标模型的输出层需要从头开始训练。\n", "* 通常，微调参数使用较小的学习率，而从头开始训练输出层可以使用更大的学习率。\n", "\n", "## 练习\n", "\n", "1. 继续提高`finetune_net`的学习率，模型的准确性如何变化？\n", "2. 在比较实验中进一步调整`finetune_net`和`scratch_net`的超参数。它们的准确性还有不同吗？\n", "3. 将输出层`finetune_net`之前的参数设置为源模型的参数，在训练期间不要更新它们。模型的准确性如何变化？提示：可以使用以下代码。\n"]}, {"cell_type": "code", "execution_count": 12, "id": "19433761", "metadata": {"execution": {"iopub.execute_input": "2022-12-07T16:40:45.450383Z", "iopub.status.busy": "2022-12-07T16:40:45.449520Z", "iopub.status.idle": "2022-12-07T16:40:45.455316Z", "shell.execute_reply": "2022-12-07T16:40:45.453890Z"}, "origin_pos": 42, "tab": ["pytorch"]}, "outputs": [], "source": ["for param in finetune_net.parameters():\n", "    param.requires_grad = False"]}, {"cell_type": "markdown", "id": "1e9107a0", "metadata": {"origin_pos": 44}, "source": ["4. 事实上，`ImageNet`数据集中有一个“热狗”类别。我们可以通过以下代码获取其输出层中的相应权重参数，但是我们怎样才能利用这个权重参数？\n"]}, {"cell_type": "code", "execution_count": 13, "id": "03889099", "metadata": {"execution": {"iopub.execute_input": "2022-12-07T16:40:45.460528Z", "iopub.status.busy": "2022-12-07T16:40:45.459409Z", "iopub.status.idle": "2022-12-07T16:40:45.472096Z", "shell.execute_reply": "2022-12-07T16:40:45.470704Z"}, "origin_pos": 46, "tab": ["pytorch"]}, "outputs": [{"data": {"text/plain": ["torch.Size([1, 512])"]}, "execution_count": 13, "metadata": {}, "output_type": "execute_result"}], "source": ["weight = pretrained_net.fc.weight\n", "hotdog_w = torch.split(weight.data, 1, dim=0)[934]\n", "hotdog_w.shape"]}, {"cell_type": "markdown", "id": "72aa964d", "metadata": {"origin_pos": 49, "tab": ["pytorch"]}, "source": ["[Discussions](https://discuss.d2l.ai/t/2894)\n"]}], "metadata": {"language_info": {"name": "python"}}, "nbformat": 4, "nbformat_minor": 5}