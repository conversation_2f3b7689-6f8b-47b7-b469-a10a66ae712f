{"cells": [{"cell_type": "markdown", "id": "e7edcd21", "metadata": {"origin_pos": 0}, "source": ["# 梯度下降\n", ":label:`sec_gd`\n", "\n", "尽管*梯度下降*（gradient descent）很少直接用于深度学习，\n", "但了解它是理解下一节随机梯度下降算法的关键。\n", "例如，由于学习率过大，优化问题可能会发散，这种现象早已在梯度下降中出现。\n", "同样地，*预处理*（preconditioning）是梯度下降中的一种常用技术，\n", "还被沿用到更高级的算法中。\n", "让我们从简单的一维梯度下降开始。\n", "\n", "## 一维梯度下降\n", "\n", "为什么梯度下降算法可以优化目标函数？\n", "一维中的梯度下降给我们很好的启发。\n", "考虑一类连续可微实值函数$f: \\mathbb{R} \\rightarrow \\mathbb{R}$，\n", "利用泰勒展开，我们可以得到\n", "\n", "$$f(x + \\epsilon) = f(x) + \\epsilon f'(x) + \\mathcal{O}(\\epsilon^2).$$\n", ":eqlabel:`gd-taylor`\n", "\n", "即在一阶近似中，$f(x+\\epsilon)$可通过$x$处的函数值$f(x)$和一阶导数$f'(x)$得出。\n", "我们可以假设在负梯度方向上移动的$\\epsilon$会减少$f$。\n", "为了简单起见，我们选择固定步长$\\eta > 0$，然后取$\\epsilon = -\\eta f'(x)$。\n", "将其代入泰勒展开式我们可以得到\n", "\n", "$$f(x - \\eta f'(x)) = f(x) - \\eta f'^2(x) + \\mathcal{O}(\\eta^2 f'^2(x)).$$\n", ":eqlabel:`gd-taylor-2`\n", "\n", "如果其导数$f'(x) \\neq 0$没有消失，我们就能继续展开，这是因为$\\eta f'^2(x)>0$。\n", "此外，我们总是可以令$\\eta$小到足以使高阶项变得不相关。\n", "因此，\n", "\n", "$$f(x - \\eta f'(x)) \\lessapprox f(x).$$\n", "\n", "这意味着，如果我们使用\n", "\n", "$$x \\leftarrow x - \\eta f'(x)$$\n", "\n", "来迭代$x$，函数$f(x)$的值可能会下降。\n", "因此，在梯度下降中，我们首先选择初始值$x$和常数$\\eta > 0$，\n", "然后使用它们连续迭代$x$，直到停止条件达成。\n", "例如，当梯度$|f'(x)|$的幅度足够小或迭代次数达到某个值时。\n", "\n", "下面我们来展示如何实现梯度下降。为了简单起见，我们选用目标函数$f(x)=x^2$。\n", "尽管我们知道$x=0$时$f(x)$能取得最小值，\n", "但我们仍然使用这个简单的函数来观察$x$的变化。\n"]}, {"cell_type": "code", "execution_count": 1, "id": "86bce226", "metadata": {"execution": {"iopub.execute_input": "2022-12-07T16:54:10.733555Z", "iopub.status.busy": "2022-12-07T16:54:10.732422Z", "iopub.status.idle": "2022-12-07T16:54:13.217626Z", "shell.execute_reply": "2022-12-07T16:54:13.216758Z"}, "origin_pos": 2, "tab": ["pytorch"]}, "outputs": [], "source": ["%matplotlib inline\n", "import numpy as np\n", "import torch\n", "from d2l import torch as d2l"]}, {"cell_type": "code", "execution_count": 2, "id": "8f482696", "metadata": {"execution": {"iopub.execute_input": "2022-12-07T16:54:13.221836Z", "iopub.status.busy": "2022-12-07T16:54:13.221185Z", "iopub.status.idle": "2022-12-07T16:54:13.225519Z", "shell.execute_reply": "2022-12-07T16:54:13.224745Z"}, "origin_pos": 5, "tab": ["pytorch"]}, "outputs": [], "source": ["def f(x):  # 目标函数\n", "    return x ** 2\n", "\n", "def f_grad(x):  # 目标函数的梯度(导数)\n", "    return 2 * x"]}, {"cell_type": "markdown", "id": "ac84de46", "metadata": {"origin_pos": 6}, "source": ["接下来，我们使用$x=10$作为初始值，并假设$\\eta=0.2$。\n", "使用梯度下降法迭代$x$共10次，我们可以看到，$x$的值最终将接近最优解。\n"]}, {"cell_type": "code", "execution_count": 3, "id": "b8edf612", "metadata": {"execution": {"iopub.execute_input": "2022-12-07T16:54:13.228651Z", "iopub.status.busy": "2022-12-07T16:54:13.228238Z", "iopub.status.idle": "2022-12-07T16:54:13.233643Z", "shell.execute_reply": "2022-12-07T16:54:13.232870Z"}, "origin_pos": 7, "tab": ["pytorch"]}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["epoch 10, x: 0.060466\n"]}], "source": ["def gd(eta, f_grad):\n", "    x = 10.0\n", "    results = [x]\n", "    for i in range(10):\n", "        x -= eta * f_grad(x)\n", "        results.append(float(x))\n", "    print(f'epoch 10, x: {x:f}')\n", "    return results\n", "\n", "results = gd(0.2, f_grad)"]}, {"cell_type": "markdown", "id": "701ae548", "metadata": {"origin_pos": 9}, "source": ["对进行$x$优化的过程可以绘制如下。\n"]}, {"cell_type": "code", "execution_count": 4, "id": "bfc97247", "metadata": {"execution": {"iopub.execute_input": "2022-12-07T16:54:13.237092Z", "iopub.status.busy": "2022-12-07T16:54:13.236555Z", "iopub.status.idle": "2022-12-07T16:54:13.474925Z", "shell.execute_reply": "2022-12-07T16:54:13.473625Z"}, "origin_pos": 10, "tab": ["pytorch"]}, "outputs": [{"data": {"image/svg+xml": ["<?xml version=\"1.0\" encoding=\"utf-8\" standalone=\"no\"?>\n", "<!DOCTYPE svg PUBLIC \"-//W3C//DTD SVG 1.1//EN\"\n", "  \"http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd\">\n", "<svg xmlns:xlink=\"http://www.w3.org/1999/xlink\" width=\"249.465625pt\" height=\"183.35625pt\" viewBox=\"0 0 249.**********.35625\" xmlns=\"http://www.w3.org/2000/svg\" version=\"1.1\">\n", " <metadata>\n", "  <rdf:RDF xmlns:dc=\"http://purl.org/dc/elements/1.1/\" xmlns:cc=\"http://creativecommons.org/ns#\" xmlns:rdf=\"http://www.w3.org/1999/02/22-rdf-syntax-ns#\">\n", "   <cc:Work>\n", "    <dc:type rdf:resource=\"http://purl.org/dc/dcmitype/StillImage\"/>\n", "    <dc:date>2022-12-07T16:54:13.438329</dc:date>\n", "    <dc:format>image/svg+xml</dc:format>\n", "    <dc:creator>\n", "     <cc:Agent>\n", "      <dc:title>Matplotlib v3.5.1, https://matplotlib.org/</dc:title>\n", "     </cc:Agent>\n", "    </dc:creator>\n", "   </cc:Work>\n", "  </rdf:RDF>\n", " </metadata>\n", " <defs>\n", "  <style type=\"text/css\">*{stroke-linejoin: round; stroke-linecap: butt}</style>\n", " </defs>\n", " <g id=\"figure_1\">\n", "  <g id=\"patch_1\">\n", "   <path d=\"M 0 183.35625 \n", "L 249.**********.35625 \n", "L 249.465625 0 \n", "L 0 0 \n", "z\n", "\" style=\"fill: #ffffff\"/>\n", "  </g>\n", "  <g id=\"axes_1\">\n", "   <g id=\"patch_2\">\n", "    <path d=\"M 46.**********.8 \n", "L 242.**********.8 \n", "L 242.265625 7.2 \n", "L 46.965625 7.2 \n", "z\n", "\" style=\"fill: #ffffff\"/>\n", "   </g>\n", "   <g id=\"matplotlib.axis_1\">\n", "    <g id=\"xtick_1\">\n", "     <g id=\"line2d_1\">\n", "      <path d=\"M 55.842898 145.8 \n", "L 55.842898 7.2 \n", "\" clip-path=\"url(#pb06a6ed127)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_2\">\n", "      <defs>\n", "       <path id=\"m9598e3f514\" d=\"M 0 0 \n", "L 0 3.5 \n", "\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </defs>\n", "      <g>\n", "       <use xlink:href=\"#m9598e3f514\" x=\"55.842898\" y=\"145.8\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_1\">\n", "      <!-- −10 -->\n", "      <g transform=\"translate(45.290554 160.398438)scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-2212\" d=\"M 678 2272 \n", "L 4684 2272 \n", "L 4684 1741 \n", "L 678 1741 \n", "L 678 2272 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "        <path id=\"DejaVuSans-31\" d=\"M 794 531 \n", "L 1825 531 \n", "L 1825 4091 \n", "L 703 3866 \n", "L 703 4441 \n", "L 1819 4666 \n", "L 2450 4666 \n", "L 2450 531 \n", "L 3481 531 \n", "L 3481 0 \n", "L 794 0 \n", "L 794 531 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "        <path id=\"DejaVuSans-30\" d=\"M 2034 4250 \n", "Q 1547 4250 1301 3770 \n", "Q 1056 3291 1056 2328 \n", "Q 1056 1369 1301 889 \n", "Q 1547 409 2034 409 \n", "Q 2525 409 2770 889 \n", "Q 3016 1369 3016 2328 \n", "Q 3016 3291 2770 3770 \n", "Q 2525 4250 2034 4250 \n", "z\n", "M 2034 4750 \n", "Q 2819 4750 3233 4129 \n", "Q 3647 3509 3647 2328 \n", "Q 3647 1150 3233 529 \n", "Q 2819 -91 2034 -91 \n", "Q 1250 -91 836 529 \n", "Q 422 1150 422 2328 \n", "Q 422 3509 836 4129 \n", "Q 1250 4750 2034 4750 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-2212\"/>\n", "       <use xlink:href=\"#DejaVuSans-31\" x=\"83.789062\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"147.412109\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_2\">\n", "     <g id=\"line2d_3\">\n", "      <path d=\"M 100.229261 145.8 \n", "L 100.229261 7.2 \n", "\" clip-path=\"url(#pb06a6ed127)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_4\">\n", "      <g>\n", "       <use xlink:href=\"#m9598e3f514\" x=\"100.229261\" y=\"145.8\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_2\">\n", "      <!-- −5 -->\n", "      <g transform=\"translate(92.858168 160.398438)scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-35\" d=\"M 691 4666 \n", "L 3169 4666 \n", "L 3169 4134 \n", "L 1269 4134 \n", "L 1269 2991 \n", "Q 1406 3038 1543 3061 \n", "Q 1681 3084 1819 3084 \n", "Q 2600 3084 3056 2656 \n", "Q 3513 2228 3513 1497 \n", "Q 3513 744 3044 326 \n", "Q 2575 -91 1722 -91 \n", "Q 1428 -91 1123 -41 \n", "Q 819 9 494 109 \n", "L 494 744 \n", "Q 775 591 1075 516 \n", "Q 1375 441 1709 441 \n", "Q 2250 441 2565 725 \n", "Q 2881 1009 2881 1497 \n", "Q 2881 1984 2565 2268 \n", "Q 2250 2553 1709 2553 \n", "Q 1456 2553 1204 2497 \n", "Q 953 2441 691 2322 \n", "L 691 4666 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-2212\"/>\n", "       <use xlink:href=\"#DejaVuSans-35\" x=\"83.789062\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_3\">\n", "     <g id=\"line2d_5\">\n", "      <path d=\"M 144.615625 145.8 \n", "L 144.615625 7.2 \n", "\" clip-path=\"url(#pb06a6ed127)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_6\">\n", "      <g>\n", "       <use xlink:href=\"#m9598e3f514\" x=\"144.615625\" y=\"145.8\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_3\">\n", "      <!-- 0 -->\n", "      <g transform=\"translate(141.434375 160.398438)scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_4\">\n", "     <g id=\"line2d_7\">\n", "      <path d=\"M 189.001989 145.8 \n", "L 189.001989 7.2 \n", "\" clip-path=\"url(#pb06a6ed127)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_8\">\n", "      <g>\n", "       <use xlink:href=\"#m9598e3f514\" x=\"189.001989\" y=\"145.8\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_4\">\n", "      <!-- 5 -->\n", "      <g transform=\"translate(185.820739 160.398438)scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-35\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_5\">\n", "     <g id=\"line2d_9\">\n", "      <path d=\"M 233.388352 145.8 \n", "L 233.388352 7.2 \n", "\" clip-path=\"url(#pb06a6ed127)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_10\">\n", "      <g>\n", "       <use xlink:href=\"#m9598e3f514\" x=\"233.388352\" y=\"145.8\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_5\">\n", "      <!-- 10 -->\n", "      <g transform=\"translate(227.025852 160.398438)scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-31\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"63.623047\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"text_6\">\n", "     <!-- x -->\n", "     <g transform=\"translate(141.65625 174.076563)scale(0.1 -0.1)\">\n", "      <defs>\n", "       <path id=\"DejaVuSans-78\" d=\"M 3513 3500 \n", "L 2247 1797 \n", "L 3578 0 \n", "L 2900 0 \n", "L 1881 1375 \n", "L 863 0 \n", "L 184 0 \n", "L 1544 1831 \n", "L 300 3500 \n", "L 978 3500 \n", "L 1906 2253 \n", "L 2834 3500 \n", "L 3513 3500 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "      </defs>\n", "      <use xlink:href=\"#DejaVuSans-78\"/>\n", "     </g>\n", "    </g>\n", "   </g>\n", "   <g id=\"matplotlib.axis_2\">\n", "    <g id=\"ytick_1\">\n", "     <g id=\"line2d_11\">\n", "      <path d=\"M 46.965625 139.5 \n", "L 242.265625 139.5 \n", "\" clip-path=\"url(#pb06a6ed127)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_12\">\n", "      <defs>\n", "       <path id=\"m22ad8e8e5b\" d=\"M 0 0 \n", "L -3.5 0 \n", "\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </defs>\n", "      <g>\n", "       <use xlink:href=\"#m22ad8e8e5b\" x=\"46.965625\" y=\"139.5\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_7\">\n", "      <!-- 0 -->\n", "      <g transform=\"translate(33.603125 143.299219)scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_2\">\n", "     <g id=\"line2d_13\">\n", "      <path d=\"M 46.965625 114.3 \n", "L 242.265625 114.3 \n", "\" clip-path=\"url(#pb06a6ed127)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_14\">\n", "      <g>\n", "       <use xlink:href=\"#m22ad8e8e5b\" x=\"46.965625\" y=\"114.3\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_8\">\n", "      <!-- 20 -->\n", "      <g transform=\"translate(27.240625 118.099219)scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-32\" d=\"M 1228 531 \n", "L 3431 531 \n", "L 3431 0 \n", "L 469 0 \n", "L 469 531 \n", "Q 828 903 1448 1529 \n", "Q 2069 2156 2228 2338 \n", "Q 2531 2678 2651 2914 \n", "Q 2772 3150 2772 3378 \n", "Q 2772 3750 2511 3984 \n", "Q 2250 4219 1831 4219 \n", "Q 1534 4219 1204 4116 \n", "Q 875 4013 500 3803 \n", "L 500 4441 \n", "Q 881 4594 1212 4672 \n", "Q 1544 4750 1819 4750 \n", "Q 2544 4750 2975 4387 \n", "Q 3406 4025 3406 3419 \n", "Q 3406 3131 3298 2873 \n", "Q 3191 2616 2906 2266 \n", "Q 2828 2175 2409 1742 \n", "Q 1991 1309 1228 531 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-32\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"63.623047\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_3\">\n", "     <g id=\"line2d_15\">\n", "      <path d=\"M 46.965625 89.1 \n", "L 242.265625 89.1 \n", "\" clip-path=\"url(#pb06a6ed127)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_16\">\n", "      <g>\n", "       <use xlink:href=\"#m22ad8e8e5b\" x=\"46.965625\" y=\"89.1\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_9\">\n", "      <!-- 40 -->\n", "      <g transform=\"translate(27.240625 92.899219)scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-34\" d=\"M 2419 4116 \n", "L 825 1625 \n", "L 2419 1625 \n", "L 2419 4116 \n", "z\n", "M 2253 4666 \n", "L 3047 4666 \n", "L 3047 1625 \n", "L 3713 1625 \n", "L 3713 1100 \n", "L 3047 1100 \n", "L 3047 0 \n", "L 2419 0 \n", "L 2419 1100 \n", "L 313 1100 \n", "L 313 1709 \n", "L 2253 4666 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-34\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"63.623047\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_4\">\n", "     <g id=\"line2d_17\">\n", "      <path d=\"M 46.965625 63.9 \n", "L 242.265625 63.9 \n", "\" clip-path=\"url(#pb06a6ed127)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_18\">\n", "      <g>\n", "       <use xlink:href=\"#m22ad8e8e5b\" x=\"46.965625\" y=\"63.9\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_10\">\n", "      <!-- 60 -->\n", "      <g transform=\"translate(27.240625 67.699219)scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-36\" d=\"M 2113 2584 \n", "Q 1688 2584 1439 2293 \n", "Q 1191 2003 1191 1497 \n", "Q 1191 994 1439 701 \n", "Q 1688 409 2113 409 \n", "Q 2538 409 2786 701 \n", "Q 3034 994 3034 1497 \n", "Q 3034 2003 2786 2293 \n", "Q 2538 2584 2113 2584 \n", "z\n", "M 3366 4563 \n", "L 3366 3988 \n", "Q 3128 4100 2886 4159 \n", "Q 2644 4219 2406 4219 \n", "Q 1781 4219 1451 3797 \n", "Q 1122 3375 1075 2522 \n", "Q 1259 2794 1537 2939 \n", "Q 1816 3084 2150 3084 \n", "Q 2853 3084 3261 2657 \n", "Q 3669 2231 3669 1497 \n", "Q 3669 778 3244 343 \n", "Q 2819 -91 2113 -91 \n", "Q 1303 -91 875 529 \n", "Q 447 1150 447 2328 \n", "Q 447 3434 972 4092 \n", "Q 1497 4750 2381 4750 \n", "Q 2619 4750 2861 4703 \n", "Q 3103 4656 3366 4563 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-36\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"63.623047\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_5\">\n", "     <g id=\"line2d_19\">\n", "      <path d=\"M 46.965625 38.7 \n", "L 242.265625 38.7 \n", "\" clip-path=\"url(#pb06a6ed127)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_20\">\n", "      <g>\n", "       <use xlink:href=\"#m22ad8e8e5b\" x=\"46.965625\" y=\"38.7\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_11\">\n", "      <!-- 80 -->\n", "      <g transform=\"translate(27.240625 42.499219)scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-38\" d=\"M 2034 2216 \n", "Q 1584 2216 1326 1975 \n", "Q 1069 1734 1069 1313 \n", "Q 1069 891 1326 650 \n", "Q 1584 409 2034 409 \n", "Q 2484 409 2743 651 \n", "Q 3003 894 3003 1313 \n", "Q 3003 1734 2745 1975 \n", "Q 2488 2216 2034 2216 \n", "z\n", "M 1403 2484 \n", "Q 997 2584 770 2862 \n", "Q 544 3141 544 3541 \n", "Q 544 4100 942 4425 \n", "Q 1341 4750 2034 4750 \n", "Q 2731 4750 3128 4425 \n", "Q 3525 4100 3525 3541 \n", "Q 3525 3141 3298 2862 \n", "Q 3072 2584 2669 2484 \n", "Q 3125 2378 3379 2068 \n", "Q 3634 1759 3634 1313 \n", "Q 3634 634 3220 271 \n", "Q 2806 -91 2034 -91 \n", "Q 1263 -91 848 271 \n", "Q 434 634 434 1313 \n", "Q 434 1759 690 2068 \n", "Q 947 2378 1403 2484 \n", "z\n", "M 1172 3481 \n", "Q 1172 3119 1398 2916 \n", "Q 1625 2713 2034 2713 \n", "Q 2441 2713 2670 2916 \n", "Q 2900 3119 2900 3481 \n", "Q 2900 3844 2670 4047 \n", "Q 2441 4250 2034 4250 \n", "Q 1625 4250 1398 4047 \n", "Q 1172 3844 1172 3481 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-38\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"63.623047\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_6\">\n", "     <g id=\"line2d_21\">\n", "      <path d=\"M 46.965625 13.5 \n", "L 242.265625 13.5 \n", "\" clip-path=\"url(#pb06a6ed127)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_22\">\n", "      <g>\n", "       <use xlink:href=\"#m22ad8e8e5b\" x=\"46.965625\" y=\"13.5\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_12\">\n", "      <!-- 100 -->\n", "      <g transform=\"translate(20.878125 17.299219)scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-31\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"127.246094\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"text_13\">\n", "     <!-- f(x) -->\n", "     <g transform=\"translate(14.798438 85.121094)rotate(-90)scale(0.1 -0.1)\">\n", "      <defs>\n", "       <path id=\"DejaVuSans-66\" d=\"M 2375 4863 \n", "L 2375 4384 \n", "L 1825 4384 \n", "Q 1516 4384 1395 4259 \n", "Q 1275 4134 1275 3809 \n", "L 1275 3500 \n", "L 2222 3500 \n", "L 2222 3053 \n", "L 1275 3053 \n", "L 1275 0 \n", "L 697 0 \n", "L 697 3053 \n", "L 147 3053 \n", "L 147 3500 \n", "L 697 3500 \n", "L 697 3744 \n", "Q 697 4328 969 4595 \n", "Q 1241 4863 1831 4863 \n", "L 2375 4863 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-28\" d=\"M 1984 4856 \n", "Q 1566 4138 1362 3434 \n", "Q 1159 2731 1159 2009 \n", "Q 1159 1288 1364 580 \n", "Q 1569 -128 1984 -844 \n", "L 1484 -844 \n", "Q 1016 -109 783 600 \n", "Q 550 1309 550 2009 \n", "Q 550 2706 781 3412 \n", "Q 1013 4119 1484 4856 \n", "L 1984 4856 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-29\" d=\"M 513 4856 \n", "L 1013 4856 \n", "Q 1481 4119 1714 3412 \n", "Q 1947 2706 1947 2009 \n", "Q 1947 1309 1714 600 \n", "Q 1481 -109 1013 -844 \n", "L 513 -844 \n", "Q 928 -128 1133 580 \n", "Q 1338 1288 1338 2009 \n", "Q 1338 2731 1133 3434 \n", "Q 928 4138 513 4856 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "      </defs>\n", "      <use xlink:href=\"#DejaVuSans-66\"/>\n", "      <use xlink:href=\"#DejaVuSans-28\" x=\"35.205078\"/>\n", "      <use xlink:href=\"#DejaVuSans-78\" x=\"74.21875\"/>\n", "      <use xlink:href=\"#DejaVuSans-29\" x=\"133.398438\"/>\n", "     </g>\n", "    </g>\n", "   </g>\n", "   <g id=\"line2d_23\">\n", "    <path d=\"M 55.842898 13.5 \n", "L 60.459075 26.263287 \n", "L 64.89772 37.893111 \n", "L 69.247573 48.679063 \n", "L 73.508668 58.65827 \n", "L 77.680989 67.866983 \n", "L 81.764535 76.340736 \n", "L 85.670535 83.947104 \n", "L 89.487765 90.909241 \n", "L 93.216216 97.259632 \n", "L 96.855897 103.030055 \n", "L 100.406807 108.251496 \n", "L 103.868946 112.954199 \n", "L 107.242311 117.167639 \n", "L 110.526898 120.920544 \n", "L 113.722716 124.240896 \n", "L 116.82976 127.155905 \n", "L 119.848034 129.692035 \n", "L 122.866306 131.93685 \n", "L 125.795806 133.837055 \n", "L 128.636533 135.4176 \n", "L 131.388489 136.702674 \n", "L 134.140443 137.745576 \n", "L 136.803625 138.524256 \n", "L 139.466807 139.076136 \n", "L 142.129989 139.401216 \n", "L 144.79317 139.499496 \n", "L 147.456352 139.370976 \n", "L 150.119534 139.015656 \n", "L 152.782716 138.433536 \n", "L 155.445898 137.624616 \n", "L 158.197852 136.550466 \n", "L 160.949807 135.234144 \n", "L 163.790535 133.621344 \n", "L 166.631262 131.750496 \n", "L 169.560761 129.550915 \n", "L 172.579033 126.997652 \n", "L 175.597307 124.153073 \n", "L 178.704352 120.920544 \n", "L 181.900169 117.273603 \n", "L 185.184763 113.185025 \n", "L 188.558128 108.626847 \n", "L 192.020263 103.570341 \n", "L 195.571168 97.986027 \n", "L 199.210853 91.843647 \n", "L 202.939308 85.112224 \n", "L 206.756534 77.76 \n", "L 210.662535 69.754465 \n", "L 214.65731 61.062347 \n", "L 218.829622 51.43911 \n", "L 223.090717 41.036545 \n", "L 227.440579 29.81839 \n", "L 231.879215 17.747588 \n", "L 233.299578 13.751881 \n", "L 233.299578 13.751881 \n", "\" clip-path=\"url(#pb06a6ed127)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_24\">\n", "    <path d=\"M 233.388352 13.5 \n", "L 197.879261 94.14 \n", "L 176.573807 123.1704 \n", "L 163.790534 133.621344 \n", "L 156.12057 137.383684 \n", "L 151.518592 138.738126 \n", "L 148.757405 139.225725 \n", "L 147.100693 139.401261 \n", "L 146.106666 139.464454 \n", "L 145.51025 139.487203 \n", "L 145.1524 139.495393 \n", "\" clip-path=\"url(#pb06a6ed127)\" style=\"fill: none; stroke: #ff7f0e; stroke-width: 1.5; stroke-linecap: square\"/>\n", "    <defs>\n", "     <path id=\"mbb588fb106\" d=\"M 0 3 \n", "C 0.795609 3 1.55874 2.683901 2.12132 2.12132 \n", "C 2.683901 1.55874 3 0.795609 3 0 \n", "C 3 -0.795609 2.683901 -1.55874 2.12132 -2.12132 \n", "C 1.55874 -2.683901 0.795609 -3 0 -3 \n", "C -0.795609 -3 -1.55874 -2.683901 -2.12132 -2.12132 \n", "C -2.683901 -1.55874 -3 -0.795609 -3 0 \n", "C -3 0.795609 -2.683901 1.55874 -2.12132 2.12132 \n", "C -1.55874 2.683901 -0.795609 3 0 3 \n", "z\n", "\" style=\"stroke: #ff7f0e\"/>\n", "    </defs>\n", "    <g clip-path=\"url(#pb06a6ed127)\">\n", "     <use xlink:href=\"#mbb588fb106\" x=\"233.388352\" y=\"13.5\" style=\"fill: #ff7f0e; stroke: #ff7f0e\"/>\n", "     <use xlink:href=\"#mbb588fb106\" x=\"197.879261\" y=\"94.14\" style=\"fill: #ff7f0e; stroke: #ff7f0e\"/>\n", "     <use xlink:href=\"#mbb588fb106\" x=\"176.573807\" y=\"123.1704\" style=\"fill: #ff7f0e; stroke: #ff7f0e\"/>\n", "     <use xlink:href=\"#mbb588fb106\" x=\"163.790534\" y=\"133.621344\" style=\"fill: #ff7f0e; stroke: #ff7f0e\"/>\n", "     <use xlink:href=\"#mbb588fb106\" x=\"156.12057\" y=\"137.383684\" style=\"fill: #ff7f0e; stroke: #ff7f0e\"/>\n", "     <use xlink:href=\"#mbb588fb106\" x=\"151.518592\" y=\"138.738126\" style=\"fill: #ff7f0e; stroke: #ff7f0e\"/>\n", "     <use xlink:href=\"#mbb588fb106\" x=\"148.757405\" y=\"139.225725\" style=\"fill: #ff7f0e; stroke: #ff7f0e\"/>\n", "     <use xlink:href=\"#mbb588fb106\" x=\"147.100693\" y=\"139.401261\" style=\"fill: #ff7f0e; stroke: #ff7f0e\"/>\n", "     <use xlink:href=\"#mbb588fb106\" x=\"146.106666\" y=\"139.464454\" style=\"fill: #ff7f0e; stroke: #ff7f0e\"/>\n", "     <use xlink:href=\"#mbb588fb106\" x=\"145.51025\" y=\"139.487203\" style=\"fill: #ff7f0e; stroke: #ff7f0e\"/>\n", "     <use xlink:href=\"#mbb588fb106\" x=\"145.1524\" y=\"139.495393\" style=\"fill: #ff7f0e; stroke: #ff7f0e\"/>\n", "    </g>\n", "   </g>\n", "   <g id=\"patch_3\">\n", "    <path d=\"M 46.**********.8 \n", "L 46.965625 7.2 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_4\">\n", "    <path d=\"M 242.**********.8 \n", "L 242.265625 7.2 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_5\">\n", "    <path d=\"M 46.**********.8 \n", "L 242.**********.8 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_6\">\n", "    <path d=\"M 46.965625 7.2 \n", "L 242.265625 7.2 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "  </g>\n", " </g>\n", " <defs>\n", "  <clipPath id=\"pb06a6ed127\">\n", "   <rect x=\"46.965625\" y=\"7.2\" width=\"195.3\" height=\"138.6\"/>\n", "  </clipPath>\n", " </defs>\n", "</svg>\n"], "text/plain": ["<Figure size 350x250 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["def show_trace(results, f):\n", "    n = max(abs(min(results)), abs(max(results)))\n", "    f_line = torch.arange(-n, n, 0.01)\n", "    d2l.set_figsize()\n", "    d2l.plot([f_line, results], [[f(x) for x in f_line], [\n", "        f(x) for x in results]], 'x', 'f(x)', fmts=['-', '-o'])\n", "\n", "show_trace(results, f)"]}, {"cell_type": "markdown", "id": "5a4af7c2", "metadata": {"origin_pos": 12}, "source": ["### 学习率\n", ":label:`subsec_gd-learningrate`\n", "\n", "*学习率*（learning rate）决定目标函数能否收敛到局部最小值，以及何时收敛到最小值。\n", "学习率$\\eta$可由算法设计者设置。\n", "请注意，如果我们使用的学习率太小，将导致$x$的更新非常缓慢，需要更多的迭代。\n", "例如，考虑同一优化问题中$\\eta = 0.05$的进度。\n", "如下所示，尽管经过了10个步骤，我们仍然离最优解很远。\n"]}, {"cell_type": "code", "execution_count": 5, "id": "8eaf42ee", "metadata": {"execution": {"iopub.execute_input": "2022-12-07T16:54:13.478529Z", "iopub.status.busy": "2022-12-07T16:54:13.477947Z", "iopub.status.idle": "2022-12-07T16:54:13.733998Z", "shell.execute_reply": "2022-12-07T16:54:13.733201Z"}, "origin_pos": 13, "tab": ["pytorch"]}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["epoch 10, x: 3.486784\n"]}, {"data": {"image/svg+xml": ["<?xml version=\"1.0\" encoding=\"utf-8\" standalone=\"no\"?>\n", "<!DOCTYPE svg PUBLIC \"-//W3C//DTD SVG 1.1//EN\"\n", "  \"http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd\">\n", "<svg xmlns:xlink=\"http://www.w3.org/1999/xlink\" width=\"249.465625pt\" height=\"183.35625pt\" viewBox=\"0 0 249.**********.35625\" xmlns=\"http://www.w3.org/2000/svg\" version=\"1.1\">\n", " <metadata>\n", "  <rdf:RDF xmlns:dc=\"http://purl.org/dc/elements/1.1/\" xmlns:cc=\"http://creativecommons.org/ns#\" xmlns:rdf=\"http://www.w3.org/1999/02/22-rdf-syntax-ns#\">\n", "   <cc:Work>\n", "    <dc:type rdf:resource=\"http://purl.org/dc/dcmitype/StillImage\"/>\n", "    <dc:date>2022-12-07T16:54:13.698586</dc:date>\n", "    <dc:format>image/svg+xml</dc:format>\n", "    <dc:creator>\n", "     <cc:Agent>\n", "      <dc:title>Matplotlib v3.5.1, https://matplotlib.org/</dc:title>\n", "     </cc:Agent>\n", "    </dc:creator>\n", "   </cc:Work>\n", "  </rdf:RDF>\n", " </metadata>\n", " <defs>\n", "  <style type=\"text/css\">*{stroke-linejoin: round; stroke-linecap: butt}</style>\n", " </defs>\n", " <g id=\"figure_1\">\n", "  <g id=\"patch_1\">\n", "   <path d=\"M 0 183.35625 \n", "L 249.**********.35625 \n", "L 249.465625 0 \n", "L 0 0 \n", "z\n", "\" style=\"fill: #ffffff\"/>\n", "  </g>\n", "  <g id=\"axes_1\">\n", "   <g id=\"patch_2\">\n", "    <path d=\"M 46.**********.8 \n", "L 242.**********.8 \n", "L 242.265625 7.2 \n", "L 46.965625 7.2 \n", "z\n", "\" style=\"fill: #ffffff\"/>\n", "   </g>\n", "   <g id=\"matplotlib.axis_1\">\n", "    <g id=\"xtick_1\">\n", "     <g id=\"line2d_1\">\n", "      <path d=\"M 55.842898 145.8 \n", "L 55.842898 7.2 \n", "\" clip-path=\"url(#p5c4ed29721)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_2\">\n", "      <defs>\n", "       <path id=\"md106606308\" d=\"M 0 0 \n", "L 0 3.5 \n", "\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </defs>\n", "      <g>\n", "       <use xlink:href=\"#md106606308\" x=\"55.842898\" y=\"145.8\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_1\">\n", "      <!-- −10 -->\n", "      <g transform=\"translate(45.290554 160.398438)scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-2212\" d=\"M 678 2272 \n", "L 4684 2272 \n", "L 4684 1741 \n", "L 678 1741 \n", "L 678 2272 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "        <path id=\"DejaVuSans-31\" d=\"M 794 531 \n", "L 1825 531 \n", "L 1825 4091 \n", "L 703 3866 \n", "L 703 4441 \n", "L 1819 4666 \n", "L 2450 4666 \n", "L 2450 531 \n", "L 3481 531 \n", "L 3481 0 \n", "L 794 0 \n", "L 794 531 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "        <path id=\"DejaVuSans-30\" d=\"M 2034 4250 \n", "Q 1547 4250 1301 3770 \n", "Q 1056 3291 1056 2328 \n", "Q 1056 1369 1301 889 \n", "Q 1547 409 2034 409 \n", "Q 2525 409 2770 889 \n", "Q 3016 1369 3016 2328 \n", "Q 3016 3291 2770 3770 \n", "Q 2525 4250 2034 4250 \n", "z\n", "M 2034 4750 \n", "Q 2819 4750 3233 4129 \n", "Q 3647 3509 3647 2328 \n", "Q 3647 1150 3233 529 \n", "Q 2819 -91 2034 -91 \n", "Q 1250 -91 836 529 \n", "Q 422 1150 422 2328 \n", "Q 422 3509 836 4129 \n", "Q 1250 4750 2034 4750 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-2212\"/>\n", "       <use xlink:href=\"#DejaVuSans-31\" x=\"83.789062\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"147.412109\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_2\">\n", "     <g id=\"line2d_3\">\n", "      <path d=\"M 100.229261 145.8 \n", "L 100.229261 7.2 \n", "\" clip-path=\"url(#p5c4ed29721)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_4\">\n", "      <g>\n", "       <use xlink:href=\"#md106606308\" x=\"100.229261\" y=\"145.8\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_2\">\n", "      <!-- −5 -->\n", "      <g transform=\"translate(92.858168 160.398438)scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-35\" d=\"M 691 4666 \n", "L 3169 4666 \n", "L 3169 4134 \n", "L 1269 4134 \n", "L 1269 2991 \n", "Q 1406 3038 1543 3061 \n", "Q 1681 3084 1819 3084 \n", "Q 2600 3084 3056 2656 \n", "Q 3513 2228 3513 1497 \n", "Q 3513 744 3044 326 \n", "Q 2575 -91 1722 -91 \n", "Q 1428 -91 1123 -41 \n", "Q 819 9 494 109 \n", "L 494 744 \n", "Q 775 591 1075 516 \n", "Q 1375 441 1709 441 \n", "Q 2250 441 2565 725 \n", "Q 2881 1009 2881 1497 \n", "Q 2881 1984 2565 2268 \n", "Q 2250 2553 1709 2553 \n", "Q 1456 2553 1204 2497 \n", "Q 953 2441 691 2322 \n", "L 691 4666 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-2212\"/>\n", "       <use xlink:href=\"#DejaVuSans-35\" x=\"83.789062\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_3\">\n", "     <g id=\"line2d_5\">\n", "      <path d=\"M 144.615625 145.8 \n", "L 144.615625 7.2 \n", "\" clip-path=\"url(#p5c4ed29721)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_6\">\n", "      <g>\n", "       <use xlink:href=\"#md106606308\" x=\"144.615625\" y=\"145.8\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_3\">\n", "      <!-- 0 -->\n", "      <g transform=\"translate(141.434375 160.398438)scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_4\">\n", "     <g id=\"line2d_7\">\n", "      <path d=\"M 189.001989 145.8 \n", "L 189.001989 7.2 \n", "\" clip-path=\"url(#p5c4ed29721)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_8\">\n", "      <g>\n", "       <use xlink:href=\"#md106606308\" x=\"189.001989\" y=\"145.8\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_4\">\n", "      <!-- 5 -->\n", "      <g transform=\"translate(185.820739 160.398438)scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-35\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_5\">\n", "     <g id=\"line2d_9\">\n", "      <path d=\"M 233.388352 145.8 \n", "L 233.388352 7.2 \n", "\" clip-path=\"url(#p5c4ed29721)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_10\">\n", "      <g>\n", "       <use xlink:href=\"#md106606308\" x=\"233.388352\" y=\"145.8\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_5\">\n", "      <!-- 10 -->\n", "      <g transform=\"translate(227.025852 160.398438)scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-31\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"63.623047\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"text_6\">\n", "     <!-- x -->\n", "     <g transform=\"translate(141.65625 174.076563)scale(0.1 -0.1)\">\n", "      <defs>\n", "       <path id=\"DejaVuSans-78\" d=\"M 3513 3500 \n", "L 2247 1797 \n", "L 3578 0 \n", "L 2900 0 \n", "L 1881 1375 \n", "L 863 0 \n", "L 184 0 \n", "L 1544 1831 \n", "L 300 3500 \n", "L 978 3500 \n", "L 1906 2253 \n", "L 2834 3500 \n", "L 3513 3500 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "      </defs>\n", "      <use xlink:href=\"#DejaVuSans-78\"/>\n", "     </g>\n", "    </g>\n", "   </g>\n", "   <g id=\"matplotlib.axis_2\">\n", "    <g id=\"ytick_1\">\n", "     <g id=\"line2d_11\">\n", "      <path d=\"M 46.965625 139.5 \n", "L 242.265625 139.5 \n", "\" clip-path=\"url(#p5c4ed29721)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_12\">\n", "      <defs>\n", "       <path id=\"ma5fdd9a67c\" d=\"M 0 0 \n", "L -3.5 0 \n", "\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </defs>\n", "      <g>\n", "       <use xlink:href=\"#ma5fdd9a67c\" x=\"46.965625\" y=\"139.5\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_7\">\n", "      <!-- 0 -->\n", "      <g transform=\"translate(33.603125 143.299219)scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_2\">\n", "     <g id=\"line2d_13\">\n", "      <path d=\"M 46.965625 114.3 \n", "L 242.265625 114.3 \n", "\" clip-path=\"url(#p5c4ed29721)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_14\">\n", "      <g>\n", "       <use xlink:href=\"#ma5fdd9a67c\" x=\"46.965625\" y=\"114.3\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_8\">\n", "      <!-- 20 -->\n", "      <g transform=\"translate(27.240625 118.099219)scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-32\" d=\"M 1228 531 \n", "L 3431 531 \n", "L 3431 0 \n", "L 469 0 \n", "L 469 531 \n", "Q 828 903 1448 1529 \n", "Q 2069 2156 2228 2338 \n", "Q 2531 2678 2651 2914 \n", "Q 2772 3150 2772 3378 \n", "Q 2772 3750 2511 3984 \n", "Q 2250 4219 1831 4219 \n", "Q 1534 4219 1204 4116 \n", "Q 875 4013 500 3803 \n", "L 500 4441 \n", "Q 881 4594 1212 4672 \n", "Q 1544 4750 1819 4750 \n", "Q 2544 4750 2975 4387 \n", "Q 3406 4025 3406 3419 \n", "Q 3406 3131 3298 2873 \n", "Q 3191 2616 2906 2266 \n", "Q 2828 2175 2409 1742 \n", "Q 1991 1309 1228 531 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-32\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"63.623047\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_3\">\n", "     <g id=\"line2d_15\">\n", "      <path d=\"M 46.965625 89.1 \n", "L 242.265625 89.1 \n", "\" clip-path=\"url(#p5c4ed29721)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_16\">\n", "      <g>\n", "       <use xlink:href=\"#ma5fdd9a67c\" x=\"46.965625\" y=\"89.1\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_9\">\n", "      <!-- 40 -->\n", "      <g transform=\"translate(27.240625 92.899219)scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-34\" d=\"M 2419 4116 \n", "L 825 1625 \n", "L 2419 1625 \n", "L 2419 4116 \n", "z\n", "M 2253 4666 \n", "L 3047 4666 \n", "L 3047 1625 \n", "L 3713 1625 \n", "L 3713 1100 \n", "L 3047 1100 \n", "L 3047 0 \n", "L 2419 0 \n", "L 2419 1100 \n", "L 313 1100 \n", "L 313 1709 \n", "L 2253 4666 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-34\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"63.623047\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_4\">\n", "     <g id=\"line2d_17\">\n", "      <path d=\"M 46.965625 63.9 \n", "L 242.265625 63.9 \n", "\" clip-path=\"url(#p5c4ed29721)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_18\">\n", "      <g>\n", "       <use xlink:href=\"#ma5fdd9a67c\" x=\"46.965625\" y=\"63.9\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_10\">\n", "      <!-- 60 -->\n", "      <g transform=\"translate(27.240625 67.699219)scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-36\" d=\"M 2113 2584 \n", "Q 1688 2584 1439 2293 \n", "Q 1191 2003 1191 1497 \n", "Q 1191 994 1439 701 \n", "Q 1688 409 2113 409 \n", "Q 2538 409 2786 701 \n", "Q 3034 994 3034 1497 \n", "Q 3034 2003 2786 2293 \n", "Q 2538 2584 2113 2584 \n", "z\n", "M 3366 4563 \n", "L 3366 3988 \n", "Q 3128 4100 2886 4159 \n", "Q 2644 4219 2406 4219 \n", "Q 1781 4219 1451 3797 \n", "Q 1122 3375 1075 2522 \n", "Q 1259 2794 1537 2939 \n", "Q 1816 3084 2150 3084 \n", "Q 2853 3084 3261 2657 \n", "Q 3669 2231 3669 1497 \n", "Q 3669 778 3244 343 \n", "Q 2819 -91 2113 -91 \n", "Q 1303 -91 875 529 \n", "Q 447 1150 447 2328 \n", "Q 447 3434 972 4092 \n", "Q 1497 4750 2381 4750 \n", "Q 2619 4750 2861 4703 \n", "Q 3103 4656 3366 4563 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-36\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"63.623047\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_5\">\n", "     <g id=\"line2d_19\">\n", "      <path d=\"M 46.965625 38.7 \n", "L 242.265625 38.7 \n", "\" clip-path=\"url(#p5c4ed29721)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_20\">\n", "      <g>\n", "       <use xlink:href=\"#ma5fdd9a67c\" x=\"46.965625\" y=\"38.7\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_11\">\n", "      <!-- 80 -->\n", "      <g transform=\"translate(27.240625 42.499219)scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-38\" d=\"M 2034 2216 \n", "Q 1584 2216 1326 1975 \n", "Q 1069 1734 1069 1313 \n", "Q 1069 891 1326 650 \n", "Q 1584 409 2034 409 \n", "Q 2484 409 2743 651 \n", "Q 3003 894 3003 1313 \n", "Q 3003 1734 2745 1975 \n", "Q 2488 2216 2034 2216 \n", "z\n", "M 1403 2484 \n", "Q 997 2584 770 2862 \n", "Q 544 3141 544 3541 \n", "Q 544 4100 942 4425 \n", "Q 1341 4750 2034 4750 \n", "Q 2731 4750 3128 4425 \n", "Q 3525 4100 3525 3541 \n", "Q 3525 3141 3298 2862 \n", "Q 3072 2584 2669 2484 \n", "Q 3125 2378 3379 2068 \n", "Q 3634 1759 3634 1313 \n", "Q 3634 634 3220 271 \n", "Q 2806 -91 2034 -91 \n", "Q 1263 -91 848 271 \n", "Q 434 634 434 1313 \n", "Q 434 1759 690 2068 \n", "Q 947 2378 1403 2484 \n", "z\n", "M 1172 3481 \n", "Q 1172 3119 1398 2916 \n", "Q 1625 2713 2034 2713 \n", "Q 2441 2713 2670 2916 \n", "Q 2900 3119 2900 3481 \n", "Q 2900 3844 2670 4047 \n", "Q 2441 4250 2034 4250 \n", "Q 1625 4250 1398 4047 \n", "Q 1172 3844 1172 3481 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-38\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"63.623047\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_6\">\n", "     <g id=\"line2d_21\">\n", "      <path d=\"M 46.965625 13.5 \n", "L 242.265625 13.5 \n", "\" clip-path=\"url(#p5c4ed29721)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_22\">\n", "      <g>\n", "       <use xlink:href=\"#ma5fdd9a67c\" x=\"46.965625\" y=\"13.5\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_12\">\n", "      <!-- 100 -->\n", "      <g transform=\"translate(20.878125 17.299219)scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-31\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"127.246094\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"text_13\">\n", "     <!-- f(x) -->\n", "     <g transform=\"translate(14.798438 85.121094)rotate(-90)scale(0.1 -0.1)\">\n", "      <defs>\n", "       <path id=\"DejaVuSans-66\" d=\"M 2375 4863 \n", "L 2375 4384 \n", "L 1825 4384 \n", "Q 1516 4384 1395 4259 \n", "Q 1275 4134 1275 3809 \n", "L 1275 3500 \n", "L 2222 3500 \n", "L 2222 3053 \n", "L 1275 3053 \n", "L 1275 0 \n", "L 697 0 \n", "L 697 3053 \n", "L 147 3053 \n", "L 147 3500 \n", "L 697 3500 \n", "L 697 3744 \n", "Q 697 4328 969 4595 \n", "Q 1241 4863 1831 4863 \n", "L 2375 4863 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-28\" d=\"M 1984 4856 \n", "Q 1566 4138 1362 3434 \n", "Q 1159 2731 1159 2009 \n", "Q 1159 1288 1364 580 \n", "Q 1569 -128 1984 -844 \n", "L 1484 -844 \n", "Q 1016 -109 783 600 \n", "Q 550 1309 550 2009 \n", "Q 550 2706 781 3412 \n", "Q 1013 4119 1484 4856 \n", "L 1984 4856 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-29\" d=\"M 513 4856 \n", "L 1013 4856 \n", "Q 1481 4119 1714 3412 \n", "Q 1947 2706 1947 2009 \n", "Q 1947 1309 1714 600 \n", "Q 1481 -109 1013 -844 \n", "L 513 -844 \n", "Q 928 -128 1133 580 \n", "Q 1338 1288 1338 2009 \n", "Q 1338 2731 1133 3434 \n", "Q 928 4138 513 4856 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "      </defs>\n", "      <use xlink:href=\"#DejaVuSans-66\"/>\n", "      <use xlink:href=\"#DejaVuSans-28\" x=\"35.205078\"/>\n", "      <use xlink:href=\"#DejaVuSans-78\" x=\"74.21875\"/>\n", "      <use xlink:href=\"#DejaVuSans-29\" x=\"133.398438\"/>\n", "     </g>\n", "    </g>\n", "   </g>\n", "   <g id=\"line2d_23\">\n", "    <path d=\"M 55.842898 13.5 \n", "L 60.459075 26.263287 \n", "L 64.89772 37.893111 \n", "L 69.247573 48.679063 \n", "L 73.508668 58.65827 \n", "L 77.680989 67.866983 \n", "L 81.764535 76.340736 \n", "L 85.670535 83.947104 \n", "L 89.487765 90.909241 \n", "L 93.216216 97.259632 \n", "L 96.855897 103.030055 \n", "L 100.406807 108.251496 \n", "L 103.868946 112.954199 \n", "L 107.242311 117.167639 \n", "L 110.526898 120.920544 \n", "L 113.722716 124.240896 \n", "L 116.82976 127.155905 \n", "L 119.848034 129.692035 \n", "L 122.866306 131.93685 \n", "L 125.795806 133.837055 \n", "L 128.636533 135.4176 \n", "L 131.388489 136.702674 \n", "L 134.140443 137.745576 \n", "L 136.803625 138.524256 \n", "L 139.466807 139.076136 \n", "L 142.129989 139.401216 \n", "L 144.79317 139.499496 \n", "L 147.456352 139.370976 \n", "L 150.119534 139.015656 \n", "L 152.782716 138.433536 \n", "L 155.445898 137.624616 \n", "L 158.197852 136.550466 \n", "L 160.949807 135.234144 \n", "L 163.790535 133.621344 \n", "L 166.631262 131.750496 \n", "L 169.560761 129.550915 \n", "L 172.579033 126.997652 \n", "L 175.597307 124.153073 \n", "L 178.704352 120.920544 \n", "L 181.900169 117.273603 \n", "L 185.184763 113.185025 \n", "L 188.558128 108.626847 \n", "L 192.020263 103.570341 \n", "L 195.571168 97.986027 \n", "L 199.210853 91.843647 \n", "L 202.939308 85.112224 \n", "L 206.756534 77.76 \n", "L 210.662535 69.754465 \n", "L 214.65731 61.062347 \n", "L 218.829622 51.43911 \n", "L 223.090717 41.036545 \n", "L 227.440579 29.81839 \n", "L 231.879215 17.747588 \n", "L 233.299578 13.751881 \n", "L 233.299578 13.751881 \n", "\" clip-path=\"url(#p5c4ed29721)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_24\">\n", "    <path d=\"M 233.388352 13.5 \n", "L 224.51108 37.44 \n", "L 216.521534 56.8314 \n", "L 209.330943 72.538434 \n", "L 202.859411 85.261132 \n", "L 197.035033 95.566517 \n", "L 191.793092 103.913878 \n", "L 187.075345 110.675242 \n", "L 182.829373 116.151946 \n", "L 179.007998 120.588076 \n", "L 175.568761 124.181342 \n", "\" clip-path=\"url(#p5c4ed29721)\" style=\"fill: none; stroke: #ff7f0e; stroke-width: 1.5; stroke-linecap: square\"/>\n", "    <defs>\n", "     <path id=\"m9711584f4d\" d=\"M 0 3 \n", "C 0.795609 3 1.55874 2.683901 2.12132 2.12132 \n", "C 2.683901 1.55874 3 0.795609 3 0 \n", "C 3 -0.795609 2.683901 -1.55874 2.12132 -2.12132 \n", "C 1.55874 -2.683901 0.795609 -3 0 -3 \n", "C -0.795609 -3 -1.55874 -2.683901 -2.12132 -2.12132 \n", "C -2.683901 -1.55874 -3 -0.795609 -3 0 \n", "C -3 0.795609 -2.683901 1.55874 -2.12132 2.12132 \n", "C -1.55874 2.683901 -0.795609 3 0 3 \n", "z\n", "\" style=\"stroke: #ff7f0e\"/>\n", "    </defs>\n", "    <g clip-path=\"url(#p5c4ed29721)\">\n", "     <use xlink:href=\"#m9711584f4d\" x=\"233.388352\" y=\"13.5\" style=\"fill: #ff7f0e; stroke: #ff7f0e\"/>\n", "     <use xlink:href=\"#m9711584f4d\" x=\"224.51108\" y=\"37.44\" style=\"fill: #ff7f0e; stroke: #ff7f0e\"/>\n", "     <use xlink:href=\"#m9711584f4d\" x=\"216.521534\" y=\"56.8314\" style=\"fill: #ff7f0e; stroke: #ff7f0e\"/>\n", "     <use xlink:href=\"#m9711584f4d\" x=\"209.330943\" y=\"72.538434\" style=\"fill: #ff7f0e; stroke: #ff7f0e\"/>\n", "     <use xlink:href=\"#m9711584f4d\" x=\"202.859411\" y=\"85.261132\" style=\"fill: #ff7f0e; stroke: #ff7f0e\"/>\n", "     <use xlink:href=\"#m9711584f4d\" x=\"197.035033\" y=\"95.566517\" style=\"fill: #ff7f0e; stroke: #ff7f0e\"/>\n", "     <use xlink:href=\"#m9711584f4d\" x=\"191.793092\" y=\"103.913878\" style=\"fill: #ff7f0e; stroke: #ff7f0e\"/>\n", "     <use xlink:href=\"#m9711584f4d\" x=\"187.075345\" y=\"110.675242\" style=\"fill: #ff7f0e; stroke: #ff7f0e\"/>\n", "     <use xlink:href=\"#m9711584f4d\" x=\"182.829373\" y=\"116.151946\" style=\"fill: #ff7f0e; stroke: #ff7f0e\"/>\n", "     <use xlink:href=\"#m9711584f4d\" x=\"179.007998\" y=\"120.588076\" style=\"fill: #ff7f0e; stroke: #ff7f0e\"/>\n", "     <use xlink:href=\"#m9711584f4d\" x=\"175.568761\" y=\"124.181342\" style=\"fill: #ff7f0e; stroke: #ff7f0e\"/>\n", "    </g>\n", "   </g>\n", "   <g id=\"patch_3\">\n", "    <path d=\"M 46.**********.8 \n", "L 46.965625 7.2 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_4\">\n", "    <path d=\"M 242.**********.8 \n", "L 242.265625 7.2 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_5\">\n", "    <path d=\"M 46.**********.8 \n", "L 242.**********.8 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_6\">\n", "    <path d=\"M 46.965625 7.2 \n", "L 242.265625 7.2 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "  </g>\n", " </g>\n", " <defs>\n", "  <clipPath id=\"p5c4ed29721\">\n", "   <rect x=\"46.965625\" y=\"7.2\" width=\"195.3\" height=\"138.6\"/>\n", "  </clipPath>\n", " </defs>\n", "</svg>\n"], "text/plain": ["<Figure size 350x250 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["show_trace(gd(0.05, f_grad), f)"]}, {"cell_type": "markdown", "id": "0ca17182", "metadata": {"origin_pos": 14}, "source": ["相反，如果我们使用过高的学习率，$\\left|\\eta f'(x)\\right|$对于一阶泰勒展开式可能太大。\n", "也就是说， :eqref:`gd-taylor`中的$\\mathcal{O}(\\eta^2 f'^2(x))$可能变得显著了。\n", "在这种情况下，$x$的迭代不能保证降低$f(x)$的值。\n", "例如，当学习率为$\\eta=1.1$时，$x$超出了最优解$x=0$并逐渐发散。\n"]}, {"cell_type": "code", "execution_count": 6, "id": "df2905a1", "metadata": {"execution": {"iopub.execute_input": "2022-12-07T16:54:13.739792Z", "iopub.status.busy": "2022-12-07T16:54:13.739225Z", "iopub.status.idle": "2022-12-07T16:54:14.138097Z", "shell.execute_reply": "2022-12-07T16:54:14.137304Z"}, "origin_pos": 15, "tab": ["pytorch"]}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["epoch 10, x: 61.917364\n"]}, {"data": {"image/svg+xml": ["<?xml version=\"1.0\" encoding=\"utf-8\" standalone=\"no\"?>\n", "<!DOCTYPE svg PUBLIC \"-//W3C//DTD SVG 1.1//EN\"\n", "  \"http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd\">\n", "<svg xmlns:xlink=\"http://www.w3.org/1999/xlink\" width=\"255.828125pt\" height=\"186.319088pt\" viewBox=\"0 0 255.**********.319088\" xmlns=\"http://www.w3.org/2000/svg\" version=\"1.1\">\n", " <metadata>\n", "  <rdf:RDF xmlns:dc=\"http://purl.org/dc/elements/1.1/\" xmlns:cc=\"http://creativecommons.org/ns#\" xmlns:rdf=\"http://www.w3.org/1999/02/22-rdf-syntax-ns#\">\n", "   <cc:Work>\n", "    <dc:type rdf:resource=\"http://purl.org/dc/dcmitype/StillImage\"/>\n", "    <dc:date>2022-12-07T16:54:14.103271</dc:date>\n", "    <dc:format>image/svg+xml</dc:format>\n", "    <dc:creator>\n", "     <cc:Agent>\n", "      <dc:title>Matplotlib v3.5.1, https://matplotlib.org/</dc:title>\n", "     </cc:Agent>\n", "    </dc:creator>\n", "   </cc:Work>\n", "  </rdf:RDF>\n", " </metadata>\n", " <defs>\n", "  <style type=\"text/css\">*{stroke-linejoin: round; stroke-linecap: butt}</style>\n", " </defs>\n", " <g id=\"figure_1\">\n", "  <g id=\"patch_1\">\n", "   <path d=\"M 0 186.319088 \n", "L 255.**********.319088 \n", "L 255.828125 0 \n", "L 0 0 \n", "z\n", "\" style=\"fill: #ffffff\"/>\n", "  </g>\n", "  <g id=\"axes_1\">\n", "   <g id=\"patch_2\">\n", "    <path d=\"M 53.**********.762838 \n", "L 248.**********.762838 \n", "L 248.628125 10.162838 \n", "L 53.328125 10.162838 \n", "z\n", "\" style=\"fill: #ffffff\"/>\n", "   </g>\n", "   <g id=\"matplotlib.axis_1\">\n", "    <g id=\"xtick_1\">\n", "     <g id=\"line2d_1\">\n", "      <path d=\"M 79.291672 148.762838 \n", "L 79.291672 10.162838 \n", "\" clip-path=\"url(#pe0d040eef7)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_2\">\n", "      <defs>\n", "       <path id=\"m1b2cf87e41\" d=\"M 0 0 \n", "L 0 3.5 \n", "\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </defs>\n", "      <g>\n", "       <use xlink:href=\"#m1b2cf87e41\" x=\"79.291672\" y=\"148.762838\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_1\">\n", "      <!-- −50 -->\n", "      <g transform=\"translate(68.739328 163.361276)scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-2212\" d=\"M 678 2272 \n", "L 4684 2272 \n", "L 4684 1741 \n", "L 678 1741 \n", "L 678 2272 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "        <path id=\"DejaVuSans-35\" d=\"M 691 4666 \n", "L 3169 4666 \n", "L 3169 4134 \n", "L 1269 4134 \n", "L 1269 2991 \n", "Q 1406 3038 1543 3061 \n", "Q 1681 3084 1819 3084 \n", "Q 2600 3084 3056 2656 \n", "Q 3513 2228 3513 1497 \n", "Q 3513 744 3044 326 \n", "Q 2575 -91 1722 -91 \n", "Q 1428 -91 1123 -41 \n", "Q 819 9 494 109 \n", "L 494 744 \n", "Q 775 591 1075 516 \n", "Q 1375 441 1709 441 \n", "Q 2250 441 2565 725 \n", "Q 2881 1009 2881 1497 \n", "Q 2881 1984 2565 2268 \n", "Q 2250 2553 1709 2553 \n", "Q 1456 2553 1204 2497 \n", "Q 953 2441 691 2322 \n", "L 691 4666 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "        <path id=\"DejaVuSans-30\" d=\"M 2034 4250 \n", "Q 1547 4250 1301 3770 \n", "Q 1056 3291 1056 2328 \n", "Q 1056 1369 1301 889 \n", "Q 1547 409 2034 409 \n", "Q 2525 409 2770 889 \n", "Q 3016 1369 3016 2328 \n", "Q 3016 3291 2770 3770 \n", "Q 2525 4250 2034 4250 \n", "z\n", "M 2034 4750 \n", "Q 2819 4750 3233 4129 \n", "Q 3647 3509 3647 2328 \n", "Q 3647 1150 3233 529 \n", "Q 2819 -91 2034 -91 \n", "Q 1250 -91 836 529 \n", "Q 422 1150 422 2328 \n", "Q 422 3509 836 4129 \n", "Q 1250 4750 2034 4750 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-2212\"/>\n", "       <use xlink:href=\"#DejaVuSans-35\" x=\"83.789062\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"147.412109\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_2\">\n", "     <g id=\"line2d_3\">\n", "      <path d=\"M 115.134899 148.762838 \n", "L 115.134899 10.162838 \n", "\" clip-path=\"url(#pe0d040eef7)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_4\">\n", "      <g>\n", "       <use xlink:href=\"#m1b2cf87e41\" x=\"115.134899\" y=\"148.762838\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_2\">\n", "      <!-- −25 -->\n", "      <g transform=\"translate(104.582555 163.361276)scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-32\" d=\"M 1228 531 \n", "L 3431 531 \n", "L 3431 0 \n", "L 469 0 \n", "L 469 531 \n", "Q 828 903 1448 1529 \n", "Q 2069 2156 2228 2338 \n", "Q 2531 2678 2651 2914 \n", "Q 2772 3150 2772 3378 \n", "Q 2772 3750 2511 3984 \n", "Q 2250 4219 1831 4219 \n", "Q 1534 4219 1204 4116 \n", "Q 875 4013 500 3803 \n", "L 500 4441 \n", "Q 881 4594 1212 4672 \n", "Q 1544 4750 1819 4750 \n", "Q 2544 4750 2975 4387 \n", "Q 3406 4025 3406 3419 \n", "Q 3406 3131 3298 2873 \n", "Q 3191 2616 2906 2266 \n", "Q 2828 2175 2409 1742 \n", "Q 1991 1309 1228 531 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-2212\"/>\n", "       <use xlink:href=\"#DejaVuSans-32\" x=\"83.789062\"/>\n", "       <use xlink:href=\"#DejaVuSans-35\" x=\"147.412109\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_3\">\n", "     <g id=\"line2d_5\">\n", "      <path d=\"M 150.978126 148.762838 \n", "L 150.978126 10.162838 \n", "\" clip-path=\"url(#pe0d040eef7)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_6\">\n", "      <g>\n", "       <use xlink:href=\"#m1b2cf87e41\" x=\"150.978126\" y=\"148.762838\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_3\">\n", "      <!-- 0 -->\n", "      <g transform=\"translate(147.796876 163.361276)scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_4\">\n", "     <g id=\"line2d_7\">\n", "      <path d=\"M 186.821353 148.762838 \n", "L 186.821353 10.162838 \n", "\" clip-path=\"url(#pe0d040eef7)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_8\">\n", "      <g>\n", "       <use xlink:href=\"#m1b2cf87e41\" x=\"186.821353\" y=\"148.762838\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_4\">\n", "      <!-- 25 -->\n", "      <g transform=\"translate(180.458853 163.361276)scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-32\"/>\n", "       <use xlink:href=\"#DejaVuSans-35\" x=\"63.623047\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_5\">\n", "     <g id=\"line2d_9\">\n", "      <path d=\"M 222.664581 148.762838 \n", "L 222.664581 10.162838 \n", "\" clip-path=\"url(#pe0d040eef7)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_10\">\n", "      <g>\n", "       <use xlink:href=\"#m1b2cf87e41\" x=\"222.664581\" y=\"148.762838\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_5\">\n", "      <!-- 50 -->\n", "      <g transform=\"translate(216.302081 163.361276)scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-35\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"63.623047\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"text_6\">\n", "     <!-- x -->\n", "     <g transform=\"translate(148.01875 177.039401)scale(0.1 -0.1)\">\n", "      <defs>\n", "       <path id=\"DejaVuSans-78\" d=\"M 3513 3500 \n", "L 2247 1797 \n", "L 3578 0 \n", "L 2900 0 \n", "L 1881 1375 \n", "L 863 0 \n", "L 184 0 \n", "L 1544 1831 \n", "L 300 3500 \n", "L 978 3500 \n", "L 1906 2253 \n", "L 2834 3500 \n", "L 3513 3500 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "      </defs>\n", "      <use xlink:href=\"#DejaVuSans-78\"/>\n", "     </g>\n", "    </g>\n", "   </g>\n", "   <g id=\"matplotlib.axis_2\">\n", "    <g id=\"ytick_1\">\n", "     <g id=\"line2d_11\">\n", "      <path d=\"M 53.328125 142.462839 \n", "L 248.628125 142.462839 \n", "\" clip-path=\"url(#pe0d040eef7)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_12\">\n", "      <defs>\n", "       <path id=\"m97156295db\" d=\"M 0 0 \n", "L -3.5 0 \n", "\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </defs>\n", "      <g>\n", "       <use xlink:href=\"#m97156295db\" x=\"53.328125\" y=\"142.462839\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_7\">\n", "      <!-- 0 -->\n", "      <g transform=\"translate(39.965625 146.262057)scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_2\">\n", "     <g id=\"line2d_13\">\n", "      <path d=\"M 53.328125 109.596934 \n", "L 248.628125 109.596934 \n", "\" clip-path=\"url(#pe0d040eef7)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_14\">\n", "      <g>\n", "       <use xlink:href=\"#m97156295db\" x=\"53.328125\" y=\"109.596934\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_8\">\n", "      <!-- 1000 -->\n", "      <g transform=\"translate(20.878125 113.396152)scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-31\" d=\"M 794 531 \n", "L 1825 531 \n", "L 1825 4091 \n", "L 703 3866 \n", "L 703 4441 \n", "L 1819 4666 \n", "L 2450 4666 \n", "L 2450 531 \n", "L 3481 531 \n", "L 3481 0 \n", "L 794 0 \n", "L 794 531 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-31\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"127.246094\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"190.869141\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_3\">\n", "     <g id=\"line2d_15\">\n", "      <path d=\"M 53.328125 76.731029 \n", "L 248.628125 76.731029 \n", "\" clip-path=\"url(#pe0d040eef7)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_16\">\n", "      <g>\n", "       <use xlink:href=\"#m97156295db\" x=\"53.328125\" y=\"76.731029\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_9\">\n", "      <!-- 2000 -->\n", "      <g transform=\"translate(20.878125 80.530247)scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-32\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"127.246094\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"190.869141\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_4\">\n", "     <g id=\"line2d_17\">\n", "      <path d=\"M 53.328125 43.865124 \n", "L 248.628125 43.865124 \n", "\" clip-path=\"url(#pe0d040eef7)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_18\">\n", "      <g>\n", "       <use xlink:href=\"#m97156295db\" x=\"53.328125\" y=\"43.865124\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_10\">\n", "      <!-- 3000 -->\n", "      <g transform=\"translate(20.878125 47.664342)scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-33\" d=\"M 2597 2516 \n", "Q 3050 2419 3304 2112 \n", "Q 3559 1806 3559 1356 \n", "Q 3559 666 3084 287 \n", "Q 2609 -91 1734 -91 \n", "Q 1441 -91 1130 -33 \n", "Q 819 25 488 141 \n", "L 488 750 \n", "Q 750 597 1062 519 \n", "Q 1375 441 1716 441 \n", "Q 2309 441 2620 675 \n", "Q 2931 909 2931 1356 \n", "Q 2931 1769 2642 2001 \n", "Q 2353 2234 1838 2234 \n", "L 1294 2234 \n", "L 1294 2753 \n", "L 1863 2753 \n", "Q 2328 2753 2575 2939 \n", "Q 2822 3125 2822 3475 \n", "Q 2822 3834 2567 4026 \n", "Q 2313 4219 1838 4219 \n", "Q 1578 4219 1281 4162 \n", "Q 984 4106 628 3988 \n", "L 628 4550 \n", "Q 988 4650 1302 4700 \n", "Q 1616 4750 1894 4750 \n", "Q 2613 4750 3031 4423 \n", "Q 3450 4097 3450 3541 \n", "Q 3450 3153 3228 2886 \n", "Q 3006 2619 2597 2516 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-33\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"127.246094\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"190.869141\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_5\">\n", "     <g id=\"line2d_19\">\n", "      <path d=\"M 53.328125 10.999219 \n", "L 248.628125 10.999219 \n", "\" clip-path=\"url(#pe0d040eef7)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_20\">\n", "      <g>\n", "       <use xlink:href=\"#m97156295db\" x=\"53.328125\" y=\"10.999219\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_11\">\n", "      <!-- 4000 -->\n", "      <g transform=\"translate(20.878125 14.798437)scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-34\" d=\"M 2419 4116 \n", "L 825 1625 \n", "L 2419 1625 \n", "L 2419 4116 \n", "z\n", "M 2253 4666 \n", "L 3047 4666 \n", "L 3047 1625 \n", "L 3713 1625 \n", "L 3713 1100 \n", "L 3047 1100 \n", "L 3047 0 \n", "L 2419 0 \n", "L 2419 1100 \n", "L 313 1100 \n", "L 313 1709 \n", "L 2253 4666 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-34\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"127.246094\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"190.869141\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"text_12\">\n", "     <!-- f(x) -->\n", "     <g transform=\"translate(14.798437 88.083932)rotate(-90)scale(0.1 -0.1)\">\n", "      <defs>\n", "       <path id=\"DejaVuSans-66\" d=\"M 2375 4863 \n", "L 2375 4384 \n", "L 1825 4384 \n", "Q 1516 4384 1395 4259 \n", "Q 1275 4134 1275 3809 \n", "L 1275 3500 \n", "L 2222 3500 \n", "L 2222 3053 \n", "L 1275 3053 \n", "L 1275 0 \n", "L 697 0 \n", "L 697 3053 \n", "L 147 3053 \n", "L 147 3500 \n", "L 697 3500 \n", "L 697 3744 \n", "Q 697 4328 969 4595 \n", "Q 1241 4863 1831 4863 \n", "L 2375 4863 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-28\" d=\"M 1984 4856 \n", "Q 1566 4138 1362 3434 \n", "Q 1159 2731 1159 2009 \n", "Q 1159 1288 1364 580 \n", "Q 1569 -128 1984 -844 \n", "L 1484 -844 \n", "Q 1016 -109 783 600 \n", "Q 550 1309 550 2009 \n", "Q 550 2706 781 3412 \n", "Q 1013 4119 1484 4856 \n", "L 1984 4856 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-29\" d=\"M 513 4856 \n", "L 1013 4856 \n", "Q 1481 4119 1714 3412 \n", "Q 1947 2706 1947 2009 \n", "Q 1947 1309 1714 600 \n", "Q 1481 -109 1013 -844 \n", "L 513 -844 \n", "Q 928 -128 1133 580 \n", "Q 1338 1288 1338 2009 \n", "Q 1338 2731 1133 3434 \n", "Q 928 4138 513 4856 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "      </defs>\n", "      <use xlink:href=\"#DejaVuSans-66\"/>\n", "      <use xlink:href=\"#DejaVuSans-28\" x=\"35.205078\"/>\n", "      <use xlink:href=\"#DejaVuSans-78\" x=\"74.21875\"/>\n", "      <use xlink:href=\"#DejaVuSans-29\" x=\"133.398438\"/>\n", "     </g>\n", "    </g>\n", "   </g>\n", "   <g id=\"line2d_21\">\n", "    <path d=\"M 62.205398 16.462838 \n", "L 66.778997 29.11151 \n", "L 71.237895 40.79903 \n", "L 75.596433 51.609024 \n", "L 79.854605 61.583441 \n", "L 84.012416 70.763299 \n", "L 88.055531 79.159788 \n", "L 92.012627 86.871468 \n", "L 95.869361 93.905735 \n", "L 99.62573 100.29971 \n", "L 103.281738 106.089566 \n", "L 106.837388 111.310506 \n", "L 110.307013 116.015406 \n", "L 113.676274 120.2158 \n", "L 116.959515 123.959735 \n", "L 120.15673 127.274299 \n", "L 123.28226 130.198579 \n", "L 126.321764 132.742768 \n", "L 129.289584 134.941899 \n", "L 132.200055 136.824991 \n", "L 135.053176 138.408056 \n", "L 137.848947 139.706792 \n", "L 140.58737 140.736581 \n", "L 143.297117 141.519543 \n", "L 145.978191 142.063133 \n", "L 148.63059 142.374726 \n", "L 151.268651 142.461489 \n", "L 153.906712 142.32571 \n", "L 156.544774 141.96739 \n", "L 159.197173 141.382763 \n", "L 161.878246 140.563187 \n", "L 164.587994 139.501288 \n", "L 167.340754 138.182112 \n", "L 170.136526 136.594302 \n", "L 172.989646 134.716233 \n", "L 175.900119 132.532206 \n", "L 178.882275 130.013423 \n", "L 181.936117 127.139375 \n", "L 185.061647 123.889059 \n", "L 188.273199 120.223887 \n", "L 191.570775 116.117354 \n", "L 194.940039 111.562407 \n", "L 198.409666 106.492389 \n", "L 201.965314 100.89729 \n", "L 205.606982 94.747763 \n", "L 209.349015 87.986987 \n", "L 213.177075 80.607456 \n", "L 217.105489 72.547286 \n", "L 221.13427 63.768623 \n", "L 225.263406 54.232704 \n", "L 229.478573 43.93575 \n", "L 233.82277 32.729078 \n", "L 238.267334 20.638935 \n", "L 239.744073 16.482088 \n", "L 239.744073 16.482088 \n", "\" clip-path=\"url(#pe0d040eef7)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_22\">\n", "    <path d=\"M 165.315417 139.176248 \n", "L 133.773377 137.730148 \n", "L 171.623825 135.647765 \n", "L 126.203288 132.649132 \n", "L 180.707933 128.331101 \n", "L 115.302359 122.113137 \n", "L 193.789047 113.159268 \n", "L 99.605021 100.265696 \n", "L 212.625853 81.698954 \n", "L 77.000855 54.962844 \n", "L 239.750852 16.462847 \n", "\" clip-path=\"url(#pe0d040eef7)\" style=\"fill: none; stroke: #ff7f0e; stroke-width: 1.5; stroke-linecap: square\"/>\n", "    <defs>\n", "     <path id=\"m8fee759a27\" d=\"M 0 3 \n", "C 0.795609 3 1.55874 2.683901 2.12132 2.12132 \n", "C 2.683901 1.55874 3 0.795609 3 0 \n", "C 3 -0.795609 2.683901 -1.55874 2.12132 -2.12132 \n", "C 1.55874 -2.683901 0.795609 -3 0 -3 \n", "C -0.795609 -3 -1.55874 -2.683901 -2.12132 -2.12132 \n", "C -2.683901 -1.55874 -3 -0.795609 -3 0 \n", "C -3 0.795609 -2.683901 1.55874 -2.12132 2.12132 \n", "C -1.55874 2.683901 -0.795609 3 0 3 \n", "z\n", "\" style=\"stroke: #ff7f0e\"/>\n", "    </defs>\n", "    <g clip-path=\"url(#pe0d040eef7)\">\n", "     <use xlink:href=\"#m8fee759a27\" x=\"165.315417\" y=\"139.176248\" style=\"fill: #ff7f0e; stroke: #ff7f0e\"/>\n", "     <use xlink:href=\"#m8fee759a27\" x=\"133.773377\" y=\"137.730148\" style=\"fill: #ff7f0e; stroke: #ff7f0e\"/>\n", "     <use xlink:href=\"#m8fee759a27\" x=\"171.623825\" y=\"135.647765\" style=\"fill: #ff7f0e; stroke: #ff7f0e\"/>\n", "     <use xlink:href=\"#m8fee759a27\" x=\"126.203288\" y=\"132.649132\" style=\"fill: #ff7f0e; stroke: #ff7f0e\"/>\n", "     <use xlink:href=\"#m8fee759a27\" x=\"180.707933\" y=\"128.331101\" style=\"fill: #ff7f0e; stroke: #ff7f0e\"/>\n", "     <use xlink:href=\"#m8fee759a27\" x=\"115.302359\" y=\"122.113137\" style=\"fill: #ff7f0e; stroke: #ff7f0e\"/>\n", "     <use xlink:href=\"#m8fee759a27\" x=\"193.789047\" y=\"113.159268\" style=\"fill: #ff7f0e; stroke: #ff7f0e\"/>\n", "     <use xlink:href=\"#m8fee759a27\" x=\"99.605021\" y=\"100.265696\" style=\"fill: #ff7f0e; stroke: #ff7f0e\"/>\n", "     <use xlink:href=\"#m8fee759a27\" x=\"212.625853\" y=\"81.698954\" style=\"fill: #ff7f0e; stroke: #ff7f0e\"/>\n", "     <use xlink:href=\"#m8fee759a27\" x=\"77.000855\" y=\"54.962844\" style=\"fill: #ff7f0e; stroke: #ff7f0e\"/>\n", "     <use xlink:href=\"#m8fee759a27\" x=\"239.750852\" y=\"16.462847\" style=\"fill: #ff7f0e; stroke: #ff7f0e\"/>\n", "    </g>\n", "   </g>\n", "   <g id=\"patch_3\">\n", "    <path d=\"M 53.**********.762838 \n", "L 53.328125 10.162838 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_4\">\n", "    <path d=\"M 248.**********.762838 \n", "L 248.628125 10.162838 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_5\">\n", "    <path d=\"M 53.**********.762838 \n", "L 248.**********.762838 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_6\">\n", "    <path d=\"M 53.328125 10.162838 \n", "L 248.628125 10.162838 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "  </g>\n", " </g>\n", " <defs>\n", "  <clipPath id=\"pe0d040eef7\">\n", "   <rect x=\"53.328125\" y=\"10.162838\" width=\"195.3\" height=\"138.6\"/>\n", "  </clipPath>\n", " </defs>\n", "</svg>\n"], "text/plain": ["<Figure size 350x250 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["show_trace(gd(1.1, f_grad), f)"]}, {"cell_type": "markdown", "id": "1f44d08b", "metadata": {"origin_pos": 16}, "source": ["### 局部最小值\n", "\n", "为了演示非凸函数的梯度下降，考虑函数$f(x) = x \\cdot \\cos(cx)$，其中$c$为某常数。\n", "这个函数有无穷多个局部最小值。\n", "根据我们选择的学习率，我们最终可能只会得到许多解的一个。\n", "下面的例子说明了（不切实际的）高学习率如何导致较差的局部最小值。\n"]}, {"cell_type": "code", "execution_count": 7, "id": "5ab8db8d", "metadata": {"execution": {"iopub.execute_input": "2022-12-07T16:54:14.143265Z", "iopub.status.busy": "2022-12-07T16:54:14.142714Z", "iopub.status.idle": "2022-12-07T16:54:14.330988Z", "shell.execute_reply": "2022-12-07T16:54:14.330135Z"}, "origin_pos": 17, "tab": ["pytorch"]}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["epoch 10, x: -1.528166\n"]}, {"data": {"image/svg+xml": ["<?xml version=\"1.0\" encoding=\"utf-8\" standalone=\"no\"?>\n", "<!DOCTYPE svg PUBLIC \"-//W3C//DTD SVG 1.1//EN\"\n", "  \"http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd\">\n", "<svg xmlns:xlink=\"http://www.w3.org/1999/xlink\" width=\"245.120313pt\" height=\"183.35625pt\" viewBox=\"0 0 245.**********.35625\" xmlns=\"http://www.w3.org/2000/svg\" version=\"1.1\">\n", " <metadata>\n", "  <rdf:RDF xmlns:dc=\"http://purl.org/dc/elements/1.1/\" xmlns:cc=\"http://creativecommons.org/ns#\" xmlns:rdf=\"http://www.w3.org/1999/02/22-rdf-syntax-ns#\">\n", "   <cc:Work>\n", "    <dc:type rdf:resource=\"http://purl.org/dc/dcmitype/StillImage\"/>\n", "    <dc:date>2022-12-07T16:54:14.300746</dc:date>\n", "    <dc:format>image/svg+xml</dc:format>\n", "    <dc:creator>\n", "     <cc:Agent>\n", "      <dc:title>Matplotlib v3.5.1, https://matplotlib.org/</dc:title>\n", "     </cc:Agent>\n", "    </dc:creator>\n", "   </cc:Work>\n", "  </rdf:RDF>\n", " </metadata>\n", " <defs>\n", "  <style type=\"text/css\">*{stroke-linejoin: round; stroke-linecap: butt}</style>\n", " </defs>\n", " <g id=\"figure_1\">\n", "  <g id=\"patch_1\">\n", "   <path d=\"M 0 183.35625 \n", "L 245.**********.35625 \n", "L 245.120313 0 \n", "L 0 0 \n", "z\n", "\" style=\"fill: #ffffff\"/>\n", "  </g>\n", "  <g id=\"axes_1\">\n", "   <g id=\"patch_2\">\n", "    <path d=\"M 42.**********.8 \n", "L 237.**********.8 \n", "L 237.920313 7.2 \n", "L 42.620312 7.2 \n", "z\n", "\" style=\"fill: #ffffff\"/>\n", "   </g>\n", "   <g id=\"matplotlib.axis_1\">\n", "    <g id=\"xtick_1\">\n", "     <g id=\"line2d_1\">\n", "      <path d=\"M 51.497585 145.8 \n", "L 51.497585 7.2 \n", "\" clip-path=\"url(#p51ac05d166)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_2\">\n", "      <defs>\n", "       <path id=\"mf46c394d00\" d=\"M 0 0 \n", "L 0 3.5 \n", "\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </defs>\n", "      <g>\n", "       <use xlink:href=\"#mf46c394d00\" x=\"51.497585\" y=\"145.8\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_1\">\n", "      <!-- −10 -->\n", "      <g transform=\"translate(40.945241 160.398438)scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-2212\" d=\"M 678 2272 \n", "L 4684 2272 \n", "L 4684 1741 \n", "L 678 1741 \n", "L 678 2272 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "        <path id=\"DejaVuSans-31\" d=\"M 794 531 \n", "L 1825 531 \n", "L 1825 4091 \n", "L 703 3866 \n", "L 703 4441 \n", "L 1819 4666 \n", "L 2450 4666 \n", "L 2450 531 \n", "L 3481 531 \n", "L 3481 0 \n", "L 794 0 \n", "L 794 531 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "        <path id=\"DejaVuSans-30\" d=\"M 2034 4250 \n", "Q 1547 4250 1301 3770 \n", "Q 1056 3291 1056 2328 \n", "Q 1056 1369 1301 889 \n", "Q 1547 409 2034 409 \n", "Q 2525 409 2770 889 \n", "Q 3016 1369 3016 2328 \n", "Q 3016 3291 2770 3770 \n", "Q 2525 4250 2034 4250 \n", "z\n", "M 2034 4750 \n", "Q 2819 4750 3233 4129 \n", "Q 3647 3509 3647 2328 \n", "Q 3647 1150 3233 529 \n", "Q 2819 -91 2034 -91 \n", "Q 1250 -91 836 529 \n", "Q 422 1150 422 2328 \n", "Q 422 3509 836 4129 \n", "Q 1250 4750 2034 4750 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-2212\"/>\n", "       <use xlink:href=\"#DejaVuSans-31\" x=\"83.789062\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"147.412109\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_2\">\n", "     <g id=\"line2d_3\">\n", "      <path d=\"M 95.883949 145.8 \n", "L 95.883949 7.2 \n", "\" clip-path=\"url(#p51ac05d166)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_4\">\n", "      <g>\n", "       <use xlink:href=\"#mf46c394d00\" x=\"95.883949\" y=\"145.8\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_2\">\n", "      <!-- −5 -->\n", "      <g transform=\"translate(88.512855 160.398438)scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-35\" d=\"M 691 4666 \n", "L 3169 4666 \n", "L 3169 4134 \n", "L 1269 4134 \n", "L 1269 2991 \n", "Q 1406 3038 1543 3061 \n", "Q 1681 3084 1819 3084 \n", "Q 2600 3084 3056 2656 \n", "Q 3513 2228 3513 1497 \n", "Q 3513 744 3044 326 \n", "Q 2575 -91 1722 -91 \n", "Q 1428 -91 1123 -41 \n", "Q 819 9 494 109 \n", "L 494 744 \n", "Q 775 591 1075 516 \n", "Q 1375 441 1709 441 \n", "Q 2250 441 2565 725 \n", "Q 2881 1009 2881 1497 \n", "Q 2881 1984 2565 2268 \n", "Q 2250 2553 1709 2553 \n", "Q 1456 2553 1204 2497 \n", "Q 953 2441 691 2322 \n", "L 691 4666 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-2212\"/>\n", "       <use xlink:href=\"#DejaVuSans-35\" x=\"83.789062\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_3\">\n", "     <g id=\"line2d_5\">\n", "      <path d=\"M 140.270312 145.8 \n", "L 140.270312 7.2 \n", "\" clip-path=\"url(#p51ac05d166)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_6\">\n", "      <g>\n", "       <use xlink:href=\"#mf46c394d00\" x=\"140.270312\" y=\"145.8\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_3\">\n", "      <!-- 0 -->\n", "      <g transform=\"translate(137.089062 160.398438)scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_4\">\n", "     <g id=\"line2d_7\">\n", "      <path d=\"M 184.656676 145.8 \n", "L 184.656676 7.2 \n", "\" clip-path=\"url(#p51ac05d166)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_8\">\n", "      <g>\n", "       <use xlink:href=\"#mf46c394d00\" x=\"184.656676\" y=\"145.8\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_4\">\n", "      <!-- 5 -->\n", "      <g transform=\"translate(181.475426 160.398438)scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-35\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_5\">\n", "     <g id=\"line2d_9\">\n", "      <path d=\"M 229.04304 145.8 \n", "L 229.04304 7.2 \n", "\" clip-path=\"url(#p51ac05d166)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_10\">\n", "      <g>\n", "       <use xlink:href=\"#mf46c394d00\" x=\"229.04304\" y=\"145.8\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_5\">\n", "      <!-- 10 -->\n", "      <g transform=\"translate(222.68054 160.398438)scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-31\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"63.623047\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"text_6\">\n", "     <!-- x -->\n", "     <g transform=\"translate(137.310937 174.076563)scale(0.1 -0.1)\">\n", "      <defs>\n", "       <path id=\"DejaVuSans-78\" d=\"M 3513 3500 \n", "L 2247 1797 \n", "L 3578 0 \n", "L 2900 0 \n", "L 1881 1375 \n", "L 863 0 \n", "L 184 0 \n", "L 1544 1831 \n", "L 300 3500 \n", "L 978 3500 \n", "L 1906 2253 \n", "L 2834 3500 \n", "L 3513 3500 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "      </defs>\n", "      <use xlink:href=\"#DejaVuSans-78\"/>\n", "     </g>\n", "    </g>\n", "   </g>\n", "   <g id=\"matplotlib.axis_2\">\n", "    <g id=\"ytick_1\">\n", "     <g id=\"line2d_11\">\n", "      <path d=\"M 42.620312 121.640967 \n", "L 237.920313 121.640967 \n", "\" clip-path=\"url(#p51ac05d166)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_12\">\n", "      <defs>\n", "       <path id=\"m3a9a66a386\" d=\"M 0 0 \n", "L -3.5 0 \n", "\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </defs>\n", "      <g>\n", "       <use xlink:href=\"#m3a9a66a386\" x=\"42.620312\" y=\"121.640967\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_7\">\n", "      <!-- −5 -->\n", "      <g transform=\"translate(20.878125 125.440185)scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-2212\"/>\n", "       <use xlink:href=\"#DejaVuSans-35\" x=\"83.789062\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_2\">\n", "     <g id=\"line2d_13\">\n", "      <path d=\"M 42.620312 76.5 \n", "L 237.920313 76.5 \n", "\" clip-path=\"url(#p51ac05d166)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_14\">\n", "      <g>\n", "       <use xlink:href=\"#m3a9a66a386\" x=\"42.620312\" y=\"76.5\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_8\">\n", "      <!-- 0 -->\n", "      <g transform=\"translate(29.257812 80.299219)scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_3\">\n", "     <g id=\"line2d_15\">\n", "      <path d=\"M 42.620312 31.359033 \n", "L 237.920313 31.359033 \n", "\" clip-path=\"url(#p51ac05d166)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_16\">\n", "      <g>\n", "       <use xlink:href=\"#m3a9a66a386\" x=\"42.620312\" y=\"31.359033\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_9\">\n", "      <!-- 5 -->\n", "      <g transform=\"translate(29.257812 35.158252)scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-35\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"text_10\">\n", "     <!-- f(x) -->\n", "     <g transform=\"translate(14.798437 85.121094)rotate(-90)scale(0.1 -0.1)\">\n", "      <defs>\n", "       <path id=\"DejaVuSans-66\" d=\"M 2375 4863 \n", "L 2375 4384 \n", "L 1825 4384 \n", "Q 1516 4384 1395 4259 \n", "Q 1275 4134 1275 3809 \n", "L 1275 3500 \n", "L 2222 3500 \n", "L 2222 3053 \n", "L 1275 3053 \n", "L 1275 0 \n", "L 697 0 \n", "L 697 3053 \n", "L 147 3053 \n", "L 147 3500 \n", "L 697 3500 \n", "L 697 3744 \n", "Q 697 4328 969 4595 \n", "Q 1241 4863 1831 4863 \n", "L 2375 4863 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-28\" d=\"M 1984 4856 \n", "Q 1566 4138 1362 3434 \n", "Q 1159 2731 1159 2009 \n", "Q 1159 1288 1364 580 \n", "Q 1569 -128 1984 -844 \n", "L 1484 -844 \n", "Q 1016 -109 783 600 \n", "Q 550 1309 550 2009 \n", "Q 550 2706 781 3412 \n", "Q 1013 4119 1484 4856 \n", "L 1984 4856 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-29\" d=\"M 513 4856 \n", "L 1013 4856 \n", "Q 1481 4119 1714 3412 \n", "Q 1947 2706 1947 2009 \n", "Q 1947 1309 1714 600 \n", "Q 1481 -109 1013 -844 \n", "L 513 -844 \n", "Q 928 -128 1133 580 \n", "Q 1338 1288 1338 2009 \n", "Q 1338 2731 1133 3434 \n", "Q 928 4138 513 4856 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "      </defs>\n", "      <use xlink:href=\"#DejaVuSans-66\"/>\n", "      <use xlink:href=\"#DejaVuSans-28\" x=\"35.205078\"/>\n", "      <use xlink:href=\"#DejaVuSans-78\" x=\"74.21875\"/>\n", "      <use xlink:href=\"#DejaVuSans-29\" x=\"133.398438\"/>\n", "     </g>\n", "    </g>\n", "   </g>\n", "   <g id=\"line2d_17\">\n", "    <path d=\"M 51.497585 76.500001 \n", "L 54.515859 62.586451 \n", "L 57.090268 51.752961 \n", "L 59.309586 43.325861 \n", "L 61.351355 36.406086 \n", "L 63.215583 30.83764 \n", "L 64.991044 26.232394 \n", "L 66.588956 22.687908 \n", "L 68.098084 19.871522 \n", "L 69.518451 17.695347 \n", "L 70.761268 16.168286 \n", "L 72.004085 14.990569 \n", "L 73.158131 14.20593 \n", "L 74.223403 13.741354 \n", "L 75.288675 13.521051 \n", "L 76.353951 13.539197 \n", "L 77.419222 13.789079 \n", "L 78.484494 14.263143 \n", "L 79.63854 15.020002 \n", "L 80.881362 16.105041 \n", "L 82.212949 17.56077 \n", "L 83.722086 19.55149 \n", "L 85.408769 22.166597 \n", "L 87.272996 25.480233 \n", "L 89.40354 29.726556 \n", "L 91.977948 35.366172 \n", "L 95.528858 43.724429 \n", "L 103.163314 61.839575 \n", "L 106.004039 67.93953 \n", "L 108.400904 72.589372 \n", "L 110.53145 76.262462 \n", "L 112.484448 79.203521 \n", "L 114.259903 81.497532 \n", "L 115.946585 83.326808 \n", "L 117.544495 84.737454 \n", "L 119.05363 85.779104 \n", "L 120.473994 86.502463 \n", "L 121.894359 86.980124 \n", "L 123.314722 87.218052 \n", "L 124.735085 87.224602 \n", "L 126.155449 87.010408 \n", "L 127.664585 86.555312 \n", "L 129.262494 85.837475 \n", "L 131.037949 84.784115 \n", "L 132.990949 83.357255 \n", "L 135.29904 81.380765 \n", "L 138.317313 78.475538 \n", "L 145.951767 70.982751 \n", "L 148.259858 69.094507 \n", "L 150.212858 67.764372 \n", "L 151.988312 66.814949 \n", "L 153.586222 66.202362 \n", "L 155.095359 65.855652 \n", "L 156.515722 65.750362 \n", "L 157.936085 65.871412 \n", "L 159.356448 66.228309 \n", "L 160.776812 66.828228 \n", "L 162.197176 67.675904 \n", "L 163.706313 68.850493 \n", "L 165.215448 70.306795 \n", "L 166.813358 72.151568 \n", "L 168.588811 74.556208 \n", "L 170.453039 77.464178 \n", "L 172.494811 81.066691 \n", "L 174.802902 85.6074 \n", "L 177.466086 91.359601 \n", "L 180.83945 99.206191 \n", "L 190.071814 120.997816 \n", "L 192.557452 126.154107 \n", "L 194.687995 130.082443 \n", "L 196.552223 133.061892 \n", "L 198.150131 135.22328 \n", "L 199.659268 136.894963 \n", "L 200.990859 138.046954 \n", "L 202.233676 138.831093 \n", "L 203.387723 139.294717 \n", "L 204.452994 139.487343 \n", "L 205.51827 139.446472 \n", "L 206.583542 139.165611 \n", "L 207.648814 138.639155 \n", "L 208.714086 137.862429 \n", "L 209.868132 136.734267 \n", "L 211.110949 135.182124 \n", "L 212.442541 133.128478 \n", "L 213.862903 130.492079 \n", "L 215.37204 127.189752 \n", "L 216.969952 123.138737 \n", "L 218.745405 117.986217 \n", "L 220.609632 111.867896 \n", "L 222.651401 104.384167 \n", "L 224.959502 95.024237 \n", "L 227.533903 83.601979 \n", "L 228.954265 76.925033 \n", "L 228.954265 76.925033 \n", "\" clip-path=\"url(#p51ac05d166)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_18\">\n", "    <path d=\"M 229.04304 76.499999 \n", "L 145.376715 71.496411 \n", "L 129.559103 85.679359 \n", "L 120.033699 86.30472 \n", "L 128.344442 86.278275 \n", "L 120.680218 86.586947 \n", "L 127.613196 86.574483 \n", "L 121.143037 86.757761 \n", "L 127.097544 86.752758 \n", "L 121.500618 86.872028 \n", "L 126.704368 86.871151 \n", "\" clip-path=\"url(#p51ac05d166)\" style=\"fill: none; stroke: #ff7f0e; stroke-width: 1.5; stroke-linecap: square\"/>\n", "    <defs>\n", "     <path id=\"m5782000d37\" d=\"M 0 3 \n", "C 0.795609 3 1.55874 2.683901 2.12132 2.12132 \n", "C 2.683901 1.55874 3 0.795609 3 0 \n", "C 3 -0.795609 2.683901 -1.55874 2.12132 -2.12132 \n", "C 1.55874 -2.683901 0.795609 -3 0 -3 \n", "C -0.795609 -3 -1.55874 -2.683901 -2.12132 -2.12132 \n", "C -2.683901 -1.55874 -3 -0.795609 -3 0 \n", "C -3 0.795609 -2.683901 1.55874 -2.12132 2.12132 \n", "C -1.55874 2.683901 -0.795609 3 0 3 \n", "z\n", "\" style=\"stroke: #ff7f0e\"/>\n", "    </defs>\n", "    <g clip-path=\"url(#p51ac05d166)\">\n", "     <use xlink:href=\"#m5782000d37\" x=\"229.04304\" y=\"76.499999\" style=\"fill: #ff7f0e; stroke: #ff7f0e\"/>\n", "     <use xlink:href=\"#m5782000d37\" x=\"145.376715\" y=\"71.496411\" style=\"fill: #ff7f0e; stroke: #ff7f0e\"/>\n", "     <use xlink:href=\"#m5782000d37\" x=\"129.559103\" y=\"85.679359\" style=\"fill: #ff7f0e; stroke: #ff7f0e\"/>\n", "     <use xlink:href=\"#m5782000d37\" x=\"120.033699\" y=\"86.30472\" style=\"fill: #ff7f0e; stroke: #ff7f0e\"/>\n", "     <use xlink:href=\"#m5782000d37\" x=\"128.344442\" y=\"86.278275\" style=\"fill: #ff7f0e; stroke: #ff7f0e\"/>\n", "     <use xlink:href=\"#m5782000d37\" x=\"120.680218\" y=\"86.586947\" style=\"fill: #ff7f0e; stroke: #ff7f0e\"/>\n", "     <use xlink:href=\"#m5782000d37\" x=\"127.613196\" y=\"86.574483\" style=\"fill: #ff7f0e; stroke: #ff7f0e\"/>\n", "     <use xlink:href=\"#m5782000d37\" x=\"121.143037\" y=\"86.757761\" style=\"fill: #ff7f0e; stroke: #ff7f0e\"/>\n", "     <use xlink:href=\"#m5782000d37\" x=\"127.097544\" y=\"86.752758\" style=\"fill: #ff7f0e; stroke: #ff7f0e\"/>\n", "     <use xlink:href=\"#m5782000d37\" x=\"121.500618\" y=\"86.872028\" style=\"fill: #ff7f0e; stroke: #ff7f0e\"/>\n", "     <use xlink:href=\"#m5782000d37\" x=\"126.704368\" y=\"86.871151\" style=\"fill: #ff7f0e; stroke: #ff7f0e\"/>\n", "    </g>\n", "   </g>\n", "   <g id=\"patch_3\">\n", "    <path d=\"M 42.**********.8 \n", "L 42.620312 7.2 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_4\">\n", "    <path d=\"M 237.**********.8 \n", "L 237.920313 7.2 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_5\">\n", "    <path d=\"M 42.**********.8 \n", "L 237.**********.8 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_6\">\n", "    <path d=\"M 42.620312 7.2 \n", "L 237.920313 7.2 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "  </g>\n", " </g>\n", " <defs>\n", "  <clipPath id=\"p51ac05d166\">\n", "   <rect x=\"42.620312\" y=\"7.2\" width=\"195.3\" height=\"138.6\"/>\n", "  </clipPath>\n", " </defs>\n", "</svg>\n"], "text/plain": ["<Figure size 350x250 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["c = torch.tensor(0.15 * np.pi)\n", "\n", "def f(x):  # 目标函数\n", "    return x * torch.cos(c * x)\n", "\n", "def f_grad(x):  # 目标函数的梯度\n", "    return torch.cos(c * x) - c * x * torch.sin(c * x)\n", "\n", "show_trace(gd(2, f_grad), f)"]}, {"cell_type": "markdown", "id": "c7689eec", "metadata": {"origin_pos": 18}, "source": ["## 多元梯度下降\n", "\n", "现在我们对单变量的情况有了更好的理解，让我们考虑一下$\\mathbf{x} = [x_1, x_2, \\ldots, x_d]^\\top$的情况。\n", "即目标函数$f: \\mathbb{R}^d \\to \\mathbb{R}$将向量映射成标量。\n", "相应地，它的梯度也是多元的，它是一个由$d$个偏导数组成的向量：\n", "\n", "$$\\nabla f(\\mathbf{x}) = \\bigg[\\frac{\\partial f(\\mathbf{x})}{\\partial x_1}, \\frac{\\partial f(\\mathbf{x})}{\\partial x_2}, \\ldots, \\frac{\\partial f(\\mathbf{x})}{\\partial x_d}\\bigg]^\\top.$$\n", "\n", "梯度中的每个偏导数元素$\\partial f(\\mathbf{x})/\\partial x_i$代表了当输入$x_i$时$f$在$\\mathbf{x}$处的变化率。\n", "和先前单变量的情况一样，我们可以对多变量函数使用相应的泰勒近似来思考。\n", "具体来说，\n", "\n", "$$f(\\mathbf{x} + \\boldsymbol{\\epsilon}) = f(\\mathbf{x}) + \\mathbf{\\boldsymbol{\\epsilon}}^\\top \\nabla f(\\mathbf{x}) + \\mathcal{O}(\\|\\boldsymbol{\\epsilon}\\|^2).$$\n", ":eqlabel:`gd-multi-taylor`\n", "\n", "换句话说，在$\\boldsymbol{\\epsilon}$的二阶项中，\n", "最陡下降的方向由负梯度$-\\nabla f(\\mathbf{x})$得出。\n", "选择合适的学习率$\\eta > 0$来生成典型的梯度下降算法：\n", "\n", "$$\\mathbf{x} \\leftarrow \\mathbf{x} - \\eta \\nabla f(\\mathbf{x}).$$\n", "\n", "这个算法在实践中的表现如何呢？\n", "我们构造一个目标函数$f(\\mathbf{x})=x_1^2+2x_2^2$，\n", "并有二维向量$\\mathbf{x} = [x_1, x_2]^\\top$作为输入，\n", "标量作为输出。\n", "梯度由$\\nabla f(\\mathbf{x}) = [2x_1, 4x_2]^\\top$给出。\n", "我们将从初始位置$[-5, -2]$通过梯度下降观察$\\mathbf{x}$的轨迹。\n", "\n", "我们还需要两个辅助函数：\n", "第一个是update函数，并将其应用于初始值20次；\n", "第二个函数会显示$\\mathbf{x}$的轨迹。\n"]}, {"cell_type": "code", "execution_count": 8, "id": "8160cddd", "metadata": {"execution": {"iopub.execute_input": "2022-12-07T16:54:14.336271Z", "iopub.status.busy": "2022-12-07T16:54:14.335620Z", "iopub.status.idle": "2022-12-07T16:54:14.343503Z", "shell.execute_reply": "2022-12-07T16:54:14.342722Z"}, "origin_pos": 19, "tab": ["pytorch"]}, "outputs": [], "source": ["def train_2d(trainer, steps=20, f_grad=None):  #@save\n", "    \"\"\"用定制的训练机优化2D目标函数\"\"\"\n", "    # s1和s2是稍后将使用的内部状态变量\n", "    x1, x2, s1, s2 = -5, -2, 0, 0\n", "    results = [(x1, x2)]\n", "    for i in range(steps):\n", "        if f_grad:\n", "            x1, x2, s1, s2 = trainer(x1, x2, s1, s2, f_grad)\n", "        else:\n", "            x1, x2, s1, s2 = trainer(x1, x2, s1, s2)\n", "        results.append((x1, x2))\n", "    print(f'epoch {i + 1}, x1: {float(x1):f}, x2: {float(x2):f}')\n", "    return results\n", "\n", "def show_trace_2d(f, results):  #@save\n", "    \"\"\"显示优化过程中2D变量的轨迹\"\"\"\n", "    d2l.set_figsize()\n", "    d2l.plt.plot(*zip(*results), '-o', color='#ff7f0e')\n", "    x1, x2 = torch.meshgrid(torch.arange(-5.5, 1.0, 0.1),\n", "                          torch.arange(-3.0, 1.0, 0.1))\n", "    d2l.plt.contour(x1, x2, f(x1, x2), colors='#1f77b4')\n", "    d2l.plt.xlabel('x1')\n", "    d2l.plt.ylabel('x2')"]}, {"cell_type": "markdown", "id": "dc47c10c", "metadata": {"origin_pos": 21}, "source": ["接下来，我们观察学习率$\\eta = 0.1$时优化变量$\\mathbf{x}$的轨迹。\n", "可以看到，经过20步之后，$\\mathbf{x}$的值接近其位于$[0, 0]$的最小值。\n", "虽然进展相当顺利，但相当缓慢。\n"]}, {"cell_type": "code", "execution_count": 9, "id": "01827428", "metadata": {"execution": {"iopub.execute_input": "2022-12-07T16:54:14.348019Z", "iopub.status.busy": "2022-12-07T16:54:14.347485Z", "iopub.status.idle": "2022-12-07T16:54:14.479980Z", "shell.execute_reply": "2022-12-07T16:54:14.479168Z"}, "origin_pos": 22, "tab": ["pytorch"]}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["epoch 20, x1: -0.057646, x2: -0.000073\n"]}, {"name": "stdout", "output_type": "stream", "text": ["/home/<USER>/miniconda3/envs/d2l-zh-release-1/lib/python3.9/site-packages/torch/functional.py:478: UserWarning: torch.meshgrid: in an upcoming release, it will be required to pass the indexing argument. (Triggered internally at  ../aten/src/ATen/native/TensorShape.cpp:2895.)\n", "  return _VF.meshgrid(tensors, **kwargs)  # type: ignore[attr-defined]\n"]}, {"data": {"image/svg+xml": ["<?xml version=\"1.0\" encoding=\"utf-8\" standalone=\"no\"?>\n", "<!DOCTYPE svg PUBLIC \"-//W3C//DTD SVG 1.1//EN\"\n", "  \"http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd\">\n", "<svg xmlns:xlink=\"http://www.w3.org/1999/xlink\" width=\"245.120313pt\" height=\"183.35625pt\" viewBox=\"0 0 245.**********.35625\" xmlns=\"http://www.w3.org/2000/svg\" version=\"1.1\">\n", " <metadata>\n", "  <rdf:RDF xmlns:dc=\"http://purl.org/dc/elements/1.1/\" xmlns:cc=\"http://creativecommons.org/ns#\" xmlns:rdf=\"http://www.w3.org/1999/02/22-rdf-syntax-ns#\">\n", "   <cc:Work>\n", "    <dc:type rdf:resource=\"http://purl.org/dc/dcmitype/StillImage\"/>\n", "    <dc:date>2022-12-07T16:54:14.450071</dc:date>\n", "    <dc:format>image/svg+xml</dc:format>\n", "    <dc:creator>\n", "     <cc:Agent>\n", "      <dc:title>Matplotlib v3.5.1, https://matplotlib.org/</dc:title>\n", "     </cc:Agent>\n", "    </dc:creator>\n", "   </cc:Work>\n", "  </rdf:RDF>\n", " </metadata>\n", " <defs>\n", "  <style type=\"text/css\">*{stroke-linejoin: round; stroke-linecap: butt}</style>\n", " </defs>\n", " <g id=\"figure_1\">\n", "  <g id=\"patch_1\">\n", "   <path d=\"M 0 183.35625 \n", "L 245.**********.35625 \n", "L 245.120313 0 \n", "L 0 0 \n", "z\n", "\" style=\"fill: #ffffff\"/>\n", "  </g>\n", "  <g id=\"axes_1\">\n", "   <g id=\"patch_2\">\n", "    <path d=\"M 42.**********.8 \n", "L 237.**********.8 \n", "L 237.920313 7.2 \n", "L 42.620312 7.2 \n", "z\n", "\" style=\"fill: #ffffff\"/>\n", "   </g>\n", "   <g id=\"matplotlib.axis_1\">\n", "    <g id=\"xtick_1\">\n", "     <g id=\"line2d_1\">\n", "      <defs>\n", "       <path id=\"m17e9656587\" d=\"M 0 0 \n", "L 0 3.5 \n", "\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </defs>\n", "      <g>\n", "       <use xlink:href=\"#m17e9656587\" x=\"88.39375\" y=\"145.8\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_1\">\n", "      <!-- −4 -->\n", "      <g transform=\"translate(81.022656 160.398438)scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-2212\" d=\"M 678 2272 \n", "L 4684 2272 \n", "L 4684 1741 \n", "L 678 1741 \n", "L 678 2272 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "        <path id=\"DejaVuSans-34\" d=\"M 2419 4116 \n", "L 825 1625 \n", "L 2419 1625 \n", "L 2419 4116 \n", "z\n", "M 2253 4666 \n", "L 3047 4666 \n", "L 3047 1625 \n", "L 3713 1625 \n", "L 3713 1100 \n", "L 3047 1100 \n", "L 3047 0 \n", "L 2419 0 \n", "L 2419 1100 \n", "L 313 1100 \n", "L 313 1709 \n", "L 2253 4666 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-2212\"/>\n", "       <use xlink:href=\"#DejaVuSans-34\" x=\"83.789062\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_2\">\n", "     <g id=\"line2d_2\">\n", "      <g>\n", "       <use xlink:href=\"#m17e9656587\" x=\"149.425\" y=\"145.8\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_2\">\n", "      <!-- −2 -->\n", "      <g transform=\"translate(142.053907 160.398438)scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-32\" d=\"M 1228 531 \n", "L 3431 531 \n", "L 3431 0 \n", "L 469 0 \n", "L 469 531 \n", "Q 828 903 1448 1529 \n", "Q 2069 2156 2228 2338 \n", "Q 2531 2678 2651 2914 \n", "Q 2772 3150 2772 3378 \n", "Q 2772 3750 2511 3984 \n", "Q 2250 4219 1831 4219 \n", "Q 1534 4219 1204 4116 \n", "Q 875 4013 500 3803 \n", "L 500 4441 \n", "Q 881 4594 1212 4672 \n", "Q 1544 4750 1819 4750 \n", "Q 2544 4750 2975 4387 \n", "Q 3406 4025 3406 3419 \n", "Q 3406 3131 3298 2873 \n", "Q 3191 2616 2906 2266 \n", "Q 2828 2175 2409 1742 \n", "Q 1991 1309 1228 531 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-2212\"/>\n", "       <use xlink:href=\"#DejaVuSans-32\" x=\"83.789062\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_3\">\n", "     <g id=\"line2d_3\">\n", "      <g>\n", "       <use xlink:href=\"#m17e9656587\" x=\"210.456251\" y=\"145.8\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_3\">\n", "      <!-- 0 -->\n", "      <g transform=\"translate(207.275001 160.398438)scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-30\" d=\"M 2034 4250 \n", "Q 1547 4250 1301 3770 \n", "Q 1056 3291 1056 2328 \n", "Q 1056 1369 1301 889 \n", "Q 1547 409 2034 409 \n", "Q 2525 409 2770 889 \n", "Q 3016 1369 3016 2328 \n", "Q 3016 3291 2770 3770 \n", "Q 2525 4250 2034 4250 \n", "z\n", "M 2034 4750 \n", "Q 2819 4750 3233 4129 \n", "Q 3647 3509 3647 2328 \n", "Q 3647 1150 3233 529 \n", "Q 2819 -91 2034 -91 \n", "Q 1250 -91 836 529 \n", "Q 422 1150 422 2328 \n", "Q 422 3509 836 4129 \n", "Q 1250 4750 2034 4750 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"text_4\">\n", "     <!-- x1 -->\n", "     <g transform=\"translate(134.129687 174.076563)scale(0.1 -0.1)\">\n", "      <defs>\n", "       <path id=\"DejaVuSans-78\" d=\"M 3513 3500 \n", "L 2247 1797 \n", "L 3578 0 \n", "L 2900 0 \n", "L 1881 1375 \n", "L 863 0 \n", "L 184 0 \n", "L 1544 1831 \n", "L 300 3500 \n", "L 978 3500 \n", "L 1906 2253 \n", "L 2834 3500 \n", "L 3513 3500 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-31\" d=\"M 794 531 \n", "L 1825 531 \n", "L 1825 4091 \n", "L 703 3866 \n", "L 703 4441 \n", "L 1819 4666 \n", "L 2450 4666 \n", "L 2450 531 \n", "L 3481 531 \n", "L 3481 0 \n", "L 794 0 \n", "L 794 531 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "      </defs>\n", "      <use xlink:href=\"#DejaVuSans-78\"/>\n", "      <use xlink:href=\"#DejaVuSans-31\" x=\"59.179688\"/>\n", "     </g>\n", "    </g>\n", "   </g>\n", "   <g id=\"matplotlib.axis_2\">\n", "    <g id=\"ytick_1\">\n", "     <g id=\"line2d_4\">\n", "      <defs>\n", "       <path id=\"mc7a2e9c33b\" d=\"M 0 0 \n", "L -3.5 0 \n", "\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </defs>\n", "      <g>\n", "       <use xlink:href=\"#mc7a2e9c33b\" x=\"42.620312\" y=\"145.8\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_5\">\n", "      <!-- −3 -->\n", "      <g transform=\"translate(20.878125 149.599219)scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-33\" d=\"M 2597 2516 \n", "Q 3050 2419 3304 2112 \n", "Q 3559 1806 3559 1356 \n", "Q 3559 666 3084 287 \n", "Q 2609 -91 1734 -91 \n", "Q 1441 -91 1130 -33 \n", "Q 819 25 488 141 \n", "L 488 750 \n", "Q 750 597 1062 519 \n", "Q 1375 441 1716 441 \n", "Q 2309 441 2620 675 \n", "Q 2931 909 2931 1356 \n", "Q 2931 1769 2642 2001 \n", "Q 2353 2234 1838 2234 \n", "L 1294 2234 \n", "L 1294 2753 \n", "L 1863 2753 \n", "Q 2328 2753 2575 2939 \n", "Q 2822 3125 2822 3475 \n", "Q 2822 3834 2567 4026 \n", "Q 2313 4219 1838 4219 \n", "Q 1578 4219 1281 4162 \n", "Q 984 4106 628 3988 \n", "L 628 4550 \n", "Q 988 4650 1302 4700 \n", "Q 1616 4750 1894 4750 \n", "Q 2613 4750 3031 4423 \n", "Q 3450 4097 3450 3541 \n", "Q 3450 3153 3228 2886 \n", "Q 3006 2619 2597 2516 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-2212\"/>\n", "       <use xlink:href=\"#DejaVuSans-33\" x=\"83.789062\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_2\">\n", "     <g id=\"line2d_5\">\n", "      <g>\n", "       <use xlink:href=\"#mc7a2e9c33b\" x=\"42.620312\" y=\"110.261538\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_6\">\n", "      <!-- −2 -->\n", "      <g transform=\"translate(20.878125 114.060757)scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-2212\"/>\n", "       <use xlink:href=\"#DejaVuSans-32\" x=\"83.789062\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_3\">\n", "     <g id=\"line2d_6\">\n", "      <g>\n", "       <use xlink:href=\"#mc7a2e9c33b\" x=\"42.620312\" y=\"74.723076\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_7\">\n", "      <!-- −1 -->\n", "      <g transform=\"translate(20.878125 78.522295)scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-2212\"/>\n", "       <use xlink:href=\"#DejaVuSans-31\" x=\"83.789062\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_4\">\n", "     <g id=\"line2d_7\">\n", "      <g>\n", "       <use xlink:href=\"#mc7a2e9c33b\" x=\"42.620312\" y=\"39.184615\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_8\">\n", "      <!-- 0 -->\n", "      <g transform=\"translate(29.257812 42.983833)scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"text_9\">\n", "     <!-- x2 -->\n", "     <g transform=\"translate(14.798437 82.640625)rotate(-90)scale(0.1 -0.1)\">\n", "      <use xlink:href=\"#DejaVuSans-78\"/>\n", "      <use xlink:href=\"#DejaVuSans-32\" x=\"59.179688\"/>\n", "     </g>\n", "    </g>\n", "   </g>\n", "   <g id=\"line2d_8\">\n", "    <path d=\"M 57.878125 110.261538 \n", "L 88.39375 81.830769 \n", "L 112.80625 64.772307 \n", "L 132.33625 54.53723 \n", "L 147.96025 48.396184 \n", "L 160.45945 44.711556 \n", "L 170.45881 42.50078 \n", "L 178.458299 41.174314 \n", "L 184.857889 40.378434 \n", "L 189.977561 39.900906 \n", "L 194.073299 39.61439 \n", "L 197.349889 39.44248 \n", "L 199.971162 39.339334 \n", "L 202.068179 39.277446 \n", "L 203.745794 39.240314 \n", "L 205.087885 39.218034 \n", "L 206.161558 39.204666 \n", "L 207.020497 39.196646 \n", "L 207.707647 39.191833 \n", "L 208.257368 39.188946 \n", "L 208.697145 39.187213 \n", "\" clip-path=\"url(#p82d62b1c15)\" style=\"fill: none; stroke: #ff7f0e; stroke-width: 1.5; stroke-linecap: square\"/>\n", "    <defs>\n", "     <path id=\"m81f550f855\" d=\"M 0 3 \n", "C 0.795609 3 1.55874 2.683901 2.12132 2.12132 \n", "C 2.683901 1.55874 3 0.795609 3 0 \n", "C 3 -0.795609 2.683901 -1.55874 2.12132 -2.12132 \n", "C 1.55874 -2.683901 0.795609 -3 0 -3 \n", "C -0.795609 -3 -1.55874 -2.683901 -2.12132 -2.12132 \n", "C -2.683901 -1.55874 -3 -0.795609 -3 0 \n", "C -3 0.795609 -2.683901 1.55874 -2.12132 2.12132 \n", "C -1.55874 2.683901 -0.795609 3 0 3 \n", "z\n", "\" style=\"stroke: #ff7f0e\"/>\n", "    </defs>\n", "    <g clip-path=\"url(#p82d62b1c15)\">\n", "     <use xlink:href=\"#m81f550f855\" x=\"57.878125\" y=\"110.261538\" style=\"fill: #ff7f0e; stroke: #ff7f0e\"/>\n", "     <use xlink:href=\"#m81f550f855\" x=\"88.39375\" y=\"81.830769\" style=\"fill: #ff7f0e; stroke: #ff7f0e\"/>\n", "     <use xlink:href=\"#m81f550f855\" x=\"112.80625\" y=\"64.772307\" style=\"fill: #ff7f0e; stroke: #ff7f0e\"/>\n", "     <use xlink:href=\"#m81f550f855\" x=\"132.33625\" y=\"54.53723\" style=\"fill: #ff7f0e; stroke: #ff7f0e\"/>\n", "     <use xlink:href=\"#m81f550f855\" x=\"147.96025\" y=\"48.396184\" style=\"fill: #ff7f0e; stroke: #ff7f0e\"/>\n", "     <use xlink:href=\"#m81f550f855\" x=\"160.45945\" y=\"44.711556\" style=\"fill: #ff7f0e; stroke: #ff7f0e\"/>\n", "     <use xlink:href=\"#m81f550f855\" x=\"170.45881\" y=\"42.50078\" style=\"fill: #ff7f0e; stroke: #ff7f0e\"/>\n", "     <use xlink:href=\"#m81f550f855\" x=\"178.458299\" y=\"41.174314\" style=\"fill: #ff7f0e; stroke: #ff7f0e\"/>\n", "     <use xlink:href=\"#m81f550f855\" x=\"184.857889\" y=\"40.378434\" style=\"fill: #ff7f0e; stroke: #ff7f0e\"/>\n", "     <use xlink:href=\"#m81f550f855\" x=\"189.977561\" y=\"39.900906\" style=\"fill: #ff7f0e; stroke: #ff7f0e\"/>\n", "     <use xlink:href=\"#m81f550f855\" x=\"194.073299\" y=\"39.61439\" style=\"fill: #ff7f0e; stroke: #ff7f0e\"/>\n", "     <use xlink:href=\"#m81f550f855\" x=\"197.349889\" y=\"39.44248\" style=\"fill: #ff7f0e; stroke: #ff7f0e\"/>\n", "     <use xlink:href=\"#m81f550f855\" x=\"199.971162\" y=\"39.339334\" style=\"fill: #ff7f0e; stroke: #ff7f0e\"/>\n", "     <use xlink:href=\"#m81f550f855\" x=\"202.068179\" y=\"39.277446\" style=\"fill: #ff7f0e; stroke: #ff7f0e\"/>\n", "     <use xlink:href=\"#m81f550f855\" x=\"203.745794\" y=\"39.240314\" style=\"fill: #ff7f0e; stroke: #ff7f0e\"/>\n", "     <use xlink:href=\"#m81f550f855\" x=\"205.087885\" y=\"39.218034\" style=\"fill: #ff7f0e; stroke: #ff7f0e\"/>\n", "     <use xlink:href=\"#m81f550f855\" x=\"206.161558\" y=\"39.204666\" style=\"fill: #ff7f0e; stroke: #ff7f0e\"/>\n", "     <use xlink:href=\"#m81f550f855\" x=\"207.020497\" y=\"39.196646\" style=\"fill: #ff7f0e; stroke: #ff7f0e\"/>\n", "     <use xlink:href=\"#m81f550f855\" x=\"207.707647\" y=\"39.191833\" style=\"fill: #ff7f0e; stroke: #ff7f0e\"/>\n", "     <use xlink:href=\"#m81f550f855\" x=\"208.257368\" y=\"39.188946\" style=\"fill: #ff7f0e; stroke: #ff7f0e\"/>\n", "     <use xlink:href=\"#m81f550f855\" x=\"208.697145\" y=\"39.187213\" style=\"fill: #ff7f0e; stroke: #ff7f0e\"/>\n", "    </g>\n", "   </g>\n", "   <g id=\"PathCollection_1\"/>\n", "   <g id=\"PathCollection_2\">\n", "    <path d=\"M 133.389338 7.2 \n", "L 131.354961 10.753845 \n", "L 131.115628 11.22768 \n", "L 129.618631 14.307692 \n", "L 128.121641 17.861537 \n", "L 128.064069 18.023063 \n", "L 126.898922 21.415384 \n", "L 125.900228 24.96923 \n", "L 125.123465 28.523076 \n", "L 125.012502 29.233828 \n", "L 124.58421 32.076922 \n", "L 124.262994 35.630769 \n", "L 124.155921 39.184616 \n", "L 124.262994 42.738462 \n", "L 124.58421 46.292308 \n", "L 125.012502 49.135401 \n", "L 125.123465 49.846154 \n", "L 125.900228 53.400001 \n", "L 126.898922 56.953846 \n", "L 128.064069 60.346166 \n", "L 128.121641 60.507693 \n", "L 129.618631 64.061538 \n", "L 131.115628 67.141548 \n", "L 131.354961 67.615382 \n", "L 133.389338 71.169229 \n", "L 134.167188 72.38502 \n", "L 135.724107 74.723076 \n", "L 137.218755 76.753852 \n", "L 138.387435 78.276924 \n", "L 140.270314 80.517389 \n", "L 141.423126 81.830766 \n", "L 143.321874 83.820924 \n", "L 144.883135 85.384613 \n", "L 146.373441 86.766672 \n", "L 148.829573 88.93846 \n", "L 149.425 89.428646 \n", "L 152.476564 91.818304 \n", "L 153.383783 92.492307 \n", "L 155.528127 93.98263 \n", "L 158.57969 95.988833 \n", "L 158.672164 96.046154 \n", "L 161.631253 97.76923 \n", "L 164.682813 99.438459 \n", "L 164.998499 99.600001 \n", "L 167.734376 100.92 \n", "L 170.785939 102.290771 \n", "L 172.861005 103.153848 \n", "L 173.837499 103.538045 \n", "L 176.889062 104.64262 \n", "L 179.940626 105.651142 \n", "L 182.992189 106.563617 \n", "L 183.530712 106.707695 \n", "L 186.04375 107.345562 \n", "L 189.095313 108.028995 \n", "L 192.146877 108.621302 \n", "L 195.198438 109.122485 \n", "L 198.250001 109.532545 \n", "L 201.301564 109.851478 \n", "L 204.353126 110.07929 \n", "L 207.404689 110.215975 \n", "L 210.456251 110.261538 \n", "L 213.507813 110.215975 \n", "L 216.559376 110.07929 \n", "L 219.610939 109.851478 \n", "L 222.662501 109.532545 \n", "L 225.714063 109.122485 \n", "L 228.765626 108.621302 \n", "L 231.817188 108.028995 \n", "L 234.868751 107.345562 \n", "L 237.38179 106.707695 \n", "L 237.920313 106.563617 \n", "\" clip-path=\"url(#p82d62b1c15)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"PathCollection_3\">\n", "    <path d=\"M 94.741 7.2 \n", "L 94.496869 7.827163 \n", "L 93.387217 10.753845 \n", "L 92.198295 14.307692 \n", "L 91.44531 16.904747 \n", "L 91.174923 17.861537 \n", "L 90.325119 21.415384 \n", "L 89.629825 24.96923 \n", "L 89.089044 28.523076 \n", "L 88.702769 32.076922 \n", "L 88.471007 35.630769 \n", "L 88.393757 39.184277 \n", "L 88.39375 39.184616 \n", "L 88.393757 39.184954 \n", "L 88.471007 42.738462 \n", "L 88.702769 46.292308 \n", "L 89.089044 49.846154 \n", "L 89.629825 53.400001 \n", "L 90.325119 56.953846 \n", "L 91.174923 60.507693 \n", "L 91.44531 61.464482 \n", "L 92.198295 64.061538 \n", "L 93.387217 67.615382 \n", "L 94.496869 70.542066 \n", "L 94.741 71.169229 \n", "L 96.287127 74.723076 \n", "L 97.548436 77.346149 \n", "L 98.008263 78.276924 \n", "L 99.931161 81.830766 \n", "L 100.599996 82.967996 \n", "L 102.061308 85.384613 \n", "L 103.651563 87.819658 \n", "L 104.403397 88.93846 \n", "L 106.703122 92.12466 \n", "L 106.976403 92.492307 \n", "L 109.754682 95.988822 \n", "L 109.801639 96.046154 \n", "L 112.806249 99.492307 \n", "L 112.903126 99.600001 \n", "L 115.857816 102.696925 \n", "L 116.308047 103.153848 \n", "L 118.909375 105.651142 \n", "L 120.04725 106.707695 \n", "L 121.960942 108.393495 \n", "L 124.155924 110.261538 \n", "L 125.012502 110.954971 \n", "L 128.064069 113.338652 \n", "L 128.697415 113.81539 \n", "L 131.115628 115.550988 \n", "L 133.748347 117.369232 \n", "L 134.167188 117.645641 \n", "L 137.218755 119.580512 \n", "L 139.426266 120.923075 \n", "L 140.270314 121.414565 \n", "L 143.321874 123.115875 \n", "L 145.876677 124.476926 \n", "L 146.373441 124.730771 \n", "L 149.425 126.217583 \n", "L 152.476564 127.631866 \n", "L 153.383788 128.030769 \n", "L 155.528127 128.936653 \n", "L 158.57969 130.156112 \n", "L 161.631253 131.305886 \n", "L 162.418734 131.584612 \n", "L 164.682813 132.355733 \n", "L 167.734376 133.328012 \n", "L 170.785939 134.233237 \n", "L 173.837499 135.071408 \n", "L 174.102859 135.138463 \n", "L 176.889062 135.816922 \n", "L 179.940626 136.495383 \n", "L 182.992189 137.109229 \n", "L 186.04375 137.65846 \n", "L 189.095313 138.143075 \n", "L 192.146877 138.563078 \n", "L 193.256511 138.692306 \n", "L 195.198438 138.910526 \n", "L 198.250001 139.191094 \n", "L 201.301564 139.409311 \n", "L 204.353126 139.565181 \n", "L 207.404689 139.658704 \n", "L 210.456251 139.689878 \n", "L 213.507813 139.658704 \n", "L 216.559376 139.565181 \n", "L 219.610939 139.409311 \n", "L 222.662501 139.191094 \n", "L 225.714063 138.910526 \n", "L 227.655991 138.692306 \n", "L 228.765626 138.563078 \n", "L 231.817188 138.143075 \n", "L 234.868751 137.65846 \n", "L 237.920313 137.109229 \n", "\" clip-path=\"url(#p82d62b1c15)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"PathCollection_4\">\n", "    <path d=\"M 66.101283 7.2 \n", "L 65.009145 10.753845 \n", "L 64.045489 14.307692 \n", "L 63.981244 14.581071 \n", "L 63.226219 17.861537 \n", "L 62.534115 21.415384 \n", "L 61.967845 24.96923 \n", "L 61.527415 28.523076 \n", "L 61.21282 32.076922 \n", "L 61.024065 35.630769 \n", "L 60.961145 39.184616 \n", "L 61.024065 42.738462 \n", "L 61.21282 46.292308 \n", "L 61.527415 49.846154 \n", "L 61.967845 53.400001 \n", "L 62.534115 56.953846 \n", "L 63.226219 60.507693 \n", "L 63.981244 63.788159 \n", "L 64.045489 64.061538 \n", "L 65.009145 67.615382 \n", "L 66.101283 71.169229 \n", "L 67.032818 73.88139 \n", "L 67.328125 74.723076 \n", "L 68.706252 78.276924 \n", "L 70.084378 81.521744 \n", "L 70.218509 81.830766 \n", "L 71.895191 85.384613 \n", "L 73.135938 87.819658 \n", "L 73.718821 88.93846 \n", "L 75.70748 92.492307 \n", "L 76.187512 93.294806 \n", "L 77.871125 96.046154 \n", "L 79.239071 98.146162 \n", "L 80.208385 99.600001 \n", "L 82.290631 102.544624 \n", "L 82.731817 103.153848 \n", "L 85.342191 106.563617 \n", "L 85.455214 106.707695 \n", "L 88.39375 110.261538 \n", "L 88.393757 110.261547 \n", "L 91.44531 113.68536 \n", "L 91.564212 113.81539 \n", "L 94.496869 116.873336 \n", "L 94.98513 117.369232 \n", "L 97.548436 119.85692 \n", "L 98.677098 120.923075 \n", "L 100.599996 122.662189 \n", "L 102.66303 124.476926 \n", "L 103.651563 125.31099 \n", "L 106.703122 127.813182 \n", "L 106.976403 128.030769 \n", "L 109.754682 130.156105 \n", "L 111.679511 131.584612 \n", "L 112.806249 132.389261 \n", "L 115.857816 134.501452 \n", "L 116.808306 135.138463 \n", "L 118.909375 136.495381 \n", "L 121.960942 138.401542 \n", "L 122.442758 138.692306 \n", "L 125.012502 140.188665 \n", "L 128.064069 141.903241 \n", "L 128.697416 142.246157 \n", "L 131.115628 143.511083 \n", "L 134.167188 145.047066 \n", "L 135.724104 145.8 \n", "\" clip-path=\"url(#p82d62b1c15)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"PathCollection_5\">\n", "    <path d=\"M 42.620312 72.385026 \n", "L 43.320213 74.723076 \n", "L 44.496043 78.276924 \n", "L 45.671872 81.521729 \n", "L 45.785952 81.830766 \n", "L 47.211915 85.384613 \n", "L 48.723432 88.872634 \n", "L 48.7525 88.93846 \n", "L 50.438126 92.492307 \n", "L 51.775006 95.129054 \n", "L 52.249024 96.046154 \n", "L 54.204398 99.600001 \n", "L 54.826565 100.666162 \n", "L 56.307021 103.153848 \n", "L 57.878125 105.651143 \n", "L 58.556253 106.707695 \n", "L 60.929685 110.215966 \n", "L 60.961151 110.261538 \n", "L 63.540821 113.81539 \n", "L 63.981244 114.393913 \n", "L 66.294014 117.369232 \n", "L 67.032818 118.277443 \n", "L 69.231252 120.923075 \n", "L 70.084378 121.906056 \n", "L 72.364666 124.476926 \n", "L 73.135938 125.31099 \n", "L 75.70748 128.030769 \n", "L 76.187512 128.518562 \n", "L 79.239071 131.549789 \n", "L 79.274952 131.584612 \n", "L 82.290631 134.400881 \n", "L 83.09947 135.138463 \n", "L 85.342191 137.109235 \n", "L 87.188189 138.692306 \n", "L 88.393757 139.689886 \n", "L 91.44531 142.152627 \n", "L 91.564212 142.246157 \n", "L 94.496869 144.474831 \n", "L 96.287127 145.8 \n", "\" clip-path=\"url(#p82d62b1c15)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"PathCollection_6\">\n", "    <path d=\"M 42.620312 117.645641 \n", "L 44.943982 120.923075 \n", "L 45.671872 121.90605 \n", "L 47.611182 124.476926 \n", "L 48.723432 125.891204 \n", "L 50.438126 128.030769 \n", "L 51.775006 129.633492 \n", "L 53.434101 131.584612 \n", "L 54.826565 133.160378 \n", "L 56.609163 135.138463 \n", "L 57.878125 136.495381 \n", "L 59.974143 138.692306 \n", "L 60.929685 139.658706 \n", "L 63.540815 142.246157 \n", "L 63.981244 142.667798 \n", "L 67.032818 145.528954 \n", "L 67.32812 145.8 \n", "\" clip-path=\"url(#p82d62b1c15)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"PathCollection_7\">\n", "    <path d=\"M 42.**********.047067 \n", "L 43.320214 145.8 \n", "\" clip-path=\"url(#p82d62b1c15)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"PathCollection_8\"/>\n", "   <g id=\"patch_3\">\n", "    <path d=\"M 42.**********.8 \n", "L 42.620312 7.2 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_4\">\n", "    <path d=\"M 237.**********.8 \n", "L 237.920313 7.2 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_5\">\n", "    <path d=\"M 42.**********.8 \n", "L 237.**********.8 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_6\">\n", "    <path d=\"M 42.620312 7.2 \n", "L 237.920313 7.2 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "  </g>\n", " </g>\n", " <defs>\n", "  <clipPath id=\"p82d62b1c15\">\n", "   <rect x=\"42.620312\" y=\"7.2\" width=\"195.3\" height=\"138.6\"/>\n", "  </clipPath>\n", " </defs>\n", "</svg>\n"], "text/plain": ["<Figure size 350x250 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["def f_2d(x1, x2):  # 目标函数\n", "    return x1 ** 2 + 2 * x2 ** 2\n", "\n", "def f_2d_grad(x1, x2):  # 目标函数的梯度\n", "    return (2 * x1, 4 * x2)\n", "\n", "def gd_2d(x1, x2, s1, s2, f_grad):\n", "    g1, g2 = f_grad(x1, x2)\n", "    return (x1 - eta * g1, x2 - eta * g2, 0, 0)\n", "\n", "eta = 0.1\n", "show_trace_2d(f_2d, train_2d(gd_2d, f_grad=f_2d_grad))"]}, {"cell_type": "markdown", "id": "042bef97", "metadata": {"origin_pos": 23}, "source": ["## 自适应方法\n", "\n", "正如我们在 :numref:`subsec_gd-learningrate`中所看到的，选择“恰到好处”的学习率$\\eta$是很棘手的。\n", "如果我们把它选得太小，就没有什么进展；如果太大，得到的解就会振荡，甚至可能发散。\n", "如果我们可以自动确定$\\eta$，或者完全不必选择学习率，会怎么样？\n", "除了考虑目标函数的值和梯度、还考虑它的曲率的二阶方法可以帮我们解决这个问题。\n", "虽然由于计算代价的原因，这些方法不能直接应用于深度学习，但它们为如何设计高级优化算法提供了有用的思维直觉，这些算法可以模拟下面概述的算法的许多理想特性。\n", "\n", "### 牛顿法\n", "\n", "回顾一些函数$f: \\mathbb{R}^d \\rightarrow \\mathbb{R}$的泰勒展开式，事实上我们可以把它写成\n", "\n", "$$f(\\mathbf{x} + \\boldsymbol{\\epsilon}) = f(\\mathbf{x}) + \\boldsymbol{\\epsilon}^\\top \\nabla f(\\mathbf{x}) + \\frac{1}{2} \\boldsymbol{\\epsilon}^\\top \\nabla^2 f(\\mathbf{x}) \\boldsymbol{\\epsilon} + \\mathcal{O}(\\|\\boldsymbol{\\epsilon}\\|^3).$$\n", ":eqlabel:`gd-hot-taylor`\n", "\n", "为了避免繁琐的符号，我们将$\\mathbf{H} \\stackrel{\\mathrm{def}}{=} \\nabla^2 f(\\mathbf{x})$定义为$f$的Hessian，是$d \\times d$矩阵。\n", "当$d$的值很小且问题很简单时，$\\mathbf{H}$很容易计算。\n", "但是对于深度神经网络而言，考虑到$\\mathbf{H}$可能非常大，\n", "$\\mathcal{O}(d^2)$个条目的存储代价会很高，\n", "此外通过反向传播进行计算可能雪上加霜。\n", "然而，我们姑且先忽略这些考量，看看会得到什么算法。\n", "\n", "毕竟，$f$的最小值满足$\\nabla f = 0$。\n", "遵循 :numref:`sec_calculus`中的微积分规则，\n", "通过取$\\boldsymbol{\\epsilon}$对 :eqref:`gd-hot-taylor`的导数，\n", "再忽略不重要的高阶项，我们便得到\n", "\n", "$$\\nabla f(\\mathbf{x}) + \\mathbf{H} \\boldsymbol{\\epsilon} = 0 \\text{ and hence }\n", "\\boldsymbol{\\epsilon} = -\\mathbf{H}^{-1} \\nabla f(\\mathbf{x}).$$\n", "\n", "也就是说，作为优化问题的一部分，我们需要将Hessian矩阵$\\mathbf{H}$求逆。\n", "\n", "举一个简单的例子，对于$f(x) = \\frac{1}{2} x^2$，我们有$\\nabla f(x) = x$和$\\mathbf{H} = 1$。\n", "因此，对于任何$x$，我们可以获得$\\epsilon = -x$。\n", "换言之，单单一步就足以完美地收敛，而无须任何调整。\n", "我们在这里比较幸运：泰勒展开式是确切的，因为$f(x+\\epsilon)= \\frac{1}{2} x^2 + \\epsilon x + \\frac{1}{2} \\epsilon^2$。\n", "\n", "让我们看看其他问题。\n", "给定一个凸双曲余弦函数$c$，其中$c$为某些常数，\n", "我们可以看到经过几次迭代后，得到了$x=0$处的全局最小值。\n"]}, {"cell_type": "code", "execution_count": 10, "id": "5573f382", "metadata": {"execution": {"iopub.execute_input": "2022-12-07T16:54:14.485265Z", "iopub.status.busy": "2022-12-07T16:54:14.484712Z", "iopub.status.idle": "2022-12-07T16:54:14.801383Z", "shell.execute_reply": "2022-12-07T16:54:14.800574Z"}, "origin_pos": 24, "tab": ["pytorch"]}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["epoch 10, x: tensor(0.)\n"]}, {"data": {"image/svg+xml": ["<?xml version=\"1.0\" encoding=\"utf-8\" standalone=\"no\"?>\n", "<!DOCTYPE svg PUBLIC \"-//W3C//DTD SVG 1.1//EN\"\n", "  \"http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd\">\n", "<svg xmlns:xlink=\"http://www.w3.org/1999/xlink\" width=\"243.103125pt\" height=\"183.35625pt\" viewBox=\"0 0 243.**********.35625\" xmlns=\"http://www.w3.org/2000/svg\" version=\"1.1\">\n", " <metadata>\n", "  <rdf:RDF xmlns:dc=\"http://purl.org/dc/elements/1.1/\" xmlns:cc=\"http://creativecommons.org/ns#\" xmlns:rdf=\"http://www.w3.org/1999/02/22-rdf-syntax-ns#\">\n", "   <cc:Work>\n", "    <dc:type rdf:resource=\"http://purl.org/dc/dcmitype/StillImage\"/>\n", "    <dc:date>2022-12-07T16:54:14.770648</dc:date>\n", "    <dc:format>image/svg+xml</dc:format>\n", "    <dc:creator>\n", "     <cc:Agent>\n", "      <dc:title>Matplotlib v3.5.1, https://matplotlib.org/</dc:title>\n", "     </cc:Agent>\n", "    </dc:creator>\n", "   </cc:Work>\n", "  </rdf:RDF>\n", " </metadata>\n", " <defs>\n", "  <style type=\"text/css\">*{stroke-linejoin: round; stroke-linecap: butt}</style>\n", " </defs>\n", " <g id=\"figure_1\">\n", "  <g id=\"patch_1\">\n", "   <path d=\"M 0 183.35625 \n", "L 243.**********.35625 \n", "L 243.103125 0 \n", "L 0 0 \n", "z\n", "\" style=\"fill: #ffffff\"/>\n", "  </g>\n", "  <g id=\"axes_1\">\n", "   <g id=\"patch_2\">\n", "    <path d=\"M 40.**********.8 \n", "L 235.**********.8 \n", "L 235.903125 7.2 \n", "L 40.603125 7.2 \n", "z\n", "\" style=\"fill: #ffffff\"/>\n", "   </g>\n", "   <g id=\"matplotlib.axis_1\">\n", "    <g id=\"xtick_1\">\n", "     <g id=\"line2d_1\">\n", "      <path d=\"M 49.480398 145.8 \n", "L 49.480398 7.2 \n", "\" clip-path=\"url(#p16ca763be7)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_2\">\n", "      <defs>\n", "       <path id=\"mc7e333dcc4\" d=\"M 0 0 \n", "L 0 3.5 \n", "\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </defs>\n", "      <g>\n", "       <use xlink:href=\"#mc7e333dcc4\" x=\"49.480398\" y=\"145.8\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_1\">\n", "      <!-- −10 -->\n", "      <g transform=\"translate(38.928054 160.398438)scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-2212\" d=\"M 678 2272 \n", "L 4684 2272 \n", "L 4684 1741 \n", "L 678 1741 \n", "L 678 2272 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "        <path id=\"DejaVuSans-31\" d=\"M 794 531 \n", "L 1825 531 \n", "L 1825 4091 \n", "L 703 3866 \n", "L 703 4441 \n", "L 1819 4666 \n", "L 2450 4666 \n", "L 2450 531 \n", "L 3481 531 \n", "L 3481 0 \n", "L 794 0 \n", "L 794 531 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "        <path id=\"DejaVuSans-30\" d=\"M 2034 4250 \n", "Q 1547 4250 1301 3770 \n", "Q 1056 3291 1056 2328 \n", "Q 1056 1369 1301 889 \n", "Q 1547 409 2034 409 \n", "Q 2525 409 2770 889 \n", "Q 3016 1369 3016 2328 \n", "Q 3016 3291 2770 3770 \n", "Q 2525 4250 2034 4250 \n", "z\n", "M 2034 4750 \n", "Q 2819 4750 3233 4129 \n", "Q 3647 3509 3647 2328 \n", "Q 3647 1150 3233 529 \n", "Q 2819 -91 2034 -91 \n", "Q 1250 -91 836 529 \n", "Q 422 1150 422 2328 \n", "Q 422 3509 836 4129 \n", "Q 1250 4750 2034 4750 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-2212\"/>\n", "       <use xlink:href=\"#DejaVuSans-31\" x=\"83.789062\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"147.412109\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_2\">\n", "     <g id=\"line2d_3\">\n", "      <path d=\"M 93.866761 145.8 \n", "L 93.866761 7.2 \n", "\" clip-path=\"url(#p16ca763be7)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_4\">\n", "      <g>\n", "       <use xlink:href=\"#mc7e333dcc4\" x=\"93.866761\" y=\"145.8\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_2\">\n", "      <!-- −5 -->\n", "      <g transform=\"translate(86.495668 160.398438)scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-35\" d=\"M 691 4666 \n", "L 3169 4666 \n", "L 3169 4134 \n", "L 1269 4134 \n", "L 1269 2991 \n", "Q 1406 3038 1543 3061 \n", "Q 1681 3084 1819 3084 \n", "Q 2600 3084 3056 2656 \n", "Q 3513 2228 3513 1497 \n", "Q 3513 744 3044 326 \n", "Q 2575 -91 1722 -91 \n", "Q 1428 -91 1123 -41 \n", "Q 819 9 494 109 \n", "L 494 744 \n", "Q 775 591 1075 516 \n", "Q 1375 441 1709 441 \n", "Q 2250 441 2565 725 \n", "Q 2881 1009 2881 1497 \n", "Q 2881 1984 2565 2268 \n", "Q 2250 2553 1709 2553 \n", "Q 1456 2553 1204 2497 \n", "Q 953 2441 691 2322 \n", "L 691 4666 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-2212\"/>\n", "       <use xlink:href=\"#DejaVuSans-35\" x=\"83.789062\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_3\">\n", "     <g id=\"line2d_5\">\n", "      <path d=\"M 138.253125 145.8 \n", "L 138.253125 7.2 \n", "\" clip-path=\"url(#p16ca763be7)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_6\">\n", "      <g>\n", "       <use xlink:href=\"#mc7e333dcc4\" x=\"138.253125\" y=\"145.8\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_3\">\n", "      <!-- 0 -->\n", "      <g transform=\"translate(135.071875 160.398438)scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_4\">\n", "     <g id=\"line2d_7\">\n", "      <path d=\"M 182.639489 145.8 \n", "L 182.639489 7.2 \n", "\" clip-path=\"url(#p16ca763be7)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_8\">\n", "      <g>\n", "       <use xlink:href=\"#mc7e333dcc4\" x=\"182.639489\" y=\"145.8\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_4\">\n", "      <!-- 5 -->\n", "      <g transform=\"translate(179.458239 160.398438)scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-35\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_5\">\n", "     <g id=\"line2d_9\">\n", "      <path d=\"M 227.025852 145.8 \n", "L 227.025852 7.2 \n", "\" clip-path=\"url(#p16ca763be7)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_10\">\n", "      <g>\n", "       <use xlink:href=\"#mc7e333dcc4\" x=\"227.025852\" y=\"145.8\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_5\">\n", "      <!-- 10 -->\n", "      <g transform=\"translate(220.663352 160.398438)scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-31\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"63.623047\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"text_6\">\n", "     <!-- x -->\n", "     <g transform=\"translate(135.29375 174.076563)scale(0.1 -0.1)\">\n", "      <defs>\n", "       <path id=\"DejaVuSans-78\" d=\"M 3513 3500 \n", "L 2247 1797 \n", "L 3578 0 \n", "L 2900 0 \n", "L 1881 1375 \n", "L 863 0 \n", "L 184 0 \n", "L 1544 1831 \n", "L 300 3500 \n", "L 978 3500 \n", "L 1906 2253 \n", "L 2834 3500 \n", "L 3513 3500 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "      </defs>\n", "      <use xlink:href=\"#DejaVuSans-78\"/>\n", "     </g>\n", "    </g>\n", "   </g>\n", "   <g id=\"matplotlib.axis_2\">\n", "    <g id=\"ytick_1\">\n", "     <g id=\"line2d_11\">\n", "      <path d=\"M 40.603125 141.221077 \n", "L 235.903125 141.221077 \n", "\" clip-path=\"url(#p16ca763be7)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_12\">\n", "      <defs>\n", "       <path id=\"m57db7aa985\" d=\"M 0 0 \n", "L -3.5 0 \n", "\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </defs>\n", "      <g>\n", "       <use xlink:href=\"#m57db7aa985\" x=\"40.603125\" y=\"141.221077\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_7\">\n", "      <!-- 0 -->\n", "      <g transform=\"translate(27.240625 145.020296)scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_2\">\n", "     <g id=\"line2d_13\">\n", "      <path d=\"M 40.603125 106.799528 \n", "L 235.903125 106.799528 \n", "\" clip-path=\"url(#p16ca763be7)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_14\">\n", "      <g>\n", "       <use xlink:href=\"#m57db7aa985\" x=\"40.603125\" y=\"106.799528\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_8\">\n", "      <!-- 20 -->\n", "      <g transform=\"translate(20.878125 110.598747)scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-32\" d=\"M 1228 531 \n", "L 3431 531 \n", "L 3431 0 \n", "L 469 0 \n", "L 469 531 \n", "Q 828 903 1448 1529 \n", "Q 2069 2156 2228 2338 \n", "Q 2531 2678 2651 2914 \n", "Q 2772 3150 2772 3378 \n", "Q 2772 3750 2511 3984 \n", "Q 2250 4219 1831 4219 \n", "Q 1534 4219 1204 4116 \n", "Q 875 4013 500 3803 \n", "L 500 4441 \n", "Q 881 4594 1212 4672 \n", "Q 1544 4750 1819 4750 \n", "Q 2544 4750 2975 4387 \n", "Q 3406 4025 3406 3419 \n", "Q 3406 3131 3298 2873 \n", "Q 3191 2616 2906 2266 \n", "Q 2828 2175 2409 1742 \n", "Q 1991 1309 1228 531 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-32\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"63.623047\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_3\">\n", "     <g id=\"line2d_15\">\n", "      <path d=\"M 40.603125 72.377979 \n", "L 235.903125 72.377979 \n", "\" clip-path=\"url(#p16ca763be7)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_16\">\n", "      <g>\n", "       <use xlink:href=\"#m57db7aa985\" x=\"40.603125\" y=\"72.377979\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_9\">\n", "      <!-- 40 -->\n", "      <g transform=\"translate(20.878125 76.177198)scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-34\" d=\"M 2419 4116 \n", "L 825 1625 \n", "L 2419 1625 \n", "L 2419 4116 \n", "z\n", "M 2253 4666 \n", "L 3047 4666 \n", "L 3047 1625 \n", "L 3713 1625 \n", "L 3713 1100 \n", "L 3047 1100 \n", "L 3047 0 \n", "L 2419 0 \n", "L 2419 1100 \n", "L 313 1100 \n", "L 313 1709 \n", "L 2253 4666 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-34\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"63.623047\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_4\">\n", "     <g id=\"line2d_17\">\n", "      <path d=\"M 40.603125 37.95643 \n", "L 235.903125 37.95643 \n", "\" clip-path=\"url(#p16ca763be7)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_18\">\n", "      <g>\n", "       <use xlink:href=\"#m57db7aa985\" x=\"40.603125\" y=\"37.95643\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_10\">\n", "      <!-- 60 -->\n", "      <g transform=\"translate(20.878125 41.755649)scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-36\" d=\"M 2113 2584 \n", "Q 1688 2584 1439 2293 \n", "Q 1191 2003 1191 1497 \n", "Q 1191 994 1439 701 \n", "Q 1688 409 2113 409 \n", "Q 2538 409 2786 701 \n", "Q 3034 994 3034 1497 \n", "Q 3034 2003 2786 2293 \n", "Q 2538 2584 2113 2584 \n", "z\n", "M 3366 4563 \n", "L 3366 3988 \n", "Q 3128 4100 2886 4159 \n", "Q 2644 4219 2406 4219 \n", "Q 1781 4219 1451 3797 \n", "Q 1122 3375 1075 2522 \n", "Q 1259 2794 1537 2939 \n", "Q 1816 3084 2150 3084 \n", "Q 2853 3084 3261 2657 \n", "Q 3669 2231 3669 1497 \n", "Q 3669 778 3244 343 \n", "Q 2819 -91 2113 -91 \n", "Q 1303 -91 875 529 \n", "Q 447 1150 447 2328 \n", "Q 447 3434 972 4092 \n", "Q 1497 4750 2381 4750 \n", "Q 2619 4750 2861 4703 \n", "Q 3103 4656 3366 4563 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-36\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"63.623047\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"text_11\">\n", "     <!-- f(x) -->\n", "     <g transform=\"translate(14.798437 85.121094)rotate(-90)scale(0.1 -0.1)\">\n", "      <defs>\n", "       <path id=\"DejaVuSans-66\" d=\"M 2375 4863 \n", "L 2375 4384 \n", "L 1825 4384 \n", "Q 1516 4384 1395 4259 \n", "Q 1275 4134 1275 3809 \n", "L 1275 3500 \n", "L 2222 3500 \n", "L 2222 3053 \n", "L 1275 3053 \n", "L 1275 0 \n", "L 697 0 \n", "L 697 3053 \n", "L 147 3053 \n", "L 147 3500 \n", "L 697 3500 \n", "L 697 3744 \n", "Q 697 4328 969 4595 \n", "Q 1241 4863 1831 4863 \n", "L 2375 4863 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-28\" d=\"M 1984 4856 \n", "Q 1566 4138 1362 3434 \n", "Q 1159 2731 1159 2009 \n", "Q 1159 1288 1364 580 \n", "Q 1569 -128 1984 -844 \n", "L 1484 -844 \n", "Q 1016 -109 783 600 \n", "Q 550 1309 550 2009 \n", "Q 550 2706 781 3412 \n", "Q 1013 4119 1484 4856 \n", "L 1984 4856 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-29\" d=\"M 513 4856 \n", "L 1013 4856 \n", "Q 1481 4119 1714 3412 \n", "Q 1947 2706 1947 2009 \n", "Q 1947 1309 1714 600 \n", "Q 1481 -109 1013 -844 \n", "L 513 -844 \n", "Q 928 -128 1133 580 \n", "Q 1338 1288 1338 2009 \n", "Q 1338 2731 1133 3434 \n", "Q 928 4138 513 4856 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "      </defs>\n", "      <use xlink:href=\"#DejaVuSans-66\"/>\n", "      <use xlink:href=\"#DejaVuSans-28\" x=\"35.205078\"/>\n", "      <use xlink:href=\"#DejaVuSans-78\" x=\"74.21875\"/>\n", "      <use xlink:href=\"#DejaVuSans-29\" x=\"133.398438\"/>\n", "     </g>\n", "    </g>\n", "   </g>\n", "   <g id=\"line2d_19\">\n", "    <path d=\"M 49.480398 13.5 \n", "L 51.522166 27.373482 \n", "L 53.563944 39.739702 \n", "L 55.605721 50.76236 \n", "L 57.647489 60.587334 \n", "L 59.689258 69.344756 \n", "L 61.731035 77.150597 \n", "L 63.772812 84.10817 \n", "L 65.814581 90.309565 \n", "L 67.856354 95.836927 \n", "L 69.986897 100.965014 \n", "L 72.117445 105.512728 \n", "L 74.247988 109.545617 \n", "L 76.378536 113.121838 \n", "L 78.597854 116.416975 \n", "L 80.817172 119.324043 \n", "L 83.125265 121.984562 \n", "L 85.522124 124.403376 \n", "L 88.007763 126.58807 \n", "L 90.582171 128.548422 \n", "L 93.245355 130.295944 \n", "L 96.08608 131.889372 \n", "L 99.104354 133.320901 \n", "L 102.300173 134.587956 \n", "L 105.762306 135.718534 \n", "L 109.490761 136.702398 \n", "L 113.66308 137.567962 \n", "L 118.279261 138.291056 \n", "L 123.339307 138.85625 \n", "L 129.020762 139.26202 \n", "L 135.14608 139.473579 \n", "L 141.537716 139.470464 \n", "L 147.751806 139.247761 \n", "L 153.522033 138.823339 \n", "L 158.759624 138.218588 \n", "L 163.464579 137.452921 \n", "L 167.636897 136.553367 \n", "L 171.454125 135.505018 \n", "L 174.916262 134.326341 \n", "L 178.112082 133.006079 \n", "L 181.041581 131.562583 \n", "L 183.793535 129.967315 \n", "L 186.456719 128.166002 \n", "L 188.942353 126.220172 \n", "L 191.339216 124.065403 \n", "L 193.647305 121.694994 \n", "L 195.866627 119.104646 \n", "L 198.085941 116.168288 \n", "L 200.216489 112.98125 \n", "L 202.347032 109.387083 \n", "L 204.388809 105.512722 \n", "L 206.430578 101.165613 \n", "L 208.472355 96.288169 \n", "L 210.514128 90.815838 \n", "L 212.555897 84.676181 \n", "L 214.508899 78.104128 \n", "L 216.461901 70.767588 \n", "L 218.414895 62.577737 \n", "L 220.367898 53.435308 \n", "L 222.320892 43.229639 \n", "L 224.273902 31.836982 \n", "L 226.226896 19.11954 \n", "L 226.937078 14.136974 \n", "L 226.937078 14.136974 \n", "\" clip-path=\"url(#p16ca763be7)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_20\">\n", "    <path d=\"M 227.025852 13.5 \n", "L 209.272924 94.217216 \n", "L 191.530286 123.880712 \n", "L 173.86341 134.710364 \n", "L 156.74043 138.479554 \n", "L 142.920474 139.440187 \n", "L 138.357749 139.49997 \n", "L 138.253126 139.5 \n", "L 138.253125 139.5 \n", "L 138.253125 139.5 \n", "L 138.253125 139.5 \n", "\" clip-path=\"url(#p16ca763be7)\" style=\"fill: none; stroke: #ff7f0e; stroke-width: 1.5; stroke-linecap: square\"/>\n", "    <defs>\n", "     <path id=\"m8999e28672\" d=\"M 0 3 \n", "C 0.795609 3 1.55874 2.683901 2.12132 2.12132 \n", "C 2.683901 1.55874 3 0.795609 3 0 \n", "C 3 -0.795609 2.683901 -1.55874 2.12132 -2.12132 \n", "C 1.55874 -2.683901 0.795609 -3 0 -3 \n", "C -0.795609 -3 -1.55874 -2.683901 -2.12132 -2.12132 \n", "C -2.683901 -1.55874 -3 -0.795609 -3 0 \n", "C -3 0.795609 -2.683901 1.55874 -2.12132 2.12132 \n", "C -1.55874 2.683901 -0.795609 3 0 3 \n", "z\n", "\" style=\"stroke: #ff7f0e\"/>\n", "    </defs>\n", "    <g clip-path=\"url(#p16ca763be7)\">\n", "     <use xlink:href=\"#m8999e28672\" x=\"227.025852\" y=\"13.5\" style=\"fill: #ff7f0e; stroke: #ff7f0e\"/>\n", "     <use xlink:href=\"#m8999e28672\" x=\"209.272924\" y=\"94.217216\" style=\"fill: #ff7f0e; stroke: #ff7f0e\"/>\n", "     <use xlink:href=\"#m8999e28672\" x=\"191.530286\" y=\"123.880712\" style=\"fill: #ff7f0e; stroke: #ff7f0e\"/>\n", "     <use xlink:href=\"#m8999e28672\" x=\"173.86341\" y=\"134.710364\" style=\"fill: #ff7f0e; stroke: #ff7f0e\"/>\n", "     <use xlink:href=\"#m8999e28672\" x=\"156.74043\" y=\"138.479554\" style=\"fill: #ff7f0e; stroke: #ff7f0e\"/>\n", "     <use xlink:href=\"#m8999e28672\" x=\"142.920474\" y=\"139.440187\" style=\"fill: #ff7f0e; stroke: #ff7f0e\"/>\n", "     <use xlink:href=\"#m8999e28672\" x=\"138.357749\" y=\"139.49997\" style=\"fill: #ff7f0e; stroke: #ff7f0e\"/>\n", "     <use xlink:href=\"#m8999e28672\" x=\"138.253126\" y=\"139.5\" style=\"fill: #ff7f0e; stroke: #ff7f0e\"/>\n", "     <use xlink:href=\"#m8999e28672\" x=\"138.253125\" y=\"139.5\" style=\"fill: #ff7f0e; stroke: #ff7f0e\"/>\n", "     <use xlink:href=\"#m8999e28672\" x=\"138.253125\" y=\"139.5\" style=\"fill: #ff7f0e; stroke: #ff7f0e\"/>\n", "     <use xlink:href=\"#m8999e28672\" x=\"138.253125\" y=\"139.5\" style=\"fill: #ff7f0e; stroke: #ff7f0e\"/>\n", "    </g>\n", "   </g>\n", "   <g id=\"patch_3\">\n", "    <path d=\"M 40.**********.8 \n", "L 40.603125 7.2 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_4\">\n", "    <path d=\"M 235.**********.8 \n", "L 235.903125 7.2 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_5\">\n", "    <path d=\"M 40.**********.8 \n", "L 235.**********.8 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_6\">\n", "    <path d=\"M 40.603125 7.2 \n", "L 235.903125 7.2 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "  </g>\n", " </g>\n", " <defs>\n", "  <clipPath id=\"p16ca763be7\">\n", "   <rect x=\"40.603125\" y=\"7.2\" width=\"195.3\" height=\"138.6\"/>\n", "  </clipPath>\n", " </defs>\n", "</svg>\n"], "text/plain": ["<Figure size 350x250 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["c = torch.tensor(0.5)\n", "\n", "def f(x):  # O目标函数\n", "    return torch.cosh(c * x)\n", "\n", "def f_grad(x):  # 目标函数的梯度\n", "    return c * torch.sinh(c * x)\n", "\n", "def f_hess(x):  # 目标函数的Hessian\n", "    return c**2 * torch.cosh(c * x)\n", "\n", "def newton(eta=1):\n", "    x = 10.0\n", "    results = [x]\n", "    for i in range(10):\n", "        x -= eta * f_grad(x) / f_hess(x)\n", "        results.append(float(x))\n", "    print('epoch 10, x:', x)\n", "    return results\n", "\n", "show_trace(newton(), f)"]}, {"cell_type": "markdown", "id": "c50b16ed", "metadata": {"origin_pos": 25}, "source": ["现在让我们考虑一个非凸函数，比如$f(x) = x \\cos(c x)$，$c$为某些常数。\n", "请注意在牛顿法中，我们最终将除以Hessian。\n", "这意味着如果二阶导数是负的，$f$的值可能会趋于增加。\n", "这是这个算法的致命缺陷！\n", "让我们看看实践中会发生什么。\n"]}, {"cell_type": "code", "execution_count": 11, "id": "cd5c68b6", "metadata": {"execution": {"iopub.execute_input": "2022-12-07T16:54:14.805000Z", "iopub.status.busy": "2022-12-07T16:54:14.804452Z", "iopub.status.idle": "2022-12-07T16:54:15.047006Z", "shell.execute_reply": "2022-12-07T16:54:15.046223Z"}, "origin_pos": 26, "tab": ["pytorch"]}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["epoch 10, x: tensor(26.8341)\n"]}, {"data": {"image/svg+xml": ["<?xml version=\"1.0\" encoding=\"utf-8\" standalone=\"no\"?>\n", "<!DOCTYPE svg PUBLIC \"-//W3C//DTD SVG 1.1//EN\"\n", "  \"http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd\">\n", "<svg xmlns:xlink=\"http://www.w3.org/1999/xlink\" width=\"251.482813pt\" height=\"183.35625pt\" viewBox=\"0 0 251.**********.35625\" xmlns=\"http://www.w3.org/2000/svg\" version=\"1.1\">\n", " <metadata>\n", "  <rdf:RDF xmlns:dc=\"http://purl.org/dc/elements/1.1/\" xmlns:cc=\"http://creativecommons.org/ns#\" xmlns:rdf=\"http://www.w3.org/1999/02/22-rdf-syntax-ns#\">\n", "   <cc:Work>\n", "    <dc:type rdf:resource=\"http://purl.org/dc/dcmitype/StillImage\"/>\n", "    <dc:date>2022-12-07T16:54:15.018163</dc:date>\n", "    <dc:format>image/svg+xml</dc:format>\n", "    <dc:creator>\n", "     <cc:Agent>\n", "      <dc:title>Matplotlib v3.5.1, https://matplotlib.org/</dc:title>\n", "     </cc:Agent>\n", "    </dc:creator>\n", "   </cc:Work>\n", "  </rdf:RDF>\n", " </metadata>\n", " <defs>\n", "  <style type=\"text/css\">*{stroke-linejoin: round; stroke-linecap: butt}</style>\n", " </defs>\n", " <g id=\"figure_1\">\n", "  <g id=\"patch_1\">\n", "   <path d=\"M -0 183.35625 \n", "L 251.**********.35625 \n", "L 251.482813 0 \n", "L -0 0 \n", "z\n", "\" style=\"fill: #ffffff\"/>\n", "  </g>\n", "  <g id=\"axes_1\">\n", "   <g id=\"patch_2\">\n", "    <path d=\"M 48.**********.8 \n", "L 244.**********.8 \n", "L 244.282813 7.2 \n", "L 48.982813 7.2 \n", "z\n", "\" style=\"fill: #ffffff\"/>\n", "   </g>\n", "   <g id=\"matplotlib.axis_1\">\n", "    <g id=\"xtick_1\">\n", "     <g id=\"line2d_1\">\n", "      <path d=\"M 82.72875 145.8 \n", "L 82.72875 7.2 \n", "\" clip-path=\"url(#p334a1e49c4)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_2\">\n", "      <defs>\n", "       <path id=\"m1d6170c986\" d=\"M 0 0 \n", "L 0 3.5 \n", "\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </defs>\n", "      <g>\n", "       <use xlink:href=\"#m1d6170c986\" x=\"82.72875\" y=\"145.8\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_1\">\n", "      <!-- −20 -->\n", "      <g transform=\"translate(72.176406 160.398438)scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-2212\" d=\"M 678 2272 \n", "L 4684 2272 \n", "L 4684 1741 \n", "L 678 1741 \n", "L 678 2272 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "        <path id=\"DejaVuSans-32\" d=\"M 1228 531 \n", "L 3431 531 \n", "L 3431 0 \n", "L 469 0 \n", "L 469 531 \n", "Q 828 903 1448 1529 \n", "Q 2069 2156 2228 2338 \n", "Q 2531 2678 2651 2914 \n", "Q 2772 3150 2772 3378 \n", "Q 2772 3750 2511 3984 \n", "Q 2250 4219 1831 4219 \n", "Q 1534 4219 1204 4116 \n", "Q 875 4013 500 3803 \n", "L 500 4441 \n", "Q 881 4594 1212 4672 \n", "Q 1544 4750 1819 4750 \n", "Q 2544 4750 2975 4387 \n", "Q 3406 4025 3406 3419 \n", "Q 3406 3131 3298 2873 \n", "Q 3191 2616 2906 2266 \n", "Q 2828 2175 2409 1742 \n", "Q 1991 1309 1228 531 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "        <path id=\"DejaVuSans-30\" d=\"M 2034 4250 \n", "Q 1547 4250 1301 3770 \n", "Q 1056 3291 1056 2328 \n", "Q 1056 1369 1301 889 \n", "Q 1547 409 2034 409 \n", "Q 2525 409 2770 889 \n", "Q 3016 1369 3016 2328 \n", "Q 3016 3291 2770 3770 \n", "Q 2525 4250 2034 4250 \n", "z\n", "M 2034 4750 \n", "Q 2819 4750 3233 4129 \n", "Q 3647 3509 3647 2328 \n", "Q 3647 1150 3233 529 \n", "Q 2819 -91 2034 -91 \n", "Q 1250 -91 836 529 \n", "Q 422 1150 422 2328 \n", "Q 422 3509 836 4129 \n", "Q 1250 4750 2034 4750 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-2212\"/>\n", "       <use xlink:href=\"#DejaVuSans-32\" x=\"83.789062\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"147.412109\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_2\">\n", "     <g id=\"line2d_3\">\n", "      <path d=\"M 146.632812 145.8 \n", "L 146.632812 7.2 \n", "\" clip-path=\"url(#p334a1e49c4)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_4\">\n", "      <g>\n", "       <use xlink:href=\"#m1d6170c986\" x=\"146.632812\" y=\"145.8\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_2\">\n", "      <!-- 0 -->\n", "      <g transform=\"translate(143.451562 160.398438)scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_3\">\n", "     <g id=\"line2d_5\">\n", "      <path d=\"M 210.536875 145.8 \n", "L 210.536875 7.2 \n", "\" clip-path=\"url(#p334a1e49c4)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_6\">\n", "      <g>\n", "       <use xlink:href=\"#m1d6170c986\" x=\"210.536875\" y=\"145.8\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_3\">\n", "      <!-- 20 -->\n", "      <g transform=\"translate(204.174375 160.398438)scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-32\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"63.623047\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"text_4\">\n", "     <!-- x -->\n", "     <g transform=\"translate(143.673438 174.076563)scale(0.1 -0.1)\">\n", "      <defs>\n", "       <path id=\"DejaVuSans-78\" d=\"M 3513 3500 \n", "L 2247 1797 \n", "L 3578 0 \n", "L 2900 0 \n", "L 1881 1375 \n", "L 863 0 \n", "L 184 0 \n", "L 1544 1831 \n", "L 300 3500 \n", "L 978 3500 \n", "L 1906 2253 \n", "L 2834 3500 \n", "L 3513 3500 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "      </defs>\n", "      <use xlink:href=\"#DejaVuSans-78\"/>\n", "     </g>\n", "    </g>\n", "   </g>\n", "   <g id=\"matplotlib.axis_2\">\n", "    <g id=\"ytick_1\">\n", "     <g id=\"line2d_7\">\n", "      <path d=\"M 48.982813 123.601727 \n", "L 244.282813 123.601727 \n", "\" clip-path=\"url(#p334a1e49c4)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_8\">\n", "      <defs>\n", "       <path id=\"mb07959f870\" d=\"M 0 0 \n", "L -3.5 0 \n", "\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </defs>\n", "      <g>\n", "       <use xlink:href=\"#mb07959f870\" x=\"48.982813\" y=\"123.601727\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_5\">\n", "      <!-- −20 -->\n", "      <g transform=\"translate(20.878125 127.400946)scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-2212\"/>\n", "       <use xlink:href=\"#DejaVuSans-32\" x=\"83.789062\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"147.412109\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_2\">\n", "     <g id=\"line2d_9\">\n", "      <path d=\"M 48.982813 100.050866 \n", "L 244.282813 100.050866 \n", "\" clip-path=\"url(#p334a1e49c4)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_10\">\n", "      <g>\n", "       <use xlink:href=\"#mb07959f870\" x=\"48.982813\" y=\"100.050866\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_6\">\n", "      <!-- −10 -->\n", "      <g transform=\"translate(20.878125 103.850084)scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-31\" d=\"M 794 531 \n", "L 1825 531 \n", "L 1825 4091 \n", "L 703 3866 \n", "L 703 4441 \n", "L 1819 4666 \n", "L 2450 4666 \n", "L 2450 531 \n", "L 3481 531 \n", "L 3481 0 \n", "L 794 0 \n", "L 794 531 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-2212\"/>\n", "       <use xlink:href=\"#DejaVuSans-31\" x=\"83.789062\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"147.412109\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_3\">\n", "     <g id=\"line2d_11\">\n", "      <path d=\"M 48.982813 76.500004 \n", "L 244.282813 76.500004 \n", "\" clip-path=\"url(#p334a1e49c4)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_12\">\n", "      <g>\n", "       <use xlink:href=\"#mb07959f870\" x=\"48.982813\" y=\"76.500004\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_7\">\n", "      <!-- 0 -->\n", "      <g transform=\"translate(35.620313 80.299223)scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_4\">\n", "     <g id=\"line2d_13\">\n", "      <path d=\"M 48.982813 52.949143 \n", "L 244.282813 52.949143 \n", "\" clip-path=\"url(#p334a1e49c4)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_14\">\n", "      <g>\n", "       <use xlink:href=\"#mb07959f870\" x=\"48.982813\" y=\"52.949143\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_8\">\n", "      <!-- 10 -->\n", "      <g transform=\"translate(29.257813 56.748362)scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-31\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"63.623047\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_5\">\n", "     <g id=\"line2d_15\">\n", "      <path d=\"M 48.982813 29.398282 \n", "L 244.282813 29.398282 \n", "\" clip-path=\"url(#p334a1e49c4)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_16\">\n", "      <g>\n", "       <use xlink:href=\"#mb07959f870\" x=\"48.982813\" y=\"29.398282\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_9\">\n", "      <!-- 20 -->\n", "      <g transform=\"translate(29.257813 33.197501)scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-32\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"63.623047\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"text_10\">\n", "     <!-- f(x) -->\n", "     <g transform=\"translate(14.798438 85.121094)rotate(-90)scale(0.1 -0.1)\">\n", "      <defs>\n", "       <path id=\"DejaVuSans-66\" d=\"M 2375 4863 \n", "L 2375 4384 \n", "L 1825 4384 \n", "Q 1516 4384 1395 4259 \n", "Q 1275 4134 1275 3809 \n", "L 1275 3500 \n", "L 2222 3500 \n", "L 2222 3053 \n", "L 1275 3053 \n", "L 1275 0 \n", "L 697 0 \n", "L 697 3053 \n", "L 147 3053 \n", "L 147 3500 \n", "L 697 3500 \n", "L 697 3744 \n", "Q 697 4328 969 4595 \n", "Q 1241 4863 1831 4863 \n", "L 2375 4863 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-28\" d=\"M 1984 4856 \n", "Q 1566 4138 1362 3434 \n", "Q 1159 2731 1159 2009 \n", "Q 1159 1288 1364 580 \n", "Q 1569 -128 1984 -844 \n", "L 1484 -844 \n", "Q 1016 -109 783 600 \n", "Q 550 1309 550 2009 \n", "Q 550 2706 781 3412 \n", "Q 1013 4119 1484 4856 \n", "L 1984 4856 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-29\" d=\"M 513 4856 \n", "L 1013 4856 \n", "Q 1481 4119 1714 3412 \n", "Q 1947 2706 1947 2009 \n", "Q 1947 1309 1714 600 \n", "Q 1481 -109 1013 -844 \n", "L 513 -844 \n", "Q 928 -128 1133 580 \n", "Q 1338 1288 1338 2009 \n", "Q 1338 2731 1133 3434 \n", "Q 928 4138 513 4856 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "      </defs>\n", "      <use xlink:href=\"#DejaVuSans-66\"/>\n", "      <use xlink:href=\"#DejaVuSans-28\" x=\"35.205078\"/>\n", "      <use xlink:href=\"#DejaVuSans-78\" x=\"74.21875\"/>\n", "      <use xlink:href=\"#DejaVuSans-29\" x=\"133.398438\"/>\n", "     </g>\n", "    </g>\n", "   </g>\n", "   <g id=\"line2d_17\">\n", "    <path d=\"M 57.860085 133.082857 \n", "L 58.690839 136.111397 \n", "L 59.393781 137.930735 \n", "L 60.000872 138.945763 \n", "L 60.512104 139.399456 \n", "L 60.927477 139.499151 \n", "L 61.342857 139.359725 \n", "L 61.79019 138.945403 \n", "L 62.301422 138.142383 \n", "L 62.908506 136.74419 \n", "L 63.643401 134.431099 \n", "L 64.506107 130.899534 \n", "L 65.496619 125.852747 \n", "L 66.678847 118.62258 \n", "L 68.148643 108.179286 \n", "L 70.289423 91.172368 \n", "L 74.12367 60.619811 \n", "L 75.721271 49.748493 \n", "L 77.031304 42.269015 \n", "L 78.149626 37.103242 \n", "L 79.108185 33.658107 \n", "L 79.938938 31.44464 \n", "L 80.641887 30.145746 \n", "L 81.248977 29.450726 \n", "L 81.760209 29.171397 \n", "L 82.239488 29.161174 \n", "L 82.718768 29.390974 \n", "L 83.23 29.895377 \n", "L 83.837084 30.832177 \n", "L 84.540032 32.356599 \n", "L 85.37078 34.730408 \n", "L 86.329344 38.173193 \n", "L 87.47962 43.171795 \n", "L 88.917463 50.482955 \n", "L 90.962391 62.180712 \n", "L 95.052254 85.784815 \n", "L 96.649852 93.588553 \n", "L 97.959882 98.933522 \n", "L 99.078207 102.611612 \n", "L 100.036766 105.058292 \n", "L 100.867519 106.628927 \n", "L 101.602418 107.585726 \n", "L 102.241458 108.087652 \n", "L 102.816592 108.27974 \n", "L 103.359777 108.239489 \n", "L 103.934914 107.968213 \n", "L 104.573957 107.40063 \n", "L 105.308853 106.418561 \n", "L 106.139603 104.913466 \n", "L 107.130117 102.625762 \n", "L 108.312342 99.296657 \n", "L 109.814089 94.348522 \n", "L 112.178538 85.66054 \n", "L 115.213981 74.680803 \n", "L 116.843535 69.614619 \n", "L 118.18552 66.138247 \n", "L 119.335792 63.746837 \n", "L 120.358259 62.113651 \n", "L 121.252915 61.076702 \n", "L 122.083668 60.441955 \n", "L 122.850517 60.131184 \n", "L 123.585414 60.072613 \n", "L 124.320311 60.236674 \n", "L 125.119111 60.650692 \n", "L 126.013769 61.379224 \n", "L 127.036233 62.510107 \n", "L 128.282362 64.235275 \n", "L 129.97582 66.991917 \n", "L 134.54496 74.625644 \n", "L 135.982802 76.500775 \n", "L 137.196978 77.739444 \n", "L 138.283347 78.54879 \n", "L 139.305813 79.042512 \n", "L 140.296325 79.275174 \n", "L 141.286839 79.277763 \n", "L 142.341255 79.05046 \n", "L 143.523481 78.55502 \n", "L 144.961322 77.694763 \n", "L 147.741149 75.693973 \n", "L 149.562415 74.539122 \n", "L 150.840496 73.976894 \n", "L 151.926865 73.728113 \n", "L 152.917378 73.719126 \n", "L 153.875939 73.928532 \n", "L 154.834499 74.36373 \n", "L 155.856965 75.080137 \n", "L 156.943334 76.118915 \n", "L 158.189463 77.634768 \n", "L 159.659257 79.795781 \n", "L 161.704186 83.240291 \n", "L 164.995246 88.782809 \n", "L 166.401136 90.699791 \n", "L 167.519457 91.866122 \n", "L 168.446065 92.532086 \n", "L 169.276819 92.864051 \n", "L 170.011714 92.929294 \n", "L 170.746613 92.766376 \n", "L 171.481508 92.365336 \n", "L 172.248357 91.68586 \n", "L 173.079111 90.64525 \n", "L 174.005717 89.113186 \n", "L 175.060136 86.908088 \n", "L 176.242361 83.890694 \n", "L 177.648249 79.647744 \n", "L 179.405612 73.583092 \n", "L 185.220883 52.899469 \n", "L 186.498963 49.476767 \n", "L 187.52143 47.320157 \n", "L 188.384134 45.968669 \n", "L 189.119029 45.189341 \n", "L 189.758069 44.808049 \n", "L 190.333206 44.710955 \n", "L 190.876391 44.839682 \n", "L 191.451529 45.214132 \n", "L 192.090569 45.921148 \n", "L 192.793511 47.054178 \n", "L 193.592312 48.790423 \n", "L 194.486971 51.288754 \n", "L 195.509435 54.827196 \n", "L 196.69166 59.752424 \n", "L 198.097548 66.609082 \n", "L 199.918813 76.688926 \n", "L 205.542371 108.663821 \n", "L 206.884357 114.620468 \n", "L 207.970726 118.510918 \n", "L 208.897338 121.058126 \n", "L 209.664186 122.57422 \n", "L 210.303229 123.401654 \n", "L 210.846414 123.780566 \n", "L 211.325693 123.860819 \n", "L 211.773019 123.717148 \n", "L 212.252299 123.326463 \n", "L 212.795483 122.585832 \n", "L 213.43452 121.309331 \n", "L 214.169422 119.304668 \n", "L 215.000175 116.362512 \n", "L 215.95874 112.113713 \n", "L 217.077056 106.083903 \n", "L 218.387089 97.728059 \n", "L 220.016643 85.794207 \n", "L 222.540857 65.407018 \n", "L 225.480438 42.114412 \n", "L 227.046086 31.426842 \n", "L 228.29222 24.356396 \n", "L 229.314684 19.730048 \n", "L 230.17739 16.759931 \n", "L 230.912285 14.958529 \n", "L 231.519376 14.001892 \n", "L 232.030608 13.581246 \n", "L 232.445981 13.503661 \n", "L 232.861355 13.665781 \n", "L 233.308688 14.110346 \n", "L 233.819926 14.962253 \n", "L 234.42701 16.449086 \n", "L 235.129959 18.808408 \n", "L 235.385569 19.833382 \n", "L 235.385569 19.833382 \n", "\" clip-path=\"url(#p334a1e49c4)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_18\">\n", "    <path d=\"M 178.584844 76.500004 \n", "L 162.608828 84.826491 \n", "L 226.496497 34.97686 \n", "L 235.40554 19.917152 \n", "L 232.264564 13.508198 \n", "L 232.373458 13.5 \n", "L 232.373312 13.5 \n", "L 232.373318 13.5 \n", "L 232.373318 13.5 \n", "L 232.373318 13.5 \n", "L 232.373318 13.5 \n", "\" clip-path=\"url(#p334a1e49c4)\" style=\"fill: none; stroke: #ff7f0e; stroke-width: 1.5; stroke-linecap: square\"/>\n", "    <defs>\n", "     <path id=\"m327ae546ff\" d=\"M 0 3 \n", "C 0.795609 3 1.55874 2.683901 2.12132 2.12132 \n", "C 2.683901 1.55874 3 0.795609 3 0 \n", "C 3 -0.795609 2.683901 -1.55874 2.12132 -2.12132 \n", "C 1.55874 -2.683901 0.795609 -3 0 -3 \n", "C -0.795609 -3 -1.55874 -2.683901 -2.12132 -2.12132 \n", "C -2.683901 -1.55874 -3 -0.795609 -3 0 \n", "C -3 0.795609 -2.683901 1.55874 -2.12132 2.12132 \n", "C -1.55874 2.683901 -0.795609 3 0 3 \n", "z\n", "\" style=\"stroke: #ff7f0e\"/>\n", "    </defs>\n", "    <g clip-path=\"url(#p334a1e49c4)\">\n", "     <use xlink:href=\"#m327ae546ff\" x=\"178.584844\" y=\"76.500004\" style=\"fill: #ff7f0e; stroke: #ff7f0e\"/>\n", "     <use xlink:href=\"#m327ae546ff\" x=\"162.608828\" y=\"84.826491\" style=\"fill: #ff7f0e; stroke: #ff7f0e\"/>\n", "     <use xlink:href=\"#m327ae546ff\" x=\"226.496497\" y=\"34.97686\" style=\"fill: #ff7f0e; stroke: #ff7f0e\"/>\n", "     <use xlink:href=\"#m327ae546ff\" x=\"235.40554\" y=\"19.917152\" style=\"fill: #ff7f0e; stroke: #ff7f0e\"/>\n", "     <use xlink:href=\"#m327ae546ff\" x=\"232.264564\" y=\"13.508198\" style=\"fill: #ff7f0e; stroke: #ff7f0e\"/>\n", "     <use xlink:href=\"#m327ae546ff\" x=\"232.373458\" y=\"13.5\" style=\"fill: #ff7f0e; stroke: #ff7f0e\"/>\n", "     <use xlink:href=\"#m327ae546ff\" x=\"232.373312\" y=\"13.5\" style=\"fill: #ff7f0e; stroke: #ff7f0e\"/>\n", "     <use xlink:href=\"#m327ae546ff\" x=\"232.373318\" y=\"13.5\" style=\"fill: #ff7f0e; stroke: #ff7f0e\"/>\n", "     <use xlink:href=\"#m327ae546ff\" x=\"232.373318\" y=\"13.5\" style=\"fill: #ff7f0e; stroke: #ff7f0e\"/>\n", "     <use xlink:href=\"#m327ae546ff\" x=\"232.373318\" y=\"13.5\" style=\"fill: #ff7f0e; stroke: #ff7f0e\"/>\n", "     <use xlink:href=\"#m327ae546ff\" x=\"232.373318\" y=\"13.5\" style=\"fill: #ff7f0e; stroke: #ff7f0e\"/>\n", "    </g>\n", "   </g>\n", "   <g id=\"patch_3\">\n", "    <path d=\"M 48.**********.8 \n", "L 48.982813 7.2 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_4\">\n", "    <path d=\"M 244.**********.8 \n", "L 244.282813 7.2 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_5\">\n", "    <path d=\"M 48.**********.8 \n", "L 244.282812 145.8 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_6\">\n", "    <path d=\"M 48.982813 7.2 \n", "L 244.282812 7.2 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "  </g>\n", " </g>\n", " <defs>\n", "  <clipPath id=\"p334a1e49c4\">\n", "   <rect x=\"48.982813\" y=\"7.2\" width=\"195.3\" height=\"138.6\"/>\n", "  </clipPath>\n", " </defs>\n", "</svg>\n"], "text/plain": ["<Figure size 350x250 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["c = torch.tensor(0.15 * np.pi)\n", "\n", "def f(x):  # 目标函数\n", "    return x * torch.cos(c * x)\n", "\n", "def f_grad(x):  # 目标函数的梯度\n", "    return torch.cos(c * x) - c * x * torch.sin(c * x)\n", "\n", "def f_hess(x):  # 目标函数的Hessian\n", "    return - 2 * c * torch.sin(c * x) - x * c**2 * torch.cos(c * x)\n", "\n", "show_trace(newton(), f)"]}, {"cell_type": "markdown", "id": "5d9dbe37", "metadata": {"origin_pos": 27}, "source": ["这发生了惊人的错误。我们怎样才能修正它？\n", "一种方法是用取Hessian的绝对值来修正，另一个策略是重新引入学习率。\n", "这似乎违背了初衷，但不完全是——拥有二阶信息可以使我们在曲率较大时保持谨慎，而在目标函数较平坦时则采用较大的学习率。\n", "让我们看看在学习率稍小的情况下它是如何生效的，比如$\\eta = 0.5$。\n", "如我们所见，我们有了一个相当高效的算法。\n"]}, {"cell_type": "code", "execution_count": 12, "id": "7cc98ee3", "metadata": {"execution": {"iopub.execute_input": "2022-12-07T16:54:15.050618Z", "iopub.status.busy": "2022-12-07T16:54:15.049856Z", "iopub.status.idle": "2022-12-07T16:54:15.228378Z", "shell.execute_reply": "2022-12-07T16:54:15.227526Z"}, "origin_pos": 28, "tab": ["pytorch"]}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["epoch 10, x: tensor(7.2699)\n"]}, {"data": {"image/svg+xml": ["<?xml version=\"1.0\" encoding=\"utf-8\" standalone=\"no\"?>\n", "<!DOCTYPE svg PUBLIC \"-//W3C//DTD SVG 1.1//EN\"\n", "  \"http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd\">\n", "<svg xmlns:xlink=\"http://www.w3.org/1999/xlink\" width=\"245.120313pt\" height=\"183.35625pt\" viewBox=\"0 0 245.**********.35625\" xmlns=\"http://www.w3.org/2000/svg\" version=\"1.1\">\n", " <metadata>\n", "  <rdf:RDF xmlns:dc=\"http://purl.org/dc/elements/1.1/\" xmlns:cc=\"http://creativecommons.org/ns#\" xmlns:rdf=\"http://www.w3.org/1999/02/22-rdf-syntax-ns#\">\n", "   <cc:Work>\n", "    <dc:type rdf:resource=\"http://purl.org/dc/dcmitype/StillImage\"/>\n", "    <dc:date>2022-12-07T16:54:15.199949</dc:date>\n", "    <dc:format>image/svg+xml</dc:format>\n", "    <dc:creator>\n", "     <cc:Agent>\n", "      <dc:title>Matplotlib v3.5.1, https://matplotlib.org/</dc:title>\n", "     </cc:Agent>\n", "    </dc:creator>\n", "   </cc:Work>\n", "  </rdf:RDF>\n", " </metadata>\n", " <defs>\n", "  <style type=\"text/css\">*{stroke-linejoin: round; stroke-linecap: butt}</style>\n", " </defs>\n", " <g id=\"figure_1\">\n", "  <g id=\"patch_1\">\n", "   <path d=\"M 0 183.35625 \n", "L 245.**********.35625 \n", "L 245.120313 0 \n", "L 0 0 \n", "z\n", "\" style=\"fill: #ffffff\"/>\n", "  </g>\n", "  <g id=\"axes_1\">\n", "   <g id=\"patch_2\">\n", "    <path d=\"M 42.**********.8 \n", "L 237.**********.8 \n", "L 237.920313 7.2 \n", "L 42.620312 7.2 \n", "z\n", "\" style=\"fill: #ffffff\"/>\n", "   </g>\n", "   <g id=\"matplotlib.axis_1\">\n", "    <g id=\"xtick_1\">\n", "     <g id=\"line2d_1\">\n", "      <path d=\"M 51.497585 145.8 \n", "L 51.497585 7.2 \n", "\" clip-path=\"url(#p36baf72a2a)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_2\">\n", "      <defs>\n", "       <path id=\"maaf0ea7eb5\" d=\"M 0 0 \n", "L 0 3.5 \n", "\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </defs>\n", "      <g>\n", "       <use xlink:href=\"#maaf0ea7eb5\" x=\"51.497585\" y=\"145.8\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_1\">\n", "      <!-- −10 -->\n", "      <g transform=\"translate(40.945241 160.398438)scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-2212\" d=\"M 678 2272 \n", "L 4684 2272 \n", "L 4684 1741 \n", "L 678 1741 \n", "L 678 2272 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "        <path id=\"DejaVuSans-31\" d=\"M 794 531 \n", "L 1825 531 \n", "L 1825 4091 \n", "L 703 3866 \n", "L 703 4441 \n", "L 1819 4666 \n", "L 2450 4666 \n", "L 2450 531 \n", "L 3481 531 \n", "L 3481 0 \n", "L 794 0 \n", "L 794 531 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "        <path id=\"DejaVuSans-30\" d=\"M 2034 4250 \n", "Q 1547 4250 1301 3770 \n", "Q 1056 3291 1056 2328 \n", "Q 1056 1369 1301 889 \n", "Q 1547 409 2034 409 \n", "Q 2525 409 2770 889 \n", "Q 3016 1369 3016 2328 \n", "Q 3016 3291 2770 3770 \n", "Q 2525 4250 2034 4250 \n", "z\n", "M 2034 4750 \n", "Q 2819 4750 3233 4129 \n", "Q 3647 3509 3647 2328 \n", "Q 3647 1150 3233 529 \n", "Q 2819 -91 2034 -91 \n", "Q 1250 -91 836 529 \n", "Q 422 1150 422 2328 \n", "Q 422 3509 836 4129 \n", "Q 1250 4750 2034 4750 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-2212\"/>\n", "       <use xlink:href=\"#DejaVuSans-31\" x=\"83.789062\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"147.412109\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_2\">\n", "     <g id=\"line2d_3\">\n", "      <path d=\"M 95.883949 145.8 \n", "L 95.883949 7.2 \n", "\" clip-path=\"url(#p36baf72a2a)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_4\">\n", "      <g>\n", "       <use xlink:href=\"#maaf0ea7eb5\" x=\"95.883949\" y=\"145.8\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_2\">\n", "      <!-- −5 -->\n", "      <g transform=\"translate(88.512855 160.398438)scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-35\" d=\"M 691 4666 \n", "L 3169 4666 \n", "L 3169 4134 \n", "L 1269 4134 \n", "L 1269 2991 \n", "Q 1406 3038 1543 3061 \n", "Q 1681 3084 1819 3084 \n", "Q 2600 3084 3056 2656 \n", "Q 3513 2228 3513 1497 \n", "Q 3513 744 3044 326 \n", "Q 2575 -91 1722 -91 \n", "Q 1428 -91 1123 -41 \n", "Q 819 9 494 109 \n", "L 494 744 \n", "Q 775 591 1075 516 \n", "Q 1375 441 1709 441 \n", "Q 2250 441 2565 725 \n", "Q 2881 1009 2881 1497 \n", "Q 2881 1984 2565 2268 \n", "Q 2250 2553 1709 2553 \n", "Q 1456 2553 1204 2497 \n", "Q 953 2441 691 2322 \n", "L 691 4666 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-2212\"/>\n", "       <use xlink:href=\"#DejaVuSans-35\" x=\"83.789062\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_3\">\n", "     <g id=\"line2d_5\">\n", "      <path d=\"M 140.270312 145.8 \n", "L 140.270312 7.2 \n", "\" clip-path=\"url(#p36baf72a2a)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_6\">\n", "      <g>\n", "       <use xlink:href=\"#maaf0ea7eb5\" x=\"140.270312\" y=\"145.8\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_3\">\n", "      <!-- 0 -->\n", "      <g transform=\"translate(137.089062 160.398438)scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_4\">\n", "     <g id=\"line2d_7\">\n", "      <path d=\"M 184.656676 145.8 \n", "L 184.656676 7.2 \n", "\" clip-path=\"url(#p36baf72a2a)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_8\">\n", "      <g>\n", "       <use xlink:href=\"#maaf0ea7eb5\" x=\"184.656676\" y=\"145.8\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_4\">\n", "      <!-- 5 -->\n", "      <g transform=\"translate(181.475426 160.398438)scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-35\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_5\">\n", "     <g id=\"line2d_9\">\n", "      <path d=\"M 229.04304 145.8 \n", "L 229.04304 7.2 \n", "\" clip-path=\"url(#p36baf72a2a)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_10\">\n", "      <g>\n", "       <use xlink:href=\"#maaf0ea7eb5\" x=\"229.04304\" y=\"145.8\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_5\">\n", "      <!-- 10 -->\n", "      <g transform=\"translate(222.68054 160.398438)scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-31\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"63.623047\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"text_6\">\n", "     <!-- x -->\n", "     <g transform=\"translate(137.310937 174.076563)scale(0.1 -0.1)\">\n", "      <defs>\n", "       <path id=\"DejaVuSans-78\" d=\"M 3513 3500 \n", "L 2247 1797 \n", "L 3578 0 \n", "L 2900 0 \n", "L 1881 1375 \n", "L 863 0 \n", "L 184 0 \n", "L 1544 1831 \n", "L 300 3500 \n", "L 978 3500 \n", "L 1906 2253 \n", "L 2834 3500 \n", "L 3513 3500 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "      </defs>\n", "      <use xlink:href=\"#DejaVuSans-78\"/>\n", "     </g>\n", "    </g>\n", "   </g>\n", "   <g id=\"matplotlib.axis_2\">\n", "    <g id=\"ytick_1\">\n", "     <g id=\"line2d_11\">\n", "      <path d=\"M 42.620312 121.640967 \n", "L 237.920313 121.640967 \n", "\" clip-path=\"url(#p36baf72a2a)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_12\">\n", "      <defs>\n", "       <path id=\"mbc3901d9e0\" d=\"M 0 0 \n", "L -3.5 0 \n", "\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </defs>\n", "      <g>\n", "       <use xlink:href=\"#mbc3901d9e0\" x=\"42.620312\" y=\"121.640967\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_7\">\n", "      <!-- −5 -->\n", "      <g transform=\"translate(20.878125 125.440185)scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-2212\"/>\n", "       <use xlink:href=\"#DejaVuSans-35\" x=\"83.789062\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_2\">\n", "     <g id=\"line2d_13\">\n", "      <path d=\"M 42.620312 76.5 \n", "L 237.920313 76.5 \n", "\" clip-path=\"url(#p36baf72a2a)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_14\">\n", "      <g>\n", "       <use xlink:href=\"#mbc3901d9e0\" x=\"42.620312\" y=\"76.5\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_8\">\n", "      <!-- 0 -->\n", "      <g transform=\"translate(29.257812 80.299219)scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_3\">\n", "     <g id=\"line2d_15\">\n", "      <path d=\"M 42.620312 31.359033 \n", "L 237.920313 31.359033 \n", "\" clip-path=\"url(#p36baf72a2a)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_16\">\n", "      <g>\n", "       <use xlink:href=\"#mbc3901d9e0\" x=\"42.620312\" y=\"31.359033\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_9\">\n", "      <!-- 5 -->\n", "      <g transform=\"translate(29.257812 35.158252)scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-35\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"text_10\">\n", "     <!-- f(x) -->\n", "     <g transform=\"translate(14.798437 85.121094)rotate(-90)scale(0.1 -0.1)\">\n", "      <defs>\n", "       <path id=\"DejaVuSans-66\" d=\"M 2375 4863 \n", "L 2375 4384 \n", "L 1825 4384 \n", "Q 1516 4384 1395 4259 \n", "Q 1275 4134 1275 3809 \n", "L 1275 3500 \n", "L 2222 3500 \n", "L 2222 3053 \n", "L 1275 3053 \n", "L 1275 0 \n", "L 697 0 \n", "L 697 3053 \n", "L 147 3053 \n", "L 147 3500 \n", "L 697 3500 \n", "L 697 3744 \n", "Q 697 4328 969 4595 \n", "Q 1241 4863 1831 4863 \n", "L 2375 4863 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-28\" d=\"M 1984 4856 \n", "Q 1566 4138 1362 3434 \n", "Q 1159 2731 1159 2009 \n", "Q 1159 1288 1364 580 \n", "Q 1569 -128 1984 -844 \n", "L 1484 -844 \n", "Q 1016 -109 783 600 \n", "Q 550 1309 550 2009 \n", "Q 550 2706 781 3412 \n", "Q 1013 4119 1484 4856 \n", "L 1984 4856 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-29\" d=\"M 513 4856 \n", "L 1013 4856 \n", "Q 1481 4119 1714 3412 \n", "Q 1947 2706 1947 2009 \n", "Q 1947 1309 1714 600 \n", "Q 1481 -109 1013 -844 \n", "L 513 -844 \n", "Q 928 -128 1133 580 \n", "Q 1338 1288 1338 2009 \n", "Q 1338 2731 1133 3434 \n", "Q 928 4138 513 4856 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "      </defs>\n", "      <use xlink:href=\"#DejaVuSans-66\"/>\n", "      <use xlink:href=\"#DejaVuSans-28\" x=\"35.205078\"/>\n", "      <use xlink:href=\"#DejaVuSans-78\" x=\"74.21875\"/>\n", "      <use xlink:href=\"#DejaVuSans-29\" x=\"133.398438\"/>\n", "     </g>\n", "    </g>\n", "   </g>\n", "   <g id=\"line2d_17\">\n", "    <path d=\"M 51.497585 76.500001 \n", "L 54.515859 62.586451 \n", "L 57.090268 51.752961 \n", "L 59.309586 43.325861 \n", "L 61.351355 36.406086 \n", "L 63.215583 30.83764 \n", "L 64.991044 26.232394 \n", "L 66.588956 22.687908 \n", "L 68.098084 19.871522 \n", "L 69.518451 17.695347 \n", "L 70.761268 16.168286 \n", "L 72.004085 14.990569 \n", "L 73.158131 14.20593 \n", "L 74.223403 13.741354 \n", "L 75.288675 13.521051 \n", "L 76.353951 13.539197 \n", "L 77.419222 13.789079 \n", "L 78.484494 14.263143 \n", "L 79.63854 15.020002 \n", "L 80.881362 16.105041 \n", "L 82.212949 17.56077 \n", "L 83.722086 19.55149 \n", "L 85.408769 22.166597 \n", "L 87.272996 25.480233 \n", "L 89.40354 29.726556 \n", "L 91.977948 35.366172 \n", "L 95.528858 43.724429 \n", "L 103.163314 61.839575 \n", "L 106.004039 67.93953 \n", "L 108.400904 72.589372 \n", "L 110.53145 76.262462 \n", "L 112.484448 79.203521 \n", "L 114.259903 81.497532 \n", "L 115.946585 83.326808 \n", "L 117.544495 84.737454 \n", "L 119.05363 85.779104 \n", "L 120.473994 86.502463 \n", "L 121.894359 86.980124 \n", "L 123.314722 87.218052 \n", "L 124.735085 87.224602 \n", "L 126.155449 87.010408 \n", "L 127.664585 86.555312 \n", "L 129.262494 85.837475 \n", "L 131.037949 84.784115 \n", "L 132.990949 83.357255 \n", "L 135.29904 81.380765 \n", "L 138.317313 78.475538 \n", "L 145.951767 70.982751 \n", "L 148.259858 69.094507 \n", "L 150.212858 67.764372 \n", "L 151.988312 66.814949 \n", "L 153.586222 66.202362 \n", "L 155.095359 65.855652 \n", "L 156.515722 65.750362 \n", "L 157.936085 65.871412 \n", "L 159.356448 66.228309 \n", "L 160.776812 66.828228 \n", "L 162.197176 67.675904 \n", "L 163.706313 68.850493 \n", "L 165.215448 70.306795 \n", "L 166.813358 72.151568 \n", "L 168.588811 74.556208 \n", "L 170.453039 77.464178 \n", "L 172.494811 81.066691 \n", "L 174.802902 85.6074 \n", "L 177.466086 91.359601 \n", "L 180.83945 99.206191 \n", "L 190.071814 120.997816 \n", "L 192.557452 126.154107 \n", "L 194.687995 130.082443 \n", "L 196.552223 133.061892 \n", "L 198.150131 135.22328 \n", "L 199.659268 136.894963 \n", "L 200.990859 138.046954 \n", "L 202.233676 138.831093 \n", "L 203.387723 139.294717 \n", "L 204.452994 139.487343 \n", "L 205.51827 139.446472 \n", "L 206.583542 139.165611 \n", "L 207.648814 138.639155 \n", "L 208.714086 137.862429 \n", "L 209.868132 136.734267 \n", "L 211.110949 135.182124 \n", "L 212.442541 133.128478 \n", "L 213.862903 130.492079 \n", "L 215.37204 127.189752 \n", "L 216.969952 123.138737 \n", "L 218.745405 117.986217 \n", "L 220.609632 111.867896 \n", "L 222.651401 104.384167 \n", "L 224.959502 95.024237 \n", "L 227.533903 83.601979 \n", "L 228.954265 76.925033 \n", "L 228.954265 76.925033 \n", "\" clip-path=\"url(#p36baf72a2a)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_18\">\n", "    <path d=\"M 229.04304 76.499999 \n", "L 206.849858 139.05722 \n", "L 205.848192 139.385423 \n", "L 205.331865 139.470774 \n", "L 205.069008 139.492617 \n", "L 204.936286 139.498145 \n", "L 204.869587 139.499539 \n", "L 204.83615 139.499884 \n", "L 204.819408 139.499974 \n", "L 204.811031 139.499996 \n", "L 204.806841 139.5 \n", "\" clip-path=\"url(#p36baf72a2a)\" style=\"fill: none; stroke: #ff7f0e; stroke-width: 1.5; stroke-linecap: square\"/>\n", "    <defs>\n", "     <path id=\"mdf988ccef2\" d=\"M 0 3 \n", "C 0.795609 3 1.55874 2.683901 2.12132 2.12132 \n", "C 2.683901 1.55874 3 0.795609 3 0 \n", "C 3 -0.795609 2.683901 -1.55874 2.12132 -2.12132 \n", "C 1.55874 -2.683901 0.795609 -3 0 -3 \n", "C -0.795609 -3 -1.55874 -2.683901 -2.12132 -2.12132 \n", "C -2.683901 -1.55874 -3 -0.795609 -3 0 \n", "C -3 0.795609 -2.683901 1.55874 -2.12132 2.12132 \n", "C -1.55874 2.683901 -0.795609 3 0 3 \n", "z\n", "\" style=\"stroke: #ff7f0e\"/>\n", "    </defs>\n", "    <g clip-path=\"url(#p36baf72a2a)\">\n", "     <use xlink:href=\"#mdf988ccef2\" x=\"229.04304\" y=\"76.499999\" style=\"fill: #ff7f0e; stroke: #ff7f0e\"/>\n", "     <use xlink:href=\"#mdf988ccef2\" x=\"206.849858\" y=\"139.05722\" style=\"fill: #ff7f0e; stroke: #ff7f0e\"/>\n", "     <use xlink:href=\"#mdf988ccef2\" x=\"205.848192\" y=\"139.385423\" style=\"fill: #ff7f0e; stroke: #ff7f0e\"/>\n", "     <use xlink:href=\"#mdf988ccef2\" x=\"205.331865\" y=\"139.470774\" style=\"fill: #ff7f0e; stroke: #ff7f0e\"/>\n", "     <use xlink:href=\"#mdf988ccef2\" x=\"205.069008\" y=\"139.492617\" style=\"fill: #ff7f0e; stroke: #ff7f0e\"/>\n", "     <use xlink:href=\"#mdf988ccef2\" x=\"204.936286\" y=\"139.498145\" style=\"fill: #ff7f0e; stroke: #ff7f0e\"/>\n", "     <use xlink:href=\"#mdf988ccef2\" x=\"204.869587\" y=\"139.499539\" style=\"fill: #ff7f0e; stroke: #ff7f0e\"/>\n", "     <use xlink:href=\"#mdf988ccef2\" x=\"204.83615\" y=\"139.499884\" style=\"fill: #ff7f0e; stroke: #ff7f0e\"/>\n", "     <use xlink:href=\"#mdf988ccef2\" x=\"204.819408\" y=\"139.499974\" style=\"fill: #ff7f0e; stroke: #ff7f0e\"/>\n", "     <use xlink:href=\"#mdf988ccef2\" x=\"204.811031\" y=\"139.499996\" style=\"fill: #ff7f0e; stroke: #ff7f0e\"/>\n", "     <use xlink:href=\"#mdf988ccef2\" x=\"204.806841\" y=\"139.5\" style=\"fill: #ff7f0e; stroke: #ff7f0e\"/>\n", "    </g>\n", "   </g>\n", "   <g id=\"patch_3\">\n", "    <path d=\"M 42.**********.8 \n", "L 42.620312 7.2 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_4\">\n", "    <path d=\"M 237.**********.8 \n", "L 237.920313 7.2 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_5\">\n", "    <path d=\"M 42.**********.8 \n", "L 237.**********.8 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_6\">\n", "    <path d=\"M 42.620312 7.2 \n", "L 237.920313 7.2 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "  </g>\n", " </g>\n", " <defs>\n", "  <clipPath id=\"p36baf72a2a\">\n", "   <rect x=\"42.620312\" y=\"7.2\" width=\"195.3\" height=\"138.6\"/>\n", "  </clipPath>\n", " </defs>\n", "</svg>\n"], "text/plain": ["<Figure size 350x250 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["show_trace(newton(0.5), f)"]}, {"cell_type": "markdown", "id": "43a7e312", "metadata": {"origin_pos": 29}, "source": ["### 收敛性分析\n", "\n", "在此，我们以部分目标凸函数$f$为例，分析它们的牛顿法收敛速度。\n", "这些目标凸函数三次可微，而且二阶导数不为零，即$f'' > 0$。\n", "由于多变量情况下的证明是对以下一维参数情况证明的直接拓展，对我们理解这个问题不能提供更多帮助，因此我们省略了多变量情况的证明。\n", "\n", "用$x^{(k)}$表示$x$在第$k^\\mathrm{th}$次迭代时的值，\n", "令$e^{(k)} \\stackrel{\\mathrm{def}}{=} x^{(k)} - x^*$表示$k^\\mathrm{th}$迭代时与最优性的距离。\n", "通过泰勒展开，我们得到条件$f'(x^*) = 0$可以写成\n", "\n", "$$0 = f'(x^{(k)} - e^{(k)}) = f'(x^{(k)}) - e^{(k)} f''(x^{(k)}) + \\frac{1}{2} (e^{(k)})^2 f'''(\\xi^{(k)}),$$\n", "\n", "这对某些$\\xi^{(k)} \\in [x^{(k)} - e^{(k)}, x^{(k)}]$成立。\n", "将上述展开除以$f''(x^{(k)})$得到\n", "\n", "$$e^{(k)} - \\frac{f'(x^{(k)})}{f''(x^{(k)})} = \\frac{1}{2} (e^{(k)})^2 \\frac{f'''(\\xi^{(k)})}{f''(x^{(k)})}.$$\n", "\n", "回想之前的方程$x^{(k+1)} = x^{(k)} - f'(x^{(k)}) / f''(x^{(k)})$。\n", "代入这个更新方程，取两边的绝对值，我们得到\n", "\n", "$$\\left|e^{(k+1)}\\right| = \\frac{1}{2}(e^{(k)})^2 \\frac{\\left|f'''(\\xi^{(k)})\\right|}{f''(x^{(k)})}.$$\n", "\n", "因此，每当我们处于有界区域$\\left|f'''(\\xi^{(k)})\\right| / (2f''(x^{(k)})) \\leq c$，\n", "我们就有一个二次递减误差\n", "\n", "$$\\left|e^{(k+1)}\\right| \\leq c (e^{(k)})^2.$$\n", "\n", "另一方面，优化研究人员称之为“线性”收敛，而将$\\left|e^{(k+1)}\\right| \\leq \\alpha \\left|e^{(k)}\\right|$这样的条件称为“恒定”收敛速度。\n", "请注意，我们无法估计整体收敛的速度，但是一旦我们接近极小值，收敛将变得非常快。\n", "另外，这种分析要求$f$在高阶导数上表现良好，即确保$f$在如何变化它的值方面没有任何“超常”的特性。\n", "\n", "### 预处理\n", "\n", "计算和存储完整的Hessian非常昂贵，而改善这个问题的一种方法是“预处理”。\n", "它回避了计算整个Hessian，而只计算“对角线”项，即如下的算法更新：\n", "\n", "$$\\mathbf{x} \\leftarrow \\mathbf{x} - \\eta \\mathrm{diag}(\\mathbf{H})^{-1} \\nabla f(\\mathbf{x}).$$\n", "\n", "虽然这不如完整的牛顿法精确，但它仍然比不使用要好得多。\n", "为什么预处理有效呢？\n", "假设一个变量以毫米表示高度，另一个变量以公里表示高度的情况。\n", "假设这两种自然尺度都以米为单位，那么我们的参数化就出现了严重的不匹配。\n", "幸运的是，使用预处理可以消除这种情况。\n", "梯度下降的有效预处理相当于为每个变量选择不同的学习率（矢量$\\mathbf{x}$的坐标）。\n", "我们将在后面一节看到，预处理推动了随机梯度下降优化算法的一些创新。\n", "\n", "### 梯度下降和线搜索\n", "\n", "梯度下降的一个关键问题是我们可能会超过目标或进展不足，\n", "解决这一问题的简单方法是结合使用线搜索和梯度下降。\n", "也就是说，我们使用$\\nabla f(\\mathbf{x})$给出的方向，\n", "然后进行二分搜索，以确定哪个学习率$\\eta$使$f(\\mathbf{x} - \\eta \\nabla f(\\mathbf{x}))$取最小值。\n", "\n", "有关分析和证明，此算法收敛迅速（请参见 :cite:<PERSON><PERSON>.Vandenberghe.2004`）。\n", "然而，对深度学习而言，这不太可行。\n", "因为线搜索的每一步都需要评估整个数据集上的目标函数，实现它的方式太昂贵了。\n", "\n", "## 小结\n", "\n", "* 学习率的大小很重要：学习率太大会使模型发散，学习率太小会没有进展。\n", "* 梯度下降会可能陷入局部极小值，而得不到全局最小值。\n", "* 在高维模型中，调整学习率是很复杂的。\n", "* 预处理有助于调节比例。\n", "* 牛顿法在凸问题中一旦开始正常工作，速度就会快得多。\n", "* 对于非凸问题，不要不作任何调整就使用牛顿法。\n", "\n", "## 练习\n", "\n", "1. 用不同的学习率和目标函数进行梯度下降实验。\n", "1. 在区间$[a, b]$中实现线搜索以最小化凸函数。\n", "    1. 是否需要导数来进行二分搜索，即决定选择$[a, (a+b)/2]$还是$[(a+b)/2, b]$。\n", "    1. 算法的收敛速度有多快？\n", "    1. 实现该算法，并将其应用于求$\\log (\\exp(x) + \\exp(-2x -3))$的最小值。\n", "1. 设计一个定义在$\\mathbb{R}^2$上的目标函数，它的梯度下降非常缓慢。提示：不同坐标的缩放方式不同。\n", "1. 使用预处理实现牛顿方法的轻量版本。\n", "    1. 使用对角Hessian作为预条件子。\n", "    1. 使用它的绝对值，而不是实际值（可能有符号）。\n", "    1. 将此应用于上述问题。\n", "1. 将上述算法应用于多个目标函数（凸或非凸）。如果把坐标旋转$45$度会怎么样？\n"]}, {"cell_type": "markdown", "id": "959a0b30", "metadata": {"origin_pos": 31, "tab": ["pytorch"]}, "source": ["[Discussions](https://discuss.d2l.ai/t/3836)\n"]}], "metadata": {"language_info": {"name": "python"}}, "nbformat": 4, "nbformat_minor": 5}