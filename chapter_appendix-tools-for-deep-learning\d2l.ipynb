{"cells": [{"cell_type": "markdown", "id": "73e7991c", "metadata": {"origin_pos": 0}, "source": ["# `d2l` API 文档\n", ":label:`sec_d2l`\n", "\n", "`d2l`包以下成员的实现及其定义和解释部分可在[源文件](https://github.com/d2l-ai/d2l-en/tree/master/d2l)中找到。\n"]}, {"cell_type": "markdown", "id": "7a383323", "metadata": {"origin_pos": 2, "tab": ["pytorch"]}, "source": ["```eval_rst\n", ".. currentmodule:: d2l.torch\n", "```\n"]}, {"cell_type": "markdown", "id": "3da62a43", "metadata": {"origin_pos": 5}, "source": ["## 模型\n", "\n", "```eval_rst\n", ".. autoclass:: <PERSON><PERSON><PERSON>\n", "   :members:\n", "\n", ".. autoclass:: LinearRegressionScratch\n", "   :members:\n", "\n", ".. autoclass:: LinearRegression\n", "   :members:\n", "\n", ".. autoclass:: Classification\n", "   :members:\n", "```\n", "\n", "## 数据\n", "\n", "```eval_rst\n", ".. autoclass:: DataModule\n", "   :members:\n", "\n", ".. autoclass:: SyntheticRegressionData\n", "   :members:\n", "\n", ".. autoclass:: FashionMNIST\n", "   :members:\n", "```\n", "\n", "## 训练\n", "\n", "```eval_rst\n", ".. autoclass:: Trainer\n", "   :members:\n", "\n", ".. autoclass:: SGD\n", "   :members:\n", "```\n", "\n", "## 公用\n", "\n", "```eval_rst\n", ".. autofunction:: add_to_class\n", "\n", ".. autofunction:: cpu\n", "\n", ".. autofunction:: gpu\n", "\n", ".. autofunction:: num_gpus\n", "\n", ".. autoclass:: ProgressBoard\n", "   :members:\n", "\n", ".. autoclass:: HyperParameters\n", "   :members:\n", "```\n"]}], "metadata": {"language_info": {"name": "python"}}, "nbformat": 4, "nbformat_minor": 5}