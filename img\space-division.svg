<?xml version="1.0" encoding="UTF-8"?>
<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="202pt" height="175pt" viewBox="0 0 202 175" version="1.1">
<defs>
<g>
<symbol overflow="visible" id="glyph0-0">
<path style="stroke:none;" d=""/>
</symbol>
<symbol overflow="visible" id="glyph0-1">
<path style="stroke:none;" d="M 2.734375 1.453125 C 1.46875 0.390625 1.203125 -0.625 1.203125 -2.296875 C 1.203125 -4.03125 1.484375 -4.875 2.734375 -5.9375 L 2.65625 -6.078125 C 1.21875 -5.234375 0.4375 -3.9375 0.4375 -2.265625 C 0.4375 -0.734375 1.203125 0.78125 2.625 1.59375 Z M 2.734375 1.453125 "/>
</symbol>
<symbol overflow="visible" id="glyph0-2">
<path style="stroke:none;" d="M 4.265625 -1.234375 L 4.140625 -1.28125 C 3.84375 -0.78125 3.65625 -0.6875 3.28125 -0.6875 L 1.171875 -0.6875 L 2.65625 -2.265625 C 3.453125 -3.109375 3.8125 -3.78125 3.8125 -4.5 C 3.8125 -5.390625 3.15625 -6.078125 2.140625 -6.078125 C 1.03125 -6.078125 0.453125 -5.34375 0.265625 -4.296875 L 0.453125 -4.25 C 0.8125 -5.125 1.140625 -5.421875 1.78125 -5.421875 C 2.546875 -5.421875 3.03125 -4.96875 3.03125 -4.15625 C 3.03125 -3.390625 2.703125 -2.703125 1.859375 -1.8125 L 0.265625 -0.109375 L 0.265625 0 L 3.78125 0 Z M 4.265625 -1.234375 "/>
</symbol>
<symbol overflow="visible" id="glyph0-3">
<path style="stroke:none;" d="M 0.75 1.265625 C 1.375 0.96875 1.75 0.40625 1.75 -0.140625 C 1.75 -0.609375 1.4375 -0.921875 1.03125 -0.921875 C 0.71875 -0.921875 0.5 -0.71875 0.5 -0.40625 C 0.5 -0.09375 0.6875 0.046875 1.015625 0.046875 C 1.109375 0.046875 1.203125 0.015625 1.28125 0.015625 C 1.34375 0.015625 1.40625 0.078125 1.40625 0.140625 C 1.40625 0.4375 1.15625 0.765625 0.65625 1.09375 Z M 0.75 1.265625 "/>
</symbol>
<symbol overflow="visible" id="glyph0-4">
<path style="stroke:none;" d=""/>
</symbol>
<symbol overflow="visible" id="glyph0-5">
<path style="stroke:none;" d="M 3.546875 0 L 3.546875 -0.140625 C 2.875 -0.140625 2.6875 -0.296875 2.6875 -0.6875 L 2.6875 -6.0625 L 2.609375 -6.078125 L 1 -5.265625 L 1 -5.140625 L 1.234375 -5.234375 C 1.40625 -5.296875 1.5625 -5.34375 1.640625 -5.34375 C 1.84375 -5.34375 1.921875 -5.203125 1.921875 -4.890625 L 1.921875 -0.859375 C 1.921875 -0.359375 1.734375 -0.171875 1.0625 -0.140625 L 1.0625 0 Z M 3.546875 0 "/>
</symbol>
<symbol overflow="visible" id="glyph0-6">
<path style="stroke:none;" d="M 0.265625 -5.9375 C 1.5625 -4.90625 1.796875 -3.875 1.796875 -2.203125 C 1.796875 -0.453125 1.53125 0.390625 0.265625 1.453125 L 0.34375 1.59375 C 1.765625 0.71875 2.5625 -0.5625 2.5625 -2.21875 C 2.5625 -3.75 1.75 -5.25 0.375 -6.078125 Z M 0.265625 -5.9375 "/>
</symbol>
<symbol overflow="visible" id="glyph0-7">
<path style="stroke:none;" d="M 1.625 -2.28125 C 1.625 -2.546875 1.390625 -2.796875 1.140625 -2.796875 C 0.859375 -2.796875 0.625 -2.5625 0.625 -2.28125 C 0.625 -2 0.84375 -1.796875 1.125 -1.796875 C 1.390625 -1.796875 1.625 -2.015625 1.625 -2.28125 Z M 1.625 -2.28125 "/>
</symbol>
<symbol overflow="visible" id="glyph0-8">
<path style="stroke:none;" d="M 5.734375 -2.875 L 5.734375 -3.46875 L 0.4375 -3.46875 L 0.4375 -2.875 Z M 5.734375 -1.078125 L 5.734375 -1.671875 L 0.4375 -1.671875 L 0.4375 -1.078125 Z M 5.734375 -1.078125 "/>
</symbol>
<symbol overflow="visible" id="glyph0-9">
<path style="stroke:none;" d="M 5.59375 0.21875 L 5.59375 -0.34375 L 1.703125 -2.28125 L 5.59375 -4.21875 L 5.59375 -4.8125 L 0.5 -2.328125 L 0.5 -2.234375 Z M 5.59375 0.21875 "/>
</symbol>
<symbol overflow="visible" id="glyph0-10">
<path style="stroke:none;" d="M 5.59375 -2.234375 L 5.59375 -2.328125 L 0.5 -4.8125 L 0.5 -4.21875 L 4.390625 -2.28125 L 0.5 -0.34375 L 0.5 0.21875 Z M 5.59375 -2.234375 "/>
</symbol>
<symbol overflow="visible" id="glyph1-0">
<path style="stroke:none;" d=""/>
</symbol>
<symbol overflow="visible" id="glyph1-1">
<path style="stroke:none;" d="M 4.359375 -4.15625 L 3 -4.15625 L 3 -3.9375 C 3.390625 -3.921875 3.5 -3.859375 3.5 -3.640625 C 3.5 -3.53125 3.46875 -3.40625 3.40625 -3.234375 L 2.75 -1.59375 L 2.046875 -3.421875 C 1.96875 -3.59375 1.96875 -3.640625 1.96875 -3.703125 C 1.96875 -3.828125 2.046875 -3.890625 2.28125 -3.921875 L 2.4375 -3.9375 L 2.4375 -4.15625 L 0.1875 -4.15625 L 0.1875 -3.9375 C 0.390625 -3.90625 0.4375 -3.875 0.5 -3.8125 C 0.578125 -3.703125 0.90625 -2.984375 1.09375 -2.53125 L 2.171875 0.125 L 2.40625 0.125 L 3.84375 -3.4375 C 4.015625 -3.859375 4.09375 -3.90625 4.359375 -3.9375 Z M 4.359375 -4.15625 "/>
</symbol>
<symbol overflow="visible" id="glyph1-2">
<path style="stroke:none;" d="M 6.359375 -4.15625 L 5.140625 -4.15625 L 5.140625 -3.9375 C 5.484375 -3.890625 5.578125 -3.828125 5.578125 -3.625 C 5.578125 -3.5 5.4375 -3.171875 5.1875 -2.46875 L 4.875 -1.625 C 4.78125 -2 4.75 -2.140625 4.5625 -2.765625 C 4.390625 -3.375 4.3125 -3.625 4.3125 -3.71875 C 4.3125 -3.859375 4.40625 -3.90625 4.75 -3.9375 L 4.75 -4.15625 L 2.640625 -4.15625 L 2.640625 -3.9375 C 3 -3.890625 3 -3.890625 3.171875 -3.296875 C 3.1875 -3.234375 3.21875 -3.171875 3.234375 -3.125 L 2.625 -1.578125 L 2.21875 -2.640625 C 1.96875 -3.28125 1.859375 -3.5625 1.859375 -3.6875 C 1.859375 -3.828125 1.953125 -3.890625 2.203125 -3.9375 L 2.203125 -4.15625 L 0.203125 -4.15625 L 0.203125 -3.9375 C 0.4375 -3.890625 0.5 -3.796875 0.71875 -3.234375 L 2.046875 0.125 L 2.265625 0.125 L 3.390625 -2.65625 L 4.3125 0.125 L 4.515625 0.125 L 5.90625 -3.484375 C 6.03125 -3.78125 6.125 -3.890625 6.359375 -3.9375 Z M 6.359375 -4.15625 "/>
</symbol>
</g>
</defs>
<g id="surface1">
<path style=" stroke:none;fill-rule:nonzero;fill:rgb(69.804382%,85.098267%,100%);fill-opacity:1;" d="M 78.804688 28 L 5 28 L 5 170 L 149.601562 170 Z M 78.804688 28 "/>
<path style="fill:none;stroke-width:1;stroke-linecap:butt;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 5.898438 76 L 194.101562 76 " transform="matrix(1,0,0,1,1,22)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:butt;stroke-linejoin:miter;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 198.101562 76 L 194.101562 76 M 194.101562 74.5 L 198.101562 76 L 194.101562 77.5 " transform="matrix(1,0,0,1,1,22)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:butt;stroke-linejoin:miter;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 1.898438 76 L 5.898438 76 M 5.898438 77.5 L 1.898438 76 L 5.898438 74.5 " transform="matrix(1,0,0,1,1,22)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:butt;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 100 5.898438 L 100 146.101562 " transform="matrix(1,0,0,1,1,22)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:butt;stroke-linejoin:miter;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 100 150.101562 L 100 146.101562 M 101.5 146.101562 L 100 150.101562 L 98.5 146.101562 " transform="matrix(1,0,0,1,1,22)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:butt;stroke-linejoin:miter;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 100 1.898438 L 100 5.898438 M 98.5 5.898438 L 100 1.898438 L 101.5 5.898438 " transform="matrix(1,0,0,1,1,22)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:butt;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-dasharray:1,4;stroke-miterlimit:10;" d="M 124 8 L 124 144 " transform="matrix(1,0,0,1,1,22)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:butt;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-dasharray:1,4;stroke-miterlimit:10;" d="M 148 8 L 148 144 " transform="matrix(1,0,0,1,1,22)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:butt;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-dasharray:1,4;stroke-miterlimit:10;" d="M 172 8 L 172 144 " transform="matrix(1,0,0,1,1,22)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:butt;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-dasharray:1,4;stroke-miterlimit:10;" d="M 76 8 L 76 144 " transform="matrix(1,0,0,1,1,22)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:butt;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-dasharray:1,4;stroke-miterlimit:10;" d="M 28 8 L 28 144 " transform="matrix(1,0,0,1,1,22)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:butt;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-dasharray:1,4;stroke-miterlimit:10;" d="M 52 8 L 52 144 " transform="matrix(1,0,0,1,1,22)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:butt;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-dasharray:1,4;stroke-miterlimit:10;" d="M 8 52 L 192 52 " transform="matrix(1,0,0,1,1,22)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:butt;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-dasharray:1,4;stroke-miterlimit:10;" d="M 8 28 L 192 28 " transform="matrix(1,0,0,1,1,22)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:butt;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-dasharray:1,4;stroke-miterlimit:10;" d="M 8 100 L 192 100 " transform="matrix(1,0,0,1,1,22)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:butt;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-dasharray:1,4;stroke-miterlimit:10;" d="M 8 124 L 192 124 " transform="matrix(1,0,0,1,1,22)"/>
<g style="fill:rgb(0%,0%,0%);fill-opacity:1;">
  <use xlink:href="#glyph0-1" x="150.631" y="70"/>
  <use xlink:href="#glyph0-2" x="153.628" y="70"/>
  <use xlink:href="#glyph0-3" x="158.128" y="70"/>
  <use xlink:href="#glyph0-4" x="160.378" y="70"/>
  <use xlink:href="#glyph0-5" x="162.628" y="70"/>
  <use xlink:href="#glyph0-6" x="167.128" y="70"/>
</g>
<path style="fill:none;stroke-width:1;stroke-linecap:butt;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 142.722656 54.636719 L 100.671875 75.664062 " transform="matrix(1,0,0,1,1,22)"/>
<path style="fill-rule:nonzero;fill:rgb(0%,0%,0%);fill-opacity:1;stroke-width:1;stroke-linecap:butt;stroke-linejoin:miter;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 100.238281 75.289062 C 100.628906 75.417969 100.84375 75.84375 100.710938 76.238281 C 100.582031 76.628906 100.15625 76.84375 99.761719 76.710938 C 99.371094 76.582031 99.15625 76.15625 99.289062 75.761719 C 99.417969 75.371094 99.84375 75.15625 100.238281 75.289062 " transform="matrix(1,0,0,1,1,22)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:butt;stroke-linejoin:miter;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 146.300781 52.851562 L 142.722656 54.636719 M 142.050781 53.296875 L 146.300781 52.851562 L 143.394531 55.980469 " transform="matrix(1,0,0,1,1,22)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 76 4 L 148 148 " transform="matrix(1,0,0,1,1,22)"/>
<g style="fill:rgb(0%,0%,0%);fill-opacity:1;">
  <use xlink:href="#glyph1-1" x="61.7925" y="14"/>
</g>
<g style="fill:rgb(0%,0%,0%);fill-opacity:1;">
  <use xlink:href="#glyph0-4" x="66.2925" y="14"/>
  <use xlink:href="#glyph0-7" x="68.5425" y="14"/>
  <use xlink:href="#glyph0-4" x="70.7925" y="14"/>
</g>
<g style="fill:rgb(0%,0%,0%);fill-opacity:1;">
  <use xlink:href="#glyph1-2" x="73.0425" y="14"/>
</g>
<g style="fill:rgb(0%,0%,0%);fill-opacity:1;">
  <use xlink:href="#glyph0-4" x="79.5405" y="14"/>
  <use xlink:href="#glyph0-8" x="81.7905" y="14"/>
  <use xlink:href="#glyph0-4" x="87.9555" y="14"/>
  <use xlink:href="#glyph0-5" x="90.2055" y="14"/>
</g>
<g style="fill:rgb(0%,0%,0%);fill-opacity:1;">
  <use xlink:href="#glyph1-1" x="8" y="14"/>
</g>
<g style="fill:rgb(0%,0%,0%);fill-opacity:1;">
  <use xlink:href="#glyph0-4" x="12.5" y="14"/>
  <use xlink:href="#glyph0-7" x="14.75" y="14"/>
  <use xlink:href="#glyph0-4" x="17" y="14"/>
</g>
<g style="fill:rgb(0%,0%,0%);fill-opacity:1;">
  <use xlink:href="#glyph1-2" x="19.25" y="14"/>
</g>
<g style="fill:rgb(0%,0%,0%);fill-opacity:1;">
  <use xlink:href="#glyph0-4" x="25.748" y="14"/>
  <use xlink:href="#glyph0-9" x="27.998" y="14"/>
  <use xlink:href="#glyph0-4" x="34.163" y="14"/>
  <use xlink:href="#glyph0-5" x="36.413" y="14"/>
</g>
<g style="fill:rgb(0%,0%,0%);fill-opacity:1;">
  <use xlink:href="#glyph1-1" x="136.087" y="14"/>
</g>
<g style="fill:rgb(0%,0%,0%);fill-opacity:1;">
  <use xlink:href="#glyph0-4" x="140.587" y="14"/>
  <use xlink:href="#glyph0-7" x="142.837" y="14"/>
  <use xlink:href="#glyph0-4" x="145.087" y="14"/>
</g>
<g style="fill:rgb(0%,0%,0%);fill-opacity:1;">
  <use xlink:href="#glyph1-2" x="147.337" y="14"/>
</g>
<g style="fill:rgb(0%,0%,0%);fill-opacity:1;">
  <use xlink:href="#glyph0-4" x="153.835" y="14"/>
  <use xlink:href="#glyph0-10" x="156.085" y="14"/>
  <use xlink:href="#glyph0-4" x="162.25" y="14"/>
  <use xlink:href="#glyph0-5" x="164.5" y="14"/>
</g>
</g>
</svg>
