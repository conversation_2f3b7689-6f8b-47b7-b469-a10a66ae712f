{"cells": [{"cell_type": "markdown", "id": "02d7e531", "metadata": {"origin_pos": 0}, "source": ["# 概率\n", ":label:`sec_prob`\n", "\n", "简单地说，机器学习就是做出预测。\n", "\n", "根据病人的临床病史，我们可能想预测他们在下一年心脏病发作的*概率*。\n", "在飞机喷气发动机的异常检测中，我们想要评估一组发动机读数为正常运行情况的概率有多大。\n", "在强化学习中，我们希望智能体（agent）能在一个环境中智能地行动。\n", "这意味着我们需要考虑在每种可行的行为下获得高奖励的概率。\n", "当我们建立推荐系统时，我们也需要考虑概率。\n", "例如，假设我们为一家大型在线书店工作，我们可能希望估计某些用户购买特定图书的概率。\n", "为此，我们需要使用概率学。\n", "有完整的课程、专业、论文、职业、甚至院系，都致力于概率学的工作。\n", "所以很自然地，我们在这部分的目标不是教授整个科目。\n", "相反，我们希望教给读者基础的概率知识，使读者能够开始构建第一个深度学习模型，\n", "以便读者可以开始自己探索它。\n", "\n", "现在让我们更认真地考虑第一个例子：根据照片区分猫和狗。\n", "这听起来可能很简单，但对于机器却可能是一个艰巨的挑战。\n", "首先，问题的难度可能取决于图像的分辨率。\n", "\n", "![不同分辨率的图像 ($10 \\times 10$, $20 \\times 20$, $40 \\times 40$, $80 \\times 80$, 和 $160 \\times 160$ pixels)](../img/cat-dog-pixels.png)\n", ":width:`300px`\n", ":label:`fig_cat_dog`\n", "\n", "如 :numref:`fig_cat_dog`所示，虽然人类很容易以$160 \\times 160$像素的分辨率识别猫和狗，\n", "但它在$40\\times40$像素上变得具有挑战性，而且在$10 \\times 10$像素下几乎是不可能的。\n", "换句话说，我们在很远的距离（从而降低分辨率）区分猫和狗的能力可能会变为猜测。\n", "概率给了我们一种正式的途径来说明我们的确定性水平。\n", "如果我们完全肯定图像是一只猫，我们说标签$y$是\"猫\"的*概率*，表示为$P(y=$\"猫\"$)$等于$1$。\n", "如果我们没有证据表明$y=$“猫”或$y=$“狗”，那么我们可以说这两种可能性是相等的，\n", "即$P(y=$\"猫\"$)=P(y=$\"狗\"$)=0.5$。\n", "如果我们不十分确定图像描绘的是一只猫，我们可以将概率赋值为$0.5<P(y=$\"猫\"$)<1$。\n", "\n", "现在考虑第二个例子：给出一些天气监测数据，我们想预测明天北京下雨的概率。\n", "如果是夏天，下雨的概率是0.5。\n", "\n", "在这两种情况下，我们都不确定结果，但这两种情况之间有一个关键区别。\n", "在第一种情况中，图像实际上是狗或猫二选一。\n", "在第二种情况下，结果实际上是一个随机的事件。\n", "因此，概率是一种灵活的语言，用于说明我们的确定程度，并且它可以有效地应用于广泛的领域中。\n", "\n", "## 基本概率论\n", "\n", "假设我们掷骰子，想知道看到1的几率有多大，而不是看到另一个数字。\n", "如果骰子是公平的，那么所有六个结果$\\{1, \\ldots, 6\\}$都有相同的可能发生，\n", "因此我们可以说$1$发生的概率为$\\frac{1}{6}$。\n", "\n", "然而现实生活中，对于我们从工厂收到的真实骰子，我们需要检查它是否有瑕疵。\n", "检查骰子的唯一方法是多次投掷并记录结果。\n", "对于每个骰子，我们将观察到$\\{1, \\ldots, 6\\}$中的一个值。\n", "对于每个值，一种自然的方法是将它出现的次数除以投掷的总次数，\n", "即此*事件*（event）概率的*估计值*。\n", "*大数定律*（law of large numbers）告诉我们：\n", "随着投掷次数的增加，这个估计值会越来越接近真实的潜在概率。\n", "让我们用代码试一试！\n", "\n", "首先，我们导入必要的软件包。\n"]}, {"cell_type": "code", "execution_count": 1, "id": "c265b497", "metadata": {"execution": {"iopub.execute_input": "2022-12-07T16:49:40.290547Z", "iopub.status.busy": "2022-12-07T16:49:40.289989Z", "iopub.status.idle": "2022-12-07T16:49:42.806841Z", "shell.execute_reply": "2022-12-07T16:49:42.805933Z"}, "origin_pos": 2, "tab": ["pytorch"]}, "outputs": [], "source": ["%matplotlib inline\n", "import torch\n", "from torch.distributions import multinomial\n", "from d2l import torch as d2l"]}, {"cell_type": "markdown", "id": "9b1b8ee3", "metadata": {"origin_pos": 5}, "source": ["在统计学中，我们把从概率分布中抽取样本的过程称为*抽样*（sampling）。\n", "笼统来说，可以把*分布*（distribution）看作对事件的概率分配，\n", "稍后我们将给出的更正式定义。\n", "将概率分配给一些离散选择的分布称为*多项分布*（multinomial distribution）。\n", "\n", "为了抽取一个样本，即掷骰子，我们只需传入一个概率向量。\n", "输出是另一个相同长度的向量：它在索引$i$处的值是采样结果中$i$出现的次数。\n"]}, {"cell_type": "code", "execution_count": 8, "id": "1b726615", "metadata": {"execution": {"iopub.execute_input": "2022-12-07T16:49:42.813099Z", "iopub.status.busy": "2022-12-07T16:49:42.812207Z", "iopub.status.idle": "2022-12-07T16:49:42.844262Z", "shell.execute_reply": "2022-12-07T16:49:42.843447Z"}, "origin_pos": 7, "tab": ["pytorch"]}, "outputs": [{"data": {"text/plain": ["tensor([1., 0., 0., 0., 0., 0.])"]}, "execution_count": 8, "metadata": {}, "output_type": "execute_result"}], "source": ["fair_probs = torch.ones([6]) / 6\n", "multinomial.Multinomial(1, fair_probs).sample()"]}, {"cell_type": "code", "execution_count": 9, "id": "c0dd89af", "metadata": {}, "outputs": [{"data": {"text/plain": ["tensor([1., 1., 1., 1., 1.])"]}, "execution_count": 9, "metadata": {}, "output_type": "execute_result"}], "source": ["torch.ones(5)"]}, {"cell_type": "markdown", "id": "e698dc25", "metadata": {"origin_pos": 10}, "source": ["在估计一个骰子的公平性时，我们希望从同一分布中生成多个样本。\n", "如果用Python的for循环来完成这个任务，速度会慢得惊人。\n", "因此我们使用深度学习框架的函数同时抽取多个样本，得到我们想要的任意形状的独立样本数组。\n"]}, {"cell_type": "code", "execution_count": 10, "id": "4e292aa1", "metadata": {"execution": {"iopub.execute_input": "2022-12-07T16:49:42.848069Z", "iopub.status.busy": "2022-12-07T16:49:42.847496Z", "iopub.status.idle": "2022-12-07T16:49:42.854831Z", "shell.execute_reply": "2022-12-07T16:49:42.853945Z"}, "origin_pos": 12, "tab": ["pytorch"]}, "outputs": [{"data": {"text/plain": ["tensor([3., 3., 1., 0., 2., 1.])"]}, "execution_count": 10, "metadata": {}, "output_type": "execute_result"}], "source": ["multinomial.Multinomial(10, fair_probs).sample()"]}, {"cell_type": "markdown", "id": "8040204f", "metadata": {"origin_pos": 15}, "source": ["现在我们知道如何对骰子进行采样，我们可以模拟1000次投掷。\n", "然后，我们可以统计1000次投掷后，每个数字被投中了多少次。\n", "具体来说，我们计算相对频率，以作为真实概率的估计。\n"]}, {"cell_type": "code", "execution_count": 15, "id": "e0064629", "metadata": {"execution": {"iopub.execute_input": "2022-12-07T16:49:42.858694Z", "iopub.status.busy": "2022-12-07T16:49:42.858141Z", "iopub.status.idle": "2022-12-07T16:49:42.865819Z", "shell.execute_reply": "2022-12-07T16:49:42.864984Z"}, "origin_pos": 17, "tab": ["pytorch"]}, "outputs": [{"data": {"text/plain": ["tensor([0.1710, 0.1590, 0.1530, 0.1520, 0.1920, 0.1730])"]}, "execution_count": 15, "metadata": {}, "output_type": "execute_result"}], "source": ["# 将结果存储为32位浮点数以进行除法\n", "counts = multinomial.Multinomial(1000, fair_probs).sample()\n", "counts / 1000  # 相对频率作为估计值"]}, {"cell_type": "markdown", "id": "beef9d67", "metadata": {"origin_pos": 20}, "source": ["因为我们是从一个公平的骰子中生成的数据，我们知道每个结果都有真实的概率$\\frac{1}{6}$，\n", "大约是$0.167$，所以上面输出的估计值看起来不错。\n", "\n", "我们也可以看到这些概率如何随着时间的推移收敛到真实概率。\n", "让我们进行500组实验，每组抽取10个样本。\n"]}, {"cell_type": "code", "execution_count": 18, "id": "dc8fa96c", "metadata": {"execution": {"iopub.execute_input": "2022-12-07T16:49:42.869470Z", "iopub.status.busy": "2022-12-07T16:49:42.868996Z", "iopub.status.idle": "2022-12-07T16:49:43.119932Z", "shell.execute_reply": "2022-12-07T16:49:43.119066Z"}, "origin_pos": 22, "tab": ["pytorch"]}, "outputs": [{"data": {"text/plain": ["tensor([[0., 2., 3., 1., 2., 2.],\n", "        [1., 0., 4., 0., 1., 4.],\n", "        [4., 2., 0., 1., 1., 2.],\n", "        ...,\n", "        [2., 3., 2., 1., 1., 1.],\n", "        [1., 1., 2., 1., 1., 4.],\n", "        [2., 0., 1., 2., 4., 1.]])"]}, "execution_count": 18, "metadata": {}, "output_type": "execute_result"}, {"data": {"image/svg+xml": ["<?xml version=\"1.0\" encoding=\"utf-8\" standalone=\"no\"?>\n", "<!DOCTYPE svg PUBLIC \"-//W3C//DTD SVG 1.1//EN\"\n", "  \"http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd\">\n", "<svg xmlns:xlink=\"http://www.w3.org/1999/xlink\" width=\"392.14375pt\" height=\"294.23625pt\" viewBox=\"0 0 392.14375 294.23625\" xmlns=\"http://www.w3.org/2000/svg\" version=\"1.1\">\n", " <metadata>\n", "  <rdf:RDF xmlns:dc=\"http://purl.org/dc/elements/1.1/\" xmlns:cc=\"http://creativecommons.org/ns#\" xmlns:rdf=\"http://www.w3.org/1999/02/22-rdf-syntax-ns#\">\n", "   <cc:Work>\n", "    <dc:type rdf:resource=\"http://purl.org/dc/dcmitype/StillImage\"/>\n", "    <dc:date>2025-07-18T15:28:30.607361</dc:date>\n", "    <dc:format>image/svg+xml</dc:format>\n", "    <dc:creator>\n", "     <cc:Agent>\n", "      <dc:title>Matplotlib v3.5.1, https://matplotlib.org/</dc:title>\n", "     </cc:Agent>\n", "    </dc:creator>\n", "   </cc:Work>\n", "  </rdf:RDF>\n", " </metadata>\n", " <defs>\n", "  <style type=\"text/css\">*{stroke-linejoin: round; stroke-linecap: butt}</style>\n", " </defs>\n", " <g id=\"figure_1\">\n", "  <g id=\"patch_1\">\n", "   <path d=\"M 0 294.23625 \n", "L 392.14375 294.23625 \n", "L 392.14375 0 \n", "L 0 0 \n", "z\n", "\" style=\"fill: #ffffff\"/>\n", "  </g>\n", "  <g id=\"axes_1\">\n", "   <g id=\"patch_2\">\n", "    <path d=\"M 50.14375 256.68 \n", "L 384.94375 256.68 \n", "L 384.94375 7.2 \n", "L 50.14375 7.2 \n", "z\n", "\" style=\"fill: #ffffff\"/>\n", "   </g>\n", "   <g id=\"matplotlib.axis_1\">\n", "    <g id=\"xtick_1\">\n", "     <g id=\"line2d_1\">\n", "      <defs>\n", "       <path id=\"m9d11e09c77\" d=\"M 0 0 \n", "L 0 3.5 \n", "\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </defs>\n", "      <g>\n", "       <use xlink:href=\"#m9d11e09c77\" x=\"65.361932\" y=\"256.68\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_1\">\n", "      <!-- 0 -->\n", "      <g transform=\"translate(62.180682 271.278437)scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-30\" d=\"M 2034 4250 \n", "Q 1547 4250 1301 3770 \n", "Q 1056 3291 1056 2328 \n", "Q 1056 1369 1301 889 \n", "Q 1547 409 2034 409 \n", "Q 2525 409 2770 889 \n", "Q 3016 1369 3016 2328 \n", "Q 3016 3291 2770 3770 \n", "Q 2525 4250 2034 4250 \n", "z\n", "M 2034 4750 \n", "Q 2819 4750 3233 4129 \n", "Q 3647 3509 3647 2328 \n", "Q 3647 1150 3233 529 \n", "Q 2819 -91 2034 -91 \n", "Q 1250 -91 836 529 \n", "Q 422 1150 422 2328 \n", "Q 422 3509 836 4129 \n", "Q 1250 4750 2034 4750 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_2\">\n", "     <g id=\"line2d_2\">\n", "      <g>\n", "       <use xlink:href=\"#m9d11e09c77\" x=\"126.356649\" y=\"256.68\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_2\">\n", "      <!-- 100 -->\n", "      <g transform=\"translate(116.812899 271.278437)scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-31\" d=\"M 794 531 \n", "L 1825 531 \n", "L 1825 4091 \n", "L 703 3866 \n", "L 703 4441 \n", "L 1819 4666 \n", "L 2450 4666 \n", "L 2450 531 \n", "L 3481 531 \n", "L 3481 0 \n", "L 794 0 \n", "L 794 531 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-31\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"127.246094\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_3\">\n", "     <g id=\"line2d_3\">\n", "      <g>\n", "       <use xlink:href=\"#m9d11e09c77\" x=\"187.351365\" y=\"256.68\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_3\">\n", "      <!-- 200 -->\n", "      <g transform=\"translate(177.807615 271.278437)scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-32\" d=\"M 1228 531 \n", "L 3431 531 \n", "L 3431 0 \n", "L 469 0 \n", "L 469 531 \n", "Q 828 903 1448 1529 \n", "Q 2069 2156 2228 2338 \n", "Q 2531 2678 2651 2914 \n", "Q 2772 3150 2772 3378 \n", "Q 2772 3750 2511 3984 \n", "Q 2250 4219 1831 4219 \n", "Q 1534 4219 1204 4116 \n", "Q 875 4013 500 3803 \n", "L 500 4441 \n", "Q 881 4594 1212 4672 \n", "Q 1544 4750 1819 4750 \n", "Q 2544 4750 2975 4387 \n", "Q 3406 4025 3406 3419 \n", "Q 3406 3131 3298 2873 \n", "Q 3191 2616 2906 2266 \n", "Q 2828 2175 2409 1742 \n", "Q 1991 1309 1228 531 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-32\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"127.246094\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_4\">\n", "     <g id=\"line2d_4\">\n", "      <g>\n", "       <use xlink:href=\"#m9d11e09c77\" x=\"248.346082\" y=\"256.68\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_4\">\n", "      <!-- 300 -->\n", "      <g transform=\"translate(238.802332 271.278437)scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-33\" d=\"M 2597 2516 \n", "Q 3050 2419 3304 2112 \n", "Q 3559 1806 3559 1356 \n", "Q 3559 666 3084 287 \n", "Q 2609 -91 1734 -91 \n", "Q 1441 -91 1130 -33 \n", "Q 819 25 488 141 \n", "L 488 750 \n", "Q 750 597 1062 519 \n", "Q 1375 441 1716 441 \n", "Q 2309 441 2620 675 \n", "Q 2931 909 2931 1356 \n", "Q 2931 1769 2642 2001 \n", "Q 2353 2234 1838 2234 \n", "L 1294 2234 \n", "L 1294 2753 \n", "L 1863 2753 \n", "Q 2328 2753 2575 2939 \n", "Q 2822 3125 2822 3475 \n", "Q 2822 3834 2567 4026 \n", "Q 2313 4219 1838 4219 \n", "Q 1578 4219 1281 4162 \n", "Q 984 4106 628 3988 \n", "L 628 4550 \n", "Q 988 4650 1302 4700 \n", "Q 1616 4750 1894 4750 \n", "Q 2613 4750 3031 4423 \n", "Q 3450 4097 3450 3541 \n", "Q 3450 3153 3228 2886 \n", "Q 3006 2619 2597 2516 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-33\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"127.246094\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_5\">\n", "     <g id=\"line2d_5\">\n", "      <g>\n", "       <use xlink:href=\"#m9d11e09c77\" x=\"309.340799\" y=\"256.68\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_5\">\n", "      <!-- 400 -->\n", "      <g transform=\"translate(299.797049 271.278437)scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-34\" d=\"M 2419 4116 \n", "L 825 1625 \n", "L 2419 1625 \n", "L 2419 4116 \n", "z\n", "M 2253 4666 \n", "L 3047 4666 \n", "L 3047 1625 \n", "L 3713 1625 \n", "L 3713 1100 \n", "L 3047 1100 \n", "L 3047 0 \n", "L 2419 0 \n", "L 2419 1100 \n", "L 313 1100 \n", "L 313 1709 \n", "L 2253 4666 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-34\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"127.246094\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_6\">\n", "     <g id=\"line2d_6\">\n", "      <g>\n", "       <use xlink:href=\"#m9d11e09c77\" x=\"370.335515\" y=\"256.68\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_6\">\n", "      <!-- 500 -->\n", "      <g transform=\"translate(360.791765 271.278437)scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-35\" d=\"M 691 4666 \n", "L 3169 4666 \n", "L 3169 4134 \n", "L 1269 4134 \n", "L 1269 2991 \n", "Q 1406 3038 1543 3061 \n", "Q 1681 3084 1819 3084 \n", "Q 2600 3084 3056 2656 \n", "Q 3513 2228 3513 1497 \n", "Q 3513 744 3044 326 \n", "Q 2575 -91 1722 -91 \n", "Q 1428 -91 1123 -41 \n", "Q 819 9 494 109 \n", "L 494 744 \n", "Q 775 591 1075 516 \n", "Q 1375 441 1709 441 \n", "Q 2250 441 2565 725 \n", "Q 2881 1009 2881 1497 \n", "Q 2881 1984 2565 2268 \n", "Q 2250 2553 1709 2553 \n", "Q 1456 2553 1204 2497 \n", "Q 953 2441 691 2322 \n", "L 691 4666 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-35\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"127.246094\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"text_7\">\n", "     <!-- Groups of experiments -->\n", "     <g transform=\"translate(160.397656 284.956562)scale(0.1 -0.1)\">\n", "      <defs>\n", "       <path id=\"DejaVuSans-47\" d=\"M 3809 666 \n", "L 3809 1919 \n", "L 2778 1919 \n", "L 2778 2438 \n", "L 4434 2438 \n", "L 4434 434 \n", "Q 4069 175 3628 42 \n", "Q 3188 -91 2688 -91 \n", "Q 1594 -91 976 548 \n", "Q 359 1188 359 2328 \n", "Q 359 3472 976 4111 \n", "Q 1594 4750 2688 4750 \n", "Q 3144 4750 3555 4637 \n", "Q 3966 4525 4313 4306 \n", "L 4313 3634 \n", "Q 3963 3931 3569 4081 \n", "Q 3175 4231 2741 4231 \n", "Q 1884 4231 1454 3753 \n", "Q 1025 3275 1025 2328 \n", "Q 1025 1384 1454 906 \n", "Q 1884 428 2741 428 \n", "Q 3075 428 3337 486 \n", "Q 3600 544 3809 666 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-72\" d=\"M 2631 2963 \n", "Q 2534 3019 2420 3045 \n", "Q 2306 3072 2169 3072 \n", "Q 1681 3072 1420 2755 \n", "Q 1159 2438 1159 1844 \n", "L 1159 0 \n", "L 581 0 \n", "L 581 3500 \n", "L 1159 3500 \n", "L 1159 2956 \n", "Q 1341 3275 1631 3429 \n", "Q 1922 3584 2338 3584 \n", "Q 2397 3584 2469 3576 \n", "Q 2541 3569 2628 3553 \n", "L 2631 2963 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-6f\" d=\"M 1959 3097 \n", "Q 1497 3097 1228 2736 \n", "Q 959 2375 959 1747 \n", "Q 959 1119 1226 758 \n", "Q 1494 397 1959 397 \n", "Q 2419 397 2687 759 \n", "Q 2956 1122 2956 1747 \n", "Q 2956 2369 2687 2733 \n", "Q 2419 3097 1959 3097 \n", "z\n", "M 1959 3584 \n", "Q 2709 3584 3137 3096 \n", "Q 3566 2609 3566 1747 \n", "Q 3566 888 3137 398 \n", "Q 2709 -91 1959 -91 \n", "Q 1206 -91 779 398 \n", "Q 353 888 353 1747 \n", "Q 353 2609 779 3096 \n", "Q 1206 3584 1959 3584 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-75\" d=\"M 544 1381 \n", "L 544 3500 \n", "L 1119 3500 \n", "L 1119 1403 \n", "Q 1119 906 1312 657 \n", "Q 1506 409 1894 409 \n", "Q 2359 409 2629 706 \n", "Q 2900 1003 2900 1516 \n", "L 2900 3500 \n", "L 3475 3500 \n", "L 3475 0 \n", "L 2900 0 \n", "L 2900 538 \n", "Q 2691 219 2414 64 \n", "Q 2138 -91 1772 -91 \n", "Q 1169 -91 856 284 \n", "Q 544 659 544 1381 \n", "z\n", "M 1991 3584 \n", "L 1991 3584 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-70\" d=\"M 1159 525 \n", "L 1159 -1331 \n", "L 581 -1331 \n", "L 581 3500 \n", "L 1159 3500 \n", "L 1159 2969 \n", "Q 1341 3281 1617 3432 \n", "Q 1894 3584 2278 3584 \n", "Q 2916 3584 3314 3078 \n", "Q 3713 2572 3713 1747 \n", "Q 3713 922 3314 415 \n", "Q 2916 -91 2278 -91 \n", "Q 1894 -91 1617 61 \n", "Q 1341 213 1159 525 \n", "z\n", "M 3116 1747 \n", "Q 3116 2381 2855 2742 \n", "Q 2594 3103 2138 3103 \n", "Q 1681 3103 1420 2742 \n", "Q 1159 2381 1159 1747 \n", "Q 1159 1113 1420 752 \n", "Q 1681 391 2138 391 \n", "Q 2594 391 2855 752 \n", "Q 3116 1113 3116 1747 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-73\" d=\"M 2834 3397 \n", "L 2834 2853 \n", "Q 2591 2978 2328 3040 \n", "Q 2066 3103 1784 3103 \n", "Q 1356 3103 1142 2972 \n", "Q 928 2841 928 2578 \n", "Q 928 2378 1081 2264 \n", "Q 1234 2150 1697 2047 \n", "L 1894 2003 \n", "Q 2506 1872 2764 1633 \n", "Q 3022 1394 3022 966 \n", "Q 3022 478 2636 193 \n", "Q 2250 -91 1575 -91 \n", "Q 1294 -91 989 -36 \n", "Q 684 19 347 128 \n", "L 347 722 \n", "Q 666 556 975 473 \n", "Q 1284 391 1588 391 \n", "Q 1994 391 2212 530 \n", "Q 2431 669 2431 922 \n", "Q 2431 1156 2273 1281 \n", "Q 2116 1406 1581 1522 \n", "L 1381 1569 \n", "Q 847 1681 609 1914 \n", "Q 372 2147 372 2553 \n", "Q 372 3047 722 3315 \n", "Q 1072 3584 1716 3584 \n", "Q 2034 3584 2315 3537 \n", "Q 2597 3491 2834 3397 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-20\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-66\" d=\"M 2375 4863 \n", "L 2375 4384 \n", "L 1825 4384 \n", "Q 1516 4384 1395 4259 \n", "Q 1275 4134 1275 3809 \n", "L 1275 3500 \n", "L 2222 3500 \n", "L 2222 3053 \n", "L 1275 3053 \n", "L 1275 0 \n", "L 697 0 \n", "L 697 3053 \n", "L 147 3053 \n", "L 147 3500 \n", "L 697 3500 \n", "L 697 3744 \n", "Q 697 4328 969 4595 \n", "Q 1241 4863 1831 4863 \n", "L 2375 4863 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-65\" d=\"M 3597 1894 \n", "L 3597 1613 \n", "L 953 1613 \n", "Q 991 1019 1311 708 \n", "Q 1631 397 2203 397 \n", "Q 2534 397 2845 478 \n", "Q 3156 559 3463 722 \n", "L 3463 178 \n", "Q 3153 47 2828 -22 \n", "Q 2503 -91 2169 -91 \n", "Q 1331 -91 842 396 \n", "Q 353 884 353 1716 \n", "Q 353 2575 817 3079 \n", "Q 1281 3584 2069 3584 \n", "Q 2775 3584 3186 3129 \n", "Q 3597 2675 3597 1894 \n", "z\n", "M 3022 2063 \n", "Q 3016 2534 2758 2815 \n", "Q 2500 3097 2075 3097 \n", "Q 1594 3097 1305 2825 \n", "Q 1016 2553 972 2059 \n", "L 3022 2063 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-78\" d=\"M 3513 3500 \n", "L 2247 1797 \n", "L 3578 0 \n", "L 2900 0 \n", "L 1881 1375 \n", "L 863 0 \n", "L 184 0 \n", "L 1544 1831 \n", "L 300 3500 \n", "L 978 3500 \n", "L 1906 2253 \n", "L 2834 3500 \n", "L 3513 3500 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-69\" d=\"M 603 3500 \n", "L 1178 3500 \n", "L 1178 0 \n", "L 603 0 \n", "L 603 3500 \n", "z\n", "M 603 4863 \n", "L 1178 4863 \n", "L 1178 4134 \n", "L 603 4134 \n", "L 603 4863 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-6d\" d=\"M 3328 2828 \n", "Q 3544 3216 3844 3400 \n", "Q 4144 3584 4550 3584 \n", "Q 5097 3584 5394 3201 \n", "Q 5691 2819 5691 2113 \n", "L 5691 0 \n", "L 5113 0 \n", "L 5113 2094 \n", "Q 5113 2597 4934 2840 \n", "Q 4756 3084 4391 3084 \n", "Q 3944 3084 3684 2787 \n", "Q 3425 2491 3425 1978 \n", "L 3425 0 \n", "L 2847 0 \n", "L 2847 2094 \n", "Q 2847 2600 2669 2842 \n", "Q 2491 3084 2119 3084 \n", "Q 1678 3084 1418 2786 \n", "Q 1159 2488 1159 1978 \n", "L 1159 0 \n", "L 581 0 \n", "L 581 3500 \n", "L 1159 3500 \n", "L 1159 2956 \n", "Q 1356 3278 1631 3431 \n", "Q 1906 3584 2284 3584 \n", "Q 2666 3584 2933 3390 \n", "Q 3200 3197 3328 2828 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-6e\" d=\"M 3513 2113 \n", "L 3513 0 \n", "L 2938 0 \n", "L 2938 2094 \n", "Q 2938 2591 2744 2837 \n", "Q 2550 3084 2163 3084 \n", "Q 1697 3084 1428 2787 \n", "Q 1159 2491 1159 1978 \n", "L 1159 0 \n", "L 581 0 \n", "L 581 3500 \n", "L 1159 3500 \n", "L 1159 2956 \n", "Q 1366 3272 1645 3428 \n", "Q 1925 3584 2291 3584 \n", "Q 2894 3584 3203 3211 \n", "Q 3513 2838 3513 2113 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-74\" d=\"M 1172 4494 \n", "L 1172 3500 \n", "L 2356 3500 \n", "L 2356 3053 \n", "L 1172 3053 \n", "L 1172 1153 \n", "Q 1172 725 1289 603 \n", "Q 1406 481 1766 481 \n", "L 2356 481 \n", "L 2356 0 \n", "L 1766 0 \n", "Q 1100 0 847 248 \n", "Q 594 497 594 1153 \n", "L 594 3053 \n", "L 172 3053 \n", "L 172 3500 \n", "L 594 3500 \n", "L 594 4494 \n", "L 1172 4494 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "      </defs>\n", "      <use xlink:href=\"#DejaVuSans-47\"/>\n", "      <use xlink:href=\"#DejaVuSans-72\" x=\"77.490234\"/>\n", "      <use xlink:href=\"#DejaVuSans-6f\" x=\"116.353516\"/>\n", "      <use xlink:href=\"#DejaVuSans-75\" x=\"177.535156\"/>\n", "      <use xlink:href=\"#DejaVuSans-70\" x=\"240.914062\"/>\n", "      <use xlink:href=\"#DejaVuSans-73\" x=\"304.390625\"/>\n", "      <use xlink:href=\"#DejaVuSans-20\" x=\"356.490234\"/>\n", "      <use xlink:href=\"#DejaVuSans-6f\" x=\"388.277344\"/>\n", "      <use xlink:href=\"#DejaVuSans-66\" x=\"449.458984\"/>\n", "      <use xlink:href=\"#DejaVuSans-20\" x=\"484.664062\"/>\n", "      <use xlink:href=\"#DejaVuSans-65\" x=\"516.451172\"/>\n", "      <use xlink:href=\"#DejaVuSans-78\" x=\"576.224609\"/>\n", "      <use xlink:href=\"#DejaVuSans-70\" x=\"635.404297\"/>\n", "      <use xlink:href=\"#DejaVuSans-65\" x=\"698.880859\"/>\n", "      <use xlink:href=\"#DejaVuSans-72\" x=\"760.404297\"/>\n", "      <use xlink:href=\"#DejaVuSans-69\" x=\"801.517578\"/>\n", "      <use xlink:href=\"#DejaVuSans-6d\" x=\"829.300781\"/>\n", "      <use xlink:href=\"#DejaVuSans-65\" x=\"926.712891\"/>\n", "      <use xlink:href=\"#DejaVuSans-6e\" x=\"988.236328\"/>\n", "      <use xlink:href=\"#DejaVuSans-74\" x=\"1051.615234\"/>\n", "      <use xlink:href=\"#DejaVuSans-73\" x=\"1090.824219\"/>\n", "     </g>\n", "    </g>\n", "   </g>\n", "   <g id=\"matplotlib.axis_2\">\n", "    <g id=\"ytick_1\">\n", "     <g id=\"line2d_7\">\n", "      <defs>\n", "       <path id=\"m22a5c1ca81\" d=\"M 0 0 \n", "L -3.5 0 \n", "\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </defs>\n", "      <g>\n", "       <use xlink:href=\"#m22a5c1ca81\" x=\"50.14375\" y=\"245.34\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_8\">\n", "      <!-- 0.00 -->\n", "      <g transform=\"translate(20.878125 249.139219)scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-2e\" d=\"M 684 794 \n", "L 1344 794 \n", "L 1344 0 \n", "L 684 0 \n", "L 684 794 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"95.410156\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"159.033203\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_2\">\n", "     <g id=\"line2d_8\">\n", "      <g>\n", "       <use xlink:href=\"#m22a5c1ca81\" x=\"50.14375\" y=\"212.939999\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_9\">\n", "      <!-- 0.05 -->\n", "      <g transform=\"translate(20.878125 216.739218)scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"95.410156\"/>\n", "       <use xlink:href=\"#DejaVuSans-35\" x=\"159.033203\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_3\">\n", "     <g id=\"line2d_9\">\n", "      <g>\n", "       <use xlink:href=\"#m22a5c1ca81\" x=\"50.14375\" y=\"180.539999\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_10\">\n", "      <!-- 0.10 -->\n", "      <g transform=\"translate(20.878125 184.339218)scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-31\" x=\"95.410156\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"159.033203\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_4\">\n", "     <g id=\"line2d_10\">\n", "      <g>\n", "       <use xlink:href=\"#m22a5c1ca81\" x=\"50.14375\" y=\"148.139998\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_11\">\n", "      <!-- 0.15 -->\n", "      <g transform=\"translate(20.878125 151.939217)scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-31\" x=\"95.410156\"/>\n", "       <use xlink:href=\"#DejaVuSans-35\" x=\"159.033203\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_5\">\n", "     <g id=\"line2d_11\">\n", "      <g>\n", "       <use xlink:href=\"#m22a5c1ca81\" x=\"50.14375\" y=\"115.739998\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_12\">\n", "      <!-- 0.20 -->\n", "      <g transform=\"translate(20.878125 119.539217)scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-32\" x=\"95.410156\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"159.033203\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_6\">\n", "     <g id=\"line2d_12\">\n", "      <g>\n", "       <use xlink:href=\"#m22a5c1ca81\" x=\"50.14375\" y=\"83.339997\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_13\">\n", "      <!-- 0.25 -->\n", "      <g transform=\"translate(20.878125 87.139216)scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-32\" x=\"95.410156\"/>\n", "       <use xlink:href=\"#DejaVuSans-35\" x=\"159.033203\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_7\">\n", "     <g id=\"line2d_13\">\n", "      <g>\n", "       <use xlink:href=\"#m22a5c1ca81\" x=\"50.14375\" y=\"50.939997\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_14\">\n", "      <!-- 0.30 -->\n", "      <g transform=\"translate(20.878125 54.739215)scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-33\" x=\"95.410156\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"159.033203\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_8\">\n", "     <g id=\"line2d_14\">\n", "      <g>\n", "       <use xlink:href=\"#m22a5c1ca81\" x=\"50.14375\" y=\"18.539996\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_15\">\n", "      <!-- 0.35 -->\n", "      <g transform=\"translate(20.878125 22.339215)scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-33\" x=\"95.410156\"/>\n", "       <use xlink:href=\"#DejaVuSans-35\" x=\"159.033203\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"text_16\">\n", "     <!-- Estimated probability -->\n", "     <g transform=\"translate(14.798438 185.463437)rotate(-90)scale(0.1 -0.1)\">\n", "      <defs>\n", "       <path id=\"DejaVuSans-45\" d=\"M 628 4666 \n", "L 3578 4666 \n", "L 3578 4134 \n", "L 1259 4134 \n", "L 1259 2753 \n", "L 3481 2753 \n", "L 3481 2222 \n", "L 1259 2222 \n", "L 1259 531 \n", "L 3634 531 \n", "L 3634 0 \n", "L 628 0 \n", "L 628 4666 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-61\" d=\"M 2194 1759 \n", "Q 1497 1759 1228 1600 \n", "Q 959 1441 959 1056 \n", "Q 959 750 1161 570 \n", "Q 1363 391 1709 391 \n", "Q 2188 391 2477 730 \n", "Q 2766 1069 2766 1631 \n", "L 2766 1759 \n", "L 2194 1759 \n", "z\n", "M 3341 1997 \n", "L 3341 0 \n", "L 2766 0 \n", "L 2766 531 \n", "Q 2569 213 2275 61 \n", "Q 1981 -91 1556 -91 \n", "Q 1019 -91 701 211 \n", "Q 384 513 384 1019 \n", "Q 384 1609 779 1909 \n", "Q 1175 2209 1959 2209 \n", "L 2766 2209 \n", "L 2766 2266 \n", "Q 2766 2663 2505 2880 \n", "Q 2244 3097 1772 3097 \n", "Q 1472 3097 1187 3025 \n", "Q 903 2953 641 2809 \n", "L 641 3341 \n", "Q 956 3463 1253 3523 \n", "Q 1550 3584 1831 3584 \n", "Q 2591 3584 2966 3190 \n", "Q 3341 2797 3341 1997 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-64\" d=\"M 2906 2969 \n", "L 2906 4863 \n", "L 3481 4863 \n", "L 3481 0 \n", "L 2906 0 \n", "L 2906 525 \n", "Q 2725 213 2448 61 \n", "Q 2172 -91 1784 -91 \n", "Q 1150 -91 751 415 \n", "Q 353 922 353 1747 \n", "Q 353 2572 751 3078 \n", "Q 1150 3584 1784 3584 \n", "Q 2172 3584 2448 3432 \n", "Q 2725 3281 2906 2969 \n", "z\n", "M 947 1747 \n", "Q 947 1113 1208 752 \n", "Q 1469 391 1925 391 \n", "Q 2381 391 2643 752 \n", "Q 2906 1113 2906 1747 \n", "Q 2906 2381 2643 2742 \n", "Q 2381 3103 1925 3103 \n", "Q 1469 3103 1208 2742 \n", "Q 947 2381 947 1747 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-62\" d=\"M 3116 1747 \n", "Q 3116 2381 2855 2742 \n", "Q 2594 3103 2138 3103 \n", "Q 1681 3103 1420 2742 \n", "Q 1159 2381 1159 1747 \n", "Q 1159 1113 1420 752 \n", "Q 1681 391 2138 391 \n", "Q 2594 391 2855 752 \n", "Q 3116 1113 3116 1747 \n", "z\n", "M 1159 2969 \n", "Q 1341 3281 1617 3432 \n", "Q 1894 3584 2278 3584 \n", "Q 2916 3584 3314 3078 \n", "Q 3713 2572 3713 1747 \n", "Q 3713 922 3314 415 \n", "Q 2916 -91 2278 -91 \n", "Q 1894 -91 1617 61 \n", "Q 1341 213 1159 525 \n", "L 1159 0 \n", "L 581 0 \n", "L 581 4863 \n", "L 1159 4863 \n", "L 1159 2969 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-6c\" d=\"M 603 4863 \n", "L 1178 4863 \n", "L 1178 0 \n", "L 603 0 \n", "L 603 4863 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-79\" d=\"M 2059 -325 \n", "Q 1816 -950 1584 -1140 \n", "Q 1353 -1331 966 -1331 \n", "L 506 -1331 \n", "L 506 -850 \n", "L 844 -850 \n", "Q 1081 -850 1212 -737 \n", "Q 1344 -625 1503 -206 \n", "L 1606 56 \n", "L 191 3500 \n", "L 800 3500 \n", "L 1894 763 \n", "L 2988 3500 \n", "L 3597 3500 \n", "L 2059 -325 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "      </defs>\n", "      <use xlink:href=\"#DejaVuSans-45\"/>\n", "      <use xlink:href=\"#DejaVuSans-73\" x=\"63.183594\"/>\n", "      <use xlink:href=\"#DejaVuSans-74\" x=\"115.283203\"/>\n", "      <use xlink:href=\"#DejaVuSans-69\" x=\"154.492188\"/>\n", "      <use xlink:href=\"#DejaVuSans-6d\" x=\"182.275391\"/>\n", "      <use xlink:href=\"#DejaVuSans-61\" x=\"279.6875\"/>\n", "      <use xlink:href=\"#DejaVuSans-74\" x=\"340.966797\"/>\n", "      <use xlink:href=\"#DejaVuSans-65\" x=\"380.175781\"/>\n", "      <use xlink:href=\"#DejaVuSans-64\" x=\"441.699219\"/>\n", "      <use xlink:href=\"#DejaVuSans-20\" x=\"505.175781\"/>\n", "      <use xlink:href=\"#DejaVuSans-70\" x=\"536.962891\"/>\n", "      <use xlink:href=\"#DejaVuSans-72\" x=\"600.439453\"/>\n", "      <use xlink:href=\"#DejaVuSans-6f\" x=\"639.302734\"/>\n", "      <use xlink:href=\"#DejaVuSans-62\" x=\"700.484375\"/>\n", "      <use xlink:href=\"#DejaVuSans-61\" x=\"763.960938\"/>\n", "      <use xlink:href=\"#DejaVuSans-62\" x=\"825.240234\"/>\n", "      <use xlink:href=\"#DejaVuSans-69\" x=\"888.716797\"/>\n", "      <use xlink:href=\"#DejaVuSans-6c\" x=\"916.5\"/>\n", "      <use xlink:href=\"#DejaVuSans-69\" x=\"944.283203\"/>\n", "      <use xlink:href=\"#DejaVuSans-74\" x=\"972.066406\"/>\n", "      <use xlink:href=\"#DejaVuSans-79\" x=\"1011.275391\"/>\n", "     </g>\n", "    </g>\n", "   </g>\n", "   <g id=\"line2d_15\">\n", "    <path d=\"M 65.361932 245.34 \n", "L 65.971879 212.939999 \n", "L 66.581826 137.339995 \n", "L 67.191773 148.139994 \n", "L 67.80172 115.739996 \n", "L 68.411668 126.539995 \n", "L 69.021615 143.511423 \n", "L 69.631562 148.139994 \n", "L 70.241509 144.539995 \n", "L 70.851456 141.660001 \n", "L 72.071351 148.139994 \n", "L 72.681298 140.663072 \n", "L 73.291245 138.882852 \n", "L 74.511139 144.089998 \n", "L 75.121086 138.610583 \n", "L 75.731034 137.339995 \n", "L 76.340981 139.613682 \n", "L 76.950928 138.419994 \n", "L 77.560875 143.511423 \n", "L 78.170822 139.303639 \n", "L 79.390717 142.74 \n", "L 80.000664 144.251996 \n", "L 80.610611 143.155379 \n", "L 81.220558 144.539995 \n", "L 81.830505 141.197142 \n", "L 82.440452 142.553794 \n", "L 83.660347 136.643221 \n", "L 84.270294 133.964998 \n", "L 84.880241 135.37636 \n", "L 85.490188 138.610583 \n", "L 86.100135 136.105713 \n", "L 86.710083 139.14 \n", "L 87.32003 135.004867 \n", "L 89.149871 138.419994 \n", "L 90.369766 140.425709 \n", "L 90.979713 139.851624 \n", "L 91.58966 136.358178 \n", "L 93.419502 143.314471 \n", "L 94.029449 141.390001 \n", "L 95.249343 140.363998 \n", "L 95.85929 141.151759 \n", "L 97.079185 140.192827 \n", "L 97.689132 137.339995 \n", "L 98.299079 138.125449 \n", "L 98.909026 140.040002 \n", "L 100.12892 141.436552 \n", "L 100.738868 143.197624 \n", "L 101.348815 141.660001 \n", "L 102.568709 142.914193 \n", "L 103.178656 144.539995 \n", "L 103.788603 145.102502 \n", "L 104.398551 143.653849 \n", "L 105.008498 143.230908 \n", "L 106.228392 146.234112 \n", "L 106.838339 145.792169 \n", "L 107.448286 147.214288 \n", "L 108.058234 146.770983 \n", "L 109.888075 148.139994 \n", "L 110.498022 149.435997 \n", "L 111.107969 148.992634 \n", "L 111.717917 147.719217 \n", "L 112.327864 148.139994 \n", "L 113.547758 147.329995 \n", "L 114.157705 148.540002 \n", "L 114.767652 146.559508 \n", "L 115.987547 147.368571 \n", "L 116.597494 146.996469 \n", "L 117.207441 145.126043 \n", "L 117.817388 145.5331 \n", "L 118.427335 146.667269 \n", "L 119.037283 146.31977 \n", "L 119.64723 145.26 \n", "L 120.867124 144.618256 \n", "L 122.087018 145.382554 \n", "L 122.696966 146.434734 \n", "L 123.306913 146.114996 \n", "L 123.91686 146.469891 \n", "L 124.526807 146.156324 \n", "L 125.136754 144.539995 \n", "L 126.966596 145.598818 \n", "L 127.576543 145.308927 \n", "L 129.406384 148.139994 \n", "L 130.016332 146.020368 \n", "L 131.236226 145.464765 \n", "L 131.846173 144.016362 \n", "L 132.45612 144.929187 \n", "L 133.066067 144.668573 \n", "L 133.676015 143.839117 \n", "L 134.285962 142.455786 \n", "L 135.505856 143.11241 \n", "L 136.115803 142.878456 \n", "L 136.72575 141.001011 \n", "L 137.335698 140.788734 \n", "L 137.945645 141.660001 \n", "L 138.555592 140.910245 \n", "L 139.775486 142.608292 \n", "L 140.385433 141.869033 \n", "L 140.995381 141.660001 \n", "L 141.605328 142.482852 \n", "L 142.825222 142.065 \n", "L 143.435169 141.3586 \n", "L 144.045116 142.15846 \n", "L 144.655064 142.451451 \n", "L 145.265011 141.267274 \n", "L 145.874958 141.075333 \n", "L 146.484905 141.853429 \n", "L 147.094852 141.660001 \n", "L 148.314747 143.173571 \n", "L 148.924694 143.444343 \n", "L 149.534641 142.312656 \n", "L 150.144588 142.122859 \n", "L 150.754535 142.395321 \n", "L 151.97443 142.022514 \n", "L 152.584377 142.289994 \n", "L 153.194324 142.106897 \n", "L 153.804271 142.813974 \n", "L 155.024165 143.323779 \n", "L 155.634113 142.703761 \n", "L 156.24406 143.388001 \n", "L 157.463954 143.024213 \n", "L 158.073901 143.269406 \n", "L 158.683848 143.93221 \n", "L 159.293796 144.168385 \n", "L 159.903743 144.816918 \n", "L 160.51369 145.04446 \n", "L 161.123637 145.679243 \n", "L 162.953479 145.12136 \n", "L 163.563426 145.34 \n", "L 164.173373 144.760855 \n", "L 165.393267 145.194543 \n", "L 166.003214 145.017105 \n", "L 167.833056 145.647687 \n", "L 169.05295 144.539995 \n", "L 169.662897 144.37256 \n", "L 170.882792 144.788278 \n", "L 171.492739 143.88171 \n", "L 172.102686 143.721817 \n", "L 173.32258 142.679322 \n", "L 174.542475 142.379997 \n", "L 175.762369 143.511423 \n", "L 176.372316 143.35967 \n", "L 176.982263 143.913912 \n", "L 177.592211 143.761619 \n", "L 178.202158 143.26258 \n", "L 178.812105 143.461927 \n", "L 179.422052 142.625103 \n", "L 180.031999 143.168569 \n", "L 180.641946 141.660001 \n", "L 181.251894 141.524296 \n", "L 182.471788 141.9286 \n", "L 183.081735 141.79361 \n", "L 183.691682 142.32461 \n", "L 184.301629 142.188983 \n", "L 184.911577 142.383656 \n", "L 185.521524 141.921812 \n", "L 186.131471 141.79025 \n", "L 186.741418 141.335995 \n", "L 187.351365 141.531045 \n", "L 187.961312 142.044945 \n", "L 188.57126 142.234578 \n", "L 189.181207 142.104705 \n", "L 189.791154 142.608292 \n", "L 190.401101 141.848736 \n", "L 192.230943 142.404117 \n", "L 192.84089 142.277141 \n", "L 193.450837 141.537158 \n", "L 194.060784 141.721132 \n", "L 194.670731 140.990698 \n", "L 195.280678 140.872712 \n", "L 195.890626 141.057208 \n", "L 197.11052 140.226632 \n", "L 198.330414 141.186569 \n", "L 198.940361 141.07091 \n", "L 199.550309 141.249497 \n", "L 200.160256 140.842701 \n", "L 200.770203 141.020719 \n", "L 201.38015 141.486425 \n", "L 201.990097 141.660001 \n", "L 202.600044 140.971859 \n", "L 203.209992 141.146169 \n", "L 203.819939 141.603156 \n", "L 205.039833 141.378259 \n", "L 205.64978 141.547789 \n", "L 206.259727 141.157244 \n", "L 206.869675 141.048151 \n", "L 208.089569 141.384256 \n", "L 208.699516 141.001011 \n", "L 209.309463 141.167846 \n", "L 210.529358 140.955058 \n", "L 211.139305 141.120001 \n", "L 211.749252 140.745804 \n", "L 212.359199 141.178014 \n", "L 213.579093 141.500658 \n", "L 214.189041 140.602036 \n", "L 214.798988 140.500977 \n", "L 215.408935 140.663072 \n", "L 216.018882 140.562582 \n", "L 216.628829 140.723132 \n", "L 217.238776 140.104794 \n", "L 217.848724 140.524065 \n", "L 218.458671 140.168571 \n", "L 219.068618 139.559763 \n", "L 219.678565 139.465985 \n", "L 220.898459 139.786871 \n", "L 221.508407 139.693305 \n", "L 222.118354 139.349302 \n", "L 222.728301 139.758531 \n", "L 223.338248 139.167693 \n", "L 224.558142 139.48351 \n", "L 225.16809 139.393227 \n", "L 225.778037 139.549093 \n", "L 229.43772 139.019996 \n", "L 231.267561 138.052083 \n", "L 231.877508 138.207148 \n", "L 232.487456 138.125449 \n", "L 233.097403 138.279133 \n", "L 234.317297 137.184602 \n", "L 234.927244 137.339995 \n", "L 236.147139 136.725056 \n", "L 236.757086 137.110212 \n", "L 237.367033 137.263674 \n", "L 237.97698 137.187885 \n", "L 238.586927 137.339995 \n", "L 239.196874 137.71762 \n", "L 239.806822 137.866824 \n", "L 240.416769 137.790001 \n", "L 241.026716 137.489479 \n", "L 241.636663 136.967584 \n", "L 242.856557 136.378359 \n", "L 243.466505 136.307918 \n", "L 244.076452 136.017545 \n", "L 244.686399 135.948814 \n", "L 245.296346 135.661617 \n", "L 245.906293 135.594546 \n", "L 248.346082 136.191825 \n", "L 248.956029 136.124108 \n", "L 249.565976 136.484555 \n", "L 250.175923 136.416316 \n", "L 251.395818 136.704701 \n", "L 252.005765 136.636414 \n", "L 253.835606 137.061286 \n", "L 254.445554 137.40945 \n", "L 255.055501 137.547694 \n", "L 256.275395 136.99605 \n", "L 256.885342 136.928574 \n", "L 257.495289 136.451387 \n", "L 259.935078 136.192501 \n", "L 260.545025 136.330649 \n", "L 261.154972 136.669186 \n", "L 261.76492 136.604395 \n", "L 262.984814 136.077228 \n", "L 263.594761 136.014841 \n", "L 264.204708 136.151009 \n", "L 264.814655 136.483898 \n", "L 265.424603 136.420845 \n", "L 266.03455 135.965451 \n", "L 268.474338 135.723231 \n", "L 270.30418 134.968483 \n", "L 270.914127 135.295028 \n", "L 272.134021 135.179997 \n", "L 272.743969 135.313017 \n", "L 273.353916 135.255787 \n", "L 273.963863 134.821047 \n", "L 274.57381 134.577205 \n", "L 275.183757 134.710438 \n", "L 277.013599 134.546895 \n", "L 277.623546 134.864354 \n", "L 278.233493 134.80971 \n", "L 278.84344 134.386152 \n", "L 279.453387 134.33318 \n", "L 280.063335 134.647645 \n", "L 280.673282 134.045085 \n", "L 281.283229 133.993522 \n", "L 281.893176 134.124263 \n", "L 282.503123 134.072768 \n", "L 283.11307 133.840552 \n", "L 283.723018 134.151136 \n", "L 284.942912 134.04914 \n", "L 285.552859 134.356576 \n", "L 286.162806 134.305284 \n", "L 286.772753 134.076264 \n", "L 287.382701 134.203558 \n", "L 288.602595 134.809208 \n", "L 289.212542 134.229127 \n", "L 289.822489 133.827806 \n", "L 290.432436 133.954048 \n", "L 291.042384 134.254281 \n", "L 292.262278 134.155008 \n", "L 292.872225 134.279039 \n", "L 293.482172 134.2296 \n", "L 294.702067 134.475277 \n", "L 295.312014 134.425713 \n", "L 295.921961 134.205431 \n", "L 296.531908 134.327367 \n", "L 298.36175 135.196914 \n", "L 298.971697 135.314997 \n", "L 299.581644 135.095845 \n", "L 300.191591 135.045702 \n", "L 300.801538 135.163254 \n", "L 301.411485 134.946188 \n", "L 303.851274 135.411431 \n", "L 308.730851 135.017999 \n", "L 309.340799 134.646728 \n", "L 310.560693 135.196074 \n", "L 311.17064 135.14792 \n", "L 311.780587 135.259997 \n", "L 312.390534 135.531136 \n", "L 313.000482 135.482508 \n", "L 314.830323 134.8639 \n", "L 315.44027 134.97503 \n", "L 316.050217 134.771067 \n", "L 316.660165 135.038788 \n", "L 317.270112 134.992169 \n", "L 317.880059 134.789636 \n", "L 318.490006 134.743847 \n", "L 319.099953 135.009067 \n", "L 320.319848 135.226394 \n", "L 320.929795 135.025714 \n", "L 321.539742 135.133822 \n", "L 322.149689 134.780752 \n", "L 322.759636 134.888938 \n", "L 323.369583 135.149436 \n", "L 324.589478 135.362533 \n", "L 325.809372 135.876442 \n", "L 326.419319 135.829505 \n", "L 327.029266 135.93349 \n", "L 328.249161 135.54 \n", "L 328.859108 135.793575 \n", "L 329.469055 135.598061 \n", "L 330.079002 135.552408 \n", "L 330.688949 135.804216 \n", "L 331.298897 135.610025 \n", "L 331.908844 135.7126 \n", "L 332.518791 135.667101 \n", "L 333.128738 135.769087 \n", "L 333.738685 135.723675 \n", "L 335.568527 136.464326 \n", "L 336.788421 136.371387 \n", "L 337.398368 136.470196 \n", "L 338.008315 136.279289 \n", "L 338.618263 135.944893 \n", "L 339.22821 136.188001 \n", "L 339.838157 136.142657 \n", "L 340.448104 136.240887 \n", "L 341.667998 136.150575 \n", "L 342.277946 136.390544 \n", "L 342.887893 136.487365 \n", "L 343.49784 136.441972 \n", "L 344.107787 136.255284 \n", "L 345.327681 136.166082 \n", "L 345.937629 136.402904 \n", "L 346.547576 136.217925 \n", "L 347.76747 136.408968 \n", "L 348.377417 136.085803 \n", "L 348.987365 136.181203 \n", "L 350.207259 136.093846 \n", "L 350.817206 136.326777 \n", "L 351.427153 136.420845 \n", "L 352.0371 136.239361 \n", "L 352.647048 136.333217 \n", "L 355.086836 136.160163 \n", "L 355.696783 135.981509 \n", "L 356.306731 136.074727 \n", "L 356.916678 136.03227 \n", "L 357.526625 136.259996 \n", "L 358.136572 136.217336 \n", "L 358.746519 136.443729 \n", "L 359.356466 136.266707 \n", "L 359.966414 136.358178 \n", "L 361.186308 136.273331 \n", "L 361.796255 136.364232 \n", "L 364.236044 136.196209 \n", "L 364.845991 136.286337 \n", "L 365.455938 136.507546 \n", "L 366.065885 136.334327 \n", "L 366.675832 136.423635 \n", "L 367.28578 136.251286 \n", "L 368.505674 136.168911 \n", "L 369.115621 136.257833 \n", "L 369.725568 136.216795 \n", "L 369.725568 136.216795 \n", "\" clip-path=\"url(#p939fb0ebd7)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_16\">\n", "    <path d=\"M 65.361932 115.739996 \n", "L 65.971879 180.539998 \n", "L 66.581826 158.939994 \n", "L 67.191773 164.339999 \n", "L 67.80172 180.539998 \n", "L 68.411668 148.139994 \n", "L 69.021615 134.254281 \n", "L 69.631562 148.139994 \n", "L 70.241509 158.939994 \n", "L 70.851456 154.619998 \n", "L 71.461403 145.194543 \n", "L 72.071351 142.74 \n", "L 72.681298 150.632302 \n", "L 73.291245 148.139994 \n", "L 74.511139 135.989996 \n", "L 75.121086 138.610583 \n", "L 75.731034 137.339995 \n", "L 76.340981 139.613682 \n", "L 77.560875 137.339995 \n", "L 78.170822 142.249091 \n", "L 79.390717 145.439997 \n", "L 81.220558 125.340002 \n", "L 82.440452 129.14689 \n", "L 83.660347 132.46258 \n", "L 84.270294 135.989996 \n", "L 84.880241 137.339995 \n", "L 86.100135 136.105713 \n", "L 86.710083 131.94 \n", "L 87.32003 135.004867 \n", "L 87.929977 136.203151 \n", "L 88.539924 135.678457 \n", "L 89.149871 133.559998 \n", "L 90.369766 135.797138 \n", "L 90.979713 133.823722 \n", "L 91.58966 136.358178 \n", "L 92.809554 132.644344 \n", "L 93.419502 135.042129 \n", "L 94.639396 136.899181 \n", "L 95.249343 139.067995 \n", "L 95.85929 138.610583 \n", "L 96.469237 139.416923 \n", "L 97.079185 138.97019 \n", "L 97.689132 136.140001 \n", "L 98.299079 134.590907 \n", "L 99.518973 133.929474 \n", "L 100.12892 135.850342 \n", "L 100.738868 134.41118 \n", "L 101.348815 136.259996 \n", "L 101.958762 134.861312 \n", "L 102.568709 134.552901 \n", "L 103.178656 135.282852 \n", "L 103.788603 134.977502 \n", "L 105.008498 136.358178 \n", "L 105.618445 134.116114 \n", "L 106.228392 132.892936 \n", "L 106.838339 130.766087 \n", "L 107.448286 132.402858 \n", "L 108.668181 130.139995 \n", "L 109.888075 129.750812 \n", "L 110.498022 127.835998 \n", "L 112.327864 129.863072 \n", "L 112.937811 129.684302 \n", "L 113.547758 130.320002 \n", "L 114.157705 130.139995 \n", "L 115.3776 131.354453 \n", "L 115.987547 130.397143 \n", "L 116.597494 130.224707 \n", "L 119.64723 133.019999 \n", "L 120.257177 132.830105 \n", "L 120.867124 133.348697 \n", "L 121.477071 133.159354 \n", "L 122.087018 134.352762 \n", "L 122.696966 134.838949 \n", "L 123.306913 135.989996 \n", "L 125.136754 137.339995 \n", "L 125.746701 137.124001 \n", "L 126.966596 137.975289 \n", "L 127.576543 137.130287 \n", "L 128.18649 135.678457 \n", "L 128.796437 135.488572 \n", "L 129.406384 134.079624 \n", "L 130.016332 133.302619 \n", "L 130.626279 133.739995 \n", "L 131.236226 133.574859 \n", "L 131.846173 134.001816 \n", "L 132.45612 133.253509 \n", "L 133.066067 133.09714 \n", "L 133.676015 133.516991 \n", "L 134.285962 134.497891 \n", "L 134.895909 134.898256 \n", "L 135.505856 134.174484 \n", "L 136.72575 134.960334 \n", "L 137.335698 134.798819 \n", "L 137.945645 134.099998 \n", "L 139.775486 135.23268 \n", "L 140.995381 134.920802 \n", "L 141.605328 135.282852 \n", "L 142.825222 134.977502 \n", "L 143.435169 135.833019 \n", "L 144.045116 136.176926 \n", "L 144.655064 135.031604 \n", "L 145.265011 135.867269 \n", "L 145.874958 136.203151 \n", "L 146.484905 137.017612 \n", "L 147.704799 135.751765 \n", "L 148.314747 135.605689 \n", "L 148.924694 134.992169 \n", "L 150.144588 135.642855 \n", "L 150.754535 134.582554 \n", "L 151.97443 134.319014 \n", "L 152.584377 133.289999 \n", "L 153.804271 133.049585 \n", "L 154.414218 132.491017 \n", "L 155.024165 133.253509 \n", "L 157.463954 134.497891 \n", "L 158.073901 134.375289 \n", "L 158.683848 133.833503 \n", "L 159.903743 134.432308 \n", "L 160.51369 134.31325 \n", "L 161.123637 133.375444 \n", "L 161.733584 134.079624 \n", "L 162.343531 133.559998 \n", "L 162.953479 134.254281 \n", "L 163.563426 134.140003 \n", "L 164.173373 133.232024 \n", "L 164.78332 133.520486 \n", "L 165.393267 134.19818 \n", "L 166.003214 134.086982 \n", "L 166.613162 133.589102 \n", "L 167.833056 134.144734 \n", "L 168.443003 133.655293 \n", "L 169.05295 133.929474 \n", "L 169.662897 133.070229 \n", "L 170.882792 132.871037 \n", "L 171.492739 133.513708 \n", "L 173.32258 134.306288 \n", "L 173.932528 134.202564 \n", "L 175.152422 134.71458 \n", "L 175.762369 134.254281 \n", "L 176.372316 134.153115 \n", "L 176.982263 134.405213 \n", "L 177.592211 134.304318 \n", "L 178.202158 133.50774 \n", "L 178.812105 133.412726 \n", "L 179.422052 133.663404 \n", "L 180.031999 133.568573 \n", "L 180.641946 133.815785 \n", "L 181.251894 134.39968 \n", "L 181.861841 134.639997 \n", "L 182.471788 134.542067 \n", "L 183.081735 134.111131 \n", "L 183.691682 134.016918 \n", "L 184.301629 134.254281 \n", "L 184.911577 134.160299 \n", "L 186.131471 135.277686 \n", "L 186.741418 135.504002 \n", "L 187.351365 135.083283 \n", "L 187.961312 135.629105 \n", "L 188.57126 135.21192 \n", "L 189.181207 135.11647 \n", "L 189.791154 135.338046 \n", "L 190.401101 135.872039 \n", "L 191.011048 136.087821 \n", "L 191.620995 135.678457 \n", "L 192.84089 135.488572 \n", "L 193.450837 135.702085 \n", "L 194.060784 136.219248 \n", "L 194.670731 136.427324 \n", "L 195.280678 136.93626 \n", "L 195.890626 136.837673 \n", "L 196.500573 137.339995 \n", "L 197.720467 137.736333 \n", "L 198.330414 137.339995 \n", "L 198.940361 137.830904 \n", "L 199.550309 137.437732 \n", "L 200.160256 137.923784 \n", "L 200.770203 137.824299 \n", "L 201.38015 138.014994 \n", "L 201.990097 137.916001 \n", "L 202.600044 138.104602 \n", "L 203.209992 137.435154 \n", "L 204.429886 137.245676 \n", "L 205.039833 137.433909 \n", "L 205.64978 137.05948 \n", "L 206.259727 136.967584 \n", "L 206.869675 137.154591 \n", "L 208.089569 135.869364 \n", "L 209.309463 136.246323 \n", "L 209.91941 135.887894 \n", "L 210.529358 136.074727 \n", "L 211.139305 135.449997 \n", "L 211.749252 135.90597 \n", "L 212.969146 136.273331 \n", "L 213.579093 136.189179 \n", "L 214.798988 136.549752 \n", "L 215.408935 136.465504 \n", "L 216.628829 136.81952 \n", "L 217.238776 136.476 \n", "L 219.068618 136.23012 \n", "L 219.678565 135.894325 \n", "L 220.288512 135.815291 \n", "L 220.898459 135.483744 \n", "L 221.508407 135.911204 \n", "L 223.948195 135.602068 \n", "L 224.558142 136.020915 \n", "L 225.16809 136.190193 \n", "L 225.778037 136.112723 \n", "L 226.387984 136.28038 \n", "L 226.997931 136.203151 \n", "L 227.607878 136.611907 \n", "L 228.217825 136.050442 \n", "L 230.047667 137.260295 \n", "L 230.657614 137.419405 \n", "L 231.267561 137.339995 \n", "L 232.487456 137.65418 \n", "L 233.097403 136.870436 \n", "L 233.70735 136.794154 \n", "L 234.317297 136.951507 \n", "L 234.927244 136.643221 \n", "L 235.537191 137.031429 \n", "L 236.147139 137.186263 \n", "L 236.757086 137.569787 \n", "L 237.367033 137.721627 \n", "L 237.97698 137.644225 \n", "L 238.586927 138.022101 \n", "L 239.806822 137.866824 \n", "L 241.026716 138.610583 \n", "L 244.686399 139.463387 \n", "L 245.906293 140.176363 \n", "L 246.51624 140.311807 \n", "L 247.736135 139.715997 \n", "L 248.346082 139.636345 \n", "L 248.956029 139.771789 \n", "L 249.565976 139.478615 \n", "L 250.175923 139.826837 \n", "L 250.785871 139.960331 \n", "L 252.005765 139.802543 \n", "L 252.615712 139.9348 \n", "L 253.225659 139.437084 \n", "L 253.835606 139.360648 \n", "L 254.445554 139.49305 \n", "L 255.055501 139.416923 \n", "L 255.665448 139.134245 \n", "L 256.275395 139.266116 \n", "L 256.885342 138.985717 \n", "L 257.495289 139.322275 \n", "L 258.715184 139.17396 \n", "L 259.935078 139.432498 \n", "L 260.545025 139.762432 \n", "L 261.154972 139.486581 \n", "L 261.76492 139.41306 \n", "L 262.374867 139.740001 \n", "L 262.984814 139.666153 \n", "L 263.594761 139.39399 \n", "L 264.204708 139.519817 \n", "L 264.814655 139.44732 \n", "L 265.424603 138.981333 \n", "L 266.03455 139.107276 \n", "L 266.644497 138.840907 \n", "L 267.254444 139.161687 \n", "L 267.864391 138.70216 \n", "L 268.474338 138.827427 \n", "L 269.084286 138.565075 \n", "L 270.914127 138.93763 \n", "L 271.524074 138.869199 \n", "L 272.134021 138.991762 \n", "L 272.743969 139.303639 \n", "L 273.353916 139.424213 \n", "L 273.963863 139.166235 \n", "L 274.57381 139.098141 \n", "L 275.183757 139.218261 \n", "L 276.403652 139.082933 \n", "L 277.013599 139.202068 \n", "L 277.623546 139.506192 \n", "L 278.233493 139.253138 \n", "L 278.84344 139.186155 \n", "L 279.453387 139.303639 \n", "L 280.673282 139.902714 \n", "L 281.283229 139.834649 \n", "L 281.893176 139.948985 \n", "L 282.503123 139.518146 \n", "L 283.11307 139.632733 \n", "L 283.723018 139.385676 \n", "L 284.332965 139.319996 \n", "L 284.942912 139.434178 \n", "L 285.552859 139.36873 \n", "L 286.162806 138.768101 \n", "L 286.772753 138.704835 \n", "L 287.992648 138.93344 \n", "L 288.602595 138.693673 \n", "L 289.822489 138.920481 \n", "L 290.432436 139.208103 \n", "L 291.042384 139.144847 \n", "L 292.262278 138.671898 \n", "L 292.872225 138.610583 \n", "L 294.092119 138.144249 \n", "L 294.702067 137.741055 \n", "L 295.312014 137.682859 \n", "L 296.531908 137.908422 \n", "L 297.141855 137.680155 \n", "L 298.36175 137.90397 \n", "L 298.971697 138.183751 \n", "L 299.581644 137.957145 \n", "L 300.191591 138.067455 \n", "L 300.801538 137.674883 \n", "L 301.411485 137.952366 \n", "L 302.021433 137.89527 \n", "L 302.63138 138.170764 \n", "L 304.461221 137.999545 \n", "L 305.071168 138.107508 \n", "L 306.291063 137.994543 \n", "L 307.510957 137.55708 \n", "L 308.120904 137.827215 \n", "L 310.560693 137.607986 \n", "L 311.17064 137.39346 \n", "L 311.780587 137.660003 \n", "L 312.390534 137.76561 \n", "L 313.000482 138.029922 \n", "L 313.610429 137.975289 \n", "L 314.830323 138.182921 \n", "L 316.050217 138.703106 \n", "L 317.270112 138.905215 \n", "L 317.880059 138.693248 \n", "L 318.490006 138.793843 \n", "L 319.099953 138.738563 \n", "L 319.7099 138.528517 \n", "L 320.929795 138.419994 \n", "L 321.539742 138.520049 \n", "L 322.149689 138.312514 \n", "L 322.759636 137.952762 \n", "L 323.369583 138.053203 \n", "L 323.979531 138.000704 \n", "L 324.589478 137.644225 \n", "L 325.199425 137.44117 \n", "L 325.809372 137.390467 \n", "L 326.419319 137.491043 \n", "L 327.029266 137.741856 \n", "L 327.639214 137.690815 \n", "L 328.859108 137.888723 \n", "L 329.469055 137.837692 \n", "L 330.688949 137.439084 \n", "L 331.298897 137.389424 \n", "L 331.908844 137.487943 \n", "L 332.518791 137.143187 \n", "L 333.128738 136.947268 \n", "L 334.348632 137.14452 \n", "L 334.95858 137.096201 \n", "L 335.568527 136.610266 \n", "L 336.178474 136.85461 \n", "L 337.398368 137.050065 \n", "L 338.008315 137.0025 \n", "L 338.618263 137.243783 \n", "L 339.22821 137.051996 \n", "L 339.838157 137.148421 \n", "L 340.448104 137.101058 \n", "L 341.058051 137.339995 \n", "L 341.667998 137.435154 \n", "L 342.277946 137.387473 \n", "L 342.887893 137.482102 \n", "L 343.49784 137.434527 \n", "L 344.107787 137.528643 \n", "L 344.717734 137.763524 \n", "L 345.937629 137.949112 \n", "L 347.157523 137.573263 \n", "L 347.76747 137.665864 \n", "L 348.377417 137.47935 \n", "L 348.987365 137.571757 \n", "L 349.597312 137.525013 \n", "L 350.207259 137.755384 \n", "L 350.817206 137.708437 \n", "L 351.427153 137.79957 \n", "L 352.0371 137.477573 \n", "L 352.647048 137.568812 \n", "L 353.256995 137.248669 \n", "L 353.866942 137.203286 \n", "L 354.476889 137.294525 \n", "L 355.086836 137.249239 \n", "L 355.696783 137.475845 \n", "L 357.526625 137.339995 \n", "L 358.746519 137.519248 \n", "L 360.576361 137.384538 \n", "L 361.186308 137.473334 \n", "L 361.796255 137.694822 \n", "L 363.016149 137.605032 \n", "L 363.626097 137.428163 \n", "L 364.236044 137.515965 \n", "L 364.845991 137.208288 \n", "L 365.455938 137.296186 \n", "L 366.065885 137.252551 \n", "L 366.675832 137.078183 \n", "L 367.28578 137.165802 \n", "L 368.505674 136.81952 \n", "L 369.115621 136.907128 \n", "L 369.725568 137.124001 \n", "L 369.725568 137.124001 \n", "\" clip-path=\"url(#p939fb0ebd7)\" style=\"fill: none; stroke: #ff7f0e; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_17\">\n", "    <path d=\"M 65.361932 50.939989 \n", "L 65.971879 18.54 \n", "L 66.581826 94.139997 \n", "L 67.191773 131.94 \n", "L 67.80172 141.660001 \n", "L 68.411668 148.139994 \n", "L 69.021615 162.025708 \n", "L 69.631562 164.339999 \n", "L 70.241509 151.739994 \n", "L 70.851456 148.139994 \n", "L 71.461403 151.085456 \n", "L 72.071351 148.139994 \n", "L 72.681298 150.632302 \n", "L 73.291245 157.397137 \n", "L 74.511139 160.290002 \n", "L 75.121086 146.234112 \n", "L 75.731034 144.539995 \n", "L 76.340981 139.613682 \n", "L 77.560875 143.511423 \n", "L 78.780769 141.096518 \n", "L 79.390717 140.040002 \n", "L 80.610611 143.155379 \n", "L 81.220558 142.139998 \n", "L 81.830505 138.882852 \n", "L 82.440452 138.084826 \n", "L 83.0504 139.500003 \n", "L 83.660347 138.733542 \n", "L 84.270294 142.065 \n", "L 84.880241 143.230908 \n", "L 85.490188 142.422348 \n", "L 86.710083 144.539995 \n", "L 87.32003 142.01027 \n", "L 87.929977 143.024213 \n", "L 88.539924 142.32461 \n", "L 89.149871 143.279999 \n", "L 89.759819 139.44732 \n", "L 90.369766 137.339995 \n", "L 90.979713 139.851624 \n", "L 91.58966 139.303639 \n", "L 92.809554 141.096518 \n", "L 93.419502 139.178295 \n", "L 94.029449 140.040002 \n", "L 94.639396 139.544082 \n", "L 95.249343 137.772002 \n", "L 95.85929 139.881171 \n", "L 96.469237 140.663072 \n", "L 97.079185 142.63811 \n", "L 97.689132 143.340001 \n", "L 98.299079 141.660001 \n", "L 98.909026 141.197142 \n", "L 99.518973 143.024213 \n", "L 101.348815 141.660001 \n", "L 101.958762 143.35967 \n", "L 102.568709 142.914193 \n", "L 103.178656 143.511423 \n", "L 103.788603 141.052496 \n", "L 104.398551 142.65692 \n", "L 105.618445 143.787757 \n", "L 106.228392 142.422348 \n", "L 106.838339 143.913912 \n", "L 107.448286 144.437139 \n", "L 108.668181 143.640002 \n", "L 109.278128 144.145481 \n", "L 109.888075 143.761619 \n", "L 110.498022 144.251996 \n", "L 111.107969 142.171573 \n", "L 111.717917 142.669868 \n", "L 113.547758 141.660001 \n", "L 114.767652 142.608292 \n", "L 115.3776 142.284577 \n", "L 117.817388 144.043447 \n", "L 118.427335 145.194543 \n", "L 119.64723 144.539995 \n", "L 120.257177 145.647687 \n", "L 120.867124 146.026953 \n", "L 122.087018 145.382554 \n", "L 122.696966 146.434734 \n", "L 123.306913 145.439997 \n", "L 123.91686 145.801854 \n", "L 124.526807 145.495104 \n", "L 125.136754 145.849091 \n", "L 126.356649 145.252874 \n", "L 126.966596 144.32823 \n", "L 127.576543 144.679803 \n", "L 129.406384 143.860756 \n", "L 130.626279 144.539995 \n", "L 131.236226 144.275779 \n", "L 131.846173 144.605452 \n", "L 132.45612 144.345408 \n", "L 133.066067 144.668573 \n", "L 133.676015 144.412565 \n", "L 134.285962 144.729473 \n", "L 135.505856 144.229652 \n", "L 136.115803 144.539995 \n", "L 137.335698 146.234112 \n", "L 138.555592 146.801158 \n", "L 139.775486 145.242436 \n", "L 140.385433 146.049674 \n", "L 140.995381 145.288794 \n", "L 142.215275 144.823464 \n", "L 142.825222 145.608744 \n", "L 143.435169 145.377204 \n", "L 144.045116 144.650768 \n", "L 144.655064 145.419391 \n", "L 145.265011 145.685451 \n", "L 145.874958 146.434734 \n", "L 146.484905 146.205666 \n", "L 147.094852 145.020002 \n", "L 147.704799 145.281176 \n", "L 148.924694 144.853041 \n", "L 150.144588 145.362856 \n", "L 151.364482 144.945632 \n", "L 151.97443 145.194543 \n", "L 152.584377 145.890003 \n", "L 153.194324 146.128969 \n", "L 153.804271 144.589317 \n", "L 154.414218 145.274697 \n", "L 155.634113 144.878252 \n", "L 156.24406 143.819999 \n", "L 156.854007 143.204895 \n", "L 157.463954 143.876843 \n", "L 158.683848 143.511423 \n", "L 159.293796 142.914193 \n", "L 159.903743 143.155379 \n", "L 161.123637 142.808354 \n", "L 162.343531 143.279999 \n", "L 162.953479 143.108944 \n", "L 163.563426 142.539996 \n", "L 164.78332 143.793657 \n", "L 165.393267 143.623635 \n", "L 166.003214 144.236383 \n", "L 166.613162 143.677718 \n", "L 167.223109 142.74 \n", "L 167.833056 142.580232 \n", "L 168.443003 142.803526 \n", "L 169.05295 142.266317 \n", "L 169.662897 142.488839 \n", "L 170.272845 141.959654 \n", "L 170.882792 141.808963 \n", "L 172.102686 142.249091 \n", "L 172.712633 141.733222 \n", "L 174.542475 141.299998 \n", "L 175.762369 140.307028 \n", "L 176.372316 140.880978 \n", "L 176.982263 139.687821 \n", "L 178.812105 141.382778 \n", "L 179.422052 141.246378 \n", "L 180.031999 140.082855 \n", "L 181.861841 140.715001 \n", "L 182.471788 141.257096 \n", "L 183.081735 141.125563 \n", "L 183.691682 140.330772 \n", "L 184.301629 140.535912 \n", "L 186.131471 140.162111 \n", "L 186.741418 139.715997 \n", "L 187.351365 140.241493 \n", "L 188.57126 138.723249 \n", "L 189.181207 138.610583 \n", "L 190.401101 139.017668 \n", "L 191.011048 138.905215 \n", "L 191.620995 139.105383 \n", "L 192.230943 138.683544 \n", "L 193.450837 139.080287 \n", "L 194.060784 138.664531 \n", "L 195.890626 139.248832 \n", "L 196.500573 139.740001 \n", "L 197.11052 139.629402 \n", "L 197.720467 138.62807 \n", "L 198.330414 139.115338 \n", "L 199.550309 139.490221 \n", "L 200.160256 139.383243 \n", "L 200.770203 138.696048 \n", "L 201.38015 138.304287 \n", "L 201.990097 137.627993 \n", "L 203.209992 138.577 \n", "L 204.429886 138.943491 \n", "L 205.039833 139.406089 \n", "L 205.64978 139.303639 \n", "L 206.259727 139.481376 \n", "L 206.869675 139.379487 \n", "L 207.479622 139.832303 \n", "L 208.699516 140.17729 \n", "L 209.309463 139.527339 \n", "L 210.529358 139.87054 \n", "L 211.139305 140.310002 \n", "L 211.749252 140.476924 \n", "L 212.359199 140.374706 \n", "L 214.798988 141.027806 \n", "L 215.408935 140.663072 \n", "L 216.628829 140.983369 \n", "L 217.238776 141.400796 \n", "L 217.848724 141.298569 \n", "L 218.458671 141.45428 \n", "L 219.068618 141.352642 \n", "L 219.678565 140.996695 \n", "L 220.288512 141.151759 \n", "L 221.508407 141.96257 \n", "L 222.728301 141.760075 \n", "L 223.338248 141.90923 \n", "L 223.948195 141.808963 \n", "L 225.778037 142.249091 \n", "L 226.387984 141.904528 \n", "L 226.997931 142.293383 \n", "L 227.607878 142.193926 \n", "L 228.217825 142.578803 \n", "L 230.047667 142.281699 \n", "L 230.657614 142.422348 \n", "L 231.267561 142.087247 \n", "L 231.877508 142.227587 \n", "L 232.487456 142.131269 \n", "L 233.70735 142.408588 \n", "L 234.317297 142.778845 \n", "L 234.927244 142.914193 \n", "L 236.147139 142.259568 \n", "L 236.757086 142.165528 \n", "L 237.367033 142.301127 \n", "L 237.97698 142.207599 \n", "L 238.586927 142.342107 \n", "L 239.196874 142.249091 \n", "L 239.806822 142.608292 \n", "L 240.416769 142.289994 \n", "L 241.636663 142.106897 \n", "L 243.466505 142.500406 \n", "L 244.076452 142.409389 \n", "L 244.686399 142.758307 \n", "L 245.296346 142.88594 \n", "L 245.906293 142.794546 \n", "L 246.51624 142.921203 \n", "L 247.126188 142.830302 \n", "L 249.565976 143.328115 \n", "L 250.175923 143.663678 \n", "L 250.785871 143.35967 \n", "L 251.395818 143.481171 \n", "L 252.615712 143.301039 \n", "L 253.225659 143.631263 \n", "L 253.835606 143.750321 \n", "L 256.275395 142.568027 \n", "L 256.885342 142.894282 \n", "L 258.715184 142.63811 \n", "L 259.325131 142.350652 \n", "L 259.935078 142.267495 \n", "L 260.545025 142.386727 \n", "L 261.76492 143.024213 \n", "L 262.984814 143.255077 \n", "L 263.594761 143.170674 \n", "L 264.204708 143.284953 \n", "L 265.424603 143.117509 \n", "L 266.03455 143.427272 \n", "L 266.644497 143.343622 \n", "L 267.254444 143.45566 \n", "L 267.864391 143.761619 \n", "L 268.474338 143.483711 \n", "L 269.694233 143.704282 \n", "L 270.30418 143.621308 \n", "L 270.914127 143.730526 \n", "L 271.524074 143.647958 \n", "L 272.134021 143.947061 \n", "L 272.743969 143.864339 \n", "L 273.353916 144.161047 \n", "L 274.57381 143.995814 \n", "L 275.183757 144.10174 \n", "L 275.793704 144.394335 \n", "L 277.013599 144.229652 \n", "L 277.623546 144.333695 \n", "L 278.233493 144.622283 \n", "L 278.84344 144.724616 \n", "L 279.453387 144.64227 \n", "L 280.063335 144.743967 \n", "L 280.673282 144.478979 \n", "L 281.283229 144.398023 \n", "L 281.893176 144.499546 \n", "L 282.503123 144.782011 \n", "L 283.11307 144.881902 \n", "L 283.723018 144.800725 \n", "L 284.332965 145.079994 \n", "L 284.942912 144.819225 \n", "L 286.162806 145.016033 \n", "L 286.772753 144.935599 \n", "L 287.382701 144.50055 \n", "L 287.992648 143.890815 \n", "L 288.602595 143.814108 \n", "L 289.212542 143.913912 \n", "L 289.822489 143.66195 \n", "L 291.042384 143.860756 \n", "L 291.652331 143.78516 \n", "L 292.872225 144.32823 \n", "L 293.482172 144.079203 \n", "L 294.092119 144.348507 \n", "L 294.702067 144.444507 \n", "L 295.312014 144.368573 \n", "L 295.921961 144.122056 \n", "L 296.531908 144.047367 \n", "L 297.141855 143.802994 \n", "L 297.751802 143.899158 \n", "L 298.36175 143.825638 \n", "L 298.971697 143.414999 \n", "L 299.581644 143.679736 \n", "L 300.191591 143.271608 \n", "L 300.801538 143.535351 \n", "L 302.021433 143.725602 \n", "L 302.63138 143.653849 \n", "L 303.241327 143.416727 \n", "L 303.851274 143.346123 \n", "L 304.461221 143.605646 \n", "L 305.071168 143.370456 \n", "L 305.681116 143.628608 \n", "L 306.90101 143.814562 \n", "L 308.120904 143.673836 \n", "L 308.730851 143.279999 \n", "L 309.950746 143.787757 \n", "L 310.560693 143.718167 \n", "L 311.780587 143.899998 \n", "L 312.390534 143.830639 \n", "L 313.000482 144.080043 \n", "L 313.610429 143.851767 \n", "L 314.220376 143.466156 \n", "L 314.830323 143.556584 \n", "L 315.44027 143.80423 \n", "L 316.050217 143.736117 \n", "L 316.660165 143.511423 \n", "L 317.270112 143.444343 \n", "L 317.880059 143.533729 \n", "L 318.490006 143.778459 \n", "L 319.099953 143.711224 \n", "L 319.7099 143.954351 \n", "L 320.929795 144.128574 \n", "L 321.539742 143.907221 \n", "L 322.149689 144.147577 \n", "L 323.369583 143.707922 \n", "L 323.979531 143.794584 \n", "L 326.419319 143.533004 \n", "L 327.029266 143.619068 \n", "L 327.639214 143.404039 \n", "L 328.249161 143.489997 \n", "L 328.859108 143.276253 \n", "L 332.518791 143.785556 \n", "L 333.128738 143.721817 \n", "L 333.738685 143.805302 \n", "L 334.348632 143.595198 \n", "L 335.568527 143.761619 \n", "L 336.178474 143.989885 \n", "L 336.788421 143.635966 \n", "L 337.398368 143.428585 \n", "L 338.008315 143.366787 \n", "L 338.618263 143.449577 \n", "L 339.22821 143.388001 \n", "L 340.448104 143.839117 \n", "L 341.058051 143.347948 \n", "L 342.277946 142.941761 \n", "L 343.49784 143.106298 \n", "L 344.107787 143.046547 \n", "L 344.717734 143.128236 \n", "L 345.327681 143.068698 \n", "L 346.547576 143.230908 \n", "L 347.157523 143.031571 \n", "L 348.377417 143.192902 \n", "L 348.987365 142.994936 \n", "L 350.207259 143.155379 \n", "L 350.817206 143.096932 \n", "L 351.427153 142.900848 \n", "L 352.0371 142.980761 \n", "L 352.647048 142.785759 \n", "L 353.256995 143.002574 \n", "L 353.866942 142.945063 \n", "L 354.476889 142.751365 \n", "L 355.696783 142.63811 \n", "L 356.306731 142.717405 \n", "L 357.526625 142.335 \n", "L 358.136572 142.54915 \n", "L 358.746519 142.359082 \n", "L 359.356466 142.438135 \n", "L 360.576361 142.328038 \n", "L 361.186308 142.406666 \n", "L 361.796255 142.618026 \n", "L 362.406202 142.695737 \n", "L 363.016149 142.640611 \n", "L 363.626097 142.850203 \n", "L 364.236044 142.79499 \n", "L 366.065885 143.024213 \n", "L 366.675832 142.838181 \n", "L 367.28578 142.783548 \n", "L 367.895727 142.989896 \n", "L 369.115621 142.880677 \n", "L 369.725568 142.955994 \n", "L 369.725568 142.955994 \n", "\" clip-path=\"url(#p939fb0ebd7)\" style=\"fill: none; stroke: #2ca02c; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_18\">\n", "    <path d=\"M 65.361932 180.539998 \n", "L 65.971879 212.939999 \n", "L 66.581826 202.139997 \n", "L 67.191773 164.339999 \n", "L 67.80172 154.619998 \n", "L 68.411668 148.139994 \n", "L 69.021615 143.511423 \n", "L 69.631562 123.839998 \n", "L 70.241509 122.939996 \n", "L 70.851456 135.179997 \n", "L 71.461403 121.630909 \n", "L 72.071351 115.739996 \n", "L 72.681298 115.739996 \n", "L 73.291245 120.368567 \n", "L 73.901192 120.060001 \n", "L 74.511139 115.739996 \n", "L 75.731034 130.139995 \n", "L 76.340981 132.79263 \n", "L 76.950928 138.419994 \n", "L 78.170822 136.358178 \n", "L 78.780769 132.644344 \n", "L 79.390717 131.94 \n", "L 80.000664 128.699993 \n", "L 81.220558 132.540002 \n", "L 81.830505 134.254281 \n", "L 82.440452 133.615858 \n", "L 83.660347 136.643221 \n", "L 84.880241 135.37636 \n", "L 86.100135 137.957145 \n", "L 86.710083 135.54 \n", "L 87.32003 135.004867 \n", "L 87.929977 131.08737 \n", "L 88.539924 134.016918 \n", "L 89.149871 135.179997 \n", "L 90.979713 133.823722 \n", "L 91.58966 134.885452 \n", "L 92.199607 134.460001 \n", "L 92.809554 135.461738 \n", "L 93.419502 135.042129 \n", "L 94.639396 136.899181 \n", "L 95.249343 136.476 \n", "L 95.85929 134.798819 \n", "L 96.469237 135.678457 \n", "L 97.079185 135.302261 \n", "L 98.299079 139.303639 \n", "L 98.909026 136.568571 \n", "L 99.518973 136.203151 \n", "L 100.12892 134.7331 \n", "L 101.958762 136.985902 \n", "L 103.178656 140.425709 \n", "L 104.398551 139.666153 \n", "L 105.008498 138.321812 \n", "L 105.618445 137.984771 \n", "L 106.838339 139.218261 \n", "L 107.448286 136.105713 \n", "L 108.058234 136.731544 \n", "L 108.668181 136.440002 \n", "L 109.278128 135.268764 \n", "L 109.888075 133.253509 \n", "L 110.498022 133.019999 \n", "L 111.107969 133.645261 \n", "L 111.717917 133.412726 \n", "L 112.327864 134.016918 \n", "L 112.937811 133.785571 \n", "L 113.547758 135.179997 \n", "L 114.157705 134.939998 \n", "L 114.767652 135.496094 \n", "L 115.3776 133.696621 \n", "L 115.987547 133.482857 \n", "L 116.597494 134.798819 \n", "L 117.207441 133.823722 \n", "L 117.817388 132.126205 \n", "L 118.427335 131.203637 \n", "L 119.037283 131.757976 \n", "L 119.64723 133.019999 \n", "L 121.477071 132.46258 \n", "L 122.087018 130.905954 \n", "L 122.696966 130.746312 \n", "L 123.306913 131.94 \n", "L 125.136754 133.412726 \n", "L 125.746701 133.236003 \n", "L 126.356649 133.704355 \n", "L 126.966596 133.52823 \n", "L 128.18649 135.678457 \n", "L 128.796437 136.105713 \n", "L 129.406384 135.302261 \n", "L 130.016332 135.725047 \n", "L 130.626279 134.939998 \n", "L 131.236226 135.358342 \n", "L 131.846173 135.179997 \n", "L 132.45612 134.421078 \n", "L 133.066067 134.832856 \n", "L 133.676015 134.663896 \n", "L 134.285962 135.634734 \n", "L 134.895909 135.461738 \n", "L 135.505856 135.850342 \n", "L 136.72575 135.509487 \n", "L 137.945645 134.099998 \n", "L 138.555592 134.483803 \n", "L 139.165539 134.330157 \n", "L 139.775486 133.125365 \n", "L 140.385433 132.46258 \n", "L 140.995381 133.365595 \n", "L 141.605328 133.739995 \n", "L 142.215275 133.598265 \n", "L 142.825222 132.446252 \n", "L 143.435169 132.819068 \n", "L 144.045116 132.18923 \n", "L 144.655064 132.063664 \n", "L 145.265011 132.430909 \n", "L 145.874958 132.30541 \n", "L 146.484905 131.698205 \n", "L 147.094852 132.540002 \n", "L 147.704799 132.416473 \n", "L 148.314747 133.240724 \n", "L 148.924694 133.583472 \n", "L 150.144588 135.179997 \n", "L 151.364482 134.906193 \n", "L 151.97443 133.86587 \n", "L 152.584377 134.190001 \n", "L 153.804271 135.7126 \n", "L 154.414218 135.135918 \n", "L 155.634113 135.745363 \n", "L 156.24406 135.611995 \n", "L 156.854007 136.338673 \n", "L 157.463954 135.350521 \n", "L 158.683848 134.254281 \n", "L 159.293796 134.552901 \n", "L 159.903743 133.601538 \n", "L 161.123637 134.195698 \n", "L 162.343531 133.964998 \n", "L 162.953479 134.254281 \n", "L 163.563426 134.939998 \n", "L 164.173373 134.822205 \n", "L 164.78332 133.915608 \n", "L 165.393267 134.19818 \n", "L 166.003214 133.306259 \n", "L 167.223109 133.868574 \n", "L 167.833056 133.761296 \n", "L 168.443003 134.41765 \n", "L 169.05295 134.308422 \n", "L 169.662897 134.953951 \n", "L 170.882792 134.7331 \n", "L 171.492739 134.994854 \n", "L 172.102686 134.51727 \n", "L 173.32258 134.306288 \n", "L 173.932528 134.564575 \n", "L 174.542475 135.179997 \n", "L 175.762369 135.678457 \n", "L 176.372316 136.277705 \n", "L 176.982263 136.518264 \n", "L 177.592211 136.055676 \n", "L 178.202158 136.294835 \n", "L 178.812105 135.838398 \n", "L 179.422052 135.731487 \n", "L 180.641946 136.203151 \n", "L 181.251894 135.756757 \n", "L 181.861841 135.652501 \n", "L 183.081735 136.115253 \n", "L 183.691682 136.675385 \n", "L 184.301629 136.899181 \n", "L 184.911577 136.791778 \n", "L 186.131471 135.928942 \n", "L 186.741418 136.476 \n", "L 187.351365 135.728059 \n", "L 187.961312 135.308315 \n", "L 189.181207 136.387059 \n", "L 189.791154 136.286337 \n", "L 190.401101 135.872039 \n", "L 191.011048 135.774784 \n", "L 191.620995 136.301536 \n", "L 192.230943 136.203151 \n", "L 192.84089 136.722854 \n", "L 195.280678 137.541872 \n", "L 195.890626 137.440465 \n", "L 196.500573 137.039994 \n", "L 198.330414 136.74822 \n", "L 198.940361 136.358178 \n", "L 199.550309 136.264882 \n", "L 201.38015 136.857854 \n", "L 202.600044 136.670973 \n", "L 203.209992 136.864227 \n", "L 203.819939 136.487365 \n", "L 204.429886 136.396763 \n", "L 205.039833 136.025211 \n", "L 205.64978 136.217925 \n", "L 206.259727 136.12965 \n", "L 206.869675 136.59837 \n", "L 207.479622 136.509226 \n", "L 208.089569 136.69659 \n", "L 209.309463 136.519741 \n", "L 209.91941 136.97697 \n", "L 210.529358 136.888116 \n", "L 211.139305 136.529996 \n", "L 211.749252 136.443729 \n", "L 213.579093 135.392457 \n", "L 214.798988 135.759509 \n", "L 215.408935 136.203151 \n", "L 216.018882 136.120641 \n", "L 216.628829 135.77855 \n", "L 217.238776 135.9576 \n", "L 217.848724 135.877051 \n", "L 219.068618 136.23012 \n", "L 219.678565 136.659685 \n", "L 221.508407 135.659067 \n", "L 222.118354 135.833019 \n", "L 222.728301 135.505248 \n", "L 223.338248 135.678457 \n", "L 225.16809 136.929356 \n", "L 225.778037 136.849086 \n", "L 226.387984 136.524907 \n", "L 227.607878 136.369214 \n", "L 228.217825 136.534032 \n", "L 228.827773 136.456726 \n", "L 229.43772 136.859998 \n", "L 230.047667 136.782064 \n", "L 230.657614 136.228238 \n", "L 231.267561 136.627907 \n", "L 231.877508 136.31518 \n", "L 232.487456 136.240365 \n", "L 234.317297 136.718412 \n", "L 235.537191 136.568571 \n", "L 236.147139 136.725056 \n", "L 236.757086 136.650637 \n", "L 237.367033 136.805721 \n", "L 237.97698 137.187885 \n", "L 238.586927 137.112626 \n", "L 239.196874 137.264476 \n", "L 239.806822 136.963693 \n", "L 240.416769 137.339995 \n", "L 241.026716 137.041037 \n", "L 241.636663 136.967584 \n", "L 242.24661 136.449272 \n", "L 242.856557 136.822194 \n", "L 244.076452 136.678775 \n", "L 244.686399 136.827457 \n", "L 245.906293 136.685457 \n", "L 246.51624 136.397719 \n", "L 247.126188 136.32863 \n", "L 248.346082 135.761256 \n", "L 248.956029 135.694968 \n", "L 249.565976 135.201385 \n", "L 250.785871 135.073772 \n", "L 251.395818 135.222348 \n", "L 252.005765 135.581038 \n", "L 253.225659 135.033198 \n", "L 254.445554 135.32585 \n", "L 255.665448 136.028813 \n", "L 256.885342 135.900003 \n", "L 257.495289 135.631133 \n", "L 258.105237 135.772805 \n", "L 259.325131 135.64721 \n", "L 259.935078 135.382502 \n", "L 260.545025 135.523179 \n", "L 261.154972 135.461738 \n", "L 261.76492 135.601296 \n", "L 262.374867 135.140002 \n", "L 263.594761 135.418528 \n", "L 264.204708 135.160183 \n", "L 264.814655 135.100973 \n", "L 265.424603 135.436044 \n", "L 266.03455 135.179997 \n", "L 266.644497 135.317034 \n", "L 267.254444 135.258075 \n", "L 267.864391 135.39405 \n", "L 268.474338 135.141199 \n", "L 269.694233 135.025714 \n", "L 270.30418 134.776204 \n", "L 273.353916 134.497891 \n", "L 273.963863 134.821047 \n", "L 274.57381 134.765583 \n", "L 275.183757 134.334782 \n", "L 275.793704 134.093751 \n", "L 277.013599 133.988279 \n", "L 277.623546 133.378969 \n", "L 278.233493 133.143431 \n", "L 278.84344 133.27846 \n", "L 279.453387 133.228635 \n", "L 280.063335 133.546229 \n", "L 280.673282 133.678979 \n", "L 281.893176 133.5782 \n", "L 282.503123 133.709743 \n", "L 284.332965 133.559998 \n", "L 284.942912 133.869636 \n", "L 285.552859 133.81956 \n", "L 286.772753 134.076264 \n", "L 287.382701 133.848489 \n", "L 287.992648 134.153115 \n", "L 288.602595 134.279512 \n", "L 289.822489 134.881464 \n", "L 290.432436 134.829727 \n", "L 291.042384 134.603614 \n", "L 291.652331 134.901288 \n", "L 292.262278 135.023648 \n", "L 292.872225 134.625562 \n", "L 293.482172 134.920802 \n", "L 294.702067 134.819048 \n", "L 295.921961 135.060311 \n", "L 297.141855 134.958895 \n", "L 297.751802 134.738952 \n", "L 300.191591 135.213571 \n", "L 300.801538 134.99581 \n", "L 301.411485 134.946188 \n", "L 302.021433 134.730232 \n", "L 302.63138 134.681537 \n", "L 303.241327 134.798819 \n", "L 303.851274 134.750201 \n", "L 304.461221 134.866719 \n", "L 306.291063 134.721812 \n", "L 306.90101 134.510782 \n", "L 307.510957 134.626431 \n", "L 308.120904 134.416694 \n", "L 309.340799 134.969922 \n", "L 309.950746 134.922086 \n", "L 311.17064 135.14792 \n", "L 312.390534 135.052316 \n", "L 313.000482 134.686433 \n", "L 314.220376 135.227533 \n", "L 316.050217 135.557477 \n", "L 316.660165 135.352587 \n", "L 317.270112 135.461738 \n", "L 317.880059 135.414212 \n", "L 318.490006 135.678457 \n", "L 319.099953 135.475247 \n", "L 319.7099 135.428039 \n", "L 320.319848 135.226394 \n", "L 320.929795 135.179997 \n", "L 321.539742 134.979906 \n", "L 322.149689 134.934311 \n", "L 322.759636 135.042129 \n", "L 323.979531 134.951296 \n", "L 324.589478 135.058313 \n", "L 325.199425 135.013065 \n", "L 326.419319 135.225312 \n", "L 327.029266 135.029297 \n", "L 327.639214 135.285237 \n", "L 328.249161 135.239999 \n", "L 328.859108 134.895658 \n", "L 330.079002 134.807586 \n", "L 330.688949 134.61523 \n", "L 331.298897 134.868602 \n", "L 331.908844 134.824928 \n", "L 332.518791 134.929068 \n", "L 334.348632 134.798819 \n", "L 334.95858 134.902079 \n", "L 335.568527 135.150807 \n", "L 336.178474 134.96157 \n", "L 336.788421 134.918475 \n", "L 338.008315 135.122138 \n", "L 338.618263 134.934649 \n", "L 339.22821 135.035998 \n", "L 339.838157 134.849532 \n", "L 340.448104 134.95062 \n", "L 341.058051 135.194307 \n", "L 341.667998 135.294179 \n", "L 342.277946 135.25121 \n", "L 342.887893 134.924211 \n", "L 343.49784 135.024024 \n", "L 344.107787 135.264892 \n", "L 344.717734 135.222348 \n", "L 345.937629 134.856696 \n", "L 346.547576 134.955583 \n", "L 347.157523 135.193998 \n", "L 347.76747 135.012408 \n", "L 348.377417 135.249674 \n", "L 349.597312 135.166121 \n", "L 350.207259 135.263077 \n", "L 351.427153 134.904252 \n", "L 352.0371 135.138727 \n", "L 352.647048 135.23491 \n", "L 353.256995 135.467696 \n", "L 353.866942 135.426069 \n", "L 354.476889 135.657474 \n", "L 355.086836 135.751765 \n", "L 355.696783 135.57396 \n", "L 358.136572 135.947896 \n", "L 358.746519 135.90597 \n", "L 359.356466 135.730058 \n", "L 360.576361 135.647625 \n", "L 361.186308 135.873333 \n", "L 361.796255 135.965045 \n", "L 362.406202 135.923602 \n", "L 363.016149 136.014841 \n", "L 363.626097 135.973465 \n", "L 364.236044 136.064231 \n", "L 364.845991 136.286337 \n", "L 365.455938 135.850342 \n", "L 366.675832 136.29273 \n", "L 367.28578 136.251286 \n", "L 367.895727 135.949258 \n", "L 369.115621 136.12797 \n", "L 369.725568 136.087203 \n", "L 369.725568 136.087203 \n", "\" clip-path=\"url(#p939fb0ebd7)\" style=\"fill: none; stroke: #d62728; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_19\">\n", "    <path d=\"M 65.361932 115.739996 \n", "L 65.971879 148.139994 \n", "L 66.581826 158.939994 \n", "L 67.191773 148.139994 \n", "L 67.80172 154.619998 \n", "L 68.411668 158.939994 \n", "L 69.021615 152.768566 \n", "L 69.631562 148.139994 \n", "L 70.241509 151.739994 \n", "L 70.851456 141.660001 \n", "L 72.071351 158.939994 \n", "L 72.681298 145.647687 \n", "L 73.291245 143.511423 \n", "L 73.901192 145.979997 \n", "L 74.511139 144.089998 \n", "L 75.121086 150.045877 \n", "L 76.340981 139.613682 \n", "L 76.950928 131.94 \n", "L 77.560875 131.168567 \n", "L 78.170822 127.521813 \n", "L 78.780769 129.826959 \n", "L 79.390717 134.639997 \n", "L 80.610611 138.170764 \n", "L 81.220558 139.740001 \n", "L 81.830505 138.882852 \n", "L 82.440452 133.615858 \n", "L 83.0504 133.019999 \n", "L 83.660347 130.37226 \n", "L 85.490188 129.081172 \n", "L 86.100135 130.551426 \n", "L 86.710083 133.739995 \n", "L 87.929977 136.203151 \n", "L 89.759819 134.705851 \n", "L 90.369766 135.797138 \n", "L 90.979713 135.330697 \n", "L 91.58966 133.412726 \n", "L 92.199607 133.019999 \n", "L 92.809554 134.053041 \n", "L 93.419502 132.284679 \n", "L 94.639396 131.60939 \n", "L 95.249343 132.588001 \n", "L 95.85929 129.716466 \n", "L 96.469237 129.447692 \n", "L 97.079185 127.966411 \n", "L 97.689132 130.139995 \n", "L 98.909026 129.62571 \n", "L 99.518973 128.245266 \n", "L 100.738868 130.017963 \n", "L 102.568709 129.327099 \n", "L 103.178656 126.02571 \n", "L 103.788603 126.8775 \n", "L 105.618445 126.378809 \n", "L 106.228392 128.128235 \n", "L 106.838339 127.948693 \n", "L 107.448286 126.848571 \n", "L 108.058234 127.604786 \n", "L 108.668181 129.240003 \n", "L 109.278128 129.942734 \n", "L 109.888075 131.50216 \n", "L 110.498022 132.155994 \n", "L 111.717917 131.729606 \n", "L 112.327864 129.863072 \n", "L 112.937811 130.504556 \n", "L 113.547758 129.510002 \n", "L 115.3776 131.354453 \n", "L 115.987547 132.711424 \n", "L 116.597494 130.987054 \n", "L 117.817388 133.615858 \n", "L 118.427335 131.94 \n", "L 119.64723 133.019999 \n", "L 120.257177 134.254281 \n", "L 120.867124 134.757385 \n", "L 122.087018 134.352762 \n", "L 122.696966 132.110524 \n", "L 123.306913 131.265001 \n", "L 124.526807 130.94816 \n", "L 126.356649 132.421185 \n", "L 126.966596 132.257642 \n", "L 127.576543 133.355534 \n", "L 128.796437 133.019999 \n", "L 130.016332 135.119435 \n", "L 130.626279 134.939998 \n", "L 131.846173 135.769087 \n", "L 132.45612 136.756215 \n", "L 133.066067 135.989996 \n", "L 133.676015 136.957696 \n", "L 134.895909 137.71565 \n", "L 137.335698 136.97697 \n", "L 137.945645 137.339995 \n", "L 138.555592 137.161485 \n", "L 140.995381 138.549596 \n", "L 141.605328 137.854281 \n", "L 142.215275 138.700625 \n", "L 144.045116 138.170764 \n", "L 144.655064 137.50488 \n", "L 145.265011 137.339995 \n", "L 146.484905 135.083283 \n", "L 147.094852 135.419996 \n", "L 147.704799 134.798819 \n", "L 148.314747 133.713721 \n", "L 149.534641 134.387485 \n", "L 150.144588 133.328574 \n", "L 150.754535 133.663404 \n", "L 151.97443 135.225312 \n", "L 152.584377 135.54 \n", "L 153.194324 134.956549 \n", "L 153.804271 134.824928 \n", "L 154.414218 135.135918 \n", "L 155.024165 135.004867 \n", "L 155.634113 135.310468 \n", "L 156.24406 136.044002 \n", "L 156.854007 135.480394 \n", "L 158.683848 136.358178 \n", "L 159.293796 135.807093 \n", "L 159.903743 135.678457 \n", "L 160.51369 135.964205 \n", "L 161.123637 136.65645 \n", "L 161.733584 136.117358 \n", "L 162.343531 136.394996 \n", "L 162.953479 135.461738 \n", "L 164.173373 136.809941 \n", "L 164.78332 136.681459 \n", "L 165.393267 135.37636 \n", "L 166.613162 135.917248 \n", "L 167.223109 136.568571 \n", "L 167.833056 136.445323 \n", "L 169.05295 136.961047 \n", "L 169.662897 136.460927 \n", "L 170.882792 136.967584 \n", "L 171.492739 136.846286 \n", "L 172.102686 136.358178 \n", "L 172.712633 136.973899 \n", "L 173.32258 136.85461 \n", "L 173.932528 137.098654 \n", "L 174.542475 136.259996 \n", "L 175.152422 136.146625 \n", "L 175.762369 136.390544 \n", "L 176.372316 136.277705 \n", "L 176.982263 135.81391 \n", "L 178.812105 135.491875 \n", "L 179.422052 136.076166 \n", "L 180.641946 136.544209 \n", "L 182.471788 136.220831 \n", "L 183.081735 136.449272 \n", "L 183.691682 135.678457 \n", "L 184.301629 135.246121 \n", "L 184.911577 135.147109 \n", "L 186.131471 135.603314 \n", "L 186.741418 135.179997 \n", "L 187.961312 136.270695 \n", "L 188.57126 136.169558 \n", "L 189.791154 135.338046 \n", "L 190.401101 135.557477 \n", "L 191.011048 136.087821 \n", "L 191.620995 135.678457 \n", "L 192.230943 135.583056 \n", "L 192.84089 135.179997 \n", "L 193.450837 135.08787 \n", "L 194.060784 134.690942 \n", "L 194.670731 134.601972 \n", "L 195.280678 133.908221 \n", "L 196.500573 133.739995 \n", "L 198.330414 134.381092 \n", "L 198.940361 134.296361 \n", "L 199.550309 134.505606 \n", "L 200.160256 134.129188 \n", "L 200.770203 134.337312 \n", "L 201.38015 133.964998 \n", "L 201.990097 134.460001 \n", "L 202.600044 134.663896 \n", "L 203.209992 134.580526 \n", "L 203.819939 134.213678 \n", "L 204.429886 134.133012 \n", "L 205.039833 133.7713 \n", "L 205.64978 134.254281 \n", "L 206.259727 134.174484 \n", "L 206.869675 133.817252 \n", "L 207.479622 134.016918 \n", "L 208.699516 133.862037 \n", "L 209.309463 134.058989 \n", "L 209.91941 133.437474 \n", "L 210.529358 133.363432 \n", "L 211.139305 133.829998 \n", "L 211.749252 133.754933 \n", "L 212.359199 133.412726 \n", "L 212.969146 133.339997 \n", "L 213.579093 133.533445 \n", "L 214.189041 133.460813 \n", "L 216.018882 132.46258 \n", "L 216.628829 132.395423 \n", "L 217.238776 132.847196 \n", "L 217.848724 132.520873 \n", "L 218.458671 132.454286 \n", "L 219.678565 132.832915 \n", "L 220.288512 133.274115 \n", "L 220.898459 133.458746 \n", "L 221.508407 133.389803 \n", "L 222.118354 133.572561 \n", "L 223.338248 133.435379 \n", "L 224.558142 131.816336 \n", "L 225.16809 131.755204 \n", "L 225.778037 131.449091 \n", "L 226.387984 131.878868 \n", "L 227.607878 131.757976 \n", "L 228.217825 132.181785 \n", "L 228.827773 132.120663 \n", "L 229.43772 131.339998 \n", "L 230.047667 131.043319 \n", "L 231.267561 131.880655 \n", "L 231.877508 131.58525 \n", "L 232.487456 131.527633 \n", "L 233.097403 131.94 \n", "L 233.70735 132.115449 \n", "L 234.927244 131.533542 \n", "L 235.537191 131.708566 \n", "L 237.97698 131.48366 \n", "L 238.586927 131.201049 \n", "L 239.196874 131.373563 \n", "L 239.806822 131.319093 \n", "L 240.416769 131.039998 \n", "L 241.636663 131.381374 \n", "L 242.856557 132.161913 \n", "L 243.466505 131.88471 \n", "L 244.076452 132.050203 \n", "L 248.346082 131.670898 \n", "L 248.956029 131.403573 \n", "L 249.565976 131.565745 \n", "L 250.175923 131.51368 \n", "L 250.785871 131.886883 \n", "L 252.005765 131.781691 \n", "L 252.615712 131.94 \n", "L 253.225659 132.306984 \n", "L 255.055501 131.524611 \n", "L 256.275395 132.249551 \n", "L 256.885342 132.197138 \n", "L 258.105237 132.502141 \n", "L 258.715184 132.449429 \n", "L 259.935078 132.749999 \n", "L 260.545025 132.49514 \n", "L 261.154972 132.040615 \n", "L 261.76492 131.990153 \n", "L 262.374867 132.139994 \n", "L 263.594761 132.834479 \n", "L 264.814655 132.730243 \n", "L 265.424603 132.875556 \n", "L 266.644497 133.555103 \n", "L 267.254444 133.501445 \n", "L 267.864391 133.642702 \n", "L 269.084286 134.309551 \n", "L 270.30418 134.583916 \n", "L 271.524074 134.472737 \n", "L 272.134021 134.227061 \n", "L 272.743969 134.362872 \n", "L 273.353916 133.929474 \n", "L 273.963863 134.065362 \n", "L 274.57381 134.388836 \n", "L 275.793704 134.281037 \n", "L 276.403652 134.601094 \n", "L 277.013599 134.360689 \n", "L 277.623546 134.493005 \n", "L 278.233493 134.439424 \n", "L 278.84344 134.570764 \n", "L 280.063335 134.096937 \n", "L 280.673282 134.228132 \n", "L 281.283229 134.176058 \n", "L 281.893176 133.760224 \n", "L 284.332965 133.559998 \n", "L 284.942912 133.151629 \n", "L 285.552859 133.103533 \n", "L 286.162806 133.412726 \n", "L 286.772753 133.542193 \n", "L 287.382701 133.848489 \n", "L 287.992648 133.621961 \n", "L 289.212542 133.524783 \n", "L 290.432436 133.778918 \n", "L 291.042384 133.555634 \n", "L 291.652331 133.50774 \n", "L 292.262278 133.286378 \n", "L 294.092119 133.146386 \n", "L 294.702067 133.443973 \n", "L 295.312014 133.225709 \n", "L 295.921961 133.350551 \n", "L 297.141855 133.258105 \n", "L 297.751802 133.381885 \n", "L 298.36175 132.997443 \n", "L 298.971697 132.783747 \n", "L 299.581644 132.907787 \n", "L 300.191591 132.863312 \n", "L 300.801538 132.986512 \n", "L 301.411485 132.775047 \n", "L 302.63138 133.019999 \n", "L 303.241327 132.975804 \n", "L 304.461221 133.547629 \n", "L 305.071168 133.502439 \n", "L 306.291063 133.085457 \n", "L 308.120904 133.442254 \n", "L 309.340799 133.35396 \n", "L 310.560693 132.944963 \n", "L 311.17064 133.062775 \n", "L 311.780587 132.700001 \n", "L 312.390534 132.17941 \n", "L 313.610429 132.734115 \n", "L 314.220376 132.851001 \n", "L 314.830323 132.651219 \n", "L 315.44027 132.29474 \n", "L 316.050217 132.254562 \n", "L 316.660165 132.528376 \n", "L 317.270112 132.487821 \n", "L 317.880059 132.759761 \n", "L 318.490006 132.407309 \n", "L 320.319848 132.287972 \n", "L 320.929795 132.557141 \n", "L 321.539742 132.517194 \n", "L 322.759636 132.744254 \n", "L 323.369583 132.704153 \n", "L 323.979531 132.35929 \n", "L 325.199425 132.58496 \n", "L 325.809372 132.545602 \n", "L 326.419319 132.35538 \n", "L 327.029266 132.015345 \n", "L 327.639214 132.278277 \n", "L 328.249161 132.240001 \n", "L 330.079002 133.019999 \n", "L 330.688949 133.128986 \n", "L 331.298897 133.089194 \n", "L 331.908844 132.605749 \n", "L 332.518791 132.567338 \n", "L 333.128738 132.676363 \n", "L 333.738685 132.637961 \n", "L 334.348632 132.892936 \n", "L 334.95858 132.415392 \n", "L 335.568527 132.37784 \n", "L 336.178474 132.19483 \n", "L 336.788421 132.448521 \n", "L 337.398368 132.266168 \n", "L 338.008315 132.229283 \n", "L 338.618263 132.336879 \n", "L 339.838157 132.263281 \n", "L 340.448104 131.94 \n", "L 341.667998 132.154092 \n", "L 342.277946 132.118017 \n", "L 342.887893 131.94 \n", "L 344.107787 131.86926 \n", "L 344.717734 131.692943 \n", "L 345.327681 131.799129 \n", "L 347.157523 131.695077 \n", "L 347.76747 131.521028 \n", "L 348.987365 131.731412 \n", "L 350.817206 131.629127 \n", "L 352.0371 131.836816 \n", "L 353.256995 131.768752 \n", "L 353.866942 132.008354 \n", "L 354.476889 131.837686 \n", "L 355.086836 131.803861 \n", "L 355.696783 132.04189 \n", "L 357.526625 131.94 \n", "L 358.136572 131.7716 \n", "L 358.746519 131.738335 \n", "L 361.186308 132.139994 \n", "L 362.406202 131.807211 \n", "L 363.626097 131.741628 \n", "L 364.845991 131.413171 \n", "L 366.065885 131.612065 \n", "L 367.28578 131.548065 \n", "L 369.115621 131.8426 \n", "L 369.725568 131.551193 \n", "L 369.725568 131.551193 \n", "\" clip-path=\"url(#p939fb0ebd7)\" style=\"fill: none; stroke: #9467bd; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_20\">\n", "    <path d=\"M 65.361932 115.739996 \n", "L 65.971879 50.939989 \n", "L 66.581826 72.539988 \n", "L 67.191773 67.139993 \n", "L 67.80172 76.860003 \n", "L 68.411668 94.139997 \n", "L 69.021615 87.968568 \n", "L 69.631562 91.439999 \n", "L 70.241509 94.139997 \n", "L 71.461403 109.849092 \n", "L 72.071351 110.340001 \n", "L 72.681298 120.724611 \n", "L 73.291245 115.739996 \n", "L 73.901192 115.739996 \n", "L 74.511139 123.839998 \n", "L 75.731034 130.139995 \n", "L 76.950928 135.179997 \n", "L 77.560875 131.168567 \n", "L 78.170822 136.358178 \n", "L 78.780769 135.461738 \n", "L 79.390717 129.240003 \n", "L 80.000664 133.883994 \n", "L 80.610611 135.678457 \n", "L 81.830505 143.511423 \n", "L 82.440452 147.022752 \n", "L 83.0504 145.979997 \n", "L 83.660347 149.185164 \n", "L 85.490188 140.516465 \n", "L 86.710083 139.14 \n", "L 87.32003 142.01027 \n", "L 87.929977 141.318943 \n", "L 88.539924 139.001533 \n", "L 89.149871 138.419994 \n", "L 89.759819 141.027806 \n", "L 90.369766 140.425709 \n", "L 90.979713 141.3586 \n", "L 91.58966 143.721817 \n", "L 92.199607 143.100002 \n", "L 92.809554 139.687821 \n", "L 95.249343 137.772002 \n", "L 95.85929 139.881171 \n", "L 96.469237 138.170764 \n", "L 98.299079 140.48182 \n", "L 98.909026 142.354283 \n", "L 99.518973 141.887369 \n", "L 100.738868 138.804407 \n", "L 101.348815 138.419994 \n", "L 103.178656 134.254281 \n", "L 103.788603 135.989996 \n", "L 104.398551 135.678457 \n", "L 105.618445 137.017612 \n", "L 106.228392 135.751765 \n", "L 108.058234 137.644225 \n", "L 109.278128 137.044108 \n", "L 109.888075 137.631894 \n", "L 110.498022 137.339995 \n", "L 111.107969 138.761052 \n", "L 112.937811 140.347592 \n", "L 113.547758 140.040002 \n", "L 114.157705 138.14 \n", "L 114.767652 137.866824 \n", "L 115.3776 138.380965 \n", "L 115.987547 137.339995 \n", "L 117.207441 138.344649 \n", "L 117.817388 137.339995 \n", "L 118.427335 137.094541 \n", "L 119.64723 135.179997 \n", "L 120.257177 133.542193 \n", "L 120.867124 132.644344 \n", "L 122.087018 133.663404 \n", "L 123.306913 133.289999 \n", "L 123.91686 131.772991 \n", "L 125.746701 131.291999 \n", "L 126.356649 129.854845 \n", "L 126.966596 130.35176 \n", "L 127.576543 128.951647 \n", "L 128.18649 128.824613 \n", "L 128.796437 128.082852 \n", "L 129.406384 128.57773 \n", "L 130.016332 129.668968 \n", "L 130.626279 130.139995 \n", "L 131.236226 130.007892 \n", "L 131.846173 130.467274 \n", "L 132.45612 130.334592 \n", "L 133.066067 130.78286 \n", "L 133.676015 130.649733 \n", "L 134.285962 129.3821 \n", "L 134.895909 128.699993 \n", "L 135.505856 129.14689 \n", "L 136.115803 129.032303 \n", "L 136.72575 130.017963 \n", "L 137.335698 130.442516 \n", "L 138.555592 130.199505 \n", "L 139.165539 129.549833 \n", "L 140.385433 130.37226 \n", "L 140.995381 130.2552 \n", "L 141.605328 129.62571 \n", "L 142.215275 129.516375 \n", "L 142.825222 130.421244 \n", "L 143.435169 130.307439 \n", "L 144.045116 130.693841 \n", "L 144.655064 131.569009 \n", "L 145.265011 131.449091 \n", "L 146.484905 132.181785 \n", "L 147.094852 133.019999 \n", "L 147.704799 133.369409 \n", "L 148.924694 133.113913 \n", "L 149.534641 132.522727 \n", "L 150.144588 132.402858 \n", "L 150.754535 133.203829 \n", "L 151.364482 133.080841 \n", "L 151.97443 133.412726 \n", "L 152.584377 132.840002 \n", "L 153.194324 132.722065 \n", "L 153.804271 133.049585 \n", "L 154.414218 132.93183 \n", "L 155.024165 131.94 \n", "L 155.634113 131.831274 \n", "L 156.24406 131.291999 \n", "L 156.854007 131.618148 \n", "L 157.463954 131.51368 \n", "L 159.293796 132.46258 \n", "L 159.903743 132.35538 \n", "L 161.123637 131.32481 \n", "L 161.733584 131.226792 \n", "L 162.953479 131.839375 \n", "L 163.563426 130.940001 \n", "L 164.173373 131.244289 \n", "L 164.78332 131.149757 \n", "L 166.613162 132.037004 \n", "L 167.223109 131.554283 \n", "L 168.443003 131.368232 \n", "L 169.662897 132.693483 \n", "L 170.272845 132.970058 \n", "L 171.492739 132.773145 \n", "L 172.102686 133.412726 \n", "L 172.712633 133.678979 \n", "L 173.32258 134.306288 \n", "L 173.932528 134.202564 \n", "L 174.542475 134.460001 \n", "L 175.152422 133.998562 \n", "L 175.762369 133.898237 \n", "L 176.372316 133.090816 \n", "L 176.982263 133.700869 \n", "L 177.592211 133.954048 \n", "L 178.202158 134.552901 \n", "L 178.812105 134.452295 \n", "L 180.031999 134.939998 \n", "L 180.641946 135.521055 \n", "L 181.251894 135.417485 \n", "L 181.861841 134.977502 \n", "L 182.471788 134.20632 \n", "L 183.081735 134.44515 \n", "L 183.691682 135.013847 \n", "L 184.301629 134.915511 \n", "L 185.521524 135.37636 \n", "L 186.131471 135.277686 \n", "L 186.741418 135.827998 \n", "L 187.351365 135.728059 \n", "L 187.961312 135.308315 \n", "L 188.57126 135.850342 \n", "L 189.181207 136.069407 \n", "L 189.791154 135.654143 \n", "L 190.401101 135.872039 \n", "L 191.011048 135.148692 \n", "L 191.620995 135.055387 \n", "L 192.230943 135.583056 \n", "L 192.84089 135.488572 \n", "L 193.450837 135.702085 \n", "L 194.060784 135.60792 \n", "L 194.670731 135.818873 \n", "L 196.500573 135.54 \n", "L 197.720467 135.95284 \n", "L 198.330414 135.268764 \n", "L 199.550309 135.092031 \n", "L 200.770203 135.499638 \n", "L 201.38015 135.411431 \n", "L 201.990097 135.611995 \n", "L 203.209992 135.436913 \n", "L 205.039833 136.025211 \n", "L 205.64978 135.656885 \n", "L 206.259727 136.12965 \n", "L 207.479622 135.95538 \n", "L 209.309463 136.519741 \n", "L 209.91941 136.97697 \n", "L 211.749252 136.712609 \n", "L 212.359199 136.893716 \n", "L 213.579093 136.720324 \n", "L 214.189041 137.163668 \n", "L 214.798988 137.076581 \n", "L 216.018882 137.427092 \n", "L 217.238776 137.253593 \n", "L 218.458671 137.597143 \n", "L 219.068618 138.022999 \n", "L 219.678565 138.190395 \n", "L 220.288512 137.848236 \n", "L 220.898459 137.761873 \n", "L 221.508407 137.42404 \n", "L 222.728301 137.756987 \n", "L 223.948195 138.581374 \n", "L 224.558142 138.246872 \n", "L 225.16809 137.668519 \n", "L 226.387984 137.992071 \n", "L 227.607878 137.82539 \n", "L 228.217825 137.501191 \n", "L 228.827773 137.420294 \n", "L 229.43772 137.579994 \n", "L 230.047667 137.977635 \n", "L 230.657614 138.13412 \n", "L 231.267561 138.052083 \n", "L 232.487456 138.361093 \n", "L 233.097403 138.279133 \n", "L 234.317297 138.58316 \n", "L 234.927244 138.965806 \n", "L 235.537191 139.114286 \n", "L 236.147139 139.492307 \n", "L 237.367033 138.408552 \n", "L 237.97698 138.328726 \n", "L 238.586927 138.022101 \n", "L 239.196874 137.491043 \n", "L 240.416769 137.339995 \n", "L 241.026716 137.489479 \n", "L 241.636663 137.861378 \n", "L 242.24661 138.008042 \n", "L 242.856557 137.26603 \n", "L 244.076452 137.560402 \n", "L 244.686399 137.04712 \n", "L 246.51624 136.832613 \n", "L 247.736135 137.555999 \n", "L 249.565976 137.981585 \n", "L 250.175923 137.482102 \n", "L 251.395818 136.916466 \n", "L 254.445554 136.576006 \n", "L 256.885342 137.134285 \n", "L 257.495289 137.476704 \n", "L 258.105237 137.408137 \n", "L 258.715184 137.747544 \n", "L 259.935078 138.014994 \n", "L 260.545025 137.541872 \n", "L 261.154972 137.675404 \n", "L 261.76492 137.406872 \n", "L 262.374867 137.539999 \n", "L 262.984814 137.273533 \n", "L 264.814655 137.076581 \n", "L 265.424603 137.208693 \n", "L 266.644497 137.078975 \n", "L 267.254444 136.81952 \n", "L 267.864391 136.756215 \n", "L 269.084286 137.017612 \n", "L 269.694233 136.954288 \n", "L 270.30418 137.275899 \n", "L 270.914127 136.828751 \n", "L 271.524074 137.148846 \n", "L 272.134021 137.085879 \n", "L 272.743969 136.643221 \n", "L 273.353916 136.771578 \n", "L 273.963863 137.0881 \n", "L 275.793704 137.464856 \n", "L 276.403652 137.402247 \n", "L 277.013599 137.712415 \n", "L 277.623546 137.463784 \n", "L 278.233493 137.772002 \n", "L 278.84344 137.893841 \n", "L 279.453387 138.199085 \n", "L 280.063335 137.401185 \n", "L 281.283229 138.009297 \n", "L 283.723018 138.483173 \n", "L 284.332965 138.419994 \n", "L 284.942912 138.716171 \n", "L 285.552859 138.4737 \n", "L 287.992648 138.93344 \n", "L 288.602595 138.870241 \n", "L 289.822489 139.096094 \n", "L 290.432436 138.507564 \n", "L 292.872225 138.957106 \n", "L 293.482172 139.240798 \n", "L 294.702067 139.11613 \n", "L 295.312014 139.397138 \n", "L 295.921961 139.5057 \n", "L 296.531908 139.443158 \n", "L 297.141855 139.721105 \n", "L 297.751802 139.31906 \n", "L 298.36175 139.257493 \n", "L 298.971697 139.365003 \n", "L 299.581644 139.303639 \n", "L 300.191591 139.578341 \n", "L 301.411485 139.789478 \n", "L 302.021433 139.727661 \n", "L 302.63138 139.333843 \n", "L 303.241327 139.439228 \n", "L 303.851274 139.213472 \n", "L 304.461221 138.659085 \n", "L 305.071168 138.929848 \n", "L 305.681116 139.035184 \n", "L 306.90101 139.570732 \n", "L 307.510957 139.673665 \n", "L 308.120904 139.613682 \n", "L 308.730851 139.877994 \n", "L 309.340799 139.817799 \n", "L 309.950746 139.596717 \n", "L 311.780587 139.420003 \n", "L 312.390534 139.680888 \n", "L 313.000482 139.303639 \n", "L 313.610429 139.245877 \n", "L 314.830323 139.44732 \n", "L 315.44027 139.074301 \n", "L 316.050217 139.017668 \n", "L 316.660165 138.804407 \n", "L 317.270112 138.748692 \n", "L 317.880059 138.849394 \n", "L 318.490006 138.638074 \n", "L 319.099953 138.738563 \n", "L 319.7099 138.683544 \n", "L 320.319848 138.783434 \n", "L 320.929795 138.728569 \n", "L 322.759636 139.484679 \n", "L 323.369583 139.428674 \n", "L 323.979531 139.677885 \n", "L 324.589478 139.773797 \n", "L 325.199425 139.717513 \n", "L 325.809372 139.510093 \n", "L 327.029266 139.700924 \n", "L 327.639214 139.645334 \n", "L 328.249161 139.740001 \n", "L 331.298897 139.465396 \n", "L 332.518791 139.947739 \n", "L 333.128738 140.040002 \n", "L 333.738685 139.984896 \n", "L 334.348632 139.636827 \n", "L 334.95858 139.729167 \n", "L 336.178474 139.621349 \n", "L 337.398368 139.804426 \n", "L 338.008315 140.040002 \n", "L 338.618263 140.130198 \n", "L 341.667998 139.861579 \n", "L 342.277946 139.950984 \n", "L 342.887893 140.182099 \n", "L 344.717734 140.022351 \n", "L 345.327681 140.110433 \n", "L 345.937629 139.917005 \n", "L 346.547576 140.145194 \n", "L 348.377417 140.405808 \n", "L 348.987365 140.352874 \n", "L 349.597312 140.438928 \n", "L 350.207259 140.109226 \n", "L 352.0371 140.36675 \n", "L 353.866942 140.210883 \n", "L 355.086836 140.380336 \n", "L 355.696783 140.328676 \n", "L 356.306731 140.14167 \n", "L 356.916678 140.361294 \n", "L 357.526625 140.310002 \n", "L 358.136572 140.124202 \n", "L 358.746519 140.073605 \n", "L 359.356466 140.291549 \n", "L 359.966414 140.240827 \n", "L 360.576361 140.323916 \n", "L 361.796255 139.424599 \n", "L 362.406202 139.641636 \n", "L 364.845991 139.974148 \n", "L 365.455938 139.92499 \n", "L 366.065885 139.744858 \n", "L 366.675832 139.827272 \n", "L 367.895727 140.251873 \n", "L 368.505674 140.332771 \n", "L 369.115621 140.02377 \n", "L 369.725568 140.104794 \n", "L 369.725568 140.104794 \n", "\" clip-path=\"url(#p939fb0ebd7)\" style=\"fill: none; stroke: #8c564b; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_21\">\n", "    <path d=\"M 50.14375 137.123998 \n", "L 384.94375 137.123998 \n", "\" clip-path=\"url(#p939fb0ebd7)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #000000; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"patch_3\">\n", "    <path d=\"M 50.14375 256.68 \n", "L 50.14375 7.2 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_4\">\n", "    <path d=\"M 384.94375 256.68 \n", "L 384.94375 7.2 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_5\">\n", "    <path d=\"M 50.14375 256.68 \n", "L 384.94375 256.68 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_6\">\n", "    <path d=\"M 50.14375 7.2 \n", "L 384.94375 7.2 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"legend_1\">\n", "    <g id=\"patch_7\">\n", "     <path d=\"M 302.089063 103.26875 \n", "L 377.94375 103.26875 \n", "Q 379.94375 103.26875 379.94375 101.26875 \n", "L 379.94375 14.2 \n", "Q 379.94375 12.2 377.94375 12.2 \n", "L 302.089063 12.2 \n", "Q 300.089063 12.2 300.089063 14.2 \n", "L 300.089063 101.26875 \n", "Q 300.089063 103.26875 302.089063 103.26875 \n", "z\n", "\" style=\"fill: #ffffff; opacity: 0.8; stroke: #cccccc; stroke-linejoin: miter\"/>\n", "    </g>\n", "    <g id=\"line2d_22\">\n", "     <path d=\"M 304.089063 20.298437 \n", "L 314.089063 20.298437 \n", "L 324.089063 20.298437 \n", "\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "    </g>\n", "    <g id=\"text_17\">\n", "     <!-- P(die=1) -->\n", "     <g transform=\"translate(332.089063 23.798437)scale(0.1 -0.1)\">\n", "      <defs>\n", "       <path id=\"DejaVuSans-50\" d=\"M 1259 4147 \n", "L 1259 2394 \n", "L 2053 2394 \n", "Q 2494 2394 2734 2622 \n", "Q 2975 2850 2975 3272 \n", "Q 2975 3691 2734 3919 \n", "Q 2494 4147 2053 4147 \n", "L 1259 4147 \n", "z\n", "M 628 4666 \n", "L 2053 4666 \n", "Q 2838 4666 3239 4311 \n", "Q 3641 3956 3641 3272 \n", "Q 3641 2581 3239 2228 \n", "Q 2838 1875 2053 1875 \n", "L 1259 1875 \n", "L 1259 0 \n", "L 628 0 \n", "L 628 4666 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-28\" d=\"M 1984 4856 \n", "Q 1566 4138 1362 3434 \n", "Q 1159 2731 1159 2009 \n", "Q 1159 1288 1364 580 \n", "Q 1569 -128 1984 -844 \n", "L 1484 -844 \n", "Q 1016 -109 783 600 \n", "Q 550 1309 550 2009 \n", "Q 550 2706 781 3412 \n", "Q 1013 4119 1484 4856 \n", "L 1984 4856 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-3d\" d=\"M 678 2906 \n", "L 4684 2906 \n", "L 4684 2381 \n", "L 678 2381 \n", "L 678 2906 \n", "z\n", "M 678 1631 \n", "L 4684 1631 \n", "L 4684 1100 \n", "L 678 1100 \n", "L 678 1631 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-29\" d=\"M 513 4856 \n", "L 1013 4856 \n", "Q 1481 4119 1714 3412 \n", "Q 1947 2706 1947 2009 \n", "Q 1947 1309 1714 600 \n", "Q 1481 -109 1013 -844 \n", "L 513 -844 \n", "Q 928 -128 1133 580 \n", "Q 1338 1288 1338 2009 \n", "Q 1338 2731 1133 3434 \n", "Q 928 4138 513 4856 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "      </defs>\n", "      <use xlink:href=\"#DejaVuSans-50\"/>\n", "      <use xlink:href=\"#DejaVuSans-28\" x=\"60.302734\"/>\n", "      <use xlink:href=\"#DejaVuSans-64\" x=\"99.316406\"/>\n", "      <use xlink:href=\"#DejaVuSans-69\" x=\"162.792969\"/>\n", "      <use xlink:href=\"#DejaVuSans-65\" x=\"190.576172\"/>\n", "      <use xlink:href=\"#DejaVuSans-3d\" x=\"252.099609\"/>\n", "      <use xlink:href=\"#DejaVuSans-31\" x=\"335.888672\"/>\n", "      <use xlink:href=\"#DejaVuSans-29\" x=\"399.511719\"/>\n", "     </g>\n", "    </g>\n", "    <g id=\"line2d_23\">\n", "     <path d=\"M 304.089063 34.976562 \n", "L 314.089063 34.976562 \n", "L 324.089063 34.976562 \n", "\" style=\"fill: none; stroke: #ff7f0e; stroke-width: 1.5; stroke-linecap: square\"/>\n", "    </g>\n", "    <g id=\"text_18\">\n", "     <!-- P(die=2) -->\n", "     <g transform=\"translate(332.089063 38.476562)scale(0.1 -0.1)\">\n", "      <use xlink:href=\"#DejaVuSans-50\"/>\n", "      <use xlink:href=\"#DejaVuSans-28\" x=\"60.302734\"/>\n", "      <use xlink:href=\"#DejaVuSans-64\" x=\"99.316406\"/>\n", "      <use xlink:href=\"#DejaVuSans-69\" x=\"162.792969\"/>\n", "      <use xlink:href=\"#DejaVuSans-65\" x=\"190.576172\"/>\n", "      <use xlink:href=\"#DejaVuSans-3d\" x=\"252.099609\"/>\n", "      <use xlink:href=\"#DejaVuSans-32\" x=\"335.888672\"/>\n", "      <use xlink:href=\"#DejaVuSans-29\" x=\"399.511719\"/>\n", "     </g>\n", "    </g>\n", "    <g id=\"line2d_24\">\n", "     <path d=\"M 304.089063 49.654687 \n", "L 314.089063 49.654687 \n", "L 324.089063 49.654687 \n", "\" style=\"fill: none; stroke: #2ca02c; stroke-width: 1.5; stroke-linecap: square\"/>\n", "    </g>\n", "    <g id=\"text_19\">\n", "     <!-- P(die=3) -->\n", "     <g transform=\"translate(332.089063 53.154687)scale(0.1 -0.1)\">\n", "      <use xlink:href=\"#DejaVuSans-50\"/>\n", "      <use xlink:href=\"#DejaVuSans-28\" x=\"60.302734\"/>\n", "      <use xlink:href=\"#DejaVuSans-64\" x=\"99.316406\"/>\n", "      <use xlink:href=\"#DejaVuSans-69\" x=\"162.792969\"/>\n", "      <use xlink:href=\"#DejaVuSans-65\" x=\"190.576172\"/>\n", "      <use xlink:href=\"#DejaVuSans-3d\" x=\"252.099609\"/>\n", "      <use xlink:href=\"#DejaVuSans-33\" x=\"335.888672\"/>\n", "      <use xlink:href=\"#DejaVuSans-29\" x=\"399.511719\"/>\n", "     </g>\n", "    </g>\n", "    <g id=\"line2d_25\">\n", "     <path d=\"M 304.089063 64.332812 \n", "L 314.089063 64.332812 \n", "L 324.089063 64.332812 \n", "\" style=\"fill: none; stroke: #d62728; stroke-width: 1.5; stroke-linecap: square\"/>\n", "    </g>\n", "    <g id=\"text_20\">\n", "     <!-- P(die=4) -->\n", "     <g transform=\"translate(332.089063 67.832812)scale(0.1 -0.1)\">\n", "      <use xlink:href=\"#DejaVuSans-50\"/>\n", "      <use xlink:href=\"#DejaVuSans-28\" x=\"60.302734\"/>\n", "      <use xlink:href=\"#DejaVuSans-64\" x=\"99.316406\"/>\n", "      <use xlink:href=\"#DejaVuSans-69\" x=\"162.792969\"/>\n", "      <use xlink:href=\"#DejaVuSans-65\" x=\"190.576172\"/>\n", "      <use xlink:href=\"#DejaVuSans-3d\" x=\"252.099609\"/>\n", "      <use xlink:href=\"#DejaVuSans-34\" x=\"335.888672\"/>\n", "      <use xlink:href=\"#DejaVuSans-29\" x=\"399.511719\"/>\n", "     </g>\n", "    </g>\n", "    <g id=\"line2d_26\">\n", "     <path d=\"M 304.089063 79.010937 \n", "L 314.089063 79.010937 \n", "L 324.089063 79.010937 \n", "\" style=\"fill: none; stroke: #9467bd; stroke-width: 1.5; stroke-linecap: square\"/>\n", "    </g>\n", "    <g id=\"text_21\">\n", "     <!-- P(die=5) -->\n", "     <g transform=\"translate(332.089063 82.510937)scale(0.1 -0.1)\">\n", "      <use xlink:href=\"#DejaVuSans-50\"/>\n", "      <use xlink:href=\"#DejaVuSans-28\" x=\"60.302734\"/>\n", "      <use xlink:href=\"#DejaVuSans-64\" x=\"99.316406\"/>\n", "      <use xlink:href=\"#DejaVuSans-69\" x=\"162.792969\"/>\n", "      <use xlink:href=\"#DejaVuSans-65\" x=\"190.576172\"/>\n", "      <use xlink:href=\"#DejaVuSans-3d\" x=\"252.099609\"/>\n", "      <use xlink:href=\"#DejaVuSans-35\" x=\"335.888672\"/>\n", "      <use xlink:href=\"#DejaVuSans-29\" x=\"399.511719\"/>\n", "     </g>\n", "    </g>\n", "    <g id=\"line2d_27\">\n", "     <path d=\"M 304.089063 93.689062 \n", "L 314.089063 93.689062 \n", "L 324.089063 93.689062 \n", "\" style=\"fill: none; stroke: #8c564b; stroke-width: 1.5; stroke-linecap: square\"/>\n", "    </g>\n", "    <g id=\"text_22\">\n", "     <!-- P(die=6) -->\n", "     <g transform=\"translate(332.089063 97.189062)scale(0.1 -0.1)\">\n", "      <defs>\n", "       <path id=\"DejaVuSans-36\" d=\"M 2113 2584 \n", "Q 1688 2584 1439 2293 \n", "Q 1191 2003 1191 1497 \n", "Q 1191 994 1439 701 \n", "Q 1688 409 2113 409 \n", "Q 2538 409 2786 701 \n", "Q 3034 994 3034 1497 \n", "Q 3034 2003 2786 2293 \n", "Q 2538 2584 2113 2584 \n", "z\n", "M 3366 4563 \n", "L 3366 3988 \n", "Q 3128 4100 2886 4159 \n", "Q 2644 4219 2406 4219 \n", "Q 1781 4219 1451 3797 \n", "Q 1122 3375 1075 2522 \n", "Q 1259 2794 1537 2939 \n", "Q 1816 3084 2150 3084 \n", "Q 2853 3084 3261 2657 \n", "Q 3669 2231 3669 1497 \n", "Q 3669 778 3244 343 \n", "Q 2819 -91 2113 -91 \n", "Q 1303 -91 875 529 \n", "Q 447 1150 447 2328 \n", "Q 447 3434 972 4092 \n", "Q 1497 4750 2381 4750 \n", "Q 2619 4750 2861 4703 \n", "Q 3103 4656 3366 4563 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "      </defs>\n", "      <use xlink:href=\"#DejaVuSans-50\"/>\n", "      <use xlink:href=\"#DejaVuSans-28\" x=\"60.302734\"/>\n", "      <use xlink:href=\"#DejaVuSans-64\" x=\"99.316406\"/>\n", "      <use xlink:href=\"#DejaVuSans-69\" x=\"162.792969\"/>\n", "      <use xlink:href=\"#DejaVuSans-65\" x=\"190.576172\"/>\n", "      <use xlink:href=\"#DejaVuSans-3d\" x=\"252.099609\"/>\n", "      <use xlink:href=\"#DejaVuSans-36\" x=\"335.888672\"/>\n", "      <use xlink:href=\"#DejaVuSans-29\" x=\"399.511719\"/>\n", "     </g>\n", "    </g>\n", "   </g>\n", "  </g>\n", " </g>\n", " <defs>\n", "  <clipPath id=\"p939fb0ebd7\">\n", "   <rect x=\"50.14375\" y=\"7.2\" width=\"334.8\" height=\"249.48\"/>\n", "  </clipPath>\n", " </defs>\n", "</svg>\n"], "text/plain": ["<Figure size 600x450 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["counts = multinomial.Multinomial(10, fair_probs).sample((500,))\n", "cum_counts = counts.cumsum(dim=0)\n", "estimates = cum_counts / cum_counts.sum(dim=1, keepdims=True)\n", "\n", "d2l.set_figsize((6, 4.5))\n", "for i in range(6):\n", "    d2l.plt.plot(estimates[:, i].numpy(),\n", "                 label=(\"P(die=\" + str(i + 1) + \")\"))\n", "d2l.plt.axhline(y=0.167, color='black', linestyle='dashed')\n", "d2l.plt.gca().set_xlabel('Groups of experiments')\n", "d2l.plt.gca().set_ylabel('Estimated probability')\n", "d2l.plt.legend();\n", "counts"]}, {"cell_type": "markdown", "id": "e6400f71", "metadata": {"origin_pos": 25}, "source": ["每条实线对应于骰子的6个值中的一个，并给出骰子在每组实验后出现值的估计概率。\n", "当我们通过更多的实验获得更多的数据时，这$6$条实体曲线向真实概率收敛。\n", "\n", "### 概率论公理\n", "\n", "在处理骰子掷出时，我们将集合$\\mathcal{S} = \\{1, 2, 3, 4, 5, 6\\}$\n", "称为*样本空间*（sample space）或*结果空间*（outcome space），\n", "其中每个元素都是*结果*（outcome）。\n", "*事件*（event）是一组给定样本空间的随机结果。\n", "例如，“看到$5$”（$\\{5\\}$）和“看到奇数”（$\\{1, 3, 5\\}$）都是掷出骰子的有效事件。\n", "注意，如果一个随机实验的结果在$\\mathcal{A}$中，则事件$\\mathcal{A}$已经发生。\n", "也就是说，如果投掷出$3$点，因为$3 \\in \\{1, 3, 5\\}$，我们可以说，“看到奇数”的事件发生了。\n", "\n", "*概率*（probability）可以被认为是将集合映射到真实值的函数。\n", "在给定的样本空间$\\mathcal{S}$中，事件$\\mathcal{A}$的概率，\n", "表示为$P(\\mathcal{A})$，满足以下属性：\n", "\n", "* 对于任意事件$\\mathcal{A}$，其概率从不会是负数，即$P(\\mathcal{A}) \\geq 0$；\n", "* 整个样本空间的概率为$1$，即$P(\\mathcal{S}) = 1$；\n", "* 对于*互斥*（mutually exclusive）事件（对于所有$i \\neq j$都有$\\mathcal{A}_i \\cap \\mathcal{A}_j = \\emptyset$）的任意一个可数序列$\\mathcal{A}_1, \\mathcal{A}_2, \\ldots$，序列中任意一个事件发生的概率等于它们各自发生的概率之和，即$P(\\bigcup_{i=1}^{\\infty} \\mathcal{A}_i) = \\sum_{i=1}^{\\infty} P(\\mathcal{A}_i)$。\n", "\n", "以上也是概率论的公理，由科尔莫戈罗夫于1933年提出。\n", "有了这个公理系统，我们可以避免任何关于随机性的哲学争论；\n", "相反，我们可以用数学语言严格地推理。\n", "例如，假设事件$\\mathcal{A}_1$为整个样本空间，\n", "且当所有$i > 1$时的$\\mathcal{A}_i = \\emptyset$，\n", "那么我们可以证明$P(\\emptyset) = 0$，即不可能发生事件的概率是$0$。\n", "\n", "### 随机变量\n", "\n", "在我们掷骰子的随机实验中，我们引入了*随机变量*（random variable）的概念。\n", "随机变量几乎可以是任何数量，并且它可以在随机实验的一组可能性中取一个值。\n", "考虑一个随机变量$X$，其值在掷骰子的样本空间$\\mathcal{S}=\\{1,2,3,4,5,6\\}$中。\n", "我们可以将事件“看到一个$5$”表示为$\\{X=5\\}$或$X=5$，\n", "其概率表示为$P(\\{X=5\\})$或$P(X=5)$。\n", "通过$P(X=a)$，我们区分了随机变量$X$和$X$可以采取的值（例如$a$）。\n", "然而，这可能会导致繁琐的表示。\n", "为了简化符号，一方面，我们可以将$P(X)$表示为随机变量$X$上的*分布*（distribution）：\n", "分布告诉我们$X$获得某一值的概率。\n", "另一方面，我们可以简单用$P(a)$表示随机变量取值$a$的概率。\n", "由于概率论中的事件是来自样本空间的一组结果，因此我们可以为随机变量指定值的可取范围。\n", "例如，$P(1 \\leq X \\leq 3)$表示事件$\\{1 \\leq X \\leq 3\\}$，\n", "即$\\{X = 1, 2, \\text{or}, 3\\}$的概率。\n", "等价地，$P(1 \\leq X \\leq 3)$表示随机变量$X$从$\\{1, 2, 3\\}$中取值的概率。\n", "\n", "请注意，*离散*（discrete）随机变量（如骰子的每一面）\n", "和*连续*（continuous）随机变量（如人的体重和身高）之间存在微妙的区别。\n", "现实生活中，测量两个人是否具有完全相同的身高没有太大意义。\n", "如果我们进行足够精确的测量，最终会发现这个星球上没有两个人具有完全相同的身高。\n", "在这种情况下，询问某人的身高是否落入给定的区间，比如是否在1.79米和1.81米之间更有意义。\n", "在这些情况下，我们将这个看到某个数值的可能性量化为*密度*（density）。\n", "高度恰好为1.80米的概率为0，但密度不是0。\n", "在任何两个不同高度之间的区间，我们都有非零的概率。\n", "在本节的其余部分中，我们将考虑离散空间中的概率。\n", "连续随机变量的概率可以参考深度学习数学附录中[随机变量](https://d2l.ai/chapter_appendix-mathematics-for-deep-learning/random-variables.html)\n", "的一节。\n", "\n", "## 处理多个随机变量\n", "\n", "很多时候，我们会考虑多个随机变量。\n", "比如，我们可能需要对疾病和症状之间的关系进行建模。\n", "给定一个疾病和一个症状，比如“流感”和“咳嗽”，以某个概率存在或不存在于某个患者身上。\n", "我们需要估计这些概率以及概率之间的关系，以便我们可以运用我们的推断来实现更好的医疗服务。\n", "\n", "再举一个更复杂的例子：图像包含数百万像素，因此有数百万个随机变量。\n", "在许多情况下，图像会附带一个*标签*（label），标识图像中的对象。\n", "我们也可以将标签视为一个随机变量。\n", "我们甚至可以将所有元数据视为随机变量，例如位置、时间、光圈、焦距、ISO、对焦距离和相机类型。\n", "所有这些都是联合发生的随机变量。\n", "当我们处理多个随机变量时，会有若干个变量是我们感兴趣的。\n", "\n", "### 联合概率\n", "\n", "第一个被称为*联合概率*（joint probability）$P(A=a,B=b)$。\n", "给定任意值$a$和$b$，联合概率可以回答：$A=a$和$B=b$同时满足的概率是多少？\n", "请注意，对于任何$a$和$b$的取值，$P(A = a, B=b) \\leq P(A=a)$。\n", "这点是确定的，因为要同时发生$A=a$和$B=b$，$A=a$就必须发生，$B=b$也必须发生（反之亦然）。因此，$A=a$和$B=b$同时发生的可能性不大于$A=a$或是$B=b$单独发生的可能性。\n", "\n", "### 条件概率\n", "\n", "联合概率的不等式带给我们一个有趣的比率：\n", "$0 \\leq \\frac{P(A=a, B=b)}{P(A=a)} \\leq 1$。\n", "我们称这个比率为*条件概率*（conditional probability），\n", "并用$P(B=b \\mid A=a)$表示它：它是$B=b$的概率，前提是$A=a$已发生。\n", "\n", "### 贝叶斯定理\n", "\n", "使用条件概率的定义，我们可以得出统计学中最有用的方程之一：\n", "*Bay<PERSON>定理*（<PERSON><PERSON>' theorem）。\n", "根据*乘法法则*（multiplication rule ）可得到$P(A, B) = P(B \\mid A) P(A)$。\n", "根据对称性，可得到$P(A, B) = P(A \\mid B) P(B)$。\n", "假设$P(B)>0$，求解其中一个条件变量，我们得到\n", "\n", "$$P(A \\mid B) = \\frac{P(B \\mid A) P(A)}{P(B)}.$$\n", "\n", "请注意，这里我们使用紧凑的表示法：\n", "其中$P(A, B)$是一个*联合分布*（joint distribution），\n", "$P(A \\mid B)$是一个*条件分布*（conditional distribution）。\n", "这种分布可以在给定值$A = a, B=b$上进行求值。\n", "\n", "### 边际化\n", "\n", "为了能进行事件概率求和，我们需要*求和法则*（sum rule），\n", "即$B$的概率相当于计算$A$的所有可能选择，并将所有选择的联合概率聚合在一起：\n", "\n", "$$P(B) = \\sum_{A} P(A, B),$$\n", "\n", "这也称为*边际化*（marginalization）。\n", "边际化结果的概率或分布称为*边际概率*（marginal probability）\n", "或*边际分布*（marginal distribution）。\n", "\n", "### 独立性\n", "\n", "另一个有用属性是*依赖*（dependence）与*独立*（independence）。\n", "如果两个随机变量$A$和$B$是独立的，意味着事件$A$的发生跟$B$事件的发生无关。\n", "在这种情况下，统计学家通常将这一点表述为$A \\perp  B$。\n", "根据贝叶斯定理，马上就能同样得到$P(A \\mid B) = P(A)$。\n", "在所有其他情况下，我们称$A$和$B$依赖。\n", "比如，两次连续抛出一个骰子的事件是相互独立的。\n", "相比之下，灯开关的位置和房间的亮度并不是（因为可能存在灯泡坏掉、电源故障，或者开关故障）。\n", "\n", "由于$P(A \\mid B) = \\frac{P(A, B)}{P(B)} = P(A)$等价于$P(A, B) = P(A)P(B)$，\n", "因此两个随机变量是独立的，当且仅当两个随机变量的联合分布是其各自分布的乘积。\n", "同样地，给定另一个随机变量$C$时，两个随机变量$A$和$B$是*条件独立的*（conditionally independent），\n", "当且仅当$P(A, B \\mid C) = P(A \\mid C)P(B \\mid C)$。\n", "这个情况表示为$A \\perp B \\mid C$。\n", "\n", "### 应用\n", ":label:`subsec_probability_hiv_app`\n", "\n", "我们实战演练一下！\n", "假设一个医生对患者进行艾滋病病毒（HIV）测试。\n", "这个测试是相当准确的，如果患者健康但测试显示他患病，这个概率只有1%；\n", "如果患者真正感染HIV，它永远不会检测不出。\n", "我们使用$D_1$来表示诊断结果（如果阳性，则为$1$，如果阴性，则为$0$），\n", "$H$来表示感染艾滋病病毒的状态（如果阳性，则为$1$，如果阴性，则为$0$）。\n", "在 :numref:`conditional_prob_D1`中列出了这样的条件概率。\n", "\n", ":条件概率为$P(D_1 \\mid H)$\n", "\n", "| 条件概率 | $H=1$ | $H=0$ |\n", "|---|---|---|\n", "|$P(D_1 = 1 \\mid H)$|            1 |         0.01 |\n", "|$P(D_1 = 0 \\mid H)$|            0 |         0.99 |\n", ":label:`conditional_prob_D1`\n", "\n", "请注意，每列的加和都是1（但每行的加和不是），因为条件概率需要总和为1，就像概率一样。\n", "让我们计算如果测试出来呈阳性，患者感染HIV的概率，即$P(H = 1 \\mid D_1 = 1)$。\n", "显然，这将取决于疾病有多常见，因为它会影响错误警报的数量。\n", "假设人口总体是相当健康的，例如，$P(H=1) = 0.0015$。\n", "为了应用贝叶斯定理，我们需要运用边际化和乘法法则来确定\n", "\n", "$$\\begin{aligned}\n", "&P(D_1 = 1) \\\\\n", "=& P(D_1=1, H=0) + P(D_1=1, H=1)  \\\\\n", "=& P(D_1=1 \\mid H=0) P(H=0) + P(D_1=1 \\mid H=1) P(H=1) \\\\\n", "=& 0.011485.\n", "\\end{aligned}\n", "$$\n", "因此，我们得到\n", "\n", "$$\\begin{aligned}\n", "&P(H = 1 \\mid D_1 = 1)\\\\ =& \\frac{P(D_1=1 \\mid H=1) P(H=1)}{P(D_1=1)} \\\\ =& 0.1306 \\end{aligned}.$$\n", "\n", "换句话说，尽管使用了非常准确的测试，患者实际上患有艾滋病的几率只有13.06%。\n", "正如我们所看到的，概率可能是违反直觉的。\n", "\n", "患者在收到这样可怕的消息后应该怎么办？\n", "很可能，患者会要求医生进行另一次测试来确定病情。\n", "第二个测试具有不同的特性，它不如第一个测试那么精确，\n", "如 :numref:`conditional_prob_D2`所示。\n", "\n", ":条件概率为$P(D_2 \\mid H)$\n", "\n", "| 条件概率 | $H=1$ | $H=0$ |\n", "|---|---|---|\n", "|$P(D_2 = 1 \\mid H)$|            0.98 |         0.03 |\n", "|$P(D_2 = 0 \\mid H)$|            0.02 |         0.97 |\n", ":label:`conditional_prob_D2`\n", "\n", "不幸的是，第二次测试也显示阳性。让我们通过假设条件独立性来计算出应用Bayes定理的必要概率：\n", "\n", "$$\\begin{aligned}\n", "&P(D_1 = 1, D_2 = 1 \\mid H = 0) \\\\\n", "=& P(D_1 = 1 \\mid H = 0) P(D_2 = 1 \\mid H = 0)  \\\\\n", "=& 0.0003,\n", "\\end{aligned}\n", "$$\n", "\n", "$$\\begin{aligned}\n", "&P(D_1 = 1, D_2 = 1 \\mid H = 1) \\\\\n", "=& P(D_1 = 1 \\mid H = 1) P(D_2 = 1 \\mid H = 1)  \\\\\n", "=& 0.98.\n", "\\end{aligned}\n", "$$\n", "现在我们可以应用边际化和乘法规则：\n", "\n", "$$\\begin{aligned}\n", "&P(D_1 = 1, D_2 = 1) \\\\\n", "=& P(D_1 = 1, D_2 = 1, H = 0) + P(D_1 = 1, D_2 = 1, H = 1)  \\\\\n", "=& P(D_1 = 1, D_2 = 1 \\mid H = 0)P(H=0) + P(D_1 = 1, D_2 = 1 \\mid H = 1)P(H=1)\\\\\n", "=& 0.00176955.\n", "\\end{aligned}\n", "$$\n", "\n", "最后，鉴于存在两次阳性检测，患者患有艾滋病的概率为\n", "\n", "$$\\begin{aligned}\n", "&P(H = 1 \\mid D_1 = 1, D_2 = 1)\\\\\n", "=& \\frac{P(D_1 = 1, D_2 = 1 \\mid H=1) P(H=1)}{P(D_1 = 1, D_2 = 1)} \\\\\n", "=& 0.8307.\n", "\\end{aligned}\n", "$$\n", "\n", "也就是说，第二次测试使我们能够对患病的情况获得更高的信心。\n", "尽管第二次检验比第一次检验的准确性要低得多，但它仍然显著提高我们的预测概率。\n", "\n", "## 期望和方差\n", "\n", "为了概括概率分布的关键特征，我们需要一些测量方法。\n", "一个随机变量$X$的*期望*（expectation，或平均值（average））表示为\n", "\n", "$$E[X] = \\sum_{x} x P(X = x).$$\n", "\n", "当函数$f(x)$的输入是从分布$P$中抽取的随机变量时，$f(x)$的期望值为\n", "\n", "$$E_{x \\sim P}[f(x)] = \\sum_x f(x) P(x).$$\n", "\n", "在许多情况下，我们希望衡量随机变量$X$与其期望值的偏置。这可以通过方差来量化\n", "\n", "$$\\mathrm{Var}[X] = E\\left[(X - E[X])^2\\right] =\n", "E[X^2] - E[X]^2.$$\n", "\n", "方差的平方根被称为*标准差*（standard deviation）。\n", "随机变量函数的方差衡量的是：当从该随机变量分布中采样不同值$x$时，\n", "函数值偏离该函数的期望的程度：\n", "\n", "$$\\mathrm{Var}[f(x)] = E\\left[\\left(f(x) - E[f(x)]\\right)^2\\right].$$\n", "\n", "## 小结\n", "\n", "* 我们可以从概率分布中采样。\n", "* 我们可以使用联合分布、条件分布、Bayes定理、边缘化和独立性假设来分析多个随机变量。\n", "* 期望和方差为概率分布的关键特征的概括提供了实用的度量形式。\n", "\n", "## 练习\n", "\n", "1. 进行$m=500$组实验，每组抽取$n=10$个样本。改变$m$和$n$，观察和分析实验结果。\n", "2. 给定两个概率为$P(\\mathcal{A})$和$P(\\mathcal{B})$的事件，计算$P(\\mathcal{A} \\cup \\mathcal{B})$和$P(\\mathcal{A} \\cap \\mathcal{B})$的上限和下限。（提示：使用[友元图](https://en.wikipedia.org/wiki/Venn_diagram)来展示这些情况。)\n", "3. 假设我们有一系列随机变量，例如$A$、$B$和$C$，其中$B$只依赖于$A$，而$C$只依赖于$B$，能简化联合概率$P(A, B, C)$吗？（提示：这是一个[马尔可夫链](https://en.wikipedia.org/wiki/Markov_chain)。)\n", "4. 在 :numref:`subsec_probability_hiv_app`中，第一个测试更准确。为什么不运行第一个测试两次，而是同时运行第一个和第二个测试?\n"]}, {"cell_type": "markdown", "id": "98e5c674", "metadata": {"origin_pos": 27, "tab": ["pytorch"]}, "source": ["[Discussions](https://discuss.d2l.ai/t/1762)\n"]}], "metadata": {"kernelspec": {"display_name": "d2l", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.23"}}, "nbformat": 4, "nbformat_minor": 5}