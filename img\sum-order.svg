<?xml version="1.0" encoding="UTF-8"?>
<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="347pt" height="123pt" viewBox="0 0 347 123" version="1.1">
<defs>
<g>
<symbol overflow="visible" id="glyph0-0">
<path style="stroke:none;" d=""/>
</symbol>
<symbol overflow="visible" id="glyph0-1">
<path style="stroke:none;" d="M 3.546875 0 L 3.546875 -0.140625 C 2.875 -0.140625 2.6875 -0.296875 2.6875 -0.6875 L 2.6875 -6.0625 L 2.609375 -6.078125 L 1 -5.265625 L 1 -5.140625 L 1.234375 -5.234375 C 1.40625 -5.296875 1.5625 -5.34375 1.640625 -5.34375 C 1.84375 -5.34375 1.921875 -5.203125 1.921875 -4.890625 L 1.921875 -0.859375 C 1.921875 -0.359375 1.734375 -0.171875 1.0625 -0.140625 L 1.0625 0 Z M 3.546875 0 "/>
</symbol>
<symbol overflow="visible" id="glyph0-2">
<path style="stroke:none;" d="M 4.265625 -1.234375 L 4.140625 -1.28125 C 3.84375 -0.78125 3.65625 -0.6875 3.28125 -0.6875 L 1.171875 -0.6875 L 2.65625 -2.265625 C 3.453125 -3.109375 3.8125 -3.78125 3.8125 -4.5 C 3.8125 -5.390625 3.15625 -6.078125 2.140625 -6.078125 C 1.03125 -6.078125 0.453125 -5.34375 0.265625 -4.296875 L 0.453125 -4.25 C 0.8125 -5.125 1.140625 -5.421875 1.78125 -5.421875 C 2.546875 -5.421875 3.03125 -4.96875 3.03125 -4.15625 C 3.03125 -3.390625 2.703125 -2.703125 1.859375 -1.8125 L 0.265625 -0.109375 L 0.265625 0 L 3.78125 0 Z M 4.265625 -1.234375 "/>
</symbol>
<symbol overflow="visible" id="glyph0-3">
<path style="stroke:none;" d="M 5.734375 -2.875 L 5.734375 -3.46875 L 0.4375 -3.46875 L 0.4375 -2.875 Z M 5.734375 -1.078125 L 5.734375 -1.671875 L 0.4375 -1.671875 L 0.4375 -1.078125 Z M 5.734375 -1.078125 "/>
</symbol>
</g>
</defs>
<g id="surface1">
<path style="fill-rule:nonzero;fill:rgb(100%,100%,100%);fill-opacity:1;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 0 0 L 10 0 L 10 10 L 0 10 Z M 0 0 " transform="matrix(1,0,0,1,1,1)"/>
<path style="fill-rule:nonzero;fill:rgb(100%,100%,100%);fill-opacity:1;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 10 0 L 20 0 L 20 10 L 10 10 Z M 10 0 " transform="matrix(1,0,0,1,1,1)"/>
<path style="fill-rule:nonzero;fill:rgb(100%,100%,100%);fill-opacity:1;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 20 0 L 30 0 L 30 10 L 20 10 Z M 20 0 " transform="matrix(1,0,0,1,1,1)"/>
<path style="fill-rule:nonzero;fill:rgb(100%,100%,100%);fill-opacity:1;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 30 0 L 40 0 L 40 10 L 30 10 Z M 30 0 " transform="matrix(1,0,0,1,1,1)"/>
<path style="fill-rule:nonzero;fill:rgb(100%,100%,100%);fill-opacity:1;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 40 0 L 50 0 L 50 10 L 40 10 Z M 40 0 " transform="matrix(1,0,0,1,1,1)"/>
<path style="fill-rule:nonzero;fill:rgb(100%,100%,100%);fill-opacity:1;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 50 0 L 60 0 L 60 10 L 50 10 Z M 50 0 " transform="matrix(1,0,0,1,1,1)"/>
<path style="fill-rule:nonzero;fill:rgb(100%,100%,100%);fill-opacity:1;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 60 0 L 70 0 L 70 10 L 60 10 Z M 60 0 " transform="matrix(1,0,0,1,1,1)"/>
<path style="fill-rule:nonzero;fill:rgb(100%,100%,100%);fill-opacity:1;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 70 0 L 80 0 L 80 10 L 70 10 Z M 70 0 " transform="matrix(1,0,0,1,1,1)"/>
<path style="fill-rule:nonzero;fill:rgb(100%,100%,100%);fill-opacity:1;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 80 0 L 90 0 L 90 10 L 80 10 Z M 80 0 " transform="matrix(1,0,0,1,1,1)"/>
<path style="fill-rule:nonzero;fill:rgb(100%,100%,100%);fill-opacity:1;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 90 0 L 100 0 L 100 10 L 90 10 Z M 90 0 " transform="matrix(1,0,0,1,1,1)"/>
<path style="fill-rule:nonzero;fill:rgb(100%,100%,100%);fill-opacity:1;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 0 10 L 10 10 L 10 20 L 0 20 Z M 0 10 " transform="matrix(1,0,0,1,1,1)"/>
<path style="fill-rule:nonzero;fill:rgb(69.804382%,85.098267%,100%);fill-opacity:1;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 10 10 L 20 10 L 20 20 L 10 20 Z M 10 10 " transform="matrix(1,0,0,1,1,1)"/>
<path style="fill-rule:nonzero;fill:rgb(69.804382%,85.098267%,100%);fill-opacity:1;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 20 10 L 30 10 L 30 20 L 20 20 Z M 20 10 " transform="matrix(1,0,0,1,1,1)"/>
<path style="fill-rule:nonzero;fill:rgb(100%,100%,100%);fill-opacity:1;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 30 10 L 40 10 L 40 20 L 30 20 Z M 30 10 " transform="matrix(1,0,0,1,1,1)"/>
<path style="fill-rule:nonzero;fill:rgb(100%,100%,100%);fill-opacity:1;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 40 10 L 50 10 L 50 20 L 40 20 Z M 40 10 " transform="matrix(1,0,0,1,1,1)"/>
<path style="fill-rule:nonzero;fill:rgb(100%,100%,100%);fill-opacity:1;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 50 10 L 60 10 L 60 20 L 50 20 Z M 50 10 " transform="matrix(1,0,0,1,1,1)"/>
<path style="fill-rule:nonzero;fill:rgb(69.804382%,85.098267%,100%);fill-opacity:1;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 60 10 L 70 10 L 70 20 L 60 20 Z M 60 10 " transform="matrix(1,0,0,1,1,1)"/>
<path style="fill-rule:nonzero;fill:rgb(69.804382%,85.098267%,100%);fill-opacity:1;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 70 10 L 80 10 L 80 20 L 70 20 Z M 70 10 " transform="matrix(1,0,0,1,1,1)"/>
<path style="fill-rule:nonzero;fill:rgb(100%,100%,100%);fill-opacity:1;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 80 10 L 90 10 L 90 20 L 80 20 Z M 80 10 " transform="matrix(1,0,0,1,1,1)"/>
<path style="fill-rule:nonzero;fill:rgb(100%,100%,100%);fill-opacity:1;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 90 10 L 100 10 L 100 20 L 90 20 Z M 90 10 " transform="matrix(1,0,0,1,1,1)"/>
<path style="fill-rule:nonzero;fill:rgb(100%,100%,100%);fill-opacity:1;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 0 20 L 10 20 L 10 30 L 0 30 Z M 0 20 " transform="matrix(1,0,0,1,1,1)"/>
<path style="fill-rule:nonzero;fill:rgb(69.804382%,85.098267%,100%);fill-opacity:1;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 10 20 L 20 20 L 20 30 L 10 30 Z M 10 20 " transform="matrix(1,0,0,1,1,1)"/>
<path style="fill-rule:nonzero;fill:rgb(39.99939%,74.902344%,100%);fill-opacity:1;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 20 20 L 30 20 L 30 30 L 20 30 Z M 20 20 " transform="matrix(1,0,0,1,1,1)"/>
<path style="fill-rule:nonzero;fill:rgb(69.804382%,85.098267%,100%);fill-opacity:1;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 30 20 L 40 20 L 40 30 L 30 30 Z M 30 20 " transform="matrix(1,0,0,1,1,1)"/>
<path style="fill-rule:nonzero;fill:rgb(100%,100%,100%);fill-opacity:1;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 40 20 L 50 20 L 50 30 L 40 30 Z M 40 20 " transform="matrix(1,0,0,1,1,1)"/>
<path style="fill-rule:nonzero;fill:rgb(69.804382%,85.098267%,100%);fill-opacity:1;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 50 20 L 60 20 L 60 30 L 50 30 Z M 50 20 " transform="matrix(1,0,0,1,1,1)"/>
<path style="fill-rule:nonzero;fill:rgb(69.804382%,85.098267%,100%);fill-opacity:1;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 60 20 L 70 20 L 70 30 L 60 30 Z M 60 20 " transform="matrix(1,0,0,1,1,1)"/>
<path style="fill-rule:nonzero;fill:rgb(69.804382%,85.098267%,100%);fill-opacity:1;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 70 20 L 80 20 L 80 30 L 70 30 Z M 70 20 " transform="matrix(1,0,0,1,1,1)"/>
<path style="fill-rule:nonzero;fill:rgb(100%,100%,100%);fill-opacity:1;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 80 20 L 90 20 L 90 30 L 80 30 Z M 80 20 " transform="matrix(1,0,0,1,1,1)"/>
<path style="fill-rule:nonzero;fill:rgb(100%,100%,100%);fill-opacity:1;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 90 20 L 100 20 L 100 30 L 90 30 Z M 90 20 " transform="matrix(1,0,0,1,1,1)"/>
<path style="fill-rule:nonzero;fill:rgb(100%,100%,100%);fill-opacity:1;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 0 30 L 10 30 L 10 40 L 0 40 Z M 0 30 " transform="matrix(1,0,0,1,1,1)"/>
<path style="fill-rule:nonzero;fill:rgb(100%,100%,100%);fill-opacity:1;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 10 30 L 20 30 L 20 40 L 10 40 Z M 10 30 " transform="matrix(1,0,0,1,1,1)"/>
<path style="fill-rule:nonzero;fill:rgb(69.804382%,85.098267%,100%);fill-opacity:1;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 20 30 L 30 30 L 30 40 L 20 40 Z M 20 30 " transform="matrix(1,0,0,1,1,1)"/>
<path style="fill-rule:nonzero;fill:rgb(69.804382%,85.098267%,100%);fill-opacity:1;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 30 30 L 40 30 L 40 40 L 30 40 Z M 30 30 " transform="matrix(1,0,0,1,1,1)"/>
<path style="fill-rule:nonzero;fill:rgb(100%,100%,100%);fill-opacity:1;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 40 30 L 50 30 L 50 40 L 40 40 Z M 40 30 " transform="matrix(1,0,0,1,1,1)"/>
<path style="fill-rule:nonzero;fill:rgb(69.804382%,85.098267%,100%);fill-opacity:1;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 50 30 L 60 30 L 60 40 L 50 40 Z M 50 30 " transform="matrix(1,0,0,1,1,1)"/>
<path style="fill-rule:nonzero;fill:rgb(39.99939%,74.902344%,100%);fill-opacity:1;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 60 30 L 70 30 L 70 40 L 60 40 Z M 60 30 " transform="matrix(1,0,0,1,1,1)"/>
<path style="fill-rule:nonzero;fill:rgb(39.99939%,74.902344%,100%);fill-opacity:1;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 70 30 L 80 30 L 80 40 L 70 40 Z M 70 30 " transform="matrix(1,0,0,1,1,1)"/>
<path style="fill-rule:nonzero;fill:rgb(69.804382%,85.098267%,100%);fill-opacity:1;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 80 30 L 90 30 L 90 40 L 80 40 Z M 80 30 " transform="matrix(1,0,0,1,1,1)"/>
<path style="fill-rule:nonzero;fill:rgb(100%,100%,100%);fill-opacity:1;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 90 30 L 100 30 L 100 40 L 90 40 Z M 90 30 " transform="matrix(1,0,0,1,1,1)"/>
<path style="fill-rule:nonzero;fill:rgb(69.804382%,85.098267%,100%);fill-opacity:1;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 0 40 L 10 40 L 10 50 L 0 50 Z M 0 40 " transform="matrix(1,0,0,1,1,1)"/>
<path style="fill-rule:nonzero;fill:rgb(100%,100%,100%);fill-opacity:1;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 10 40 L 20 40 L 20 50 L 10 50 Z M 10 40 " transform="matrix(1,0,0,1,1,1)"/>
<path style="fill-rule:nonzero;fill:rgb(100%,100%,100%);fill-opacity:1;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 20 40 L 30 40 L 30 50 L 20 50 Z M 20 40 " transform="matrix(1,0,0,1,1,1)"/>
<path style="fill-rule:nonzero;fill:rgb(100%,100%,100%);fill-opacity:1;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 30 40 L 40 40 L 40 50 L 30 50 Z M 30 40 " transform="matrix(1,0,0,1,1,1)"/>
<path style="fill-rule:nonzero;fill:rgb(69.804382%,85.098267%,100%);fill-opacity:1;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 40 40 L 50 40 L 50 50 L 40 50 Z M 40 40 " transform="matrix(1,0,0,1,1,1)"/>
<path style="fill-rule:nonzero;fill:rgb(39.99939%,74.902344%,100%);fill-opacity:1;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 50 40 L 60 40 L 60 50 L 50 50 Z M 50 40 " transform="matrix(1,0,0,1,1,1)"/>
<path style="fill-rule:nonzero;fill:rgb(24.705505%,63.922119%,99.215698%);fill-opacity:1;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 60 40 L 70 40 L 70 50 L 60 50 Z M 60 40 " transform="matrix(1,0,0,1,1,1)"/>
<path style="fill-rule:nonzero;fill:rgb(39.99939%,74.902344%,100%);fill-opacity:1;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 70 40 L 80 40 L 80 50 L 70 50 Z M 70 40 " transform="matrix(1,0,0,1,1,1)"/>
<path style="fill-rule:nonzero;fill:rgb(69.804382%,85.098267%,100%);fill-opacity:1;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 80 40 L 90 40 L 90 50 L 80 50 Z M 80 40 " transform="matrix(1,0,0,1,1,1)"/>
<path style="fill-rule:nonzero;fill:rgb(100%,100%,100%);fill-opacity:1;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 90 40 L 100 40 L 100 50 L 90 50 Z M 90 40 " transform="matrix(1,0,0,1,1,1)"/>
<path style="fill-rule:nonzero;fill:rgb(69.804382%,85.098267%,100%);fill-opacity:1;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 0 50 L 10 50 L 10 60 L 0 60 Z M 0 50 " transform="matrix(1,0,0,1,1,1)"/>
<path style="fill-rule:nonzero;fill:rgb(100%,100%,100%);fill-opacity:1;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 10 50 L 20 50 L 20 60 L 10 60 Z M 10 50 " transform="matrix(1,0,0,1,1,1)"/>
<path style="fill-rule:nonzero;fill:rgb(100%,100%,100%);fill-opacity:1;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 20 50 L 30 50 L 30 60 L 20 60 Z M 20 50 " transform="matrix(1,0,0,1,1,1)"/>
<path style="fill-rule:nonzero;fill:rgb(100%,100%,100%);fill-opacity:1;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 30 50 L 40 50 L 40 60 L 30 60 Z M 30 50 " transform="matrix(1,0,0,1,1,1)"/>
<path style="fill-rule:nonzero;fill:rgb(100%,100%,100%);fill-opacity:1;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 40 50 L 50 50 L 50 60 L 40 60 Z M 40 50 " transform="matrix(1,0,0,1,1,1)"/>
<path style="fill-rule:nonzero;fill:rgb(39.99939%,74.902344%,100%);fill-opacity:1;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 50 50 L 60 50 L 60 60 L 50 60 Z M 50 50 " transform="matrix(1,0,0,1,1,1)"/>
<path style="fill-rule:nonzero;fill:rgb(39.99939%,74.902344%,100%);fill-opacity:1;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 60 50 L 70 50 L 70 60 L 60 60 Z M 60 50 " transform="matrix(1,0,0,1,1,1)"/>
<path style="fill-rule:nonzero;fill:rgb(39.99939%,74.902344%,100%);fill-opacity:1;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 70 50 L 80 50 L 80 60 L 70 60 Z M 70 50 " transform="matrix(1,0,0,1,1,1)"/>
<path style="fill-rule:nonzero;fill:rgb(69.804382%,85.098267%,100%);fill-opacity:1;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 80 50 L 90 50 L 90 60 L 80 60 Z M 80 50 " transform="matrix(1,0,0,1,1,1)"/>
<path style="fill-rule:nonzero;fill:rgb(100%,100%,100%);fill-opacity:1;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 90 50 L 100 50 L 100 60 L 90 60 Z M 90 50 " transform="matrix(1,0,0,1,1,1)"/>
<path style="fill-rule:nonzero;fill:rgb(69.804382%,85.098267%,100%);fill-opacity:1;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 0 60 L 10 60 L 10 70 L 0 70 Z M 0 60 " transform="matrix(1,0,0,1,1,1)"/>
<path style="fill-rule:nonzero;fill:rgb(100%,100%,100%);fill-opacity:1;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 10 60 L 20 60 L 20 70 L 10 70 Z M 10 60 " transform="matrix(1,0,0,1,1,1)"/>
<path style="fill-rule:nonzero;fill:rgb(100%,100%,100%);fill-opacity:1;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 20 60 L 30 60 L 30 70 L 20 70 Z M 20 60 " transform="matrix(1,0,0,1,1,1)"/>
<path style="fill-rule:nonzero;fill:rgb(100%,100%,100%);fill-opacity:1;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 30 60 L 40 60 L 40 70 L 30 70 Z M 30 60 " transform="matrix(1,0,0,1,1,1)"/>
<path style="fill-rule:nonzero;fill:rgb(100%,100%,100%);fill-opacity:1;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 40 60 L 50 60 L 50 70 L 40 70 Z M 40 60 " transform="matrix(1,0,0,1,1,1)"/>
<path style="fill-rule:nonzero;fill:rgb(69.804382%,85.098267%,100%);fill-opacity:1;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 50 60 L 60 60 L 60 70 L 50 70 Z M 50 60 " transform="matrix(1,0,0,1,1,1)"/>
<path style="fill-rule:nonzero;fill:rgb(69.804382%,85.098267%,100%);fill-opacity:1;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 60 60 L 70 60 L 70 70 L 60 70 Z M 60 60 " transform="matrix(1,0,0,1,1,1)"/>
<path style="fill-rule:nonzero;fill:rgb(100%,100%,100%);fill-opacity:1;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 70 60 L 80 60 L 80 70 L 70 70 Z M 70 60 " transform="matrix(1,0,0,1,1,1)"/>
<path style="fill-rule:nonzero;fill:rgb(100%,100%,100%);fill-opacity:1;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 80 60 L 90 60 L 90 70 L 80 70 Z M 80 60 " transform="matrix(1,0,0,1,1,1)"/>
<path style="fill-rule:nonzero;fill:rgb(100%,100%,100%);fill-opacity:1;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 90 60 L 100 60 L 100 70 L 90 70 Z M 90 60 " transform="matrix(1,0,0,1,1,1)"/>
<path style="fill-rule:nonzero;fill:rgb(39.99939%,74.902344%,100%);fill-opacity:1;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 0 70 L 10 70 L 10 80 L 0 80 Z M 0 70 " transform="matrix(1,0,0,1,1,1)"/>
<path style="fill-rule:nonzero;fill:rgb(69.804382%,85.098267%,100%);fill-opacity:1;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 10 70 L 20 70 L 20 80 L 10 80 Z M 10 70 " transform="matrix(1,0,0,1,1,1)"/>
<path style="fill-rule:nonzero;fill:rgb(100%,100%,100%);fill-opacity:1;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 20 70 L 30 70 L 30 80 L 20 80 Z M 20 70 " transform="matrix(1,0,0,1,1,1)"/>
<path style="fill-rule:nonzero;fill:rgb(100%,100%,100%);fill-opacity:1;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 30 70 L 40 70 L 40 80 L 30 80 Z M 30 70 " transform="matrix(1,0,0,1,1,1)"/>
<path style="fill-rule:nonzero;fill:rgb(100%,100%,100%);fill-opacity:1;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 40 70 L 50 70 L 50 80 L 40 80 Z M 40 70 " transform="matrix(1,0,0,1,1,1)"/>
<path style="fill-rule:nonzero;fill:rgb(100%,100%,100%);fill-opacity:1;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 50 70 L 60 70 L 60 80 L 50 80 Z M 50 70 " transform="matrix(1,0,0,1,1,1)"/>
<path style="fill-rule:nonzero;fill:rgb(100%,100%,100%);fill-opacity:1;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 60 70 L 70 70 L 70 80 L 60 80 Z M 60 70 " transform="matrix(1,0,0,1,1,1)"/>
<path style="fill-rule:nonzero;fill:rgb(69.804382%,85.098267%,100%);fill-opacity:1;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 70 70 L 80 70 L 80 80 L 70 80 Z M 70 70 " transform="matrix(1,0,0,1,1,1)"/>
<path style="fill-rule:nonzero;fill:rgb(69.804382%,85.098267%,100%);fill-opacity:1;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 80 70 L 90 70 L 90 80 L 80 80 Z M 80 70 " transform="matrix(1,0,0,1,1,1)"/>
<path style="fill-rule:nonzero;fill:rgb(100%,100%,100%);fill-opacity:1;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 90 70 L 100 70 L 100 80 L 90 80 Z M 90 70 " transform="matrix(1,0,0,1,1,1)"/>
<path style="fill-rule:nonzero;fill:rgb(39.99939%,74.902344%,100%);fill-opacity:1;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 0 80 L 10 80 L 10 90 L 0 90 Z M 0 80 " transform="matrix(1,0,0,1,1,1)"/>
<path style="fill-rule:nonzero;fill:rgb(69.804382%,85.098267%,100%);fill-opacity:1;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 10 80 L 20 80 L 20 90 L 10 90 Z M 10 80 " transform="matrix(1,0,0,1,1,1)"/>
<path style="fill-rule:nonzero;fill:rgb(100%,100%,100%);fill-opacity:1;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 20 80 L 30 80 L 30 90 L 20 90 Z M 20 80 " transform="matrix(1,0,0,1,1,1)"/>
<path style="fill-rule:nonzero;fill:rgb(69.804382%,85.098267%,100%);fill-opacity:1;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 30 80 L 40 80 L 40 90 L 30 90 Z M 30 80 " transform="matrix(1,0,0,1,1,1)"/>
<path style="fill-rule:nonzero;fill:rgb(69.804382%,85.098267%,100%);fill-opacity:1;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 40 80 L 50 80 L 50 90 L 40 90 Z M 40 80 " transform="matrix(1,0,0,1,1,1)"/>
<path style="fill-rule:nonzero;fill:rgb(100%,100%,100%);fill-opacity:1;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 50 80 L 60 80 L 60 90 L 50 90 Z M 50 80 " transform="matrix(1,0,0,1,1,1)"/>
<path style="fill-rule:nonzero;fill:rgb(69.804382%,85.098267%,100%);fill-opacity:1;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 60 80 L 70 80 L 70 90 L 60 90 Z M 60 80 " transform="matrix(1,0,0,1,1,1)"/>
<path style="fill-rule:nonzero;fill:rgb(39.99939%,74.902344%,100%);fill-opacity:1;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 70 80 L 80 80 L 80 90 L 70 90 Z M 70 80 " transform="matrix(1,0,0,1,1,1)"/>
<path style="fill-rule:nonzero;fill:rgb(69.804382%,85.098267%,100%);fill-opacity:1;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 80 80 L 90 80 L 90 90 L 80 90 Z M 80 80 " transform="matrix(1,0,0,1,1,1)"/>
<path style="fill-rule:nonzero;fill:rgb(69.804382%,85.098267%,100%);fill-opacity:1;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 90 80 L 100 80 L 100 90 L 90 90 Z M 90 80 " transform="matrix(1,0,0,1,1,1)"/>
<path style="fill-rule:nonzero;fill:rgb(69.804382%,85.098267%,100%);fill-opacity:1;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 0 90 L 10 90 L 10 100 L 0 100 Z M 0 90 " transform="matrix(1,0,0,1,1,1)"/>
<path style="fill-rule:nonzero;fill:rgb(100%,100%,100%);fill-opacity:1;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 10 90 L 20 90 L 20 100 L 10 100 Z M 10 90 " transform="matrix(1,0,0,1,1,1)"/>
<path style="fill-rule:nonzero;fill:rgb(100%,100%,100%);fill-opacity:1;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 20 90 L 30 90 L 30 100 L 20 100 Z M 20 90 " transform="matrix(1,0,0,1,1,1)"/>
<path style="fill-rule:nonzero;fill:rgb(69.804382%,85.098267%,100%);fill-opacity:1;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 30 90 L 40 90 L 40 100 L 30 100 Z M 30 90 " transform="matrix(1,0,0,1,1,1)"/>
<path style="fill-rule:nonzero;fill:rgb(39.99939%,74.902344%,100%);fill-opacity:1;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 40 90 L 50 90 L 50 100 L 40 100 Z M 40 90 " transform="matrix(1,0,0,1,1,1)"/>
<path style="fill-rule:nonzero;fill:rgb(69.804382%,85.098267%,100%);fill-opacity:1;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 50 90 L 60 90 L 60 100 L 50 100 Z M 50 90 " transform="matrix(1,0,0,1,1,1)"/>
<path style="fill-rule:nonzero;fill:rgb(39.99939%,74.902344%,100%);fill-opacity:1;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 60 90 L 70 90 L 70 100 L 60 100 Z M 60 90 " transform="matrix(1,0,0,1,1,1)"/>
<path style="fill-rule:nonzero;fill:rgb(24.705505%,63.922119%,99.215698%);fill-opacity:1;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 70 90 L 80 90 L 80 100 L 70 100 Z M 70 90 " transform="matrix(1,0,0,1,1,1)"/>
<path style="fill-rule:nonzero;fill:rgb(39.99939%,74.902344%,100%);fill-opacity:1;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 80 90 L 90 90 L 90 100 L 80 100 Z M 80 90 " transform="matrix(1,0,0,1,1,1)"/>
<path style="fill-rule:nonzero;fill:rgb(69.804382%,85.098267%,100%);fill-opacity:1;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 90 90 L 100 90 L 100 100 L 90 100 Z M 90 90 " transform="matrix(1,0,0,1,1,1)"/>
<path style="fill-rule:nonzero;fill:rgb(100%,100%,100%);fill-opacity:1;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 150 0 L 160 0 L 160 10 L 150 10 Z M 150 0 " transform="matrix(1,0,0,1,1,1)"/>
<path style="fill-rule:nonzero;fill:rgb(100%,100%,100%);fill-opacity:1;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 168 0 L 178 0 L 178 10 L 168 10 Z M 168 0 " transform="matrix(1,0,0,1,1,1)"/>
<path style="fill-rule:nonzero;fill:rgb(100%,100%,100%);fill-opacity:1;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 187 0 L 197 0 L 197 10 L 187 10 Z M 187 0 " transform="matrix(1,0,0,1,1,1)"/>
<path style="fill-rule:nonzero;fill:rgb(100%,100%,100%);fill-opacity:1;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 207 0 L 217 0 L 217 10 L 207 10 Z M 207 0 " transform="matrix(1,0,0,1,1,1)"/>
<path style="fill-rule:nonzero;fill:rgb(100%,100%,100%);fill-opacity:1;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 226 0 L 236 0 L 236 10 L 226 10 Z M 226 0 " transform="matrix(1,0,0,1,1,1)"/>
<path style="fill-rule:nonzero;fill:rgb(100%,100%,100%);fill-opacity:1;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 244 0 L 254 0 L 254 10 L 244 10 Z M 244 0 " transform="matrix(1,0,0,1,1,1)"/>
<path style="fill-rule:nonzero;fill:rgb(100%,100%,100%);fill-opacity:1;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 262 0 L 272 0 L 272 10 L 262 10 Z M 262 0 " transform="matrix(1,0,0,1,1,1)"/>
<path style="fill-rule:nonzero;fill:rgb(100%,100%,100%);fill-opacity:1;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 281 0 L 291 0 L 291 10 L 281 10 Z M 281 0 " transform="matrix(1,0,0,1,1,1)"/>
<path style="fill-rule:nonzero;fill:rgb(100%,100%,100%);fill-opacity:1;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 300 0 L 310 0 L 310 10 L 300 10 Z M 300 0 " transform="matrix(1,0,0,1,1,1)"/>
<path style="fill-rule:nonzero;fill:rgb(100%,100%,100%);fill-opacity:1;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 320 0 L 330 0 L 330 10 L 320 10 Z M 320 0 " transform="matrix(1,0,0,1,1,1)"/>
<path style="fill-rule:nonzero;fill:rgb(100%,100%,100%);fill-opacity:1;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 150 10 L 160 10 L 160 20 L 150 20 Z M 150 10 " transform="matrix(1,0,0,1,1,1)"/>
<path style="fill-rule:nonzero;fill:rgb(69.804382%,85.098267%,100%);fill-opacity:1;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 168 10 L 178 10 L 178 20 L 168 20 Z M 168 10 " transform="matrix(1,0,0,1,1,1)"/>
<path style="fill-rule:nonzero;fill:rgb(69.804382%,85.098267%,100%);fill-opacity:1;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 187 10 L 197 10 L 197 20 L 187 20 Z M 187 10 " transform="matrix(1,0,0,1,1,1)"/>
<path style="fill-rule:nonzero;fill:rgb(100%,100%,100%);fill-opacity:1;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 207 10 L 217 10 L 217 20 L 207 20 Z M 207 10 " transform="matrix(1,0,0,1,1,1)"/>
<path style="fill-rule:nonzero;fill:rgb(100%,100%,100%);fill-opacity:1;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 226 10 L 236 10 L 236 20 L 226 20 Z M 226 10 " transform="matrix(1,0,0,1,1,1)"/>
<path style="fill-rule:nonzero;fill:rgb(100%,100%,100%);fill-opacity:1;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 244 10 L 254 10 L 254 20 L 244 20 Z M 244 10 " transform="matrix(1,0,0,1,1,1)"/>
<path style="fill-rule:nonzero;fill:rgb(69.804382%,85.098267%,100%);fill-opacity:1;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 262 10 L 272 10 L 272 20 L 262 20 Z M 262 10 " transform="matrix(1,0,0,1,1,1)"/>
<path style="fill-rule:nonzero;fill:rgb(69.804382%,85.098267%,100%);fill-opacity:1;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 281 10 L 291 10 L 291 20 L 281 20 Z M 281 10 " transform="matrix(1,0,0,1,1,1)"/>
<path style="fill-rule:nonzero;fill:rgb(100%,100%,100%);fill-opacity:1;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 300 10 L 310 10 L 310 20 L 300 20 Z M 300 10 " transform="matrix(1,0,0,1,1,1)"/>
<path style="fill-rule:nonzero;fill:rgb(100%,100%,100%);fill-opacity:1;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 320 10 L 330 10 L 330 20 L 320 20 Z M 320 10 " transform="matrix(1,0,0,1,1,1)"/>
<path style="fill-rule:nonzero;fill:rgb(100%,100%,100%);fill-opacity:1;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 150 20 L 160 20 L 160 30 L 150 30 Z M 150 20 " transform="matrix(1,0,0,1,1,1)"/>
<path style="fill-rule:nonzero;fill:rgb(69.804382%,85.098267%,100%);fill-opacity:1;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 168 20 L 178 20 L 178 30 L 168 30 Z M 168 20 " transform="matrix(1,0,0,1,1,1)"/>
<path style="fill-rule:nonzero;fill:rgb(39.99939%,74.902344%,100%);fill-opacity:1;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 187 20 L 197 20 L 197 30 L 187 30 Z M 187 20 " transform="matrix(1,0,0,1,1,1)"/>
<path style="fill-rule:nonzero;fill:rgb(69.804382%,85.098267%,100%);fill-opacity:1;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 207 20 L 217 20 L 217 30 L 207 30 Z M 207 20 " transform="matrix(1,0,0,1,1,1)"/>
<path style="fill-rule:nonzero;fill:rgb(100%,100%,100%);fill-opacity:1;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 226 20 L 236 20 L 236 30 L 226 30 Z M 226 20 " transform="matrix(1,0,0,1,1,1)"/>
<path style="fill-rule:nonzero;fill:rgb(69.804382%,85.098267%,100%);fill-opacity:1;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 244 20 L 254 20 L 254 30 L 244 30 Z M 244 20 " transform="matrix(1,0,0,1,1,1)"/>
<path style="fill-rule:nonzero;fill:rgb(69.804382%,85.098267%,100%);fill-opacity:1;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 262 20 L 272 20 L 272 30 L 262 30 Z M 262 20 " transform="matrix(1,0,0,1,1,1)"/>
<path style="fill-rule:nonzero;fill:rgb(69.804382%,85.098267%,100%);fill-opacity:1;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 281 20 L 291 20 L 291 30 L 281 30 Z M 281 20 " transform="matrix(1,0,0,1,1,1)"/>
<path style="fill-rule:nonzero;fill:rgb(100%,100%,100%);fill-opacity:1;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 300 20 L 310 20 L 310 30 L 300 30 Z M 300 20 " transform="matrix(1,0,0,1,1,1)"/>
<path style="fill-rule:nonzero;fill:rgb(100%,100%,100%);fill-opacity:1;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 320 20 L 330 20 L 330 30 L 320 30 Z M 320 20 " transform="matrix(1,0,0,1,1,1)"/>
<path style="fill-rule:nonzero;fill:rgb(100%,100%,100%);fill-opacity:1;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 150 30 L 160 30 L 160 40 L 150 40 Z M 150 30 " transform="matrix(1,0,0,1,1,1)"/>
<path style="fill-rule:nonzero;fill:rgb(100%,100%,100%);fill-opacity:1;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 168 30 L 178 30 L 178 40 L 168 40 Z M 168 30 " transform="matrix(1,0,0,1,1,1)"/>
<path style="fill-rule:nonzero;fill:rgb(69.804382%,85.098267%,100%);fill-opacity:1;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 187 30 L 197 30 L 197 40 L 187 40 Z M 187 30 " transform="matrix(1,0,0,1,1,1)"/>
<path style="fill-rule:nonzero;fill:rgb(69.804382%,85.098267%,100%);fill-opacity:1;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 207 30 L 217 30 L 217 40 L 207 40 Z M 207 30 " transform="matrix(1,0,0,1,1,1)"/>
<path style="fill-rule:nonzero;fill:rgb(100%,100%,100%);fill-opacity:1;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 226 30 L 236 30 L 236 40 L 226 40 Z M 226 30 " transform="matrix(1,0,0,1,1,1)"/>
<path style="fill-rule:nonzero;fill:rgb(69.804382%,85.098267%,100%);fill-opacity:1;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 244 30 L 254 30 L 254 40 L 244 40 Z M 244 30 " transform="matrix(1,0,0,1,1,1)"/>
<path style="fill-rule:nonzero;fill:rgb(39.99939%,74.902344%,100%);fill-opacity:1;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 262 30 L 272 30 L 272 40 L 262 40 Z M 262 30 " transform="matrix(1,0,0,1,1,1)"/>
<path style="fill-rule:nonzero;fill:rgb(39.99939%,74.902344%,100%);fill-opacity:1;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 281 30 L 291 30 L 291 40 L 281 40 Z M 281 30 " transform="matrix(1,0,0,1,1,1)"/>
<path style="fill-rule:nonzero;fill:rgb(69.804382%,85.098267%,100%);fill-opacity:1;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 300 30 L 310 30 L 310 40 L 300 40 Z M 300 30 " transform="matrix(1,0,0,1,1,1)"/>
<path style="fill-rule:nonzero;fill:rgb(100%,100%,100%);fill-opacity:1;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 320 30 L 330 30 L 330 40 L 320 40 Z M 320 30 " transform="matrix(1,0,0,1,1,1)"/>
<path style="fill-rule:nonzero;fill:rgb(69.804382%,85.098267%,100%);fill-opacity:1;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 150 40 L 160 40 L 160 50 L 150 50 Z M 150 40 " transform="matrix(1,0,0,1,1,1)"/>
<path style="fill-rule:nonzero;fill:rgb(100%,100%,100%);fill-opacity:1;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 168 40 L 178 40 L 178 50 L 168 50 Z M 168 40 " transform="matrix(1,0,0,1,1,1)"/>
<path style="fill-rule:nonzero;fill:rgb(100%,100%,100%);fill-opacity:1;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 187 40 L 197 40 L 197 50 L 187 50 Z M 187 40 " transform="matrix(1,0,0,1,1,1)"/>
<path style="fill-rule:nonzero;fill:rgb(100%,100%,100%);fill-opacity:1;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 207 40 L 217 40 L 217 50 L 207 50 Z M 207 40 " transform="matrix(1,0,0,1,1,1)"/>
<path style="fill-rule:nonzero;fill:rgb(69.804382%,85.098267%,100%);fill-opacity:1;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 226 40 L 236 40 L 236 50 L 226 50 Z M 226 40 " transform="matrix(1,0,0,1,1,1)"/>
<path style="fill-rule:nonzero;fill:rgb(39.99939%,74.902344%,100%);fill-opacity:1;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 244 40 L 254 40 L 254 50 L 244 50 Z M 244 40 " transform="matrix(1,0,0,1,1,1)"/>
<path style="fill-rule:nonzero;fill:rgb(24.705505%,63.922119%,99.215698%);fill-opacity:1;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 262 40 L 272 40 L 272 50 L 262 50 Z M 262 40 " transform="matrix(1,0,0,1,1,1)"/>
<path style="fill-rule:nonzero;fill:rgb(39.99939%,74.902344%,100%);fill-opacity:1;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 281 40 L 291 40 L 291 50 L 281 50 Z M 281 40 " transform="matrix(1,0,0,1,1,1)"/>
<path style="fill-rule:nonzero;fill:rgb(69.804382%,85.098267%,100%);fill-opacity:1;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 300 40 L 310 40 L 310 50 L 300 50 Z M 300 40 " transform="matrix(1,0,0,1,1,1)"/>
<path style="fill-rule:nonzero;fill:rgb(100%,100%,100%);fill-opacity:1;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 320 40 L 330 40 L 330 50 L 320 50 Z M 320 40 " transform="matrix(1,0,0,1,1,1)"/>
<path style="fill-rule:nonzero;fill:rgb(69.804382%,85.098267%,100%);fill-opacity:1;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 150 50 L 160 50 L 160 60 L 150 60 Z M 150 50 " transform="matrix(1,0,0,1,1,1)"/>
<path style="fill-rule:nonzero;fill:rgb(100%,100%,100%);fill-opacity:1;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 168 50 L 178 50 L 178 60 L 168 60 Z M 168 50 " transform="matrix(1,0,0,1,1,1)"/>
<path style="fill-rule:nonzero;fill:rgb(100%,100%,100%);fill-opacity:1;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 187 50 L 197 50 L 197 60 L 187 60 Z M 187 50 " transform="matrix(1,0,0,1,1,1)"/>
<path style="fill-rule:nonzero;fill:rgb(100%,100%,100%);fill-opacity:1;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 207 50 L 217 50 L 217 60 L 207 60 Z M 207 50 " transform="matrix(1,0,0,1,1,1)"/>
<path style="fill-rule:nonzero;fill:rgb(100%,100%,100%);fill-opacity:1;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 226 50 L 236 50 L 236 60 L 226 60 Z M 226 50 " transform="matrix(1,0,0,1,1,1)"/>
<path style="fill-rule:nonzero;fill:rgb(39.99939%,74.902344%,100%);fill-opacity:1;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 244 50 L 254 50 L 254 60 L 244 60 Z M 244 50 " transform="matrix(1,0,0,1,1,1)"/>
<path style="fill-rule:nonzero;fill:rgb(39.99939%,74.902344%,100%);fill-opacity:1;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 262 50 L 272 50 L 272 60 L 262 60 Z M 262 50 " transform="matrix(1,0,0,1,1,1)"/>
<path style="fill-rule:nonzero;fill:rgb(39.99939%,74.902344%,100%);fill-opacity:1;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 281 50 L 291 50 L 291 60 L 281 60 Z M 281 50 " transform="matrix(1,0,0,1,1,1)"/>
<path style="fill-rule:nonzero;fill:rgb(69.804382%,85.098267%,100%);fill-opacity:1;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 300 50 L 310 50 L 310 60 L 300 60 Z M 300 50 " transform="matrix(1,0,0,1,1,1)"/>
<path style="fill-rule:nonzero;fill:rgb(100%,100%,100%);fill-opacity:1;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 320 50 L 330 50 L 330 60 L 320 60 Z M 320 50 " transform="matrix(1,0,0,1,1,1)"/>
<path style="fill-rule:nonzero;fill:rgb(69.804382%,85.098267%,100%);fill-opacity:1;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 150 60 L 160 60 L 160 70 L 150 70 Z M 150 60 " transform="matrix(1,0,0,1,1,1)"/>
<path style="fill-rule:nonzero;fill:rgb(100%,100%,100%);fill-opacity:1;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 168 60 L 178 60 L 178 70 L 168 70 Z M 168 60 " transform="matrix(1,0,0,1,1,1)"/>
<path style="fill-rule:nonzero;fill:rgb(100%,100%,100%);fill-opacity:1;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 187 60 L 197 60 L 197 70 L 187 70 Z M 187 60 " transform="matrix(1,0,0,1,1,1)"/>
<path style="fill-rule:nonzero;fill:rgb(100%,100%,100%);fill-opacity:1;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 207 60 L 217 60 L 217 70 L 207 70 Z M 207 60 " transform="matrix(1,0,0,1,1,1)"/>
<path style="fill-rule:nonzero;fill:rgb(100%,100%,100%);fill-opacity:1;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 226 60 L 236 60 L 236 70 L 226 70 Z M 226 60 " transform="matrix(1,0,0,1,1,1)"/>
<path style="fill-rule:nonzero;fill:rgb(69.804382%,85.098267%,100%);fill-opacity:1;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 244 60 L 254 60 L 254 70 L 244 70 Z M 244 60 " transform="matrix(1,0,0,1,1,1)"/>
<path style="fill-rule:nonzero;fill:rgb(69.804382%,85.098267%,100%);fill-opacity:1;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 262 60 L 272 60 L 272 70 L 262 70 Z M 262 60 " transform="matrix(1,0,0,1,1,1)"/>
<path style="fill-rule:nonzero;fill:rgb(100%,100%,100%);fill-opacity:1;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 281 60 L 291 60 L 291 70 L 281 70 Z M 281 60 " transform="matrix(1,0,0,1,1,1)"/>
<path style="fill-rule:nonzero;fill:rgb(100%,100%,100%);fill-opacity:1;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 300 60 L 310 60 L 310 70 L 300 70 Z M 300 60 " transform="matrix(1,0,0,1,1,1)"/>
<path style="fill-rule:nonzero;fill:rgb(100%,100%,100%);fill-opacity:1;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 320 60 L 330 60 L 330 70 L 320 70 Z M 320 60 " transform="matrix(1,0,0,1,1,1)"/>
<path style="fill-rule:nonzero;fill:rgb(39.99939%,74.902344%,100%);fill-opacity:1;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 150 70 L 160 70 L 160 80 L 150 80 Z M 150 70 " transform="matrix(1,0,0,1,1,1)"/>
<path style="fill-rule:nonzero;fill:rgb(69.804382%,85.098267%,100%);fill-opacity:1;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 168 70 L 178 70 L 178 80 L 168 80 Z M 168 70 " transform="matrix(1,0,0,1,1,1)"/>
<path style="fill-rule:nonzero;fill:rgb(100%,100%,100%);fill-opacity:1;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 187 70 L 197 70 L 197 80 L 187 80 Z M 187 70 " transform="matrix(1,0,0,1,1,1)"/>
<path style="fill-rule:nonzero;fill:rgb(100%,100%,100%);fill-opacity:1;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 207 70 L 217 70 L 217 80 L 207 80 Z M 207 70 " transform="matrix(1,0,0,1,1,1)"/>
<path style="fill-rule:nonzero;fill:rgb(100%,100%,100%);fill-opacity:1;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 226 70 L 236 70 L 236 80 L 226 80 Z M 226 70 " transform="matrix(1,0,0,1,1,1)"/>
<path style="fill-rule:nonzero;fill:rgb(100%,100%,100%);fill-opacity:1;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 244 70 L 254 70 L 254 80 L 244 80 Z M 244 70 " transform="matrix(1,0,0,1,1,1)"/>
<path style="fill-rule:nonzero;fill:rgb(100%,100%,100%);fill-opacity:1;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 262 70 L 272 70 L 272 80 L 262 80 Z M 262 70 " transform="matrix(1,0,0,1,1,1)"/>
<path style="fill-rule:nonzero;fill:rgb(69.804382%,85.098267%,100%);fill-opacity:1;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 281 70 L 291 70 L 291 80 L 281 80 Z M 281 70 " transform="matrix(1,0,0,1,1,1)"/>
<path style="fill-rule:nonzero;fill:rgb(69.804382%,85.098267%,100%);fill-opacity:1;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 300 70 L 310 70 L 310 80 L 300 80 Z M 300 70 " transform="matrix(1,0,0,1,1,1)"/>
<path style="fill-rule:nonzero;fill:rgb(100%,100%,100%);fill-opacity:1;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 320 70 L 330 70 L 330 80 L 320 80 Z M 320 70 " transform="matrix(1,0,0,1,1,1)"/>
<path style="fill-rule:nonzero;fill:rgb(39.99939%,74.902344%,100%);fill-opacity:1;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 150 80 L 160 80 L 160 90 L 150 90 Z M 150 80 " transform="matrix(1,0,0,1,1,1)"/>
<path style="fill-rule:nonzero;fill:rgb(69.804382%,85.098267%,100%);fill-opacity:1;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 168 80 L 178 80 L 178 90 L 168 90 Z M 168 80 " transform="matrix(1,0,0,1,1,1)"/>
<path style="fill-rule:nonzero;fill:rgb(100%,100%,100%);fill-opacity:1;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 187 80 L 197 80 L 197 90 L 187 90 Z M 187 80 " transform="matrix(1,0,0,1,1,1)"/>
<path style="fill-rule:nonzero;fill:rgb(69.804382%,85.098267%,100%);fill-opacity:1;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 207 80 L 217 80 L 217 90 L 207 90 Z M 207 80 " transform="matrix(1,0,0,1,1,1)"/>
<path style="fill-rule:nonzero;fill:rgb(69.804382%,85.098267%,100%);fill-opacity:1;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 226 80 L 236 80 L 236 90 L 226 90 Z M 226 80 " transform="matrix(1,0,0,1,1,1)"/>
<path style="fill-rule:nonzero;fill:rgb(100%,100%,100%);fill-opacity:1;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 244 80 L 254 80 L 254 90 L 244 90 Z M 244 80 " transform="matrix(1,0,0,1,1,1)"/>
<path style="fill-rule:nonzero;fill:rgb(69.804382%,85.098267%,100%);fill-opacity:1;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 262 80 L 272 80 L 272 90 L 262 90 Z M 262 80 " transform="matrix(1,0,0,1,1,1)"/>
<path style="fill-rule:nonzero;fill:rgb(39.99939%,74.902344%,100%);fill-opacity:1;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 281 80 L 291 80 L 291 90 L 281 90 Z M 281 80 " transform="matrix(1,0,0,1,1,1)"/>
<path style="fill-rule:nonzero;fill:rgb(69.804382%,85.098267%,100%);fill-opacity:1;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 300 80 L 310 80 L 310 90 L 300 90 Z M 300 80 " transform="matrix(1,0,0,1,1,1)"/>
<path style="fill-rule:nonzero;fill:rgb(69.804382%,85.098267%,100%);fill-opacity:1;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 320 80 L 330 80 L 330 90 L 320 90 Z M 320 80 " transform="matrix(1,0,0,1,1,1)"/>
<path style="fill-rule:nonzero;fill:rgb(69.804382%,85.098267%,100%);fill-opacity:1;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 150 90 L 160 90 L 160 100 L 150 100 Z M 150 90 " transform="matrix(1,0,0,1,1,1)"/>
<path style="fill-rule:nonzero;fill:rgb(100%,100%,100%);fill-opacity:1;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 168 90 L 178 90 L 178 100 L 168 100 Z M 168 90 " transform="matrix(1,0,0,1,1,1)"/>
<path style="fill-rule:nonzero;fill:rgb(100%,100%,100%);fill-opacity:1;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 187 90 L 197 90 L 197 100 L 187 100 Z M 187 90 " transform="matrix(1,0,0,1,1,1)"/>
<path style="fill-rule:nonzero;fill:rgb(69.804382%,85.098267%,100%);fill-opacity:1;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 207 90 L 217 90 L 217 100 L 207 100 Z M 207 90 " transform="matrix(1,0,0,1,1,1)"/>
<path style="fill-rule:nonzero;fill:rgb(39.99939%,74.902344%,100%);fill-opacity:1;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 226 90 L 236 90 L 236 100 L 226 100 Z M 226 90 " transform="matrix(1,0,0,1,1,1)"/>
<path style="fill-rule:nonzero;fill:rgb(69.804382%,85.098267%,100%);fill-opacity:1;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 244 90 L 254 90 L 254 100 L 244 100 Z M 244 90 " transform="matrix(1,0,0,1,1,1)"/>
<path style="fill-rule:nonzero;fill:rgb(39.99939%,74.902344%,100%);fill-opacity:1;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 262 90 L 272 90 L 272 100 L 262 100 Z M 262 90 " transform="matrix(1,0,0,1,1,1)"/>
<path style="fill-rule:nonzero;fill:rgb(24.705505%,63.922119%,99.215698%);fill-opacity:1;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 281 90 L 291 90 L 291 100 L 281 100 Z M 281 90 " transform="matrix(1,0,0,1,1,1)"/>
<path style="fill-rule:nonzero;fill:rgb(39.99939%,74.902344%,100%);fill-opacity:1;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 300 90 L 310 90 L 310 100 L 300 100 Z M 300 90 " transform="matrix(1,0,0,1,1,1)"/>
<path style="fill-rule:nonzero;fill:rgb(69.804382%,85.098267%,100%);fill-opacity:1;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 320 90 L 330 90 L 330 100 L 320 100 Z M 320 90 " transform="matrix(1,0,0,1,1,1)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:butt;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 163.5 4 L 163.5 90.101562 " transform="matrix(1,0,0,1,1,1)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:butt;stroke-linejoin:miter;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 163.5 94.101562 L 163.5 90.101562 M 165 90.101562 L 163.5 94.101562 L 162 90.101562 " transform="matrix(1,0,0,1,1,1)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:butt;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 182.5 4 L 182.5 90.101562 " transform="matrix(1,0,0,1,1,1)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:butt;stroke-linejoin:miter;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 182.5 94.101562 L 182.5 90.101562 M 184 90.101562 L 182.5 94.101562 L 181 90.101562 " transform="matrix(1,0,0,1,1,1)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:butt;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 221 4 L 221 90.101562 " transform="matrix(1,0,0,1,1,1)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:butt;stroke-linejoin:miter;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 221 94.101562 L 221 90.101562 M 222.5 90.101562 L 221 94.101562 L 219.5 90.101562 " transform="matrix(1,0,0,1,1,1)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:butt;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 257.5 4 L 257.5 90.101562 " transform="matrix(1,0,0,1,1,1)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:butt;stroke-linejoin:miter;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 257.5 94.101562 L 257.5 90.101562 M 259 90.101562 L 257.5 94.101562 L 256 90.101562 " transform="matrix(1,0,0,1,1,1)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:butt;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 201.5 4 L 201.5 90.101562 " transform="matrix(1,0,0,1,1,1)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:butt;stroke-linejoin:miter;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 201.5 94.101562 L 201.5 90.101562 M 203 90.101562 L 201.5 94.101562 L 200 90.101562 " transform="matrix(1,0,0,1,1,1)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:butt;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 240 4 L 240 90.101562 " transform="matrix(1,0,0,1,1,1)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:butt;stroke-linejoin:miter;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 240 94.101562 L 240 90.101562 M 241.5 90.101562 L 240 94.101562 L 238.5 90.101562 " transform="matrix(1,0,0,1,1,1)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:butt;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 276.5 4 L 276.5 90.101562 " transform="matrix(1,0,0,1,1,1)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:butt;stroke-linejoin:miter;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 276.5 94.101562 L 276.5 90.101562 M 278 90.101562 L 276.5 94.101562 L 275 90.101562 " transform="matrix(1,0,0,1,1,1)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:butt;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 295.5 4 L 295.5 90.101562 " transform="matrix(1,0,0,1,1,1)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:butt;stroke-linejoin:miter;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 295.5 94.101562 L 295.5 90.101562 M 297 90.101562 L 295.5 94.101562 L 294 90.101562 " transform="matrix(1,0,0,1,1,1)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:butt;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 314.5 4 L 314.5 90.101562 " transform="matrix(1,0,0,1,1,1)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:butt;stroke-linejoin:miter;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 314.5 94.101562 L 314.5 90.101562 M 316 90.101562 L 314.5 94.101562 L 313 90.101562 " transform="matrix(1,0,0,1,1,1)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:butt;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 333.5 4 L 333.5 90.101562 " transform="matrix(1,0,0,1,1,1)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:butt;stroke-linejoin:miter;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 333.5 94.101562 L 333.5 90.101562 M 335 90.101562 L 333.5 94.101562 L 332 90.101562 " transform="matrix(1,0,0,1,1,1)"/>
<g style="fill:rgb(0%,0%,0%);fill-opacity:1;">
  <use xlink:href="#glyph0-1" x="337" y="54"/>
</g>
<path style="fill:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 163 104 C 163 104 297.5625 104 327.601562 104 " transform="matrix(1,0,0,1,1,1)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:butt;stroke-linejoin:miter;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 331.601562 104 L 327.601562 104 M 327.601562 102.5 L 331.601562 104 L 327.601562 105.5 " transform="matrix(1,0,0,1,1,1)"/>
<g style="fill:rgb(0%,0%,0%);fill-opacity:1;">
  <use xlink:href="#glyph0-2" x="247.75" y="114"/>
</g>
<g style="fill:rgb(0%,0%,0%);fill-opacity:1;">
  <use xlink:href="#glyph0-3" x="122.9175" y="53"/>
</g>
</g>
</svg>
