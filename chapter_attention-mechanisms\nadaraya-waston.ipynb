{"cells": [{"cell_type": "markdown", "id": "63d0bab3", "metadata": {"origin_pos": 0}, "source": ["# 注意力汇聚：Nadaraya-Watson 核回归\n", ":label:`sec_nadar<PERSON>-watson`\n", "\n", "上节介绍了框架下的注意力机制的主要成分 :numref:`fig_qkv`：\n", "查询（自主提示）和键（非自主提示）之间的交互形成了注意力汇聚；\n", "注意力汇聚有选择地聚合了值（感官输入）以生成最终的输出。\n", "本节将介绍注意力汇聚的更多细节，\n", "以便从宏观上了解注意力机制在实践中的运作方式。\n", "具体来说，1964年提出的Nadaraya-Watson核回归模型\n", "是一个简单但完整的例子，可以用于演示具有注意力机制的机器学习。\n"]}, {"cell_type": "code", "execution_count": 1, "id": "ddecad34", "metadata": {"execution": {"iopub.execute_input": "2022-12-07T17:03:14.607385Z", "iopub.status.busy": "2022-12-07T17:03:14.607078Z", "iopub.status.idle": "2022-12-07T17:03:16.977517Z", "shell.execute_reply": "2022-12-07T17:03:16.976672Z"}, "origin_pos": 2, "tab": ["pytorch"]}, "outputs": [], "source": ["import torch\n", "from torch import nn\n", "from d2l import torch as d2l"]}, {"cell_type": "markdown", "id": "16fe0b40", "metadata": {"origin_pos": 5}, "source": ["## [**生成数据集**]\n", "\n", "简单起见，考虑下面这个回归问题：\n", "给定的成对的“输入－输出”数据集\n", "$\\{(x_1, y_1), \\ldots, (x_n, y_n)\\}$，\n", "如何学习$f$来预测任意新输入$x$的输出$\\hat{y} = f(x)$？\n", "\n", "根据下面的非线性函数生成一个人工数据集，\n", "其中加入的噪声项为$\\epsilon$：\n", "\n", "$$y_i = 2\\sin(x_i) + x_i^{0.8} + \\epsilon,$$\n", "\n", "其中$\\epsilon$服从均值为$0$和标准差为$0.5$的正态分布。\n", "在这里生成了$50$个训练样本和$50$个测试样本。\n", "为了更好地可视化之后的注意力模式，需要将训练样本进行排序。\n"]}, {"cell_type": "code", "execution_count": 2, "id": "0ce60ca4", "metadata": {"execution": {"iopub.execute_input": "2022-12-07T17:03:16.981445Z", "iopub.status.busy": "2022-12-07T17:03:16.981067Z", "iopub.status.idle": "2022-12-07T17:03:17.006494Z", "shell.execute_reply": "2022-12-07T17:03:17.005633Z"}, "origin_pos": 7, "tab": ["pytorch"]}, "outputs": [], "source": ["n_train = 50  # 训练样本数\n", "x_train, _ = torch.sort(torch.rand(n_train) * 5)   # 排序后的训练样本"]}, {"cell_type": "code", "execution_count": 3, "id": "497c4599", "metadata": {"execution": {"iopub.execute_input": "2022-12-07T17:03:17.010038Z", "iopub.status.busy": "2022-12-07T17:03:17.009746Z", "iopub.status.idle": "2022-12-07T17:03:17.018830Z", "shell.execute_reply": "2022-12-07T17:03:17.018059Z"}, "origin_pos": 11, "tab": ["pytorch"]}, "outputs": [{"data": {"text/plain": ["50"]}, "execution_count": 3, "metadata": {}, "output_type": "execute_result"}], "source": ["def f(x):\n", "    return 2 * torch.sin(x) + x**0.8\n", "\n", "y_train = f(x_train) + torch.normal(0.0, 0.5, (n_train,))  # 训练样本的输出\n", "x_test = torch.arange(0, 5, 0.1)  # 测试样本\n", "y_truth = f(x_test)  # 测试样本的真实输出\n", "n_test = len(x_test)  # 测试样本数\n", "n_test"]}, {"cell_type": "markdown", "id": "695dd82b", "metadata": {"origin_pos": 14}, "source": ["下面的函数将绘制所有的训练样本（样本由圆圈表示），\n", "不带噪声项的真实数据生成函数$f$（标记为“Truth”），\n", "以及学习得到的预测函数（标记为“Pred”）。\n"]}, {"cell_type": "code", "execution_count": 4, "id": "d00deac2", "metadata": {"execution": {"iopub.execute_input": "2022-12-07T17:03:17.022077Z", "iopub.status.busy": "2022-12-07T17:03:17.021640Z", "iopub.status.idle": "2022-12-07T17:03:17.026183Z", "shell.execute_reply": "2022-12-07T17:03:17.025450Z"}, "origin_pos": 15, "tab": ["pytorch"]}, "outputs": [], "source": ["def plot_kernel_reg(y_hat):\n", "    d2l.plot(x_test, [y_truth, y_hat], 'x', 'y', legend=['Truth', 'Pred'],\n", "             xlim=[0, 5], ylim=[-1, 5])\n", "    d2l.plt.plot(x_train, y_train, 'o', alpha=0.5);"]}, {"cell_type": "markdown", "id": "59ebd861", "metadata": {"origin_pos": 16}, "source": ["## 平均汇聚\n", "\n", "先使用最简单的估计器来解决回归问题。\n", "基于平均汇聚来计算所有训练样本输出值的平均值：\n", "\n", "$$f(x) = \\frac{1}{n}\\sum_{i=1}^n y_i,$$\n", ":eqlabel:`eq_avg-pooling`\n", "\n", "如下图所示，这个估计器确实不够聪明。\n", "真实函数$f$（“Truth”）和预测函数（“Pred”）相差很大。\n"]}, {"cell_type": "code", "execution_count": 5, "id": "aba1c574", "metadata": {"execution": {"iopub.execute_input": "2022-12-07T17:03:17.029370Z", "iopub.status.busy": "2022-12-07T17:03:17.028949Z", "iopub.status.idle": "2022-12-07T17:03:17.230922Z", "shell.execute_reply": "2022-12-07T17:03:17.230081Z"}, "origin_pos": 18, "tab": ["pytorch"]}, "outputs": [{"data": {"image/svg+xml": ["<?xml version=\"1.0\" encoding=\"utf-8\" standalone=\"no\"?>\n", "<!DOCTYPE svg PUBLIC \"-//W3C//DTD SVG 1.1//EN\"\n", "  \"http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd\">\n", "<svg xmlns:xlink=\"http://www.w3.org/1999/xlink\" width=\"248.301563pt\" height=\"187.155469pt\" viewBox=\"0 0 248.**********.155469\" xmlns=\"http://www.w3.org/2000/svg\" version=\"1.1\">\n", " <metadata>\n", "  <rdf:RDF xmlns:dc=\"http://purl.org/dc/elements/1.1/\" xmlns:cc=\"http://creativecommons.org/ns#\" xmlns:rdf=\"http://www.w3.org/1999/02/22-rdf-syntax-ns#\">\n", "   <cc:Work>\n", "    <dc:type rdf:resource=\"http://purl.org/dc/dcmitype/StillImage\"/>\n", "    <dc:date>2022-12-07T17:03:17.181198</dc:date>\n", "    <dc:format>image/svg+xml</dc:format>\n", "    <dc:creator>\n", "     <cc:Agent>\n", "      <dc:title>Matplotlib v3.5.1, https://matplotlib.org/</dc:title>\n", "     </cc:Agent>\n", "    </dc:creator>\n", "   </cc:Work>\n", "  </rdf:RDF>\n", " </metadata>\n", " <defs>\n", "  <style type=\"text/css\">*{stroke-linejoin: round; stroke-linecap: butt}</style>\n", " </defs>\n", " <g id=\"figure_1\">\n", "  <g id=\"patch_1\">\n", "   <path d=\"M 0 187.155469 \n", "L 248.**********.155469 \n", "L 248.301563 0 \n", "L 0 0 \n", "z\n", "\" style=\"fill: #ffffff\"/>\n", "  </g>\n", "  <g id=\"axes_1\">\n", "   <g id=\"patch_2\">\n", "    <path d=\"M 42.**********.599219 \n", "L 237.**********.599219 \n", "L 237.920313 10.999219 \n", "L 42.620312 10.999219 \n", "z\n", "\" style=\"fill: #ffffff\"/>\n", "   </g>\n", "   <g id=\"matplotlib.axis_1\">\n", "    <g id=\"xtick_1\">\n", "     <g id=\"line2d_1\">\n", "      <path d=\"M 42.**********.599219 \n", "L 42.620312 10.999219 \n", "\" clip-path=\"url(#pb0a4204e0b)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_2\">\n", "      <defs>\n", "       <path id=\"m768fa5918f\" d=\"M 0 0 \n", "L 0 3.5 \n", "\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </defs>\n", "      <g>\n", "       <use xlink:href=\"#m768fa5918f\" x=\"42.620312\" y=\"149.599219\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_1\">\n", "      <!-- 0 -->\n", "      <g transform=\"translate(39.439062 164.197656)scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-30\" d=\"M 2034 4250 \n", "Q 1547 4250 1301 3770 \n", "Q 1056 3291 1056 2328 \n", "Q 1056 1369 1301 889 \n", "Q 1547 409 2034 409 \n", "Q 2525 409 2770 889 \n", "Q 3016 1369 3016 2328 \n", "Q 3016 3291 2770 3770 \n", "Q 2525 4250 2034 4250 \n", "z\n", "M 2034 4750 \n", "Q 2819 4750 3233 4129 \n", "Q 3647 3509 3647 2328 \n", "Q 3647 1150 3233 529 \n", "Q 2819 -91 2034 -91 \n", "Q 1250 -91 836 529 \n", "Q 422 1150 422 2328 \n", "Q 422 3509 836 4129 \n", "Q 1250 4750 2034 4750 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_2\">\n", "     <g id=\"line2d_3\">\n", "      <path d=\"M 81.680312 149.599219 \n", "L 81.680312 10.999219 \n", "\" clip-path=\"url(#pb0a4204e0b)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_4\">\n", "      <g>\n", "       <use xlink:href=\"#m768fa5918f\" x=\"81.680312\" y=\"149.599219\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_2\">\n", "      <!-- 1 -->\n", "      <g transform=\"translate(78.499062 164.197656)scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-31\" d=\"M 794 531 \n", "L 1825 531 \n", "L 1825 4091 \n", "L 703 3866 \n", "L 703 4441 \n", "L 1819 4666 \n", "L 2450 4666 \n", "L 2450 531 \n", "L 3481 531 \n", "L 3481 0 \n", "L 794 0 \n", "L 794 531 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-31\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_3\">\n", "     <g id=\"line2d_5\">\n", "      <path d=\"M 120.740313 149.599219 \n", "L 120.740313 10.999219 \n", "\" clip-path=\"url(#pb0a4204e0b)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_6\">\n", "      <g>\n", "       <use xlink:href=\"#m768fa5918f\" x=\"120.740313\" y=\"149.599219\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_3\">\n", "      <!-- 2 -->\n", "      <g transform=\"translate(117.559062 164.197656)scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-32\" d=\"M 1228 531 \n", "L 3431 531 \n", "L 3431 0 \n", "L 469 0 \n", "L 469 531 \n", "Q 828 903 1448 1529 \n", "Q 2069 2156 2228 2338 \n", "Q 2531 2678 2651 2914 \n", "Q 2772 3150 2772 3378 \n", "Q 2772 3750 2511 3984 \n", "Q 2250 4219 1831 4219 \n", "Q 1534 4219 1204 4116 \n", "Q 875 4013 500 3803 \n", "L 500 4441 \n", "Q 881 4594 1212 4672 \n", "Q 1544 4750 1819 4750 \n", "Q 2544 4750 2975 4387 \n", "Q 3406 4025 3406 3419 \n", "Q 3406 3131 3298 2873 \n", "Q 3191 2616 2906 2266 \n", "Q 2828 2175 2409 1742 \n", "Q 1991 1309 1228 531 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-32\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_4\">\n", "     <g id=\"line2d_7\">\n", "      <path d=\"M 159.800313 149.599219 \n", "L 159.800313 10.999219 \n", "\" clip-path=\"url(#pb0a4204e0b)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_8\">\n", "      <g>\n", "       <use xlink:href=\"#m768fa5918f\" x=\"159.800313\" y=\"149.599219\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_4\">\n", "      <!-- 3 -->\n", "      <g transform=\"translate(156.619063 164.197656)scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-33\" d=\"M 2597 2516 \n", "Q 3050 2419 3304 2112 \n", "Q 3559 1806 3559 1356 \n", "Q 3559 666 3084 287 \n", "Q 2609 -91 1734 -91 \n", "Q 1441 -91 1130 -33 \n", "Q 819 25 488 141 \n", "L 488 750 \n", "Q 750 597 1062 519 \n", "Q 1375 441 1716 441 \n", "Q 2309 441 2620 675 \n", "Q 2931 909 2931 1356 \n", "Q 2931 1769 2642 2001 \n", "Q 2353 2234 1838 2234 \n", "L 1294 2234 \n", "L 1294 2753 \n", "L 1863 2753 \n", "Q 2328 2753 2575 2939 \n", "Q 2822 3125 2822 3475 \n", "Q 2822 3834 2567 4026 \n", "Q 2313 4219 1838 4219 \n", "Q 1578 4219 1281 4162 \n", "Q 984 4106 628 3988 \n", "L 628 4550 \n", "Q 988 4650 1302 4700 \n", "Q 1616 4750 1894 4750 \n", "Q 2613 4750 3031 4423 \n", "Q 3450 4097 3450 3541 \n", "Q 3450 3153 3228 2886 \n", "Q 3006 2619 2597 2516 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-33\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_5\">\n", "     <g id=\"line2d_9\">\n", "      <path d=\"M 198.860313 149.599219 \n", "L 198.860313 10.999219 \n", "\" clip-path=\"url(#pb0a4204e0b)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_10\">\n", "      <g>\n", "       <use xlink:href=\"#m768fa5918f\" x=\"198.860313\" y=\"149.599219\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_5\">\n", "      <!-- 4 -->\n", "      <g transform=\"translate(195.679063 164.197656)scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-34\" d=\"M 2419 4116 \n", "L 825 1625 \n", "L 2419 1625 \n", "L 2419 4116 \n", "z\n", "M 2253 4666 \n", "L 3047 4666 \n", "L 3047 1625 \n", "L 3713 1625 \n", "L 3713 1100 \n", "L 3047 1100 \n", "L 3047 0 \n", "L 2419 0 \n", "L 2419 1100 \n", "L 313 1100 \n", "L 313 1709 \n", "L 2253 4666 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-34\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_6\">\n", "     <g id=\"line2d_11\">\n", "      <path d=\"M 237.**********.599219 \n", "L 237.920313 10.999219 \n", "\" clip-path=\"url(#pb0a4204e0b)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_12\">\n", "      <g>\n", "       <use xlink:href=\"#m768fa5918f\" x=\"237.920313\" y=\"149.599219\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_6\">\n", "      <!-- 5 -->\n", "      <g transform=\"translate(234.739063 164.197656)scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-35\" d=\"M 691 4666 \n", "L 3169 4666 \n", "L 3169 4134 \n", "L 1269 4134 \n", "L 1269 2991 \n", "Q 1406 3038 1543 3061 \n", "Q 1681 3084 1819 3084 \n", "Q 2600 3084 3056 2656 \n", "Q 3513 2228 3513 1497 \n", "Q 3513 744 3044 326 \n", "Q 2575 -91 1722 -91 \n", "Q 1428 -91 1123 -41 \n", "Q 819 9 494 109 \n", "L 494 744 \n", "Q 775 591 1075 516 \n", "Q 1375 441 1709 441 \n", "Q 2250 441 2565 725 \n", "Q 2881 1009 2881 1497 \n", "Q 2881 1984 2565 2268 \n", "Q 2250 2553 1709 2553 \n", "Q 1456 2553 1204 2497 \n", "Q 953 2441 691 2322 \n", "L 691 4666 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-35\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"text_7\">\n", "     <!-- x -->\n", "     <g transform=\"translate(137.310937 177.875781)scale(0.1 -0.1)\">\n", "      <defs>\n", "       <path id=\"DejaVuSans-78\" d=\"M 3513 3500 \n", "L 2247 1797 \n", "L 3578 0 \n", "L 2900 0 \n", "L 1881 1375 \n", "L 863 0 \n", "L 184 0 \n", "L 1544 1831 \n", "L 300 3500 \n", "L 978 3500 \n", "L 1906 2253 \n", "L 2834 3500 \n", "L 3513 3500 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "      </defs>\n", "      <use xlink:href=\"#DejaVuSans-78\"/>\n", "     </g>\n", "    </g>\n", "   </g>\n", "   <g id=\"matplotlib.axis_2\">\n", "    <g id=\"ytick_1\">\n", "     <g id=\"line2d_13\">\n", "      <path d=\"M 42.**********.599219 \n", "L 237.**********.599219 \n", "\" clip-path=\"url(#pb0a4204e0b)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_14\">\n", "      <defs>\n", "       <path id=\"m828be866d4\" d=\"M 0 0 \n", "L -3.5 0 \n", "\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </defs>\n", "      <g>\n", "       <use xlink:href=\"#m828be866d4\" x=\"42.620312\" y=\"149.599219\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_8\">\n", "      <!-- −1 -->\n", "      <g transform=\"translate(20.878125 153.398437)scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-2212\" d=\"M 678 2272 \n", "L 4684 2272 \n", "L 4684 1741 \n", "L 678 1741 \n", "L 678 2272 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-2212\"/>\n", "       <use xlink:href=\"#DejaVuSans-31\" x=\"83.789062\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_2\">\n", "     <g id=\"line2d_15\">\n", "      <path d=\"M 42.620312 126.499219 \n", "L 237.920313 126.499219 \n", "\" clip-path=\"url(#pb0a4204e0b)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_16\">\n", "      <g>\n", "       <use xlink:href=\"#m828be866d4\" x=\"42.620312\" y=\"126.499219\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_9\">\n", "      <!-- 0 -->\n", "      <g transform=\"translate(29.257812 130.298437)scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_3\">\n", "     <g id=\"line2d_17\">\n", "      <path d=\"M 42.620312 103.399219 \n", "L 237.920313 103.399219 \n", "\" clip-path=\"url(#pb0a4204e0b)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_18\">\n", "      <g>\n", "       <use xlink:href=\"#m828be866d4\" x=\"42.620312\" y=\"103.399219\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_10\">\n", "      <!-- 1 -->\n", "      <g transform=\"translate(29.257812 107.198437)scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-31\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_4\">\n", "     <g id=\"line2d_19\">\n", "      <path d=\"M 42.620312 80.299219 \n", "L 237.920313 80.299219 \n", "\" clip-path=\"url(#pb0a4204e0b)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_20\">\n", "      <g>\n", "       <use xlink:href=\"#m828be866d4\" x=\"42.620312\" y=\"80.299219\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_11\">\n", "      <!-- 2 -->\n", "      <g transform=\"translate(29.257812 84.098437)scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-32\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_5\">\n", "     <g id=\"line2d_21\">\n", "      <path d=\"M 42.620312 57.199219 \n", "L 237.920313 57.199219 \n", "\" clip-path=\"url(#pb0a4204e0b)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_22\">\n", "      <g>\n", "       <use xlink:href=\"#m828be866d4\" x=\"42.620312\" y=\"57.199219\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_12\">\n", "      <!-- 3 -->\n", "      <g transform=\"translate(29.257812 60.998437)scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-33\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_6\">\n", "     <g id=\"line2d_23\">\n", "      <path d=\"M 42.620312 34.099219 \n", "L 237.920313 34.099219 \n", "\" clip-path=\"url(#pb0a4204e0b)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_24\">\n", "      <g>\n", "       <use xlink:href=\"#m828be866d4\" x=\"42.620312\" y=\"34.099219\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_13\">\n", "      <!-- 4 -->\n", "      <g transform=\"translate(29.257812 37.898438)scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-34\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_7\">\n", "     <g id=\"line2d_25\">\n", "      <path d=\"M 42.620312 10.999219 \n", "L 237.920313 10.999219 \n", "\" clip-path=\"url(#pb0a4204e0b)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_26\">\n", "      <g>\n", "       <use xlink:href=\"#m828be866d4\" x=\"42.620312\" y=\"10.999219\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_14\">\n", "      <!-- 5 -->\n", "      <g transform=\"translate(29.257812 14.798437)scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-35\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"text_15\">\n", "     <!-- y -->\n", "     <g transform=\"translate(14.798437 83.258594)rotate(-90)scale(0.1 -0.1)\">\n", "      <defs>\n", "       <path id=\"DejaVuSans-79\" d=\"M 2059 -325 \n", "Q 1816 -950 1584 -1140 \n", "Q 1353 -1331 966 -1331 \n", "L 506 -1331 \n", "L 506 -850 \n", "L 844 -850 \n", "Q 1081 -850 1212 -737 \n", "Q 1344 -625 1503 -206 \n", "L 1606 56 \n", "L 191 3500 \n", "L 800 3500 \n", "L 1894 763 \n", "L 2988 3500 \n", "L 3597 3500 \n", "L 2059 -325 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "      </defs>\n", "      <use xlink:href=\"#DejaVuSans-79\"/>\n", "     </g>\n", "    </g>\n", "   </g>\n", "   <g id=\"line2d_27\">\n", "    <path d=\"M 42.620312 126.499219 \n", "L 46.526313 118.225812 \n", "L 50.432313 110.946345 \n", "L 54.338313 104.029425 \n", "L 58.244313 97.4097 \n", "L 62.150312 91.082293 \n", "L 66.056313 85.061866 \n", "L 69.962312 79.370737 \n", "L 73.868313 74.033949 \n", "L 77.774314 69.076775 \n", "L 81.680312 64.523261 \n", "L 85.586313 60.395214 \n", "L 89.492314 56.711598 \n", "L 93.398311 53.487961 \n", "L 97.304312 50.736132 \n", "L 101.210313 48.46391 \n", "L 105.116313 46.674891 \n", "L 109.022314 45.368338 \n", "L 112.928315 44.539111 \n", "L 116.834312 44.177706 \n", "L 120.740313 44.270242 \n", "L 124.646309 44.798618 \n", "L 128.552314 45.740654 \n", "L 132.458311 47.070245 \n", "L 136.364316 48.757689 \n", "L 140.270312 50.76986 \n", "L 144.176318 53.070611 \n", "L 148.082314 55.621064 \n", "L 151.98832 58.380042 \n", "L 155.894316 61.304393 \n", "L 159.800313 64.349483 \n", "L 163.706318 67.469619 \n", "L 167.612314 70.618454 \n", "L 171.518311 73.749494 \n", "L 175.424316 76.816549 \n", "L 179.330312 79.774142 \n", "L 183.236318 82.578044 \n", "L 187.142314 85.185605 \n", "L 191.048311 87.556281 \n", "L 194.954316 89.651973 \n", "L 198.860313 91.437389 \n", "L 202.766309 92.880457 \n", "L 206.672305 93.952601 \n", "L 210.57832 94.629017 \n", "L 214.484316 94.888934 \n", "L 218.390312 94.715829 \n", "L 222.296309 94.097555 \n", "L 226.202305 93.026506 \n", "L 230.10832 91.499643 \n", "L 234.014316 89.518571 \n", "\" clip-path=\"url(#pb0a4204e0b)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_28\">\n", "    <path d=\"M 42.620312 79.038427 \n", "L 46.526313 79.038427 \n", "L 50.432313 79.038427 \n", "L 54.338313 79.038427 \n", "L 58.244313 79.038427 \n", "L 62.150312 79.038427 \n", "L 66.056313 79.038427 \n", "L 69.962312 79.038427 \n", "L 73.868313 79.038427 \n", "L 77.774314 79.038427 \n", "L 81.680312 79.038427 \n", "L 85.586313 79.038427 \n", "L 89.492314 79.038427 \n", "L 93.398311 79.038427 \n", "L 97.304312 79.038427 \n", "L 101.210313 79.038427 \n", "L 105.116313 79.038427 \n", "L 109.022314 79.038427 \n", "L 112.928315 79.038427 \n", "L 116.834312 79.038427 \n", "L 120.740313 79.038427 \n", "L 124.646309 79.038427 \n", "L 128.552314 79.038427 \n", "L 132.458311 79.038427 \n", "L 136.364316 79.038427 \n", "L 140.270312 79.038427 \n", "L 144.176318 79.038427 \n", "L 148.082314 79.038427 \n", "L 151.98832 79.038427 \n", "L 155.894316 79.038427 \n", "L 159.800313 79.038427 \n", "L 163.706318 79.038427 \n", "L 167.612314 79.038427 \n", "L 171.518311 79.038427 \n", "L 175.424316 79.038427 \n", "L 179.330312 79.038427 \n", "L 183.236318 79.038427 \n", "L 187.142314 79.038427 \n", "L 191.048311 79.038427 \n", "L 194.954316 79.038427 \n", "L 198.860313 79.038427 \n", "L 202.766309 79.038427 \n", "L 206.672305 79.038427 \n", "L 210.57832 79.038427 \n", "L 214.484316 79.038427 \n", "L 218.390312 79.038427 \n", "L 222.296309 79.038427 \n", "L 226.202305 79.038427 \n", "L 230.10832 79.038427 \n", "L 234.014316 79.038427 \n", "\" clip-path=\"url(#pb0a4204e0b)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #bf00bf; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_29\">\n", "    <defs>\n", "     <path id=\"m7ace88be88\" d=\"M 0 3 \n", "C 0.795609 3 1.55874 2.683901 2.12132 2.12132 \n", "C 2.683901 1.55874 3 0.795609 3 0 \n", "C 3 -0.795609 2.683901 -1.55874 2.12132 -2.12132 \n", "C 1.55874 -2.683901 0.795609 -3 0 -3 \n", "C -0.795609 -3 -1.55874 -2.683901 -2.12132 -2.12132 \n", "C -2.683901 -1.55874 -3 -0.795609 -3 0 \n", "C -3 0.795609 -2.683901 1.55874 -2.12132 2.12132 \n", "C -1.55874 2.683901 -0.795609 3 0 3 \n", "z\n", "\" style=\"stroke: #ff7f0e; stroke-opacity: 0.5\"/>\n", "    </defs>\n", "    <g clip-path=\"url(#pb0a4204e0b)\">\n", "     <use xlink:href=\"#m7ace88be88\" x=\"47.284101\" y=\"122.258096\" style=\"fill: #ff7f0e; fill-opacity: 0.5; stroke: #ff7f0e; stroke-opacity: 0.5\"/>\n", "     <use xlink:href=\"#m7ace88be88\" x=\"47.606516\" y=\"111.114186\" style=\"fill: #ff7f0e; fill-opacity: 0.5; stroke: #ff7f0e; stroke-opacity: 0.5\"/>\n", "     <use xlink:href=\"#m7ace88be88\" x=\"51.726435\" y=\"122.404574\" style=\"fill: #ff7f0e; fill-opacity: 0.5; stroke: #ff7f0e; stroke-opacity: 0.5\"/>\n", "     <use xlink:href=\"#m7ace88be88\" x=\"52.335236\" y=\"128.59023\" style=\"fill: #ff7f0e; fill-opacity: 0.5; stroke: #ff7f0e; stroke-opacity: 0.5\"/>\n", "     <use xlink:href=\"#m7ace88be88\" x=\"53.492552\" y=\"95.60659\" style=\"fill: #ff7f0e; fill-opacity: 0.5; stroke: #ff7f0e; stroke-opacity: 0.5\"/>\n", "     <use xlink:href=\"#m7ace88be88\" x=\"55.498434\" y=\"94.84244\" style=\"fill: #ff7f0e; fill-opacity: 0.5; stroke: #ff7f0e; stroke-opacity: 0.5\"/>\n", "     <use xlink:href=\"#m7ace88be88\" x=\"59.18996\" y=\"104.793079\" style=\"fill: #ff7f0e; fill-opacity: 0.5; stroke: #ff7f0e; stroke-opacity: 0.5\"/>\n", "     <use xlink:href=\"#m7ace88be88\" x=\"66.404594\" y=\"98.825684\" style=\"fill: #ff7f0e; fill-opacity: 0.5; stroke: #ff7f0e; stroke-opacity: 0.5\"/>\n", "     <use xlink:href=\"#m7ace88be88\" x=\"69.1377\" y=\"75.296504\" style=\"fill: #ff7f0e; fill-opacity: 0.5; stroke: #ff7f0e; stroke-opacity: 0.5\"/>\n", "     <use xlink:href=\"#m7ace88be88\" x=\"69.734616\" y=\"89.851566\" style=\"fill: #ff7f0e; fill-opacity: 0.5; stroke: #ff7f0e; stroke-opacity: 0.5\"/>\n", "     <use xlink:href=\"#m7ace88be88\" x=\"74.429683\" y=\"91.85764\" style=\"fill: #ff7f0e; fill-opacity: 0.5; stroke: #ff7f0e; stroke-opacity: 0.5\"/>\n", "     <use xlink:href=\"#m7ace88be88\" x=\"75.914442\" y=\"63.140743\" style=\"fill: #ff7f0e; fill-opacity: 0.5; stroke: #ff7f0e; stroke-opacity: 0.5\"/>\n", "     <use xlink:href=\"#m7ace88be88\" x=\"76.186604\" y=\"89.655134\" style=\"fill: #ff7f0e; fill-opacity: 0.5; stroke: #ff7f0e; stroke-opacity: 0.5\"/>\n", "     <use xlink:href=\"#m7ace88be88\" x=\"85.198643\" y=\"75.578684\" style=\"fill: #ff7f0e; fill-opacity: 0.5; stroke: #ff7f0e; stroke-opacity: 0.5\"/>\n", "     <use xlink:href=\"#m7ace88be88\" x=\"85.478934\" y=\"59.595447\" style=\"fill: #ff7f0e; fill-opacity: 0.5; stroke: #ff7f0e; stroke-opacity: 0.5\"/>\n", "     <use xlink:href=\"#m7ace88be88\" x=\"85.7427\" y=\"81.314411\" style=\"fill: #ff7f0e; fill-opacity: 0.5; stroke: #ff7f0e; stroke-opacity: 0.5\"/>\n", "     <use xlink:href=\"#m7ace88be88\" x=\"86.56718\" y=\"52.295319\" style=\"fill: #ff7f0e; fill-opacity: 0.5; stroke: #ff7f0e; stroke-opacity: 0.5\"/>\n", "     <use xlink:href=\"#m7ace88be88\" x=\"87.824273\" y=\"70.143308\" style=\"fill: #ff7f0e; fill-opacity: 0.5; stroke: #ff7f0e; stroke-opacity: 0.5\"/>\n", "     <use xlink:href=\"#m7ace88be88\" x=\"93.210894\" y=\"46.77532\" style=\"fill: #ff7f0e; fill-opacity: 0.5; stroke: #ff7f0e; stroke-opacity: 0.5\"/>\n", "     <use xlink:href=\"#m7ace88be88\" x=\"97.124797\" y=\"46.058528\" style=\"fill: #ff7f0e; fill-opacity: 0.5; stroke: #ff7f0e; stroke-opacity: 0.5\"/>\n", "     <use xlink:href=\"#m7ace88be88\" x=\"102.139028\" y=\"62.917481\" style=\"fill: #ff7f0e; fill-opacity: 0.5; stroke: #ff7f0e; stroke-opacity: 0.5\"/>\n", "     <use xlink:href=\"#m7ace88be88\" x=\"107.054211\" y=\"65.014439\" style=\"fill: #ff7f0e; fill-opacity: 0.5; stroke: #ff7f0e; stroke-opacity: 0.5\"/>\n", "     <use xlink:href=\"#m7ace88be88\" x=\"111.162003\" y=\"22.821134\" style=\"fill: #ff7f0e; fill-opacity: 0.5; stroke: #ff7f0e; stroke-opacity: 0.5\"/>\n", "     <use xlink:href=\"#m7ace88be88\" x=\"120.344163\" y=\"53.246916\" style=\"fill: #ff7f0e; fill-opacity: 0.5; stroke: #ff7f0e; stroke-opacity: 0.5\"/>\n", "     <use xlink:href=\"#m7ace88be88\" x=\"125.125425\" y=\"32.003153\" style=\"fill: #ff7f0e; fill-opacity: 0.5; stroke: #ff7f0e; stroke-opacity: 0.5\"/>\n", "     <use xlink:href=\"#m7ace88be88\" x=\"130.222534\" y=\"65.511554\" style=\"fill: #ff7f0e; fill-opacity: 0.5; stroke: #ff7f0e; stroke-opacity: 0.5\"/>\n", "     <use xlink:href=\"#m7ace88be88\" x=\"132.464699\" y=\"65.626093\" style=\"fill: #ff7f0e; fill-opacity: 0.5; stroke: #ff7f0e; stroke-opacity: 0.5\"/>\n", "     <use xlink:href=\"#m7ace88be88\" x=\"134.328873\" y=\"43.26986\" style=\"fill: #ff7f0e; fill-opacity: 0.5; stroke: #ff7f0e; stroke-opacity: 0.5\"/>\n", "     <use xlink:href=\"#m7ace88be88\" x=\"139.770792\" y=\"45.787699\" style=\"fill: #ff7f0e; fill-opacity: 0.5; stroke: #ff7f0e; stroke-opacity: 0.5\"/>\n", "     <use xlink:href=\"#m7ace88be88\" x=\"157.669862\" y=\"74.334999\" style=\"fill: #ff7f0e; fill-opacity: 0.5; stroke: #ff7f0e; stroke-opacity: 0.5\"/>\n", "     <use xlink:href=\"#m7ace88be88\" x=\"165.812714\" y=\"76.473885\" style=\"fill: #ff7f0e; fill-opacity: 0.5; stroke: #ff7f0e; stroke-opacity: 0.5\"/>\n", "     <use xlink:href=\"#m7ace88be88\" x=\"171.519447\" y=\"33.341799\" style=\"fill: #ff7f0e; fill-opacity: 0.5; stroke: #ff7f0e; stroke-opacity: 0.5\"/>\n", "     <use xlink:href=\"#m7ace88be88\" x=\"175.979852\" y=\"84.3256\" style=\"fill: #ff7f0e; fill-opacity: 0.5; stroke: #ff7f0e; stroke-opacity: 0.5\"/>\n", "     <use xlink:href=\"#m7ace88be88\" x=\"176.817448\" y=\"84.123718\" style=\"fill: #ff7f0e; fill-opacity: 0.5; stroke: #ff7f0e; stroke-opacity: 0.5\"/>\n", "     <use xlink:href=\"#m7ace88be88\" x=\"178.625784\" y=\"66.945137\" style=\"fill: #ff7f0e; fill-opacity: 0.5; stroke: #ff7f0e; stroke-opacity: 0.5\"/>\n", "     <use xlink:href=\"#m7ace88be88\" x=\"180.472665\" y=\"76.834062\" style=\"fill: #ff7f0e; fill-opacity: 0.5; stroke: #ff7f0e; stroke-opacity: 0.5\"/>\n", "     <use xlink:href=\"#m7ace88be88\" x=\"180.502726\" y=\"53.798665\" style=\"fill: #ff7f0e; fill-opacity: 0.5; stroke: #ff7f0e; stroke-opacity: 0.5\"/>\n", "     <use xlink:href=\"#m7ace88be88\" x=\"182.810368\" y=\"81.660426\" style=\"fill: #ff7f0e; fill-opacity: 0.5; stroke: #ff7f0e; stroke-opacity: 0.5\"/>\n", "     <use xlink:href=\"#m7ace88be88\" x=\"183.897356\" y=\"84.336309\" style=\"fill: #ff7f0e; fill-opacity: 0.5; stroke: #ff7f0e; stroke-opacity: 0.5\"/>\n", "     <use xlink:href=\"#m7ace88be88\" x=\"187.506662\" y=\"75.489507\" style=\"fill: #ff7f0e; fill-opacity: 0.5; stroke: #ff7f0e; stroke-opacity: 0.5\"/>\n", "     <use xlink:href=\"#m7ace88be88\" x=\"190.340914\" y=\"89.163053\" style=\"fill: #ff7f0e; fill-opacity: 0.5; stroke: #ff7f0e; stroke-opacity: 0.5\"/>\n", "     <use xlink:href=\"#m7ace88be88\" x=\"201.541754\" y=\"95.648612\" style=\"fill: #ff7f0e; fill-opacity: 0.5; stroke: #ff7f0e; stroke-opacity: 0.5\"/>\n", "     <use xlink:href=\"#m7ace88be88\" x=\"204.830174\" y=\"113.366038\" style=\"fill: #ff7f0e; fill-opacity: 0.5; stroke: #ff7f0e; stroke-opacity: 0.5\"/>\n", "     <use xlink:href=\"#m7ace88be88\" x=\"209.726214\" y=\"86.845308\" style=\"fill: #ff7f0e; fill-opacity: 0.5; stroke: #ff7f0e; stroke-opacity: 0.5\"/>\n", "     <use xlink:href=\"#m7ace88be88\" x=\"216.282585\" y=\"103.495354\" style=\"fill: #ff7f0e; fill-opacity: 0.5; stroke: #ff7f0e; stroke-opacity: 0.5\"/>\n", "     <use xlink:href=\"#m7ace88be88\" x=\"217.032047\" y=\"100.890533\" style=\"fill: #ff7f0e; fill-opacity: 0.5; stroke: #ff7f0e; stroke-opacity: 0.5\"/>\n", "     <use xlink:href=\"#m7ace88be88\" x=\"217.69695\" y=\"88.878071\" style=\"fill: #ff7f0e; fill-opacity: 0.5; stroke: #ff7f0e; stroke-opacity: 0.5\"/>\n", "     <use xlink:href=\"#m7ace88be88\" x=\"224.089046\" y=\"73.596342\" style=\"fill: #ff7f0e; fill-opacity: 0.5; stroke: #ff7f0e; stroke-opacity: 0.5\"/>\n", "     <use xlink:href=\"#m7ace88be88\" x=\"224.203405\" y=\"114.981765\" style=\"fill: #ff7f0e; fill-opacity: 0.5; stroke: #ff7f0e; stroke-opacity: 0.5\"/>\n", "     <use xlink:href=\"#m7ace88be88\" x=\"229.940283\" y=\"117.196654\" style=\"fill: #ff7f0e; fill-opacity: 0.5; stroke: #ff7f0e; stroke-opacity: 0.5\"/>\n", "    </g>\n", "   </g>\n", "   <g id=\"patch_3\">\n", "    <path d=\"M 42.**********.599219 \n", "L 42.620312 10.999219 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_4\">\n", "    <path d=\"M 237.**********.599219 \n", "L 237.920313 10.999219 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_5\">\n", "    <path d=\"M 42.**********.599219 \n", "L 237.**********.599219 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_6\">\n", "    <path d=\"M 42.620312 10.999219 \n", "L 237.920313 10.999219 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"legend_1\">\n", "    <g id=\"patch_7\">\n", "     <path d=\"M 111.600781 144.599219 \n", "L 168.939844 144.599219 \n", "Q 170.939844 144.599219 170.939844 142.599219 \n", "L 170.939844 114.242969 \n", "Q 170.939844 112.242969 168.939844 112.242969 \n", "L 111.600781 112.242969 \n", "Q 109.600781 112.242969 109.600781 114.242969 \n", "L 109.600781 142.599219 \n", "Q 109.600781 144.599219 111.600781 144.599219 \n", "z\n", "\" style=\"fill: #ffffff; opacity: 0.8; stroke: #cccccc; stroke-linejoin: miter\"/>\n", "    </g>\n", "    <g id=\"line2d_30\">\n", "     <path d=\"M 113.600781 120.341406 \n", "L 123.600781 120.341406 \n", "L 133.600781 120.341406 \n", "\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "    </g>\n", "    <g id=\"text_16\">\n", "     <!-- Truth -->\n", "     <g transform=\"translate(141.600781 123.841406)scale(0.1 -0.1)\">\n", "      <defs>\n", "       <path id=\"DejaVuSans-54\" d=\"M -19 4666 \n", "L 3928 4666 \n", "L 3928 4134 \n", "L 2272 4134 \n", "L 2272 0 \n", "L 1638 0 \n", "L 1638 4134 \n", "L -19 4134 \n", "L -19 4666 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-72\" d=\"M 2631 2963 \n", "Q 2534 3019 2420 3045 \n", "Q 2306 3072 2169 3072 \n", "Q 1681 3072 1420 2755 \n", "Q 1159 2438 1159 1844 \n", "L 1159 0 \n", "L 581 0 \n", "L 581 3500 \n", "L 1159 3500 \n", "L 1159 2956 \n", "Q 1341 3275 1631 3429 \n", "Q 1922 3584 2338 3584 \n", "Q 2397 3584 2469 3576 \n", "Q 2541 3569 2628 3553 \n", "L 2631 2963 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-75\" d=\"M 544 1381 \n", "L 544 3500 \n", "L 1119 3500 \n", "L 1119 1403 \n", "Q 1119 906 1312 657 \n", "Q 1506 409 1894 409 \n", "Q 2359 409 2629 706 \n", "Q 2900 1003 2900 1516 \n", "L 2900 3500 \n", "L 3475 3500 \n", "L 3475 0 \n", "L 2900 0 \n", "L 2900 538 \n", "Q 2691 219 2414 64 \n", "Q 2138 -91 1772 -91 \n", "Q 1169 -91 856 284 \n", "Q 544 659 544 1381 \n", "z\n", "M 1991 3584 \n", "L 1991 3584 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-74\" d=\"M 1172 4494 \n", "L 1172 3500 \n", "L 2356 3500 \n", "L 2356 3053 \n", "L 1172 3053 \n", "L 1172 1153 \n", "Q 1172 725 1289 603 \n", "Q 1406 481 1766 481 \n", "L 2356 481 \n", "L 2356 0 \n", "L 1766 0 \n", "Q 1100 0 847 248 \n", "Q 594 497 594 1153 \n", "L 594 3053 \n", "L 172 3053 \n", "L 172 3500 \n", "L 594 3500 \n", "L 594 4494 \n", "L 1172 4494 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-68\" d=\"M 3513 2113 \n", "L 3513 0 \n", "L 2938 0 \n", "L 2938 2094 \n", "Q 2938 2591 2744 2837 \n", "Q 2550 3084 2163 3084 \n", "Q 1697 3084 1428 2787 \n", "Q 1159 2491 1159 1978 \n", "L 1159 0 \n", "L 581 0 \n", "L 581 4863 \n", "L 1159 4863 \n", "L 1159 2956 \n", "Q 1366 3272 1645 3428 \n", "Q 1925 3584 2291 3584 \n", "Q 2894 3584 3203 3211 \n", "Q 3513 2838 3513 2113 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "      </defs>\n", "      <use xlink:href=\"#DejaVuSans-54\"/>\n", "      <use xlink:href=\"#DejaVuSans-72\" x=\"46.333984\"/>\n", "      <use xlink:href=\"#DejaVuSans-75\" x=\"87.447266\"/>\n", "      <use xlink:href=\"#DejaVuSans-74\" x=\"150.826172\"/>\n", "      <use xlink:href=\"#DejaVuSans-68\" x=\"190.035156\"/>\n", "     </g>\n", "    </g>\n", "    <g id=\"line2d_31\">\n", "     <path d=\"M 113.600781 135.019531 \n", "L 123.600781 135.019531 \n", "L 133.600781 135.019531 \n", "\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #bf00bf; stroke-width: 1.5\"/>\n", "    </g>\n", "    <g id=\"text_17\">\n", "     <!-- Pred -->\n", "     <g transform=\"translate(141.600781 138.519531)scale(0.1 -0.1)\">\n", "      <defs>\n", "       <path id=\"DejaVuSans-50\" d=\"M 1259 4147 \n", "L 1259 2394 \n", "L 2053 2394 \n", "Q 2494 2394 2734 2622 \n", "Q 2975 2850 2975 3272 \n", "Q 2975 3691 2734 3919 \n", "Q 2494 4147 2053 4147 \n", "L 1259 4147 \n", "z\n", "M 628 4666 \n", "L 2053 4666 \n", "Q 2838 4666 3239 4311 \n", "Q 3641 3956 3641 3272 \n", "Q 3641 2581 3239 2228 \n", "Q 2838 1875 2053 1875 \n", "L 1259 1875 \n", "L 1259 0 \n", "L 628 0 \n", "L 628 4666 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-65\" d=\"M 3597 1894 \n", "L 3597 1613 \n", "L 953 1613 \n", "Q 991 1019 1311 708 \n", "Q 1631 397 2203 397 \n", "Q 2534 397 2845 478 \n", "Q 3156 559 3463 722 \n", "L 3463 178 \n", "Q 3153 47 2828 -22 \n", "Q 2503 -91 2169 -91 \n", "Q 1331 -91 842 396 \n", "Q 353 884 353 1716 \n", "Q 353 2575 817 3079 \n", "Q 1281 3584 2069 3584 \n", "Q 2775 3584 3186 3129 \n", "Q 3597 2675 3597 1894 \n", "z\n", "M 3022 2063 \n", "Q 3016 2534 2758 2815 \n", "Q 2500 3097 2075 3097 \n", "Q 1594 3097 1305 2825 \n", "Q 1016 2553 972 2059 \n", "L 3022 2063 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-64\" d=\"M 2906 2969 \n", "L 2906 4863 \n", "L 3481 4863 \n", "L 3481 0 \n", "L 2906 0 \n", "L 2906 525 \n", "Q 2725 213 2448 61 \n", "Q 2172 -91 1784 -91 \n", "Q 1150 -91 751 415 \n", "Q 353 922 353 1747 \n", "Q 353 2572 751 3078 \n", "Q 1150 3584 1784 3584 \n", "Q 2172 3584 2448 3432 \n", "Q 2725 3281 2906 2969 \n", "z\n", "M 947 1747 \n", "Q 947 1113 1208 752 \n", "Q 1469 391 1925 391 \n", "Q 2381 391 2643 752 \n", "Q 2906 1113 2906 1747 \n", "Q 2906 2381 2643 2742 \n", "Q 2381 3103 1925 3103 \n", "Q 1469 3103 1208 2742 \n", "Q 947 2381 947 1747 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "      </defs>\n", "      <use xlink:href=\"#DejaVuSans-50\"/>\n", "      <use xlink:href=\"#DejaVuSans-72\" x=\"58.552734\"/>\n", "      <use xlink:href=\"#DejaVuSans-65\" x=\"97.416016\"/>\n", "      <use xlink:href=\"#DejaVuSans-64\" x=\"158.939453\"/>\n", "     </g>\n", "    </g>\n", "   </g>\n", "  </g>\n", " </g>\n", " <defs>\n", "  <clipPath id=\"pb0a4204e0b\">\n", "   <rect x=\"42.620312\" y=\"10.999219\" width=\"195.3\" height=\"138.6\"/>\n", "  </clipPath>\n", " </defs>\n", "</svg>\n"], "text/plain": ["<Figure size 350x250 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["y_hat = torch.repeat_interleave(y_train.mean(), n_test)\n", "plot_kernel_reg(y_hat)"]}, {"cell_type": "markdown", "id": "b221607e", "metadata": {"origin_pos": 21}, "source": ["## [**非参数注意力汇聚**]\n", "\n", "显然，平均汇聚忽略了输入$x_i$。\n", "于是Nadaraya :cite:`<PERSON><PERSON><PERSON>.1964`和\n", "Watson :cite:<PERSON><PERSON>.1964`提出了一个更好的想法，\n", "根据输入的位置对输出$y_i$进行加权：\n", "\n", "$$f(x) = \\sum_{i=1}^n \\frac{K(x - x_i)}{\\sum_{j=1}^n K(x - x_j)} y_i,$$\n", ":eqlabel:`eq_nadar<PERSON>-watson`\n", "\n", "其中$K$是*核*（kernel）。\n", "公式 :eqref:`eq_nadar<PERSON>-watson`所描述的估计器被称为\n", "*Nadaraya-Watson核回归*（Nadaraya-Watson kernel regression）。\n", "这里不会深入讨论核函数的细节，\n", "但受此启发，\n", "我们可以从 :numref:`fig_qkv`中的注意力机制框架的角度\n", "重写 :eqref:`eq_nadar<PERSON>-watson`，\n", "成为一个更加通用的*注意力汇聚*（attention pooling）公式：\n", "\n", "$$f(x) = \\sum_{i=1}^n \\alpha(x, x_i) y_i,$$\n", ":eqlabel:`eq_attn-pooling`\n", "\n", "其中$x$是查询，$(x_i, y_i)$是键值对。\n", "比较 :eqref:`eq_attn-pooling`和 :eqref:`eq_avg-pooling`，\n", "注意力汇聚是$y_i$的加权平均。\n", "将查询$x$和键$x_i$之间的关系建模为\n", "*注意力权重*（attention weight）$\\alpha(x, x_i)$，\n", "如 :eqref:`eq_attn-pooling`所示，\n", "这个权重将被分配给每一个对应值$y_i$。\n", "对于任何查询，模型在所有键值对注意力权重都是一个有效的概率分布：\n", "它们是非负的，并且总和为1。\n", "\n", "为了更好地理解注意力汇聚，\n", "下面考虑一个*高斯核*（Gaussian kernel），其定义为：\n", "\n", "$$K(u) = \\frac{1}{\\sqrt{2\\pi}} \\exp(-\\frac{u^2}{2}).$$\n", "\n", "将高斯核代入 :eqref:`eq_attn-pooling`和\n", " :eqref:`eq_nadar<PERSON>-watson`可以得到：\n", "\n", "$$\\begin{aligned} f(x) &=\\sum_{i=1}^n \\alpha(x, x_i) y_i\\\\ &= \\sum_{i=1}^n \\frac{\\exp\\left(-\\frac{1}{2}(x - x_i)^2\\right)}{\\sum_{j=1}^n \\exp\\left(-\\frac{1}{2}(x - x_j)^2\\right)} y_i \\\\&= \\sum_{i=1}^n \\mathrm{softmax}\\left(-\\frac{1}{2}(x - x_i)^2\\right) y_i. \\end{aligned}$$\n", ":eqlabel:`eq_nadar<PERSON>-watson-gaussian`\n", "\n", "在 :eqref:`eq_nadaraya-watson-gaussian`中，\n", "如果一个键$x_i$越是接近给定的查询$x$，\n", "那么分配给这个键对应值$y_i$的注意力权重就会越大，\n", "也就“获得了更多的注意力”。\n", "\n", "值得注意的是，Na<PERSON>aya-Watson核回归是一个非参数模型。\n", "因此， :eqref:`eq_nadar<PERSON>-watson-gaussian`是\n", "*非参数的注意力汇聚*（nonparametric attention pooling）模型。\n", "接下来，我们将基于这个非参数的注意力汇聚模型来绘制预测结果。\n", "从绘制的结果会发现新的模型预测线是平滑的，并且比平均汇聚的预测更接近真实。\n"]}, {"cell_type": "code", "execution_count": 6, "id": "23247ed8", "metadata": {"execution": {"iopub.execute_input": "2022-12-07T17:03:17.234196Z", "iopub.status.busy": "2022-12-07T17:03:17.233905Z", "iopub.status.idle": "2022-12-07T17:03:17.417131Z", "shell.execute_reply": "2022-12-07T17:03:17.416319Z"}, "origin_pos": 23, "tab": ["pytorch"]}, "outputs": [{"data": {"image/svg+xml": ["<?xml version=\"1.0\" encoding=\"utf-8\" standalone=\"no\"?>\n", "<!DOCTYPE svg PUBLIC \"-//W3C//DTD SVG 1.1//EN\"\n", "  \"http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd\">\n", "<svg xmlns:xlink=\"http://www.w3.org/1999/xlink\" width=\"248.301563pt\" height=\"187.155469pt\" viewBox=\"0 0 248.**********.155469\" xmlns=\"http://www.w3.org/2000/svg\" version=\"1.1\">\n", " <metadata>\n", "  <rdf:RDF xmlns:dc=\"http://purl.org/dc/elements/1.1/\" xmlns:cc=\"http://creativecommons.org/ns#\" xmlns:rdf=\"http://www.w3.org/1999/02/22-rdf-syntax-ns#\">\n", "   <cc:Work>\n", "    <dc:type rdf:resource=\"http://purl.org/dc/dcmitype/StillImage\"/>\n", "    <dc:date>2022-12-07T17:03:17.366047</dc:date>\n", "    <dc:format>image/svg+xml</dc:format>\n", "    <dc:creator>\n", "     <cc:Agent>\n", "      <dc:title>Matplotlib v3.5.1, https://matplotlib.org/</dc:title>\n", "     </cc:Agent>\n", "    </dc:creator>\n", "   </cc:Work>\n", "  </rdf:RDF>\n", " </metadata>\n", " <defs>\n", "  <style type=\"text/css\">*{stroke-linejoin: round; stroke-linecap: butt}</style>\n", " </defs>\n", " <g id=\"figure_1\">\n", "  <g id=\"patch_1\">\n", "   <path d=\"M 0 187.155469 \n", "L 248.**********.155469 \n", "L 248.301563 0 \n", "L 0 0 \n", "z\n", "\" style=\"fill: #ffffff\"/>\n", "  </g>\n", "  <g id=\"axes_1\">\n", "   <g id=\"patch_2\">\n", "    <path d=\"M 42.**********.599219 \n", "L 237.**********.599219 \n", "L 237.920313 10.999219 \n", "L 42.620312 10.999219 \n", "z\n", "\" style=\"fill: #ffffff\"/>\n", "   </g>\n", "   <g id=\"matplotlib.axis_1\">\n", "    <g id=\"xtick_1\">\n", "     <g id=\"line2d_1\">\n", "      <path d=\"M 42.**********.599219 \n", "L 42.620312 10.999219 \n", "\" clip-path=\"url(#pde3be799e7)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_2\">\n", "      <defs>\n", "       <path id=\"md74a6bf8e9\" d=\"M 0 0 \n", "L 0 3.5 \n", "\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </defs>\n", "      <g>\n", "       <use xlink:href=\"#md74a6bf8e9\" x=\"42.620312\" y=\"149.599219\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_1\">\n", "      <!-- 0 -->\n", "      <g transform=\"translate(39.439062 164.197656)scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-30\" d=\"M 2034 4250 \n", "Q 1547 4250 1301 3770 \n", "Q 1056 3291 1056 2328 \n", "Q 1056 1369 1301 889 \n", "Q 1547 409 2034 409 \n", "Q 2525 409 2770 889 \n", "Q 3016 1369 3016 2328 \n", "Q 3016 3291 2770 3770 \n", "Q 2525 4250 2034 4250 \n", "z\n", "M 2034 4750 \n", "Q 2819 4750 3233 4129 \n", "Q 3647 3509 3647 2328 \n", "Q 3647 1150 3233 529 \n", "Q 2819 -91 2034 -91 \n", "Q 1250 -91 836 529 \n", "Q 422 1150 422 2328 \n", "Q 422 3509 836 4129 \n", "Q 1250 4750 2034 4750 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_2\">\n", "     <g id=\"line2d_3\">\n", "      <path d=\"M 81.680312 149.599219 \n", "L 81.680312 10.999219 \n", "\" clip-path=\"url(#pde3be799e7)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_4\">\n", "      <g>\n", "       <use xlink:href=\"#md74a6bf8e9\" x=\"81.680312\" y=\"149.599219\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_2\">\n", "      <!-- 1 -->\n", "      <g transform=\"translate(78.499062 164.197656)scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-31\" d=\"M 794 531 \n", "L 1825 531 \n", "L 1825 4091 \n", "L 703 3866 \n", "L 703 4441 \n", "L 1819 4666 \n", "L 2450 4666 \n", "L 2450 531 \n", "L 3481 531 \n", "L 3481 0 \n", "L 794 0 \n", "L 794 531 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-31\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_3\">\n", "     <g id=\"line2d_5\">\n", "      <path d=\"M 120.740313 149.599219 \n", "L 120.740313 10.999219 \n", "\" clip-path=\"url(#pde3be799e7)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_6\">\n", "      <g>\n", "       <use xlink:href=\"#md74a6bf8e9\" x=\"120.740313\" y=\"149.599219\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_3\">\n", "      <!-- 2 -->\n", "      <g transform=\"translate(117.559062 164.197656)scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-32\" d=\"M 1228 531 \n", "L 3431 531 \n", "L 3431 0 \n", "L 469 0 \n", "L 469 531 \n", "Q 828 903 1448 1529 \n", "Q 2069 2156 2228 2338 \n", "Q 2531 2678 2651 2914 \n", "Q 2772 3150 2772 3378 \n", "Q 2772 3750 2511 3984 \n", "Q 2250 4219 1831 4219 \n", "Q 1534 4219 1204 4116 \n", "Q 875 4013 500 3803 \n", "L 500 4441 \n", "Q 881 4594 1212 4672 \n", "Q 1544 4750 1819 4750 \n", "Q 2544 4750 2975 4387 \n", "Q 3406 4025 3406 3419 \n", "Q 3406 3131 3298 2873 \n", "Q 3191 2616 2906 2266 \n", "Q 2828 2175 2409 1742 \n", "Q 1991 1309 1228 531 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-32\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_4\">\n", "     <g id=\"line2d_7\">\n", "      <path d=\"M 159.800313 149.599219 \n", "L 159.800313 10.999219 \n", "\" clip-path=\"url(#pde3be799e7)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_8\">\n", "      <g>\n", "       <use xlink:href=\"#md74a6bf8e9\" x=\"159.800313\" y=\"149.599219\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_4\">\n", "      <!-- 3 -->\n", "      <g transform=\"translate(156.619063 164.197656)scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-33\" d=\"M 2597 2516 \n", "Q 3050 2419 3304 2112 \n", "Q 3559 1806 3559 1356 \n", "Q 3559 666 3084 287 \n", "Q 2609 -91 1734 -91 \n", "Q 1441 -91 1130 -33 \n", "Q 819 25 488 141 \n", "L 488 750 \n", "Q 750 597 1062 519 \n", "Q 1375 441 1716 441 \n", "Q 2309 441 2620 675 \n", "Q 2931 909 2931 1356 \n", "Q 2931 1769 2642 2001 \n", "Q 2353 2234 1838 2234 \n", "L 1294 2234 \n", "L 1294 2753 \n", "L 1863 2753 \n", "Q 2328 2753 2575 2939 \n", "Q 2822 3125 2822 3475 \n", "Q 2822 3834 2567 4026 \n", "Q 2313 4219 1838 4219 \n", "Q 1578 4219 1281 4162 \n", "Q 984 4106 628 3988 \n", "L 628 4550 \n", "Q 988 4650 1302 4700 \n", "Q 1616 4750 1894 4750 \n", "Q 2613 4750 3031 4423 \n", "Q 3450 4097 3450 3541 \n", "Q 3450 3153 3228 2886 \n", "Q 3006 2619 2597 2516 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-33\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_5\">\n", "     <g id=\"line2d_9\">\n", "      <path d=\"M 198.860313 149.599219 \n", "L 198.860313 10.999219 \n", "\" clip-path=\"url(#pde3be799e7)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_10\">\n", "      <g>\n", "       <use xlink:href=\"#md74a6bf8e9\" x=\"198.860313\" y=\"149.599219\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_5\">\n", "      <!-- 4 -->\n", "      <g transform=\"translate(195.679063 164.197656)scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-34\" d=\"M 2419 4116 \n", "L 825 1625 \n", "L 2419 1625 \n", "L 2419 4116 \n", "z\n", "M 2253 4666 \n", "L 3047 4666 \n", "L 3047 1625 \n", "L 3713 1625 \n", "L 3713 1100 \n", "L 3047 1100 \n", "L 3047 0 \n", "L 2419 0 \n", "L 2419 1100 \n", "L 313 1100 \n", "L 313 1709 \n", "L 2253 4666 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-34\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_6\">\n", "     <g id=\"line2d_11\">\n", "      <path d=\"M 237.**********.599219 \n", "L 237.920313 10.999219 \n", "\" clip-path=\"url(#pde3be799e7)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_12\">\n", "      <g>\n", "       <use xlink:href=\"#md74a6bf8e9\" x=\"237.920313\" y=\"149.599219\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_6\">\n", "      <!-- 5 -->\n", "      <g transform=\"translate(234.739063 164.197656)scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-35\" d=\"M 691 4666 \n", "L 3169 4666 \n", "L 3169 4134 \n", "L 1269 4134 \n", "L 1269 2991 \n", "Q 1406 3038 1543 3061 \n", "Q 1681 3084 1819 3084 \n", "Q 2600 3084 3056 2656 \n", "Q 3513 2228 3513 1497 \n", "Q 3513 744 3044 326 \n", "Q 2575 -91 1722 -91 \n", "Q 1428 -91 1123 -41 \n", "Q 819 9 494 109 \n", "L 494 744 \n", "Q 775 591 1075 516 \n", "Q 1375 441 1709 441 \n", "Q 2250 441 2565 725 \n", "Q 2881 1009 2881 1497 \n", "Q 2881 1984 2565 2268 \n", "Q 2250 2553 1709 2553 \n", "Q 1456 2553 1204 2497 \n", "Q 953 2441 691 2322 \n", "L 691 4666 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-35\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"text_7\">\n", "     <!-- x -->\n", "     <g transform=\"translate(137.310937 177.875781)scale(0.1 -0.1)\">\n", "      <defs>\n", "       <path id=\"DejaVuSans-78\" d=\"M 3513 3500 \n", "L 2247 1797 \n", "L 3578 0 \n", "L 2900 0 \n", "L 1881 1375 \n", "L 863 0 \n", "L 184 0 \n", "L 1544 1831 \n", "L 300 3500 \n", "L 978 3500 \n", "L 1906 2253 \n", "L 2834 3500 \n", "L 3513 3500 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "      </defs>\n", "      <use xlink:href=\"#DejaVuSans-78\"/>\n", "     </g>\n", "    </g>\n", "   </g>\n", "   <g id=\"matplotlib.axis_2\">\n", "    <g id=\"ytick_1\">\n", "     <g id=\"line2d_13\">\n", "      <path d=\"M 42.**********.599219 \n", "L 237.**********.599219 \n", "\" clip-path=\"url(#pde3be799e7)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_14\">\n", "      <defs>\n", "       <path id=\"mc2a623a318\" d=\"M 0 0 \n", "L -3.5 0 \n", "\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </defs>\n", "      <g>\n", "       <use xlink:href=\"#mc2a623a318\" x=\"42.620312\" y=\"149.599219\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_8\">\n", "      <!-- −1 -->\n", "      <g transform=\"translate(20.878125 153.398437)scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-2212\" d=\"M 678 2272 \n", "L 4684 2272 \n", "L 4684 1741 \n", "L 678 1741 \n", "L 678 2272 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-2212\"/>\n", "       <use xlink:href=\"#DejaVuSans-31\" x=\"83.789062\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_2\">\n", "     <g id=\"line2d_15\">\n", "      <path d=\"M 42.620312 126.499219 \n", "L 237.920313 126.499219 \n", "\" clip-path=\"url(#pde3be799e7)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_16\">\n", "      <g>\n", "       <use xlink:href=\"#mc2a623a318\" x=\"42.620312\" y=\"126.499219\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_9\">\n", "      <!-- 0 -->\n", "      <g transform=\"translate(29.257812 130.298437)scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_3\">\n", "     <g id=\"line2d_17\">\n", "      <path d=\"M 42.620312 103.399219 \n", "L 237.920313 103.399219 \n", "\" clip-path=\"url(#pde3be799e7)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_18\">\n", "      <g>\n", "       <use xlink:href=\"#mc2a623a318\" x=\"42.620312\" y=\"103.399219\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_10\">\n", "      <!-- 1 -->\n", "      <g transform=\"translate(29.257812 107.198437)scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-31\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_4\">\n", "     <g id=\"line2d_19\">\n", "      <path d=\"M 42.620312 80.299219 \n", "L 237.920313 80.299219 \n", "\" clip-path=\"url(#pde3be799e7)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_20\">\n", "      <g>\n", "       <use xlink:href=\"#mc2a623a318\" x=\"42.620312\" y=\"80.299219\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_11\">\n", "      <!-- 2 -->\n", "      <g transform=\"translate(29.257812 84.098437)scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-32\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_5\">\n", "     <g id=\"line2d_21\">\n", "      <path d=\"M 42.620312 57.199219 \n", "L 237.920313 57.199219 \n", "\" clip-path=\"url(#pde3be799e7)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_22\">\n", "      <g>\n", "       <use xlink:href=\"#mc2a623a318\" x=\"42.620312\" y=\"57.199219\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_12\">\n", "      <!-- 3 -->\n", "      <g transform=\"translate(29.257812 60.998437)scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-33\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_6\">\n", "     <g id=\"line2d_23\">\n", "      <path d=\"M 42.620312 34.099219 \n", "L 237.920313 34.099219 \n", "\" clip-path=\"url(#pde3be799e7)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_24\">\n", "      <g>\n", "       <use xlink:href=\"#mc2a623a318\" x=\"42.620312\" y=\"34.099219\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_13\">\n", "      <!-- 4 -->\n", "      <g transform=\"translate(29.257812 37.898438)scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-34\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_7\">\n", "     <g id=\"line2d_25\">\n", "      <path d=\"M 42.620312 10.999219 \n", "L 237.920313 10.999219 \n", "\" clip-path=\"url(#pde3be799e7)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_26\">\n", "      <g>\n", "       <use xlink:href=\"#mc2a623a318\" x=\"42.620312\" y=\"10.999219\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_14\">\n", "      <!-- 5 -->\n", "      <g transform=\"translate(29.257812 14.798437)scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-35\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"text_15\">\n", "     <!-- y -->\n", "     <g transform=\"translate(14.798437 83.258594)rotate(-90)scale(0.1 -0.1)\">\n", "      <defs>\n", "       <path id=\"DejaVuSans-79\" d=\"M 2059 -325 \n", "Q 1816 -950 1584 -1140 \n", "Q 1353 -1331 966 -1331 \n", "L 506 -1331 \n", "L 506 -850 \n", "L 844 -850 \n", "Q 1081 -850 1212 -737 \n", "Q 1344 -625 1503 -206 \n", "L 1606 56 \n", "L 191 3500 \n", "L 800 3500 \n", "L 1894 763 \n", "L 2988 3500 \n", "L 3597 3500 \n", "L 2059 -325 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "      </defs>\n", "      <use xlink:href=\"#DejaVuSans-79\"/>\n", "     </g>\n", "    </g>\n", "   </g>\n", "   <g id=\"line2d_27\">\n", "    <path d=\"M 42.620312 126.499219 \n", "L 46.526313 118.225812 \n", "L 50.432313 110.946345 \n", "L 54.338313 104.029425 \n", "L 58.244313 97.4097 \n", "L 62.150312 91.082293 \n", "L 66.056313 85.061866 \n", "L 69.962312 79.370737 \n", "L 73.868313 74.033949 \n", "L 77.774314 69.076775 \n", "L 81.680312 64.523261 \n", "L 85.586313 60.395214 \n", "L 89.492314 56.711598 \n", "L 93.398311 53.487961 \n", "L 97.304312 50.736132 \n", "L 101.210313 48.46391 \n", "L 105.116313 46.674891 \n", "L 109.022314 45.368338 \n", "L 112.928315 44.539111 \n", "L 116.834312 44.177706 \n", "L 120.740313 44.270242 \n", "L 124.646309 44.798618 \n", "L 128.552314 45.740654 \n", "L 132.458311 47.070245 \n", "L 136.364316 48.757689 \n", "L 140.270312 50.76986 \n", "L 144.176318 53.070611 \n", "L 148.082314 55.621064 \n", "L 151.98832 58.380042 \n", "L 155.894316 61.304393 \n", "L 159.800313 64.349483 \n", "L 163.706318 67.469619 \n", "L 167.612314 70.618454 \n", "L 171.518311 73.749494 \n", "L 175.424316 76.816549 \n", "L 179.330312 79.774142 \n", "L 183.236318 82.578044 \n", "L 187.142314 85.185605 \n", "L 191.048311 87.556281 \n", "L 194.954316 89.651973 \n", "L 198.860313 91.437389 \n", "L 202.766309 92.880457 \n", "L 206.672305 93.952601 \n", "L 210.57832 94.629017 \n", "L 214.484316 94.888934 \n", "L 218.390312 94.715829 \n", "L 222.296309 94.097555 \n", "L 226.202305 93.026506 \n", "L 230.10832 91.499643 \n", "L 234.014316 89.518571 \n", "\" clip-path=\"url(#pde3be799e7)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_28\">\n", "    <path d=\"M 42.620312 88.665844 \n", "L 46.526313 87.532131 \n", "L 50.432313 86.3665 \n", "L 54.338313 85.170559 \n", "L 58.244313 83.946403 \n", "L 62.150312 82.696714 \n", "L 66.056313 81.424948 \n", "L 69.962312 80.135377 \n", "L 73.868313 78.833307 \n", "L 77.774314 77.5252 \n", "L 81.680312 76.218889 \n", "L 85.586313 74.923731 \n", "L 89.492314 73.650778 \n", "L 93.398311 72.412853 \n", "L 97.304312 71.224661 \n", "L 101.210313 70.102679 \n", "L 105.116313 69.064923 \n", "L 109.022314 68.130554 \n", "L 112.928315 67.319182 \n", "L 116.834312 66.649992 \n", "L 120.740313 66.140628 \n", "L 124.646309 65.805934 \n", "L 128.552314 65.656725 \n", "L 132.458311 65.698665 \n", "L 136.364316 65.931509 \n", "L 140.270312 66.348904 \n", "L 144.176318 66.938545 \n", "L 148.082314 67.683094 \n", "L 151.98832 68.561436 \n", "L 155.894316 69.550159 \n", "L 159.800313 70.625178 \n", "L 163.706318 71.76306 \n", "L 167.612314 72.94222 \n", "L 171.518311 74.143625 \n", "L 175.424316 75.351215 \n", "L 179.330312 76.551959 \n", "L 183.236318 77.735751 \n", "L 187.142314 78.895023 \n", "L 191.048311 80.024374 \n", "L 194.954316 81.120228 \n", "L 198.860313 82.180303 \n", "L 202.766309 83.203376 \n", "L 206.672305 84.188962 \n", "L 210.57832 85.137015 \n", "L 214.484316 86.047866 \n", "L 218.390312 86.922011 \n", "L 222.296309 87.760072 \n", "L 226.202305 88.562741 \n", "L 230.10832 89.330719 \n", "L 234.014316 90.064727 \n", "\" clip-path=\"url(#pde3be799e7)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #bf00bf; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_29\">\n", "    <defs>\n", "     <path id=\"m97e8f50f5c\" d=\"M 0 3 \n", "C 0.795609 3 1.55874 2.683901 2.12132 2.12132 \n", "C 2.683901 1.55874 3 0.795609 3 0 \n", "C 3 -0.795609 2.683901 -1.55874 2.12132 -2.12132 \n", "C 1.55874 -2.683901 0.795609 -3 0 -3 \n", "C -0.795609 -3 -1.55874 -2.683901 -2.12132 -2.12132 \n", "C -2.683901 -1.55874 -3 -0.795609 -3 0 \n", "C -3 0.795609 -2.683901 1.55874 -2.12132 2.12132 \n", "C -1.55874 2.683901 -0.795609 3 0 3 \n", "z\n", "\" style=\"stroke: #ff7f0e; stroke-opacity: 0.5\"/>\n", "    </defs>\n", "    <g clip-path=\"url(#pde3be799e7)\">\n", "     <use xlink:href=\"#m97e8f50f5c\" x=\"47.284101\" y=\"122.258096\" style=\"fill: #ff7f0e; fill-opacity: 0.5; stroke: #ff7f0e; stroke-opacity: 0.5\"/>\n", "     <use xlink:href=\"#m97e8f50f5c\" x=\"47.606516\" y=\"111.114186\" style=\"fill: #ff7f0e; fill-opacity: 0.5; stroke: #ff7f0e; stroke-opacity: 0.5\"/>\n", "     <use xlink:href=\"#m97e8f50f5c\" x=\"51.726435\" y=\"122.404574\" style=\"fill: #ff7f0e; fill-opacity: 0.5; stroke: #ff7f0e; stroke-opacity: 0.5\"/>\n", "     <use xlink:href=\"#m97e8f50f5c\" x=\"52.335236\" y=\"128.59023\" style=\"fill: #ff7f0e; fill-opacity: 0.5; stroke: #ff7f0e; stroke-opacity: 0.5\"/>\n", "     <use xlink:href=\"#m97e8f50f5c\" x=\"53.492552\" y=\"95.60659\" style=\"fill: #ff7f0e; fill-opacity: 0.5; stroke: #ff7f0e; stroke-opacity: 0.5\"/>\n", "     <use xlink:href=\"#m97e8f50f5c\" x=\"55.498434\" y=\"94.84244\" style=\"fill: #ff7f0e; fill-opacity: 0.5; stroke: #ff7f0e; stroke-opacity: 0.5\"/>\n", "     <use xlink:href=\"#m97e8f50f5c\" x=\"59.18996\" y=\"104.793079\" style=\"fill: #ff7f0e; fill-opacity: 0.5; stroke: #ff7f0e; stroke-opacity: 0.5\"/>\n", "     <use xlink:href=\"#m97e8f50f5c\" x=\"66.404594\" y=\"98.825684\" style=\"fill: #ff7f0e; fill-opacity: 0.5; stroke: #ff7f0e; stroke-opacity: 0.5\"/>\n", "     <use xlink:href=\"#m97e8f50f5c\" x=\"69.1377\" y=\"75.296504\" style=\"fill: #ff7f0e; fill-opacity: 0.5; stroke: #ff7f0e; stroke-opacity: 0.5\"/>\n", "     <use xlink:href=\"#m97e8f50f5c\" x=\"69.734616\" y=\"89.851566\" style=\"fill: #ff7f0e; fill-opacity: 0.5; stroke: #ff7f0e; stroke-opacity: 0.5\"/>\n", "     <use xlink:href=\"#m97e8f50f5c\" x=\"74.429683\" y=\"91.85764\" style=\"fill: #ff7f0e; fill-opacity: 0.5; stroke: #ff7f0e; stroke-opacity: 0.5\"/>\n", "     <use xlink:href=\"#m97e8f50f5c\" x=\"75.914442\" y=\"63.140743\" style=\"fill: #ff7f0e; fill-opacity: 0.5; stroke: #ff7f0e; stroke-opacity: 0.5\"/>\n", "     <use xlink:href=\"#m97e8f50f5c\" x=\"76.186604\" y=\"89.655134\" style=\"fill: #ff7f0e; fill-opacity: 0.5; stroke: #ff7f0e; stroke-opacity: 0.5\"/>\n", "     <use xlink:href=\"#m97e8f50f5c\" x=\"85.198643\" y=\"75.578684\" style=\"fill: #ff7f0e; fill-opacity: 0.5; stroke: #ff7f0e; stroke-opacity: 0.5\"/>\n", "     <use xlink:href=\"#m97e8f50f5c\" x=\"85.478934\" y=\"59.595447\" style=\"fill: #ff7f0e; fill-opacity: 0.5; stroke: #ff7f0e; stroke-opacity: 0.5\"/>\n", "     <use xlink:href=\"#m97e8f50f5c\" x=\"85.7427\" y=\"81.314411\" style=\"fill: #ff7f0e; fill-opacity: 0.5; stroke: #ff7f0e; stroke-opacity: 0.5\"/>\n", "     <use xlink:href=\"#m97e8f50f5c\" x=\"86.56718\" y=\"52.295319\" style=\"fill: #ff7f0e; fill-opacity: 0.5; stroke: #ff7f0e; stroke-opacity: 0.5\"/>\n", "     <use xlink:href=\"#m97e8f50f5c\" x=\"87.824273\" y=\"70.143308\" style=\"fill: #ff7f0e; fill-opacity: 0.5; stroke: #ff7f0e; stroke-opacity: 0.5\"/>\n", "     <use xlink:href=\"#m97e8f50f5c\" x=\"93.210894\" y=\"46.77532\" style=\"fill: #ff7f0e; fill-opacity: 0.5; stroke: #ff7f0e; stroke-opacity: 0.5\"/>\n", "     <use xlink:href=\"#m97e8f50f5c\" x=\"97.124797\" y=\"46.058528\" style=\"fill: #ff7f0e; fill-opacity: 0.5; stroke: #ff7f0e; stroke-opacity: 0.5\"/>\n", "     <use xlink:href=\"#m97e8f50f5c\" x=\"102.139028\" y=\"62.917481\" style=\"fill: #ff7f0e; fill-opacity: 0.5; stroke: #ff7f0e; stroke-opacity: 0.5\"/>\n", "     <use xlink:href=\"#m97e8f50f5c\" x=\"107.054211\" y=\"65.014439\" style=\"fill: #ff7f0e; fill-opacity: 0.5; stroke: #ff7f0e; stroke-opacity: 0.5\"/>\n", "     <use xlink:href=\"#m97e8f50f5c\" x=\"111.162003\" y=\"22.821134\" style=\"fill: #ff7f0e; fill-opacity: 0.5; stroke: #ff7f0e; stroke-opacity: 0.5\"/>\n", "     <use xlink:href=\"#m97e8f50f5c\" x=\"120.344163\" y=\"53.246916\" style=\"fill: #ff7f0e; fill-opacity: 0.5; stroke: #ff7f0e; stroke-opacity: 0.5\"/>\n", "     <use xlink:href=\"#m97e8f50f5c\" x=\"125.125425\" y=\"32.003153\" style=\"fill: #ff7f0e; fill-opacity: 0.5; stroke: #ff7f0e; stroke-opacity: 0.5\"/>\n", "     <use xlink:href=\"#m97e8f50f5c\" x=\"130.222534\" y=\"65.511554\" style=\"fill: #ff7f0e; fill-opacity: 0.5; stroke: #ff7f0e; stroke-opacity: 0.5\"/>\n", "     <use xlink:href=\"#m97e8f50f5c\" x=\"132.464699\" y=\"65.626093\" style=\"fill: #ff7f0e; fill-opacity: 0.5; stroke: #ff7f0e; stroke-opacity: 0.5\"/>\n", "     <use xlink:href=\"#m97e8f50f5c\" x=\"134.328873\" y=\"43.26986\" style=\"fill: #ff7f0e; fill-opacity: 0.5; stroke: #ff7f0e; stroke-opacity: 0.5\"/>\n", "     <use xlink:href=\"#m97e8f50f5c\" x=\"139.770792\" y=\"45.787699\" style=\"fill: #ff7f0e; fill-opacity: 0.5; stroke: #ff7f0e; stroke-opacity: 0.5\"/>\n", "     <use xlink:href=\"#m97e8f50f5c\" x=\"157.669862\" y=\"74.334999\" style=\"fill: #ff7f0e; fill-opacity: 0.5; stroke: #ff7f0e; stroke-opacity: 0.5\"/>\n", "     <use xlink:href=\"#m97e8f50f5c\" x=\"165.812714\" y=\"76.473885\" style=\"fill: #ff7f0e; fill-opacity: 0.5; stroke: #ff7f0e; stroke-opacity: 0.5\"/>\n", "     <use xlink:href=\"#m97e8f50f5c\" x=\"171.519447\" y=\"33.341799\" style=\"fill: #ff7f0e; fill-opacity: 0.5; stroke: #ff7f0e; stroke-opacity: 0.5\"/>\n", "     <use xlink:href=\"#m97e8f50f5c\" x=\"175.979852\" y=\"84.3256\" style=\"fill: #ff7f0e; fill-opacity: 0.5; stroke: #ff7f0e; stroke-opacity: 0.5\"/>\n", "     <use xlink:href=\"#m97e8f50f5c\" x=\"176.817448\" y=\"84.123718\" style=\"fill: #ff7f0e; fill-opacity: 0.5; stroke: #ff7f0e; stroke-opacity: 0.5\"/>\n", "     <use xlink:href=\"#m97e8f50f5c\" x=\"178.625784\" y=\"66.945137\" style=\"fill: #ff7f0e; fill-opacity: 0.5; stroke: #ff7f0e; stroke-opacity: 0.5\"/>\n", "     <use xlink:href=\"#m97e8f50f5c\" x=\"180.472665\" y=\"76.834062\" style=\"fill: #ff7f0e; fill-opacity: 0.5; stroke: #ff7f0e; stroke-opacity: 0.5\"/>\n", "     <use xlink:href=\"#m97e8f50f5c\" x=\"180.502726\" y=\"53.798665\" style=\"fill: #ff7f0e; fill-opacity: 0.5; stroke: #ff7f0e; stroke-opacity: 0.5\"/>\n", "     <use xlink:href=\"#m97e8f50f5c\" x=\"182.810368\" y=\"81.660426\" style=\"fill: #ff7f0e; fill-opacity: 0.5; stroke: #ff7f0e; stroke-opacity: 0.5\"/>\n", "     <use xlink:href=\"#m97e8f50f5c\" x=\"183.897356\" y=\"84.336309\" style=\"fill: #ff7f0e; fill-opacity: 0.5; stroke: #ff7f0e; stroke-opacity: 0.5\"/>\n", "     <use xlink:href=\"#m97e8f50f5c\" x=\"187.506662\" y=\"75.489507\" style=\"fill: #ff7f0e; fill-opacity: 0.5; stroke: #ff7f0e; stroke-opacity: 0.5\"/>\n", "     <use xlink:href=\"#m97e8f50f5c\" x=\"190.340914\" y=\"89.163053\" style=\"fill: #ff7f0e; fill-opacity: 0.5; stroke: #ff7f0e; stroke-opacity: 0.5\"/>\n", "     <use xlink:href=\"#m97e8f50f5c\" x=\"201.541754\" y=\"95.648612\" style=\"fill: #ff7f0e; fill-opacity: 0.5; stroke: #ff7f0e; stroke-opacity: 0.5\"/>\n", "     <use xlink:href=\"#m97e8f50f5c\" x=\"204.830174\" y=\"113.366038\" style=\"fill: #ff7f0e; fill-opacity: 0.5; stroke: #ff7f0e; stroke-opacity: 0.5\"/>\n", "     <use xlink:href=\"#m97e8f50f5c\" x=\"209.726214\" y=\"86.845308\" style=\"fill: #ff7f0e; fill-opacity: 0.5; stroke: #ff7f0e; stroke-opacity: 0.5\"/>\n", "     <use xlink:href=\"#m97e8f50f5c\" x=\"216.282585\" y=\"103.495354\" style=\"fill: #ff7f0e; fill-opacity: 0.5; stroke: #ff7f0e; stroke-opacity: 0.5\"/>\n", "     <use xlink:href=\"#m97e8f50f5c\" x=\"217.032047\" y=\"100.890533\" style=\"fill: #ff7f0e; fill-opacity: 0.5; stroke: #ff7f0e; stroke-opacity: 0.5\"/>\n", "     <use xlink:href=\"#m97e8f50f5c\" x=\"217.69695\" y=\"88.878071\" style=\"fill: #ff7f0e; fill-opacity: 0.5; stroke: #ff7f0e; stroke-opacity: 0.5\"/>\n", "     <use xlink:href=\"#m97e8f50f5c\" x=\"224.089046\" y=\"73.596342\" style=\"fill: #ff7f0e; fill-opacity: 0.5; stroke: #ff7f0e; stroke-opacity: 0.5\"/>\n", "     <use xlink:href=\"#m97e8f50f5c\" x=\"224.203405\" y=\"114.981765\" style=\"fill: #ff7f0e; fill-opacity: 0.5; stroke: #ff7f0e; stroke-opacity: 0.5\"/>\n", "     <use xlink:href=\"#m97e8f50f5c\" x=\"229.940283\" y=\"117.196654\" style=\"fill: #ff7f0e; fill-opacity: 0.5; stroke: #ff7f0e; stroke-opacity: 0.5\"/>\n", "    </g>\n", "   </g>\n", "   <g id=\"patch_3\">\n", "    <path d=\"M 42.**********.599219 \n", "L 42.620312 10.999219 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_4\">\n", "    <path d=\"M 237.**********.599219 \n", "L 237.920313 10.999219 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_5\">\n", "    <path d=\"M 42.**********.599219 \n", "L 237.**********.599219 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_6\">\n", "    <path d=\"M 42.620312 10.999219 \n", "L 237.920313 10.999219 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"legend_1\">\n", "    <g id=\"patch_7\">\n", "     <path d=\"M 111.600781 144.599219 \n", "L 168.939844 144.599219 \n", "Q 170.939844 144.599219 170.939844 142.599219 \n", "L 170.939844 114.242969 \n", "Q 170.939844 112.242969 168.939844 112.242969 \n", "L 111.600781 112.242969 \n", "Q 109.600781 112.242969 109.600781 114.242969 \n", "L 109.600781 142.599219 \n", "Q 109.600781 144.599219 111.600781 144.599219 \n", "z\n", "\" style=\"fill: #ffffff; opacity: 0.8; stroke: #cccccc; stroke-linejoin: miter\"/>\n", "    </g>\n", "    <g id=\"line2d_30\">\n", "     <path d=\"M 113.600781 120.341406 \n", "L 123.600781 120.341406 \n", "L 133.600781 120.341406 \n", "\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "    </g>\n", "    <g id=\"text_16\">\n", "     <!-- Truth -->\n", "     <g transform=\"translate(141.600781 123.841406)scale(0.1 -0.1)\">\n", "      <defs>\n", "       <path id=\"DejaVuSans-54\" d=\"M -19 4666 \n", "L 3928 4666 \n", "L 3928 4134 \n", "L 2272 4134 \n", "L 2272 0 \n", "L 1638 0 \n", "L 1638 4134 \n", "L -19 4134 \n", "L -19 4666 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-72\" d=\"M 2631 2963 \n", "Q 2534 3019 2420 3045 \n", "Q 2306 3072 2169 3072 \n", "Q 1681 3072 1420 2755 \n", "Q 1159 2438 1159 1844 \n", "L 1159 0 \n", "L 581 0 \n", "L 581 3500 \n", "L 1159 3500 \n", "L 1159 2956 \n", "Q 1341 3275 1631 3429 \n", "Q 1922 3584 2338 3584 \n", "Q 2397 3584 2469 3576 \n", "Q 2541 3569 2628 3553 \n", "L 2631 2963 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-75\" d=\"M 544 1381 \n", "L 544 3500 \n", "L 1119 3500 \n", "L 1119 1403 \n", "Q 1119 906 1312 657 \n", "Q 1506 409 1894 409 \n", "Q 2359 409 2629 706 \n", "Q 2900 1003 2900 1516 \n", "L 2900 3500 \n", "L 3475 3500 \n", "L 3475 0 \n", "L 2900 0 \n", "L 2900 538 \n", "Q 2691 219 2414 64 \n", "Q 2138 -91 1772 -91 \n", "Q 1169 -91 856 284 \n", "Q 544 659 544 1381 \n", "z\n", "M 1991 3584 \n", "L 1991 3584 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-74\" d=\"M 1172 4494 \n", "L 1172 3500 \n", "L 2356 3500 \n", "L 2356 3053 \n", "L 1172 3053 \n", "L 1172 1153 \n", "Q 1172 725 1289 603 \n", "Q 1406 481 1766 481 \n", "L 2356 481 \n", "L 2356 0 \n", "L 1766 0 \n", "Q 1100 0 847 248 \n", "Q 594 497 594 1153 \n", "L 594 3053 \n", "L 172 3053 \n", "L 172 3500 \n", "L 594 3500 \n", "L 594 4494 \n", "L 1172 4494 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-68\" d=\"M 3513 2113 \n", "L 3513 0 \n", "L 2938 0 \n", "L 2938 2094 \n", "Q 2938 2591 2744 2837 \n", "Q 2550 3084 2163 3084 \n", "Q 1697 3084 1428 2787 \n", "Q 1159 2491 1159 1978 \n", "L 1159 0 \n", "L 581 0 \n", "L 581 4863 \n", "L 1159 4863 \n", "L 1159 2956 \n", "Q 1366 3272 1645 3428 \n", "Q 1925 3584 2291 3584 \n", "Q 2894 3584 3203 3211 \n", "Q 3513 2838 3513 2113 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "      </defs>\n", "      <use xlink:href=\"#DejaVuSans-54\"/>\n", "      <use xlink:href=\"#DejaVuSans-72\" x=\"46.333984\"/>\n", "      <use xlink:href=\"#DejaVuSans-75\" x=\"87.447266\"/>\n", "      <use xlink:href=\"#DejaVuSans-74\" x=\"150.826172\"/>\n", "      <use xlink:href=\"#DejaVuSans-68\" x=\"190.035156\"/>\n", "     </g>\n", "    </g>\n", "    <g id=\"line2d_31\">\n", "     <path d=\"M 113.600781 135.019531 \n", "L 123.600781 135.019531 \n", "L 133.600781 135.019531 \n", "\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #bf00bf; stroke-width: 1.5\"/>\n", "    </g>\n", "    <g id=\"text_17\">\n", "     <!-- Pred -->\n", "     <g transform=\"translate(141.600781 138.519531)scale(0.1 -0.1)\">\n", "      <defs>\n", "       <path id=\"DejaVuSans-50\" d=\"M 1259 4147 \n", "L 1259 2394 \n", "L 2053 2394 \n", "Q 2494 2394 2734 2622 \n", "Q 2975 2850 2975 3272 \n", "Q 2975 3691 2734 3919 \n", "Q 2494 4147 2053 4147 \n", "L 1259 4147 \n", "z\n", "M 628 4666 \n", "L 2053 4666 \n", "Q 2838 4666 3239 4311 \n", "Q 3641 3956 3641 3272 \n", "Q 3641 2581 3239 2228 \n", "Q 2838 1875 2053 1875 \n", "L 1259 1875 \n", "L 1259 0 \n", "L 628 0 \n", "L 628 4666 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-65\" d=\"M 3597 1894 \n", "L 3597 1613 \n", "L 953 1613 \n", "Q 991 1019 1311 708 \n", "Q 1631 397 2203 397 \n", "Q 2534 397 2845 478 \n", "Q 3156 559 3463 722 \n", "L 3463 178 \n", "Q 3153 47 2828 -22 \n", "Q 2503 -91 2169 -91 \n", "Q 1331 -91 842 396 \n", "Q 353 884 353 1716 \n", "Q 353 2575 817 3079 \n", "Q 1281 3584 2069 3584 \n", "Q 2775 3584 3186 3129 \n", "Q 3597 2675 3597 1894 \n", "z\n", "M 3022 2063 \n", "Q 3016 2534 2758 2815 \n", "Q 2500 3097 2075 3097 \n", "Q 1594 3097 1305 2825 \n", "Q 1016 2553 972 2059 \n", "L 3022 2063 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-64\" d=\"M 2906 2969 \n", "L 2906 4863 \n", "L 3481 4863 \n", "L 3481 0 \n", "L 2906 0 \n", "L 2906 525 \n", "Q 2725 213 2448 61 \n", "Q 2172 -91 1784 -91 \n", "Q 1150 -91 751 415 \n", "Q 353 922 353 1747 \n", "Q 353 2572 751 3078 \n", "Q 1150 3584 1784 3584 \n", "Q 2172 3584 2448 3432 \n", "Q 2725 3281 2906 2969 \n", "z\n", "M 947 1747 \n", "Q 947 1113 1208 752 \n", "Q 1469 391 1925 391 \n", "Q 2381 391 2643 752 \n", "Q 2906 1113 2906 1747 \n", "Q 2906 2381 2643 2742 \n", "Q 2381 3103 1925 3103 \n", "Q 1469 3103 1208 2742 \n", "Q 947 2381 947 1747 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "      </defs>\n", "      <use xlink:href=\"#DejaVuSans-50\"/>\n", "      <use xlink:href=\"#DejaVuSans-72\" x=\"58.552734\"/>\n", "      <use xlink:href=\"#DejaVuSans-65\" x=\"97.416016\"/>\n", "      <use xlink:href=\"#DejaVuSans-64\" x=\"158.939453\"/>\n", "     </g>\n", "    </g>\n", "   </g>\n", "  </g>\n", " </g>\n", " <defs>\n", "  <clipPath id=\"pde3be799e7\">\n", "   <rect x=\"42.620312\" y=\"10.999219\" width=\"195.3\" height=\"138.6\"/>\n", "  </clipPath>\n", " </defs>\n", "</svg>\n"], "text/plain": ["<Figure size 350x250 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["# X_repeat的形状:(n_test,n_train),\n", "# 每一行都包含着相同的测试输入（例如：同样的查询）\n", "X_repeat = x_test.repeat_interleave(n_train).reshape((-1, n_train))\n", "# x_train包含着键。attention_weights的形状：(n_test,n_train),\n", "# 每一行都包含着要在给定的每个查询的值（y_train）之间分配的注意力权重\n", "attention_weights = nn.functional.softmax(-(X_repeat - x_train)**2 / 2, dim=1)\n", "# y_hat的每个元素都是值的加权平均值，其中的权重是注意力权重\n", "y_hat = torch.matmul(attention_weights, y_train)\n", "plot_kernel_reg(y_hat)"]}, {"cell_type": "markdown", "id": "e03fe17c", "metadata": {"origin_pos": 26}, "source": ["现在来观察注意力的权重。\n", "这里测试数据的输入相当于查询，而训练数据的输入相当于键。\n", "因为两个输入都是经过排序的，因此由观察可知“查询-键”对越接近，\n", "注意力汇聚的[**注意力权重**]就越高。\n"]}, {"cell_type": "code", "execution_count": 7, "id": "02e02bac", "metadata": {"execution": {"iopub.execute_input": "2022-12-07T17:03:17.421378Z", "iopub.status.busy": "2022-12-07T17:03:17.420799Z", "iopub.status.idle": "2022-12-07T17:03:17.654258Z", "shell.execute_reply": "2022-12-07T17:03:17.653464Z"}, "origin_pos": 28, "tab": ["pytorch"]}, "outputs": [{"data": {"image/svg+xml": ["<?xml version=\"1.0\" encoding=\"utf-8\" standalone=\"no\"?>\n", "<!DOCTYPE svg PUBLIC \"-//W3C//DTD SVG 1.1//EN\"\n", "  \"http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd\">\n", "<svg xmlns:xlink=\"http://www.w3.org/1999/xlink\" width=\"199.80175pt\" height=\"159.039469pt\" viewBox=\"0 0 199.80175 159.039469\" xmlns=\"http://www.w3.org/2000/svg\" version=\"1.1\">\n", " <metadata>\n", "  <rdf:RDF xmlns:dc=\"http://purl.org/dc/elements/1.1/\" xmlns:cc=\"http://creativecommons.org/ns#\" xmlns:rdf=\"http://www.w3.org/1999/02/22-rdf-syntax-ns#\">\n", "   <cc:Work>\n", "    <dc:type rdf:resource=\"http://purl.org/dc/dcmitype/StillImage\"/>\n", "    <dc:date>2022-12-07T17:03:17.605503</dc:date>\n", "    <dc:format>image/svg+xml</dc:format>\n", "    <dc:creator>\n", "     <cc:Agent>\n", "      <dc:title>Matplotlib v3.5.1, https://matplotlib.org/</dc:title>\n", "     </cc:Agent>\n", "    </dc:creator>\n", "   </cc:Work>\n", "  </rdf:RDF>\n", " </metadata>\n", " <defs>\n", "  <style type=\"text/css\">*{stroke-linejoin: round; stroke-linecap: butt}</style>\n", " </defs>\n", " <g id=\"figure_1\">\n", "  <g id=\"patch_1\">\n", "   <path d=\"M 0 159.039469 \n", "L 199.80175 159.039469 \n", "L 199.80175 0 \n", "L 0 0 \n", "z\n", "\" style=\"fill: #ffffff\"/>\n", "  </g>\n", "  <g id=\"axes_1\">\n", "   <g id=\"patch_2\">\n", "    <path d=\"M 40.**********.483219 \n", "L 152.**********.483219 \n", "L 152.203125 9.883219 \n", "L 40.603125 9.883219 \n", "z\n", "\" style=\"fill: #ffffff\"/>\n", "   </g>\n", "   <g clip-path=\"url(#p9f7c4196c7)\">\n", "    <image xlink:href=\"data:image/png;base64,\n", "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\" id=\"imagec3ed14de45\" transform=\"scale(1 -1)translate(0 -111.6)\" x=\"40.603125\" y=\"-9.883219\" width=\"111.6\" height=\"111.6\"/>\n", "   </g>\n", "   <g id=\"matplotlib.axis_1\">\n", "    <g id=\"xtick_1\">\n", "     <g id=\"line2d_1\">\n", "      <defs>\n", "       <path id=\"m466fa0e01c\" d=\"M 0 0 \n", "L 0 3.5 \n", "\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </defs>\n", "      <g>\n", "       <use xlink:href=\"#m466fa0e01c\" x=\"41.719125\" y=\"121.483219\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_1\">\n", "      <!-- 0 -->\n", "      <g transform=\"translate(38.537875 136.081656)scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-30\" d=\"M 2034 4250 \n", "Q 1547 4250 1301 3770 \n", "Q 1056 3291 1056 2328 \n", "Q 1056 1369 1301 889 \n", "Q 1547 409 2034 409 \n", "Q 2525 409 2770 889 \n", "Q 3016 1369 3016 2328 \n", "Q 3016 3291 2770 3770 \n", "Q 2525 4250 2034 4250 \n", "z\n", "M 2034 4750 \n", "Q 2819 4750 3233 4129 \n", "Q 3647 3509 3647 2328 \n", "Q 3647 1150 3233 529 \n", "Q 2819 -91 2034 -91 \n", "Q 1250 -91 836 529 \n", "Q 422 1150 422 2328 \n", "Q 422 3509 836 4129 \n", "Q 1250 4750 2034 4750 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_2\">\n", "     <g id=\"line2d_2\">\n", "      <g>\n", "       <use xlink:href=\"#m466fa0e01c\" x=\"86.359125\" y=\"121.483219\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_2\">\n", "      <!-- 20 -->\n", "      <g transform=\"translate(79.996625 136.081656)scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-32\" d=\"M 1228 531 \n", "L 3431 531 \n", "L 3431 0 \n", "L 469 0 \n", "L 469 531 \n", "Q 828 903 1448 1529 \n", "Q 2069 2156 2228 2338 \n", "Q 2531 2678 2651 2914 \n", "Q 2772 3150 2772 3378 \n", "Q 2772 3750 2511 3984 \n", "Q 2250 4219 1831 4219 \n", "Q 1534 4219 1204 4116 \n", "Q 875 4013 500 3803 \n", "L 500 4441 \n", "Q 881 4594 1212 4672 \n", "Q 1544 4750 1819 4750 \n", "Q 2544 4750 2975 4387 \n", "Q 3406 4025 3406 3419 \n", "Q 3406 3131 3298 2873 \n", "Q 3191 2616 2906 2266 \n", "Q 2828 2175 2409 1742 \n", "Q 1991 1309 1228 531 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-32\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"63.623047\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_3\">\n", "     <g id=\"line2d_3\">\n", "      <g>\n", "       <use xlink:href=\"#m466fa0e01c\" x=\"130.999125\" y=\"121.483219\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_3\">\n", "      <!-- 40 -->\n", "      <g transform=\"translate(124.636625 136.081656)scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-34\" d=\"M 2419 4116 \n", "L 825 1625 \n", "L 2419 1625 \n", "L 2419 4116 \n", "z\n", "M 2253 4666 \n", "L 3047 4666 \n", "L 3047 1625 \n", "L 3713 1625 \n", "L 3713 1100 \n", "L 3047 1100 \n", "L 3047 0 \n", "L 2419 0 \n", "L 2419 1100 \n", "L 313 1100 \n", "L 313 1709 \n", "L 2253 4666 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-34\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"63.623047\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"text_4\">\n", "     <!-- Sorted training inputs -->\n", "     <g transform=\"translate(41.889844 149.759781)scale(0.1 -0.1)\">\n", "      <defs>\n", "       <path id=\"DejaVuSans-53\" d=\"M 3425 4513 \n", "L 3425 3897 \n", "Q 3066 4069 2747 4153 \n", "Q 2428 4238 2131 4238 \n", "Q 1616 4238 1336 4038 \n", "Q 1056 3838 1056 3469 \n", "Q 1056 3159 1242 3001 \n", "Q 1428 2844 1947 2747 \n", "L 2328 2669 \n", "Q 3034 2534 3370 2195 \n", "Q 3706 1856 3706 1288 \n", "Q 3706 609 3251 259 \n", "Q 2797 -91 1919 -91 \n", "Q 1588 -91 1214 -16 \n", "Q 841 59 441 206 \n", "L 441 856 \n", "Q 825 641 1194 531 \n", "Q 1563 422 1919 422 \n", "Q 2459 422 2753 634 \n", "Q 3047 847 3047 1241 \n", "Q 3047 1584 2836 1778 \n", "Q 2625 1972 2144 2069 \n", "L 1759 2144 \n", "Q 1053 2284 737 2584 \n", "Q 422 2884 422 3419 \n", "Q 422 4038 858 4394 \n", "Q 1294 4750 2059 4750 \n", "Q 2388 4750 2728 4690 \n", "Q 3069 4631 3425 4513 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-6f\" d=\"M 1959 3097 \n", "Q 1497 3097 1228 2736 \n", "Q 959 2375 959 1747 \n", "Q 959 1119 1226 758 \n", "Q 1494 397 1959 397 \n", "Q 2419 397 2687 759 \n", "Q 2956 1122 2956 1747 \n", "Q 2956 2369 2687 2733 \n", "Q 2419 3097 1959 3097 \n", "z\n", "M 1959 3584 \n", "Q 2709 3584 3137 3096 \n", "Q 3566 2609 3566 1747 \n", "Q 3566 888 3137 398 \n", "Q 2709 -91 1959 -91 \n", "Q 1206 -91 779 398 \n", "Q 353 888 353 1747 \n", "Q 353 2609 779 3096 \n", "Q 1206 3584 1959 3584 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-72\" d=\"M 2631 2963 \n", "Q 2534 3019 2420 3045 \n", "Q 2306 3072 2169 3072 \n", "Q 1681 3072 1420 2755 \n", "Q 1159 2438 1159 1844 \n", "L 1159 0 \n", "L 581 0 \n", "L 581 3500 \n", "L 1159 3500 \n", "L 1159 2956 \n", "Q 1341 3275 1631 3429 \n", "Q 1922 3584 2338 3584 \n", "Q 2397 3584 2469 3576 \n", "Q 2541 3569 2628 3553 \n", "L 2631 2963 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-74\" d=\"M 1172 4494 \n", "L 1172 3500 \n", "L 2356 3500 \n", "L 2356 3053 \n", "L 1172 3053 \n", "L 1172 1153 \n", "Q 1172 725 1289 603 \n", "Q 1406 481 1766 481 \n", "L 2356 481 \n", "L 2356 0 \n", "L 1766 0 \n", "Q 1100 0 847 248 \n", "Q 594 497 594 1153 \n", "L 594 3053 \n", "L 172 3053 \n", "L 172 3500 \n", "L 594 3500 \n", "L 594 4494 \n", "L 1172 4494 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-65\" d=\"M 3597 1894 \n", "L 3597 1613 \n", "L 953 1613 \n", "Q 991 1019 1311 708 \n", "Q 1631 397 2203 397 \n", "Q 2534 397 2845 478 \n", "Q 3156 559 3463 722 \n", "L 3463 178 \n", "Q 3153 47 2828 -22 \n", "Q 2503 -91 2169 -91 \n", "Q 1331 -91 842 396 \n", "Q 353 884 353 1716 \n", "Q 353 2575 817 3079 \n", "Q 1281 3584 2069 3584 \n", "Q 2775 3584 3186 3129 \n", "Q 3597 2675 3597 1894 \n", "z\n", "M 3022 2063 \n", "Q 3016 2534 2758 2815 \n", "Q 2500 3097 2075 3097 \n", "Q 1594 3097 1305 2825 \n", "Q 1016 2553 972 2059 \n", "L 3022 2063 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-64\" d=\"M 2906 2969 \n", "L 2906 4863 \n", "L 3481 4863 \n", "L 3481 0 \n", "L 2906 0 \n", "L 2906 525 \n", "Q 2725 213 2448 61 \n", "Q 2172 -91 1784 -91 \n", "Q 1150 -91 751 415 \n", "Q 353 922 353 1747 \n", "Q 353 2572 751 3078 \n", "Q 1150 3584 1784 3584 \n", "Q 2172 3584 2448 3432 \n", "Q 2725 3281 2906 2969 \n", "z\n", "M 947 1747 \n", "Q 947 1113 1208 752 \n", "Q 1469 391 1925 391 \n", "Q 2381 391 2643 752 \n", "Q 2906 1113 2906 1747 \n", "Q 2906 2381 2643 2742 \n", "Q 2381 3103 1925 3103 \n", "Q 1469 3103 1208 2742 \n", "Q 947 2381 947 1747 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-20\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-61\" d=\"M 2194 1759 \n", "Q 1497 1759 1228 1600 \n", "Q 959 1441 959 1056 \n", "Q 959 750 1161 570 \n", "Q 1363 391 1709 391 \n", "Q 2188 391 2477 730 \n", "Q 2766 1069 2766 1631 \n", "L 2766 1759 \n", "L 2194 1759 \n", "z\n", "M 3341 1997 \n", "L 3341 0 \n", "L 2766 0 \n", "L 2766 531 \n", "Q 2569 213 2275 61 \n", "Q 1981 -91 1556 -91 \n", "Q 1019 -91 701 211 \n", "Q 384 513 384 1019 \n", "Q 384 1609 779 1909 \n", "Q 1175 2209 1959 2209 \n", "L 2766 2209 \n", "L 2766 2266 \n", "Q 2766 2663 2505 2880 \n", "Q 2244 3097 1772 3097 \n", "Q 1472 3097 1187 3025 \n", "Q 903 2953 641 2809 \n", "L 641 3341 \n", "Q 956 3463 1253 3523 \n", "Q 1550 3584 1831 3584 \n", "Q 2591 3584 2966 3190 \n", "Q 3341 2797 3341 1997 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-69\" d=\"M 603 3500 \n", "L 1178 3500 \n", "L 1178 0 \n", "L 603 0 \n", "L 603 3500 \n", "z\n", "M 603 4863 \n", "L 1178 4863 \n", "L 1178 4134 \n", "L 603 4134 \n", "L 603 4863 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-6e\" d=\"M 3513 2113 \n", "L 3513 0 \n", "L 2938 0 \n", "L 2938 2094 \n", "Q 2938 2591 2744 2837 \n", "Q 2550 3084 2163 3084 \n", "Q 1697 3084 1428 2787 \n", "Q 1159 2491 1159 1978 \n", "L 1159 0 \n", "L 581 0 \n", "L 581 3500 \n", "L 1159 3500 \n", "L 1159 2956 \n", "Q 1366 3272 1645 3428 \n", "Q 1925 3584 2291 3584 \n", "Q 2894 3584 3203 3211 \n", "Q 3513 2838 3513 2113 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-67\" d=\"M 2906 1791 \n", "Q 2906 2416 2648 2759 \n", "Q 2391 3103 1925 3103 \n", "Q 1463 3103 1205 2759 \n", "Q 947 2416 947 1791 \n", "Q 947 1169 1205 825 \n", "Q 1463 481 1925 481 \n", "Q 2391 481 2648 825 \n", "Q 2906 1169 2906 1791 \n", "z\n", "M 3481 434 \n", "Q 3481 -459 3084 -895 \n", "Q 2688 -1331 1869 -1331 \n", "Q 1566 -1331 1297 -1286 \n", "Q 1028 -1241 775 -1147 \n", "L 775 -588 \n", "Q 1028 -725 1275 -790 \n", "Q 1522 -856 1778 -856 \n", "Q 2344 -856 2625 -561 \n", "Q 2906 -266 2906 331 \n", "L 2906 616 \n", "Q 2728 306 2450 153 \n", "Q 2172 0 1784 0 \n", "Q 1141 0 747 490 \n", "Q 353 981 353 1791 \n", "Q 353 2603 747 3093 \n", "Q 1141 3584 1784 3584 \n", "Q 2172 3584 2450 3431 \n", "Q 2728 3278 2906 2969 \n", "L 2906 3500 \n", "L 3481 3500 \n", "L 3481 434 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-70\" d=\"M 1159 525 \n", "L 1159 -1331 \n", "L 581 -1331 \n", "L 581 3500 \n", "L 1159 3500 \n", "L 1159 2969 \n", "Q 1341 3281 1617 3432 \n", "Q 1894 3584 2278 3584 \n", "Q 2916 3584 3314 3078 \n", "Q 3713 2572 3713 1747 \n", "Q 3713 922 3314 415 \n", "Q 2916 -91 2278 -91 \n", "Q 1894 -91 1617 61 \n", "Q 1341 213 1159 525 \n", "z\n", "M 3116 1747 \n", "Q 3116 2381 2855 2742 \n", "Q 2594 3103 2138 3103 \n", "Q 1681 3103 1420 2742 \n", "Q 1159 2381 1159 1747 \n", "Q 1159 1113 1420 752 \n", "Q 1681 391 2138 391 \n", "Q 2594 391 2855 752 \n", "Q 3116 1113 3116 1747 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-75\" d=\"M 544 1381 \n", "L 544 3500 \n", "L 1119 3500 \n", "L 1119 1403 \n", "Q 1119 906 1312 657 \n", "Q 1506 409 1894 409 \n", "Q 2359 409 2629 706 \n", "Q 2900 1003 2900 1516 \n", "L 2900 3500 \n", "L 3475 3500 \n", "L 3475 0 \n", "L 2900 0 \n", "L 2900 538 \n", "Q 2691 219 2414 64 \n", "Q 2138 -91 1772 -91 \n", "Q 1169 -91 856 284 \n", "Q 544 659 544 1381 \n", "z\n", "M 1991 3584 \n", "L 1991 3584 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-73\" d=\"M 2834 3397 \n", "L 2834 2853 \n", "Q 2591 2978 2328 3040 \n", "Q 2066 3103 1784 3103 \n", "Q 1356 3103 1142 2972 \n", "Q 928 2841 928 2578 \n", "Q 928 2378 1081 2264 \n", "Q 1234 2150 1697 2047 \n", "L 1894 2003 \n", "Q 2506 1872 2764 1633 \n", "Q 3022 1394 3022 966 \n", "Q 3022 478 2636 193 \n", "Q 2250 -91 1575 -91 \n", "Q 1294 -91 989 -36 \n", "Q 684 19 347 128 \n", "L 347 722 \n", "Q 666 556 975 473 \n", "Q 1284 391 1588 391 \n", "Q 1994 391 2212 530 \n", "Q 2431 669 2431 922 \n", "Q 2431 1156 2273 1281 \n", "Q 2116 1406 1581 1522 \n", "L 1381 1569 \n", "Q 847 1681 609 1914 \n", "Q 372 2147 372 2553 \n", "Q 372 3047 722 3315 \n", "Q 1072 3584 1716 3584 \n", "Q 2034 3584 2315 3537 \n", "Q 2597 3491 2834 3397 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "      </defs>\n", "      <use xlink:href=\"#DejaVuSans-53\"/>\n", "      <use xlink:href=\"#DejaVuSans-6f\" x=\"63.476562\"/>\n", "      <use xlink:href=\"#DejaVuSans-72\" x=\"124.658203\"/>\n", "      <use xlink:href=\"#DejaVuSans-74\" x=\"165.771484\"/>\n", "      <use xlink:href=\"#DejaVuSans-65\" x=\"204.980469\"/>\n", "      <use xlink:href=\"#DejaVuSans-64\" x=\"266.503906\"/>\n", "      <use xlink:href=\"#DejaVuSans-20\" x=\"329.980469\"/>\n", "      <use xlink:href=\"#DejaVuSans-74\" x=\"361.767578\"/>\n", "      <use xlink:href=\"#DejaVuSans-72\" x=\"400.976562\"/>\n", "      <use xlink:href=\"#DejaVuSans-61\" x=\"442.089844\"/>\n", "      <use xlink:href=\"#DejaVuSans-69\" x=\"503.369141\"/>\n", "      <use xlink:href=\"#DejaVuSans-6e\" x=\"531.152344\"/>\n", "      <use xlink:href=\"#DejaVuSans-69\" x=\"594.53125\"/>\n", "      <use xlink:href=\"#DejaVuSans-6e\" x=\"622.314453\"/>\n", "      <use xlink:href=\"#DejaVuSans-67\" x=\"685.693359\"/>\n", "      <use xlink:href=\"#DejaVuSans-20\" x=\"749.169922\"/>\n", "      <use xlink:href=\"#DejaVuSans-69\" x=\"780.957031\"/>\n", "      <use xlink:href=\"#DejaVuSans-6e\" x=\"808.740234\"/>\n", "      <use xlink:href=\"#DejaVuSans-70\" x=\"872.119141\"/>\n", "      <use xlink:href=\"#DejaVuSans-75\" x=\"935.595703\"/>\n", "      <use xlink:href=\"#DejaVuSans-74\" x=\"998.974609\"/>\n", "      <use xlink:href=\"#DejaVuSans-73\" x=\"1038.183594\"/>\n", "     </g>\n", "    </g>\n", "   </g>\n", "   <g id=\"matplotlib.axis_2\">\n", "    <g id=\"ytick_1\">\n", "     <g id=\"line2d_4\">\n", "      <defs>\n", "       <path id=\"m7feb6ea594\" d=\"M 0 0 \n", "L -3.5 0 \n", "\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </defs>\n", "      <g>\n", "       <use xlink:href=\"#m7feb6ea594\" x=\"40.603125\" y=\"10.999219\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_5\">\n", "      <!-- 0 -->\n", "      <g transform=\"translate(27.240625 14.798438)scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_2\">\n", "     <g id=\"line2d_5\">\n", "      <g>\n", "       <use xlink:href=\"#m7feb6ea594\" x=\"40.603125\" y=\"33.319219\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_6\">\n", "      <!-- 10 -->\n", "      <g transform=\"translate(20.878125 37.118438)scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-31\" d=\"M 794 531 \n", "L 1825 531 \n", "L 1825 4091 \n", "L 703 3866 \n", "L 703 4441 \n", "L 1819 4666 \n", "L 2450 4666 \n", "L 2450 531 \n", "L 3481 531 \n", "L 3481 0 \n", "L 794 0 \n", "L 794 531 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-31\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"63.623047\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_3\">\n", "     <g id=\"line2d_6\">\n", "      <g>\n", "       <use xlink:href=\"#m7feb6ea594\" x=\"40.603125\" y=\"55.639219\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_7\">\n", "      <!-- 20 -->\n", "      <g transform=\"translate(20.878125 59.438438)scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-32\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"63.623047\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_4\">\n", "     <g id=\"line2d_7\">\n", "      <g>\n", "       <use xlink:href=\"#m7feb6ea594\" x=\"40.603125\" y=\"77.959219\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_8\">\n", "      <!-- 30 -->\n", "      <g transform=\"translate(20.878125 81.758438)scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-33\" d=\"M 2597 2516 \n", "Q 3050 2419 3304 2112 \n", "Q 3559 1806 3559 1356 \n", "Q 3559 666 3084 287 \n", "Q 2609 -91 1734 -91 \n", "Q 1441 -91 1130 -33 \n", "Q 819 25 488 141 \n", "L 488 750 \n", "Q 750 597 1062 519 \n", "Q 1375 441 1716 441 \n", "Q 2309 441 2620 675 \n", "Q 2931 909 2931 1356 \n", "Q 2931 1769 2642 2001 \n", "Q 2353 2234 1838 2234 \n", "L 1294 2234 \n", "L 1294 2753 \n", "L 1863 2753 \n", "Q 2328 2753 2575 2939 \n", "Q 2822 3125 2822 3475 \n", "Q 2822 3834 2567 4026 \n", "Q 2313 4219 1838 4219 \n", "Q 1578 4219 1281 4162 \n", "Q 984 4106 628 3988 \n", "L 628 4550 \n", "Q 988 4650 1302 4700 \n", "Q 1616 4750 1894 4750 \n", "Q 2613 4750 3031 4423 \n", "Q 3450 4097 3450 3541 \n", "Q 3450 3153 3228 2886 \n", "Q 3006 2619 2597 2516 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-33\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"63.623047\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_5\">\n", "     <g id=\"line2d_8\">\n", "      <g>\n", "       <use xlink:href=\"#m7feb6ea594\" x=\"40.603125\" y=\"100.279219\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_9\">\n", "      <!-- 40 -->\n", "      <g transform=\"translate(20.878125 104.078438)scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-34\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"63.623047\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"text_10\">\n", "     <!-- Sorted testing inputs -->\n", "     <g transform=\"translate(14.798438 118.160563)rotate(-90)scale(0.1 -0.1)\">\n", "      <use xlink:href=\"#DejaVuSans-53\"/>\n", "      <use xlink:href=\"#DejaVuSans-6f\" x=\"63.476562\"/>\n", "      <use xlink:href=\"#DejaVuSans-72\" x=\"124.658203\"/>\n", "      <use xlink:href=\"#DejaVuSans-74\" x=\"165.771484\"/>\n", "      <use xlink:href=\"#DejaVuSans-65\" x=\"204.980469\"/>\n", "      <use xlink:href=\"#DejaVuSans-64\" x=\"266.503906\"/>\n", "      <use xlink:href=\"#DejaVuSans-20\" x=\"329.980469\"/>\n", "      <use xlink:href=\"#DejaVuSans-74\" x=\"361.767578\"/>\n", "      <use xlink:href=\"#DejaVuSans-65\" x=\"400.976562\"/>\n", "      <use xlink:href=\"#DejaVuSans-73\" x=\"462.5\"/>\n", "      <use xlink:href=\"#DejaVuSans-74\" x=\"514.599609\"/>\n", "      <use xlink:href=\"#DejaVuSans-69\" x=\"553.808594\"/>\n", "      <use xlink:href=\"#DejaVuSans-6e\" x=\"581.591797\"/>\n", "      <use xlink:href=\"#DejaVuSans-67\" x=\"644.970703\"/>\n", "      <use xlink:href=\"#DejaVuSans-20\" x=\"708.447266\"/>\n", "      <use xlink:href=\"#DejaVuSans-69\" x=\"740.234375\"/>\n", "      <use xlink:href=\"#DejaVuSans-6e\" x=\"768.017578\"/>\n", "      <use xlink:href=\"#DejaVuSans-70\" x=\"831.396484\"/>\n", "      <use xlink:href=\"#DejaVuSans-75\" x=\"894.873047\"/>\n", "      <use xlink:href=\"#DejaVuSans-74\" x=\"958.251953\"/>\n", "      <use xlink:href=\"#DejaVuSans-73\" x=\"997.460938\"/>\n", "     </g>\n", "    </g>\n", "   </g>\n", "   <g id=\"patch_3\">\n", "    <path d=\"M 40.**********.483219 \n", "L 40.603125 9.883219 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_4\">\n", "    <path d=\"M 152.**********.483219 \n", "L 152.203125 9.883219 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_5\">\n", "    <path d=\"M 40.**********.483219 \n", "L 152.**********.483219 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_6\">\n", "    <path d=\"M 40.603125 9.883219 \n", "L 152.203125 9.883219 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "  </g>\n", "  <g id=\"axes_2\">\n", "   <g id=\"patch_7\">\n", "    <path d=\"M 159.178125 107.263219 \n", "L 163.336125 107.263219 \n", "L 163.336125 24.103219 \n", "L 159.178125 24.103219 \n", "z\n", "\" style=\"fill: #ffffff\"/>\n", "   </g>\n", "   <g id=\"patch_8\">\n", "    <path clip-path=\"url(#p6b94195695)\" style=\"fill: #ffffff; stroke: #ffffff; stroke-width: 0.01; stroke-linejoin: miter\"/>\n", "   </g>\n", "   <image xlink:href=\"data:image/png;base64,\n", "iVBORw0KGgoAAAANSUhEUgAAAAYAAABzCAYAAAC7I3M6AAAAv0lEQVR4nNWVQQrDQAwDXfD/v9pLT1l71Q/sBBQMpTmukMbWhuSlz1txeDK0T+d3wrYFmyFm9BTjZsFjVY/g6GgSyKFaLsOHt82gKPlT+QvaJUbVWNS/waldjqKXGh24oB9lO/RL+AOBGGoSFjLsqDm4mj6W/lSFDDcK4ZvGbXTQ36BJQAa0Hlk2nAWCF081xsBKWiQcjyNykwM6jCx0nM8jO2wH74GbQ5S/IMIXMbASnAodFwllR/mOy76Puam+t3oOUronbv0AAAAASUVORK5CYII=\" id=\"imageb1d26b6869\" transform=\"scale(1 -1)translate(0 -82.8)\" x=\"159.12\" y=\"-23.76\" width=\"4.32\" height=\"82.8\"/>\n", "   <g id=\"matplotlib.axis_3\">\n", "    <g id=\"ytick_6\">\n", "     <g id=\"line2d_9\">\n", "      <defs>\n", "       <path id=\"m22678b18ed\" d=\"M 0 0 \n", "L 3.5 0 \n", "\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </defs>\n", "      <g>\n", "       <use xlink:href=\"#m22678b18ed\" x=\"163.336125\" y=\"86.326859\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_11\">\n", "      <!-- 0.02 -->\n", "      <g transform=\"translate(170.336125 90.126078)scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-2e\" d=\"M 684 794 \n", "L 1344 794 \n", "L 1344 0 \n", "L 684 0 \n", "L 684 794 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"95.410156\"/>\n", "       <use xlink:href=\"#DejaVuSans-32\" x=\"159.033203\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_7\">\n", "     <g id=\"line2d_10\">\n", "      <g>\n", "       <use xlink:href=\"#m22678b18ed\" x=\"163.336125\" y=\"65.38984\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_12\">\n", "      <!-- 0.04 -->\n", "      <g transform=\"translate(170.336125 69.189059)scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"95.410156\"/>\n", "       <use xlink:href=\"#DejaVuSans-34\" x=\"159.033203\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_8\">\n", "     <g id=\"line2d_11\">\n", "      <g>\n", "       <use xlink:href=\"#m22678b18ed\" x=\"163.336125\" y=\"44.452821\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_13\">\n", "      <!-- 0.06 -->\n", "      <g transform=\"translate(170.336125 48.25204)scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-36\" d=\"M 2113 2584 \n", "Q 1688 2584 1439 2293 \n", "Q 1191 2003 1191 1497 \n", "Q 1191 994 1439 701 \n", "Q 1688 409 2113 409 \n", "Q 2538 409 2786 701 \n", "Q 3034 994 3034 1497 \n", "Q 3034 2003 2786 2293 \n", "Q 2538 2584 2113 2584 \n", "z\n", "M 3366 4563 \n", "L 3366 3988 \n", "Q 3128 4100 2886 4159 \n", "Q 2644 4219 2406 4219 \n", "Q 1781 4219 1451 3797 \n", "Q 1122 3375 1075 2522 \n", "Q 1259 2794 1537 2939 \n", "Q 1816 3084 2150 3084 \n", "Q 2853 3084 3261 2657 \n", "Q 3669 2231 3669 1497 \n", "Q 3669 778 3244 343 \n", "Q 2819 -91 2113 -91 \n", "Q 1303 -91 875 529 \n", "Q 447 1150 447 2328 \n", "Q 447 3434 972 4092 \n", "Q 1497 4750 2381 4750 \n", "Q 2619 4750 2861 4703 \n", "Q 3103 4656 3366 4563 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"95.410156\"/>\n", "       <use xlink:href=\"#DejaVuSans-36\" x=\"159.033203\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "   </g>\n", "   <g id=\"LineCollection_1\"/>\n", "   <g id=\"patch_9\">\n", "    <path d=\"M 159.178125 107.263219 \n", "L 161.257125 107.263219 \n", "L 163.336125 107.263219 \n", "L 163.336125 24.103219 \n", "L 161.257125 24.103219 \n", "L 159.178125 24.103219 \n", "L 159.178125 107.263219 \n", "z\n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "  </g>\n", " </g>\n", " <defs>\n", "  <clipPath id=\"p9f7c4196c7\">\n", "   <rect x=\"40.603125\" y=\"9.883219\" width=\"111.6\" height=\"111.6\"/>\n", "  </clipPath>\n", "  <clipPath id=\"p6b94195695\">\n", "   <rect x=\"159.178125\" y=\"24.103219\" width=\"4.158\" height=\"83.16\"/>\n", "  </clipPath>\n", " </defs>\n", "</svg>\n"], "text/plain": ["<Figure size 250x250 with 2 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["d2l.show_heatmaps(attention_weights.unsqueeze(0).unsqueeze(0),\n", "                  xlabel='Sorted training inputs',\n", "                  ylabel='Sorted testing inputs')"]}, {"cell_type": "markdown", "id": "a620a425", "metadata": {"origin_pos": 31}, "source": ["## [**带参数注意力汇聚**]\n", "\n", "非参数的Nadaraya-Watson核回归具有*一致性*（consistency）的优点：\n", "如果有足够的数据，此模型会收敛到最优结果。\n", "尽管如此，我们还是可以轻松地将可学习的参数集成到注意力汇聚中。\n", "\n", "例如，与 :eqref:`eq_nadar<PERSON>-watson-gaussian`略有不同，\n", "在下面的查询$x$和键$x_i$之间的距离乘以可学习参数$w$：\n", "\n", "$$\\begin{aligned}f(x) &= \\sum_{i=1}^n \\alpha(x, x_i) y_i \\\\&= \\sum_{i=1}^n \\frac{\\exp\\left(-\\frac{1}{2}((x - x_i)w)^2\\right)}{\\sum_{j=1}^n \\exp\\left(-\\frac{1}{2}((x - x_j)w)^2\\right)} y_i \\\\&= \\sum_{i=1}^n \\mathrm{softmax}\\left(-\\frac{1}{2}((x - x_i)w)^2\\right) y_i.\\end{aligned}$$\n", ":eqlabel:`eq_nadaraya-watson-gaussian-para`\n", "\n", "本节的余下部分将通过训练这个模型\n", " :eqref:`eq_nadar<PERSON>-watson-gaussian-para`来学习注意力汇聚的参数。\n", "\n", "### 批量矩阵乘法\n", "\n", ":label:`subsec_batch_dot`\n", "\n", "为了更有效地计算小批量数据的注意力，\n", "我们可以利用深度学习开发框架中提供的批量矩阵乘法。\n", "\n", "假设第一个小批量数据包含$n$个矩阵$\\mathbf{X}_1,\\ldots, \\mathbf{X}_n$，\n", "形状为$a\\times b$，\n", "第二个小批量包含$n$个矩阵$\\mathbf{Y}_1, \\ldots, \\mathbf{Y}_n$，\n", "形状为$b\\times c$。\n", "它们的批量矩阵乘法得到$n$个矩阵\n", "$\\mathbf{X}_1\\mathbf{Y}_1, \\ldots, \\mathbf{X}_n\\mathbf{Y}_n$，\n", "形状为$a\\times c$。\n", "因此，[**假定两个张量的形状分别是$(n,a,b)$和$(n,b,c)$，\n", "它们的批量矩阵乘法输出的形状为$(n,a,c)$**]。\n"]}, {"cell_type": "code", "execution_count": 8, "id": "b6aba15e", "metadata": {"execution": {"iopub.execute_input": "2022-12-07T17:03:17.657875Z", "iopub.status.busy": "2022-12-07T17:03:17.657304Z", "iopub.status.idle": "2022-12-07T17:03:17.663914Z", "shell.execute_reply": "2022-12-07T17:03:17.663166Z"}, "origin_pos": 33, "tab": ["pytorch"]}, "outputs": [{"data": {"text/plain": ["torch.<PERSON><PERSON>([2, 1, 6])"]}, "execution_count": 8, "metadata": {}, "output_type": "execute_result"}], "source": ["X = torch.ones((2, 1, 4))\n", "Y = torch.ones((2, 4, 6))\n", "torch.bmm(X, Y).shape"]}, {"cell_type": "markdown", "id": "71c2b13d", "metadata": {"origin_pos": 36}, "source": ["在注意力机制的背景中，我们可以[**使用小批量矩阵乘法来计算小批量数据中的加权平均值**]。\n"]}, {"cell_type": "code", "execution_count": 9, "id": "af046843", "metadata": {"execution": {"iopub.execute_input": "2022-12-07T17:03:17.667365Z", "iopub.status.busy": "2022-12-07T17:03:17.666830Z", "iopub.status.idle": "2022-12-07T17:03:17.674665Z", "shell.execute_reply": "2022-12-07T17:03:17.673922Z"}, "origin_pos": 38, "tab": ["pytorch"]}, "outputs": [{"data": {"text/plain": ["tensor([[[ 4.5000]],\n", "\n", "        [[14.5000]]])"]}, "execution_count": 9, "metadata": {}, "output_type": "execute_result"}], "source": ["weights = torch.ones((2, 10)) * 0.1\n", "values = torch.arange(20.0).reshape((2, 10))\n", "torch.bmm(weights.unsqueeze(1), values.unsqueeze(-1))"]}, {"cell_type": "markdown", "id": "64852514", "metadata": {"origin_pos": 41}, "source": ["### 定义模型\n", "\n", "基于 :eqref:`eq_nadar<PERSON>-watson-gaussian-para`中的\n", "[**带参数的注意力汇聚**]，使用小批量矩阵乘法，\n", "定义Nadaraya-Watson核回归的带参数版本为：\n"]}, {"cell_type": "code", "execution_count": 10, "id": "369f94cd", "metadata": {"execution": {"iopub.execute_input": "2022-12-07T17:03:17.678202Z", "iopub.status.busy": "2022-12-07T17:03:17.677575Z", "iopub.status.idle": "2022-12-07T17:03:17.683574Z", "shell.execute_reply": "2022-12-07T17:03:17.682829Z"}, "origin_pos": 43, "tab": ["pytorch"]}, "outputs": [], "source": ["class NWKernelRegression(nn.Module):\n", "    def __init__(self, **kwargs):\n", "        super().__init__(**kwargs)\n", "        self.w = nn.Parameter(torch.rand((1,), requires_grad=True))\n", "\n", "    def forward(self, queries, keys, values):\n", "        # queries和attention_weights的形状为(查询个数，“键－值”对个数)\n", "        queries = queries.repeat_interleave(keys.shape[1]).reshape((-1, keys.shape[1]))\n", "        self.attention_weights = nn.functional.softmax(\n", "            -((queries - keys) * self.w)**2 / 2, dim=1)\n", "        # values的形状为(查询个数，“键－值”对个数)\n", "        return torch.bmm(self.attention_weights.unsqueeze(1),\n", "                         values.unsqueeze(-1)).reshape(-1)"]}, {"cell_type": "markdown", "id": "9143e03d", "metadata": {"origin_pos": 46}, "source": ["### 训练\n", "\n", "接下来，[**将训练数据集变换为键和值**]用于训练注意力模型。\n", "在带参数的注意力汇聚模型中，\n", "任何一个训练样本的输入都会和除自己以外的所有训练样本的“键－值”对进行计算，\n", "从而得到其对应的预测输出。\n"]}, {"cell_type": "code", "execution_count": 11, "id": "6e016b42", "metadata": {"execution": {"iopub.execute_input": "2022-12-07T17:03:17.686624Z", "iopub.status.busy": "2022-12-07T17:03:17.686342Z", "iopub.status.idle": "2022-12-07T17:03:17.692130Z", "shell.execute_reply": "2022-12-07T17:03:17.691359Z"}, "origin_pos": 48, "tab": ["pytorch"]}, "outputs": [], "source": ["# X_tile的形状:(n_train，n_train)，每一行都包含着相同的训练输入\n", "X_tile = x_train.repeat((n_train, 1))\n", "# Y_tile的形状:(n_train，n_train)，每一行都包含着相同的训练输出\n", "Y_tile = y_train.repeat((n_train, 1))\n", "# keys的形状:('n_train'，'n_train'-1)\n", "keys = X_tile[(1 - torch.eye(n_train)).type(torch.bool)].reshape((n_train, -1))\n", "# values的形状:('n_train'，'n_train'-1)\n", "values = Y_tile[(1 - torch.eye(n_train)).type(torch.bool)].reshape((n_train, -1))"]}, {"cell_type": "markdown", "id": "928e14dd", "metadata": {"origin_pos": 51}, "source": ["[**训练带参数的注意力汇聚模型**]时，使用平方损失函数和随机梯度下降。\n"]}, {"cell_type": "code", "execution_count": 12, "id": "eb4d677a", "metadata": {"execution": {"iopub.execute_input": "2022-12-07T17:03:17.695258Z", "iopub.status.busy": "2022-12-07T17:03:17.694975Z", "iopub.status.idle": "2022-12-07T17:03:18.413073Z", "shell.execute_reply": "2022-12-07T17:03:18.412262Z"}, "origin_pos": 53, "tab": ["pytorch"]}, "outputs": [{"data": {"image/svg+xml": ["<?xml version=\"1.0\" encoding=\"utf-8\" standalone=\"no\"?>\n", "<!DOCTYPE svg PUBLIC \"-//W3C//DTD SVG 1.1//EN\"\n", "  \"http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd\">\n", "<svg xmlns:xlink=\"http://www.w3.org/1999/xlink\" width=\"246.284375pt\" height=\"183.35625pt\" viewBox=\"0 0 246.**********.35625\" xmlns=\"http://www.w3.org/2000/svg\" version=\"1.1\">\n", " <metadata>\n", "  <rdf:RDF xmlns:dc=\"http://purl.org/dc/elements/1.1/\" xmlns:cc=\"http://creativecommons.org/ns#\" xmlns:rdf=\"http://www.w3.org/1999/02/22-rdf-syntax-ns#\">\n", "   <cc:Work>\n", "    <dc:type rdf:resource=\"http://purl.org/dc/dcmitype/StillImage\"/>\n", "    <dc:date>2022-12-07T17:03:18.379413</dc:date>\n", "    <dc:format>image/svg+xml</dc:format>\n", "    <dc:creator>\n", "     <cc:Agent>\n", "      <dc:title>Matplotlib v3.5.1, https://matplotlib.org/</dc:title>\n", "     </cc:Agent>\n", "    </dc:creator>\n", "   </cc:Work>\n", "  </rdf:RDF>\n", " </metadata>\n", " <defs>\n", "  <style type=\"text/css\">*{stroke-linejoin: round; stroke-linecap: butt}</style>\n", " </defs>\n", " <g id=\"figure_1\">\n", "  <g id=\"patch_1\">\n", "   <path d=\"M 0 183.35625 \n", "L 246.**********.35625 \n", "L 246.284375 0 \n", "L 0 0 \n", "z\n", "\" style=\"fill: #ffffff\"/>\n", "  </g>\n", "  <g id=\"axes_1\">\n", "   <g id=\"patch_2\">\n", "    <path d=\"M 40.**********.8 \n", "L 235.**********.8 \n", "L 235.903125 7.2 \n", "L 40.603125 7.2 \n", "z\n", "\" style=\"fill: #ffffff\"/>\n", "   </g>\n", "   <g id=\"matplotlib.axis_1\">\n", "    <g id=\"xtick_1\">\n", "     <g id=\"line2d_1\">\n", "      <path d=\"M 40.**********.8 \n", "L 40.603125 7.2 \n", "\" clip-path=\"url(#pca94a13d1e)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_2\">\n", "      <defs>\n", "       <path id=\"m77cd13dc1b\" d=\"M 0 0 \n", "L 0 3.5 \n", "\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </defs>\n", "      <g>\n", "       <use xlink:href=\"#m77cd13dc1b\" x=\"40.603125\" y=\"145.8\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_1\">\n", "      <!-- 1 -->\n", "      <g transform=\"translate(37.421875 160.398438)scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-31\" d=\"M 794 531 \n", "L 1825 531 \n", "L 1825 4091 \n", "L 703 3866 \n", "L 703 4441 \n", "L 1819 4666 \n", "L 2450 4666 \n", "L 2450 531 \n", "L 3481 531 \n", "L 3481 0 \n", "L 794 0 \n", "L 794 531 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-31\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_2\">\n", "     <g id=\"line2d_3\">\n", "      <path d=\"M 89.428125 145.8 \n", "L 89.428125 7.2 \n", "\" clip-path=\"url(#pca94a13d1e)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_4\">\n", "      <g>\n", "       <use xlink:href=\"#m77cd13dc1b\" x=\"89.428125\" y=\"145.8\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_2\">\n", "      <!-- 2 -->\n", "      <g transform=\"translate(86.246875 160.398438)scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-32\" d=\"M 1228 531 \n", "L 3431 531 \n", "L 3431 0 \n", "L 469 0 \n", "L 469 531 \n", "Q 828 903 1448 1529 \n", "Q 2069 2156 2228 2338 \n", "Q 2531 2678 2651 2914 \n", "Q 2772 3150 2772 3378 \n", "Q 2772 3750 2511 3984 \n", "Q 2250 4219 1831 4219 \n", "Q 1534 4219 1204 4116 \n", "Q 875 4013 500 3803 \n", "L 500 4441 \n", "Q 881 4594 1212 4672 \n", "Q 1544 4750 1819 4750 \n", "Q 2544 4750 2975 4387 \n", "Q 3406 4025 3406 3419 \n", "Q 3406 3131 3298 2873 \n", "Q 3191 2616 2906 2266 \n", "Q 2828 2175 2409 1742 \n", "Q 1991 1309 1228 531 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-32\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_3\">\n", "     <g id=\"line2d_5\">\n", "      <path d=\"M 138.253125 145.8 \n", "L 138.253125 7.2 \n", "\" clip-path=\"url(#pca94a13d1e)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_6\">\n", "      <g>\n", "       <use xlink:href=\"#m77cd13dc1b\" x=\"138.253125\" y=\"145.8\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_3\">\n", "      <!-- 3 -->\n", "      <g transform=\"translate(135.071875 160.398438)scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-33\" d=\"M 2597 2516 \n", "Q 3050 2419 3304 2112 \n", "Q 3559 1806 3559 1356 \n", "Q 3559 666 3084 287 \n", "Q 2609 -91 1734 -91 \n", "Q 1441 -91 1130 -33 \n", "Q 819 25 488 141 \n", "L 488 750 \n", "Q 750 597 1062 519 \n", "Q 1375 441 1716 441 \n", "Q 2309 441 2620 675 \n", "Q 2931 909 2931 1356 \n", "Q 2931 1769 2642 2001 \n", "Q 2353 2234 1838 2234 \n", "L 1294 2234 \n", "L 1294 2753 \n", "L 1863 2753 \n", "Q 2328 2753 2575 2939 \n", "Q 2822 3125 2822 3475 \n", "Q 2822 3834 2567 4026 \n", "Q 2313 4219 1838 4219 \n", "Q 1578 4219 1281 4162 \n", "Q 984 4106 628 3988 \n", "L 628 4550 \n", "Q 988 4650 1302 4700 \n", "Q 1616 4750 1894 4750 \n", "Q 2613 4750 3031 4423 \n", "Q 3450 4097 3450 3541 \n", "Q 3450 3153 3228 2886 \n", "Q 3006 2619 2597 2516 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-33\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_4\">\n", "     <g id=\"line2d_7\">\n", "      <path d=\"M 187.078125 145.8 \n", "L 187.078125 7.2 \n", "\" clip-path=\"url(#pca94a13d1e)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_8\">\n", "      <g>\n", "       <use xlink:href=\"#m77cd13dc1b\" x=\"187.078125\" y=\"145.8\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_4\">\n", "      <!-- 4 -->\n", "      <g transform=\"translate(183.896875 160.398438)scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-34\" d=\"M 2419 4116 \n", "L 825 1625 \n", "L 2419 1625 \n", "L 2419 4116 \n", "z\n", "M 2253 4666 \n", "L 3047 4666 \n", "L 3047 1625 \n", "L 3713 1625 \n", "L 3713 1100 \n", "L 3047 1100 \n", "L 3047 0 \n", "L 2419 0 \n", "L 2419 1100 \n", "L 313 1100 \n", "L 313 1709 \n", "L 2253 4666 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-34\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_5\">\n", "     <g id=\"line2d_9\">\n", "      <path d=\"M 235.**********.8 \n", "L 235.903125 7.2 \n", "\" clip-path=\"url(#pca94a13d1e)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_10\">\n", "      <g>\n", "       <use xlink:href=\"#m77cd13dc1b\" x=\"235.903125\" y=\"145.8\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_5\">\n", "      <!-- 5 -->\n", "      <g transform=\"translate(232.721875 160.398438)scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-35\" d=\"M 691 4666 \n", "L 3169 4666 \n", "L 3169 4134 \n", "L 1269 4134 \n", "L 1269 2991 \n", "Q 1406 3038 1543 3061 \n", "Q 1681 3084 1819 3084 \n", "Q 2600 3084 3056 2656 \n", "Q 3513 2228 3513 1497 \n", "Q 3513 744 3044 326 \n", "Q 2575 -91 1722 -91 \n", "Q 1428 -91 1123 -41 \n", "Q 819 9 494 109 \n", "L 494 744 \n", "Q 775 591 1075 516 \n", "Q 1375 441 1709 441 \n", "Q 2250 441 2565 725 \n", "Q 2881 1009 2881 1497 \n", "Q 2881 1984 2565 2268 \n", "Q 2250 2553 1709 2553 \n", "Q 1456 2553 1204 2497 \n", "Q 953 2441 691 2322 \n", "L 691 4666 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-35\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"text_6\">\n", "     <!-- epoch -->\n", "     <g transform=\"translate(123.025 174.076563)scale(0.1 -0.1)\">\n", "      <defs>\n", "       <path id=\"DejaVuSans-65\" d=\"M 3597 1894 \n", "L 3597 1613 \n", "L 953 1613 \n", "Q 991 1019 1311 708 \n", "Q 1631 397 2203 397 \n", "Q 2534 397 2845 478 \n", "Q 3156 559 3463 722 \n", "L 3463 178 \n", "Q 3153 47 2828 -22 \n", "Q 2503 -91 2169 -91 \n", "Q 1331 -91 842 396 \n", "Q 353 884 353 1716 \n", "Q 353 2575 817 3079 \n", "Q 1281 3584 2069 3584 \n", "Q 2775 3584 3186 3129 \n", "Q 3597 2675 3597 1894 \n", "z\n", "M 3022 2063 \n", "Q 3016 2534 2758 2815 \n", "Q 2500 3097 2075 3097 \n", "Q 1594 3097 1305 2825 \n", "Q 1016 2553 972 2059 \n", "L 3022 2063 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-70\" d=\"M 1159 525 \n", "L 1159 -1331 \n", "L 581 -1331 \n", "L 581 3500 \n", "L 1159 3500 \n", "L 1159 2969 \n", "Q 1341 3281 1617 3432 \n", "Q 1894 3584 2278 3584 \n", "Q 2916 3584 3314 3078 \n", "Q 3713 2572 3713 1747 \n", "Q 3713 922 3314 415 \n", "Q 2916 -91 2278 -91 \n", "Q 1894 -91 1617 61 \n", "Q 1341 213 1159 525 \n", "z\n", "M 3116 1747 \n", "Q 3116 2381 2855 2742 \n", "Q 2594 3103 2138 3103 \n", "Q 1681 3103 1420 2742 \n", "Q 1159 2381 1159 1747 \n", "Q 1159 1113 1420 752 \n", "Q 1681 391 2138 391 \n", "Q 2594 391 2855 752 \n", "Q 3116 1113 3116 1747 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-6f\" d=\"M 1959 3097 \n", "Q 1497 3097 1228 2736 \n", "Q 959 2375 959 1747 \n", "Q 959 1119 1226 758 \n", "Q 1494 397 1959 397 \n", "Q 2419 397 2687 759 \n", "Q 2956 1122 2956 1747 \n", "Q 2956 2369 2687 2733 \n", "Q 2419 3097 1959 3097 \n", "z\n", "M 1959 3584 \n", "Q 2709 3584 3137 3096 \n", "Q 3566 2609 3566 1747 \n", "Q 3566 888 3137 398 \n", "Q 2709 -91 1959 -91 \n", "Q 1206 -91 779 398 \n", "Q 353 888 353 1747 \n", "Q 353 2609 779 3096 \n", "Q 1206 3584 1959 3584 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-63\" d=\"M 3122 3366 \n", "L 3122 2828 \n", "Q 2878 2963 2633 3030 \n", "Q 2388 3097 2138 3097 \n", "Q 1578 3097 1268 2742 \n", "Q 959 2388 959 1747 \n", "Q 959 1106 1268 751 \n", "Q 1578 397 2138 397 \n", "Q 2388 397 2633 464 \n", "Q 2878 531 3122 666 \n", "L 3122 134 \n", "Q 2881 22 2623 -34 \n", "Q 2366 -91 2075 -91 \n", "Q 1284 -91 818 406 \n", "Q 353 903 353 1747 \n", "Q 353 2603 823 3093 \n", "Q 1294 3584 2113 3584 \n", "Q 2378 3584 2631 3529 \n", "Q 2884 3475 3122 3366 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-68\" d=\"M 3513 2113 \n", "L 3513 0 \n", "L 2938 0 \n", "L 2938 2094 \n", "Q 2938 2591 2744 2837 \n", "Q 2550 3084 2163 3084 \n", "Q 1697 3084 1428 2787 \n", "Q 1159 2491 1159 1978 \n", "L 1159 0 \n", "L 581 0 \n", "L 581 4863 \n", "L 1159 4863 \n", "L 1159 2956 \n", "Q 1366 3272 1645 3428 \n", "Q 1925 3584 2291 3584 \n", "Q 2894 3584 3203 3211 \n", "Q 3513 2838 3513 2113 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "      </defs>\n", "      <use xlink:href=\"#DejaVuSans-65\"/>\n", "      <use xlink:href=\"#DejaVuSans-70\" x=\"61.523438\"/>\n", "      <use xlink:href=\"#DejaVuSans-6f\" x=\"125\"/>\n", "      <use xlink:href=\"#DejaVuSans-63\" x=\"186.181641\"/>\n", "      <use xlink:href=\"#DejaVuSans-68\" x=\"241.162109\"/>\n", "     </g>\n", "    </g>\n", "   </g>\n", "   <g id=\"matplotlib.axis_2\">\n", "    <g id=\"ytick_1\">\n", "     <g id=\"line2d_11\">\n", "      <path d=\"M 40.603125 126.169391 \n", "L 235.903125 126.169391 \n", "\" clip-path=\"url(#pca94a13d1e)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_12\">\n", "      <defs>\n", "       <path id=\"mac4b71acd0\" d=\"M 0 0 \n", "L -3.5 0 \n", "\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </defs>\n", "      <g>\n", "       <use xlink:href=\"#mac4b71acd0\" x=\"40.603125\" y=\"126.169391\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_7\">\n", "      <!-- 38 -->\n", "      <g transform=\"translate(20.878125 129.96861)scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-38\" d=\"M 2034 2216 \n", "Q 1584 2216 1326 1975 \n", "Q 1069 1734 1069 1313 \n", "Q 1069 891 1326 650 \n", "Q 1584 409 2034 409 \n", "Q 2484 409 2743 651 \n", "Q 3003 894 3003 1313 \n", "Q 3003 1734 2745 1975 \n", "Q 2488 2216 2034 2216 \n", "z\n", "M 1403 2484 \n", "Q 997 2584 770 2862 \n", "Q 544 3141 544 3541 \n", "Q 544 4100 942 4425 \n", "Q 1341 4750 2034 4750 \n", "Q 2731 4750 3128 4425 \n", "Q 3525 4100 3525 3541 \n", "Q 3525 3141 3298 2862 \n", "Q 3072 2584 2669 2484 \n", "Q 3125 2378 3379 2068 \n", "Q 3634 1759 3634 1313 \n", "Q 3634 634 3220 271 \n", "Q 2806 -91 2034 -91 \n", "Q 1263 -91 848 271 \n", "Q 434 634 434 1313 \n", "Q 434 1759 690 2068 \n", "Q 947 2378 1403 2484 \n", "z\n", "M 1172 3481 \n", "Q 1172 3119 1398 2916 \n", "Q 1625 2713 2034 2713 \n", "Q 2441 2713 2670 2916 \n", "Q 2900 3119 2900 3481 \n", "Q 2900 3844 2670 4047 \n", "Q 2441 4250 2034 4250 \n", "Q 1625 4250 1398 4047 \n", "Q 1172 3844 1172 3481 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-33\"/>\n", "       <use xlink:href=\"#DejaVuSans-38\" x=\"63.623047\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_2\">\n", "     <g id=\"line2d_13\">\n", "      <path d=\"M 40.603125 100.088017 \n", "L 235.903125 100.088017 \n", "\" clip-path=\"url(#pca94a13d1e)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_14\">\n", "      <g>\n", "       <use xlink:href=\"#mac4b71acd0\" x=\"40.603125\" y=\"100.088017\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_8\">\n", "      <!-- 40 -->\n", "      <g transform=\"translate(20.878125 103.887236)scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-30\" d=\"M 2034 4250 \n", "Q 1547 4250 1301 3770 \n", "Q 1056 3291 1056 2328 \n", "Q 1056 1369 1301 889 \n", "Q 1547 409 2034 409 \n", "Q 2525 409 2770 889 \n", "Q 3016 1369 3016 2328 \n", "Q 3016 3291 2770 3770 \n", "Q 2525 4250 2034 4250 \n", "z\n", "M 2034 4750 \n", "Q 2819 4750 3233 4129 \n", "Q 3647 3509 3647 2328 \n", "Q 3647 1150 3233 529 \n", "Q 2819 -91 2034 -91 \n", "Q 1250 -91 836 529 \n", "Q 422 1150 422 2328 \n", "Q 422 3509 836 4129 \n", "Q 1250 4750 2034 4750 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-34\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"63.623047\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_3\">\n", "     <g id=\"line2d_15\">\n", "      <path d=\"M 40.603125 74.006642 \n", "L 235.903125 74.006642 \n", "\" clip-path=\"url(#pca94a13d1e)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_16\">\n", "      <g>\n", "       <use xlink:href=\"#mac4b71acd0\" x=\"40.603125\" y=\"74.006642\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_9\">\n", "      <!-- 42 -->\n", "      <g transform=\"translate(20.878125 77.805861)scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-34\"/>\n", "       <use xlink:href=\"#DejaVuSans-32\" x=\"63.623047\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_4\">\n", "     <g id=\"line2d_17\">\n", "      <path d=\"M 40.603125 47.925267 \n", "L 235.903125 47.925267 \n", "\" clip-path=\"url(#pca94a13d1e)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_18\">\n", "      <g>\n", "       <use xlink:href=\"#mac4b71acd0\" x=\"40.603125\" y=\"47.925267\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_10\">\n", "      <!-- 44 -->\n", "      <g transform=\"translate(20.878125 51.724486)scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-34\"/>\n", "       <use xlink:href=\"#DejaVuSans-34\" x=\"63.623047\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_5\">\n", "     <g id=\"line2d_19\">\n", "      <path d=\"M 40.603125 21.843893 \n", "L 235.903125 21.843893 \n", "\" clip-path=\"url(#pca94a13d1e)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_20\">\n", "      <g>\n", "       <use xlink:href=\"#mac4b71acd0\" x=\"40.603125\" y=\"21.843893\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_11\">\n", "      <!-- 46 -->\n", "      <g transform=\"translate(20.878125 25.643112)scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-36\" d=\"M 2113 2584 \n", "Q 1688 2584 1439 2293 \n", "Q 1191 2003 1191 1497 \n", "Q 1191 994 1439 701 \n", "Q 1688 409 2113 409 \n", "Q 2538 409 2786 701 \n", "Q 3034 994 3034 1497 \n", "Q 3034 2003 2786 2293 \n", "Q 2538 2584 2113 2584 \n", "z\n", "M 3366 4563 \n", "L 3366 3988 \n", "Q 3128 4100 2886 4159 \n", "Q 2644 4219 2406 4219 \n", "Q 1781 4219 1451 3797 \n", "Q 1122 3375 1075 2522 \n", "Q 1259 2794 1537 2939 \n", "Q 1816 3084 2150 3084 \n", "Q 2853 3084 3261 2657 \n", "Q 3669 2231 3669 1497 \n", "Q 3669 778 3244 343 \n", "Q 2819 -91 2113 -91 \n", "Q 1303 -91 875 529 \n", "Q 447 1150 447 2328 \n", "Q 447 3434 972 4092 \n", "Q 1497 4750 2381 4750 \n", "Q 2619 4750 2861 4703 \n", "Q 3103 4656 3366 4563 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-34\"/>\n", "       <use xlink:href=\"#DejaVuSans-36\" x=\"63.623047\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"text_12\">\n", "     <!-- loss -->\n", "     <g transform=\"translate(14.798437 86.157813)rotate(-90)scale(0.1 -0.1)\">\n", "      <defs>\n", "       <path id=\"DejaVuSans-6c\" d=\"M 603 4863 \n", "L 1178 4863 \n", "L 1178 0 \n", "L 603 0 \n", "L 603 4863 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-73\" d=\"M 2834 3397 \n", "L 2834 2853 \n", "Q 2591 2978 2328 3040 \n", "Q 2066 3103 1784 3103 \n", "Q 1356 3103 1142 2972 \n", "Q 928 2841 928 2578 \n", "Q 928 2378 1081 2264 \n", "Q 1234 2150 1697 2047 \n", "L 1894 2003 \n", "Q 2506 1872 2764 1633 \n", "Q 3022 1394 3022 966 \n", "Q 3022 478 2636 193 \n", "Q 2250 -91 1575 -91 \n", "Q 1294 -91 989 -36 \n", "Q 684 19 347 128 \n", "L 347 722 \n", "Q 666 556 975 473 \n", "Q 1284 391 1588 391 \n", "Q 1994 391 2212 530 \n", "Q 2431 669 2431 922 \n", "Q 2431 1156 2273 1281 \n", "Q 2116 1406 1581 1522 \n", "L 1381 1569 \n", "Q 847 1681 609 1914 \n", "Q 372 2147 372 2553 \n", "Q 372 3047 722 3315 \n", "Q 1072 3584 1716 3584 \n", "Q 2034 3584 2315 3537 \n", "Q 2597 3491 2834 3397 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "      </defs>\n", "      <use xlink:href=\"#DejaVuSans-6c\"/>\n", "      <use xlink:href=\"#DejaVuSans-6f\" x=\"27.783203\"/>\n", "      <use xlink:href=\"#DejaVuSans-73\" x=\"88.964844\"/>\n", "      <use xlink:href=\"#DejaVuSans-73\" x=\"141.064453\"/>\n", "     </g>\n", "    </g>\n", "   </g>\n", "   <g id=\"line2d_21\">\n", "    <path d=\"M 40.603125 13.5 \n", "L 89.428125 138.457517 \n", "L 138.253125 138.7844 \n", "L 187.078125 139.131181 \n", "L 235.903125 139.5 \n", "\" clip-path=\"url(#pca94a13d1e)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_3\">\n", "    <path d=\"M 40.**********.8 \n", "L 40.603125 7.2 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_4\">\n", "    <path d=\"M 235.**********.8 \n", "L 235.903125 7.2 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_5\">\n", "    <path d=\"M 40.**********.8 \n", "L 235.**********.8 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_6\">\n", "    <path d=\"M 40.603125 7.2 \n", "L 235.903125 7.2 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "  </g>\n", " </g>\n", " <defs>\n", "  <clipPath id=\"pca94a13d1e\">\n", "   <rect x=\"40.603125\" y=\"7.2\" width=\"195.3\" height=\"138.6\"/>\n", "  </clipPath>\n", " </defs>\n", "</svg>\n"], "text/plain": ["<Figure size 350x250 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["net = NWKernelRegression()\n", "loss = nn.MSELoss(reduction='none')\n", "trainer = torch.optim.SGD(net.parameters(), lr=0.5)\n", "animator = d2l.Animator(xlabel='epoch', ylabel='loss', xlim=[1, 5])\n", "\n", "for epoch in range(5):\n", "    trainer.zero_grad()\n", "    l = loss(net(x_train, keys, values), y_train)\n", "    l.sum().backward()\n", "    trainer.step()\n", "    print(f'epoch {epoch + 1}, loss {float(l.sum()):.6f}')\n", "    animator.add(epoch + 1, float(l.sum()))"]}, {"cell_type": "markdown", "id": "6a49fefb", "metadata": {"origin_pos": 56}, "source": ["如下所示，训练完带参数的注意力汇聚模型后可以发现：\n", "在尝试拟合带噪声的训练数据时，\n", "[**预测结果绘制**]的线不如之前非参数模型的平滑。\n"]}, {"cell_type": "code", "execution_count": 13, "id": "67823c8b", "metadata": {"execution": {"iopub.execute_input": "2022-12-07T17:03:18.416473Z", "iopub.status.busy": "2022-12-07T17:03:18.416179Z", "iopub.status.idle": "2022-12-07T17:03:18.597739Z", "shell.execute_reply": "2022-12-07T17:03:18.596895Z"}, "origin_pos": 58, "tab": ["pytorch"]}, "outputs": [{"data": {"image/svg+xml": ["<?xml version=\"1.0\" encoding=\"utf-8\" standalone=\"no\"?>\n", "<!DOCTYPE svg PUBLIC \"-//W3C//DTD SVG 1.1//EN\"\n", "  \"http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd\">\n", "<svg xmlns:xlink=\"http://www.w3.org/1999/xlink\" width=\"248.301563pt\" height=\"187.155469pt\" viewBox=\"0 0 248.**********.155469\" xmlns=\"http://www.w3.org/2000/svg\" version=\"1.1\">\n", " <metadata>\n", "  <rdf:RDF xmlns:dc=\"http://purl.org/dc/elements/1.1/\" xmlns:cc=\"http://creativecommons.org/ns#\" xmlns:rdf=\"http://www.w3.org/1999/02/22-rdf-syntax-ns#\">\n", "   <cc:Work>\n", "    <dc:type rdf:resource=\"http://purl.org/dc/dcmitype/StillImage\"/>\n", "    <dc:date>2022-12-07T17:03:18.547234</dc:date>\n", "    <dc:format>image/svg+xml</dc:format>\n", "    <dc:creator>\n", "     <cc:Agent>\n", "      <dc:title>Matplotlib v3.5.1, https://matplotlib.org/</dc:title>\n", "     </cc:Agent>\n", "    </dc:creator>\n", "   </cc:Work>\n", "  </rdf:RDF>\n", " </metadata>\n", " <defs>\n", "  <style type=\"text/css\">*{stroke-linejoin: round; stroke-linecap: butt}</style>\n", " </defs>\n", " <g id=\"figure_1\">\n", "  <g id=\"patch_1\">\n", "   <path d=\"M 0 187.155469 \n", "L 248.**********.155469 \n", "L 248.301563 0 \n", "L 0 0 \n", "z\n", "\" style=\"fill: #ffffff\"/>\n", "  </g>\n", "  <g id=\"axes_1\">\n", "   <g id=\"patch_2\">\n", "    <path d=\"M 42.**********.599219 \n", "L 237.**********.599219 \n", "L 237.920313 10.999219 \n", "L 42.620312 10.999219 \n", "z\n", "\" style=\"fill: #ffffff\"/>\n", "   </g>\n", "   <g id=\"matplotlib.axis_1\">\n", "    <g id=\"xtick_1\">\n", "     <g id=\"line2d_1\">\n", "      <path d=\"M 42.**********.599219 \n", "L 42.620312 10.999219 \n", "\" clip-path=\"url(#p9ca02c5e89)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_2\">\n", "      <defs>\n", "       <path id=\"m8aa7c56eed\" d=\"M 0 0 \n", "L 0 3.5 \n", "\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </defs>\n", "      <g>\n", "       <use xlink:href=\"#m8aa7c56eed\" x=\"42.620312\" y=\"149.599219\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_1\">\n", "      <!-- 0 -->\n", "      <g transform=\"translate(39.439062 164.197656)scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-30\" d=\"M 2034 4250 \n", "Q 1547 4250 1301 3770 \n", "Q 1056 3291 1056 2328 \n", "Q 1056 1369 1301 889 \n", "Q 1547 409 2034 409 \n", "Q 2525 409 2770 889 \n", "Q 3016 1369 3016 2328 \n", "Q 3016 3291 2770 3770 \n", "Q 2525 4250 2034 4250 \n", "z\n", "M 2034 4750 \n", "Q 2819 4750 3233 4129 \n", "Q 3647 3509 3647 2328 \n", "Q 3647 1150 3233 529 \n", "Q 2819 -91 2034 -91 \n", "Q 1250 -91 836 529 \n", "Q 422 1150 422 2328 \n", "Q 422 3509 836 4129 \n", "Q 1250 4750 2034 4750 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_2\">\n", "     <g id=\"line2d_3\">\n", "      <path d=\"M 81.680312 149.599219 \n", "L 81.680312 10.999219 \n", "\" clip-path=\"url(#p9ca02c5e89)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_4\">\n", "      <g>\n", "       <use xlink:href=\"#m8aa7c56eed\" x=\"81.680312\" y=\"149.599219\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_2\">\n", "      <!-- 1 -->\n", "      <g transform=\"translate(78.499062 164.197656)scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-31\" d=\"M 794 531 \n", "L 1825 531 \n", "L 1825 4091 \n", "L 703 3866 \n", "L 703 4441 \n", "L 1819 4666 \n", "L 2450 4666 \n", "L 2450 531 \n", "L 3481 531 \n", "L 3481 0 \n", "L 794 0 \n", "L 794 531 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-31\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_3\">\n", "     <g id=\"line2d_5\">\n", "      <path d=\"M 120.740313 149.599219 \n", "L 120.740313 10.999219 \n", "\" clip-path=\"url(#p9ca02c5e89)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_6\">\n", "      <g>\n", "       <use xlink:href=\"#m8aa7c56eed\" x=\"120.740313\" y=\"149.599219\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_3\">\n", "      <!-- 2 -->\n", "      <g transform=\"translate(117.559062 164.197656)scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-32\" d=\"M 1228 531 \n", "L 3431 531 \n", "L 3431 0 \n", "L 469 0 \n", "L 469 531 \n", "Q 828 903 1448 1529 \n", "Q 2069 2156 2228 2338 \n", "Q 2531 2678 2651 2914 \n", "Q 2772 3150 2772 3378 \n", "Q 2772 3750 2511 3984 \n", "Q 2250 4219 1831 4219 \n", "Q 1534 4219 1204 4116 \n", "Q 875 4013 500 3803 \n", "L 500 4441 \n", "Q 881 4594 1212 4672 \n", "Q 1544 4750 1819 4750 \n", "Q 2544 4750 2975 4387 \n", "Q 3406 4025 3406 3419 \n", "Q 3406 3131 3298 2873 \n", "Q 3191 2616 2906 2266 \n", "Q 2828 2175 2409 1742 \n", "Q 1991 1309 1228 531 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-32\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_4\">\n", "     <g id=\"line2d_7\">\n", "      <path d=\"M 159.800313 149.599219 \n", "L 159.800313 10.999219 \n", "\" clip-path=\"url(#p9ca02c5e89)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_8\">\n", "      <g>\n", "       <use xlink:href=\"#m8aa7c56eed\" x=\"159.800313\" y=\"149.599219\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_4\">\n", "      <!-- 3 -->\n", "      <g transform=\"translate(156.619063 164.197656)scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-33\" d=\"M 2597 2516 \n", "Q 3050 2419 3304 2112 \n", "Q 3559 1806 3559 1356 \n", "Q 3559 666 3084 287 \n", "Q 2609 -91 1734 -91 \n", "Q 1441 -91 1130 -33 \n", "Q 819 25 488 141 \n", "L 488 750 \n", "Q 750 597 1062 519 \n", "Q 1375 441 1716 441 \n", "Q 2309 441 2620 675 \n", "Q 2931 909 2931 1356 \n", "Q 2931 1769 2642 2001 \n", "Q 2353 2234 1838 2234 \n", "L 1294 2234 \n", "L 1294 2753 \n", "L 1863 2753 \n", "Q 2328 2753 2575 2939 \n", "Q 2822 3125 2822 3475 \n", "Q 2822 3834 2567 4026 \n", "Q 2313 4219 1838 4219 \n", "Q 1578 4219 1281 4162 \n", "Q 984 4106 628 3988 \n", "L 628 4550 \n", "Q 988 4650 1302 4700 \n", "Q 1616 4750 1894 4750 \n", "Q 2613 4750 3031 4423 \n", "Q 3450 4097 3450 3541 \n", "Q 3450 3153 3228 2886 \n", "Q 3006 2619 2597 2516 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-33\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_5\">\n", "     <g id=\"line2d_9\">\n", "      <path d=\"M 198.860313 149.599219 \n", "L 198.860313 10.999219 \n", "\" clip-path=\"url(#p9ca02c5e89)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_10\">\n", "      <g>\n", "       <use xlink:href=\"#m8aa7c56eed\" x=\"198.860313\" y=\"149.599219\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_5\">\n", "      <!-- 4 -->\n", "      <g transform=\"translate(195.679063 164.197656)scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-34\" d=\"M 2419 4116 \n", "L 825 1625 \n", "L 2419 1625 \n", "L 2419 4116 \n", "z\n", "M 2253 4666 \n", "L 3047 4666 \n", "L 3047 1625 \n", "L 3713 1625 \n", "L 3713 1100 \n", "L 3047 1100 \n", "L 3047 0 \n", "L 2419 0 \n", "L 2419 1100 \n", "L 313 1100 \n", "L 313 1709 \n", "L 2253 4666 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-34\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_6\">\n", "     <g id=\"line2d_11\">\n", "      <path d=\"M 237.**********.599219 \n", "L 237.920313 10.999219 \n", "\" clip-path=\"url(#p9ca02c5e89)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_12\">\n", "      <g>\n", "       <use xlink:href=\"#m8aa7c56eed\" x=\"237.920313\" y=\"149.599219\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_6\">\n", "      <!-- 5 -->\n", "      <g transform=\"translate(234.739063 164.197656)scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-35\" d=\"M 691 4666 \n", "L 3169 4666 \n", "L 3169 4134 \n", "L 1269 4134 \n", "L 1269 2991 \n", "Q 1406 3038 1543 3061 \n", "Q 1681 3084 1819 3084 \n", "Q 2600 3084 3056 2656 \n", "Q 3513 2228 3513 1497 \n", "Q 3513 744 3044 326 \n", "Q 2575 -91 1722 -91 \n", "Q 1428 -91 1123 -41 \n", "Q 819 9 494 109 \n", "L 494 744 \n", "Q 775 591 1075 516 \n", "Q 1375 441 1709 441 \n", "Q 2250 441 2565 725 \n", "Q 2881 1009 2881 1497 \n", "Q 2881 1984 2565 2268 \n", "Q 2250 2553 1709 2553 \n", "Q 1456 2553 1204 2497 \n", "Q 953 2441 691 2322 \n", "L 691 4666 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-35\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"text_7\">\n", "     <!-- x -->\n", "     <g transform=\"translate(137.310937 177.875781)scale(0.1 -0.1)\">\n", "      <defs>\n", "       <path id=\"DejaVuSans-78\" d=\"M 3513 3500 \n", "L 2247 1797 \n", "L 3578 0 \n", "L 2900 0 \n", "L 1881 1375 \n", "L 863 0 \n", "L 184 0 \n", "L 1544 1831 \n", "L 300 3500 \n", "L 978 3500 \n", "L 1906 2253 \n", "L 2834 3500 \n", "L 3513 3500 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "      </defs>\n", "      <use xlink:href=\"#DejaVuSans-78\"/>\n", "     </g>\n", "    </g>\n", "   </g>\n", "   <g id=\"matplotlib.axis_2\">\n", "    <g id=\"ytick_1\">\n", "     <g id=\"line2d_13\">\n", "      <path d=\"M 42.**********.599219 \n", "L 237.**********.599219 \n", "\" clip-path=\"url(#p9ca02c5e89)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_14\">\n", "      <defs>\n", "       <path id=\"m20e9ff1fe0\" d=\"M 0 0 \n", "L -3.5 0 \n", "\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </defs>\n", "      <g>\n", "       <use xlink:href=\"#m20e9ff1fe0\" x=\"42.620312\" y=\"149.599219\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_8\">\n", "      <!-- −1 -->\n", "      <g transform=\"translate(20.878125 153.398437)scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-2212\" d=\"M 678 2272 \n", "L 4684 2272 \n", "L 4684 1741 \n", "L 678 1741 \n", "L 678 2272 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-2212\"/>\n", "       <use xlink:href=\"#DejaVuSans-31\" x=\"83.789062\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_2\">\n", "     <g id=\"line2d_15\">\n", "      <path d=\"M 42.620312 126.499219 \n", "L 237.920313 126.499219 \n", "\" clip-path=\"url(#p9ca02c5e89)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_16\">\n", "      <g>\n", "       <use xlink:href=\"#m20e9ff1fe0\" x=\"42.620312\" y=\"126.499219\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_9\">\n", "      <!-- 0 -->\n", "      <g transform=\"translate(29.257812 130.298437)scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_3\">\n", "     <g id=\"line2d_17\">\n", "      <path d=\"M 42.620312 103.399219 \n", "L 237.920313 103.399219 \n", "\" clip-path=\"url(#p9ca02c5e89)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_18\">\n", "      <g>\n", "       <use xlink:href=\"#m20e9ff1fe0\" x=\"42.620312\" y=\"103.399219\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_10\">\n", "      <!-- 1 -->\n", "      <g transform=\"translate(29.257812 107.198437)scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-31\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_4\">\n", "     <g id=\"line2d_19\">\n", "      <path d=\"M 42.620312 80.299219 \n", "L 237.920313 80.299219 \n", "\" clip-path=\"url(#p9ca02c5e89)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_20\">\n", "      <g>\n", "       <use xlink:href=\"#m20e9ff1fe0\" x=\"42.620312\" y=\"80.299219\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_11\">\n", "      <!-- 2 -->\n", "      <g transform=\"translate(29.257812 84.098437)scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-32\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_5\">\n", "     <g id=\"line2d_21\">\n", "      <path d=\"M 42.620312 57.199219 \n", "L 237.920313 57.199219 \n", "\" clip-path=\"url(#p9ca02c5e89)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_22\">\n", "      <g>\n", "       <use xlink:href=\"#m20e9ff1fe0\" x=\"42.620312\" y=\"57.199219\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_12\">\n", "      <!-- 3 -->\n", "      <g transform=\"translate(29.257812 60.998437)scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-33\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_6\">\n", "     <g id=\"line2d_23\">\n", "      <path d=\"M 42.620312 34.099219 \n", "L 237.920313 34.099219 \n", "\" clip-path=\"url(#p9ca02c5e89)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_24\">\n", "      <g>\n", "       <use xlink:href=\"#m20e9ff1fe0\" x=\"42.620312\" y=\"34.099219\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_13\">\n", "      <!-- 4 -->\n", "      <g transform=\"translate(29.257812 37.898438)scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-34\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_7\">\n", "     <g id=\"line2d_25\">\n", "      <path d=\"M 42.620312 10.999219 \n", "L 237.920313 10.999219 \n", "\" clip-path=\"url(#p9ca02c5e89)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_26\">\n", "      <g>\n", "       <use xlink:href=\"#m20e9ff1fe0\" x=\"42.620312\" y=\"10.999219\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_14\">\n", "      <!-- 5 -->\n", "      <g transform=\"translate(29.257812 14.798437)scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-35\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"text_15\">\n", "     <!-- y -->\n", "     <g transform=\"translate(14.798437 83.258594)rotate(-90)scale(0.1 -0.1)\">\n", "      <defs>\n", "       <path id=\"DejaVuSans-79\" d=\"M 2059 -325 \n", "Q 1816 -950 1584 -1140 \n", "Q 1353 -1331 966 -1331 \n", "L 506 -1331 \n", "L 506 -850 \n", "L 844 -850 \n", "Q 1081 -850 1212 -737 \n", "Q 1344 -625 1503 -206 \n", "L 1606 56 \n", "L 191 3500 \n", "L 800 3500 \n", "L 1894 763 \n", "L 2988 3500 \n", "L 3597 3500 \n", "L 2059 -325 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "      </defs>\n", "      <use xlink:href=\"#DejaVuSans-79\"/>\n", "     </g>\n", "    </g>\n", "   </g>\n", "   <g id=\"line2d_27\">\n", "    <path d=\"M 42.620312 126.499219 \n", "L 46.526313 118.225812 \n", "L 50.432313 110.946345 \n", "L 54.338313 104.029425 \n", "L 58.244313 97.4097 \n", "L 62.150312 91.082293 \n", "L 66.056313 85.061866 \n", "L 69.962312 79.370737 \n", "L 73.868313 74.033949 \n", "L 77.774314 69.076775 \n", "L 81.680312 64.523261 \n", "L 85.586313 60.395214 \n", "L 89.492314 56.711598 \n", "L 93.398311 53.487961 \n", "L 97.304312 50.736132 \n", "L 101.210313 48.46391 \n", "L 105.116313 46.674891 \n", "L 109.022314 45.368338 \n", "L 112.928315 44.539111 \n", "L 116.834312 44.177706 \n", "L 120.740313 44.270242 \n", "L 124.646309 44.798618 \n", "L 128.552314 45.740654 \n", "L 132.458311 47.070245 \n", "L 136.364316 48.757689 \n", "L 140.270312 50.76986 \n", "L 144.176318 53.070611 \n", "L 148.082314 55.621064 \n", "L 151.98832 58.380042 \n", "L 155.894316 61.304393 \n", "L 159.800313 64.349483 \n", "L 163.706318 67.469619 \n", "L 167.612314 70.618454 \n", "L 171.518311 73.749494 \n", "L 175.424316 76.816549 \n", "L 179.330312 79.774142 \n", "L 183.236318 82.578044 \n", "L 187.142314 85.185605 \n", "L 191.048311 87.556281 \n", "L 194.954316 89.651973 \n", "L 198.860313 91.437389 \n", "L 202.766309 92.880457 \n", "L 206.672305 93.952601 \n", "L 210.57832 94.629017 \n", "L 214.484316 94.888934 \n", "L 218.390312 94.715829 \n", "L 222.296309 94.097555 \n", "L 226.202305 93.026506 \n", "L 230.10832 91.499643 \n", "L 234.014316 89.518571 \n", "\" clip-path=\"url(#p9ca02c5e89)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_28\">\n", "    <path d=\"M 42.620312 117.679869 \n", "L 46.526313 117.090358 \n", "L 50.432313 118.164111 \n", "L 54.338313 106.870888 \n", "L 58.244313 101.769046 \n", "L 62.150312 102.938271 \n", "L 66.056313 92.491054 \n", "L 69.962312 84.750108 \n", "L 73.868313 83.115067 \n", "L 77.774314 79.493817 \n", "L 81.680312 70.616636 \n", "L 85.586313 67.877172 \n", "L 89.492314 64.3908 \n", "L 93.398311 47.2555 \n", "L 97.304312 47.084394 \n", "L 101.210313 60.653619 \n", "L 105.116313 63.659535 \n", "L 109.022314 44.837605 \n", "L 112.928315 23.989686 \n", "L 116.834312 50.431046 \n", "L 120.740313 51.131067 \n", "L 124.646309 35.093047 \n", "L 128.552314 57.770652 \n", "L 132.458311 58.864749 \n", "L 136.364316 47.774584 \n", "L 140.270312 45.762326 \n", "L 144.176318 45.787396 \n", "L 148.082314 47.694941 \n", "L 151.98832 74.334966 \n", "L 155.894316 74.335032 \n", "L 159.800313 74.389214 \n", "L 163.706318 76.361075 \n", "L 167.612314 67.869643 \n", "L 171.518311 40.389531 \n", "L 175.424316 77.423582 \n", "L 179.330312 71.077865 \n", "L 183.236318 77.249171 \n", "L 187.142314 79.803822 \n", "L 191.048311 86.43058 \n", "L 194.954316 89.385816 \n", "L 198.860313 96.284772 \n", "L 202.766309 103.066196 \n", "L 206.672305 104.080078 \n", "L 210.57832 87.967075 \n", "L 214.484316 99.0151 \n", "L 218.390312 96.649958 \n", "L 222.296309 93.810635 \n", "L 226.202305 97.970008 \n", "L 230.10832 116.512385 \n", "L 234.014316 117.193022 \n", "\" clip-path=\"url(#p9ca02c5e89)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #bf00bf; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_29\">\n", "    <defs>\n", "     <path id=\"md5227dd831\" d=\"M 0 3 \n", "C 0.795609 3 1.55874 2.683901 2.12132 2.12132 \n", "C 2.683901 1.55874 3 0.795609 3 0 \n", "C 3 -0.795609 2.683901 -1.55874 2.12132 -2.12132 \n", "C 1.55874 -2.683901 0.795609 -3 0 -3 \n", "C -0.795609 -3 -1.55874 -2.683901 -2.12132 -2.12132 \n", "C -2.683901 -1.55874 -3 -0.795609 -3 0 \n", "C -3 0.795609 -2.683901 1.55874 -2.12132 2.12132 \n", "C -1.55874 2.683901 -0.795609 3 0 3 \n", "z\n", "\" style=\"stroke: #ff7f0e; stroke-opacity: 0.5\"/>\n", "    </defs>\n", "    <g clip-path=\"url(#p9ca02c5e89)\">\n", "     <use xlink:href=\"#md5227dd831\" x=\"47.284101\" y=\"122.258096\" style=\"fill: #ff7f0e; fill-opacity: 0.5; stroke: #ff7f0e; stroke-opacity: 0.5\"/>\n", "     <use xlink:href=\"#md5227dd831\" x=\"47.606516\" y=\"111.114186\" style=\"fill: #ff7f0e; fill-opacity: 0.5; stroke: #ff7f0e; stroke-opacity: 0.5\"/>\n", "     <use xlink:href=\"#md5227dd831\" x=\"51.726435\" y=\"122.404574\" style=\"fill: #ff7f0e; fill-opacity: 0.5; stroke: #ff7f0e; stroke-opacity: 0.5\"/>\n", "     <use xlink:href=\"#md5227dd831\" x=\"52.335236\" y=\"128.59023\" style=\"fill: #ff7f0e; fill-opacity: 0.5; stroke: #ff7f0e; stroke-opacity: 0.5\"/>\n", "     <use xlink:href=\"#md5227dd831\" x=\"53.492552\" y=\"95.60659\" style=\"fill: #ff7f0e; fill-opacity: 0.5; stroke: #ff7f0e; stroke-opacity: 0.5\"/>\n", "     <use xlink:href=\"#md5227dd831\" x=\"55.498434\" y=\"94.84244\" style=\"fill: #ff7f0e; fill-opacity: 0.5; stroke: #ff7f0e; stroke-opacity: 0.5\"/>\n", "     <use xlink:href=\"#md5227dd831\" x=\"59.18996\" y=\"104.793079\" style=\"fill: #ff7f0e; fill-opacity: 0.5; stroke: #ff7f0e; stroke-opacity: 0.5\"/>\n", "     <use xlink:href=\"#md5227dd831\" x=\"66.404594\" y=\"98.825684\" style=\"fill: #ff7f0e; fill-opacity: 0.5; stroke: #ff7f0e; stroke-opacity: 0.5\"/>\n", "     <use xlink:href=\"#md5227dd831\" x=\"69.1377\" y=\"75.296504\" style=\"fill: #ff7f0e; fill-opacity: 0.5; stroke: #ff7f0e; stroke-opacity: 0.5\"/>\n", "     <use xlink:href=\"#md5227dd831\" x=\"69.734616\" y=\"89.851566\" style=\"fill: #ff7f0e; fill-opacity: 0.5; stroke: #ff7f0e; stroke-opacity: 0.5\"/>\n", "     <use xlink:href=\"#md5227dd831\" x=\"74.429683\" y=\"91.85764\" style=\"fill: #ff7f0e; fill-opacity: 0.5; stroke: #ff7f0e; stroke-opacity: 0.5\"/>\n", "     <use xlink:href=\"#md5227dd831\" x=\"75.914442\" y=\"63.140743\" style=\"fill: #ff7f0e; fill-opacity: 0.5; stroke: #ff7f0e; stroke-opacity: 0.5\"/>\n", "     <use xlink:href=\"#md5227dd831\" x=\"76.186604\" y=\"89.655134\" style=\"fill: #ff7f0e; fill-opacity: 0.5; stroke: #ff7f0e; stroke-opacity: 0.5\"/>\n", "     <use xlink:href=\"#md5227dd831\" x=\"85.198643\" y=\"75.578684\" style=\"fill: #ff7f0e; fill-opacity: 0.5; stroke: #ff7f0e; stroke-opacity: 0.5\"/>\n", "     <use xlink:href=\"#md5227dd831\" x=\"85.478934\" y=\"59.595447\" style=\"fill: #ff7f0e; fill-opacity: 0.5; stroke: #ff7f0e; stroke-opacity: 0.5\"/>\n", "     <use xlink:href=\"#md5227dd831\" x=\"85.7427\" y=\"81.314411\" style=\"fill: #ff7f0e; fill-opacity: 0.5; stroke: #ff7f0e; stroke-opacity: 0.5\"/>\n", "     <use xlink:href=\"#md5227dd831\" x=\"86.56718\" y=\"52.295319\" style=\"fill: #ff7f0e; fill-opacity: 0.5; stroke: #ff7f0e; stroke-opacity: 0.5\"/>\n", "     <use xlink:href=\"#md5227dd831\" x=\"87.824273\" y=\"70.143308\" style=\"fill: #ff7f0e; fill-opacity: 0.5; stroke: #ff7f0e; stroke-opacity: 0.5\"/>\n", "     <use xlink:href=\"#md5227dd831\" x=\"93.210894\" y=\"46.77532\" style=\"fill: #ff7f0e; fill-opacity: 0.5; stroke: #ff7f0e; stroke-opacity: 0.5\"/>\n", "     <use xlink:href=\"#md5227dd831\" x=\"97.124797\" y=\"46.058528\" style=\"fill: #ff7f0e; fill-opacity: 0.5; stroke: #ff7f0e; stroke-opacity: 0.5\"/>\n", "     <use xlink:href=\"#md5227dd831\" x=\"102.139028\" y=\"62.917481\" style=\"fill: #ff7f0e; fill-opacity: 0.5; stroke: #ff7f0e; stroke-opacity: 0.5\"/>\n", "     <use xlink:href=\"#md5227dd831\" x=\"107.054211\" y=\"65.014439\" style=\"fill: #ff7f0e; fill-opacity: 0.5; stroke: #ff7f0e; stroke-opacity: 0.5\"/>\n", "     <use xlink:href=\"#md5227dd831\" x=\"111.162003\" y=\"22.821134\" style=\"fill: #ff7f0e; fill-opacity: 0.5; stroke: #ff7f0e; stroke-opacity: 0.5\"/>\n", "     <use xlink:href=\"#md5227dd831\" x=\"120.344163\" y=\"53.246916\" style=\"fill: #ff7f0e; fill-opacity: 0.5; stroke: #ff7f0e; stroke-opacity: 0.5\"/>\n", "     <use xlink:href=\"#md5227dd831\" x=\"125.125425\" y=\"32.003153\" style=\"fill: #ff7f0e; fill-opacity: 0.5; stroke: #ff7f0e; stroke-opacity: 0.5\"/>\n", "     <use xlink:href=\"#md5227dd831\" x=\"130.222534\" y=\"65.511554\" style=\"fill: #ff7f0e; fill-opacity: 0.5; stroke: #ff7f0e; stroke-opacity: 0.5\"/>\n", "     <use xlink:href=\"#md5227dd831\" x=\"132.464699\" y=\"65.626093\" style=\"fill: #ff7f0e; fill-opacity: 0.5; stroke: #ff7f0e; stroke-opacity: 0.5\"/>\n", "     <use xlink:href=\"#md5227dd831\" x=\"134.328873\" y=\"43.26986\" style=\"fill: #ff7f0e; fill-opacity: 0.5; stroke: #ff7f0e; stroke-opacity: 0.5\"/>\n", "     <use xlink:href=\"#md5227dd831\" x=\"139.770792\" y=\"45.787699\" style=\"fill: #ff7f0e; fill-opacity: 0.5; stroke: #ff7f0e; stroke-opacity: 0.5\"/>\n", "     <use xlink:href=\"#md5227dd831\" x=\"157.669862\" y=\"74.334999\" style=\"fill: #ff7f0e; fill-opacity: 0.5; stroke: #ff7f0e; stroke-opacity: 0.5\"/>\n", "     <use xlink:href=\"#md5227dd831\" x=\"165.812714\" y=\"76.473885\" style=\"fill: #ff7f0e; fill-opacity: 0.5; stroke: #ff7f0e; stroke-opacity: 0.5\"/>\n", "     <use xlink:href=\"#md5227dd831\" x=\"171.519447\" y=\"33.341799\" style=\"fill: #ff7f0e; fill-opacity: 0.5; stroke: #ff7f0e; stroke-opacity: 0.5\"/>\n", "     <use xlink:href=\"#md5227dd831\" x=\"175.979852\" y=\"84.3256\" style=\"fill: #ff7f0e; fill-opacity: 0.5; stroke: #ff7f0e; stroke-opacity: 0.5\"/>\n", "     <use xlink:href=\"#md5227dd831\" x=\"176.817448\" y=\"84.123718\" style=\"fill: #ff7f0e; fill-opacity: 0.5; stroke: #ff7f0e; stroke-opacity: 0.5\"/>\n", "     <use xlink:href=\"#md5227dd831\" x=\"178.625784\" y=\"66.945137\" style=\"fill: #ff7f0e; fill-opacity: 0.5; stroke: #ff7f0e; stroke-opacity: 0.5\"/>\n", "     <use xlink:href=\"#md5227dd831\" x=\"180.472665\" y=\"76.834062\" style=\"fill: #ff7f0e; fill-opacity: 0.5; stroke: #ff7f0e; stroke-opacity: 0.5\"/>\n", "     <use xlink:href=\"#md5227dd831\" x=\"180.502726\" y=\"53.798665\" style=\"fill: #ff7f0e; fill-opacity: 0.5; stroke: #ff7f0e; stroke-opacity: 0.5\"/>\n", "     <use xlink:href=\"#md5227dd831\" x=\"182.810368\" y=\"81.660426\" style=\"fill: #ff7f0e; fill-opacity: 0.5; stroke: #ff7f0e; stroke-opacity: 0.5\"/>\n", "     <use xlink:href=\"#md5227dd831\" x=\"183.897356\" y=\"84.336309\" style=\"fill: #ff7f0e; fill-opacity: 0.5; stroke: #ff7f0e; stroke-opacity: 0.5\"/>\n", "     <use xlink:href=\"#md5227dd831\" x=\"187.506662\" y=\"75.489507\" style=\"fill: #ff7f0e; fill-opacity: 0.5; stroke: #ff7f0e; stroke-opacity: 0.5\"/>\n", "     <use xlink:href=\"#md5227dd831\" x=\"190.340914\" y=\"89.163053\" style=\"fill: #ff7f0e; fill-opacity: 0.5; stroke: #ff7f0e; stroke-opacity: 0.5\"/>\n", "     <use xlink:href=\"#md5227dd831\" x=\"201.541754\" y=\"95.648612\" style=\"fill: #ff7f0e; fill-opacity: 0.5; stroke: #ff7f0e; stroke-opacity: 0.5\"/>\n", "     <use xlink:href=\"#md5227dd831\" x=\"204.830174\" y=\"113.366038\" style=\"fill: #ff7f0e; fill-opacity: 0.5; stroke: #ff7f0e; stroke-opacity: 0.5\"/>\n", "     <use xlink:href=\"#md5227dd831\" x=\"209.726214\" y=\"86.845308\" style=\"fill: #ff7f0e; fill-opacity: 0.5; stroke: #ff7f0e; stroke-opacity: 0.5\"/>\n", "     <use xlink:href=\"#md5227dd831\" x=\"216.282585\" y=\"103.495354\" style=\"fill: #ff7f0e; fill-opacity: 0.5; stroke: #ff7f0e; stroke-opacity: 0.5\"/>\n", "     <use xlink:href=\"#md5227dd831\" x=\"217.032047\" y=\"100.890533\" style=\"fill: #ff7f0e; fill-opacity: 0.5; stroke: #ff7f0e; stroke-opacity: 0.5\"/>\n", "     <use xlink:href=\"#md5227dd831\" x=\"217.69695\" y=\"88.878071\" style=\"fill: #ff7f0e; fill-opacity: 0.5; stroke: #ff7f0e; stroke-opacity: 0.5\"/>\n", "     <use xlink:href=\"#md5227dd831\" x=\"224.089046\" y=\"73.596342\" style=\"fill: #ff7f0e; fill-opacity: 0.5; stroke: #ff7f0e; stroke-opacity: 0.5\"/>\n", "     <use xlink:href=\"#md5227dd831\" x=\"224.203405\" y=\"114.981765\" style=\"fill: #ff7f0e; fill-opacity: 0.5; stroke: #ff7f0e; stroke-opacity: 0.5\"/>\n", "     <use xlink:href=\"#md5227dd831\" x=\"229.940283\" y=\"117.196654\" style=\"fill: #ff7f0e; fill-opacity: 0.5; stroke: #ff7f0e; stroke-opacity: 0.5\"/>\n", "    </g>\n", "   </g>\n", "   <g id=\"patch_3\">\n", "    <path d=\"M 42.**********.599219 \n", "L 42.620312 10.999219 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_4\">\n", "    <path d=\"M 237.**********.599219 \n", "L 237.920313 10.999219 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_5\">\n", "    <path d=\"M 42.**********.599219 \n", "L 237.**********.599219 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_6\">\n", "    <path d=\"M 42.620312 10.999219 \n", "L 237.920313 10.999219 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"legend_1\">\n", "    <g id=\"patch_7\">\n", "     <path d=\"M 111.600781 144.599219 \n", "L 168.939844 144.599219 \n", "Q 170.939844 144.599219 170.939844 142.599219 \n", "L 170.939844 114.242969 \n", "Q 170.939844 112.242969 168.939844 112.242969 \n", "L 111.600781 112.242969 \n", "Q 109.600781 112.242969 109.600781 114.242969 \n", "L 109.600781 142.599219 \n", "Q 109.600781 144.599219 111.600781 144.599219 \n", "z\n", "\" style=\"fill: #ffffff; opacity: 0.8; stroke: #cccccc; stroke-linejoin: miter\"/>\n", "    </g>\n", "    <g id=\"line2d_30\">\n", "     <path d=\"M 113.600781 120.341406 \n", "L 123.600781 120.341406 \n", "L 133.600781 120.341406 \n", "\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "    </g>\n", "    <g id=\"text_16\">\n", "     <!-- Truth -->\n", "     <g transform=\"translate(141.600781 123.841406)scale(0.1 -0.1)\">\n", "      <defs>\n", "       <path id=\"DejaVuSans-54\" d=\"M -19 4666 \n", "L 3928 4666 \n", "L 3928 4134 \n", "L 2272 4134 \n", "L 2272 0 \n", "L 1638 0 \n", "L 1638 4134 \n", "L -19 4134 \n", "L -19 4666 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-72\" d=\"M 2631 2963 \n", "Q 2534 3019 2420 3045 \n", "Q 2306 3072 2169 3072 \n", "Q 1681 3072 1420 2755 \n", "Q 1159 2438 1159 1844 \n", "L 1159 0 \n", "L 581 0 \n", "L 581 3500 \n", "L 1159 3500 \n", "L 1159 2956 \n", "Q 1341 3275 1631 3429 \n", "Q 1922 3584 2338 3584 \n", "Q 2397 3584 2469 3576 \n", "Q 2541 3569 2628 3553 \n", "L 2631 2963 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-75\" d=\"M 544 1381 \n", "L 544 3500 \n", "L 1119 3500 \n", "L 1119 1403 \n", "Q 1119 906 1312 657 \n", "Q 1506 409 1894 409 \n", "Q 2359 409 2629 706 \n", "Q 2900 1003 2900 1516 \n", "L 2900 3500 \n", "L 3475 3500 \n", "L 3475 0 \n", "L 2900 0 \n", "L 2900 538 \n", "Q 2691 219 2414 64 \n", "Q 2138 -91 1772 -91 \n", "Q 1169 -91 856 284 \n", "Q 544 659 544 1381 \n", "z\n", "M 1991 3584 \n", "L 1991 3584 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-74\" d=\"M 1172 4494 \n", "L 1172 3500 \n", "L 2356 3500 \n", "L 2356 3053 \n", "L 1172 3053 \n", "L 1172 1153 \n", "Q 1172 725 1289 603 \n", "Q 1406 481 1766 481 \n", "L 2356 481 \n", "L 2356 0 \n", "L 1766 0 \n", "Q 1100 0 847 248 \n", "Q 594 497 594 1153 \n", "L 594 3053 \n", "L 172 3053 \n", "L 172 3500 \n", "L 594 3500 \n", "L 594 4494 \n", "L 1172 4494 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-68\" d=\"M 3513 2113 \n", "L 3513 0 \n", "L 2938 0 \n", "L 2938 2094 \n", "Q 2938 2591 2744 2837 \n", "Q 2550 3084 2163 3084 \n", "Q 1697 3084 1428 2787 \n", "Q 1159 2491 1159 1978 \n", "L 1159 0 \n", "L 581 0 \n", "L 581 4863 \n", "L 1159 4863 \n", "L 1159 2956 \n", "Q 1366 3272 1645 3428 \n", "Q 1925 3584 2291 3584 \n", "Q 2894 3584 3203 3211 \n", "Q 3513 2838 3513 2113 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "      </defs>\n", "      <use xlink:href=\"#DejaVuSans-54\"/>\n", "      <use xlink:href=\"#DejaVuSans-72\" x=\"46.333984\"/>\n", "      <use xlink:href=\"#DejaVuSans-75\" x=\"87.447266\"/>\n", "      <use xlink:href=\"#DejaVuSans-74\" x=\"150.826172\"/>\n", "      <use xlink:href=\"#DejaVuSans-68\" x=\"190.035156\"/>\n", "     </g>\n", "    </g>\n", "    <g id=\"line2d_31\">\n", "     <path d=\"M 113.600781 135.019531 \n", "L 123.600781 135.019531 \n", "L 133.600781 135.019531 \n", "\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #bf00bf; stroke-width: 1.5\"/>\n", "    </g>\n", "    <g id=\"text_17\">\n", "     <!-- Pred -->\n", "     <g transform=\"translate(141.600781 138.519531)scale(0.1 -0.1)\">\n", "      <defs>\n", "       <path id=\"DejaVuSans-50\" d=\"M 1259 4147 \n", "L 1259 2394 \n", "L 2053 2394 \n", "Q 2494 2394 2734 2622 \n", "Q 2975 2850 2975 3272 \n", "Q 2975 3691 2734 3919 \n", "Q 2494 4147 2053 4147 \n", "L 1259 4147 \n", "z\n", "M 628 4666 \n", "L 2053 4666 \n", "Q 2838 4666 3239 4311 \n", "Q 3641 3956 3641 3272 \n", "Q 3641 2581 3239 2228 \n", "Q 2838 1875 2053 1875 \n", "L 1259 1875 \n", "L 1259 0 \n", "L 628 0 \n", "L 628 4666 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-65\" d=\"M 3597 1894 \n", "L 3597 1613 \n", "L 953 1613 \n", "Q 991 1019 1311 708 \n", "Q 1631 397 2203 397 \n", "Q 2534 397 2845 478 \n", "Q 3156 559 3463 722 \n", "L 3463 178 \n", "Q 3153 47 2828 -22 \n", "Q 2503 -91 2169 -91 \n", "Q 1331 -91 842 396 \n", "Q 353 884 353 1716 \n", "Q 353 2575 817 3079 \n", "Q 1281 3584 2069 3584 \n", "Q 2775 3584 3186 3129 \n", "Q 3597 2675 3597 1894 \n", "z\n", "M 3022 2063 \n", "Q 3016 2534 2758 2815 \n", "Q 2500 3097 2075 3097 \n", "Q 1594 3097 1305 2825 \n", "Q 1016 2553 972 2059 \n", "L 3022 2063 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-64\" d=\"M 2906 2969 \n", "L 2906 4863 \n", "L 3481 4863 \n", "L 3481 0 \n", "L 2906 0 \n", "L 2906 525 \n", "Q 2725 213 2448 61 \n", "Q 2172 -91 1784 -91 \n", "Q 1150 -91 751 415 \n", "Q 353 922 353 1747 \n", "Q 353 2572 751 3078 \n", "Q 1150 3584 1784 3584 \n", "Q 2172 3584 2448 3432 \n", "Q 2725 3281 2906 2969 \n", "z\n", "M 947 1747 \n", "Q 947 1113 1208 752 \n", "Q 1469 391 1925 391 \n", "Q 2381 391 2643 752 \n", "Q 2906 1113 2906 1747 \n", "Q 2906 2381 2643 2742 \n", "Q 2381 3103 1925 3103 \n", "Q 1469 3103 1208 2742 \n", "Q 947 2381 947 1747 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "      </defs>\n", "      <use xlink:href=\"#DejaVuSans-50\"/>\n", "      <use xlink:href=\"#DejaVuSans-72\" x=\"58.552734\"/>\n", "      <use xlink:href=\"#DejaVuSans-65\" x=\"97.416016\"/>\n", "      <use xlink:href=\"#DejaVuSans-64\" x=\"158.939453\"/>\n", "     </g>\n", "    </g>\n", "   </g>\n", "  </g>\n", " </g>\n", " <defs>\n", "  <clipPath id=\"p9ca02c5e89\">\n", "   <rect x=\"42.620312\" y=\"10.999219\" width=\"195.3\" height=\"138.6\"/>\n", "  </clipPath>\n", " </defs>\n", "</svg>\n"], "text/plain": ["<Figure size 350x250 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["# keys的形状:(n_test，n_train)，每一行包含着相同的训练输入（例如，相同的键）\n", "keys = x_train.repeat((n_test, 1))\n", "# value的形状:(n_test，n_train)\n", "values = y_train.repeat((n_test, 1))\n", "y_hat = net(x_test, keys, values).unsqueeze(1).detach()\n", "plot_kernel_reg(y_hat)"]}, {"cell_type": "markdown", "id": "ad919e07", "metadata": {"origin_pos": 61}, "source": ["为什么新的模型更不平滑了呢？\n", "下面看一下输出结果的绘制图：\n", "与非参数的注意力汇聚模型相比，\n", "带参数的模型加入可学习的参数后，\n", "[**曲线在注意力权重较大的区域变得更不平滑**]。\n"]}, {"cell_type": "code", "execution_count": 14, "id": "f888eb56", "metadata": {"execution": {"iopub.execute_input": "2022-12-07T17:03:18.601446Z", "iopub.status.busy": "2022-12-07T17:03:18.600867Z", "iopub.status.idle": "2022-12-07T17:03:18.772291Z", "shell.execute_reply": "2022-12-07T17:03:18.771449Z"}, "origin_pos": 63, "tab": ["pytorch"]}, "outputs": [{"data": {"image/svg+xml": ["<?xml version=\"1.0\" encoding=\"utf-8\" standalone=\"no\"?>\n", "<!DOCTYPE svg PUBLIC \"-//W3C//DTD SVG 1.1//EN\"\n", "  \"http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd\">\n", "<svg xmlns:xlink=\"http://www.w3.org/1999/xlink\" width=\"199.80175pt\" height=\"159.039469pt\" viewBox=\"0 0 199.80175 159.039469\" xmlns=\"http://www.w3.org/2000/svg\" version=\"1.1\">\n", " <metadata>\n", "  <rdf:RDF xmlns:dc=\"http://purl.org/dc/elements/1.1/\" xmlns:cc=\"http://creativecommons.org/ns#\" xmlns:rdf=\"http://www.w3.org/1999/02/22-rdf-syntax-ns#\">\n", "   <cc:Work>\n", "    <dc:type rdf:resource=\"http://purl.org/dc/dcmitype/StillImage\"/>\n", "    <dc:date>2022-12-07T17:03:18.724885</dc:date>\n", "    <dc:format>image/svg+xml</dc:format>\n", "    <dc:creator>\n", "     <cc:Agent>\n", "      <dc:title>Matplotlib v3.5.1, https://matplotlib.org/</dc:title>\n", "     </cc:Agent>\n", "    </dc:creator>\n", "   </cc:Work>\n", "  </rdf:RDF>\n", " </metadata>\n", " <defs>\n", "  <style type=\"text/css\">*{stroke-linejoin: round; stroke-linecap: butt}</style>\n", " </defs>\n", " <g id=\"figure_1\">\n", "  <g id=\"patch_1\">\n", "   <path d=\"M 0 159.039469 \n", "L 199.80175 159.039469 \n", "L 199.80175 0 \n", "L 0 0 \n", "z\n", "\" style=\"fill: #ffffff\"/>\n", "  </g>\n", "  <g id=\"axes_1\">\n", "   <g id=\"patch_2\">\n", "    <path d=\"M 40.**********.483219 \n", "L 152.**********.483219 \n", "L 152.203125 9.883219 \n", "L 40.603125 9.883219 \n", "z\n", "\" style=\"fill: #ffffff\"/>\n", "   </g>\n", "   <g clip-path=\"url(#p1919480c70)\">\n", "    <image xlink:href=\"data:image/png;base64,\n", "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\" id=\"image90d3dc3ed4\" transform=\"scale(1 -1)translate(0 -111.6)\" x=\"40.603125\" y=\"-9.883219\" width=\"111.6\" height=\"111.6\"/>\n", "   </g>\n", "   <g id=\"matplotlib.axis_1\">\n", "    <g id=\"xtick_1\">\n", "     <g id=\"line2d_1\">\n", "      <defs>\n", "       <path id=\"m16d1bd91d2\" d=\"M 0 0 \n", "L 0 3.5 \n", "\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </defs>\n", "      <g>\n", "       <use xlink:href=\"#m16d1bd91d2\" x=\"41.719125\" y=\"121.483219\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_1\">\n", "      <!-- 0 -->\n", "      <g transform=\"translate(38.537875 136.081656)scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-30\" d=\"M 2034 4250 \n", "Q 1547 4250 1301 3770 \n", "Q 1056 3291 1056 2328 \n", "Q 1056 1369 1301 889 \n", "Q 1547 409 2034 409 \n", "Q 2525 409 2770 889 \n", "Q 3016 1369 3016 2328 \n", "Q 3016 3291 2770 3770 \n", "Q 2525 4250 2034 4250 \n", "z\n", "M 2034 4750 \n", "Q 2819 4750 3233 4129 \n", "Q 3647 3509 3647 2328 \n", "Q 3647 1150 3233 529 \n", "Q 2819 -91 2034 -91 \n", "Q 1250 -91 836 529 \n", "Q 422 1150 422 2328 \n", "Q 422 3509 836 4129 \n", "Q 1250 4750 2034 4750 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_2\">\n", "     <g id=\"line2d_2\">\n", "      <g>\n", "       <use xlink:href=\"#m16d1bd91d2\" x=\"86.359125\" y=\"121.483219\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_2\">\n", "      <!-- 20 -->\n", "      <g transform=\"translate(79.996625 136.081656)scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-32\" d=\"M 1228 531 \n", "L 3431 531 \n", "L 3431 0 \n", "L 469 0 \n", "L 469 531 \n", "Q 828 903 1448 1529 \n", "Q 2069 2156 2228 2338 \n", "Q 2531 2678 2651 2914 \n", "Q 2772 3150 2772 3378 \n", "Q 2772 3750 2511 3984 \n", "Q 2250 4219 1831 4219 \n", "Q 1534 4219 1204 4116 \n", "Q 875 4013 500 3803 \n", "L 500 4441 \n", "Q 881 4594 1212 4672 \n", "Q 1544 4750 1819 4750 \n", "Q 2544 4750 2975 4387 \n", "Q 3406 4025 3406 3419 \n", "Q 3406 3131 3298 2873 \n", "Q 3191 2616 2906 2266 \n", "Q 2828 2175 2409 1742 \n", "Q 1991 1309 1228 531 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-32\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"63.623047\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_3\">\n", "     <g id=\"line2d_3\">\n", "      <g>\n", "       <use xlink:href=\"#m16d1bd91d2\" x=\"130.999125\" y=\"121.483219\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_3\">\n", "      <!-- 40 -->\n", "      <g transform=\"translate(124.636625 136.081656)scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-34\" d=\"M 2419 4116 \n", "L 825 1625 \n", "L 2419 1625 \n", "L 2419 4116 \n", "z\n", "M 2253 4666 \n", "L 3047 4666 \n", "L 3047 1625 \n", "L 3713 1625 \n", "L 3713 1100 \n", "L 3047 1100 \n", "L 3047 0 \n", "L 2419 0 \n", "L 2419 1100 \n", "L 313 1100 \n", "L 313 1709 \n", "L 2253 4666 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-34\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"63.623047\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"text_4\">\n", "     <!-- Sorted training inputs -->\n", "     <g transform=\"translate(41.889844 149.759781)scale(0.1 -0.1)\">\n", "      <defs>\n", "       <path id=\"DejaVuSans-53\" d=\"M 3425 4513 \n", "L 3425 3897 \n", "Q 3066 4069 2747 4153 \n", "Q 2428 4238 2131 4238 \n", "Q 1616 4238 1336 4038 \n", "Q 1056 3838 1056 3469 \n", "Q 1056 3159 1242 3001 \n", "Q 1428 2844 1947 2747 \n", "L 2328 2669 \n", "Q 3034 2534 3370 2195 \n", "Q 3706 1856 3706 1288 \n", "Q 3706 609 3251 259 \n", "Q 2797 -91 1919 -91 \n", "Q 1588 -91 1214 -16 \n", "Q 841 59 441 206 \n", "L 441 856 \n", "Q 825 641 1194 531 \n", "Q 1563 422 1919 422 \n", "Q 2459 422 2753 634 \n", "Q 3047 847 3047 1241 \n", "Q 3047 1584 2836 1778 \n", "Q 2625 1972 2144 2069 \n", "L 1759 2144 \n", "Q 1053 2284 737 2584 \n", "Q 422 2884 422 3419 \n", "Q 422 4038 858 4394 \n", "Q 1294 4750 2059 4750 \n", "Q 2388 4750 2728 4690 \n", "Q 3069 4631 3425 4513 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-6f\" d=\"M 1959 3097 \n", "Q 1497 3097 1228 2736 \n", "Q 959 2375 959 1747 \n", "Q 959 1119 1226 758 \n", "Q 1494 397 1959 397 \n", "Q 2419 397 2687 759 \n", "Q 2956 1122 2956 1747 \n", "Q 2956 2369 2687 2733 \n", "Q 2419 3097 1959 3097 \n", "z\n", "M 1959 3584 \n", "Q 2709 3584 3137 3096 \n", "Q 3566 2609 3566 1747 \n", "Q 3566 888 3137 398 \n", "Q 2709 -91 1959 -91 \n", "Q 1206 -91 779 398 \n", "Q 353 888 353 1747 \n", "Q 353 2609 779 3096 \n", "Q 1206 3584 1959 3584 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-72\" d=\"M 2631 2963 \n", "Q 2534 3019 2420 3045 \n", "Q 2306 3072 2169 3072 \n", "Q 1681 3072 1420 2755 \n", "Q 1159 2438 1159 1844 \n", "L 1159 0 \n", "L 581 0 \n", "L 581 3500 \n", "L 1159 3500 \n", "L 1159 2956 \n", "Q 1341 3275 1631 3429 \n", "Q 1922 3584 2338 3584 \n", "Q 2397 3584 2469 3576 \n", "Q 2541 3569 2628 3553 \n", "L 2631 2963 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-74\" d=\"M 1172 4494 \n", "L 1172 3500 \n", "L 2356 3500 \n", "L 2356 3053 \n", "L 1172 3053 \n", "L 1172 1153 \n", "Q 1172 725 1289 603 \n", "Q 1406 481 1766 481 \n", "L 2356 481 \n", "L 2356 0 \n", "L 1766 0 \n", "Q 1100 0 847 248 \n", "Q 594 497 594 1153 \n", "L 594 3053 \n", "L 172 3053 \n", "L 172 3500 \n", "L 594 3500 \n", "L 594 4494 \n", "L 1172 4494 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-65\" d=\"M 3597 1894 \n", "L 3597 1613 \n", "L 953 1613 \n", "Q 991 1019 1311 708 \n", "Q 1631 397 2203 397 \n", "Q 2534 397 2845 478 \n", "Q 3156 559 3463 722 \n", "L 3463 178 \n", "Q 3153 47 2828 -22 \n", "Q 2503 -91 2169 -91 \n", "Q 1331 -91 842 396 \n", "Q 353 884 353 1716 \n", "Q 353 2575 817 3079 \n", "Q 1281 3584 2069 3584 \n", "Q 2775 3584 3186 3129 \n", "Q 3597 2675 3597 1894 \n", "z\n", "M 3022 2063 \n", "Q 3016 2534 2758 2815 \n", "Q 2500 3097 2075 3097 \n", "Q 1594 3097 1305 2825 \n", "Q 1016 2553 972 2059 \n", "L 3022 2063 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-64\" d=\"M 2906 2969 \n", "L 2906 4863 \n", "L 3481 4863 \n", "L 3481 0 \n", "L 2906 0 \n", "L 2906 525 \n", "Q 2725 213 2448 61 \n", "Q 2172 -91 1784 -91 \n", "Q 1150 -91 751 415 \n", "Q 353 922 353 1747 \n", "Q 353 2572 751 3078 \n", "Q 1150 3584 1784 3584 \n", "Q 2172 3584 2448 3432 \n", "Q 2725 3281 2906 2969 \n", "z\n", "M 947 1747 \n", "Q 947 1113 1208 752 \n", "Q 1469 391 1925 391 \n", "Q 2381 391 2643 752 \n", "Q 2906 1113 2906 1747 \n", "Q 2906 2381 2643 2742 \n", "Q 2381 3103 1925 3103 \n", "Q 1469 3103 1208 2742 \n", "Q 947 2381 947 1747 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-20\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-61\" d=\"M 2194 1759 \n", "Q 1497 1759 1228 1600 \n", "Q 959 1441 959 1056 \n", "Q 959 750 1161 570 \n", "Q 1363 391 1709 391 \n", "Q 2188 391 2477 730 \n", "Q 2766 1069 2766 1631 \n", "L 2766 1759 \n", "L 2194 1759 \n", "z\n", "M 3341 1997 \n", "L 3341 0 \n", "L 2766 0 \n", "L 2766 531 \n", "Q 2569 213 2275 61 \n", "Q 1981 -91 1556 -91 \n", "Q 1019 -91 701 211 \n", "Q 384 513 384 1019 \n", "Q 384 1609 779 1909 \n", "Q 1175 2209 1959 2209 \n", "L 2766 2209 \n", "L 2766 2266 \n", "Q 2766 2663 2505 2880 \n", "Q 2244 3097 1772 3097 \n", "Q 1472 3097 1187 3025 \n", "Q 903 2953 641 2809 \n", "L 641 3341 \n", "Q 956 3463 1253 3523 \n", "Q 1550 3584 1831 3584 \n", "Q 2591 3584 2966 3190 \n", "Q 3341 2797 3341 1997 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-69\" d=\"M 603 3500 \n", "L 1178 3500 \n", "L 1178 0 \n", "L 603 0 \n", "L 603 3500 \n", "z\n", "M 603 4863 \n", "L 1178 4863 \n", "L 1178 4134 \n", "L 603 4134 \n", "L 603 4863 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-6e\" d=\"M 3513 2113 \n", "L 3513 0 \n", "L 2938 0 \n", "L 2938 2094 \n", "Q 2938 2591 2744 2837 \n", "Q 2550 3084 2163 3084 \n", "Q 1697 3084 1428 2787 \n", "Q 1159 2491 1159 1978 \n", "L 1159 0 \n", "L 581 0 \n", "L 581 3500 \n", "L 1159 3500 \n", "L 1159 2956 \n", "Q 1366 3272 1645 3428 \n", "Q 1925 3584 2291 3584 \n", "Q 2894 3584 3203 3211 \n", "Q 3513 2838 3513 2113 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-67\" d=\"M 2906 1791 \n", "Q 2906 2416 2648 2759 \n", "Q 2391 3103 1925 3103 \n", "Q 1463 3103 1205 2759 \n", "Q 947 2416 947 1791 \n", "Q 947 1169 1205 825 \n", "Q 1463 481 1925 481 \n", "Q 2391 481 2648 825 \n", "Q 2906 1169 2906 1791 \n", "z\n", "M 3481 434 \n", "Q 3481 -459 3084 -895 \n", "Q 2688 -1331 1869 -1331 \n", "Q 1566 -1331 1297 -1286 \n", "Q 1028 -1241 775 -1147 \n", "L 775 -588 \n", "Q 1028 -725 1275 -790 \n", "Q 1522 -856 1778 -856 \n", "Q 2344 -856 2625 -561 \n", "Q 2906 -266 2906 331 \n", "L 2906 616 \n", "Q 2728 306 2450 153 \n", "Q 2172 0 1784 0 \n", "Q 1141 0 747 490 \n", "Q 353 981 353 1791 \n", "Q 353 2603 747 3093 \n", "Q 1141 3584 1784 3584 \n", "Q 2172 3584 2450 3431 \n", "Q 2728 3278 2906 2969 \n", "L 2906 3500 \n", "L 3481 3500 \n", "L 3481 434 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-70\" d=\"M 1159 525 \n", "L 1159 -1331 \n", "L 581 -1331 \n", "L 581 3500 \n", "L 1159 3500 \n", "L 1159 2969 \n", "Q 1341 3281 1617 3432 \n", "Q 1894 3584 2278 3584 \n", "Q 2916 3584 3314 3078 \n", "Q 3713 2572 3713 1747 \n", "Q 3713 922 3314 415 \n", "Q 2916 -91 2278 -91 \n", "Q 1894 -91 1617 61 \n", "Q 1341 213 1159 525 \n", "z\n", "M 3116 1747 \n", "Q 3116 2381 2855 2742 \n", "Q 2594 3103 2138 3103 \n", "Q 1681 3103 1420 2742 \n", "Q 1159 2381 1159 1747 \n", "Q 1159 1113 1420 752 \n", "Q 1681 391 2138 391 \n", "Q 2594 391 2855 752 \n", "Q 3116 1113 3116 1747 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-75\" d=\"M 544 1381 \n", "L 544 3500 \n", "L 1119 3500 \n", "L 1119 1403 \n", "Q 1119 906 1312 657 \n", "Q 1506 409 1894 409 \n", "Q 2359 409 2629 706 \n", "Q 2900 1003 2900 1516 \n", "L 2900 3500 \n", "L 3475 3500 \n", "L 3475 0 \n", "L 2900 0 \n", "L 2900 538 \n", "Q 2691 219 2414 64 \n", "Q 2138 -91 1772 -91 \n", "Q 1169 -91 856 284 \n", "Q 544 659 544 1381 \n", "z\n", "M 1991 3584 \n", "L 1991 3584 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-73\" d=\"M 2834 3397 \n", "L 2834 2853 \n", "Q 2591 2978 2328 3040 \n", "Q 2066 3103 1784 3103 \n", "Q 1356 3103 1142 2972 \n", "Q 928 2841 928 2578 \n", "Q 928 2378 1081 2264 \n", "Q 1234 2150 1697 2047 \n", "L 1894 2003 \n", "Q 2506 1872 2764 1633 \n", "Q 3022 1394 3022 966 \n", "Q 3022 478 2636 193 \n", "Q 2250 -91 1575 -91 \n", "Q 1294 -91 989 -36 \n", "Q 684 19 347 128 \n", "L 347 722 \n", "Q 666 556 975 473 \n", "Q 1284 391 1588 391 \n", "Q 1994 391 2212 530 \n", "Q 2431 669 2431 922 \n", "Q 2431 1156 2273 1281 \n", "Q 2116 1406 1581 1522 \n", "L 1381 1569 \n", "Q 847 1681 609 1914 \n", "Q 372 2147 372 2553 \n", "Q 372 3047 722 3315 \n", "Q 1072 3584 1716 3584 \n", "Q 2034 3584 2315 3537 \n", "Q 2597 3491 2834 3397 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "      </defs>\n", "      <use xlink:href=\"#DejaVuSans-53\"/>\n", "      <use xlink:href=\"#DejaVuSans-6f\" x=\"63.476562\"/>\n", "      <use xlink:href=\"#DejaVuSans-72\" x=\"124.658203\"/>\n", "      <use xlink:href=\"#DejaVuSans-74\" x=\"165.771484\"/>\n", "      <use xlink:href=\"#DejaVuSans-65\" x=\"204.980469\"/>\n", "      <use xlink:href=\"#DejaVuSans-64\" x=\"266.503906\"/>\n", "      <use xlink:href=\"#DejaVuSans-20\" x=\"329.980469\"/>\n", "      <use xlink:href=\"#DejaVuSans-74\" x=\"361.767578\"/>\n", "      <use xlink:href=\"#DejaVuSans-72\" x=\"400.976562\"/>\n", "      <use xlink:href=\"#DejaVuSans-61\" x=\"442.089844\"/>\n", "      <use xlink:href=\"#DejaVuSans-69\" x=\"503.369141\"/>\n", "      <use xlink:href=\"#DejaVuSans-6e\" x=\"531.152344\"/>\n", "      <use xlink:href=\"#DejaVuSans-69\" x=\"594.53125\"/>\n", "      <use xlink:href=\"#DejaVuSans-6e\" x=\"622.314453\"/>\n", "      <use xlink:href=\"#DejaVuSans-67\" x=\"685.693359\"/>\n", "      <use xlink:href=\"#DejaVuSans-20\" x=\"749.169922\"/>\n", "      <use xlink:href=\"#DejaVuSans-69\" x=\"780.957031\"/>\n", "      <use xlink:href=\"#DejaVuSans-6e\" x=\"808.740234\"/>\n", "      <use xlink:href=\"#DejaVuSans-70\" x=\"872.119141\"/>\n", "      <use xlink:href=\"#DejaVuSans-75\" x=\"935.595703\"/>\n", "      <use xlink:href=\"#DejaVuSans-74\" x=\"998.974609\"/>\n", "      <use xlink:href=\"#DejaVuSans-73\" x=\"1038.183594\"/>\n", "     </g>\n", "    </g>\n", "   </g>\n", "   <g id=\"matplotlib.axis_2\">\n", "    <g id=\"ytick_1\">\n", "     <g id=\"line2d_4\">\n", "      <defs>\n", "       <path id=\"m70faac3c31\" d=\"M 0 0 \n", "L -3.5 0 \n", "\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </defs>\n", "      <g>\n", "       <use xlink:href=\"#m70faac3c31\" x=\"40.603125\" y=\"10.999219\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_5\">\n", "      <!-- 0 -->\n", "      <g transform=\"translate(27.240625 14.798438)scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_2\">\n", "     <g id=\"line2d_5\">\n", "      <g>\n", "       <use xlink:href=\"#m70faac3c31\" x=\"40.603125\" y=\"33.319219\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_6\">\n", "      <!-- 10 -->\n", "      <g transform=\"translate(20.878125 37.118438)scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-31\" d=\"M 794 531 \n", "L 1825 531 \n", "L 1825 4091 \n", "L 703 3866 \n", "L 703 4441 \n", "L 1819 4666 \n", "L 2450 4666 \n", "L 2450 531 \n", "L 3481 531 \n", "L 3481 0 \n", "L 794 0 \n", "L 794 531 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-31\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"63.623047\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_3\">\n", "     <g id=\"line2d_6\">\n", "      <g>\n", "       <use xlink:href=\"#m70faac3c31\" x=\"40.603125\" y=\"55.639219\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_7\">\n", "      <!-- 20 -->\n", "      <g transform=\"translate(20.878125 59.438438)scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-32\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"63.623047\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_4\">\n", "     <g id=\"line2d_7\">\n", "      <g>\n", "       <use xlink:href=\"#m70faac3c31\" x=\"40.603125\" y=\"77.959219\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_8\">\n", "      <!-- 30 -->\n", "      <g transform=\"translate(20.878125 81.758438)scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-33\" d=\"M 2597 2516 \n", "Q 3050 2419 3304 2112 \n", "Q 3559 1806 3559 1356 \n", "Q 3559 666 3084 287 \n", "Q 2609 -91 1734 -91 \n", "Q 1441 -91 1130 -33 \n", "Q 819 25 488 141 \n", "L 488 750 \n", "Q 750 597 1062 519 \n", "Q 1375 441 1716 441 \n", "Q 2309 441 2620 675 \n", "Q 2931 909 2931 1356 \n", "Q 2931 1769 2642 2001 \n", "Q 2353 2234 1838 2234 \n", "L 1294 2234 \n", "L 1294 2753 \n", "L 1863 2753 \n", "Q 2328 2753 2575 2939 \n", "Q 2822 3125 2822 3475 \n", "Q 2822 3834 2567 4026 \n", "Q 2313 4219 1838 4219 \n", "Q 1578 4219 1281 4162 \n", "Q 984 4106 628 3988 \n", "L 628 4550 \n", "Q 988 4650 1302 4700 \n", "Q 1616 4750 1894 4750 \n", "Q 2613 4750 3031 4423 \n", "Q 3450 4097 3450 3541 \n", "Q 3450 3153 3228 2886 \n", "Q 3006 2619 2597 2516 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-33\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"63.623047\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_5\">\n", "     <g id=\"line2d_8\">\n", "      <g>\n", "       <use xlink:href=\"#m70faac3c31\" x=\"40.603125\" y=\"100.279219\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_9\">\n", "      <!-- 40 -->\n", "      <g transform=\"translate(20.878125 104.078438)scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-34\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"63.623047\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"text_10\">\n", "     <!-- Sorted testing inputs -->\n", "     <g transform=\"translate(14.798438 118.160563)rotate(-90)scale(0.1 -0.1)\">\n", "      <use xlink:href=\"#DejaVuSans-53\"/>\n", "      <use xlink:href=\"#DejaVuSans-6f\" x=\"63.476562\"/>\n", "      <use xlink:href=\"#DejaVuSans-72\" x=\"124.658203\"/>\n", "      <use xlink:href=\"#DejaVuSans-74\" x=\"165.771484\"/>\n", "      <use xlink:href=\"#DejaVuSans-65\" x=\"204.980469\"/>\n", "      <use xlink:href=\"#DejaVuSans-64\" x=\"266.503906\"/>\n", "      <use xlink:href=\"#DejaVuSans-20\" x=\"329.980469\"/>\n", "      <use xlink:href=\"#DejaVuSans-74\" x=\"361.767578\"/>\n", "      <use xlink:href=\"#DejaVuSans-65\" x=\"400.976562\"/>\n", "      <use xlink:href=\"#DejaVuSans-73\" x=\"462.5\"/>\n", "      <use xlink:href=\"#DejaVuSans-74\" x=\"514.599609\"/>\n", "      <use xlink:href=\"#DejaVuSans-69\" x=\"553.808594\"/>\n", "      <use xlink:href=\"#DejaVuSans-6e\" x=\"581.591797\"/>\n", "      <use xlink:href=\"#DejaVuSans-67\" x=\"644.970703\"/>\n", "      <use xlink:href=\"#DejaVuSans-20\" x=\"708.447266\"/>\n", "      <use xlink:href=\"#DejaVuSans-69\" x=\"740.234375\"/>\n", "      <use xlink:href=\"#DejaVuSans-6e\" x=\"768.017578\"/>\n", "      <use xlink:href=\"#DejaVuSans-70\" x=\"831.396484\"/>\n", "      <use xlink:href=\"#DejaVuSans-75\" x=\"894.873047\"/>\n", "      <use xlink:href=\"#DejaVuSans-74\" x=\"958.251953\"/>\n", "      <use xlink:href=\"#DejaVuSans-73\" x=\"997.460938\"/>\n", "     </g>\n", "    </g>\n", "   </g>\n", "   <g id=\"patch_3\">\n", "    <path d=\"M 40.**********.483219 \n", "L 40.603125 9.883219 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_4\">\n", "    <path d=\"M 152.**********.483219 \n", "L 152.203125 9.883219 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_5\">\n", "    <path d=\"M 40.**********.483219 \n", "L 152.**********.483219 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_6\">\n", "    <path d=\"M 40.603125 9.883219 \n", "L 152.203125 9.883219 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "  </g>\n", "  <g id=\"axes_2\">\n", "   <g id=\"patch_7\">\n", "    <path d=\"M 159.178125 107.263219 \n", "L 163.336125 107.263219 \n", "L 163.336125 24.103219 \n", "L 159.178125 24.103219 \n", "z\n", "\" style=\"fill: #ffffff\"/>\n", "   </g>\n", "   <g id=\"patch_8\">\n", "    <path clip-path=\"url(#peafd0c02b1)\" style=\"fill: #ffffff; stroke: #ffffff; stroke-width: 0.01; stroke-linejoin: miter\"/>\n", "   </g>\n", "   <image xlink:href=\"data:image/png;base64,\n", "iVBORw0KGgoAAAANSUhEUgAAAAYAAABzCAYAAAC7I3M6AAAAv0lEQVR4nNWVQQrDQAwDXfD/v9pLT1l71Q/sBBQMpTmukMbWhuSlz1txeDK0T+d3wrYFmyFm9BTjZsFjVY/g6GgSyKFaLsOHt82gKPlT+QvaJUbVWNS/waldjqKXGh24oB9lO/RL+AOBGGoSFjLsqDm4mj6W/lSFDDcK4ZvGbXTQ36BJQAa0Hlk2nAWCF081xsBKWiQcjyNykwM6jCx0nM8jO2wH74GbQ5S/IMIXMbASnAodFwllR/mOy76Puam+t3oOUronbv0AAAAASUVORK5CYII=\" id=\"image87e01f41c9\" transform=\"scale(1 -1)translate(0 -82.8)\" x=\"159.12\" y=\"-23.76\" width=\"4.32\" height=\"82.8\"/>\n", "   <g id=\"matplotlib.axis_3\">\n", "    <g id=\"ytick_6\">\n", "     <g id=\"line2d_9\">\n", "      <defs>\n", "       <path id=\"m40a8fe6393\" d=\"M 0 0 \n", "L 3.5 0 \n", "\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </defs>\n", "      <g>\n", "       <use xlink:href=\"#m40a8fe6393\" x=\"163.336125\" y=\"107.263219\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_11\">\n", "      <!-- 0.00 -->\n", "      <g transform=\"translate(170.336125 111.062437)scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-2e\" d=\"M 684 794 \n", "L 1344 794 \n", "L 1344 0 \n", "L 684 0 \n", "L 684 794 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"95.410156\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"159.033203\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_7\">\n", "     <g id=\"line2d_10\">\n", "      <g>\n", "       <use xlink:href=\"#m40a8fe6393\" x=\"163.336125\" y=\"86.473189\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_12\">\n", "      <!-- 0.25 -->\n", "      <g transform=\"translate(170.336125 90.272408)scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-35\" d=\"M 691 4666 \n", "L 3169 4666 \n", "L 3169 4134 \n", "L 1269 4134 \n", "L 1269 2991 \n", "Q 1406 3038 1543 3061 \n", "Q 1681 3084 1819 3084 \n", "Q 2600 3084 3056 2656 \n", "Q 3513 2228 3513 1497 \n", "Q 3513 744 3044 326 \n", "Q 2575 -91 1722 -91 \n", "Q 1428 -91 1123 -41 \n", "Q 819 9 494 109 \n", "L 494 744 \n", "Q 775 591 1075 516 \n", "Q 1375 441 1709 441 \n", "Q 2250 441 2565 725 \n", "Q 2881 1009 2881 1497 \n", "Q 2881 1984 2565 2268 \n", "Q 2250 2553 1709 2553 \n", "Q 1456 2553 1204 2497 \n", "Q 953 2441 691 2322 \n", "L 691 4666 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-32\" x=\"95.410156\"/>\n", "       <use xlink:href=\"#DejaVuSans-35\" x=\"159.033203\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_8\">\n", "     <g id=\"line2d_11\">\n", "      <g>\n", "       <use xlink:href=\"#m40a8fe6393\" x=\"163.336125\" y=\"65.683159\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_13\">\n", "      <!-- 0.50 -->\n", "      <g transform=\"translate(170.336125 69.482378)scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-35\" x=\"95.410156\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"159.033203\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_9\">\n", "     <g id=\"line2d_12\">\n", "      <g>\n", "       <use xlink:href=\"#m40a8fe6393\" x=\"163.336125\" y=\"44.89313\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_14\">\n", "      <!-- 0.75 -->\n", "      <g transform=\"translate(170.336125 48.692348)scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-37\" d=\"M 525 4666 \n", "L 3525 4666 \n", "L 3525 4397 \n", "L 1831 0 \n", "L 1172 0 \n", "L 2766 4134 \n", "L 525 4134 \n", "L 525 4666 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-37\" x=\"95.410156\"/>\n", "       <use xlink:href=\"#DejaVuSans-35\" x=\"159.033203\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "   </g>\n", "   <g id=\"LineCollection_1\"/>\n", "   <g id=\"patch_9\">\n", "    <path d=\"M 159.178125 107.263219 \n", "L 161.257125 107.263219 \n", "L 163.336125 107.263219 \n", "L 163.336125 24.103219 \n", "L 161.257125 24.103219 \n", "L 159.178125 24.103219 \n", "L 159.178125 107.263219 \n", "z\n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "  </g>\n", " </g>\n", " <defs>\n", "  <clipPath id=\"p1919480c70\">\n", "   <rect x=\"40.603125\" y=\"9.883219\" width=\"111.6\" height=\"111.6\"/>\n", "  </clipPath>\n", "  <clipPath id=\"peafd0c02b1\">\n", "   <rect x=\"159.178125\" y=\"24.103219\" width=\"4.158\" height=\"83.16\"/>\n", "  </clipPath>\n", " </defs>\n", "</svg>\n"], "text/plain": ["<Figure size 250x250 with 2 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["d2l.show_heatmaps(net.attention_weights.unsqueeze(0).unsqueeze(0),\n", "                  xlabel='Sorted training inputs',\n", "                  ylabel='Sorted testing inputs')"]}, {"cell_type": "markdown", "id": "ca0f3c0c", "metadata": {"origin_pos": 66}, "source": ["## 小结\n", "\n", "* Nadaraya-Watson核回归是具有注意力机制的机器学习范例。\n", "* Nadaraya-Watson核回归的注意力汇聚是对训练数据中输出的加权平均。从注意力的角度来看，分配给每个值的注意力权重取决于将值所对应的键和查询作为输入的函数。\n", "* 注意力汇聚可以分为非参数型和带参数型。\n", "\n", "## 练习\n", "\n", "1. 增加训练数据的样本数量，能否得到更好的非参数的Nadaraya-Watson核回归模型？\n", "1. 在带参数的注意力汇聚的实验中学习得到的参数$w$的价值是什么？为什么在可视化注意力权重时，它会使加权区域更加尖锐？\n", "1. 如何将超参数添加到非参数的Nadaraya-Watson核回归中以实现更好地预测结果？\n", "1. 为本节的核回归设计一个新的带参数的注意力汇聚模型。训练这个新模型并可视化其注意力权重。\n"]}, {"cell_type": "markdown", "id": "60d0f506", "metadata": {"origin_pos": 68, "tab": ["pytorch"]}, "source": ["[Discussions](https://discuss.d2l.ai/t/5760)\n"]}], "metadata": {"language_info": {"name": "python"}}, "nbformat": 4, "nbformat_minor": 5}