<?xml version="1.0" encoding="UTF-8"?>
<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="202pt" height="154pt" viewBox="0 0 202 154" version="1.1">
<defs>
<g>
<symbol overflow="visible" id="glyph0-0">
<path style="stroke:none;" d=""/>
</symbol>
<symbol overflow="visible" id="glyph0-1">
<path style="stroke:none;" d="M 2.734375 1.453125 C 1.46875 0.390625 1.203125 -0.625 1.203125 -2.296875 C 1.203125 -4.03125 1.484375 -4.875 2.734375 -5.9375 L 2.65625 -6.078125 C 1.21875 -5.234375 0.4375 -3.9375 0.4375 -2.265625 C 0.4375 -0.734375 1.203125 0.78125 2.625 1.59375 Z M 2.734375 1.453125 "/>
</symbol>
<symbol overflow="visible" id="glyph0-2">
<path style="stroke:none;" d="M 0.546875 -4.59375 C 0.921875 -5.25 1.328125 -5.546875 1.890625 -5.546875 C 2.484375 -5.546875 2.859375 -5.234375 2.859375 -4.625 C 2.859375 -4.078125 2.578125 -3.671875 2.140625 -3.421875 C 1.953125 -3.3125 1.71875 -3.21875 1.375 -3.09375 L 1.375 -2.96875 C 1.890625 -2.96875 2.09375 -2.9375 2.296875 -2.875 C 2.921875 -2.703125 3.234375 -2.265625 3.234375 -1.578125 C 3.234375 -0.8125 2.734375 -0.203125 2.0625 -0.203125 C 1.8125 -0.203125 1.625 -0.25 1.28125 -0.484375 C 1.03125 -0.65625 0.890625 -0.71875 0.734375 -0.71875 C 0.53125 -0.71875 0.375 -0.578125 0.375 -0.390625 C 0.375 -0.0625 0.71875 0.125 1.375 0.125 C 2.171875 0.125 3.03125 -0.140625 3.46875 -0.71875 C 3.71875 -1.046875 3.875 -1.5 3.875 -1.96875 C 3.875 -2.4375 3.734375 -2.859375 3.484375 -3.125 C 3.296875 -3.328125 3.125 -3.4375 2.734375 -3.609375 C 3.34375 -3.96875 3.578125 -4.421875 3.578125 -4.84375 C 3.578125 -5.59375 3 -6.078125 2.171875 -6.078125 C 1.234375 -6.078125 0.671875 -5.484375 0.40625 -4.625 Z M 0.546875 -4.59375 "/>
</symbol>
<symbol overflow="visible" id="glyph0-3">
<path style="stroke:none;" d="M 0.75 1.265625 C 1.375 0.96875 1.75 0.40625 1.75 -0.140625 C 1.75 -0.609375 1.4375 -0.921875 1.03125 -0.921875 C 0.71875 -0.921875 0.5 -0.71875 0.5 -0.40625 C 0.5 -0.09375 0.6875 0.046875 1.015625 0.046875 C 1.109375 0.046875 1.203125 0.015625 1.28125 0.015625 C 1.34375 0.015625 1.40625 0.078125 1.40625 0.140625 C 1.40625 0.4375 1.15625 0.765625 0.65625 1.09375 Z M 0.75 1.265625 "/>
</symbol>
<symbol overflow="visible" id="glyph0-4">
<path style="stroke:none;" d=""/>
</symbol>
<symbol overflow="visible" id="glyph0-5">
<path style="stroke:none;" d="M 3.546875 0 L 3.546875 -0.140625 C 2.875 -0.140625 2.6875 -0.296875 2.6875 -0.6875 L 2.6875 -6.0625 L 2.609375 -6.078125 L 1 -5.265625 L 1 -5.140625 L 1.234375 -5.234375 C 1.40625 -5.296875 1.5625 -5.34375 1.640625 -5.34375 C 1.84375 -5.34375 1.921875 -5.203125 1.921875 -4.890625 L 1.921875 -0.859375 C 1.921875 -0.359375 1.734375 -0.171875 1.0625 -0.140625 L 1.0625 0 Z M 3.546875 0 "/>
</symbol>
<symbol overflow="visible" id="glyph0-6">
<path style="stroke:none;" d="M 0.265625 -5.9375 C 1.5625 -4.90625 1.796875 -3.875 1.796875 -2.203125 C 1.796875 -0.453125 1.53125 0.390625 0.265625 1.453125 L 0.34375 1.59375 C 1.765625 0.71875 2.5625 -0.5625 2.5625 -2.21875 C 2.5625 -3.75 1.75 -5.25 0.375 -6.078125 Z M 0.265625 -5.9375 "/>
</symbol>
<symbol overflow="visible" id="glyph0-7">
<path style="stroke:none;" d="M 2.5625 -1.75 L 2.5625 -2.3125 L 0.34375 -2.3125 L 0.34375 -1.75 Z M 2.5625 -1.75 "/>
</symbol>
<symbol overflow="visible" id="glyph0-8">
<path style="stroke:none;" d="M 4.265625 -1.234375 L 4.140625 -1.28125 C 3.84375 -0.78125 3.65625 -0.6875 3.28125 -0.6875 L 1.171875 -0.6875 L 2.65625 -2.265625 C 3.453125 -3.109375 3.8125 -3.78125 3.8125 -4.5 C 3.8125 -5.390625 3.15625 -6.078125 2.140625 -6.078125 C 1.03125 -6.078125 0.453125 -5.34375 0.265625 -4.296875 L 0.453125 -4.25 C 0.8125 -5.125 1.140625 -5.421875 1.78125 -5.421875 C 2.546875 -5.421875 3.03125 -4.96875 3.03125 -4.15625 C 3.03125 -3.390625 2.703125 -2.703125 1.859375 -1.8125 L 0.265625 -0.109375 L 0.265625 0 L 3.78125 0 Z M 4.265625 -1.234375 "/>
</symbol>
</g>
</defs>
<g id="surface1">
<path style="fill-rule:nonzero;fill:rgb(0%,0%,0%);fill-opacity:1;stroke-width:1;stroke-linecap:butt;stroke-linejoin:miter;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 170.847656 53.050781 C 170.316406 52.414062 170.40625 51.46875 171.042969 50.9375 C 171.679688 50.410156 172.625 50.496094 173.152344 51.132812 C 173.683594 51.769531 173.59375 52.71875 172.957031 53.246094 C 172.320312 53.777344 171.375 53.6875 170.847656 53.050781 " transform="matrix(1,0,0,1,1,1)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:butt;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 5.898438 76 L 194.101562 76 " transform="matrix(1,0,0,1,1,1)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:butt;stroke-linejoin:miter;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 198.101562 76 L 194.101562 76 M 194.101562 74.5 L 198.101562 76 L 194.101562 77.5 " transform="matrix(1,0,0,1,1,1)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:butt;stroke-linejoin:miter;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 1.898438 76 L 5.898438 76 M 5.898438 77.5 L 1.898438 76 L 5.898438 74.5 " transform="matrix(1,0,0,1,1,1)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:butt;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 100 5.898438 L 100 146.101562 " transform="matrix(1,0,0,1,1,1)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:butt;stroke-linejoin:miter;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 100 150.101562 L 100 146.101562 M 101.5 146.101562 L 100 150.101562 L 98.5 146.101562 " transform="matrix(1,0,0,1,1,1)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:butt;stroke-linejoin:miter;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 100 1.898438 L 100 5.898438 M 98.5 5.898438 L 100 1.898438 L 101.5 5.898438 " transform="matrix(1,0,0,1,1,1)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:butt;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-dasharray:1,4;stroke-miterlimit:10;" d="M 124 8 L 124 144 " transform="matrix(1,0,0,1,1,1)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:butt;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-dasharray:1,4;stroke-miterlimit:10;" d="M 148 8 L 148 144 " transform="matrix(1,0,0,1,1,1)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:butt;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-dasharray:1,4;stroke-miterlimit:10;" d="M 172 8 L 172 144 " transform="matrix(1,0,0,1,1,1)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:butt;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-dasharray:1,4;stroke-miterlimit:10;" d="M 76 8 L 76 144 " transform="matrix(1,0,0,1,1,1)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:butt;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-dasharray:1,4;stroke-miterlimit:10;" d="M 28 8 L 28 144 " transform="matrix(1,0,0,1,1,1)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:butt;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-dasharray:1,4;stroke-miterlimit:10;" d="M 52 8 L 52 144 " transform="matrix(1,0,0,1,1,1)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:butt;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-dasharray:1,4;stroke-miterlimit:10;" d="M 8 52 L 192 52 " transform="matrix(1,0,0,1,1,1)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:butt;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-dasharray:1,4;stroke-miterlimit:10;" d="M 8 28 L 192 28 " transform="matrix(1,0,0,1,1,1)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:butt;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-dasharray:1,4;stroke-miterlimit:10;" d="M 8 100 L 192 100 " transform="matrix(1,0,0,1,1,1)"/>
<path style="fill:none;stroke-width:1;stroke-linecap:butt;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-dasharray:1,4;stroke-miterlimit:10;" d="M 8 124 L 192 124 " transform="matrix(1,0,0,1,1,1)"/>
<path style="fill-rule:nonzero;fill:rgb(0%,0%,0%);fill-opacity:1;stroke-width:1;stroke-linecap:butt;stroke-linejoin:miter;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 122.644531 27.539062 C 122.953125 26.769531 123.824219 26.390625 124.59375 26.699219 C 125.363281 27.003906 125.738281 27.878906 125.433594 28.648438 C 125.128906 29.417969 124.253906 29.792969 123.484375 29.484375 C 122.714844 29.179688 122.339844 28.308594 122.644531 27.539062 " transform="matrix(1,0,0,1,1,1)"/>
<path style="fill-rule:nonzero;fill:rgb(0%,0%,0%);fill-opacity:1;stroke-width:1;stroke-linecap:butt;stroke-linejoin:miter;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 50.644531 123.539062 C 50.953125 122.769531 51.824219 122.390625 52.59375 122.699219 C 53.363281 123.003906 53.738281 123.878906 53.433594 124.648438 C 53.128906 125.417969 52.253906 125.792969 51.484375 125.484375 C 50.714844 125.179688 50.339844 124.308594 50.644531 123.539062 " transform="matrix(1,0,0,1,1,1)"/>
<g style="fill:rgb(0%,0%,0%);fill-opacity:1;">
  <use xlink:href="#glyph0-1" x="175.875" y="50"/>
  <use xlink:href="#glyph0-2" x="178.872" y="50"/>
  <use xlink:href="#glyph0-3" x="183.372" y="50"/>
  <use xlink:href="#glyph0-4" x="185.622" y="50"/>
  <use xlink:href="#glyph0-5" x="187.872" y="50"/>
  <use xlink:href="#glyph0-6" x="192.372" y="50"/>
</g>
<g style="fill:rgb(0%,0%,0%);fill-opacity:1;">
  <use xlink:href="#glyph0-1" x="52.756" y="122"/>
  <use xlink:href="#glyph0-7" x="55.753" y="122"/>
  <use xlink:href="#glyph0-8" x="58.75" y="122"/>
  <use xlink:href="#glyph0-3" x="63.25" y="122"/>
  <use xlink:href="#glyph0-4" x="65.5" y="122"/>
  <use xlink:href="#glyph0-7" x="67.75" y="122"/>
  <use xlink:href="#glyph0-8" x="70.747" y="122"/>
  <use xlink:href="#glyph0-6" x="75.247" y="122"/>
</g>
<g style="fill:rgb(0%,0%,0%);fill-opacity:1;">
  <use xlink:href="#glyph0-1" x="127.631" y="25"/>
  <use xlink:href="#glyph0-5" x="130.628" y="25"/>
  <use xlink:href="#glyph0-3" x="135.128" y="25"/>
  <use xlink:href="#glyph0-4" x="137.378" y="25"/>
  <use xlink:href="#glyph0-8" x="139.628" y="25"/>
  <use xlink:href="#glyph0-6" x="144.128" y="25"/>
</g>
</g>
</svg>
